[versions]
#升级依赖版本 请注意兼容 kotlin 和 agp 版本问题
agp = "7.4.2"
baseNet = "1.1"
kotlin = "1.8.22"
logger = "1.4"
logger-logan = "1.4"
apm = "1.0"
mmkv = "1.2.9"
okhttp = "4.12.0"
shViedeoview = "1.1-SNAPSHOT-test-05-drm-ts-yy-00007"
kotlinGradlePlugin = "1.8.22"
coreKtx = "1.7.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.6.1"
material = "1.10.0"
activity = "1.8.0"
constraintlayout = "2.1.4"
kotlinGradlePluginVersion = "1.9 .24"


[libraries]
#播放器
base-net = { module = "com.sh.ott.common:base-net", version.ref = "baseNet" }
mmkv = { module = "com.tencent:mmkv", version.ref = "mmkv" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
sh-trace-canary = { module = "com.sh.ott.apm:trace-canary", version.ref = "apm" }
sh-viedeoview = { module = "com.sh.ott.player:video-view", version.ref = "shViedeoview" }
#日志
logger = { module = "com.sh.ott.common:logger", version.ref = "logger" }
logan = { module = "com.sh.ott.common:logan", version.ref = "logger-logan" }
kotlin-gradle-plugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlinGradlePlugin" }
core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
jetbrains-kotlin-gradle-plugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlinGradlePluginVersion" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
android-library = { id = "com.android.library", version.ref = "agp" }

