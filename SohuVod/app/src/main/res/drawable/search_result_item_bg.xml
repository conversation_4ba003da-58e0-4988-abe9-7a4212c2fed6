<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="false">
        <shape>
            <!--    <solid android:color="@color/colorBlue" />-->
            <gradient
                android:startColor="#4B4A5C"
                android:endColor="#2B2B42 "
                android:angle="90"
                />
            <corners android:radius="@dimen/x10" />

        </shape>
    </item>

    <item android:state_focused="true">
        <shape>
            <solid android:color="@color/tv_color_e8e8ff"/>
            <corners android:radius="@dimen/x10"/>
        </shape>
    </item>

</selector>

