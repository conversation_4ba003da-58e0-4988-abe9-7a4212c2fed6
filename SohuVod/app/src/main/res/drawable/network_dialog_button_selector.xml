<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">

        <shape>
            <!-- 描边 -->
            <stroke
                android:width="@dimen/x4"
                android:color="#e8e8e8"/>
            <!-- 圆角 -->
            <corners
                android:radius="@dimen/y45" />
        </shape>
    </item>

    <item>
        <shape>
            <!-- 描边 -->
            <stroke
                android:width="@dimen/x2"
                android:color="#888888"/>
            <!-- 圆角 -->
            <corners
                android:radius="@dimen/y45" />
        </shape>
    </item>
</selector>