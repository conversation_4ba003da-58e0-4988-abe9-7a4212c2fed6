<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@color/transparent" android:state_selected="true" />
    <item android:state_selected="false">
        <shape>
            <!-- 描边 -->
            <stroke android:width="@dimen/x2" android:color="#888888" />
            <!-- 圆角 -->
            <corners android:radius="@dimen/y45" />
        </shape>
    </item>

</selector>