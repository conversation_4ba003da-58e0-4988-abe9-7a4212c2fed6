<?xml version="1.0" encoding="utf-8"?>
<selector
    xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape
            xmlns:android="http://schemas.android.com/apk/res/android">
            <!--    <solid android:color="@color/colorBlue" />-->
            <corners android:radius="100dp" />
            <gradient
                android:startColor="#F0D1B0"
                android:endColor="#D6996A"
                />
        </shape>
    </item>

    <item>
        <shape>
            <solid android:color="#B8363646" />
            <!-- 描边 -->
<!--            <stroke-->
<!--                android:width="@dimen/x2"-->
<!--                android:color="#D79B6C"/>-->
            <!-- 圆角 -->
            <corners
                android:radius="100px" />
            <!--<padding-->
            <!--android:left="10dp"-->
            <!--android:top="10dp"-->
            <!--android:right="10dp"-->
            <!--android:bottom="10dp" />-->
        </shape>
    </item>
</selector>