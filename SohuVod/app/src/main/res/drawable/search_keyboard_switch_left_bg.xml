<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_focused="true">
        <shape>
            <gradient
                android:startColor="#E4705C"
                android:endColor="#C53D3D"
                />
            <corners android:radius="@dimen/x10" />
        </shape>
    </item>

    <item android:state_focused="false">
        <shape>
            <solid android:color="#41445C"/>
            <corners android:topLeftRadius="@dimen/x10" android:bottomLeftRadius="@dimen/x10" />
        </shape>
    </item>

</selector>

