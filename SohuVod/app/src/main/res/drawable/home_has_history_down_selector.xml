<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape
            xmlns:android="http://schemas.android.com/apk/res/android">
            <!--    <solid android:color="@color/colorBlue" />-->
            <corners android:radius="@dimen/x10"/>
            <gradient
                android:startColor="#E4705C"
                android:endColor="#C53D3D"/>
        </shape>
    </item>

    <item>
        <shape
            xmlns:android="http://schemas.android.com/apk/res/android">
            <solid android:color="@color/no_history_no_focus" />
            <corners android:bottomLeftRadius="@dimen/x10"
                android:bottomRightRadius="@dimen/x10"/>

        </shape>
    </item>
</selector>