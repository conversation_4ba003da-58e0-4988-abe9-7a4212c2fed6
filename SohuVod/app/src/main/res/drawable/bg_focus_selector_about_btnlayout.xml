<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/x10"></corners>
            <gradient
                android:angle="0"
                android:startColor="#E4705C"
                android:endColor="#C53D3D"
                android:type="linear" />
        </shape>
    </item>
    <item android:state_focused="false">
        <shape>
            <!--角的半径-->
            <corners android:radius="@dimen/x10"/>
            <solid android:color="#41445C"></solid>
        </shape>
    </item>
</selector>