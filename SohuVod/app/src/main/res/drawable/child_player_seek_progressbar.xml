<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" >

    <item
        android:id="@android:id/background"
        android:drawable="@drawable/child_player_seek_background"/>
    <item android:id="@android:id/secondaryProgress">
        <!--<clip android:drawable="@drawable/child_player_seek_progress_second" />-->
        <scale
            android:drawable="@drawable/child_player_seek_progress_second"
            android:scaleWidth="100%" />
    </item>
    <item android:id="@android:id/progress">
        <clip android:drawable="@drawable/child_player_seek_progress_drawable" />
        <!--<scale-->
            <!--android:drawable="@drawable/child_player_seek_progress_drawable"-->
            <!--android:scaleWidth="100%" />-->
        <!--<clip android:drawable="@drawable/home_message_view_bg_focused" />-->
    </item>

</layer-list>