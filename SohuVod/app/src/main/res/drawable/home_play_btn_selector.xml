<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--<item android:drawable="@drawable/home_play_btn_focus" android:state_selected="true" />-->
    <!--<item android:drawable="@drawable/home_play_btn_unfocus" />-->

    <item android:state_selected="true">
        <shape>
            <!-- 描边 -->
            <solid
                android:color="#f0c21a"/>
            <!-- 圆角 -->
            <corners
                android:radius="@dimen/y30" />
        </shape>
    </item>

    <item>
        <shape>
            <!-- 描边 -->
            <solid
                android:color="#3c3c3c"/>
            <!-- 圆角 -->
            <corners
                android:radius="@dimen/y30" />
        </shape>
    </item>

</selector>