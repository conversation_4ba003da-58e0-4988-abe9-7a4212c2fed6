<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_focused="false">
        <shape>
            <solid android:color="@color/tv_color_4dffffff" />
            <corners android:radius="@dimen/x10"/>
        </shape>
    </item>

    <item android:state_focused="true">
        <shape xmlns:android="http://schemas.android.com/apk/res/android">
            <gradient
                android:endColor="#C53D3D"
                android:startColor="#E4705C" />
            <corners android:radius="@dimen/x10" />
        </shape>
    </item>
</selector>