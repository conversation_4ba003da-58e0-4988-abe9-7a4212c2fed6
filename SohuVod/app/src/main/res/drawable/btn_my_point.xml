<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape>
            <!-- 描边 -->
            <stroke android:width="@dimen/x2" android:color="#e8e8e8" />
            <!-- 圆角 -->
            <corners android:radius="@dimen/y41" />
            <solid android:color="#B7363646" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape>
            <!-- 描边 -->
            <stroke android:width="@dimen/x2" android:color="#e8e8e8" />
            <!-- 圆角 -->
            <corners android:radius="@dimen/y41" />
            <solid android:color="#B7363646" />
        </shape>
    </item>
    <item android:state_selected="false">
        <shape>
            <!-- 圆角 -->
            <corners android:radius="@dimen/y41" />
            <solid android:color="#B7363646" />
        </shape>
    </item>

</selector>