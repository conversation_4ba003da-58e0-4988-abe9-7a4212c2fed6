<?xml version="1.0" encoding="utf-8"?>
<selector
    xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape
            xmlns:android="http://schemas.android.com/apk/res/android">
            <!--    <solid android:color="@color/colorBlue" />-->
            <corners android:radius="5px" />
            <gradient
                android:startColor="#E4705C"
                android:endColor="#C53D3D"
                />
        </shape>
    </item>

    <item>
        <shape>
            <!-- 圆角 -->
            <corners
                android:radius="5px" />

            <gradient
                android:startColor="#5C607B"
                android:endColor="#414562"/>
            <!--<padding-->
            <!--android:left="10dp"-->
            <!--android:top="10dp"-->
            <!--android:right="10dp"-->
            <!--android:bottom="10dp" />-->
        </shape>
    </item>
</selector>