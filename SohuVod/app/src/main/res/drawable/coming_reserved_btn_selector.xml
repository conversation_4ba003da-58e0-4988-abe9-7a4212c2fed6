<?xml version="1.0" encoding="utf-8"?>
<selector
    xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape
            xmlns:android="http://schemas.android.com/apk/res/android">
            <!--    <solid android:color="@color/colorBlue" />-->
            <corners android:radius="5px" />
            <gradient
                android:startColor="#E4705C"
                android:endColor="#C53D3D"
                />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <stroke
                android:width="1.5dp"
                android:color="#8D8D9E" />
            <corners android:radius="@dimen/x5"></corners>
        </shape>
    </item>
</selector>