package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.text.TextUtils
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import com.lib_dlna_core.SohuDlnaManger
import com.lib_dlna_core.SohuDlnaManger.Companion.getInstance
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.sh.ott.video.contor.ShControlComponent
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.isVisible
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.videodetail.activity.state.FEE_ALBUM
import com.sohuott.tv.vod.videodetail.activity.state.FEE_FREE
import com.sohuott.tv.vod.videodetail.activity.state.FEE_VIDEO

/**
 * 提示文案展示
 */
class VideoPromptCopyComponent constructor(context: Context) :
    ShControlComponent(context) {


    private lateinit var mHintText: TextView
    private var dlnaPermissionView: View? = null

    private var isSingle = false
    private var isTicket = false
    private var isMember = false
    private var mTicketCount = 0
    var feeType = FEE_FREE
    var currentType = HINT_TEXT_DEFAULT

    init {
        layoutInflater.inflate(R.layout.video_component_prompt_copy, this, true)
        mHintText = findViewById(R.id.player_hint)
        dlnaPermissionView = findViewById(R.id.layout_dlna_permission)
        onScreenModeChanged(PlayerConstants.ScreenMode.NORMAL)
        gone()
    }

    fun setMemberPayInfo(
        isSingle: Boolean?,
        isMember: Boolean?,
        isTicket: Boolean?,
        mTicketCount: Int?
    ) {
        this.isSingle = isSingle ?: false
        this.isTicket = isTicket ?: false
        this.isMember = isMember ?: false
        this.mTicketCount = mTicketCount ?: 0
    }

    fun hide(){
        gone()
    }

    override fun onScreenModeChanged(screenMode: Int) {
        AppLogger.d("setPayHintText onScreenModeChanged screenMode: ${screenMode}")

        if (screenMode == PlayerConstants.ScreenMode.FULL) {
            mHintText.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                context.resources.getDimensionPixelSize(R.dimen.y40).toFloat()
            );

        } else if (screenMode == PlayerConstants.ScreenMode.TINY) {
            mHintText.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                getResources().getDimensionPixelSize(R.dimen.y26).toFloat()
            );
        } else {
            mHintText.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                getResources().getDimensionPixelSize(R.dimen.y32)
                    .toFloat()
            );
        }
    }

    fun hasShow(): Boolean {
        return visibility == VISIBLE
    }

    fun getHasShowDlnaPermissionView(): Boolean {
        return dlnaPermissionView?.visibility == VISIBLE
    }

    fun setPayHintText(type: Int) {
        AppLogger.d("setPayHintText type is ${type}")
        currentType = type
        val isSohuVideo = SohuDlnaManger.getInstance().getIsOttSohuVideo()
        val isDlna = SohuDlnaManger.getInstance().getIsDlna()
        dlnaPermissionView?.isVisible(type == HINT_TEXT_DLNA_PERMISSION_KEY_FAIL)
        when (type) {
            HINT_TEXT_DLNA_PERMISSION_KEY_FAIL -> {
                visible()
                val map = HashMap<String, String>(1)
                map["pageId"] = "1064"
                RequestManager.getInstance().onAllEvent(EventInfo(10135, "imp"), map, null, null)
            }

            HINT_TEXT_DLNA_PLAY -> {
                mHintText.setText("正在接收投放请求")
                mHintText.setVisibility(VISIBLE)
                visible()
            }

            HINT_TEXT_TRY_FINISH -> {
                if (isDlna && !isSohuVideo) return

                val tryHintText = getHintTryFinishText()
                if (mHintText.getVisibility() == VISIBLE && TextUtils.equals(
                        mHintText.getText(),
                        tryHintText
                    )
                ) {
                    return
                }
                if (tryHintText.isEmpty()) {
                    return
                }
                mHintText.setText(tryHintText)
                mHintText.setVisibility(VISIBLE)
                visible()

//                }
            }

            HINT_TEXT_ERROR -> {
                if (isDlna && !isSohuVideo) return;

                val text = resources.getString(R.string.data_err)
                if (mHintText.getVisibility() == VISIBLE && TextUtils.equals(
                        mHintText.getText(),
                        text
                    )
                ) {
                    return
                }
                mHintText.setText(text)
                mHintText.setVisibility(VISIBLE)
                visible()

            }
            HINT_TEXT_DRM_ERROR -> {
                if (isDlna && !isSohuVideo) return;

                val text = resources.getString(R.string.data_drm_err)
                if (mHintText.getVisibility() == VISIBLE && TextUtils.equals(
                        mHintText.getText(),
                        text
                    )
                ) {
                    return
                }
                mHintText.setText(text)
                mHintText.setVisibility(VISIBLE)
                visible()

            }

            HINT_TEXT_ERROR_OFFLINE -> {
                if (isDlna && !isSohuVideo) return

                val textOffline = resources.getString(R.string.scale_player_video_offline)
                if (mHintText.getVisibility() == VISIBLE && TextUtils.equals(
                        mHintText.getText(),
                        textOffline
                    )
                ) {
                    return
                }
                mHintText.setVisibility(VISIBLE)
                mHintText.setText(textOffline)
                visible()

            }

            HINT_TEXT_NORMAL -> {
                if (isDlna && !isSohuVideo) return
                if (isTicket || isSingle) {

                    val textPay = "应版权方要求，该片需要购买观看"
                    if (mHintText.getVisibility() == VISIBLE && TextUtils.equals(
                            mHintText.getText(),
                            textPay
                        )
                    ) {
                        return
                    }
                    mHintText.setText(textPay)
                    mHintText.setVisibility(VISIBLE)
                    visible()

                    return
                }
                if (feeType == FEE_VIDEO) {

                    if (!isMember) {
                        return
                    }
                    val textVip = "请开通超级会员后观看"
                    if (mHintText.getVisibility() == VISIBLE && TextUtils.equals(
                            mHintText.getText(),
                            textVip
                        )
                    ) {
                        return
                    }
                    mHintText.setText(textVip)
                    mHintText.setVisibility(VISIBLE)
                    visible()

                } else if (feeType == FEE_ALBUM) {
                    if (!isMember) {
                        return
                    }
                    val textPay = "请开通超级会员后观看"
                    if (mHintText.getVisibility() == VISIBLE && TextUtils.equals(
                            mHintText.getText(),
                            textPay
                        )
                    ) {
                        return
                    }
                    mHintText.setText(textPay)
                    mHintText.setVisibility(VISIBLE)
                    visible()

                } else {
                    mHintText.setVisibility(GONE)
                    hide()
                }
            }

            HINT_TEXT_NAN_CHUAN -> {
                if (isDlna && !isSohuVideo) return
                val textYun = "云视听播控平台鉴权失败！"
                if (mHintText.getVisibility() == VISIBLE && TextUtils.equals(
                        mHintText.getText(),
                        textYun
                    )
                ) {
                    return
                }
                mHintText.setText(textYun)
                mHintText.setVisibility(VISIBLE)
                visible()
            }

            HINT_TEXT_FINISH -> {
                if (isDlna && !isSohuVideo) return

                mHintText.setText(R.string.scale_player_complete_hint)
                mHintText.setVisibility(VISIBLE)
                visible()

            }

            HINT_TEXT_ERROR_NOT_SUPPORTED -> {
                if (isDlna && !isSohuVideo) return

                mHintText.setText(R.string.scale_player_not_right_play_hint)
                mHintText.setVisibility(VISIBLE)
                visible()
            }

            else -> {
                mHintText.setVisibility(GONE)
                hide()
            }
        }

    }

    private fun getHintTryFinishText(): String {
        var tv = ""
        if (!UserLoginHelper.getInstants().getIsLogin()) {
            if (isSingle && isMember && isTicket) {
                tv = "试看结束，开通会员用券观看全片"
                return tv
            }

            if (isSingle && isMember) {
                tv = "试看结束，开通会员观看全片"
                return tv
            }
            if ((isSingle || isMember) && isTicket) {
                tv = "试看结束，开通会员用券观看全片"
                return tv
            }
            if (isMember) {
                tv = "试看结束，开通会员观看全片"
                return tv
            }
            if (isSingle) {
                tv = "试看结束，购买后观看全片"
                return tv
            }
            if (isTicket) {
                tv = "试看结束，开通会员用券观看全片"
            }
        } else {
            if (UserLoginHelper.getInstants().isVip()) {
                if ((isSingle || isMember) && isTicket) {
                    tv = if (mTicketCount > 0) {
                        "试看结束，用券观看全片"
                    } else {
                        "试看结束，续费会员用卷观看全片"
                    }
                    return tv
                }
                if (isSingle) {
                    tv = "试看结束，购买后观看全片"
                    return tv
                }
                if (isTicket) {
                    tv = if (mTicketCount > 0) {
                        "试看结束，用券观看全片"
                    } else {
                        "试看结束，续费会员用卷观看全片"
                    }
                }
            } else {
                if ((isSingle || isMember) && isTicket) {
                    tv = if (mTicketCount > 0) {
                        "试看结束，开通会员激活观影券观看全片"
                    } else {
                        "试看结束，开通会员用券观看全片"
                    }
                    return tv
                }
                if (isSingle && isMember) {
                    tv = "试看结束，开通会员观看全片"
                    return tv
                }
                if (isMember) {
                    tv = "试看结束，开通会员观看全片"
                    return tv
                }
                if (isSingle) {
                    tv = "试看结束，购买后观看全片"
                    return tv
                }
                if (isTicket) {
                    tv = if (mTicketCount > 0) {
                        "试看结束，开通会员续费激活观影券观看全片"
                    } else {
                        "试看结束，开通会员用券观看全片"
                    }
                }
            }
        }
        return tv
    }

    fun refreshPayHintText() {
        setPayHintText(currentType)
    }


    companion object {

        //试看结束
        const val HINT_TEXT_TRY_FINISH: Int = 0

        //错误提示
        const val HINT_TEXT_ERROR = 1

        //正常付费提示
        const val HINT_TEXT_NORMAL: Int = 2

        //南传鉴权失败
        const val HINT_TEXT_NAN_CHUAN = 3

        //视频已下线
        const val HINT_TEXT_ERROR_OFFLINE = 4

        //暂不提供播
        const val HINT_TEXT_ERROR_NOT_SUPPORTED = 5

        //播放完成
        const val HINT_TEXT_FINISH = 6

        //dlna 播放请求
        const val HINT_TEXT_DLNA_PLAY: Int = 7

        //dlna 鉴权失败
        const val HINT_TEXT_DLNA_PERMISSION_KEY_FAIL: Int = 8

        //drm 外采失败
        const val HINT_TEXT_DRM_ERROR = 9


        //默认隐藏
        const val HINT_TEXT_DEFAULT: Int = -1
    }
}