package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.DiffCallback;
import androidx.leanback.widget.HorizontalGridView;
import androidx.leanback.widget.OnChildViewHolderSelectedListener;
import androidx.leanback.widget.Presenter;
import androidx.recyclerview.widget.RecyclerView;

import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AlbumInfo;
import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.model.PgcEpisodeVideos;
import com.sohuott.tv.vod.lib.model.VrsEpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.ui.EpisodeLayoutNew;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.SimpleDisposableObsever;
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager;
import com.sohuott.tv.vod.videodetail.activity.service.EpisodeServiceManger;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by fenglei on 16-6-28.
 */
public class BaseEpisodePicFragment extends EpisodeBaseFragmentNew {
    public int mPage = 1;
    protected int mPageSize = 30;
    //提前10个item请求
    protected int mPreCount = 10;
    protected int mStartPageIndex, mEndPageIndex; //是否有向前一页以及向后一页数据，进行分页加载
    protected boolean mNeedSelect = true; //是否需要滚动到指定位置，默认初始化和播放回调需要
    protected boolean mIsPlaying = true; //是否在当前页播放，触发选中状态

    protected String mPartnerNo;
    protected HorizontalGridView horizontalGridView;
    private CustomItemBridgeAdapter itemBridgeAdapter;
    private ArrayObjectAdapter arrayObjectAdapter;
    private ArrayList<EpisodeVideos.Video> videoList = new ArrayList<>();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mPartnerNo = Util.getPartnerNo(getContext());
        if (mVideoOrder != -1 && mVideoOrder != 0) {
            if (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER) {
                mVideoOrder = mTotalCount - mVideoOrder + 1;
            }
            mVideoOrder -= 1;
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = (ViewGroup) inflater.inflate(R.layout.fragment_episode_common_layout, container, false);
        initUI();
        return mRootView;
    }

    public Presenter getPresenter() {
        return null;
    }

    @Override
    protected void initUI() {
        horizontalGridView = mRootView.findViewById(R.id.episode_list);
        arrayObjectAdapter = new ArrayObjectAdapter(getPresenter());
        itemBridgeAdapter = new CustomItemBridgeAdapter(arrayObjectAdapter) {
            @Override
            public OnItemViewClickedListener getOnItemViewClickedListener() {
                return (focusView, itemViewHolder, item) -> {
                    EpisodeVideos.Video video = (EpisodeVideos.Video) item;
                    if (StringUtil.isEmpty(video.tvName)) {
                        ToastUtils.showToast2(getContext(), "该集已下线");
                        return;
                    }
                    if (video.type == 1) {

                        String pageId;
                        if (!isMenu) {
                            pageId = "1041";
                        } else {
                            pageId = "1045";
                        }
                        VideoDetailRequestManager.recommendListClick(pageId, String.valueOf(video.id), String.valueOf(video.index + 1));
                        ActivityLauncher.startVideoDetailActivity(getContext(), video.id, 0);
                        return;
                    }
//                    if (screenView != null) {
                    if (mFocusBorderView != null) {
                        mFocusBorderView.setVisibility(View.GONE);
                    }
//                        screenView.setVisibility(View.VISIBLE);
                    if (mDataType == Constant.DATA_TYPE_VRS) {
                        VideoDetailRequestManager.tabClick(mIsTrailerTab ? 10283 : 10282, String.valueOf(mAid), String.valueOf(video.tvVerId), isMenu, -1);
                        EpisodeServiceManger.getInstants().onEpisodeClickVideo(mAid, video.tvVerId, video.tvVerId, mDataType, mIsTrailerTab);
//                            screenView.setPlayParamsAndPlayToFullscreen(mAid, video.tvVerId, video.tvVerId, mDataType, mIsTrailerTab);
                    } else {
                        VideoDetailRequestManager.tabClick(10282, String.valueOf(video.id), String.valueOf(video.tvVerId), isMenu, -1);
                        EpisodeServiceManger.getInstants().onEpisodeClickVideo(video.id, video.tvVerId, video.tvVerId, mDataType, mIsTrailerTab);
//                            screenView.setPlayParamsAndPlayToFullscreen(video.id, video.tvVerId, video.tvVerId, mDataType, mIsTrailerTab);
                    }
                    mEpisodeIsSelected = true;
//                    }
                };
            }
        };
        horizontalGridView.setAdapter(itemBridgeAdapter);
        horizontalGridView.setHorizontalSpacing(getContext().getResources().getDimensionPixelOffset(R.dimen.x48));

        //分页加载逻辑
        horizontalGridView.setOnChildViewHolderSelectedListener(new OnChildViewHolderSelectedListener() {
            @Override
            public void onChildViewHolderSelected(RecyclerView parent, RecyclerView.ViewHolder child, int position, int subposition) {
                LibDeprecatedLogger.d("position : " + position + " , mcount" + arrayObjectAdapter.size());
                LibDeprecatedLogger.d("endpageindex : " + mEndPageIndex + " , startpageindex : " + mStartPageIndex + " , sumpagecount : " + (int) Math.ceil((double) mTotalCount / mPageSize));
                if (position == arrayObjectAdapter.size() - mPreCount) {
                    if (mEndPageIndex != (int) Math.ceil((double) mTotalCount / mPageSize)) {
                        getData(++mEndPageIndex);
                    }
                } else if (position == mPreCount) {
                    if (mStartPageIndex != 1) {
                        getData(--mStartPageIndex);
                    }
                }
            }

        });
    }


    protected void currentPage() {
        mPage = mVideoOrder / 30 + 1;
        mStartPageIndex = mPage;
        mEndPageIndex = mPage;
    }

    protected void currentOrder() {
        mVideoOrder = mVideoOrder % 30;
    }

    protected void getData(int page) {
        if (page < 1 || page > (int) Math.ceil((double) mTotalCount / mPageSize)) {
            return;
        }
        if (mDataType == Constant.DATA_TYPE_VRS) {
            getVrsData(page);
        } else {
            getPgcData(page);
        }
    }

    @Override
    public void setItemSelect(int videoOrder, boolean hasFocus, int vid) {
        mVid = vid;
        LibDeprecatedLogger.d("videoOrder : " + videoOrder + " , hasFocus : " + hasFocus);

        int order;
        if (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER) {
            order = mTotalCount - videoOrder;
        } else {
            order = videoOrder - 1;
        }

        LibDeprecatedLogger.d("order : " + order + " , mVideoOrder : " + mVideoOrder + " , videoOrder : " + videoOrder);

        if (mVideoOrder / 30 + 1 >= mStartPageIndex && mVideoOrder / 30 + 1 <= mEndPageIndex) {
            //在区间里不请求直接滑动
            if (!videoList.isEmpty()) {
                for (int i = 0; i < videoList.size(); i++) {
                    if (videoList.get(i).tvVerId == mVid) {
                        videoList.get(i).isSelected = true;
                    } else {
                        videoList.get(i).isSelected = false;
                    }
                }
                setItemsInAdapter(videoList);
                for (int i = 0; i < videoList.size(); i++) {
                    if (videoList.get(i).tvVerId == vid) {
                        horizontalGridView.setSelectedPositionSmooth(i);
                    }
                }

            }
        } else {
            if (order < 0) {
                return;
            }
            videoList.clear();
            mVideoOrder = order;
            mNeedSelect = true;
            currentPage();
            currentOrder();
            getData(mPage);
        }

    }

    @Override
    public void setItemUnSelect() {

    }

    //获取pgc选集
    protected void getPgcData(int page) {
        DisposableObserver<PgcEpisodeVideos> episodeDisposableObserver =
                new SimpleDisposableObsever<PgcEpisodeVideos>(AlbumInfo.class.getSimpleName()) {
                    @Override
                    public void onNext(PgcEpisodeVideos response) {
                        String responseData = response == null ? "null" : String.valueOf(response.status);
                        LibDeprecatedLogger.d("Get PGC episode data response: " + responseData);
                        if (response != null && response.status == 0) {
                            EpisodeVideos episodeVideos = PgcEpisodeVideos.pgcConvert2Videos(response.data);
                            if (episodeVideos != null && episodeVideos.videos != null && episodeVideos.videos.size() != 0) {
                                LibDeprecatedLogger.d("Get PGC episode data: " + episodeVideos.toString());
                                int index = 0;
                                if (videoList.size() != 0) {
                                    if (page > mPage) {
                                        index = videoList.size();
                                    }
                                }
                                LibDeprecatedLogger.v("getdata() : " + mVideoOrder + " ，index : " + index + " , page : " + page + " , mpage : " + mPage);

                                videoList.addAll(index, episodeVideos.videos);

                                if (mNeedSelect && mEpisodeIsSelected && videoList.get(mVideoOrder).type != 1) {
                                    videoList.get(mVideoOrder).isSelected = true;
                                }

                                setItemsInAdapter(videoList);

                                //分页时不滚动到指定位置，只有刚初始化的时候需要滚动和播放器回调时滚动
                                if (mNeedSelect) {
                                    horizontalGridView.setSelectedPosition(mVideoOrder);
                                    mNeedSelect = false;
                                }

                                //TODO:封装
                                //如果前面不够10个，且不是第一页，向前请求
                                if (mVideoOrder - mPreCount < 0 && mStartPageIndex != 1) {
                                    mStartPageIndex -= 1;
                                    getData(mStartPageIndex);
                                } else if (mVideoOrder + mPreCount > videoList.size() && mEndPageIndex < Math.ceil(mTotalCount / (1.0 * mPageSize))) {
                                    //后面不够10个，且不是最后一页
                                    mEndPageIndex += 1;
                                    getData(mEndPageIndex);
                                }
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.w("Get VRS episode data error!", e);
                    }

                    @Override
                    public void onComplete() {
                        //AppLogger.d("Get VRS episode data complete!");
                    }
                };
        NetworkApi.getPgcEpisdoeVideosData(mAid, page, mPageSize, mSortOrder, episodeDisposableObserver);
//        compositeDisposable.add(episodeDisposableObserver);
    }

    //获取vrs选集
    protected void getVrsData(int page) {

        DisposableObserver<VrsEpisodeVideos> episodeVideosDisposableObserver =
                new DisposableObserver<VrsEpisodeVideos>() {
                    @Override
                    public void onNext(VrsEpisodeVideos response) {
                        int playingIndex = 0;
                        String responseData = response == null ? "null" : String.valueOf(response.status);
                        LibDeprecatedLogger.d("Get VRS episode data response: " + responseData);
                        if (response != null && response.status == 0) {
                            EpisodeVideos episodeVideos = VrsEpisodeVideos.vrsConvert2Videos(response.data);
                            if (episodeVideos != null && episodeVideos.videos != null) {
                                LibDeprecatedLogger.d("Get VRS episode data: " + episodeVideos.toString());
                                int index = 0;
                                if (videoList.size() != 0) {
                                    //当前page在初始化page的前后还是后面
                                    if (page > mPage) {
                                        index = videoList.size();
                                    }
                                }
                                LibDeprecatedLogger.v("getdata() : " + mVideoOrder + " ，index : " + index + " , page : " + page + " , mpage : " + mPage);

                                //1.取一个总的list，找到当前的index
                                //2.设置index的selected = true
                                //3.setitems重新刷新列表页面

                                videoList.addAll(index, episodeVideos.videos);

                                if (videoList.size() != 0) {
                                    for (int i = 0; i < videoList.size(); i++) {
                                        if (videoList.get(i).tvVerId == mVid) {
                                            playingIndex = i;
                                        }
                                    }
                                } else {
                                    return;
                                }
                                if (mNeedSelect && mEpisodeIsSelected && videoList.size() > 0) {
                                    videoList.get(playingIndex).isSelected = true;
                                }

                                setItemsInAdapter(videoList);

                                //分页时不滚动到指定位置，只有刚初始化的时候需要滚动和播放器回调时滚动
                                if (mNeedSelect) {
                                    horizontalGridView.setSelectedPosition(playingIndex);
                                    mNeedSelect = false;
                                }

                                //TODO:封装
                                //如果前面不够10个，且不是第一页，向前请求
                                if (mVideoOrder - mPreCount < 0 && mStartPageIndex != 1) {
                                    mStartPageIndex -= 1;
                                    getData(mStartPageIndex);
                                } else if (mVideoOrder + mPreCount > videoList.size() && mEndPageIndex < Math.ceil(mTotalCount / (1.0 * mPageSize))) {
                                    //后面不够10个，且不是最后一页
                                    mEndPageIndex += 1;
                                    getData(mEndPageIndex);
                                } else if (mEndPageIndex >= Math.ceil(mTotalCount / (1.0 * mPageSize)) && recommendVideos != null) {
                                    //最后一页加上推荐列表
                                    videoList.addAll(convertToEpisodeVideos(recommendVideos));
                                    setItemsInAdapter(videoList);
                                }

                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                    }
                };


        LibDeprecatedLogger.d("request data page :" + page + ", aid : " + mAid + " , sortorder : " + mSortOrder);
        NetworkApi.getVrsEpisdoeVideosData(mAid, Constant.DATA_TYPE_VRS, mSortOrder, mPartnerNo, page, mPageSize, episodeVideosDisposableObserver);
    }

    private List<EpisodeVideos.Video> convertToEpisodeVideos(List<AlbumInfoRecommendModel> recommendList) {
        List<EpisodeVideos.Video> videos = new ArrayList<>();
        for (int i = 0; i < recommendList.size(); i++) {
            EpisodeVideos.Video video = new EpisodeVideos.Video();
            AlbumInfoRecommendModel model = recommendList.get(i);
            video.index = i;
            video.type = 1;
            video.tvSets = Integer.parseInt(model.getTvSets());
            video.id = Integer.parseInt(model.getAlbumId());
            video.tvVerId = Integer.parseInt(model.getTvVerId());
            if (model.getPic_url() == null || model.getPic_url().equals("")) {
                video.videoExtendsPic_640_360 = model.getAlbumExtendsPic_640_360();
            } else {
                video.videoExtendsPic_640_360 = model.getPic_url();

            }
            video.tvName = model.getAlbumName();
            video.tvSubName = model.getTvComment();
            videos.add(video);
        }
        return videos;
    }


    public void setItemsInAdapter(ArrayList<EpisodeVideos.Video> list) {
        arrayObjectAdapter.setItems(list, new DiffCallback<EpisodeVideos.Video>() {

            @Override
            public boolean areItemsTheSame(@NonNull EpisodeVideos.Video oldItem, @NonNull EpisodeVideos.Video newItem) {
                return oldItem.videoOrder == newItem.videoOrder;
            }

            @Override
            public boolean areContentsTheSame(@NonNull EpisodeVideos.Video oldItem, @NonNull EpisodeVideos.Video newItem) {
                AppLogger.INSTANCE.v("oldItem isMenu:" + isMenu + "oldItem name:" + oldItem.tvSubName + "oldItem isSelected:" + oldItem.isSelected + "newItem isSelected:" + newItem.isSelected);
                return false;
            }
        });
    }
}
