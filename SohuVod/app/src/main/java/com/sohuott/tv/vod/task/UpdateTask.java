package com.sohuott.tv.vod.task;

import android.content.Context;
import android.os.AsyncTask;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.lib.model.UpdateInfo;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.DownloadUtil;
import com.sohuott.tv.vod.view.AboutView;

import java.util.Objects;

/**
 * Created by wenjingbian on 2016/3/15.
 */
public class UpdateTask extends AsyncTask<UpdateInfo, Integer, Boolean> implements IDownloadListener {

    private static final String TAG = "Vod_DownloadTask";

    private static final String DOWNLOAD_APK_NAME = "SohuVod.apk";

    private AboutView mAboutView;

    private Context mContext;

    private int mNetworkResponseCode;

    private int mUpdateStatus;

    public UpdateTask(Context context, AboutView aboutView) {
        this.mAboutView = aboutView;
        this.mContext = context;
    }

    @Override
    protected Boolean doInBackground(UpdateInfo... params) {
        if (params[0] != null) {
            mUpdateStatus = params[0].data.status;
            return DownloadUtil.downloadFile(this, params[0].data.downloadUrl.toString(),
                    Util.getApkDownloadPath(mContext));
        } else {
            return false;
        }
    }

    private Integer downProgress=-1;
    @Override
    protected void onProgressUpdate(Integer... values) {
        super.onProgressUpdate(values);
        if (!Objects.equals(downProgress, values[0])){
            downProgress=values[0];
            AppLogger.d(TAG, "downloaded value = " + values[0]);
            mAboutView.updateDownloadProgress(values[0]);
        }
    }

    @Override
    protected void onPostExecute(Boolean result) {
        super.onPostExecute(result);
        mAboutView.responseDownloadResult(result, mUpdateStatus, mNetworkResponseCode);
    }

    @Override
    public void setNetworkResponseCode(int networkResponseCode) {
        this.mNetworkResponseCode = networkResponseCode;
    }

    @Override
    public void updateProgress(int value) {
        publishProgress(value);
    }
}
