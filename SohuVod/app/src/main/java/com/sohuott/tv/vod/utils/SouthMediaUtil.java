package com.sohuott.tv.vod.utils;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sohu.ott.base.lib_user.UserApp;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.app.SohuAppUtil;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.EncryUtils;
import com.sohuott.tv.vod.lib.utils.SystemUtils;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.view.SouthMediaCheckFailDialog;

import org.dom4j.DocumentHelper;

import java.text.SimpleDateFormat;
import java.util.HashMap;

import cn.gd.snmottclient.SNMOTTClient;
import cn.gd.snmottclient.util.SNMOTTSDKCallBack;

/**
 * Created by yizhang210244 on 2017/12/26.
 */

public class SouthMediaUtil {
    private static HashMap<String, String> params = new HashMap();
    private static Gson gson = new GsonBuilder().disableHtmlEscaping().create();
    private static boolean SNM_CHECK_FLAG = false;
    public static final String SDK_VERSION = "5.2";
    public static Handler mHandler = new Handler(Looper.getMainLooper());

    public static void southNewMediaCheck(final Context context) {
        if (SNM_CHECK_FLAG) {
            LibDeprecatedLogger.w("Have been checked!");
            return;
        }
        SNM_CHECK_FLAG = true;
        String params = gson.toJson(getParams(context));
//        Log.e("SNM","snm login params ：" + params);
        SNMOTTClient.getInstance().getData("login", params, new SNMOTTSDKCallBack() {
            @Override
            public void onSuccess(String result) {
                if (result != null && !"".equals(result)) {
                    LibDeprecatedLogger.d("南传启动回调成功 : " + result);
                    String flag = null;
                    try {
                        flag = DocumentHelper.parseText(result).getRootElement().elementText("Result");
                    } catch (Throwable e) {
                        e.printStackTrace();
                        LibDeprecatedLogger.d("SNM login parseText Fail! Throwable=" + e.getLocalizedMessage().toString());
                    }
                    LibDeprecatedLogger.d("SNM login register  flag:" + flag);
                    if ("998".equals(flag)) {
                        SouthMediaCheckFail(context);
                    } else {
                        LibDeprecatedLogger.d("SNM login register success!");
                    }
                } else {
                    //失败
                    SouthMediaCheckFail(context);
                }
            }

            @Override
            public void onFailure(String s) {
                // 失败回调
                LibDeprecatedLogger.d("南传启动回调失败 : " + s);
            }
        });
    }

    public static final HashMap getParams(Context context) {
        String userId = UserInfoHelper.getGid();
        String account = "SNM_" + userId;
        String productLine = "SNMYT";
        String channel = Util.getPartnerNo(context);
        if (TextUtils.isEmpty(channel)) {
            channel = "";
        }
        String mac = UserInfoHelper.getUserDeviceMacAddress();
        if (TextUtils.isEmpty(mac)) {
            mac = "";
        } else {
            mac = mac.replace("%3A", ":");
        }
        LibDeprecatedLogger.d("mac : " + mac);
        String appVersionCode = String.valueOf(Util.getVersionCode(context));
        String appVersionName = Util.getVersionName(context);

        String resolution = String.valueOf(SystemUtils.getScreenWidth(context)) + "*" + SystemUtils.getScreenHeight(context);
        String vendorInfo = android.os.Build.MANUFACTURER;
        String modelNo = android.os.Build.MODEL;
        String boardNo = Build.BOARD;
        String platformVer = Build.PRODUCT;
        String androidVer = android.os.Build.VERSION.RELEASE;

        String ip = Util.getIpv4Addr(context);
        String AES_IV = "adjiganaadjigana";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        long timestamp = System.currentTimeMillis();
        String formattedDate = sdf.format(timestamp);


        params.put("Source", "snm_sohu");
        params.put("sdkver", SDK_VERSION);
        params.put("productLine", productLine);
        params.put("channel", channel);
        params.put("account", account);
        params.put("LoginAccount", userId);
        params.put("Mac", EncryUtils.AesDesEncrypt(mac, UserApp.Config.SNM_AES_SECRET, AES_IV, EncryUtils.Encryption.AES, EncryUtils.EncryptMode.ECB));
        params.put("wifimac", EncryUtils.AesDesEncrypt(mac, UserApp.Config.SNM_AES_SECRET, AES_IV, EncryUtils.Encryption.AES, EncryUtils.EncryptMode.ECB));
        params.put("ip", EncryUtils.AesDesEncrypt(ip, UserApp.Config.SNM_AES_SECRET, AES_IV, EncryUtils.Encryption.AES, EncryUtils.EncryptMode.ECB));
        params.put("appVersionName", appVersionName);
        params.put("apkver", appVersionCode);
        params.put("vendorinfo", vendorInfo);
        params.put("modelno", modelNo);
        params.put("boardno", boardNo);
        params.put("resolution", resolution);
        params.put("u1_ramSize", Util.getTotalRam());
        params.put("u1_flashSize", Util.getTotalRom());
        params.put("androidVer", androidVer);
        params.put("TimeStamp", formattedDate);
        return params;
    }

    private static void SouthMediaCheckFail(Context context) {
        mHandler.post(()->{
            SouthMediaCheckFailDialog dialog = new SouthMediaCheckFailDialog(context);
            dialog.show();
            dialog.setOkButtonListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    SohuAppUtil.exitApp(context);
                }
            });
            dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
                @Override
                public void onCancel(DialogInterface dialog) {
                    SohuAppUtil.exitApp(context);
                }
            });

            ToastUtils.showToast2(context, "云视听播控平台认证失败");
            LibDeprecatedLogger.d("SNM login register fail! 998");
        });
    }
}
