package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.FilterBean;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by rita on 16-2-1.
 */
public class NewFilterAdapter extends RecyclerView.Adapter<NewFilterAdapter.ViewHolder> {

    private FocusBorderView mFocusView;

    private List<FilterBean.DataEntity> mFilterData;
    private List<Integer> mFilterValue;
    private int mSelectedPos = 0;

    public int getFocusPos() {
        return mFocusPos;
    }

    public void setFocusPos(int mFocusPos) {
        this.mFocusPos = mFocusPos;
    }

    private int mFocusPos = -1;
    private Context mContext;
    private FilterItemAdapter.SelectItemChangeCallback mCallback;


    public NewFilterAdapter(Context context) {
        mContext = context;
        mFilterData = new ArrayList<>();
        mFilterValue = new ArrayList<>();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(
                R.layout.new_filter_list, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        holder.titleView.setText(mFilterData.get(position).name);
        holder.adapter.add(mFilterData.get(position));
        holder.adapter.setSelectPos(mFilterValue.get(holder.getAdapterPosition()));
        if (mSelectedPos == position) {
            holder.adapter.hasFocus(true);
        } else {
            holder.adapter.hasFocus(false);
        }
    }

    @Override
    public int getItemCount() {
        if (mFilterData == null) {
            return 0;
        }
        return mFilterData.size();
    }

    public void setFocusView(FocusBorderView focusView) {
        this.mFocusView = focusView;
    }

    public void add(List<FilterBean.DataEntity> models) {
        mFilterData.addAll(models);
        notifyDataSetChanged();
    }

    public void addValueList(List<Integer> models) {
        mFilterValue.addAll(models);
        notifyDataSetChanged();
    }

    public void setOnSelectChangeListener(FilterItemAdapter.SelectItemChangeCallback callback) {
        mCallback = callback;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private TextView titleView;
        public RecyclerView recyclerView;
        private CustomLinearLayoutManager manager;
        public FilterItemAdapter adapter;

        public ViewHolder(View v) {
            super(v);

            titleView = (TextView) itemView.findViewById(R.id.filter_cat);
            recyclerView = (RecyclerView) itemView.findViewById(R.id.filter_content);
            manager = new CustomLinearLayoutManager(mContext);
            manager.setOrientation(LinearLayoutManager.HORIZONTAL);
//            manager.setCustomPadding(mContext.getResources().getDimensionPixelSize(R.dimen.x52),
//                    mContext.getResources().getDimensionPixelSize(R.dimen.x207));
            adapter = new FilterItemAdapter(recyclerView);
            adapter.setFocusView(mFocusView);
            recyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            recyclerView.setLayoutManager(manager);
            recyclerView.setAdapter(adapter);
            recyclerView.setItemViewCacheSize(0);
            if (mCallback != null) {
                adapter.setOnSelectChangeListener(mCallback);
            }
        }
    }

    public void releaseAll() {
        mFocusView = null;
        if (mFilterData != null) {
            mFilterData.clear();
            mFilterData = null;
        }
        if (mFilterValue != null) {
            mFilterValue.clear();
            mFilterValue = null;
        }
        mContext = null;
        mCallback = null;
    }
}
