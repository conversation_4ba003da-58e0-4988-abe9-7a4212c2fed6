package com.sohuott.tv.vod.ui;

import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.widget.TextView;

import java.lang.ref.WeakReference;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by fengle<PERSON> on 15-8-7.
 */
public class TimerView extends TextView {

    public static final int MSG_TIME = 0;

    private TopBarHandler mTopBarHandler;

    static class TopBarHandler extends Handler {

        private WeakReference<TimerView> topBarViewWeakReference;

        public TopBarHandler(TimerView topBarView) {
            super();
            this.topBarViewWeakReference = new WeakReference<>(topBarView);
        }

        @Override
        public void handleMessage(Message msg) {
            TimerView topBarView = topBarViewWeakReference.get();
            if(topBarView != null) {
                switch (msg.what) {
                    case MSG_TIME:
                        topBarView.updateCurrentTime();
                        topBarView.postTimeMsg();
                        break;
                    default:
                        break;
                }
            }
        }
    }

    public TimerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mTopBarHandler = new TopBarHandler(this);
        updateCurrentTime();
        postTimeMsg();
    }

    public void updateCurrentTime() {
        Calendar currentTime = Calendar.getInstance();
        currentTime.setTime(new Date());
        int sysHour = currentTime.get(Calendar.HOUR_OF_DAY);
        int sysMinute = currentTime.get(Calendar.MINUTE);
        String hourStr = String.format("%2d:", sysHour);
        String minuteStr;
        if (sysMinute < 10) {
            minuteStr = String.format("0%d", sysMinute);
        } else {
            minuteStr = String.format("%2d", sysMinute);
        }
        setText(hourStr + minuteStr);
    }

    private void postTimeMsg() {
        Message m = mTopBarHandler.obtainMessage(MSG_TIME);
        Calendar c = Calendar.getInstance();
        int sec = c.get(Calendar.SECOND);
        long uptimeMillis = (60 - sec) * 1000;
        mTopBarHandler.sendMessageDelayed(m, uptimeMillis);
    }

}
