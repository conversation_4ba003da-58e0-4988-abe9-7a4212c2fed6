package com.sohuott.tv.vod.activity

import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.lib_viewbind_ext.viewBinding
import com.sohu.lib_utils.FormatUtils
import com.sohu.ott.base.lib_user.HeaderHelper.getHeaders
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohu.ott.base.lib_user.UserInfoHelper.getGid
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.account.common.AccountService
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.base_router.RouterPath
import com.sohuott.tv.vod.databinding.ActivityIssuesFeedbackBinding
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.ToastUtils
import com.sohuott.tv.vod.lib.utils.UrlWrapper
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.service.LoggerUpload
import com.sohuott.tv.vod.service.LoggerUpload.LoggerUploadCallBack
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response


/**
 * 问题反馈
 */
@Route(path = RouterPath.Setting.ISSUES_FEEDBACK_ACTIVITY)
class IssuesFeedbackActivity : AppCompatActivity() {
    private val QRCODE_PICTURE_SIZE: Int = 400

    private val TAG = "IssuesFeedbackActivity"

    private val mViewBinding: ActivityIssuesFeedbackBinding by viewBinding(
        ActivityIssuesFeedbackBinding::bind
    )

    // 通过name来映射URL中的不同参数
    @JvmField
    @Autowired(name = "reportTime")
    var reportTime: Long = 0
    override fun onCreate(savedInstanceState: Bundle?) {
        ARouter.getInstance().inject(this)
        super.onCreate(savedInstanceState)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        setContentView(R.layout.activity_issues_feedback)
        mViewBinding.pbLoading.show()
        mViewBinding.error.gone()
        mViewBinding.container2.gone()
        isOnReport = false
        issuesPush()
    }

    private fun initSuccessView() {
        mViewBinding.content1.text =
            applicationContext.resources.getString(R.string.txt_activity_feedback_vertion)
                .plus("V").plus(Util.getVersionName(this))
        mViewBinding.content2.text =
            applicationContext.resources.getString(R.string.txt_activity_feedback_report_time)
                .plus(FormatUtils.formatDate5(reportTime))
        mViewBinding.content3.text =
            (applicationContext.resources.getString(R.string.txt_activity_feedback_gid)
                .plus(UserInfoHelper.getGid()))
        mViewBinding.content4.text = (applicationContext.resources
            .getString(R.string.txt_activity_feedback_problem_id)
            .plus(UserInfoHelper.getGid()).plus(reportTime))
    }


    private var isOnReport = false

    private fun issuesPush() {
        if (isOnReport) {
            ToastUtils.showToast(this@IssuesFeedbackActivity, "日志已经正在上报中，请稍后！")
            //正在上报提示
            return
        }
        isOnReport = true
        reportTime = System.currentTimeMillis()
        LoggerUpload.getInstants().release()
        LoggerUpload.getInstants().push(reportTime, object : LoggerUploadCallBack {
            override fun onError(errorType: Int) {
                isOnReport = false
                mViewBinding.pbLoading.hide()
                mViewBinding.container2.gone()
                mViewBinding.error.visible()
                if (errorType == LoggerUpload.FEEDBACK_UPLOAD_ERROR_NETWORK) {
                    mViewBinding.error.text="反馈过于频繁，请稍后再试。"
                    ToastUtils.showToast(this@IssuesFeedbackActivity, "反馈过于频繁，请稍后再试。")
                } else {
                    mViewBinding.error.text="反馈报错，请稍后再试。"
                    ToastUtils.showToast(this@IssuesFeedbackActivity, "反馈报错，请稍后再试。")
                }
            }

            override fun onSuccess() {
                ToastUtils.showToast(this@IssuesFeedbackActivity, "日志已上传，请描述详细问题")
                isOnReport = false
                mViewBinding.pbLoading.hide()
                mViewBinding.container2.visible()
                mViewBinding.error.gone()
                initSuccessView()
                requestQrImage()
            }
        })
    }

    private fun requestQrImage() {
        val url = UrlWrapper.getFeedbackQrCodeUrl(
            QRCODE_PICTURE_SIZE,
            QRCODE_PICTURE_SIZE,
            getGid(),
            UserInfoHelper.getGid().plus(reportTime),
            "",
            Constant.UPLOAD_TYPE_2
        )
        //        new NormalLoadPictrue(this).getPicture(mQrCodeImageUrl, mQrCodeImage);
        AccountService(applicationContext).getQrImage(
            getHeaders(),
            url,
            object : Callback<ResponseBody> {
                override fun onResponse(
                    call: Call<ResponseBody>,
                    response: Response<ResponseBody>
                ) {
                    if (response.isSuccessful) {
                        try {
                            val bitmap = BitmapFactory.decodeStream(response.body()!!.byteStream())
                            mViewBinding.qrcodeImage.setImageBitmap(bitmap)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                }
            })
        AppLogger.d(TAG, "report image url :$url")
    }

    //设置窗口大小
    private fun setDisplay() {
        //设置弹出窗口与屏幕对齐
        val win = this.window
        val density = (resources.displayMetrics.density).toInt()
        //设置内边距，这里设置为0
        win.decorView.setPadding(1 * density, 1 * density, 1 * density, 1 * density)
        val lp = win.attributes
        //设置窗口宽度
        lp.width = resources.getDimensionPixelSize(R.dimen.x600)
        //设置窗口高度
        lp.height = resources.getDimensionPixelSize(R.dimen.y400)
        //设置Dialog位置
        lp.gravity = Gravity.CENTER
        win.attributes = lp
    }
}