package com.sohuott.tv.vod.adapter;

import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.view.FocusBorderView;

/**
 * Created by yizhang210244 on 2017/12/29.
 */

public abstract class PersonalCinemaCommonAdapter extends RecyclerView.Adapter{

    protected int mSelectedPosition = 0;
    protected FocusBorderView mFocusBorderView;
    protected RecyclerView mRootRecyclerView;
    protected RecyclerView mParentRecyclerView;

    public int getSelectedPosition() {
        return mSelectedPosition;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        mFocusBorderView = focusBorderView;
    }

    @Override
    public long getItemId(int position) {
        return position +100;
    }
}
