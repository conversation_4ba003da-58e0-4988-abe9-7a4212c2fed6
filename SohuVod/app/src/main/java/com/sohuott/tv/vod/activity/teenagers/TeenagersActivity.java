package com.sohuott.tv.vod.activity.teenagers;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.GridLayoutManager;
import androidx.leanback.widget.ItemBridgeAdapter;
import androidx.leanback.widget.VerticalGridView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.ListHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.BaseActivity;
import com.sohuott.tv.vod.activity.TeenagerLockActivity;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.fragment.lb.HomeContentFragment;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.model.TeenModeDesc;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.presenter.launcher.selector.ContentPresenterSelector;
import com.sohuott.tv.vod.utils.ActivityLauncher;

import java.lang.ref.WeakReference;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;


/**
 * 青少年模式页面
 */
public class TeenagersActivity extends BaseActivity implements View.OnClickListener {

    /**
     * 请求参数  id
     */
    private int mRequestTabCode;
    /**
     * 加载动画
     */
    private LoadingView mPbLoading;
    /**
     * 错误文案显示控件
     */
    private TextView mTvError;
    /**
     * 展示列表
     */
    private VerticalGridView mVerticalGridView;
    /**
     * 返回按钮文字
     */
    private TextView mTvBack;
    /**
     * 返回布局
     */
    private LinearLayout mLlBack;

    public static final int REFRESH_DURATION = 1000 * 60 * 30;

    public static final int REFRESH_WHAT = 0xe110;

    public RefreshHandler refreshHandler;

    /**
     * 返回icon
     */
//    private ImageView mIvBack;

    private ConstraintLayout mTitleBar;

    /**
     * 用户帮助类
     */
    private LoginUserInformationHelper mHelper;

    /**
     * adapter
     */
    private ArrayObjectAdapter mAdapter;

    /**
     * 数据列表帮助类
     * 来源{@link HomeContentFragment }
     */
    private ListHelper mListHelper;

    /**
     * 只有初次加载才会显示加载框
     */
//    private Boolean isFirst = false;

    private ItemBridgeAdapter itemBridgeAdapter;

    private View mView;

    private Boolean isBackFocus = false;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        setContentView(R.layout.activity_teenagers);
        initIntent();
        refreshHandler = new RefreshHandler(this);
        initView();
        initAdapter();
        initListener();
        loadData();
        AppLogger.INSTANCE.v("onCreate");
    }

    @Override
    protected void onResume() {
        super.onResume();
        sendRefreshMessage();
        pushExposure();
        AppLogger.INSTANCE.v("onResume");
    }

    @Override
    protected void onStop() {
        super.onStop();
        refreshHandler.removeMessages(REFRESH_WHAT);
        AppLogger.INSTANCE.v("onStop");
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
//        isFirst = true;
    }

    /**
     * 初始化view
     */
    private void initView() {
        mPbLoading = (LoadingView) findViewById(R.id.pb_loading);
        mTitleBar = (ConstraintLayout) findViewById(R.id.cl_teenagers_title_bar);
        mTvBack = (TextView) findViewById(R.id.tv_teenagers_back);
//        mIvBack = (ImageView) findViewById(R.id.iv_teenagers_back);
        mLlBack = (LinearLayout) findViewById(R.id.ll_teenagers_back);
        mTvError = (TextView) findViewById(R.id.tv_error);
        mView = (View) findViewById(R.id.teenager_view);
        mVerticalGridView = (VerticalGridView) findViewById(R.id.leanback_teenagers_vg);
        //设置间距
        mVerticalGridView.setVerticalSpacing(48);
        //设置动画
        mVerticalGridView.setItemAnimator(null);
//        mTitleBar.setFocusableInTouchMode(true);
        mHelper = LoginUserInformationHelper.getHelper(this);
    }

    /**
     * 初始化列表适配器
     */
    private void initAdapter() {
        ContentPresenterSelector presenterSelector = new ContentPresenterSelector();
        mAdapter = new ArrayObjectAdapter(presenterSelector);
        itemBridgeAdapter = new ItemBridgeAdapter(mAdapter);
        mVerticalGridView.setAdapter(itemBridgeAdapter);
    }

    /**
     * 初始化监听
     */
    private void initListener() {
        mLlBack.setOnClickListener(this);
        mVerticalGridView.addOnScrollListener(onScrollListener);
        mLlBack.setOnFocusChangeListener(backFocusChangeListener);

    }

    /**
     * 返回按钮焦点变化监听
     */
    private View.OnFocusChangeListener backFocusChangeListener = new View.OnFocusChangeListener() {
        @Override
        public void onFocusChange(View back, boolean hasFocus) {
            int backTvColor;
            Drawable backIvSrc;
            isBackFocus = hasFocus;
            if (hasFocus) {
                backIvSrc = ContextCompat.getDrawable(TeenagersActivity.this, R.drawable.ic_sign_out_focus_true);
                backTvColor = ContextCompat.getColor(TeenagersActivity.this, R.color.teenagers_text_title);
            } else {
                backIvSrc = ContextCompat.getDrawable(TeenagersActivity.this, R.drawable.ic_sign_out_focus_false);
                backTvColor = ContextCompat.getColor(TeenagersActivity.this, R.color.teenagers_text_title_50);
            }
//            mIvBack.setImageDrawable(backIvSrc);
            mTvBack.setTextColor(backTvColor);
            mTvBack.setCompoundDrawablesWithIntrinsicBounds(backIvSrc, null, null, null);
        }
    };

    /**
     * 列表滑动监听
     */
    private RecyclerView.OnScrollListener onScrollListener
            = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            switch (newState) {
                //当屏幕滚动且用户使用的触碰或手指还在屏幕上，停止加载图片
                case RecyclerView.SCROLL_STATE_DRAGGING:
                    //由于用户的操作，屏幕产生惯性滑动，停止加载图片
                case RecyclerView.SCROLL_STATE_SETTLING:
                    Glide.with(TeenagersActivity.this).pauseRequests();
                    break;
                case RecyclerView.SCROLL_STATE_IDLE:
                    Glide.with(TeenagersActivity.this).resumeRequests();
            }
        }
    };


    /**
     * 获取页面传递Intent数据
     */
    private void initIntent() {
        mRequestTabCode = (int) getIntent().getLongExtra(HomeContentFragment.BUNDLE_KEY_TAB_CODE, -1L);
    }

    /**
     * 上传页面曝光
     */
    private void pushExposure() {
        TeenagersManger.getInstance().exposureTeenagerPage();
    }

    /**
     * 加载数据
     */
    private void loadData() {
//        if (isFirst) {
        setLoadingUi();
//        } else {
//            setTransparentView(true);
//        }
        //当tableId为-1 从服务器获取tableId 之后再请求
        if (mRequestTabCode == -1) {
            requestTeenModeDesc();
            return;
        }
        NetworkApi.getHomeContent(mRequestTabCode, mHelper.getLoginPassport(), mHelper.getLoginToken(),1, 30, new Observer<ContentGroup>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(ContentGroup value) {
                if (value == null || value.data.isEmpty()) {
                    setErrorTvUi();
                } else {
                    setData(value);

                    mTvError.setVisibility(View.GONE);
                    mPbLoading.setVisibility(View.GONE);
                    mVerticalGridView.setVisibility(View.VISIBLE);
//                    if (isFirst) {
//                        isFirst = false;
//                    } else {
//                        setTransparentView(false);
//                    }

//                    mTitleBar.setFocusableInTouchMode(false);
                    //动态设置为了 初次进入不抢焦点
                    mLlBack.setFocusableInTouchMode(true);
                    mLlBack.setFocusable(true);
                    //是为了当退出获取焦点下面代码 列表不去抢焦点可以停留到退出按钮上
                    if (isBackFocus) {
                        return;
                    }
                    //不加post 部分可能会导致动画不显示
                    mVerticalGridView.post(new InnerRunnable(TeenagersActivity.this));
//                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                setErrorTvUi();
                e.printStackTrace();
            }

            @Override
            public void onComplete() {
                AppLogger.INSTANCE.v("initView: mVerticalGridView.requestFocus()");

            }
        });
    }

    private static class InnerRunnable implements Runnable {
        private WeakReference<TeenagersActivity> mWrapper;
        InnerRunnable(TeenagersActivity teenagersActivity){
            mWrapper = new WeakReference<>(teenagersActivity);
        }
        @Override
        public void run() {
            TeenagersActivity teenagersActivity = mWrapper.get();
            if (teenagersActivity != null){
                teenagersActivity.mVerticalGridView.requestFocus();
            }
        }
    }

    /**
     * 当tableId为-1 从服务器获取tableId 之后再请求
     */
    private void requestTeenModeDesc() {
        NetworkApi.getTeenModeDesc(new DisposableObserver<TeenModeDesc>() {
            @Override
            public void onNext(TeenModeDesc value) {
                if (value != null && value.data != null) {
                    mRequestTabCode = value.data.teen_channel_id;
                    loadData();
                } else {
                    setErrorTvUi();
                }
            }

            @Override
            public void onError(Throwable e) {
                setErrorTvUi();
            }

            @Override
            public void onComplete() {
            }
        });
    }

    /**
     * 设置及更新数据
     *
     * @param data 数据源
     */
    private void setData(ContentGroup data) {

        mAdapter.clear();
        AppLogger.INSTANCE.v("id：" + mRequestTabCode);
        if (mListHelper == null) {
            mListHelper = new ListHelper(this, mRequestTabCode, mVerticalGridView, mAdapter);
            mListHelper.setMode(true);
        }
        mListHelper.assemblyListData(data);
    }

    /**
     * 加载中展示页面
     */
    private void setLoadingUi() {
        mTvError.setVisibility(View.GONE);
        mPbLoading.setVisibility(View.VISIBLE);
        mVerticalGridView.setVisibility(View.INVISIBLE);
    }

    /**
     * 设置错误页面
     */
    private void setErrorTvUi() {
        mPbLoading.hide();
        mTvError.setText(getString(R.string.home_loading_error));
        mTvError.setVisibility(View.VISIBLE);
    }

    private void setTransparentView(Boolean isVisible) {
        int visible;
        if (isVisible) {
            visible = View.VISIBLE;
        } else {
            visible = View.GONE;
        }
        mView.setVisibility(visible);
    }


    /**
     * 点击事件
     *
     * @param v view
     */
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            //退出
            case R.id.ll_teenagers_back:
                TeenagersManger.getInstance().exposureTeenagerExitClick();
                goToLockActivity();
                break;
        }
    }

    /**
     * 清空内存
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        backFocusChangeListener = null;
        onScrollListener = null;
        mHelper = null;
        if (null != mAdapter) {
            mAdapter.clear();
            mAdapter = null;
        }
        mListHelper = null;
        if (null != mPbLoading) {
            mPbLoading.clearAnimation();
        }
    }

    @Override
    public void onBackPressed() {
//        super.onBackPressed();
        boolean isTop = true;
        if (mVerticalGridView.getFocusedChild() != null) {
            isTop = mVerticalGridView.getLayoutManager().getPosition(mVerticalGridView.getFocusedChild()) == 0;
        }
        // 按返回键回到顶部
        if (isTop) {
            goToLockActivity();
            // 在顶部时退出
        } else {
            // 不在顶部
            mVerticalGridView.scrollToPosition(0);
        }
    }

    /**
     * 退出回到正常模式首页
     */
    private void exit() {
        TeenagersManger.clearTeenagerPassword(TeenagersActivity.this);
        TeenagersManger.getInstance().putTimeout(0L, TeenagersActivity.this);
        ActivityLauncher.startHomeActivity(TeenagersActivity.this, false);
        TeenagersManger.putTeenagerIntervalTime(TeenagersActivity.this, "");
        ToastUtils.showToast(TeenagersActivity.this, "已退出青少年模式");
        finish();
    }

    private void goToLockActivity() {
        ActivityLauncher.startChildLockActivity(TeenagersActivity.this, TeenagerLockActivity.TYPE_FINISH, TeenagerLockActivity.REQUEST_CODE_FINISH, (long) mRequestTabCode);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == TeenagerLockActivity.REQUEST_CODE_FINISH && resultCode == TeenagerLockActivity.RESULT_CODE_FINISH) {
            exit();
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     * 跳转青少年页面
     *
     * @param context 上下文
     * @param id      青少年tab Id
     */
    public static void actionStart(Context context, Long id) {
        Intent intent = new Intent(context, TeenagersActivity.class);
        intent.putExtra(HomeContentFragment.BUNDLE_KEY_TAB_CODE, id);
        context.startActivity(intent);
    }

    private void sendRefreshMessage() {
        refreshHandler.sendEmptyMessageDelayed(REFRESH_WHAT, REFRESH_DURATION);
    }

    private final static class RefreshHandler extends Handler {
        private WeakReference<TeenagersActivity> weakReference;

        public RefreshHandler(TeenagersActivity activity) {
            weakReference = new WeakReference<TeenagersActivity>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            TeenagersActivity activity = weakReference.get();
            switch (msg.what) {
                case REFRESH_WHAT:
                    activity.loadData();
                    activity.sendRefreshMessage();
                    break;
            }
        }
    }

}