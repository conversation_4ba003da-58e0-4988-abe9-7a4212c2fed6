package com.sohuott.tv.vod.videodetail.activity

import android.content.Context
import android.text.TextUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.sh.ott.video.ShPlayerConfig
import com.sh.ott.video.ad.AdTsManger
import com.sh.ott.video.ad.RequestAdTs
import com.sh.ott.video.base.ResolutionServer
import com.sh.ott.video.base.component.Resolution
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.base.SystemPlayerFactory
import com.sh.ott.video.player.sofa.SofaPlayerFactory
import com.sh.ott.video.util.serverValueTransformMedia
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohu.ott.lib_widget.SoHuVideoProgressBar
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger
import com.sohuott.tv.vod.app.config.AppConfigDatabase
import com.sohuott.tv.vod.app.config.AppPlayerConfig
import com.sohuott.tv.vod.app.config.ResolutionInfo
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.AlbumInfo
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo
import com.sohuott.tv.vod.lib.model.PgcPlayList
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.videodetail.activity.model.VideoPlayInfo
import com.sohuott.tv.vod.videodetail.activity.state.ResolutionApp
import com.sohuott.tv.vod.videodetail.activity.state.VideoDetailViewResult
import com.sohuott.tv.vod.videodetail.activity.state.VideoInfoResponse
import com.sohuott.tv.vod.videodetail.activity.state.VideoPlayResolutionInfo
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.VideoInfoOnlySeeItem
import java.util.Arrays
import kotlin.math.min


fun setPlayerChange(context: Context, type: Int) {
    if (type == 1) {
        PlaySettingHelper.setPlaySpeedIsOpen(false)
        PlaySettingHelper.setNeedSkipHeaderAndEnd(false)
        Util.setPlayParams(context, 1)
        ShPlayerConfig.filmPlayerFactory = SystemPlayerFactory.create()
    } else {
        PlaySettingHelper.setPlaySpeedIsOpen(true)
        PlaySettingHelper.setNeedSkipHeaderAndEnd(true)
        Util.setPlayParams(context, 0)
        ShPlayerConfig.filmPlayerFactory = SofaPlayerFactory.create()
    }
}

/**
 * 查找清晰度设置
 */
fun getPlayResolution(): Int {
    var clarity = PlaySettingHelper.getPlayClarity()
    LibDeprecatedLogger.d("清晰度设置 clarity: ${clarity.toString()}")
    var resolution = ResolutionApp.entries.find {
        clarity == it.appValue
    }
    return resolution?.appValue ?: ResolutionApp.APP_HIGH.appValue
}


/**
 * 转换只看他数据并初次查找
 * @param this lookHimSet
 * @param currentOnlySeeMenuItem 当前只看他选中数据
 * @
 */
inline fun String?.conversionOnlySeeData(
    currentOnlySeeMenuItem: ScaleContentOnlySeeMenuItem?,
    crossinline action: (videoInfoOnlySeeDatas: MutableList<VideoInfoOnlySeeItem>?, haveOnlySee: Boolean, isFind: Boolean, findItem: ScaleContentOnlySeeMenuItem?, proSeeList: MutableList<SoHuVideoProgressBar.SecondListBean>, onlySeeList: MutableList<ScaleContentOnlySeeMenuItem>) -> Unit
) {
    var videoInfoOnlySeeDatas: MutableList<VideoInfoOnlySeeItem>? = null
    if (this?.isNotEmpty() == true) {
        videoInfoOnlySeeDatas = Gson().fromJson<MutableList<VideoInfoOnlySeeItem>>(
            this,
            object : TypeToken<MutableList<VideoInfoOnlySeeItem?>?>() {}.type
        )
    } else {
        videoInfoOnlySeeDatas = null
    }
    val haveOnlySee = videoInfoOnlySeeDatas != null
    if (!haveOnlySee) {
        action(videoInfoOnlySeeDatas, false, false, null, mutableListOf(), mutableListOf())
    } else {
        currentOnlySeeMenuItem.findOnlySeeDataItem(
            videoInfoOnlySeeItem = videoInfoOnlySeeDatas!!
        ) { isFind, findItem, proSeeList, onlySeeList ->
            action(videoInfoOnlySeeDatas, true, isFind, findItem, proSeeList, onlySeeList)
        }
    }
}

/**
 * 查找只看他数据
 * @param videoInfoOnlySeeItem 只看他服务器json 数据
 * @param  this currentOnlySeeMenuItem 当前只看他选中数据
 * @param action 返回数据
 */
inline fun ScaleContentOnlySeeMenuItem?.findOnlySeeDataItem(
    videoInfoOnlySeeItem: MutableList<VideoInfoOnlySeeItem>,
    action: (isFind: Boolean, findItem: ScaleContentOnlySeeMenuItem?, proSeeList: MutableList<SoHuVideoProgressBar.SecondListBean>, onlySeeList: MutableList<ScaleContentOnlySeeMenuItem>) -> Unit
) {
    val proSeeList: MutableList<SoHuVideoProgressBar.SecondListBean> = mutableListOf()
    val onlySeeList: MutableList<ScaleContentOnlySeeMenuItem> = mutableListOf()
    val oldId = this?.id ?: "0"
    var findOnlySeeMenuItem: ScaleContentOnlySeeMenuItem? = null
    var finIndex = -1
    if (videoInfoOnlySeeItem != null) {
        val firstItem = ScaleContentOnlySeeMenuItem()
        firstItem.id = "0"
        firstItem.name = "观看完整版"
        var selectId = oldId
        firstItem.hasCurrentSelected = selectId == "0"
        onlySeeList.add(firstItem)
        for (data in videoInfoOnlySeeItem!!) {
            var starId = data.starId
            var name1 = data.name
            var minute = data.minute
            var imgUrl = data.imgUrl
            var timeArray = data.timeArray
            val item = ScaleContentOnlySeeMenuItem()
            var isFind = starId == selectId
            //设置本片段时间
            if (minute != 0L) {
                val min = minute / 60
                val Sec = minute % 60
                val minStr = ""
                val SecStr = ""
                item.allTime =
                    "本集共" + appendOnlySeeAllTime(min) + ":" + appendOnlySeeAllTime(
                        Sec
                    )
            }
            //设置唯一id
            item.id = starId
            //转换明星姓名集合
            val names: List<String> = getOnlySeeStringToListData(str = name1)
            //拼接明星姓名
            if (names.size > 1) {
                val mName = StringBuilder()
                for (index in names.indices) {
                    val name = names[index]
                    if (names.size - 1 == index) {
                        mName.append(name).append(" 片段")
                    } else {
                        mName.append(name).append("&")
                    }
                }
                item.name = mName.toString()
            } else {
                item.name = name1 + " 片段"
            }
            //设置并转换 头像地址集合
            item.imagesUrl = getOnlySeeStringToListData(imgUrl)
            //设置 开始到结束片段 集合
            item.timeArray = timeArray
            item.hasCurrentSelected = isFind
            if (isFind) {
                finIndex = 1
                findOnlySeeMenuItem = ScaleContentOnlySeeMenuItem()
                findOnlySeeMenuItem.id = starId
                findOnlySeeMenuItem!!.allTime = item.allTime
                findOnlySeeMenuItem!!.timeArray = item.timeArray
                findOnlySeeMenuItem!!.imagesUrl = item.imagesUrl
                findOnlySeeMenuItem!!.name = item.name
            }
            //添加本条数据
            onlySeeList.add(item)
        }
    }
    val isFind = finIndex == 1
    //没有找到恢复默认全部
    if (!isFind) {
        onlySeeList[0].hasCurrentSelected = true
    }
    proSeeList.clear()
    val isSeeModel = findOnlySeeMenuItem != null && findOnlySeeMenuItem.id != "0"
    if (isSeeModel) {
        val list = findOnlySeeMenuItem!!.timeArray
        if (list != null) {
            for ((start, end) in list) {
                val bean = SoHuVideoProgressBar.SecondListBean()
                //                float start = (data.getStart() * 1.0f / mDuration) * 100f;
//                float end = (data.getEnd() * 1.0f / mDuration) * 100f;
                bean.start = start.toFloat()
                bean.end = end.toFloat()
                proSeeList.add(bean)
            }
        }
    }

    action(finIndex == 1, findOnlySeeMenuItem, proSeeList, onlySeeList)
}

/**
 * 转换 本片段 分/秒所需展示的时间格式 00
 *
 * @param time 分/秒
 * @return 00格式
 */
fun appendOnlySeeAllTime(time: Long): String {
    var SecStr = ""
    SecStr = if (time < 10) {
        "0$time"
    } else {
        time.toString() + ""
    }
    return SecStr
}

// "," 分隔的string 转换 至 list string 数据
fun getOnlySeeStringToListData(str: String): MutableList<String> {
    return Arrays.asList(*str.split(",".toRegex()).dropLastWhile { it.isEmpty() }
        .toTypedArray())
}

//标清：2；高清 ：1；超清 ：21；蓝光 ：31
inline fun findDlnaResolutionApp(definition: String?): Int {
    if (definition.isNullOrEmpty()) {
        return ResolutionApp.APP_HIGH.appValue
    }
    if (definition.equals(ResolutionServer.MEDIA_NORMAL.serverValue264.toString())) {
        return ResolutionApp.APP_NORMAL.appValue
    }
    if (definition.equals(ResolutionServer.MEDIA_HIGH.serverValue264.toString())) {
        return ResolutionApp.APP_HIGH.appValue
    }
    if (definition.equals(ResolutionServer.MEDIA_SUPER.serverValue264.toString())) {
        return ResolutionApp.APP_SUPER.appValue
    }
    if (definition.equals(ResolutionServer.MEDIA_ORIGINAL.serverValue264.toString())) {
        return ResolutionApp.MEDIA_BLUE.appValue
    }
    if (definition.equals(ResolutionServer.MEDIA_ORIGINAL_HDR.serverValue264.toString())) {
        return ResolutionApp.APP_ORIGINAL_HDR.appValue
    }
    if (definition.equals(ResolutionServer.MEDIA_4K.serverValue264.toString())) {
        return ResolutionApp.APP_ORIGINAL_HDR.appValue
    }
    return ResolutionApp.APP_HIGH.appValue
}


inline fun ResolutionInfo.findPlayerResolution(): Resolution {
    val resolutionServer = ResolutionServer.entries.find {
        it.serverValue264 == this.videoPlayInfo?.serviceVersionId || it.serverValue265 == this.videoPlayInfo?.serviceVersionId
    } ?: ResolutionServer.MEDIA_HIGH
    return Resolution.entries.find { it.mediaValue == resolutionServer.mediaValue }
        ?: Resolution.MEDIA_HIGH
}

/**
 * @param this 排序后的清晰度配置列表
 * @return 返回根据配置开关调整的清晰度列表和 查找对应的播放地址等信息
 * @param currentAppId 当前本地/需要获取清晰度值 对应 [ResolutionInfo.id]
 */
inline fun MutableList<ResolutionInfo>.conversionResolution(
    currentAppId: Int,
    isVip: Boolean,
    isLogin: Boolean,
    action: (
        finderInfo: ResolutionInfo?
    ) -> Unit
) {
    var resolutionInfos: MutableList<ResolutionInfo>? = mutableListOf()
    resolutionInfos =
        if (isLogin) {
            if (isVip) {
                //是vip 返回全部
                this
            } else {
                //不是vip 返回 不需要vip的清晰度 isVip配置为false的清晰度
                this.filter { !it.isVip }.toMutableList()
            }
        } else {
            //过滤isLogin=false
            this.filter { !it.isLogin }.toMutableList()
        }
    AppLogger.d("conversionResolution 过滤vip和login条件之后清晰度集合 login:$isLogin isVip:$isVip  :${resolutionInfos.toString()}")
    val finderResolutionInfo = resolutionInfos.find { it.id == currentAppId }
    if (finderResolutionInfo != null) {
        AppLogger.d("conversionResolution 查找到的清晰度为:${finderResolutionInfo.toString()}")
        action(finderResolutionInfo)
    } else {
        val maxIndex = resolutionInfos.lastOrNull()?.priority ?: 0
        var finderNextResolutionInfo: ResolutionInfo? = null
        //从当前清晰度降序至0
        for (index in currentAppId downTo 0) {
            AppLogger.d("conversionResolution  inc:$index")
            val finderInfo = resolutionInfos.find { it.id == index }
            if (finderInfo != null) {
                finderNextResolutionInfo = finderInfo
                AppLogger.d("conversionResolution action ResolutionInfo inc is :${finderNextResolutionInfo.toString()}")
                break
            }
        }
        //从当前清晰度升序
        if (finderNextResolutionInfo == null) {
            for (index in currentAppId..maxIndex) {
                AppLogger.d("conversionResolution dec index:$index")
                val finderInfo = resolutionInfos.find { it.id == index }
                if (finderInfo != null) {
                    finderNextResolutionInfo = finderInfo
                    AppLogger.d("conversionResolution action ResolutionInfo inc is :${finderNextResolutionInfo.toString()}")
                    break
                }
            }
        }
        AppLogger.d("conversionResolution action ResolutionInfo is :${finderNextResolutionInfo.toString()}")
        action(finderNextResolutionInfo)
    }
}


/**
 * 拼接 剧享片头的地址
 */
fun String?.appendAdTsUrl(): String? {
    val tsDate = AdTsManger.getInstants().getAdTsData()
    if (tsDate.hasAdTs) {
        return this?.plus("&adinfo=" + Util.encode(tsDate.ts + "|" + tsDate.tsDuration))
    }
    return this
}

/**
 * 请求广告获取广告ts
 */
fun setRequestAdTs(
    dataType: Int,
    vid: Long?,
    aid: Int?,
    tvInsertAdTime: Double?,
    serviceVersionId: Int,
    isDlna: Boolean
) {
    if (dataType == Constant.DATA_TYPE_VRS && ShPlayerConfig.filmPlayerFactory !is SystemPlayerFactory) {
        val mRequestAdTs = RequestAdTs()

        val is265 = (ResolutionServer.entries.find {
            serviceVersionId == it.serverValue264
        } == null)

        mRequestAdTs.vid = (vid ?: 0).toString()
        mRequestAdTs.aid = aid.toString()
        mRequestAdTs.gid = UserInfoHelper.getGid() ?: ""
        //点位
        mRequestAdTs.point = (tvInsertAdTime ?: 0.0)
        mRequestAdTs.is265=is265
        //清晰度、
        mRequestAdTs.videoClarity =
            serviceVersionId
        mRequestAdTs.isDlna = isDlna
        AdTsManger.getInstants().setRequestAdTsModel(mRequestAdTs)
    } else {
        AdTsManger.getInstants().setRequestAdTsModel(null)
    }
}

/**
 * 转换pgc 播放地址及清晰度
 */
fun List<PgcPlayList.PgcPlayInfo>.conversionPgcVideoPlayResolutionInfo(pgcAlbumInfo: PgcAlbumInfo?): MutableList<VideoPlayResolutionInfo> {
    val infos = mutableListOf<VideoPlayResolutionInfo>()
    if (this.isNotEmpty()) {
        this.forEach { playInfo ->
            infos.add(VideoPlayResolutionInfo().also { playResolution ->
                playResolution.url = playInfo.m3u8PlayUrl
                playResolution.resolution =
                    serverValueTransformMedia(playInfo.versionCode)
                val album = pgcAlbumInfo?.data?.playList?.find {
                    playInfo.versionCode == it.versionId
                }?.let {
                    playResolution.tvVerId = it.tvVerId
                    playResolution.hasLogo = it.hasLogo != 0
                }
            })
        }
    }
    return infos
}


inline fun getViewResult(isVrs: Boolean, albumInfoData: AlbumInfo?): VideoDetailViewResult {
    AppLogger.d("detailViewResult state isVrs:$isVrs albumInfoData")
    if (albumInfoData?.data == null || albumInfoData.status != 0) {
        if (albumInfoData?.status != 50003) {
            AppLogger.d("数据返回异常，请稍候重试 status ${albumInfoData?.status}")
            return VideoDetailViewResult.Fail(errorMessage = "数据返回异常，请稍候重试")
        }
        return VideoDetailViewResult.Fail(errorMessage = "专辑已下线，请观看其他影片。")
    }
    val isShowVip =
        (((albumInfoData.data?.tvIsFee ?: 0) > 0 || (albumInfoData.data?.tvIsEarly
            ?: 0) > 0))
    val name = albumInfoData.data?.tvName

    var maxLine = 2
    var decMaxLine = 2
    if (isVrs) {
        maxLine = 1
        decMaxLine = 2
    } else {
        maxLine = 2
        decMaxLine = 3
    }
    val score =
        if (!TextUtils.isEmpty(albumInfoData.data.scoreSource) && albumInfoData.data?.scoreSource == "1") {
            albumInfoData.data?.score
        } else {
            albumInfoData.data?.doubanScore
        }?.plus("分") ?: ""
    var lable = ""
    var secondCateNames: Array<String> = arrayOf()
    val genreName = albumInfoData.data?.genreName
    if (!genreName.isNullOrEmpty()) {
        if (genreName.contains(";")) {
            secondCateNames = genreName.split(";".toRegex()).toTypedArray()
        } else if (genreName.contains(",")) {
            secondCateNames = genreName.split(",".toRegex()).toTypedArray()
        } else if (genreName.trim { it <= ' ' }.isNotEmpty()) {
            secondCateNames = arrayOf(genreName.trim { it <= ' ' })
        }
    }
    //年份
    val year = albumInfoData.data?.tvYear
    if (!year.isNullOrEmpty() && !TextUtils.equals(year, "0")) {
        lable = lable.plus(year).plus(" | ")
    }
    //类型
    for (index in secondCateNames.indices) {
        lable = if (index == secondCateNames.size - 1) {
            lable.plus(secondCateNames[index])
        } else {
            lable.plus(secondCateNames[index]).plus(" | ")
        }
    }

    //青少年
    val isTeenager: Boolean = TeenagersManger.isTeenager()

    //明星拼接
//            val actors:String = albumInfoData.data?.actors?.forEach {
//                val firstDesc = ""
//                firstDesc.plus(it.name).plus("  ")
//                ""
//            } ?: ""

    //设置文本
    var firstDesc = ""
    albumInfoData.data.actors?.forEach {
        firstDesc = firstDesc.plus(it.name).plus("  ")
    }
    var secondDesc = ""
    if (firstDesc.isNotEmpty()) {
        secondDesc = firstDesc.plus("| ")
    }

    secondDesc = secondDesc.plus(albumInfoData.data.tvDesc ?: "")

    var videoCount = 0
    var episodePlayingVideoOrder = 0
    var hasEpisode = false
    if (isVrs) {
        if (TextUtils.isEmpty(albumInfoData.data.maxVideoOrder)) {
            videoCount = albumInfoData.data.trailerAppendCount
        } else if (albumInfoData.data.cateCode == Constant.CATECODE_MOVIE) {
            // movie
            // 播放dts 时，去掉预告, dts 都是电影
//                    if (mPresenter.isDts()) {
//                        videoCount = albumInfoData.data.maxVideoOrder.toInt()
//                    } else {
            videoCount =
                albumInfoData.data.maxVideoOrder.toInt() + albumInfoData.data.trailerCount
//                    }
        } else {
            videoCount =
                albumInfoData.data.maxVideoOrder.toInt() + albumInfoData.data.trailerAppendCount + albumInfoData.extend.fanwaiCount
        }
        if (albumInfoData.data.cateCode == Constant.CATECODE_VARIETY) {
            episodePlayingVideoOrder = albumInfoData.data.latestVideoCount.toInt()
        }

        if (TextUtils.isEmpty(albumInfoData.data.maxVideoOrder)) {
            hasEpisode = albumInfoData.data.trailerAppendCount > 0
        } else {
            hasEpisode =
                albumInfoData.data.maxVideoOrder.toInt() + albumInfoData.data.trailerAppendCount > 0
        }
    } else {
        if (TextUtils.isEmpty(albumInfoData.data.latestVideoCount)) {
            videoCount = 0
        } else {
            videoCount =
                albumInfoData.data.latestVideoCount.toInt() + albumInfoData.data.trailerAppendCount
        }
        episodePlayingVideoOrder = albumInfoData.data.videoOrder

        if (TextUtils.isEmpty(albumInfoData.data.latestVideoCount)) {
            hasEpisode = false
        } else {
            hasEpisode = albumInfoData.data.latestVideoCount.toInt() > 0
        }
    }

    val recommendList = albumInfoData?.extend?.recommendList ?: mutableListOf()
    recommendList.subList(
        0,
        min(5.0, recommendList.size.toDouble() ?: 0.0).toInt()
    )

//            val dec =
    return VideoDetailViewResult.Success(
        response = VideoInfoResponse(
            dec = albumInfoData.data.tvDesc,
            isShowVip = isShowVip,
            videoName = name,
            videoMaxLine = maxLine,
            videoLength = albumInfoData.data.tvLength,
            score = score,
            id = albumInfoData.data.id,
            label = lable,
            decMaxLine = decMaxLine,
            isTeenager = isTeenager,
            cateCode = albumInfoData.data.cateCode,
            serviceVid = albumInfoData.data.tvVerId,
            tvAreaId = albumInfoData.data.tvAreaId,
            trailerId = albumInfoData.data.trailerId,
            videoOrder = albumInfoData.data.videoOrder,
            sortOrder = albumInfoData?.extend?.sortOrder ?: 0,
            tvIsEarly = albumInfoData.data.tvIsEarly,
            albumEpisodeType = albumInfoData.data.episodeType,
            tvIsIntrest = albumInfoData.data.tvIsIntrest,
            isShowTitle = albumInfoData.data.isShowTitle,
            videoCount = videoCount,
            trailerCount = albumInfoData.data.trailerCount,
            episodePlayingVideoOrder = episodePlayingVideoOrder,
            hasTrailer = albumInfoData.data?.hasTrailer ?: false,
            updateNotification = albumInfoData.data.updateNotification,
            tvSets = albumInfoData.data.tvSets,
            maxVideoOrder = albumInfoData.data.maxVideoOrder,
            albumSeries = albumInfoData.extend?.albumSeries,
            recommendList = recommendList,
            isExtendNull = albumInfoData.extend == null,
            latestVideoCount = albumInfoData.data.latestVideoCount,
            hasEpisode = hasEpisode,
            firstDesc = firstDesc,
            secondDesc = secondDesc,
            recordNumber = albumInfoData.data.recordNumber,
            ottFee = albumInfoData.data.ottFee,
            tvIsFee = albumInfoData.data.tvIsFee,
            useTicket = albumInfoData.data.useTicket,
            paySeparate = albumInfoData.data.paySeparate,
            cateCodeSecond = albumInfoData.data.cateCodeSecond,
            isAudit = albumInfoData.data.isAudit,
            cornerType = albumInfoData.data.cornerType,
            tvVerPic = albumInfoData.data.tvVerPic,
            albumExtendsPic_640_360 = albumInfoData.data.albumExtendsPic_640_360,
            copyRightTips = albumInfoData?.extend?.ott_copyright_tips,
            pgclogoInfo = LogoInfo().also {
                it.dimension = albumInfoData?.data?.logoInfo?.dimension
                it.logo = albumInfoData?.data?.logoInfo?.logo ?: 0
                it.logoleft = albumInfoData?.data?.logoInfo?.logoleft ?: 0
                it.height = albumInfoData?.data?.logoInfo?.height ?: 0f
                it.width = albumInfoData?.data?.logoInfo?.width ?: 0f
                it.logoSideMargin = albumInfoData?.data?.logoInfo?.side_margin ?: 0f
                it.logoTopMargin = albumInfoData?.data?.logoInfo?.top_margin ?: 0f
                it.orientation = albumInfoData?.data?.logoInfo?.orientation ?: 0
            }
        )
    )
}


fun List<PgcPlayList.PgcPlayInfo>.addPgcVideoPlayInfo(pgcAlbumInfo: PgcAlbumInfo?): MutableList<ResolutionInfo> {
    val playList = mutableListOf<PgcAlbumInfo.DataEntity.PlayListEntity>()
    AppLogger.d("addPgcVideoPlayInfo")
    if (this.isNotEmpty()) {
        this.forEach { playInfo ->
            playList.add(PgcAlbumInfo.DataEntity.PlayListEntity().apply {
                this.versionId = playInfo.versionCode
                this.url = playInfo.m3u8PlayUrl
                val album = pgcAlbumInfo?.data?.playList?.find {
                    playInfo.versionCode == it.versionId
                }?.let {
                    this.tvVerId = it.tvVerId
                    this.hasLogo = it.hasLogo
                }
            })
        }
    }
    return playList.addVideoPlayInfo()
}

fun getResolutionConfigFilterInfo(): List<ResolutionInfo>? {
    return Gson().fromJson(
        AppConfigDatabase.getAppPlayerResolutionConfig(),
        AppPlayerConfig::class.java
    )?.resolutions?.filter {
        it.enable
    }
}


fun List<PgcAlbumInfo.DataEntity.PlayListEntity>.addVideoPlayInfo(mKey: String? = ""): MutableList<ResolutionInfo> {
    //先过滤 可以展示清晰度的集合
    val configResolutionInfos = getResolutionConfigFilterInfo()
    AppLogger.d("addVideoPlayInfo configResolutionInfos :${configResolutionInfos.toString()}")
    //服务器返回的播放数据
    val playList = this
    val priorityPlayList = mutableListOf<PgcAlbumInfo.DataEntity.PlayListEntity>()

    /**
     * key 是 serviceIdPriority value是 服务器返回的 当前serviceIds的所匹配清晰度
     * 示例
     * key is serviceIdPriority[32,265] value is VideoPlayInfo
     */
    val priorityIdMap = mutableMapOf<MutableList<Int>, MutableList<VideoPlayInfo>>()
    //过滤出 需要合并的清晰度配置
    configResolutionInfos?.filter { !it.serviceIdPriority.isNullOrEmpty() }
        ?.let { configResolutionInfo ->
            val priorityVideoPlayInfos = mutableListOf<VideoPlayInfo>()
            AppLogger.d("addVideoPlayInfo csm配置的清晰度优先级为 ：${configResolutionInfo.toString()}")
            configResolutionInfo.forEach { info ->
                AppLogger.d("addVideoPlayInfo 遍历csm配置的清晰度优先级为 ：${info.toString()}")
                playList.forEach { playInfo ->
//                    AppLogger.d("addVideoPlayInfo 遍历服务器配置的清晰度值为  is  versionId: ${playInfo.versionId}  serviceId:${info.serviceId}  ")
                    //查找配置优先级清晰度 对应的所有播放信息 整合
                    if (info.serviceId?.contains(playInfo.versionId) == true) {
                        priorityVideoPlayInfos.add(VideoPlayInfo().apply {
                            url = playInfo.url?.plus(mKey)
                            hasLogo = playInfo.hasLogo != 0
                            tvVerId = playInfo.tvVerId
                            tryPlayUrl = playInfo.tryPlayUrl
                            serviceVersionId = playInfo.versionId
                        }
                        )
                    }
                }
                priorityIdMap[info.serviceIdPriority!!] = priorityVideoPlayInfos
                AppLogger.d("addVideoPlayInfo 过滤的清晰度集合为 key is ${info.serviceIdPriority}   ：${priorityVideoPlayInfos.toString()}")
            }
        }
    val infos = mutableListOf<ResolutionInfo>()
    if (playList.isNotEmpty()) {
        playList.forEach { playinfo ->
            //服务器清晰度id
            val pe = playinfo.versionId
            //查找对应 cms配置 得清晰度 相关信息
            val findConfigResolutionInfo =
                configResolutionInfos?.find { configResolutionInfo ->
                    configResolutionInfo.serviceId?.contains(
                        pe
                    ) == true
                }
            //查找到添加到集合
            findConfigResolutionInfo?.let { resolutionInfo ->
                //如果没有设置优先清晰度 直接添加 播放地址信息
                if (resolutionInfo.serviceIdPriority.isNullOrEmpty()) {
                    resolutionInfo.apply {
                        this.videoPlayInfo = VideoPlayInfo().apply {
                            url = playinfo.url?.plus(mKey)
                            hasLogo = playinfo.hasLogo != 0
                            tvVerId = playinfo.tvVerId
                            tryPlayUrl = playinfo.tryPlayUrl
                            serviceVersionId = playinfo.versionId
                        }
                    }
                    AppLogger.d("addVideoPlayInfo findInfo：${resolutionInfo.toString()}")
                    infos.add(resolutionInfo)
                } else {
                    //查找 优先清晰度进行添加videoPlayInfo
                    val priorityResolutionInfo = priorityIdMap[resolutionInfo.serviceIdPriority]
                    val findInfo =
                        priorityResolutionInfo?.find { resolutionInfo.serviceIdPriority?.contains(it.serviceVersionId) == true }
                    if (findInfo != null) {
                        infos.add(resolutionInfo.apply {
                            this.videoPlayInfo = findInfo
                        })
                        priorityIdMap.clear()
                        AppLogger.d("addVideoPlayInfo findInfo：${findInfo.toString()}")
                    } else {
                        val findInfoNext =
                            priorityResolutionInfo?.find {
                                resolutionInfo.serviceIdPriority?.contains(
                                    it.serviceVersionId
                                ) == false
                            }
                        if (findInfoNext != null) {
                            infos.add(resolutionInfo.apply {
                                this.videoPlayInfo = findInfoNext
                            })
                            priorityIdMap.clear()
                            AppLogger.d("addVideoPlayInfo findInfoNext：${findInfoNext.toString()}")
                        }
                    }
                }
            }
        }
    }
    infos.sortBy {
        it.priority
    }
    AppLogger.d("addVideoPlayInfo 清晰度集合为：${infos.toString()}")
    return infos
}


/**
 * 转换vrs 播放地址及清晰度
 */
fun List<PgcAlbumInfo.DataEntity.PlayListEntity>.conversionVideoPlayResolutionInfo(mKey: String? = ""): MutableList<VideoPlayResolutionInfo> {
    val infos = mutableListOf<VideoPlayResolutionInfo>()
    if (this.isNotEmpty()) {
        this.forEach {
            val pe = it.versionId
            infos.add(VideoPlayResolutionInfo().apply {
                url = it.url.plus(mKey)
                hasLogo = it.hasLogo != 0
                tvVerId = it.tvVerId
                tryPlayUrl = it.tryPlayUrl
                resolution = serverValueTransformMedia(pe)
                serviceVersionId = pe
            })
        }
    }
    return infos
}

fun chooseLayoutType(
    isTrailer: Boolean,
    mCateCode: Int,
    mIsShowTitle: Int,
    mVideoType: Int,
    sType: Int
): Int {
    if (isTrailer && sType != 38) {
        return 3
    }
    // 电影和动漫判断showTitle类型
    if (mCateCode == Constant.CATECODE_DRAMA || mCateCode == Constant.CATECODE_COMIC) {
        return mIsShowTitle
    } else if (mCateCode == Constant.CATECODE_VARIETY) {
        //综艺固定大图文
//            mLayoutType = 2;
        return 2
    } else if (mVideoType == Constant.DATA_TYPE_PGC || mCateCode == Constant.CATECODE_DOCUMENTARY || mCateCode == Constant.EDU_CATE_CODE) {
        //pgc 纪录片 课堂 文字链
        return 1
    } else if (mCateCode == Constant.CATECODE_MOVIE) {
        return 3
    }
    return 0
}

/**
 * 是否在播放中
 */
fun Int.playerIsPlaying(): Boolean {
    return this != PlayerConstants.VideoState.ERROR
            && this != PlayerConstants.VideoState.IDLE
            && this != PlayerConstants.VideoState.PREPARING
            && this != PlayerConstants.VideoState.START_ABORT
            && this != PlayerConstants.VideoState.PLAYBACK_COMPLETED
            && this != PlayerConstants.VideoState.PAUSED
            && this != PlayerConstants.VideoState.STOPPED
}

fun Int.playerIsStart(): Boolean {
    return this == PlayerConstants.VideoState.BUFFERED
            || this == PlayerConstants.VideoState.PLAYING
            || this == PlayerConstants.VideoState.PLAYING_BACK
            || this == PlayerConstants.VideoState.BUFFERING
}

/**
 * 没有开始播放
 */
fun Int.playerIsNotStart(): Boolean {
    return this == PlayerConstants.VideoState.ERROR
            || this == PlayerConstants.VideoState.IDLE
            || this == PlayerConstants.VideoState.PREPARING
            || this == PlayerConstants.VideoState.START_ABORT
            || this == PlayerConstants.VideoState.PLAYBACK_COMPLETED
            || this == PlayerConstants.VideoState.STOPPED
}