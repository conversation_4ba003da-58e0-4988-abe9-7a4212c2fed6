package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.CornerTagImageView;

/**
 * Created by music on 2016/3/31.
 */
public class ExitRecommendAdapter extends RecyclerView.Adapter<ExitRecommendAdapter.ViewHolder> {
    private VideoDetailRecommend recommend;
    private int mVideoType = 0;
    private FocusBorderView mFocusBorderView;

    public ExitRecommendAdapter(Context context){
    }

    public void setData(VideoDetailRecommend recommend , int videoType){
        this.recommend = recommend;
        this.mVideoType = videoType;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View modelView;
        if (mVideoType == Constant.DATA_TYPE_VRS) {
            modelView = LayoutInflater.from(parent.getContext()).inflate(R.layout.list_grid_item, parent, false);
        } else { // VR & PGC
            modelView = LayoutInflater.from(parent.getContext()).inflate(R.layout.list_grid_item_hor,parent,false);
        }

        return new ViewHolder(modelView);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        if (recommend == null || recommend.getData() == null || recommend.getData().size() <= 0 || position >= recommend.getData().size()) {
            return;
        }
        VideoDetailRecommend.DataEntity dataEntity = recommend.getData().get(position);
        holder.mTitleView.setText(recommend.getData().get(position).getTvName());

        if (mVideoType == Constant.DATA_TYPE_VR) {
            holder.mImageView.setCornerTypeWithType(0, 0, 0, 0, CornerTagImageView.CORNER_TYPE_VR);
            holder.mImageView.setCornerHeightRes(R.dimen.y50);
        } else if (mVideoType == Constant.DATA_TYPE_PGC) {
            holder.mImageView.setCornerTypeWithType(0, 0, 0, 0,CornerTagImageView.CORNER_TYPE_PGC);
            holder.mImageView.setCornerHeightRes(R.dimen.y50);
        } else {
//            holder.mImageView.setCornerType(dataEntity.getTvIsFee(), dataEntity.getOttFee(), dataEntity.getCornerType());
            holder.mImageView.setCornerTypeWithType(dataEntity.getTvIsFee(), dataEntity.getTvIsEarly(), dataEntity.useTicket,
                    dataEntity.paySeparate,dataEntity.getCornerType());
            holder.mImageView.setCornerHeightRes(R.dimen.y33);
            if (!TextUtils.isEmpty(recommend.getData().get(position).scoreSource) && recommend.getData().get(position).scoreSource.equals("1")) {
                holder.itemView.findViewById(R.id.doubangIV).setVisibility(View.INVISIBLE);
                ((TextView) holder.itemView.findViewById(R.id.scoreTV)).setText(recommend.getData().get(position).score);
            } else {
                holder.itemView.findViewById(R.id.doubangIV).setVisibility(View.VISIBLE);
                ((TextView) holder.itemView.findViewById(R.id.scoreTV)).setText(recommend.getData().get(position).doubanScore);
            }
        }
        String posterUrl;
        if (mVideoType == Constant.DATA_TYPE_VRS) {
            posterUrl = recommend.getData().get(position).getTvVerPic();
        } else { // VR & PGC
            posterUrl = recommend.getData().get(position).getAlbumExtendsPic_640_360();
        }
        holder.mImageView.setImageRes(posterUrl,false);
    }

    @Override
    public int getItemCount() {
        return mVideoType != Constant.DATA_TYPE_VRS ? 2 : 4;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.mFocusBorderView = focusBorderView;
    }

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener{
        CornerTagImageView mImageView;
        TextView mTitleView;

        public ViewHolder(View itemView) {
            super(itemView);
            mImageView = (CornerTagImageView) itemView.findViewById(R.id.grid_model_image);
            mTitleView = (TextView) itemView.findViewById(R.id.grid_model_title);
            mImageView.setClearWhenDetached(false);
//            mImageView.setTag(mTitleView);

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
                        mImageView.setSelected(true);
                        mImageView.startOrStopMarquee(true);
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setVisibility(View.VISIBLE);
                            mFocusBorderView.setFocusView(v);
                            FocusUtil.setFocusAnimator(v, mFocusBorderView);
                        }
                    } else {
                        mImageView.setSelected(false);
                        mImageView.startOrStopMarquee(false);
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setVisibility(View.GONE);
                            mFocusBorderView.setUnFocusView(v);
                            FocusUtil.setUnFocusAnimator(v);
                        }
                    }
                }
            });
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View v) {
            if (recommend == null
                    || recommend.getData() == null
                    || v == null){
                return;
            }
            int aid = recommend.getData().get(getPosition()).getId();
            ActivityLauncher.startVideoDetailActivity(v.getContext(), aid, mVideoType, Constant.PAGE_EXIT_RECOMMEND);
            RequestManager.getInstance().onClickExitRecommendItem(aid,getPosition());
        }
    }
}
