package com.sohuott.tv.vod.utils;

import android.content.Context;
import android.widget.TextView;

import com.sohuott.tv.vod.lib.db.greendao.ChildSearchHistory;
import com.sohuott.tv.vod.lib.db.greendao.ChildSearchHistoryDao;
import com.sohuott.tv.vod.lib.db.greendao.DaoSessionInstance;
import com.sohuott.tv.vod.lib.db.greendao.SearchHistory;
import com.sohuott.tv.vod.lib.db.greendao.SearchHistoryDao;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.HotSearchNew;
import com.sohuott.tv.vod.lib.model.SearchResult;
import com.sohuott.tv.vod.lib.utils.Constant;

/**
 * Created by fenglei on 17-7-11.
 */

public class SearchUtil {

    public static final int SEARCH_CID_ACTOR = 200;
    public static final int SEARCH_CID_PRODUCER = 35;
    public static final int SEARCH_CID_PGCLIST = 33;

//    public static void showSearchSuggestTitle(SearchSuggest.DataBean.RBean rBean, TextView textView) {
//        if(rBean != null && textView != null) {
//            if(rBean.getCid() == SEARCH_CID_ACTOR) {
//                textView.setText(rBean.getN());
//            } else if(rBean.getCid() == SEARCH_CID_PRODUCER) {
//                textView.setText(rBean.getTitle());
//            } else {
//                textView.setText(rBean.getT());
//            }
//        }
//    }
//
//    public static void jumpSearchSuggest(Context context, SearchSuggest.DataBean.RBean rBean) {
//        if(rBean == null || context == null) {
//            return;
//        }
//        try {
//            if(rBean.getCid() == SEARCH_CID_ACTOR) {
//                ActivityLauncher.startActorListActivity(context, rBean.getId(), rBean.getN());
//            } else if(rBean.getCid() == SEARCH_CID_PRODUCER) {
//                ActivityLauncher.startProducerActivity(context, Integer.parseInt(rBean.getPgcUseId()));
//            } else if(rBean.getCid() == SEARCH_CID_PGCLIST) {
//                ActivityLauncher.startProducerActivity(context, rBean.getAid());
//            } else {
//                ActivityLauncher.startVideoDetailActivity(context, rBean.getAid());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    public static void showSearchTitle(HotSearchNew.DataBean dataBean, TextView textView) {
        if(dataBean != null && textView != null) {
            if(dataBean.getCid() == SEARCH_CID_ACTOR) {
                textView.setText(dataBean.getN());
            } else if(dataBean.getCid() == SEARCH_CID_PRODUCER) {
                textView.setText(dataBean.getTitle());
            } else {
                textView.setText(dataBean.getT());
            }
        }
    }

    public static void jumpSearch(Context context, HotSearchNew.DataBean dataBean) {
        if(dataBean == null || context == null) {
            return;
        }
        try {
            if(dataBean.getCid() == SEARCH_CID_ACTOR) {
                ActivityLauncher.startActorListActivity(context, dataBean.getId(), dataBean.getN());
            } else if(dataBean.getCid() == SEARCH_CID_PRODUCER) {
                ActivityLauncher.startProducerActivity(context, Integer.parseInt(dataBean.getPgcUserId()));
            } else if(dataBean.getCid() == SEARCH_CID_PGCLIST) {
                ActivityLauncher.startProducerActivity(context, Integer.parseInt(dataBean.getPgcUserId()), dataBean.getAid());
            } else {
                ActivityLauncher.startVideoDetailActivity(context, dataBean.getAid(), Constant.PAGE_SEARCH);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void saveSearchHistory(Context context, com.sohuott.tv.vod.model.SearchResult.Data.RelationItem itemsBean) {
        if(context == null || itemsBean == null) {
            return;
        }
        SearchHistoryDao searchHistoryDao = DaoSessionInstance.getDaoSession(context).getSearchHistoryDao();
        SearchHistory searchHistory = new SearchHistory();
        searchHistory.setAlbumId(itemsBean.getId());
        searchHistory.setAlbumTitle(itemsBean.getTvName());
        searchHistory.setPic_480_660(itemsBean.getTvHorBigPic());
        searchHistory.setPic_640_480(itemsBean.getTvPic());
        searchHistory.setClickCount(Integer.valueOf((int) System.currentTimeMillis()));
        searchHistory.setTvType(Constant.DATA_TYPE_VRS);
        SearchHistory searchHistoryFromDb = searchHistoryDao.queryBuilder().where(
                SearchHistoryDao.Properties.AlbumId.eq(itemsBean.getId())).unique();
        if (searchHistoryFromDb != null) {
            searchHistory.setId(searchHistoryFromDb.getId());
            searchHistoryDao.update(searchHistory);
        } else {
            searchHistoryDao.insert(searchHistory);
        }
    }



    public static void saveSearchHistory(Context context, com.sohuott.tv.vod.model.SearchResult.Data.SearchItem itemsBean) {
        if(context == null || itemsBean == null) {
            return;
        }
        SearchHistoryDao searchHistoryDao = DaoSessionInstance.getDaoSession(context).getSearchHistoryDao();
        SearchHistory searchHistory = new SearchHistory();
        searchHistory.setAlbumId(itemsBean.getId());
        searchHistory.setAlbumTitle(itemsBean.getTvName());
        searchHistory.setPic_480_660(itemsBean.getTvHorBigPic());
        searchHistory.setPic_640_480(itemsBean.getTvPic());
        searchHistory.setClickCount(Integer.valueOf((int) System.currentTimeMillis()));
        searchHistory.setTvType(Constant.DATA_TYPE_VRS);
        SearchHistory searchHistoryFromDb = searchHistoryDao.queryBuilder().where(
                SearchHistoryDao.Properties.AlbumId.eq(itemsBean.getId())).unique();
        if (searchHistoryFromDb != null) {
            searchHistory.setId(searchHistoryFromDb.getId());
            searchHistoryDao.update(searchHistory);
        } else {
            searchHistoryDao.insert(searchHistory);
        }
    }

    public static void saveSearchHistory(Context context, HotSearchNew.DataBean dataBean) {
        if(context == null || dataBean == null) {
            return;
        }
        SearchHistoryDao searchHistoryDao = DaoSessionInstance.getDaoSession(context).getSearchHistoryDao();
        SearchHistory searchHistory = new SearchHistory();
        searchHistory.setAlbumId(dataBean.getAid());
        searchHistory.setCid((long)dataBean.getCid());
        searchHistory.setAlbumTitle(dataBean.getT());
        searchHistory.setPic_480_660(dataBean.getHorBigPic());
        searchHistory.setPic_640_480(dataBean.getPic());
        searchHistory.setClickCount(Integer.valueOf((int) System.currentTimeMillis()));
        searchHistory.setTvType(Constant.DATA_TYPE_VRS);
        SearchHistory searchHistoryFromDb = searchHistoryDao.queryBuilder().where(
                SearchHistoryDao.Properties.AlbumId.eq(dataBean.getAid())).unique();
        if (searchHistoryFromDb != null) {
            searchHistory.setId(searchHistoryFromDb.getId());
            searchHistoryDao.update(searchHistory);
        } else {
            searchHistoryDao.insert(searchHistory);
        }
    }

    public static void saveSearchHistory(Context context, SearchResult.DataBean.ItemsBean itemsBean) {
        if(context == null || itemsBean == null) {
            return;
        }
        SearchHistoryDao searchHistoryDao = DaoSessionInstance.getDaoSession(context).getSearchHistoryDao();
        SearchHistory searchHistory = new SearchHistory();
        searchHistory.setAlbumId(itemsBean.getId());
        searchHistory.setAlbumTitle(itemsBean.getTitle());
        searchHistory.setPic_480_660(itemsBean.getPic_480_660());
        searchHistory.setPic_640_480(itemsBean.getBig_pic());
        if(itemsBean.getDataType().equals("star")){
            searchHistory.setAlbumTitle(itemsBean.getStarName());
            searchHistory.setTvType(Constant.DATA_TYPE_SEARCH_ACTOR);
        }else if(itemsBean.getDataType().equals("album")) {
            searchHistory.setTvType(Constant.DATA_TYPE_VRS);
        } else {
            searchHistory.setTvType(Constant.DATA_TYPE_PGC);
        }
        searchHistory.setClickCount(Integer.valueOf((int) System.currentTimeMillis()));
        SearchHistory searchHistoryFromDb = searchHistoryDao.queryBuilder().where(
                SearchHistoryDao.Properties.AlbumId.eq(itemsBean.getId())).unique();
        if (searchHistoryFromDb != null) {
            searchHistory.setId(searchHistoryFromDb.getId());
            searchHistoryDao.update(searchHistory);
        } else {
            searchHistoryDao.insert(searchHistory);
        }
    }

    public static void saveChildSearchHistory(Context context, HotSearchNew.DataBean dataBean) {
        if(context == null || dataBean == null) {
            return;
        }
        try {
            ChildSearchHistoryDao childSearchHistoryDao = DaoSessionInstance.getDaoSession(context).getChildSearchHistoryDao();
            ChildSearchHistory childSearchHistory = new ChildSearchHistory();
            childSearchHistory.setAlbumId(dataBean.getAid());
            childSearchHistory.setCid((long)dataBean.getCid());
            childSearchHistory.setAlbumTitle(dataBean.getT());
            childSearchHistory.setPic_480_660(dataBean.getHorBigPic());
            childSearchHistory.setPic_640_480(dataBean.getPic());
            childSearchHistory.setClickCount(Integer.valueOf((int) System.currentTimeMillis()));
            childSearchHistory.setTvType(Constant.DATA_TYPE_VRS);
            ChildSearchHistory childSearchHistoryFromDb = childSearchHistoryDao.queryBuilder().where(
                    ChildSearchHistoryDao.Properties.AlbumId.eq(dataBean.getAid())).unique();
            if (childSearchHistoryFromDb != null) {
                childSearchHistory.setId(childSearchHistoryFromDb.getId());
                childSearchHistoryDao.update(childSearchHistory);
            } else {
                childSearchHistoryDao.insert(childSearchHistory);
            }
        }catch (Exception ex){
            LibDeprecatedLogger.e("HotSearch saveChildSearchHistory: " + ex.toString());
        }
    }

    public static void saveChildSearchHistory(Context context, SearchResult.DataBean.ItemsBean itemsBean) {
        if(context == null || itemsBean == null) {
            return;
        }
        try{
            ChildSearchHistoryDao childSearchHistoryDao = DaoSessionInstance.getDaoSession(context).getChildSearchHistoryDao();
            ChildSearchHistory childSearchHistory = new ChildSearchHistory();
            childSearchHistory.setAlbumId(itemsBean.getId());
            childSearchHistory.setAlbumTitle(itemsBean.getTitle());
            childSearchHistory.setPic_480_660(itemsBean.getPic_480_660());
            childSearchHistory.setPic_640_480(itemsBean.getBig_pic());
            if(itemsBean.getDataType().equals("star")){
                childSearchHistory.setAlbumTitle(itemsBean.getStarName());
                childSearchHistory.setTvType(Constant.DATA_TYPE_SEARCH_ACTOR);
            }else if(itemsBean.getDataType().equals("album")) {
                childSearchHistory.setTvType(Constant.DATA_TYPE_VRS);
            } else {
                childSearchHistory.setTvType(Constant.DATA_TYPE_PGC);
            }
            childSearchHistory.setClickCount(Integer.valueOf((int) System.currentTimeMillis()));
            ChildSearchHistory childSearchHistoryFromDb = childSearchHistoryDao.queryBuilder().where(
                    ChildSearchHistoryDao.Properties.AlbumId.eq(itemsBean.getId())).unique();
            if (childSearchHistoryFromDb != null) {
                childSearchHistory.setId(childSearchHistoryFromDb.getId());
                childSearchHistoryDao.update(childSearchHistory);
            } else {
                childSearchHistoryDao.insert(childSearchHistory);
            }
        }catch (Exception ex){
            LibDeprecatedLogger.e("SearchResult saveChildSearchHistory: " + ex.toString());
        }

    }


    public static  boolean isVRS(int cid) {
        return cid == 100 || cid == 101 || cid == 106 || cid == 115 || cid == 107 || cid == 121 || cid == 119;
    }

}
