package com.sohuott.tv.vod.receiver;

import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;

import androidx.annotation.RequiresApi;

import com.sohu.lib_utils.ServiceNotificationUtil;
import com.sohuott.tv.vod.AppLogger;

public class CancelService extends Service {
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        AppLogger.d("CancelService======onCreate");
        startForeground(ServiceNotificationUtil.NOTIFICATION_FOREGROUND_ID, ServiceNotificationUtil.getNotification(this));
        stopSelf();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return super.onStartCommand(intent, flags, startId);
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public void onDestroy() {
        super.onDestroy();
        //stopForeground 停止服务并且取消通知栏 ，这种情况比较特殊，
        // 所以还要调用ServiceNotificationManager.removeNotification();
        ServiceNotificationUtil.removeNotification(getApplicationContext());
        stopForeground(true);
        AppLogger.d("CancelService======="+"onDestroy1");
    }

}

