package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.ui.EpisodeLayoutNew;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager;
import com.sohuott.tv.vod.videodetail.activity.service.EpisodeServiceManger;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.EpisodeHorzTabView;
import com.sohuott.tv.vod.widget.PlayingView;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by fenglei on 16-7-5.
 */
public class EpisodeBaseFragmentNew extends BaseFragment implements View.OnKeyListener, View.OnClickListener {
    protected int mAid;
    protected int mVid;
    protected int mTotalCount;
    protected int mStart;
    protected int mEnd;
    protected int mSortOrder;
    protected int mDataType;
    protected int mType;
    protected int mCateCode;
    protected int mVideoOrder;
    protected boolean mIsTrailerTab;


    protected ViewGroup mRootView;
    protected EpisodeHorzTabView mTabView;
    protected FocusBorderView mFocusBorderView;
    protected TextView mEpisodePoints;
    //用于标记是否是播放器内嵌的选集，用于统计上报
    protected boolean mIsInitFromPlayer;
    //当前播放剧集是否处于当前本Fragment
    protected boolean mEpisodeIsSelected = false;
    protected PlayingView mPlayingView;
    private List<EpisodeVideos.Video> videos;

    //推荐视频列表
    public ArrayList<AlbumInfoRecommendModel> recommendVideos;

    //是否是全屏播控选集
    protected boolean isMenu = false;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mTotalCount = getArguments().getInt("totalCount");
        mStart = getArguments().getInt("start");
        mEnd = getArguments().getInt("end");
        mSortOrder = getArguments().getInt("sortOrder");
        mAid = getArguments().getInt("aid");
        mVid = getArguments().getInt("vid");
        mDataType = getArguments().getInt("dataType");
        mType = getArguments().getInt("type");
        mCateCode = getArguments().getInt("cateCode");
        mVideoOrder = getArguments().getInt("videoOrder");
        mEpisodeIsSelected = getArguments().getBoolean("episodeSelected");
        isMenu = getArguments().getBoolean("isMenu");
        mIsTrailerTab = getArguments().getBoolean("trailerTab");
        videos = (List<EpisodeVideos.Video>) getArguments().getSerializable("vidoes");
        recommendVideos = (ArrayList<AlbumInfoRecommendModel>) getArguments().getSerializable("recommends");
    }

    protected void initUI() {
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            ViewGroup viewGroup = (ViewGroup) mRootView.getChildAt(i);
            if (Util.getManufactureName().equalsIgnoreCase("Rockchip")) {
                viewGroup.setBackgroundResource(R.drawable.episode_vrs_item_corner);
            }
            viewGroup.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
                        if (v.findViewById(R.id.episode_title2) != null) {
//                            ((ScrollingTextView) v.findViewById(R.id.episode_title1)).setEllipsize(TextUtils.TruncateAt.MARQUEE);
//                            ((ScrollingTextView) v.findViewById(R.id.episode_title1)).setSelfFocus(true);
//                            ((TextView) v.findViewById(R.id.episode_title2)).setTextColor(Color.parseColor("#E8E8FF"));
                        }
//                        if (v.findViewById(R.id.episode_title2) != null) {
//                            ((TextView) v.findViewById(R.id.episode_title2)).setEllipsize(TextUtils.TruncateAt.MARQUEE);
//                            ((TextView) v.findViewById(R.id.episode_title2)).setSelfFocus(true);
//                        }
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setFocusView(v);
                            FocusUtil.setFocusAnimator(v, mFocusBorderView, 1f, 100);
                        }
                    } else {
                        if (v.findViewById(R.id.episode_title2) != null) {
//                            ((ScrollingTextView) v.findViewById(R.id.episode_title1)).setSelfFocus(false);
//                            ((ScrollingTextView) v.findViewById(R.id.episode_title1)).setEllipsize(TextUtils.TruncateAt.END);
//                            ((TextView) v.findViewById(R.id.episode_title2)).setTextColor(Color.parseColor("#E8E8FF"));
                        }
//                        if (v.findViewById(R.id.episode_title2) != null) {
//                            ((ScrollingTextView) v.findViewById(R.id.episode_title2)).setSelfFocus(false);
//                            ((ScrollingTextView) v.findViewById(R.id.episode_title2)).setEllipsize(TextUtils.TruncateAt.END);
//                        }
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(v);
                            FocusUtil.setUnFocusAnimator(v, 100);
                        }
                    }
                }
            });
            viewGroup.setOnKeyListener(this);
            viewGroup.setOnClickListener(this);
        }
        int dataSize = mEnd - mStart + 1;
        if (dataSize < mRootView.getChildCount()) {
            for (int i = dataSize; i < mRootView.getChildCount(); i++) {
                mRootView.getChildAt(i).setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if ((videos != null && videos.size() > 0)
                || (videos == null && recommendVideos != null && recommendVideos.size() != 0 && (mStart > mEnd))
                || (videos != null && recommendVideos != null && recommendVideos.size() != 0)) {
            setUI(videos);
        }
    }

    public void setUI(List<EpisodeVideos.Video> videos) {
    }

    protected void showFullTag(boolean b) {
    }

    public void setTabView(EpisodeHorzTabView tabView) {
        mTabView = tabView;
    }

    //左右翻页找焦点
    public void focusMoveToUp() {
        if (mRootView == null) {
            return;
        }
        View view = mRootView.getChildAt(mRootView.getChildCount() - 1);
        for (int i = mRootView.getChildCount() - 1; i > -1; i--) {
            if (mRootView.getChildAt(i).getVisibility() == View.VISIBLE) {
                view = mRootView.getChildAt(i);
                break;
            }
        }
        if (mFocusBorderView != null
                && mFocusBorderView.getFocusView() != null
                && mFocusBorderView.getFocusView().getId() == R.id.episode_tv) {
            view.findViewById(R.id.episode_tv).requestFocus();
        }
    }

    //左右翻页找焦点
    public void focusMoveToDown() {
        if (mRootView == null) {
            return;
        }

        View view = mRootView.getChildAt(0);
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            if (mRootView.getChildAt(i).getVisibility() == View.VISIBLE) {
                view = mRootView.getChildAt(i);
                break;
            }
        }
        if (mFocusBorderView != null
                && mFocusBorderView.getFocusView() != null
                && mFocusBorderView.getFocusView().getId() == R.id.episode_tv) {
            view.findViewById(R.id.episode_tv).requestFocus();
        }
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        mFocusBorderView = focusBorderView;
    }

    public void setEpisodePoints(TextView view) {
        mEpisodePoints = view;
    }

    public void setFragmentFocus(boolean tabPressed) {
        if (mRootView == null) {
            return;
        }
        if (tabPressed
                && (this instanceof EpisodeTrailerFragment)
                && mRootView.getChildCount() > 3
                && mRootView.getChildAt(3).getVisibility() == View.VISIBLE) {
            mRootView.getChildAt(3).requestFocus();
        } else {
            for (int i = 0; i < mRootView.getChildCount(); i++) {
                if (mRootView.getChildAt(i).isSelected()) {
                    mRootView.getChildAt(i).requestFocus();
                    return;
                }
            }
            for (int i = 0; i < mRootView.getChildCount(); i++) {
                if (mRootView.getChildAt(i).getVisibility() == View.VISIBLE) {
                    mRootView.getChildAt(i).requestFocus();
                    return;
                }
            }
            mRootView.getChildAt(0).requestFocus();
        }
    }

    public View getFragmentFocusView(boolean fromTab) {
        if (mRootView == null) {
            return null;
        }
        if (fromTab
                && (this instanceof EpisodeTrailerFragment)
                && mRootView.getChildCount() > 3
                && mRootView.getChildAt(3).getVisibility() == View.VISIBLE) {
            return mRootView.getChildAt(3);
        } else {
            for (int i = 0; i < mRootView.getChildCount(); i++) {
                if (mRootView.getChildAt(i).isSelected()) {
                    return mRootView.getChildAt(i);
                }
            }
            return mRootView.getChildAt(0);
        }
    }

    public void setItemUnSelect() {
        mEpisodeIsSelected = false;
        if (mRootView == null) {
            return;
        }
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            if (mRootView.getChildAt(i).isSelected()) {
                mRootView.getChildAt(i).setSelected(false);
                PlayingView view = (PlayingView) mRootView.getChildAt(i).findViewById(R.id.episode_playing_view);
                if (view != null) {
                    view.hide();
                } else {
                    view = (PlayingView) mRootView.getChildAt(i).findViewById(R.id.on_play_icon);
                    if (view != null) {
                        view.hide();
                    }
                }
            }
        }
    }

    public void setItemSelect(int videoOrder, boolean hasFocus, int vid) {
        LibDeprecatedLogger.d("isMenu : " + isMenu);
        if (videos != null && videos.size() > 0) {
            setUI(videos);
        }
        mEpisodeIsSelected = true;
        if (videoOrder < mStart || videoOrder > mEnd) {
            return;
        }

        int orderIndex;
        // set original select item unselect
        if (videoOrder != mVideoOrder) {
            if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
                orderIndex = mVideoOrder - mStart;
            } else {
                orderIndex = mEnd - mVideoOrder;
            }
            if (orderIndex >= 0 && orderIndex < mRootView.getChildCount()) {
                View childView = mRootView.getChildAt(orderIndex);
                if (childView != null) {
                    childView.setSelected(false);
                    PlayingView view = (PlayingView) childView.findViewById(R.id.episode_playing_view);
                    if (view != null) {
                        view.hide();
                    } else {
                        view = (PlayingView) childView.findViewById(R.id.on_play_icon);
                        if (view != null) {
                            view.hide();
                        }
                    }
                }

            }
        }

        // set current select item select
        if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
            orderIndex = videoOrder - mStart;
        } else {
            orderIndex = mEnd - videoOrder;
        }
        if (orderIndex >= 0 && orderIndex < mRootView.getChildCount()) {
            View childView = mRootView.getChildAt(orderIndex);
            if (childView != null) {
                childView.setSelected(true);
                mPlayingView = (PlayingView) childView.findViewById(R.id.episode_playing_view);
                if (mPlayingView != null) {
                    mPlayingView.show();
                } else {
                    mPlayingView = (PlayingView) childView.findViewById(R.id.on_play_icon);
                    if (mPlayingView != null) {
                        mPlayingView.show();
                    }
                }

                if (hasFocus) {
                    childView.requestFocus();
                }
            }

        }
        mVideoOrder = videoOrder;
    }

    public void setIsInitFromPlayer(boolean isInitFromPlayer) {
        mIsInitFromPlayer = isInitFromPlayer;
    }

    /**
     * 切换剧集
     *
     * @param v The view that was clicked.
     */
    @Override
    public void onClick(View v) {
        Object tag = v.getTag();
        if (tag != null) {
            if (tag instanceof String) {
                String tag_str = (String) tag;
                if (TextUtils.equals(tag_str, Constant.EPISODE_OFFLINE)) {
                    ToastUtils.showToast2(getContext(), "该集已下线");
                    return;
                }
            }
        } else {
            ToastUtils.showToast2(getContext(), "该集已下线");
            return;
        }

        if (v.getTag() instanceof EpisodeVideos.Video) {
            EpisodeVideos.Video video = (EpisodeVideos.Video) v.getTag();
            if (video != null) {
                int videoOrder = video.videoOrder;

                if (video.tvStype == 1) {
                    RequestManager.getInstance().onEvent("6_info", "6_info_episode_normal", String.valueOf(video.id),
                            String.valueOf(video.tvVerId), null, null, null);
                } else {
                    RequestManager.getInstance().onEvent("6_info", "6_info_episode_trailer", String.valueOf(video.id),
                            String.valueOf(video.tvVerId), null, null, null);
                }

                v.setSelected(true);
                mEpisodeIsSelected = true;
                mPlayingView = (PlayingView) v.findViewById(R.id.episode_playing_view);
                if (mPlayingView != null) {
                    mPlayingView.show();
                } else {
                    mPlayingView = (PlayingView) v.findViewById(R.id.on_play_icon);
                    if (mPlayingView != null) {
                        mPlayingView.show();
                    }
                }


                int orderIndex;
                if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
                    orderIndex = mVideoOrder - mStart;
                } else {
                    orderIndex = mEnd - mVideoOrder;
                }
                if (orderIndex >= 0 && orderIndex < mRootView.getChildCount()) {
                    View childView = mRootView.getChildAt(orderIndex);
                    if (childView != null) {
                        childView.setSelected(false);
                        if (childView instanceof ViewParent) {
                            PlayingView playingView = (PlayingView) childView.findViewById(R.id.episode_playing_view);
                            if (playingView != null) {
                                playingView.hide();
                            } else {
                                playingView = (PlayingView) childView.findViewById(R.id.on_play_icon);
                                if (playingView != null) {
                                    playingView.hide();
                                }
                            }
                            updateChildView(childView, false);
                        }
                    }
                }

                mVideoOrder = videoOrder;
                int aid = 0;
                if (mDataType == Constant.DATA_TYPE_VRS) {
                    VideoDetailRequestManager.tabClick(10282, String.valueOf(mAid), String.valueOf(video.tvVerId), isMenu, video.tvStype);
                    aid = mAid;
                } else {
                    aid = video.id;
                }
                EpisodeServiceManger.getInstants().onEpisodeClickVideo(aid, video.tvVerId, video.tvVerId, mDataType, mIsTrailerTab);

            }
        } else if (v.getTag() instanceof AlbumInfoRecommendModel) {
            AlbumInfoRecommendModel model = (AlbumInfoRecommendModel) v.getTag();
//            v.setSelected(true);
            ActivityLauncher.startVideoDetailActivity(getContext(), Integer.parseInt(model.getAlbumId()), 0);
            String pageId;
            if (!isMenu) {
                pageId = "1041";
            } else {
                pageId = "1045";
            }
            VideoDetailRequestManager.recommendListClick(pageId, model.getAlbumId(), String.valueOf(model.getIndex() + 1));
        }

    }

    protected void updateChildView(View view, boolean selected) {

    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            switch (keyCode) {
                case KeyEvent.KEYCODE_DPAD_DOWN:
                    if (isMenu) {
                        return false;
                    }
                    if (mTabView != null) {
                        mTabView.setCurrentTabFocus();
                        return true;
                    }
                    break;
                case KeyEvent.KEYCODE_DPAD_UP:
                    if (isMenu && mTabView != null) {
                        mTabView.setCurrentTabFocus();
                        return true;
                    } else {
                        return false;
                    }
                case KeyEvent.KEYCODE_DPAD_RIGHT:
                    // 检查推荐视频列表是否为空
                    boolean hasRecommendVideos = recommendVideos != null && !recommendVideos.isEmpty();
                    int recommendTotalSize = hasRecommendVideos ? recommendVideos.get(0).getSize() : 0; // 推荐列表总长
                    int recommendCurrentSize = hasRecommendVideos ? recommendVideos.size() : 0; // 当前页推荐列表长度
                    int realTotal = mTotalCount + recommendTotalSize; // 加上所有的推荐列表总长度
                    int currentIndex = mRootView.indexOfChild((View) v.getParent()); // 当前key事件view所在index位置

                    if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) { // 如果是升序
                        if (currentIndex + mStart > realTotal) { // 当前位置大于总位置（代表有推荐列表）
                            // 检查推荐视频是否分成了两页面
                            if (recommendCurrentSize != recommendTotalSize) {
                                return recommendVideos.get(currentIndex - 10).getIndex() == recommendTotalSize - 1;
                            } else {
                                // 检查推荐视频是否没有分成两页面
                                return recommendTotalSize == currentIndex - 9;
                            }
                        } else {
                            //为了拦截播控选集菜单右侧焦点出界问题
                            if (currentIndex + mStart == realTotal) return true;
                        }
                    }
//                    if ((mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER && (mStart + mRootView.indexOfChild((View) v.getParent())) == (mTotalCount + (recommendVideos != null ? recommendVideos.size() : 0)))
//                            || (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER && (mEnd - mRootView.indexOfChild((View) v.getParent())) == 1)) {
//                        return true;
//                    }
                    break;
                case KeyEvent.KEYCODE_DPAD_LEFT:
                    if ((mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER && (mStart + mRootView.indexOfChild((View) v.getParent())) == 1)
                            || (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER && (mEnd - mRootView.indexOfChild((View) v.getParent())) == mTotalCount)) {
                        return true;
                    }
                    break;
                default:
                    break;
            }

        }
        return false;
    }

    public void onPageScrollStateStop() {
        if (!isVisible()) {
            return;
        }
        if (mRootView != null) {
            View focusView = mRootView.getFocusedChild();
            recoverFocus(focusView);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (mPlayingView != null) {
            if (isVisibleToUser & mEpisodeIsSelected) {
                mPlayingView.show();
            } else {
                mPlayingView.hide();
            }
        }
    }

    protected void recoverFocus(View focusView) {
        if (focusView == null || mFocusBorderView == null) {
            return;
        }
        mFocusBorderView.setFocusView(focusView);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mPlayingView != null) {
            mPlayingView = null;
        }
    }
}
