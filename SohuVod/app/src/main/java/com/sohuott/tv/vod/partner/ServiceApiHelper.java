package com.sohuott.tv.vod.partner;

import android.app.Activity;
import android.content.Context;

import com.sohuott.tv.vod.app.SohuAppUtil;
import com.sohuott.tv.vod.lib.base.ActivityManagerUtil;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.videodetail.activity.VideoActivity;

public class ServiceApiHelper {

    public interface IPlayerServiceApiListener {

        void onPlayerResume();

        void onPlayerPause();

        void onPlayerForward();

        void onPlayerBackward();

        void onPlayerSeekTo(int sec);

        void onPlayerFastSeek(int sec);

        void setPlayerFullScreen(boolean isFullScreen);
    }

    private IPlayerServiceApiListener mListener;
    private Context mContext;

    public ServiceApiHelper(Context context) {
        this.mContext = context;
    }

    public void setIPlayerServiceApiListener(IPlayerServiceApiListener listener) {
        this.mListener = listener;
    }

    public void release(){
        mListener = null;
        mContext = null;
    }

    public void playerOnResume() {
        LibDeprecatedLogger.d("Call playerOnResume()");

        if (mListener != null) {
            mListener.onPlayerResume();
        }
    }

    public void playerOnPause() {
        LibDeprecatedLogger.d("Call playerOnPause()");

        if (mListener != null) {
            mListener.onPlayerPause();
        }
    }

    public void playerOnStop() {
        LibDeprecatedLogger.d("Call playerOnStop()");

        Activity topActivity = ActivityManagerUtil.getTopActivity();
        if (topActivity instanceof VideoActivity) {
            topActivity.finish();
        }
    }

    public void playerOnForward() {
        LibDeprecatedLogger.d("Call playerOnForward()");

        if (mListener != null) {
            mListener.onPlayerForward();
        }
    }

    public void playerOnBackward() {
        LibDeprecatedLogger.d("Call playerOnBackward()");

        if (mListener != null) {
            mListener.onPlayerBackward();
        }
    }

    public void playerOnSeekTo(int sec) {
        LibDeprecatedLogger.d("Call playerOnSeekTo( int sec = " + sec + " )");

        if(sec <= 0){
            LibDeprecatedLogger.e("Illegal target time: " + sec);
            return;
        }

        if (mListener != null) {
            mListener.onPlayerSeekTo(sec);
        }
    }

    public void playerOnFastSeek(int sec) {
        LibDeprecatedLogger.d("Call playerOnFastSeek( int sec = " + sec + " )");

        if(sec <= 0){
            LibDeprecatedLogger.e("Illegal target time: " + sec);
            return;
        }

        if (mListener != null) {
            mListener.onPlayerFastSeek(sec);
        }
    }

    public void playerSetFullScreen(boolean isFullScreen) {
        LibDeprecatedLogger.d("Call playerSetFullScreen( boolean isFullScreen = " + isFullScreen + " )");

        if (mListener != null) {
            mListener.setPlayerFullScreen(isFullScreen);
        }
    }

    public void backToHome() {
    }

    public void exitApp() {
        LibDeprecatedLogger.d("Call exitApp()");

        SohuAppUtil.exitApp(mContext);
    }

}
