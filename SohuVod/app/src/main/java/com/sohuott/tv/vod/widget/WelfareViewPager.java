package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.ListWelfareModel.DataEntity.ActivityListEntity;
import com.sohuott.tv.vod.lib.widgets.ViewPager;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.utils.StbCompatUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * welfare banner
 *
 * <AUTHOR>
 */
public class WelfareViewPager extends ViewPager {
    private ArrayList<Integer> childDisToCenter = new ArrayList<>();
    private SparseArray<Integer> childIndex = new SparseArray<>();
    private Context mContext;
    private OnClickListener mOnItemClickListener;
    private OnFocusChangeListener mOnItemFocusChangeListener;
    private long mLastKeyTime;
    private static final long MIN_KEY_DISPATCH_INTERVAL_MS = 200;
    private static final long INTERVAL_MS = 3000;
    public static final int MSG_AUTO = 0;
    public static final int MSG_FOCUS = 1;
    private static class InnerHandler extends Handler {
        WeakReference<WelfareViewPager> mWrapper;
        InnerHandler (WelfareViewPager welfareViewPager){
            mWrapper = new WeakReference<>(welfareViewPager);
        }
        @Override
        public void handleMessage(@NonNull Message msg) {
            WelfareViewPager welfareViewPager = mWrapper.get();
            if (welfareViewPager == null){
                return;
            }
            if (welfareViewPager.isShown()) {
                if (msg.what == MSG_AUTO) {
                    final int item = welfareViewPager.getCurrentItem();
                    welfareViewPager.setCurrentItem(item + 1);
                    sendEmptyMessageDelayed(0, INTERVAL_MS);
                } else if (msg.what == MSG_FOCUS) {
                    welfareViewPager.mFocusBorderView.setVisibility(View.VISIBLE);
                    if (welfareViewPager.hasFocus() && welfareViewPager.getCurrentView() != null) {
                        welfareViewPager.mFocusBorderView.setFocusView(welfareViewPager.getCurrentView());
                        FocusUtil.setFocusAnimator(welfareViewPager.getCurrentView(), welfareViewPager.mFocusBorderView, 1f, 100);
                    }
                }
            }
        }
    }
    private Handler mHandler = new InnerHandler(this);

    public WelfareViewPager(Context context) {
        super(context);
        init(context);
    }

    public WelfareViewPager(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }


    private void init(Context context) {
        mContext = context;
        setPageTransformer(true, new ScaleTransform());
        setScrollerDuration(500);
        addOnPageChangeListener(new SimpleOnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                if (hasFocus()) {
                    mFocusBorderView.setVisibility(View.INVISIBLE);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                if (hasFocus()) {
                    if (state == ViewPager.SCROLL_STATE_IDLE) {
                        mHandler.removeMessages(MSG_FOCUS);
                        mHandler.sendEmptyMessage(MSG_FOCUS);
                    }
                }

            }

            @Override
            public void onPageSelected(int position) {
                if (hasFocus()) {
                    mHandler.sendEmptyMessageDelayed(MSG_FOCUS, 600);
                }
            }
        });
    }

    public void setOnItemClickListener(OnClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }

    public void setOnItemFocusChangeListener(OnFocusChangeListener onFocusChangeListener) {
        mOnItemFocusChangeListener = onFocusChangeListener;
    }

    private List<ActivityListEntity> mDatas = new ArrayList<>();

    public void setData(List<ActivityListEntity> datas) {
        this.mDatas = datas;
        setAdapter(new InfinitePagerAdapter());
        post(new InnerRunnable(this));
    }

    private static class InnerRunnable implements Runnable {
        WeakReference<WelfareViewPager> mWrapper;
        InnerRunnable(WelfareViewPager viewPager){
            mWrapper = new WeakReference<>(viewPager);
        }
        @Override
        public void run() {
            WelfareViewPager welfareViewPager = mWrapper.get();
            if (welfareViewPager != null){
                welfareViewPager.mHandler.sendEmptyMessageDelayed(MSG_AUTO, INTERVAL_MS);
            }
        }
    }

    public void startPauseLoop(boolean play) {
        if (play) {
            mHandler.sendEmptyMessageDelayed(MSG_AUTO, INTERVAL_MS);
        } else {
            mHandler.removeMessages(MSG_AUTO);
        }
    }

    private FocusBorderView mFocusBorderView;

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        mFocusBorderView = focusBorderView;
    }

    @Override

    public void setCurrentItem(int item, boolean smoothScroll) {
        super.setCurrentItem(item, smoothScroll);
        //适配小米电视4.1.2 非当前view未刷新
        if (StbCompatUtils.isXiaomi()
                && Build.MODEL.equals("MiTV")
                && Build.VERSION.SDK_INT == Build.VERSION_CODES.JELLY_BEAN) {
            ((ViewGroup) getParent()).postInvalidateDelayed(100);
        }

    }

    @Override
    protected boolean pageLeft() {
        if (mFirstLayout) {
            mFirstLayout = false;
        }
        return super.pageLeft();
    }


    @Override
    protected boolean pageRight() {
        if (mFirstLayout) {
            mFirstLayout = false;
        }
        return super.pageRight();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getRepeatCount() > 0) {
            if ((System.currentTimeMillis() - mLastKeyTime) < MIN_KEY_DISPATCH_INTERVAL_MS) {
                return true;
            }
            mLastKeyTime = System.currentTimeMillis();
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    protected int getChildDrawingOrder(int childCount, int i) {
        if (i == 0 || childIndex.size() != childCount) {
            childDisToCenter.clear();
            childIndex.clear();
            int viewPagerCenterX = getViewCenterX(this);
            for (int m = 0; m < childCount; m++) {
                int dis = Math.abs(viewPagerCenterX - getViewCenterX(getChildAt(m)));
                if (childIndex.get(dis) != null) {
                    dis++;
                }
                childDisToCenter.add(dis);
                childIndex.put(dis, m);
            }
            Collections.sort(childDisToCenter);
        }
        int index = childIndex.get(childDisToCenter.get(childCount - 1 - i));
        return index;
    }

    private int getViewCenterX(View view) {
        int[] array = new int[2];
        view.getLocationInWindow(array);
        return array[0] + view.getWidth() >> 1;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mHandler.removeMessages(MSG_AUTO);
        mHandler.removeMessages(MSG_FOCUS);
    }

    /**
     * change item animation
     */
    class ScaleTransform implements PageTransformer {
        private float mScaleY = 0.81f;
        private float mScaleX = 0.64f;

        @Override
        public void transformPage(View page, float position) {
            if (position < -1 || position > 1) {
                // This page is way off-screen to the left/right.
                page.setScaleX(mScaleX);
                page.setScaleY(mScaleY);
            } else {
                //position < 0  1-2:1[0,-1] ;2-1:1[-1,0]
                // position > 0 1-2:2[1,0] ;2-1:2[0,1]
                page.setScaleY(getCurrentScale(position, mScaleY));
                page.setScaleX(getCurrentScale(position, mScaleX));
            }
        }

        private float getCurrentScale(float range, float scale) {
            return (1 + range * (range > 0 ? -1 : 1)) * (1 - scale) + scale;
        }
    }

    private View mCurrentView;

    public View getCurrentView() {
        return mCurrentView;
    }

    class InfinitePagerAdapter extends PagerAdapter {

        @Override
        public void setPrimaryItem(ViewGroup container, int position, Object object) {
            super.setPrimaryItem(container, position, object);
            mCurrentView = (View) object;
        }

        @Override
        public int getCount() {
            if (mDatas != null && mDatas.size() < 3) {
                return mDatas.size();
            }
            return Short.MAX_VALUE;
        }

        @Override
        public void startUpdate(ViewGroup container) {
            super.startUpdate(container);
            ViewPager viewPager = (ViewPager) container;
            int position = viewPager.getCurrentItem();
            if (position == 0) {
                position = getFirstItemPosition();
            } else if (position == getCount() - 1) {
                position = getLastItemPosition();
            }
            viewPager.setCurrentItem(position, true);
        }

        private int getFirstItemPosition() {
            if (getCount() < 3) {
                return 0;
            }
            return Short.MAX_VALUE / mDatas.size() / 2 * mDatas.size();
        }

        private int getLastItemPosition() {
            if (getCount() < 3) {
                return getCount() - 1;
            }
            return Short.MAX_VALUE / mDatas.size() / 2 * mDatas.size() - 1;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            View view = (View) object;
            container.removeView(view);
        }

        @Override
        public Object instantiateItem(ViewGroup container, final int position) {
            final GlideImageView imageView;
            imageView = new GlideImageView(mContext);
            imageView.setId(R.id.welfare_banner_view);
            imageView.setScaleType(ImageView.ScaleType.FIT_XY);
            imageView.setFocusable(true);
            imageView.setClearWhenDetached(false);
            if (mOnItemClickListener != null) {
                imageView.setOnClickListener(mOnItemClickListener);
            }
            if (mOnItemFocusChangeListener != null) {
                imageView.setOnFocusChangeListener(mOnItemFocusChangeListener);
            }
            final int realPos = position % mDatas.size();
            imageView.setTag(imageView.getId(), mDatas.get(realPos).id);
            container.addView(imageView);
            imageView.setImageRes(mDatas.get(realPos).poster);
            return imageView;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }
    }
}
