package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ProducerIntro;
import com.sohuott.tv.vod.lib.model.ProducerVideoList;
import com.sohuott.tv.vod.view.ListProducerView;
import java.lang.ref.WeakReference;
import java.util.List;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by rita on 16-1-27.
 */
public class ListProducerPresenterImpl {

    private static final int SINGLE_PAGE_SIZE = 20;
    private int mProducerId;
    private int mPlaylistId;

    private boolean mStopDataLoading = false;
    private ListProducerView mListView;

    public ListProducerPresenterImpl(int producerId) {
        mProducerId = producerId;
    }

    public void setView(ListProducerView view) {
        this.mListView = new WeakReference<ListProducerView>(view).get();
    }

    public void reloadActorRelativeDate() {
        mListView.showLoading();
        performProducerIntroRequest();
    }

    public void setActorId(int producerId) {
        mProducerId = producerId;
    }

    public void onLastItemViewed() {
        searchForMoreCharacters();
    }

    public void performProducerIntroRequest() {
        NetworkApi.getProducerIntro(mProducerId, new Observer<ProducerIntro>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(ProducerIntro value) {
                ProducerIntro.DataEntity producer = value.data;
                if (producer != null) {
                    mListView.setProducerIcon(producer.bigPhoto);
                    mListView.setProducerName(producer.nickName);
                    mListView.setProducerFanCount(producer.fanCount);
                    mListView.setProducerPlayCount(producer.playCount);
                    mListView.setProducerIntro(producer.signature);
                    mListView.hideLoading();
                    List<ProducerIntro.DataEntity.AlbumsEntity> albumList = producer.albums;
                    if (albumList != null && albumList.size() > 0) {
                        searchForProducerFirstVideo(albumList.get(0).playlistid);
                        mListView.showAlbumList(albumList);
                    } else {
                        mListView.onSideListError();
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                mListView.onSideListError();
                LibDeprecatedLogger.d("performProducerIntroRequest error: " + e.getMessage(), e);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("performProducerIntroRequest onComplete");
            }
        });
    }

    public void searchForProducerFirstVideo(final int playlistId) {
        mStopDataLoading = false;
        mPlaylistId = playlistId;
        mListView.showListLoading();
        NetworkApi.getProducerAlbumDetails(mPlaylistId, 1, new Observer<ProducerVideoList>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(ProducerVideoList value) {
                if (value != null && value.data !=
                        null && value.data.result != null
                        && value.data.result.videos != null
                        && value.data.result.videos.size() > 0
                        && (value.extend != null && value.extend.playListId == mPlaylistId)) {
                    mListView.setCount(value.data.count);
                    mListView.add(value.data.result.videos);
                    mListView.hideListLoading();
                } else {
                    mListView.onListError();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("searchForProducerFirstVideo error: " + e.getMessage(), e);
                mListView.onListError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("searchForProducerFirstVideo onComplete");
            }
        });
    }

    private void searchForMoreCharacters() {
        mListView.disableLastItemViewListener();
        int size = mListView.getAdapter().getItemCount();

        if (size % SINGLE_PAGE_SIZE != 0 || mStopDataLoading) {
            return;
        }

        NetworkApi.getProducerAlbumDetails(mPlaylistId, size / SINGLE_PAGE_SIZE + 1, new Observer<ProducerVideoList>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(ProducerVideoList value) {
                if (value == null || value.data == null) {
                    mStopDataLoading = true;
                    return;
                } else {
                    List<ProducerVideoList.DataEntity.ResultEntity.VideoDetails> list = value.data.result.videos;
                    if (list == null || list.size() < 1) {
                        mStopDataLoading = true;
                        return;
                    } else if (list.size() < 10) {
                        mStopDataLoading = true;
                        if (value.extend != null && value.extend.playListId == mPlaylistId) {
                            mListView.add(list);
                        }
                        return;
                    }

                    mListView.activateLastItemViewListener();
                    mListView.add(list);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("searchForMoreCharacters error: " + e.getMessage(), e);
                mListView.onListError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("searchForMoreCharacters onComplete");
            }
        });
    }
}
