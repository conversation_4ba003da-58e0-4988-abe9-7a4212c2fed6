package com.sohuott.tv.vod.adapter;

import android.graphics.Color;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.PersonalCinemaModel;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.HomeViewJump;

import java.lang.ref.WeakReference;
import java.util.List;

import static com.sohuott.tv.vod.adapter.PersonalCinemaAdapter.PERSONAL_TYPE_PGC_RECOMMEND;
import static com.sohuott.tv.vod.adapter.PersonalCinemaAdapter.PERSONAL_TYPE_STAR_MOVIE_RECOMMEND;
import static com.sohuott.tv.vod.adapter.PersonalCinemaAdapter.PERSONAL_TYPE_TAG_RECOMMEND;
import static com.sohuott.tv.vod.adapter.PersonalCinemaAdapter.PERSONAL_TYPE_VRS_RECOMMEND;

/**
 * Created by yizhang210244 on 2017/12/28.
 */

public class PersonalCinemaMovieListAdapter extends PersonalCinemaCommonAdapter{

    private List<PersonalCinemaModel.ContentsBean> mContentsBeanList;
    private int mType;
    private String mTagName;
    private int mStarId;

    public PersonalCinemaMovieListAdapter(RecyclerView rootRecyclerView, RecyclerView parentRecyclerView) {
        mRootRecyclerView = new WeakReference<RecyclerView>(rootRecyclerView).get();
        mParentRecyclerView = new WeakReference<RecyclerView>(parentRecyclerView).get();
        mParentRecyclerView.setOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if(recyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE
                        && mFocusBorderView != null){
                    View focusView = recyclerView.getFocusedChild();
                    if(focusView != null){
                        mFocusBorderView.setFocusView(focusView);
                        FocusUtil.setFocusAnimator(focusView, mFocusBorderView);
                    }
                    PersonalCinemaCommonAdapter adapter = (PersonalCinemaCommonAdapter) recyclerView.getAdapter();
                   ViewHolder item_viewholder = (ViewHolder) recyclerView.findViewHolderForAdapterPosition(adapter.getSelectedPosition());
                    if(item_viewholder != null){
                        item_viewholder.setTVOnFocus(item_viewholder.titleTV);
                    }
                    reBindImage();
                }
            }
        });

    }

    public void setContentsBeanList(List<PersonalCinemaModel.ContentsBean> contentsBeanList) {
        mContentsBeanList = contentsBeanList;
    }

    public void setType(int type) {
        mType = type;
    }

    public void setTagName(String tagName){
        mTagName = tagName;
    }

    public void setStarId(int starId){
        mStarId = starId;
    }

    private void reBindImage(){
        if(mParentRecyclerView != null){
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) mParentRecyclerView.getLayoutManager();
            if(linearLayoutManager != null){
                int lastItemPosition = linearLayoutManager.findLastVisibleItemPosition();
                int firstItemPosition = linearLayoutManager.findFirstVisibleItemPosition();
                if(firstItemPosition > 0){
                    firstItemPosition--;
                }
                if(lastItemPosition < (getItemCount() -1) ){
                    lastItemPosition++;
                }
                for (int i = firstItemPosition; i <= lastItemPosition; i++) {
                    ViewHolder viewHolder = (ViewHolder) mParentRecyclerView.findViewHolderForAdapterPosition(i);
                    if(viewHolder != null){
                        if(viewHolder.isShouldBindImageAgain){
                            PersonalCinemaModel.ContentsBean contentsBean = mContentsBeanList.get(i);
                            viewHolder.posterIV.setImageRes(contentsBean.getPicUrl());
                            viewHolder.mImageUrl = contentsBean.getPicUrl();
                            viewHolder.isShouldBindImageAgain = false;
                        }
                    }
                }
            }
        }
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v;
        RecyclerView.ViewHolder viewHolder = null;
        v = LayoutInflater.from(parent.getContext()).inflate(R.layout.personal_cinema_movie_list_item, parent, false);
        viewHolder = new ViewHolder(v);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        PersonalCinemaModel.ContentsBean contentsBean = mContentsBeanList.get(position);
        if(viewHolder != null){
            viewHolder.titleTV.setText(contentsBean.getName());
            if(mParentRecyclerView != null && mParentRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                viewHolder.posterIV.setImageRes(contentsBean.getPicUrl());
                viewHolder.mImageUrl = contentsBean.getPicUrl();
                viewHolder.isShouldBindImageAgain = false;
            }else {
                if(viewHolder.mImageUrl != null && !viewHolder.mImageUrl.equals(contentsBean.getPicUrl())){
                    viewHolder.posterIV.setImageResource(R.drawable.vertical_default_big_poster);
                }
                viewHolder.isShouldBindImageAgain = true;

            }

            setCornerType(contentsBean,viewHolder.posterIV);
            setHintTV(viewHolder.hintTV,contentsBean.getAlbumParam());
            if(contentsBean.getAlbumParam() != null){
                viewHolder.scoreSource = contentsBean.getAlbumParam().getScoreSource();
                viewHolder.score = contentsBean.getAlbumParam().getScore();
                viewHolder.doubanScore = contentsBean.getAlbumParam().getDoubanScore();
                viewHolder.showScore();
            }
        }
    }

    @Override
    public void onViewRecycled(RecyclerView.ViewHolder holder) {
//        ViewHolder viewHolder = (ViewHolder) holder;
//        if(viewHolder != null){
//            viewHolder.posterIV.clearImage();
//        }
        super.onViewRecycled(holder);
    }

    @Override
    public long getItemId(int position) {
        return position + 100;
    }



    @Override
    public int getItemCount() {
        if(mContentsBeanList != null){
            return mContentsBeanList.size();
        }
        return 0;
    }

    private void reportEvent(int aid){
        String stype = "";
        switch (mType){
            case PERSONAL_TYPE_VRS_RECOMMEND:
                stype = "6_personal_cinema_vrs_list_recommend_click";
                RequestManager.getInstance().onEvent("6_personal_cinema", stype,
                        String.valueOf(aid), null, null, null, null);
                break;
            case PERSONAL_TYPE_PGC_RECOMMEND:
                stype = "6_personal_cinema_pgc_list_recommend_click";
                RequestManager.getInstance().onEvent("6_personal_cinema", stype,
                        String.valueOf(aid), null, null, null, null);
                break;
            case PERSONAL_TYPE_STAR_MOVIE_RECOMMEND:
                stype = "6_personal_cinema_star_movie_list_recommend_click";
                RequestManager.getInstance().onEvent("6_personal_cinema", stype,
                        String.valueOf(mStarId), String.valueOf(aid), null, null, null);
                break;
            case PERSONAL_TYPE_TAG_RECOMMEND:
                stype = "6_personal_cinema_tag_list_recommend_click";
                RequestManager.getInstance().onEvent("6_personal_cinema", stype,
                        mTagName, String.valueOf(aid), null, null, null);
                break;
        }
    }

    private void setHintTV(TextView hintTV,PersonalCinemaModel.AlbumParamBean albumParam) {
        try {
            if (albumParam != null) {
                int cateCode = Integer.parseInt(albumParam.getCateCode());
                //100：电影；101：电视剧；106：综艺；107：纪录片；115：动漫；10001：美剧；
                if (cateCode == 101 || cateCode == 107 || cateCode == 115 || cateCode == 10001) {
                    int tvSets = Integer.parseInt(albumParam.getTvSets());
                    int latestVideoCount = Integer.parseInt(albumParam.getLatestVideoCount());
                    if (latestVideoCount != 0) {
                        if (tvSets == latestVideoCount) {
                            hintTV.setText(latestVideoCount + "集全");
                            setHintTVUI(hintTV, 0, albumParam.getLatestVideoCount().length());
                        } else {
                            hintTV.setText("更新至" + latestVideoCount + "集");
                            setHintTVUI(hintTV, 3, albumParam.getLatestVideoCount().length());
                        }
                        hintTV.setBackgroundResource(R.drawable.home_album_hint_tv_bg);
                        hintTV.setVisibility(View.VISIBLE);
                    } else {
                        hintTV.setVisibility(View.GONE);
                    }
                } else if (cateCode == 106) {
                    if (!TextUtils.isEmpty(albumParam.getShowDate())) {
                        hintTV.setBackgroundResource(R.drawable.home_album_hint_tv_bg);
                        hintTV.setText(albumParam.getShowDate() + "期");
                        hintTV.setVisibility(View.VISIBLE);
                    } else {
                        hintTV.setVisibility(View.GONE);
                    }
                } else {
                    hintTV.setVisibility(View.GONE);
                }
            } else {
                hintTV.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            hintTV.setVisibility(View.GONE);
        }
    }

    private void setHintTVUI(TextView hintTV,int index, int length) {
        ForegroundColorSpan whiteSpan = new ForegroundColorSpan(Color.parseColor("#d5d5d5"));
        ForegroundColorSpan yellowSpan = new ForegroundColorSpan(Color.parseColor("#ffbd5f"));
        SpannableStringBuilder builder = new SpannableStringBuilder(hintTV.getText().toString());
        if (index > 0) {
            builder.setSpan(whiteSpan, 0, index, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        builder.setSpan(yellowSpan, index, index + length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        builder.setSpan(whiteSpan, index + length, hintTV.getText().toString().length(),
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        hintTV.setText(builder);
    }

    private void setCornerType(PersonalCinemaModel.ContentsBean content, CornerTagImageView posterIV) {
        if(content != null) {
            try {
                int paddingX = posterIV.getResources().getDimensionPixelSize(R.dimen.x7);
                int paddingY = posterIV.getResources().getDimensionPixelSize(R.dimen.y7);
                posterIV.setCornerPaddingX(paddingX);
                posterIV.setCornerPaddingY(paddingY);
                String cornerType = "";
                if (content.getAlbumParam() != null) {
                    cornerType = content.getAlbumParam().getCornerType();
                }
                String tvIsFee = "";
                if (null != content.getAlbumParam()) {
                    tvIsFee = content.getAlbumParam().getTvIsFee();
                }
                int tvIsEarly = 0;
                if (content.getAlbumParam() != null) {
                    tvIsEarly = content.getAlbumParam().getTvIsEarly();
                }
                int ticketInt = 0;
                if (content.getAlbumParam() != null) {
                    ticketInt = content.getAlbumParam().getUseTicket();
                }
                int single = 0;
                if (content.getAlbumParam() != null) {
                    single = content.getAlbumParam().getPaySeparate();
                }
                int isFeeInt = HomeViewJump.string2Int(tvIsFee);
                int cornerTypeInt = HomeViewJump.string2Int(cornerType);
                posterIV.setCornerTypeWithType(isFeeInt, tvIsEarly, ticketInt, single,cornerTypeInt);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    class ViewHolder extends RecyclerView.ViewHolder{
        CornerTagImageView posterIV;
        TextView titleTV;
        TextView hintTV;
        TextView scoreTV;
        ImageView scoreIV;
        String score = "";
        String scoreSource = "";
        String doubanScore = "";
        String mImageUrl;
        boolean isShouldBindImageAgain = false;
        public ViewHolder(final View itemView) {
            super(itemView);
            posterIV = (CornerTagImageView) itemView.findViewById(R.id.posterIV);
            hintTV = (TextView) itemView.findViewById(R.id.hintTV);
            scoreIV = (ImageView) itemView.findViewById(R.id.doubangIV);
            scoreTV = (TextView) itemView.findViewById(R.id.scoreTV);
            titleTV = (TextView) itemView.findViewById(R.id.titleTV);
            posterIV.setCornerHeightRes(R.dimen.y40);
            posterIV.setClearWhenDetached(false);
            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if(event.getAction() != KeyEvent.ACTION_DOWN){
                        return false;
                    }
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_DPAD_LEFT:
                            if (getAdapterPosition() == 0) {
                                if(mParentRecyclerView != null
                                        && mParentRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                                    if(v.getAnimation() == null || v.getAnimation().hasEnded()){
                                        v.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                R.anim.shake_x));
                                        if(mFocusBorderView != null){
                                            mFocusBorderView.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                    R.anim.shake_x));
                                        }
                                    }
                                }
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                            if (getAdapterPosition() == getItemCount() - 1) {
                                if(mParentRecyclerView != null
                                        && mParentRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                                    if(v.getAnimation() == null || v.getAnimation().hasEnded()){
                                        v.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                R.anim.shake_x));
                                        if(mFocusBorderView != null){
                                            mFocusBorderView.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                    R.anim.shake_x));
                                        }
                                    }
                                }
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_UP:
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            break;
                        default:
                            break;
                    }
                    return false;
                }
            });

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if(hasFocus){
                        mSelectedPosition = getAdapterPosition();
                        if (mFocusBorderView != null) {
                            if(mRootRecyclerView != null&& mRootRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE){
                                return;
                            }
                            if(mParentRecyclerView != null && mParentRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE){
                                return;
                            }
                            mFocusBorderView.setFocusView(v);
                            FocusUtil.setFocusAnimator(v, mFocusBorderView);
                            setTVOnFocus(titleTV);
//                            showScore();
                        }
                    }else {
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(v);
                            FocusUtil.setUnFocusAnimator(v);
                        }
                        setTVUnFocus(titleTV);
//                        hideScore();
                    }
                }
            });

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    int position = getAdapterPosition();
                    PersonalCinemaModel.ContentsBean contentsBean = mContentsBeanList.get(position);
                    if(contentsBean != null){
                        if(contentsBean.getDataType() == 0){
                            ActivityLauncher.startVideoDetailActivity(v.getContext(), contentsBean.getAlbumId(), Constant.PAGE_GRID_LIST);
                            reportEvent(contentsBean.getAlbumId());
                        }else {
                            ActivityLauncher.startVideoDetailActivity(v.getContext(), contentsBean.getVideoId(),
                                    contentsBean.getCateCode() == CornerTagImageView.CORNER_TYPE_VR ? Constant.DATA_TYPE_VR : Constant.DATA_TYPE_PGC, Constant.PAGE_GRID_LIST);
                            reportEvent(contentsBean.getVideoId());
                        }
                    }
                }
            });

        }

        public void setTVOnFocus(TextView textView) {
            textView.setSelected(true);
            textView.setMarqueeRepeatLimit(-1);
            textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
        }

        public void setTVUnFocus(TextView textView) {
            textView.setSelected(false);
            textView.setEllipsize(TextUtils.TruncateAt.END);
        }

        public void showScore() {
            if ("1".equals(scoreSource)) {
                if (!TextUtils.isEmpty(score)) {
                    scoreTV.setText(score);
                    scoreTV.setVisibility(View.VISIBLE);
                    scoreIV.setVisibility(View.GONE);
                }
            } else if ("2".equals(scoreSource)) {
                if (!TextUtils.isEmpty(doubanScore)) {
                    scoreTV.setText(doubanScore);
                    scoreTV.setVisibility(View.VISIBLE);
                    scoreIV.setVisibility(View.VISIBLE);
                }
            }
        }

        private void hideScore() {
            scoreIV.setVisibility(View.GONE);
            scoreTV.setVisibility(View.GONE);
        }

    }
}
