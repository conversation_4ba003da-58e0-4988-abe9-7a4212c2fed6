package com.sohuott.tv.vod.widget;

import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.IdRes;
import androidx.core.content.res.ResourcesCompat;

import com.sohuott.tv.vod.R;

/**
 * <AUTHOR>
 */
public class BasePopupWindow extends PopupWindow {
    public BasePopupWindow(View contentView){
        this(contentView, ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,true);
    }
    public BasePopupWindow(View contentView, int width, int height, boolean focusable) {
        super(contentView, width, height, focusable);
        setBackgroundDrawable(ResourcesCompat
                .getDrawable(contentView.getResources()
                        , R.drawable.dialog_more_detail_bg, null));
        setTouchable(true);
        setFocusable(true);
        setOutsideTouchable(true);
        setAnimationStyle(R.style.PopupAnimation);
        setContentView(contentView);
    }

    public BasePopupWindow setText(@IdRes int resId, CharSequence content){
        ((TextView)getContentView().findViewById(resId)).setText(content);
        return this;
    }

    public void showWindow(View parent){
        showAtLocation(parent, Gravity.CENTER, 0, 0);
    }

}
