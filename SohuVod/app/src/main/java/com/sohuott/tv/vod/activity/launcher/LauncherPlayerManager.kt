package com.sohuott.tv.vod.activity.launcher

import android.content.Context
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.leanback.widget.Presenter.ViewHolder
import cn.gd.snmottclient.SNMOTTClient
import cn.gd.snmottclient.util.SNMOTTSDKCallBack
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.sh.ott.video.base.component.ShDataSource
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.base.OnStateChangeListener
import com.sh.ott.video.player.render.RenderFactory
import com.sh.ott.video.player.sofa.SofaPlayerFactory
import com.sh.ott.video.view.ShVideoView
import com.sohu.lib_utils.DeviceUtils
import com.sohu.ott.base.lib_user.UserApp
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.app.config.AppConfigDatabase
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.ContentGroup
import com.sohuott.tv.vod.lib.model.ContentGroup.DataBean.ContentsBean.AlbumListBean
import com.sohuott.tv.vod.lib.model.PianHuaItem
import com.sohuott.tv.vod.lib.model.SouthMediaCheckResult
import com.sohuott.tv.vod.lib.utils.EncryUtils
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.presenter.launcher.TypeNewFilmPresenterKt
import com.sohuott.tv.vod.presenter.launcher.TypeRecommendContentPresenterKt
import com.sohuott.tv.vod.presenter.launcher.TypeZeroContentPresenterKt.TypeZeroViewHolder
import com.sohuott.tv.vod.videodetail.activity.LogoInfo
import com.sohuott.tv.vod.videodetail.activity.addVideoPlayInfo
import com.sohuott.tv.vod.videodetail.activity.control.LauncherVideoStartPreparingComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoLogoComponent
import com.sohuott.tv.vod.videodetail.activity.conversionResolution
import com.sohuott.tv.vod.videodetail.activity.conversionVideoPlayResolutionInfo
import com.sohuott.tv.vod.videodetail.activity.getPlayResolution
import com.sohuott.tv.vod.videodetail.activity.repository.VideoRepository
import com.sohuott.tv.vod.videodetail.activity.state.VideoPlayResolutionInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

class LauncherPlayerManager :
    Handler.Callback, SNMOTTSDKCallBack, OnStateChangeListener {
    //    private var screenViewNew: SimplifyScaleScreenViewNew? = null
    private var vh: ViewHolder? = null
    private var index = 0
    private var loopCount = 0
    private var currentCount = 0
    private var context: Context? = null
    private var playList: List<PianHuaItem>? = null
    private var needMute = true
    private var countdownHelper: CountdownHelper? = null

    //        private var commonVideoView: CommonVideoView? = null
    private var playerCallback: PlayerCallback? = null
    private var isScale = false
    private val params = HashMap<Any, Any>()
    private val mGson = Gson()
    private var mHelper: LoginUserInformationHelper? = null
    var mHandler = Handler(Looper.getMainLooper())
    var aid: Int? = 0
    var vid: Int? = 0
    private var mScope: CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    private var videoRepository: VideoRepository = VideoRepository()

    private var mLauncherPlayerComponent: LauncherPlayerComponent? = null
    private var mVideoLogoComponent: VideoLogoComponent? = null


    private var mShVideoView: ShVideoView? = null


    val mRunnnable = Runnable {
        southMediaCheckPermission(
            mHelper?.loginPassport,
            Util.getSouthMediaLoginType(mHelper?.utype),
            aid,
            vid,
            0,
            0
        )
    }

    interface PlayerCallback {
        fun onPlayed()
        fun onPlayCompleted()
        fun onError()
    }

    fun setPlayerCallback(playerCallback: PlayerCallback) {
        this.playerCallback = playerCallback
    }

    //首页推荐流视频
    fun setPlayParamsAndPlayRecommend(
        context: Context?,
        albumListBean: AlbumListBean?,
        vh: ViewHolder,
        isScale: Boolean = false,
        mShVideoView: ShVideoView
    ) {
        LibDeprecatedLogger.d("setPlayParamsAndPlay, contentBean = ${albumListBean?.tvName}")
        if (mHelper == null) mHelper = LoginUserInformationHelper.getHelper(context);
        this.aid = albumListBean?.trailerId
        this.vid = albumListBean?.goodTrailerId
        if (aid == null || vid == null || aid == 0 || vid == 0) {
            AppLogger.e("动态视频 播放参数为空  aid :$aid  vid :$vid")
            releasePlayer()
            return
        }
        this.context = context
        this.vh = vh
        this.mShVideoView?.let {
            releasePlayer()
        }
//        this.commonVideoView = commonVideoView
        this.mShVideoView = mShVideoView
        this.needMute = false
        requestVideoInfoAndStart(context!!, aid, vid)
//        screenViewNew = SimplifyScaleScreenViewNew(context, commonVideoView).apply {
//            setIsRecommendVideo(true)
//            setSimplifyPlayerCallback(this@LauncherPlayerManager)
//            setCirculate(false)
//            setDynamicVideo(isScale)
//            setPlayParamsAndPlay(
//                aid!!,
//                vid!!,
//                Constant.DATA_TYPE_VRS
//            )
//        }
        mHandler.postDelayed(mRunnnable, 3000)

    }

    fun setPlayParamsAndPlay(
        context: Context?,
        contentBean: ContentGroup.DataBean.ContentsBean?,
        viewHolder: ViewHolder?,
        isVip: Boolean = false,
        isScale: Boolean = false,
        mShVideoView: ShVideoView
    ) {
        LibDeprecatedLogger.d("setPlayParamsAndPlay, contentBean = ${contentBean?.name}")
        this.context = context
        this.index = 0
        this.loopCount = 1
        this.currentCount = 0
        this.needMute = true
//        this.commonVideoView = commonVideoView
        this.mShVideoView?.let {
            releasePlayer()
        }
        this.mShVideoView = mShVideoView
        vh = viewHolder
        this.isScale = isScale

//        if (!contentBean?.parameterPianhua?.isNotEmpty()!!) {
//            AppLogger.d("${contentBean?.name} parameterPianhua == null")
//            return
//        }
        playList = convertToList(contentBean)
        //单片花 -> 3次
        //2片花 -> 2次
        //大于3片花 -> 1次
        //vip -> 1次

        loopCount = if (isVip) {
            1
        } else {
            when (playList?.size) {
                1 -> 3
                2 -> 2
                else -> 1
            }
        }
        LibDeprecatedLogger.d("setPlayParamsAndPlay, loopCount = ${loopCount}   playList  size：${playList?.size}")
        startPlay()
    }

    private fun convertToList(contentBean: ContentGroup.DataBean.ContentsBean?): List<PianHuaItem>? {
        LibDeprecatedLogger.d("pianhua = ${contentBean?.parameterPianhua}")
        var list: List<PianHuaItem>? = null
        try {
            list = Gson().fromJson(
                contentBean?.parameterPianhua,
                object : TypeToken<List<PianHuaItem>>() {}.type
            )
        } catch (e: Exception) {
            LibDeprecatedLogger.e("convertToList error = ${e.message}")
        }
        return list
    }


    fun play(context: Context?, index: Int) {
        val aid = playList?.get(index)?.albumId?.toInt()
        val vid = playList?.get(index)?.tvVerId?.toInt()
        if (aid == null || vid == null) {
            AppLogger.e("动态视频 播放参数为空  aid :$aid  vid :$vid")
            releasePlayer()
            return
        }
        mLauncherPlayerComponent?.setVolumeCountDown(false, 5)
        requestVideoInfoAndStart(context!!, aid, vid)

//        screenViewNew = SimplifyScaleScreenViewNew(context, commonVideoView).also {
//            it.setSimplifyPlayerCallback(this)
//            it.setCirculate(false)
//            it.setDynamicVideo(isScale)
//            it.setPlayParamsAndPlay(
//                aid,
//                vid,
//                Constant.DATA_TYPE_VRS
//            )
//        }
    }

    fun releasePlayer() {
        mHandler.removeCallbacks(mRunnnable)
        LibDeprecatedLogger.d("homePlay releasePlayer vh = $vh")
        if (vh is TypeZeroViewHolder) {
            (vh as TypeZeroViewHolder).binding.titleRoot.visibility =
                View.GONE
        }
//        mShVideoView?.setFilmMute(false)

        mLauncherPlayerComponent?.setVolumeCountDown(false, 5)
        mShVideoView?.removeFilmOnStateChangeListener(this@LauncherPlayerManager)
        mShVideoView?.release().also {
            mShVideoView?.removeAllVideoControlComponent()
            mLauncherPlayerComponent = null
            mShVideoView = null
        }
        countdownHelper?.cancel().also { countdownHelper = null }
        vh = null
        context = null
        playList = null
    }

    fun onPlayed() {
        mShVideoView?.setFilmMute(needMute)
        if (needMute) {
            if (countdownHelper == null) {
                countdownHelper = CountdownHelper(object : CountdownHelper.OnCountdownListener {
                    override fun onTick(secondsUntilFinished: Int) {
                        // 每秒执行一次该方法，倒计时剩余时间为 secondsUntilFinished 秒
                        println("Countdown: $secondsUntilFinished")
                        if (secondsUntilFinished == 0) {
//                            mShVideoView?.setFilmMute(false)
                            mLauncherPlayerComponent?.setVolumeCountDown(
                                false,
                                secondsUntilFinished
                            )
                            if (vh is TypeZeroViewHolder) {
                                (vh as TypeZeroViewHolder).binding.titleRoot.visibility =
                                    View.VISIBLE
                            }
                        } else {
//                            mShVideoView?.setFilmMute(true)
                            mLauncherPlayerComponent?.setVolumeCountDown(true, secondsUntilFinished)
                            if (vh is TypeZeroViewHolder) {
                                (vh as TypeZeroViewHolder).binding.titleRoot.visibility =
                                    View.GONE
                            }
                        }
                    }

                    override fun onFinish() {
                        // 倒计时结束，执行该方法
                        println("Countdown finished")
                        mLauncherPlayerComponent?.setVolumeCountDown(false, 0)
                        if (vh is TypeZeroViewHolder) {
                            (vh as TypeZeroViewHolder).binding.titleRoot.visibility =
                                View.VISIBLE
                        }
                        needMute = false
//                        mShVideoView?.setFilmMute(false)
                    }
                })
            }
            countdownHelper?.start()
        }
    }

    private fun startPlay() {
        play(context, index)
    }

    fun onPlayCompleted() {
        LibDeprecatedLogger.d("onPlayCompleted")
        //推荐流视频单独处理
        if (vh is TypeRecommendContentPresenterKt.TypeRecommendViewHolder) {
            (vh as TypeRecommendContentPresenterKt.TypeRecommendViewHolder).apply {
                binding.groupPlayComplete.visible()
            }
        } else {
            //其他需要循环
            if (index < playList?.size!! - 1) {
                //继续播放
                index += 1
                startPlay()
                LibDeprecatedLogger.d("onPlayCompleted index:$index")
            } else {
                currentCount += 1
                if (currentCount < loopCount) {//在循环里，重新播放
                    index = 0
                    LibDeprecatedLogger.d("onPlayCompleted currentCount:$currentCount")
                    startPlay()
                } else {
                    LibDeprecatedLogger.d("onPlayCompleted 恢复view")
                    //恢复view
                    when (vh) {
                        is TypeZeroViewHolder -> {
                            (vh as TypeZeroViewHolder).apply {
                                binding.rootPlayer.visibility = View.GONE
                                binding.typeZeroFocus.layoutParams?.height = 346
                                binding.rootPlayer.layoutParams?.height = 346
                            }
                        }

                        is TypeNewFilmPresenterKt.TypeNewFilmViewHolder -> {
                            val vh = vh as TypeNewFilmPresenterKt.TypeNewFilmViewHolder
                            vh.binding.rootPlayer.visibility = View.GONE
                        }
                    }
                    playerCallback?.onPlayCompleted()
                    releasePlayer()
                }
            }
        }


    }


    fun onError() {
        if (vh is TypeZeroViewHolder) {
            restoreTypeZeroPlayer(vh as TypeZeroViewHolder)
        } else if (vh is TypeNewFilmPresenterKt.TypeNewFilmViewHolder) {
            (vh as TypeNewFilmPresenterKt.TypeNewFilmViewHolder)?.binding?.rootPlayer?.visibility =
                View.GONE
        }
        playerCallback?.onError()
        releasePlayer()
    }

    override fun handleMessage(msg: Message): Boolean {
        when (msg.what) {
            1 -> {
                needMute = false
                mShVideoView?.setFilmMute(needMute)
            }
        }
        return true
    }

    fun isPlaying(): Boolean {
        return playState == PlayerConstants.VideoState.PLAYING
    }


    class CountdownHelper(private val onCountdownListener: OnCountdownListener) {

        private var countDownTimer: CountDownTimer? = null

        fun start() {
            countDownTimer =
                object : CountDownTimer(6000, 1000) { // 总倒计时时间为 5 秒，每隔 1 秒回调一次 onTick() 方法
                    override fun onTick(millisUntilFinished: Long) {
                        val seconds = millisUntilFinished / 1000 // 剩余的秒数
                        onCountdownListener.onTick(seconds.toInt())
                    }

                    override fun onFinish() {
                        onCountdownListener.onFinish()
                    }
                }.start()
        }

        fun cancel() {
            countDownTimer?.cancel()
        }

        interface OnCountdownListener {
            fun onTick(secondsUntilFinished: Int)
            fun onFinish()
        }
    }

    private fun restoreTypeZeroPlayer(viewHolder: TypeZeroViewHolder) {
        if (viewHolder.binding.rootPlayer.visibility == View.VISIBLE) {
            viewHolder.binding.typeZeroFocus.visibility = View.GONE
            viewHolder.binding.typeZeroFocus.layoutParams.height = 346
            viewHolder.binding.rootPlayer.layoutParams.height = 346
            viewHolder.binding.rootPlayer.visibility = View.GONE
        }
    }


    override fun onSuccess(p0: String?) {
        val value: SouthMediaCheckResult = mGson.fromJson(p0, SouthMediaCheckResult::class.java)
        LibDeprecatedLogger.d("南传播放鉴权返回成功$value")
        if (TextUtils.equals(value.getReturncode(), "998")) {
            //失败
            LibDeprecatedLogger.d("南传播放鉴权失败")
            mHandler.post {
                if (vh != null) {
                    (vh as TypeRecommendContentPresenterKt.TypeRecommendViewHolder).apply {
                        this.binding.posterVer.gone()
                        this.binding.playingIcon.gone()
                        this.binding.playingIcon.cancelWaveAnimation()
                        this.binding.nameBg.visible()
                        this.binding.rootPlayer.removeAllViews()
                        this.binding.rootPlayer.gone()
                        this.binding.groupPlayComplete.gone()
                    }
                }
                releasePlayer()
            }
        } else {
            LibDeprecatedLogger.d("南传播放鉴权成功")
        }
        LibDeprecatedLogger.d("南传播放鉴权 returnCode = " + value.returncode + "/returnMsg" + value.returnmsg)
    }

    override fun onFailure(p0: String?) {
        LibDeprecatedLogger.d("南传播放鉴权返回失败$p0")
    }


    fun southMediaCheckPermission(
        passport: String?,
        loginType: Int,
        aid: Int?,
        vid: Int?,
        videoType: Int,
        feeType: Int
    ) {
        SNMOTTClient.getInstance().getData(
            "pauth",
            mGson.toJson(getParams(context, passport, loginType, aid, vid, videoType, feeType)),
            this@LauncherPlayerManager
        )
    }

    fun getParams(
        context: Context?,
        passport: String?,
        logintype: Int,
        aid: Int?,
        vid: Int?,
        videoType: Int,
        feeType: Int
    ): HashMap<*, *> {
        val userId = UserInfoHelper.getGid() ?: ""
        val account = "SNM_$userId"
        var channel = Util.getPartnerNo(context)
        if (TextUtils.isEmpty(channel)) {
            channel = ""
        }
        var mac = DeviceUtils.getWifiMacAddress()
        mac = if (TextUtils.isEmpty(mac)) {
            ""
        } else {
            mac?.replace("%3A", ":")
        }
        val appVersionName = Util.getVersionName(context)
        val ip = Util.getIpv4Addr(context)
        val AES_IV = "adjiganaadjigana"
        params["source"] = "snm_sohu"
        params["mac"] = EncryUtils.AesDesEncrypt(
            mac,
            UserApp.Config.SNM_AES_SECRET,
            AES_IV,
            EncryUtils.Encryption.AES,
            EncryUtils.EncryptMode.ECB
        )
        params["wifimac"] = EncryUtils.AesDesEncrypt(
            mac,
            UserApp.Config.SNM_AES_SECRET,
            AES_IV,
            EncryUtils.Encryption.AES,
            EncryUtils.EncryptMode.ECB
        )
        params["clientip"] = EncryUtils.AesDesEncrypt(
            ip,
            UserApp.Config.SNM_AES_SECRET,
            AES_IV,
            EncryUtils.Encryption.AES,
            EncryUtils.EncryptMode.ECB
        )
        params["guid"] = userId
        params["snmaccount"] = account
        params["loginname"] = EncryUtils.AesDesEncrypt(
            passport,
            UserApp.Config.SNM_AES_SECRET,
            AES_IV,
            EncryUtils.Encryption.AES,
            EncryUtils.EncryptMode.ECB
        )
        params["logintype"] = if ("" == passport) "" else logintype.toString()
        params["vid"] = if (videoType == 0) "VRS$vid" else "PGC$vid"
        params["cid"] = if (videoType == 0) "VRS$aid" else "PGC$aid"
        params["videotype"] = "1"
        params["feetype"] = feeType
        params["authresult"] = 1
        params["playtype"] = 1
        params["qua"] = "PT=SNMYT&CHID=$channel&APP_VER=$appVersionName"

        return params
    }

    private var playState: Int = PlayerConstants.VideoState.IDLE

    override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
        this.playState = playState
        when (playState) {
            PlayerConstants.VideoState.PLAYBACK_COMPLETED -> {
                onPlayCompleted()
            }

            PlayerConstants.VideoState.ERROR -> {
                onError()
            }

            PlayerConstants.VideoState.PREPARED -> {
                mShVideoView?.setFilmMute(false)
                mShVideoView?.setBackgroundColor((ContextCompat.getColor(context!!, R.color.black)))
            }

            PlayerConstants.VideoState.PLAYING -> {
                onPlayed()
            }
        }
    }


    private fun requestVideoInfoAndStart(context: Context, aid: Int?, vid: Int?) {
        mShVideoView?.removeFilmOnStateChangeListener(this@LauncherPlayerManager)
        mShVideoView?.removeAllVideoControlComponent()
        mShVideoView?.background =
            (ContextCompat.getDrawable(context, R.drawable.scale_player_bg))
        mLauncherPlayerComponent = LauncherPlayerComponent(context)
        mVideoLogoComponent = VideoLogoComponent(context)
        mShVideoView?.addFilmVideoControlComponent(mLauncherPlayerComponent!!)
        mShVideoView?.addFilmVideoControlComponent(mVideoLogoComponent!!)
        mShVideoView?.addFilmVideoControlComponent(LauncherVideoStartPreparingComponent(context))
        mShVideoView?.setFilmScreenAspectRatioType(PlayerConstants.ScreenAspectRatio.DEFAULT)
        mShVideoView?.addFilmOnStateChangeListener(this@LauncherPlayerManager)
        mShVideoView?.setFilmPlayerFactory(SofaPlayerFactory.create())
        mShVideoView?.changeFilmRenderFactory(RenderFactory.textureViewRenderFactory())
        mScope.launch(Dispatchers.Main) {
            val videoInfo = videoRepository.requestVrsVideo(
                aid,
                vid,
                UserLoginHelper.getInstants().getLoginPassport(),
                false
            ).await()

            videoInfo.data?.playInfo?.addVideoPlayInfo()
                ?.conversionResolution(
                    AppConfigDatabase.getDefaultResolutionId(),
                    UserLoginHelper.getInstants().isVip(),
                    UserLoginHelper.getInstants().getIsLogin(),
                    action = { info ->
                        val url = info?.videoPlayInfo?.url
                        val dataSource = ShDataSource()
                        dataSource.adSkip = true
                        dataSource.url = url
                        val haslog = (videoInfo?.data?.logoInfo?.logo != 0)
                        mVideoLogoComponent?.setLogoInfo(LogoInfo().also {
                            it.dimension = videoInfo?.data?.logoInfo?.dimension
                            it.logo = videoInfo?.data?.logoInfo?.logo ?: 0
                            it.logoleft = videoInfo?.data?.logoInfo?.logoleft ?: 0
                            it.height = videoInfo?.data?.logoInfo?.height ?: 0f
                            it.width = videoInfo?.data?.logoInfo?.width ?: 0f
                            it.logoSideMargin = videoInfo?.data?.logoInfo?.side_margin ?: 0f
                            it.logoTopMargin = videoInfo?.data?.logoInfo?.top_margin ?: 0f
                            it.orientation = videoInfo?.data?.logoInfo?.orientation ?: 0
                        })
                        mVideoLogoComponent?.setChangeResolutionEnableLogo(info?.videoPlayInfo?.hasLogo ?: haslog)
                        mShVideoView?.setDataSource(dataSource)
                        mShVideoView?.prepareAsync()
                    })
        }
    }

}