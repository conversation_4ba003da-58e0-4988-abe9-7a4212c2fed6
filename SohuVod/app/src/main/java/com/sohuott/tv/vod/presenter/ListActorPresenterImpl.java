package com.sohuott.tv.vod.presenter;

import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ActorDetails;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.LocationConfigInfo;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.utils.StringUtils;
import com.sohuott.tv.vod.view.ListActorView;

import java.lang.ref.WeakReference;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by rita on 16-1-27.
 */
public class ListActorPresenterImpl {

    private static final String TAG = ListActorPresenterImpl.class.getSimpleName();

    private static final int SINGLE_PAGE_SIZE = 10;
    private int mActorId, mDirectorId;

    private boolean mStopDataLoading = false;
    private ListActorView mListView;

    public ListActorPresenterImpl(int actorId) {
        mActorId = actorId;
    }

    public void setDirectorId(int mDirectorId) {
        this.mDirectorId = mDirectorId;
    }

    public void setView(ListActorView view) {
        this.mListView = new WeakReference<ListActorView>(view).get();
    }

    public void reloadActorRelativeDate() {
        searchForCharacters();
        performActorRequest();
    }

    public void setActorId(int actorId) {
        mActorId = actorId;
    }

    public void onLastItemViewed() {
        searchForMoreCharacters();
    }

    public void searchForCharacters() {
        mListView.showLoading();
        NetworkApi.getActorOrDirectorVideoList(mActorId, mDirectorId, SINGLE_PAGE_SIZE, 1, new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                if (value != null && value.data !=
                        null && value.data.result != null) {
                    mListView.add(value.data.result);
                    mListView.hideLoading();
                } else {
                    mListView.onError();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("searchForCharacters error: " + e.getMessage(), e);
                mListView.onError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("searchForCharacters onComplete");
            }
        });
    }

    private void searchForMoreCharacters() {
        mListView.disableLastItemViewListener();
        int size = mListView.getAdapter().getItemCount();

        if (size % SINGLE_PAGE_SIZE != 0 || mStopDataLoading) {
            return;
        }

        NetworkApi.getActorOrDirectorVideoList(mActorId, mDirectorId, SINGLE_PAGE_SIZE, size / SINGLE_PAGE_SIZE + 1,
                new Observer<VideoGridListBean>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(VideoGridListBean value) {
                        if (value == null || value.data == null) {
                            mStopDataLoading = true;
                            return;
                        } else {
                            List<ListAlbumModel> list = value.data.result;
                            if (list == null || list.size() < 1) {
                                mStopDataLoading = true;
                                return;
                            } else if (list.size() < 10) {
                                mStopDataLoading = true;
                            }

                            mListView.activateLastItemViewListener();
                            mListView.add(list);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.e("searchForMoreCharacters error: " + e.getMessage(), e);
                        mListView.onError();
                    }

                    @Override
                    public void onComplete() {
                        LibDeprecatedLogger.d("searchForMoreCharacters onComplete");
                    }
                });
    }

    private String get252ImageUrl(ActorDetails.DataEntity list) {
        String resultUrl = list.imageTvSquare;
        AppLogger.d(TAG, "get252ImageUrl picUrl ? " + resultUrl);
        if (StringUtils.isNotEmptyStr(list.imageVerDefault)) {
//            if (picUrl.contains("//photocdn.tv.snmsohu.aisee.tv/")) {
//                resultUrl = picUrl.replace("/img/", "/img/c_lfill,w_252,h_252,g_faces/");
//            } else
            LocationConfigInfo.DataBean mConfigInfo = HomeData.getLocationConfigInfo(null);
            if (mConfigInfo != null && StringUtil.isNotEmpty(mConfigInfo.newImgDomain)) {
                AppLogger.d(TAG,
                        "get252ImageUrl mConfigInfo.newImgDomain ? " + mConfigInfo.newImgDomain);
                if (list.imageVerDefault.contains(mConfigInfo.newImgDomain)) {
                    resultUrl = list.imageVerDefault.replace("/img/", "/img/c_lfill,w_252,h_252,g_faces/");
                }
            }
        }
        AppLogger.d(TAG, "get252ImageUrl resultUrl ? " + resultUrl);
        return resultUrl;
    }

    public void performActorRequest() {
        NetworkApi.getActorOrDirectorDetails(mDirectorId > 0 ? mDirectorId : mActorId, new Observer<ActorDetails>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(ActorDetails value) {
                ActorDetails.DataEntity list = value.data;
                if (list != null) {
                    mListView.setBackground(get252ImageUrl(list));
                    mListView.setListTitle(list.name);
                    mListView.setListSubTitle(list.profession);
                    mListView.setListCategory(list.introduction);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("performActorRequest error: " + e.getMessage(), e);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("performActorRequest onComplete");
            }
        });

    }
}
