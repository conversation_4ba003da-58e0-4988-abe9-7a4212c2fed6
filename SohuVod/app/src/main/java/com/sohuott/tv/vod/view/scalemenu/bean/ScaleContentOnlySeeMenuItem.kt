package com.sohuott.tv.vod.view.scalemenu.bean

import com.google.gson.annotations.SerializedName

/**
 * Description:
 * Created by cuipengyu on 2023/7/6.
 * Last Modified: 2023/7/6
 */
data class ScaleContentOnlySeeMenuItem(
    var id: String = "",
    //展示的名称
    var name: String = "",
    //需要拼接图片的地址
    var imagesUrl: MutableList<String> = mutableListOf(),
    //本片段展示时长
    var allTime: String? = null,
    //是否是已经选择的状态
    var hasCurrentSelected: Boolean = false,
    //是否是只看TA模式
    var isUseOnlySeeModel: Boolean = false,
    //时间开始到结束的位置集合
    var timeArray: MutableList<VideoInfoOnlySeeTimeItem>? = null,
) {
}

data class VideoInfoOnlySeeItem(
    @SerializedName("starid")
    var starId: String = "",
    @SerializedName("name")
    var name: String = "",
    @SerializedName("minute")
    var minute: Long = 0,
    @SerializedName("imgurl")
    var imgUrl: String = "",
    @SerializedName("timeArray")
    var timeArray: MutableList<VideoInfoOnlySeeTimeItem>? = null,
) {

}

data class VideoInfoOnlySeeTimeItem(
    @SerializedName("start")
    var start: Long = 0,
    @SerializedName("end")
    var end: Long = 0,
) {

}