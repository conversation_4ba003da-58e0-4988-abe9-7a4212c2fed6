package com.sohuott.tv.vod.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.lib.model.HotSearchNew;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.SearchUtil;
import com.sohuott.tv.vod.widget.GlideImageView;

/**
 * Created by fenglei on 17-6-20.
 */

public class HotSearchItemView extends RelativeLayout implements View.OnClickListener, View.OnFocusChangeListener {

//    protected CornerTagImageView cornerTagDraweeView;
    protected GlideImageView cornerTagDraweeView;
    protected TextView titleTV;
    private int childIndex;
    private int type;
    private String mPageName = "6_search";

    public HotSearchItemView(Context context) {
        super(context);
        initUI(context);
    }

    public HotSearchItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initUI(context);
    }

    public HotSearchItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initUI(context);
    }

    public void customLayoutInflater(Context context){
        LayoutInflater.from(context).inflate(
                R.layout.search_no_input_adapter_hot_search_with_album_pic_item, this, true);
    }

    protected void initUI(Context context) {
        setFocusable(true);
//        setBackgroundResource(R.drawable.tnine_keyboard_item_view_selector);
        setOnClickListener(this);
        setOnFocusChangeListener(this);
//        LayoutInflater.from(context).inflate(
//                R.layout.search_no_input_adapter_hot_search_with_album_pic_item, this, true);
        //cornerTagDraweeView = (CornerTagImageView) findViewById(R.id.hot_search_video_poster_iv);
        customLayoutInflater(context);
        cornerTagDraweeView = (GlideImageView) findViewById(R.id.hot_search_video_poster_iv);
        titleTV = (TextView) findViewById(R.id.hot_search_video_title_tv);
    }

    public void setPageName(String pageName){
        this.mPageName = pageName;
    }

    public void setIndex(int i) {
        childIndex = i;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setUI(HotSearchNew.DataBean dataBean) {
        if(dataBean != null) {
            SearchUtil.showSearchTitle(dataBean, titleTV);
            cornerTagDraweeView.setImageRes(dataBean.getPic());
        }
        setTag(dataBean);
    }

//    public void setUI(SearchSuggest.DataBean.RBean rBean) {
//        if(rBean != null) {
//            SearchUtil.showSearchSuggestTitle(rBean, titleTV);
//            if(!TextUtils.isEmpty(rBean.getPic())) {
//                Uri uri = Uri.parse(rBean.getPic());
//                cornerTagDraweeView.setImageURI(uri);
//            }
//        }
//        setTag(rBean);
//    }
    //child override
    protected void jumpSearch(Context context,HotSearchNew.DataBean dataBean){
        SearchUtil.jumpSearch(context, dataBean);
    }
    protected void saveSearchHistory(Context context,HotSearchNew.DataBean dataBean){
        SearchUtil.saveSearchHistory(context, dataBean);
    }


    @Override
    public void onClick(View v) {
        try {
            if(v.getTag() instanceof HotSearchNew.DataBean) {
                HotSearchNew.DataBean dataBean = (HotSearchNew.DataBean) v.getTag();
                //SearchUtil.jumpSearch(v.getContext(), dataBean);
                jumpSearch(v.getContext(), dataBean);
                //SearchUtil.saveSearchHistory(v.getContext(), dataBean);
                saveSearchHistory(v.getContext(), dataBean);
                if(dataBean != null) {
                    if(type == HotSearchLayout.HOT) {
                        RequestManager.getInstance().onClickSearchHotItem(mPageName,childIndex + 1, dataBean.getAid());
                    } else {
                        RequestManager.getInstance().onClickSearchSuggestItem(mPageName,childIndex + 1, dataBean.getAid());
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if(keyCode == KeyEvent.KEYCODE_DPAD_LEFT && childIndex == 0) {
            return ((SearchInputActivity)getContext()).onPressLeftKeyRequestFocus(this);
        }
        return super.onKeyDown(keyCode, event);
    }

//    private void saveSearchHistory(SearchSuggest.DataBean.RBean rBean) {
//        if(rBean == null) {
//            return;
//        }
//        SearchHistoryDao searchHistoryDao = DaoSessionInstance.getDaoSession(getContext()).getSearchHistoryDao();
//        SearchHistory searchHistory = new SearchHistory();
//        searchHistory.setAlbumId(rBean.getAid());
//        searchHistory.setAlbumTitle(rBean.getT());
//        searchHistory.setPic_480_660(rBean.getHorBigPic());
//        searchHistory.setPic_640_480(rBean.getPic());
//        searchHistory.setClickCount(Integer.valueOf((int) System.currentTimeMillis()));
//        SearchHistory searchHistoryFromDb = searchHistoryDao.queryBuilder().where(
//                SearchHistoryDao.Properties.AlbumId.eq(rBean.getAid())).unique();
//        if (searchHistoryFromDb != null) {
//            searchHistory.setId(searchHistoryFromDb.getId());
//            searchHistoryDao.update(searchHistory);
//        } else {
//            searchHistoryDao.insert(searchHistory);
//        }
//    }

    protected FocusBorderView focusBorderView;

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.focusBorderView = focusBorderView;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
            if (focusBorderView != null) {
                focusBorderView.setFocusView(v);
                FocusUtil.setFocusAnimator(v, focusBorderView);
            }
            setTVOnFocus(titleTV);
        } else {
            if (focusBorderView != null) {
                focusBorderView.setUnFocusView(v);
                FocusUtil.setUnFocusAnimator(v);
            }
            setTVUnFocus(titleTV);
        }
    }

    protected void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    protected void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

}
