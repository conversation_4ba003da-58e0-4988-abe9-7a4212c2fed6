package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;

import androidx.leanback.widget.Presenter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.model.VipHeader;

import java.util.ArrayList;
import java.util.List;


public class TypeVipHeaderPresenter extends Presenter {
    private Context mContext;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        LibDeprecatedLogger.d("onCreateViewHolder");
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_vip_header_layout, parent, false);

        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        LibDeprecatedLogger.d("onBindViewHolder");
        ViewHolder vh = (ViewHolder) viewHolder;
        List<Button> btnList = new ArrayList();
        btnList.add(vh.btn1);
        btnList.add(vh.btn2);
        btnList.add(vh.btn3);
        btnList.add(vh.btn4);
        if (item instanceof VipHeader) {
            for (int i = 0; i < ((VipHeader) item).getList().size(); i++) {
                btnList.get(i).setText(((VipHeader) item).getList().get(i).name);
            }
        }

        Glide.with(mContext)
                .load(((VipHeader) item).getList().get(0).picUrl)
                .transform(new RoundedCorners(5))
                .into(vh.poster);
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {
        LibDeprecatedLogger.d("onUnbindViewHolder");
    }

    @Override
    public void onViewDetachedFromWindow(Presenter.ViewHolder holder) {
        LibDeprecatedLogger.d("onViewDetachedFromWindow");
        super.onViewDetachedFromWindow(holder);
    }


    public static class ViewHolder extends Presenter.ViewHolder {
        Button btn1, btn2, btn3, btn4;
        ImageView poster;
        public ViewHolder(View view) {
            super(view);
            btn1 = (Button) view.findViewById(R.id.vip_header_banner_btn1);
            btn2 = (Button) view.findViewById(R.id.vip_header_banner_btn2);
            btn3 = (Button) view.findViewById(R.id.vip_header_banner_btn3);
            btn4 = (Button) view.findViewById(R.id.vip_header_banner_btn4);
            poster = (ImageView) view.findViewById(R.id.vip_header_poster);
        }
    }
}
