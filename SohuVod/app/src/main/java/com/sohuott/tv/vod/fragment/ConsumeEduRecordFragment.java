package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.activity.BaseFragmentActivity;
import com.sohuott.tv.vod.activity.ListEduUserRelatedActivity;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.adapter.ConsumeEduRecordAdapter;
import com.sohuott.tv.vod.adapter.ListEduRecordAdapter;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ConsumeRecord;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.EduEmptyView;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

import static com.sohuott.tv.vod.activity.ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class ConsumeEduRecordFragment extends BaseFragment implements ListEduRecordAdapter.FocusController {

    private CustomLinearRecyclerView mConsumeRecordRecyclerView;
    private LoadingView mLoadingView;
    private LinearLayout mErrorView, mConsumeRecordView, mConsumeRecordHeader;
    private EduEmptyView mEmptyView;
    private FocusBorderView mFocusBorderView;
    private TextView mConsumeHintTextView;

    private ConsumeEduRecordAdapter mConsumeRecordAdapter;
    private CustomLinearLayoutManager mLayoutManager;

    private List<ConsumeRecord.DataEntity.ContentEntity> mConsumeRecordList;
    private List<ListAlbumModel> mVipRecommendList;

    private BaseFragmentActivity mActivity;
    private LoginUserInformationHelper mHelper;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View consumeRecordView = inflater.inflate(R.layout.fragment_consume_edu, container, false);
        initView(consumeRecordView);
        initData();

        RequestManager.getInstance().onConsumeRecordExposureEvent();
        setSubPageName("6_list_consume_record");
        HashMap pathInfo = new HashMap<>();
        pathInfo.put("pageId", "1015");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), pathInfo, null,
                null);
        return consumeRecordView;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mConsumeRecordList != null) {
            mConsumeRecordList.clear();
            mConsumeRecordList = null;
        }
        if (mVipRecommendList != null) {
            mVipRecommendList.clear();
            mVipRecommendList = null;
        }
        if (mConsumeRecordAdapter != null) {
            mConsumeRecordAdapter.releaseAll();
            mConsumeRecordAdapter = null;
        }
        mActivity = null;
        mHelper = null;
    }

    @Override
    public void onFocusSelected(int position) {
        (mActivity).focusLeftItem(position);
    }

    public void requestFocusAtPos(int position) {
        if (position < 0) {
            return;
        }
        if (mConsumeRecordRecyclerView != null && mConsumeRecordRecyclerView.getChildAt(position) != null) {
            mConsumeRecordRecyclerView.findViewHolderForAdapterPosition(
                    mLayoutManager.findFirstCompletelyVisibleItemPosition()).itemView.requestFocus();
        } else {
            if (mEmptyView != null) {
                mEmptyView.focusAtPos();
            }
        }
    }

    private void initView(View view) {
        mConsumeHintTextView = (TextView) view.findViewById(R.id.tv_consume_hint);
        mConsumeRecordView = (LinearLayout) view.findViewById(R.id.layout_consume_record);
        mConsumeRecordHeader = (LinearLayout) view.findViewById(R.id.layout_cr_header);
        mFocusBorderView = (FocusBorderView) view.findViewById(R.id.focus_border_view);
        mLoadingView = (LoadingView) view.findViewById(R.id.detail_loading_view);
        mErrorView = (LinearLayout) view.findViewById(R.id.err_view);
        mEmptyView = (EduEmptyView) view.findViewById(R.id.layout_edu_empty_view);
        mEmptyView.setParentTag(LIST_INDEX_CONSUME_RECORD);
        mEmptyView.setFocusBorderView(mFocusBorderView);
        mEmptyView.setFocusController(this);

        mConsumeRecordRecyclerView = (CustomLinearRecyclerView) view.findViewById(R.id.crv_consume_record);
        mConsumeRecordRecyclerView.setOnScrollListener(new FinishScrollListener());
        mLayoutManager = new CustomLinearLayoutManager(getContext());
        mLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mConsumeRecordRecyclerView.setLayoutManager(mLayoutManager);
        mConsumeRecordAdapter = new ConsumeEduRecordAdapter(getContext(), mConsumeRecordRecyclerView);
        mConsumeRecordAdapter.setFocusBorderView(mFocusBorderView);
        mConsumeRecordAdapter.setFocusController(this);
        mConsumeRecordRecyclerView.setAdapter(mConsumeRecordAdapter);
    }

    public void initData() {
        showLoadingView();
        if (mActivity == null) {
            if (getActivity() instanceof ListEduUserRelatedActivity) {
                mActivity = (ListEduUserRelatedActivity) getActivity();
            } else {
                mActivity = (ListUserRelatedActivity) getActivity();
            }
            mEmptyView.setParentTag(mActivity.getLeftSelectedTag());
        }
        if (mHelper == null) {
            mHelper = LoginUserInformationHelper.getHelper(getContext());
        }
        requestConsumeRecord();
    }

    private void displayEmptyView() {
        if (mActivity == null || mActivity.getLeftSelectedTag() != LIST_INDEX_CONSUME_RECORD &&
                mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_EDU_CONSUME_RECORD) {
            return;
        }

        mConsumeHintTextView.setVisibility(View.GONE);
        mConsumeRecordView.setVisibility(View.GONE);
        mConsumeRecordHeader.setVisibility(View.GONE);
        mEmptyView.setVisibility(View.VISIBLE);
        mEmptyView.setBtnVisibility(false);
        mLoadingView.setVisibility(View.GONE);
        requestVipRecommendList();
    }

    private void displayErrorView() {
        if (mActivity == null || mActivity.getLeftSelectedTag() != LIST_INDEX_CONSUME_RECORD &&
                mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_EDU_CONSUME_RECORD) {
            return;
        }

        mErrorView.setVisibility(View.VISIBLE);
        mConsumeHintTextView.setVisibility(View.GONE);
        mConsumeRecordView.setVisibility(View.GONE);
        mConsumeRecordHeader.setVisibility(View.GONE);
        mLoadingView.setVisibility(View.GONE);
    }

    private void displayConsumeRecordView() {
        if (mActivity == null) {
            return;
        }

        mEmptyView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.GONE);
        mLoadingView.setVisibility(View.GONE);
        mConsumeRecordAdapter.setDataSource(mConsumeRecordList);
        mConsumeRecordAdapter.notifyDataSetChanged();
        mConsumeHintTextView.setVisibility(View.VISIBLE);
        mConsumeRecordHeader.setVisibility(View.VISIBLE);
        mConsumeRecordView.setVisibility(View.VISIBLE);
    }


    private void showLoadingView() {
        if (mActivity == null || mActivity.getLeftSelectedTag() != LIST_INDEX_CONSUME_RECORD &&
                mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_EDU_CONSUME_RECORD) {
            return;
        }

        mErrorView.setVisibility(View.GONE);
        mEmptyView.setVisibility(View.GONE);
        mConsumeRecordView.setVisibility(View.GONE);
        mConsumeHintTextView.setVisibility(View.GONE);
        mConsumeRecordHeader.setVisibility(View.GONE);
        mLoadingView.setVisibility(View.VISIBLE);
    }

    private void showUnloginView() {
        if (mActivity == null || mActivity.getLeftSelectedTag() != LIST_INDEX_CONSUME_RECORD &&
                mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_EDU_CONSUME_RECORD) {
            return;
        }

        mLoadingView.setVisibility(View.GONE);
        mConsumeRecordView.setVisibility(View.GONE);
        mConsumeHintTextView.setVisibility(View.GONE);
        mConsumeRecordHeader.setVisibility(View.GONE);
        mEmptyView.setVisibility(View.VISIBLE);
        mEmptyView.setBtnVisibility(true);
        mEmptyView.setBtnListener(EduEmptyView.TAG_LOGIN);
        requestVipRecommendList();
    }

    private void hideLoadingView() {
        mLoadingView.setVisibility(View.GONE);
    }

    private void requestConsumeRecord() {
        if (mHelper.getIsLogin()) {
            NetworkApi.getUserConsumeRecord(21, 0,mHelper.getLoginPassport(), mHelper.getLoginToken(), -1, 0, 0,
                    1000, new Observer<ConsumeRecord>() {
                        @Override
                        public void onSubscribe(Disposable d) {

                        }

                        @Override
                        public void onNext(ConsumeRecord value) {
                            if (getActivity() != null && mActivity.getLeftSelectedTag()
                                    != LIST_INDEX_CONSUME_RECORD &&
                                    mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_EDU_CONSUME_RECORD) {
                                return;
                            }

                            hideLoadingView();
                            if (value != null && value.getData() != null && value.getData().getContent() != null
                                    && value.getData().getContent().size() > 0) {
                                mConsumeRecordList = value.getData().getContent();
                                displayConsumeRecordView();
                            } else {
                                displayEmptyView();
                            }
                        }

                        @Override
                        public void onError(Throwable e) {
                            LibDeprecatedLogger.e("requestConsumeRecord onError(): " + e.getMessage());
                            if (getActivity() != null && mActivity.getLeftSelectedTag()
                                    != LIST_INDEX_CONSUME_RECORD &&
                                    mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_EDU_CONSUME_RECORD) {
                                return;
                            }
                            displayErrorView();
                        }

                        @Override
                        public void onComplete() {
                            LibDeprecatedLogger.d("requestConsumeRecord onComplete()");
                        }
                    });
        } else {
            showUnloginView();
        }
    }

    private void requestVipRecommendList() {
        NetworkApi.getVideoList(5, 1, new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                LibDeprecatedLogger.d("requestVipRecommendList onNext()");
                if (getActivity() != null && mActivity.getLeftSelectedTag()
                        != LIST_INDEX_CONSUME_RECORD&&
                        mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_EDU_CONSUME_RECORD) {
                    return;
                }

                if (value != null && value.data != null && value.data.result.size() > 0) {
                    mVipRecommendList = value.data.result;
                    updateVipRecommendListView();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestVipRecommendList onError():" + e.getMessage());
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestVipRecommendList onComplete()");
            }
        });
    }

    private void updateVipRecommendListView() {
        if (mActivity == null) {
            return;
        }

        // Update data of mPromoteRecycler
        if (mActivity.getLeftSelectedTag() == LIST_INDEX_CONSUME_RECORD) {
//            mEmptyView.setListView(mVipRecommendList);
        }

        RequestManager.getInstance().onVipRecommendListExposureEvent();
    }

    private class FinishScrollListener extends RecyclerView.OnScrollListener {

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                if (mFocusBorderView != null) {
                    if (mConsumeRecordRecyclerView == null) {
                        return;
                    }
                    if (mConsumeRecordRecyclerView.getFocusedChild() == null) {
                        return;
                    }

                    RecyclerView.ViewHolder viewHolder = mConsumeRecordRecyclerView.getChildViewHolder(
                            mConsumeRecordRecyclerView.getFocusedChild());
                    if (viewHolder != null && viewHolder.itemView != null) {
                        mFocusBorderView.setFocusView(viewHolder.itemView);
                        mFocusBorderView.setScaleUp(1);
                    }
                }
            }
        }
    }
}
