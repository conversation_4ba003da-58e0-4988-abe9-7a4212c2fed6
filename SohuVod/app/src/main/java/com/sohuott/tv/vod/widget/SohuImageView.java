package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

public class SohuImageView extends ImageView {

    public OnReloadListener mOnReloadListener;

    public SohuImageView(Context context) {
        super(context);
    }

    public SohuImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SohuImageView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    protected void onDraw(Canvas canvas) {
        if (getDrawable() != null) {

            if (getDrawable() instanceof BitmapDrawable && ((BitmapDrawable) getDrawable()).getBitmap().isRecycled()) {
                if (mOnReloadListener != null) {
                    mOnReloadListener.onReload();
                }
                return;
            }
        }
        try {
            super.onDraw(canvas);
        } catch (RuntimeException e) {
            LibDeprecatedLogger.e("---Draw image view fail!---", e);
        }
    }

    public interface OnReloadListener {
        void onReload();
    }

}
