package com.sohuott.tv.vod.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.PersonalCinemaModel;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Created by yizhang210244 on 2018/1/8.
 */

public class PersonalCinemaStarAdapter extends PersonalCinemaCommonAdapter{

    private List<PersonalCinemaModel.StarContentsBean> mStarContentsBeanList;

    public PersonalCinemaStarAdapter(RecyclerView rootRecyclerView, RecyclerView parentRecyclerView) {
        mRootRecyclerView = new WeakReference<RecyclerView>(rootRecyclerView).get();
        mParentRecyclerView = new WeakReference<RecyclerView>(parentRecyclerView).get();
    }

    public void setStarContentsBeanList(List<PersonalCinemaModel.StarContentsBean> starContentsBeanList) {
        mStarContentsBeanList = starContentsBeanList;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v;
        RecyclerView.ViewHolder viewHolder = null;
        v = LayoutInflater.from(parent.getContext()).inflate(R.layout.personal_cinema_start_list_item, parent, false);
        viewHolder = new ViewHolder(v);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        PersonalCinemaModel.StarContentsBean starContentsBean = mStarContentsBeanList.get(position);
        if(viewHolder != null && starContentsBean != null){
            viewHolder.name.setText(starContentsBean.getName());
            viewHolder.avatar.setCircleImageRes(starContentsBean.getPicUrl());
        }

    }

    @Override
    public int getItemCount() {
        if(mStarContentsBeanList != null){
            return mStarContentsBeanList.size();
        }
        return 0;
    }

    class ViewHolder extends RecyclerView.ViewHolder{
        private ImageView coverView;
        private GlideImageView avatar;
        private TextView name;

        public ViewHolder(final View itemView) {
            super(itemView);
            coverView = (ImageView) itemView.findViewById(R.id.focus_cover);
            avatar = (GlideImageView) itemView.findViewById(R.id.focus);
            name = (TextView) itemView.findViewById(R.id.detail_actor_name);
            avatar.setClearWhenDetached(false);
            avatar.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if(event.getAction() != KeyEvent.ACTION_DOWN){
                        return false;
                    }
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_DPAD_LEFT:
                            if (getAdapterPosition() == 0) {
                                if(mParentRecyclerView != null
                                        && mParentRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                                    if(v.getAnimation() == null || v.getAnimation().hasEnded()){
                                        v.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                R.anim.shake_x));
                                    }
                                    if(coverView.getAnimation() == null || coverView.getAnimation().hasEnded()){
                                        coverView.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                R.anim.shake_x));
                                    }

                                }
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                            if (getAdapterPosition() == getItemCount() - 1) {
                                if(mParentRecyclerView != null
                                        && mParentRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                                    if(v.getAnimation() == null || v.getAnimation().hasEnded()){
                                        v.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                R.anim.shake_x));
                                    }
                                    if(coverView.getAnimation() == null || coverView.getAnimation().hasEnded()){
                                        coverView.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                R.anim.shake_x));
                                    }
                                }
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_UP:
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            break;
                        default:
                            break;
                    }
                    return false;
                }
            });

            avatar.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if(hasFocus){
                        mSelectedPosition = getAdapterPosition();
                        coverView.setVisibility(View.VISIBLE);
                    }else {
                        coverView.setVisibility(View.GONE);
                    }
                }
            });

            avatar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    int position = getAdapterPosition();
                    PersonalCinemaModel.StarContentsBean starContentsBean = mStarContentsBeanList.get(position);
                    if(starContentsBean != null){
                        switch (starContentsBean.getType()){
                            case 1:
                                //演员
                                ActivityLauncher.startActorListActivity(v.getContext(), starContentsBean.getId(), false, starContentsBean.getName());
                                RequestManager.getInstance().onEvent("6_personal_cinema", "6_personal_cinema_actor_click",
                                        String.valueOf(starContentsBean.getId()), null, null, null, null);
                                break;
                            case 2:
                                //导演
                                ActivityLauncher.startActorListActivity(v.getContext(), starContentsBean.getId(), true, starContentsBean.getName());
                                RequestManager.getInstance().onEvent("6_personal_cinema", "6_personal_cinema_director_click",
                                        String.valueOf(starContentsBean.getId()), null, null, null, null);
                                break;
                            case 3:
                                //出品人
                                ActivityLauncher.startProducerActivity(v.getContext(), starContentsBean.getId());
                                RequestManager.getInstance().onEvent("6_personal_cinema", "6_personal_cinema_producer_click",
                                        String.valueOf(starContentsBean.getId()), null, null, null, null);
                                break;
                        }
                    }
                }
            });

        }
    }

}