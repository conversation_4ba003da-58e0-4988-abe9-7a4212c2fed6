package com.sohuott.tv.vod.view;

import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.utils.FocusUtil;

/**
*
* <AUTHOR>
* created at 2017/10/16
*/
public class FocusLinearLayout extends LinearLayout implements View.OnFocusChangeListener {
    public FocusLinearLayout(Context context) {
        super(context);
        init(context,null);
    }

    public FocusLinearLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context,attrs);
    }

    public FocusLinearLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context,attrs);
    }

    FocusBorderView mFocusBorderView;

    private void init(Context context,AttributeSet attrs) {
        super.setOnFocusChangeListener(this);
        setFocusable(true);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, new int[]{R.attr.focus});
        int viewId = typedArray.getResourceId(0, 0);
        if (viewId > 0&& context instanceof Activity) {
            mFocusBorderView= (FocusBorderView) ((Activity)context).findViewById(viewId);
        }
        typedArray.recycle();
    }

    OnFocusChangeListener mOnFocusChangeListener;

    @Override
    public void setOnFocusChangeListener(OnFocusChangeListener l) {
        this.mOnFocusChangeListener = l;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
            if (mFocusBorderView != null) {
                mFocusBorderView.setFocusView(v);
                FocusUtil.setFocusAnimator(v, mFocusBorderView);
            }
        } else {
            if (mFocusBorderView != null) {
                mFocusBorderView.setUnFocusView(v);
                FocusUtil.setUnFocusAnimator(v);
            }
        }
        if(mOnFocusChangeListener!=null){
            mOnFocusChangeListener.onFocusChange(v,hasFocus);
        }
    }
}
