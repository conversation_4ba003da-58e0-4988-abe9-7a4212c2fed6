package com.sohuott.tv.vod.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.lib.model.FilterBean;
import com.sohuott.tv.vod.lib.model.MenuListBean;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.view.GridListViewNew;
import com.sohuott.tv.vod.widget.CornerTagImageView;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by wenjingbian on 2017/5/31.
 */

public class GridListVideoPresenterImplNew {

    private static final int PAGE_SIZE = 40;

    private GridListViewNew mGridListViewNew;
    private Context mContext;

    private List<FilterBean.DataEntity> mFilterList;
    private List<Integer> mFilterValue = new ArrayList<>();

    private int mOttCateId;
    private int mCateCode;
    private int mCurrPage;
    private int mCurrSize;
    private int mSubCateId;
    private int mCateCodeFirst; //identify sohu class video
    private boolean isPgc;
    private boolean isRequestData = true;
    private String mFilterParamStr;

    private Observable mObservable;

    public GridListVideoPresenterImplNew(Context context, GridListViewNew gridListView, int videoType,
                                         int cateCodeFirst, int ottCateId) {
        this.mContext = context;
        this.mGridListViewNew = gridListView;
        this.mCateCodeFirst = cateCodeFirst;
        this.mOttCateId = ottCateId;
        isPgc = videoType == 0 ? false : true;
    }

    /**
     * Request left list data
     */
    public void requestLeftList() {
        NetworkApi.getMenuList(mOttCateId, new Observer<MenuListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(MenuListBean value) {
                LibDeprecatedLogger.d("requestLeftList() onNext");
                if (value != null && value.data != null) {
                    mGridListViewNew.updateLeftListView(value.data);
                } else {
                    if (mGridListViewNew != null) {
                        mGridListViewNew.displayLeftErrorView();
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("requestLeftList() error, error = " + e.getMessage());
                if (mGridListViewNew != null) {
                    mGridListViewNew.displayLeftErrorView();
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestLeftList() onComplete");
            }
        });
    }

    /**
     * Request header list data
     * <p>
     * page == 1 & pageSize == 4
     */
    public void requestHeaderList() {
        NetworkApi.getTagList(mOttCateId, 1, 4, new Observer<AllLabel>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(AllLabel value) {
                LibDeprecatedLogger.d("requestHeaderList(): onNext().");
                if (value != null && value.data != null && value.data.result != null
                        && value.data.result.size() > 0) {
                    mGridListViewNew.setGridListHeaderData(value.data.result);
                } else {
                    mGridListViewNew.catchGridListHeaderDataError();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("requestHeaderList(), onError() = " + e.getMessage());
                mGridListViewNew.catchGridListHeaderDataError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestHeaderList(): onComplete()");
            }
        });
    }

    /**
     * Request filter list of the pointed ottCateCode
     */
    public void requestFilterList() {
        NetworkApi.getFilterList(UrlWrapper.getFilterListUrl(mCateCode, mOttCateId), new Observer<FilterBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(FilterBean value) {
                LibDeprecatedLogger.d("requestFilterList(): onNext()");
                if (value != null && value.data != null && value.data.size() > 0) {
                    mFilterList = value.data;
                    for (FilterBean.DataEntity dataEntity : value.data) {
                        mFilterValue.add(0);
                    }
                    mGridListViewNew.onRequestFilterListDone();
                } else {
                    LibDeprecatedLogger.d("requestFilterList(): onNext()-- data is null.");
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("requestFilterList(): onError()");
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestFilterList(): onComplete()");
            }
        });
    }

    /**
     * Request video list data
     *
     * @param subCateCode 0 if get all video data
     */
    public void requestVideoGridList(final int subCateCode, boolean isLongPress) {
        //reset current page number
        mCurrSize = 0;
        //display loading view on the right view
        mGridListViewNew.displayGridListLoadingView();
        //cancel network request
        if (mObservable != null) {
            mObservable.unsubscribeOn(Schedulers.io());
        }
        //request network to get grid list items of certain tab
        mObservable = NetworkApi.getVideoList(getRequestUrl(null, subCateCode, isLongPress), new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                LibDeprecatedLogger.d("requestVideoGridList() onNext()");
                if (value == null || value.extend == null) {
                    return;
                }

                //update video list view if subCateCode equals the selected tab's subCateCode
                if ((mSubCateId == 0 && (isPgc ? value.extend.subClassifyId == -1 : value.extend.id == -1))
                        || mSubCateId == (isPgc ? value.extend.subClassifyId : value.extend.id)) {
                    if (value.data != null && value.data.result != null && value.data.result.size() > 0) {
                        mGridListViewNew.updateGridListView(value.data, false, subCateCode);
                        mCurrSize = value.data.result.size();
                        isRequestData = isRequestData(value.data.count);
                    } else {
                        if (mGridListViewNew != null) {
                            mGridListViewNew.displayGridListErrorView(mContext.getResources().getString(R.string.data_empty));
                        }
                    }
                    RequestManager.getInstance().onGridListNewSubViewExposureEvent(mSubCateId);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("requestVideoGridList() onError(), e == " + e);
                if (mGridListViewNew != null) {
                    mGridListViewNew.displayGridListErrorView(mContext.getResources().getString(R.string.data_err));
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestVideoGridList() onComplete()");
            }
        });
    }

    /**
     * Request more video data
     *
     * @param filterParam filter string consist of filter values
     * @param subCateCode 0 if get all video data
     */
    public void requestMoreVideoData(String filterParam, int subCateCode, boolean isLongPress) {
        if (!isRequestData) {
            return;
        }
        NetworkApi.getVideoList(getRequestUrl(filterParam, subCateCode, isLongPress), new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                LibDeprecatedLogger.d("requestVideoGridList() onNext");
                if (value == null || value.extend == null) {
                    return;
                }

                //update data source of video data if subCateCode equals the selected tab's subCateCode
                if ((mSubCateId == 0 && (isPgc ? value.extend.subClassifyId == -1 : value.extend.id == -1))
                        || mSubCateId == (isPgc ? value.extend.subClassifyId : value.extend.id)) {
                    mGridListViewNew.addGridListItems(value.data);
                    if (value.data != null && value.data.result != null && value.data.result.size() > 0) {
                        mCurrSize += value.data.result.size();
                        isRequestData = isRequestData(value.data.count);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestVideoGridList() onError(), e == " + e);
                mGridListViewNew.addGridListItemsError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestVideoGridList() onComplete()");
            }
        });
    }

    /**
     * Request video data with filter values
     *
     * @param filterParam filter string consists of filter values
     */
    public void requestVideoGridListByFilter(String filterParam, boolean isLongPress) {
        //display child loading view
        mGridListViewNew.displayGridListFilterLoadingView();
        //reset current page number
        mCurrSize = 0;
        //reset filter value
        mFilterParamStr = filterParam;
        if (mObservable != null) {
            mObservable.unsubscribeOn(Schedulers.io());
        }
        mObservable = NetworkApi.getVideoList(getRequestUrl(filterParam, 0, isLongPress), new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                LibDeprecatedLogger.d("requestVideoGridListByFilter(): onNext().");
                if (value != null && value.data != null && value.data.result != null
                        && value.data.result.size() > 0) {
                    mGridListViewNew.updateGridListView(value.data, true, -100);
                    mCurrSize = value.data.result.size();
                    isRequestData = isRequestData(value.data.count);
                } else {
                    mGridListViewNew.displayGridListFilterErrorView(mContext.getResources().getString(R.string.data_err_filter));
                }
                RequestManager.getInstance().onGridListNewSubViewExposureEvent(mSubCateId);
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestVideoGridListByFilter(): onError()--" + e.getMessage());
                mGridListViewNew.displayGridListFilterErrorView(mContext.getResources().getString(R.string.data_err));
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestVideoGridListByFilter(): onComplete().");
            }
        });
    }

    public void requestMoreVideoDataByFilter(boolean isLongPress) {
        if (!isRequestData) {
            return;
        }

        NetworkApi.getVideoList(getRequestUrl(mFilterParamStr, 0, isLongPress), new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                LibDeprecatedLogger.d("requestMoreVideoDataByFilter() onNext");
                if (value != null && value.data != null && value.data.result != null
                        && value.data.result.size() > 0) {
                    //update data source of video data if subCateCode equals the selected tab's subCateCode
                    mGridListViewNew.addGridListItems(value.data);
                    mCurrSize += value.data.result.size();
                    isRequestData = isRequestData(value.data.count);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestMoreVideoDataByFilter(): onError()--" + e.getMessage());
                mGridListViewNew.addGridListItemsError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestMoreVideoDataByFilter(): onComplete().");
            }
        });
    }

    public List<FilterBean.DataEntity> getFilterList() {
        return mFilterList;
    }

    public List<Integer> getFilterValue() {
        return mFilterValue;
    }

    public void updateFilterValue(List<Integer> filterValue) {
        mFilterValue.clear();
        mFilterValue.addAll(filterValue);
    }

    /**
     * Get url when request video list data
     *
     * @param filterParam
     */
    private String getRequestUrl(String filterParam, int subCateCode, boolean isLongPress) {
        LibDeprecatedLogger.d("filterParam: " + filterParam + ", subCateCode: " + subCateCode + ", page = " + mCurrPage + ", isLongPress: " + isLongPress);
        if (!TextUtils.isEmpty(filterParam)) { //request video data with filter values
            if (isPgc) {
                if (mCateCodeFirst != CornerTagImageView.CORNER_TYPE_SOHUCLASS) { //PGC
                    return UrlWrapper.getPgcListForFilter(mOttCateId, getPageSize(isLongPress), mCurrPage, filterParam);
                } else { //Sohu class
                    return UrlWrapper.getSohuclassListForFilter(mOttCateId, getPageSize(isLongPress), mCurrPage, filterParam);
                }
            } else { //VRS
                return UrlWrapper.getVideoListForFilterByOttCateId(mOttCateId, getPageSize(isLongPress), mCurrPage, filterParam);
            }
        } else {
            this.mSubCateId = subCateCode;
            if (subCateCode == 0) { //request all video data
                if (isPgc) {
                    if (mCateCodeFirst != CornerTagImageView.CORNER_TYPE_SOHUCLASS) { //PGC
                        return UrlWrapper.getPgcVideoListForAll(mOttCateId, getPageSize(isLongPress), mCurrPage);
                    } else { //Sohu class
                        return UrlWrapper.getSohuclassVideoListForAll(mOttCateId, getPageSize(isLongPress), mCurrPage);
                    }
                } else { //VRS
                    return UrlWrapper.getVideoListForAll(mOttCateId, getPageSize(isLongPress), mCurrPage);
                }
            } else { //request video data with the pointed subCateCode
                if (isPgc) {
                    if (mCateCodeFirst != CornerTagImageView.CORNER_TYPE_SOHUCLASS) { //PGC
                        return UrlWrapper.getPgcListForMenu(mOttCateId, subCateCode, getPageSize(isLongPress), mCurrPage);
                    } else { //Sohu class
                        return UrlWrapper.getSohuclassListForMenu(mOttCateId, subCateCode, getPageSize(isLongPress), mCurrPage);
                    }
                } else { //VRS
                    return UrlWrapper.getVideoListForMenu(mOttCateId, subCateCode, getPageSize(isLongPress), mCurrPage);
                }
            }
        }
    }

    private int getPageSize(boolean isLongPress) {
        if (mCurrSize >= (2 * PAGE_SIZE) && mCurrSize % (2 * PAGE_SIZE) == 0 && isLongPress) {
            mCurrPage = mCurrSize / (2 * PAGE_SIZE) + 1;
            return 2 * PAGE_SIZE;
        } else {
            mCurrPage = mCurrSize / PAGE_SIZE + 1;
            return PAGE_SIZE;
        }
    }

    private boolean isRequestData(int count) {
        if (count <= mCurrSize) {
            return false;
        } else {
            return true;
        }
    }

    public void releaseAll() {
        mContext = null;
        if (mFilterList != null) {
            mFilterList.clear();
            mFilterList = null;
        }
        if (mFilterValue != null) {
            mFilterValue.clear();
            mFilterValue = null;
        }
    }
}
