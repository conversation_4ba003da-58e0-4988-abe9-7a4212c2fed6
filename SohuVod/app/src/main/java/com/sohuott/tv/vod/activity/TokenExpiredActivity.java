package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import com.sohuott.tv.vod.R;

/**
 * <AUTHOR>
 *         created at 2017/11/21
 */
public class TokenExpiredActivity extends BaseFragmentActivity implements View.OnClickListener {

    /**
     * 马上登录
     */
    private Button mLoginBtn;
    private Button mCancelBtn;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_token_expired);
        initView();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case  R.id.btn_cancel:
                finish();
                break;
            case R.id.btn_login:
                startActivity(new Intent(this,LoginActivity.class));
                finish();
                break;
        }
    }

    private void initView() {
        mLoginBtn = (Button) findViewById(R.id.btn_login);
        mLoginBtn.setOnClickListener(this);
        mCancelBtn = (Button) findViewById(R.id.btn_cancel);
        mCancelBtn.setOnClickListener(this);
    }
}
