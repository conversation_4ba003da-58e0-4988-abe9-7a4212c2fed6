package com.sohuott.tv.vod.adapter;

import android.os.Build;
import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.load.model.LazyHeaders;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.ui.ScaleFocusChangeListener;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.CornerTagImageView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by XiyingCao on 16-1-6.
 */
public class ListVideoAdapter extends RecyclerView.Adapter<ListVideoAdapter.ViewHolder> {

    // 当前被选择item的位置
    private int mSelctedPos = 0;
    // 上一个被选择的位置
    private int mSelctedPosPre = 0;
    // 创建ViewHolder的数量
    private int mViewHolderSize = 0;

    private RecyclerView mParent;
    private ArrayList<ListAlbumModel> mModels;
    private TagScaleFocusChangeListener mScaleFocusChangeListener;
    private TagListSelectedChangeCallback mTagListSelectedChangeCallback;
    private long mLabel;
    private boolean mContinueslyPlay;
    private boolean mIsDts = false;

    public ListVideoAdapter(RecyclerView recyclerView, long label) {
        mLabel = label;
        mModels = new ArrayList<>();
        mParent = new WeakReference<RecyclerView>(recyclerView).get();
        mScaleFocusChangeListener = new TagScaleFocusChangeListener();
        mViewHolderSize = 0;
    }

    public void setDts(boolean isDts) {
        mIsDts = isDts;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        mViewHolderSize++;
        LibDeprecatedLogger.w("List view size: " + mViewHolderSize);
        View modelView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_list, viewGroup, false);
        return new ViewHolder(modelView);
    }

    @Override
    public void onBindViewHolder(ViewHolder viewHolder, int position) {
        ListAlbumModel model = mModels.get(position);
        LibDeprecatedLogger.w("List position: " + position + "," + mSelctedPos+ "," + mSelctedPosPre);
        LibDeprecatedLogger.w("List Album:" + model.tvName + "," + model.tvVerPic);

        viewHolder.mImageView.setCornerHeightRes(R.dimen.y40);
        if (mIsDts) {
            // dts 不区分付费，只显示dts logo。
            viewHolder.mImageView.setCornerTypeWithType(0, 0, 0, 0, CornerTagImageView.CORNER_TYPE_DTS);
        } else {
            viewHolder.mImageView.setCornerTypeWithType(model.tvIsFee, model.tvIsEarly, model.useTicket, model.paySeparate, model.cornerType);
        }
        GlideUrl glideUrl = new GlideUrl(model.tvVerPic, new LazyHeaders.Builder()
                .addHeader("ImageTag", "ListVideo")
                .build());
        Glide.with(viewHolder.itemView.getContext())
                .load(glideUrl)
                .into(viewHolder.mImageView);
//        viewHolder.mImageView.setImageRes(model.tvVerPic);
        if (mSelctedPos == position) {
            viewHolder.itemView.requestFocus();
        }
    }

    @Override
    public void onAttachedToRecyclerView(RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        if (Util.isSupportTouchVersion(recyclerView.getContext())) {
            return;
        }

        recyclerView.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                return true;
            }
        });
    }

    @Override
    public int getItemCount() {
        if (mModels == null) {
            return 0;
        }
        return mModels.size();
    }

    public int getSelctedPos() {
        return mSelctedPos;
    }


    public void resetSelctedPos(int pos) {
        mSelctedPos = pos;
        mSelctedPosPre = 0;
        mViewHolderSize = 0;
    }

    public void updateLabel(long label) {
        mLabel = label;
    }

    public void clear() {
        mModels.clear();
        notifyDataSetChanged();
    }

    public void add(ListAlbumModel model) {
        if (model != null) {
            mModels.add(model);
            notifyItemInserted(getItemCount() - 1);
        }
    }

    public void add(List<ListAlbumModel> models) {
        if (models != null && models.size() > 0) {
            int size = getItemCount();
            this.mModels.addAll(models);
            notifyItemRangeInserted(size, models.size());
        }
    }

    public void setFocusBorderView(FocusBorderView view) {
        mScaleFocusChangeListener.setFocusBorderView(view);
    }

    public void setTagListSelectedChangeCallback(TagListSelectedChangeCallback callback) {
        mTagListSelectedChangeCallback = callback;
    }

    public ListAlbumModel getItem(int pos) {
        return mModels.get(pos);
    }

    public void setContinueslyPlay(boolean b) {
        mContinueslyPlay = b;
    }

    public interface TagListSelectedChangeCallback {
        void onSelectedChanged(int position);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        CornerTagImageView mImageView;

        public ViewHolder(View v) {
            super(v);
            mImageView = (CornerTagImageView) v.findViewById(R.id.album_image);
            v.setOnFocusChangeListener(mScaleFocusChangeListener);
            v.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (Util.isSupportTouchVersion(mParent.getContext())
                            && mTagListSelectedChangeCallback != null) {
                        if (v.getParent() instanceof RecyclerView) {
                            mSelctedPosPre = mSelctedPos;
                            mSelctedPos = ((RecyclerView) v.getParent()).getChildAdapterPosition(v);
                        }
                        mTagListSelectedChangeCallback.onSelectedChanged(mSelctedPos);
                    }

                    ListAlbumModel model = mModels.get(getAdapterPosition());
                    ActivityLauncher.startVideoDetailDts(v.getContext(), Constant.PAGE_GRID_LIST, model.id, Constant.DATA_TYPE_VRS, mIsDts, model.cateCode);
                    RequestManager.getInstance().onTagVideoListClickEvent(mLabel, model.id, model.cateCode);
                }
            });
        }
    }

    class TagScaleFocusChangeListener extends ScaleFocusChangeListener {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            super.onFocusChange(v, hasFocus);
            if (hasFocus && mTagListSelectedChangeCallback != null) {
                mTagListSelectedChangeCallback.onSelectedChanged(-1);
                if (v.getParent() instanceof RecyclerView) {
                    mSelctedPosPre = mSelctedPos;
                    mSelctedPos = ((RecyclerView) v.getParent()).getChildAdapterPosition(v);
                }
                LibDeprecatedLogger.w("List position: " + mSelctedPos+ "," + mSelctedPosPre);

                String deviceModel = Build.MODEL;
                // 极米适配使用备份直播地址
                if (deviceModel != null && deviceModel.contains("XGIMI")) {
                    LibDeprecatedLogger.d("device: " + deviceModel);
                    if (mSelctedPos >= 2 && mSelctedPosPre > mSelctedPos) {
                        // 往前滑
                        notifyItemChanged(mSelctedPos - 2);
                    } else if (mSelctedPos == 4 && mSelctedPosPre < mSelctedPos && mViewHolderSize > 5) {
                        // 往后滑
                        notifyItemChanged(5);
                    }
                }
            }
        }
    }
}
