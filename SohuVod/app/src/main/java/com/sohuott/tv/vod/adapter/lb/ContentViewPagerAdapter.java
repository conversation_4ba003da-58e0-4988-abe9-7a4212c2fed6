package com.sohuott.tv.vod.adapter.lb;


import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.sohuott.tv.vod.fragment.HomeAllCategoryFragmentNew;
import com.sohuott.tv.vod.fragment.MyFragment;
import com.sohuott.tv.vod.fragment.lb.HomeContentFragment;
import com.sohuott.tv.vod.lib.model.launcher.HomeTab;
import com.sohuott.tv.vod.lib.utils.Constant;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;


public class ContentViewPagerAdapter extends SmartFragmentStatePagerAdapter {
    private static final String TAG = "ContentViewPagerAdapter";

    private HashMap<Integer, WeakReference<Fragment>> fragments =
            new HashMap<Integer, WeakReference<Fragment>>();
    private List<HomeTab.TabItem> dataBeans;

    public ContentViewPagerAdapter(@NonNull FragmentManager fm) {
        super(fm);
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        int type = dataBeans.get(position).getType();
        Fragment fragment = null;
        Bundle bundle = new Bundle();
        if (type == Constant.TYPE_MY) {
            fragment = new MyFragment();
        } else if (type == Constant.TYPE_CLASSIFY) {
            fragment = new HomeAllCategoryFragmentNew();
        } else if (type == Constant.TYPE_CAROUSEL) {
//            fragment = new HomeCarouselFragment();
        } else {
//            if (fragments.get(position) != null) {
//                fragment = fragments.get(position).get();
//                if (fragment != null) {
//                    return fragment;
//                }
//            }
            fragment = new HomeContentFragment();

        }
        bundle.putInt("type", type);
        bundle.putInt("ottCategoryId", dataBeans.get(position).getOttCategoryId());
        bundle.putLong("cateCode", dataBeans.get(position).getCateCode());
        bundle.putInt("subClassifyId", dataBeans.get(position).getSubClassifyId());
        bundle.putLong("id", dataBeans.get(position).getId());
        bundle.putInt("position", position);
        fragment.setArguments(bundle);
        fragments.put(position, new WeakReference<Fragment>(fragment));
        return fragment;
    }

    public Fragment getFragment(int position) {
        if (fragments == null || fragments.get(position) == null) {
            return getItem(position);
        }
        return fragments.get(position).get();
    }

    @Override
    public int getCount() {
        return dataBeans == null ? 0 : dataBeans.size();
    }

    public void setData(List<com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem> dataBeans) {
        this.dataBeans = dataBeans;
    }
}
