package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.GridListActivityNew;
import com.sohuott.tv.vod.activity.GridListTagActivityNew;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wenjingbian on 2017/6/24.
 */

public class GridListTagAdapterNew extends RecyclerView.Adapter<GridListTagAdapterNew.HeaderViewHolder> {

    private RecyclerView mRecyclerView;
    private FocusBorderView mFocusView;
    private Context mContext;

    private List<AllLabel.LabelItem> mLabelList = new ArrayList<>();

    public GridListTagAdapterNew(Context context, RecyclerView recyclerView) {
        this.mContext = context;
        this.mRecyclerView = recyclerView;
    }

    @Override
    public HeaderViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.list_grid_header_item_new, parent, false);
        HeaderViewHolder viewHolder = new HeaderViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(HeaderViewHolder holder, int position) {
        AllLabel.LabelItem labelItem = mLabelList.get(position);
        String title = TextUtils.isEmpty(labelItem.name) ? "" : labelItem.name;
        holder.tv_label_title.setText(title);
        String likeCount = TextUtils.isEmpty(labelItem.likeCount) ? "" : labelItem.likeCount;
        holder.tv_label_like.setText(likeCount);

        Glide.with(mContext)
                .load(labelItem.smallPicUrl)
                .into(holder.giv_label);
    }

    @Override
    public int getItemCount() {
        return mLabelList != null ? mLabelList.size() : 0;
    }

    public void setLabelList(List<AllLabel.LabelItem> labelList) {
        this.mLabelList = labelList;
    }

    public void addLabelItems(List<AllLabel.LabelItem> labelList) {
        if (mLabelList != null) {
            int start = mLabelList.size();
            mLabelList.addAll(labelList);
            notifyItemRangeInserted(start, labelList.size());
        }
    }

    public void setFocusView(FocusBorderView focusView) {
        this.mFocusView = focusView;
    }

    public void releaseAll() {
        mContext = null;
        mRecyclerView = null;
        if (mLabelList != null) {
            mLabelList.clear();
            mLabelList = null;
        }
    }

    /**
     * Custom ViewHolder
     */
    class HeaderViewHolder extends RecyclerView.ViewHolder {

        TextView tv_label_title, tv_label_like;
        ImageView giv_label;


        public HeaderViewHolder(final View itemView) {
            super(itemView);

            tv_label_title = (TextView) itemView.findViewById(R.id.tv_label_title);
            tv_label_like = (TextView) itemView.findViewById(R.id.tv_label_like);
            giv_label = (ImageView) itemView.findViewById(R.id.giv_label);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ActivityLauncher.startListVideoActivity(mContext, mLabelList.get(getAdapterPosition()).id);
                    RequestManager.getInstance().onGridTagListNewTagClickEvent(mLabelList.get(getAdapterPosition()).id);
                }
            });

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if (event.getAction() != KeyEvent.ACTION_DOWN) {
                        return false;
                    }

                    int pos = getAdapterPosition();
                    if (mContext instanceof GridListActivityNew) {
                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                            if (mRecyclerView != null) {
                                mRecyclerView.setVisibility(View.GONE);
                            }
                            ((GridListActivityNew) mContext).focusRightView();
                            return true;
                        } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getRepeatCount() == 0) {
                            ((GridListActivityNew) mContext).setFocusRoute(false);
                            ((GridListActivityNew) mContext).focusOnTopBar();
                        } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && pos == 0) {
                            ((GridListActivityNew) mContext).setLeftSelectedPos();
                        } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && pos == getItemCount() - 1) {
                            if (mRecyclerView != null) {
                                mRecyclerView.setVisibility(View.GONE);
                            }
                            ((GridListActivityNew) mContext).focusRightView();
                            return true;
                        }
                    } else if (mContext instanceof GridListTagActivityNew) {
                        //for the first line of tag list
                        if (pos / 4 == 0 && keyCode == KeyEvent.KEYCODE_DPAD_UP
                                && event.getAction() == KeyEvent.ACTION_DOWN) {
                            if (event.getRepeatCount() == 0) {
                                ((GridListTagActivityNew) mContext).setFocusRoute(false);
                                ((GridListTagActivityNew) mContext).focusOnTopBar();
                                ((GridListTagActivityNew) mContext).setCurrLineTxt(-1);
                            }
                            return true;
                        }

                        //for last row of tag list
                        if (pos / 4 == (getItemCount() - 1) / 4 && event.getAction() == KeyEvent.ACTION_DOWN) {
                            if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                                return true;
                            } else if (pos == getItemCount() - 1 && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                                return true;
                            }
                        }

                        //for the first item of every line
                        if (pos % 4 == 0 && keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                                && event.getAction() == KeyEvent.ACTION_DOWN) {
                            ((GridListTagActivityNew) mContext).setSelectedPos();
                            ((GridListTagActivityNew) mContext).setCurrLineTxt(-1);
                            return true;
                        }
                        //for the last item of every line
                        if (pos % 4 == 3 && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT
                                && event.getAction() == KeyEvent.ACTION_DOWN) {
                            if (mRecyclerView == null) {
                                return false;
                            }
                            //next item will request focus if it exists.
                            if (mRecyclerView.findViewHolderForAdapterPosition(pos + 1) != null
                                    && mRecyclerView.findViewHolderForAdapterPosition(pos + 1).itemView != null) {
                                mRecyclerView.findViewHolderForAdapterPosition(pos + 1).itemView.requestFocus();
                            }
                            return true;

                        }
                    }
                    return false;
                }
            });

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    tv_label_title.setSelected(hasFocus);

                    if (hasFocus) {
                        tv_label_title.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                        tv_label_title.setMarqueeRepeatLimit(-1);
                        //focus itemView
                        if (mFocusView != null && mRecyclerView != null
                                && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                            mFocusView.setFocusView(itemView);
                            FocusUtil.setFocusAnimator(itemView, mFocusView, FocusUtil.HOME_SCALE, 100);
                        }

                        if (mContext instanceof GridListTagActivityNew) {
                            ((GridListTagActivityNew) mContext).setCurrLineTxt(getAdapterPosition());
                        }
                    } else {
                        tv_label_title.setEllipsize(TextUtils.TruncateAt.END);
                        //unfocus itemView
                        if (mFocusView != null) {
                            mFocusView.setUnFocusView(itemView);
                        }
                        FocusUtil.setUnFocusAnimator(itemView, 100);
                    }
                }
            });
        }
    }
}
