package com.sohuott.tv.vod.videodetail.activity.control


import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Handler
import android.os.Message
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.lib_dlna_core.SohuDlnaManger.Companion.getInstance
import com.sh.ott.video.ad.AdRequestFactory
import com.sh.ott.video.component.AdPauseControlComponent
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohuott.tv.vod.GlideApp
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.isGone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.widget.DrawableVerticalCenterTextView
import com.sohuott.tv.vod.widget.GlideImageView
import java.lang.ref.WeakReference

/**
 * 暂停广告展示
 */
open class BaseVideoAdPauseComponent constructor(context: Context) :
    BaseControlComponent(context), AdPauseControlComponent {

    var pauseImageView: GlideImageView? = null
    var pauseImageMaxView: ImageView? = null
    var pauseVideoPoster: ImageView? = null
    var tvPauseImageViewTips: TextView? = null
    var pauseTextView: DrawableVerticalCenterTextView? = null
    var pauseTextMaxView: TextView? = null
    var currentPlayState: Int? = null
    var posterUrl: String? = null
    var adType: Int = -1
    var adIndex = 0
    var mInnerHandler: InnerHandler? = null
    private var mImageUrls: MutableList<String?>? = null

    init {
        mInnerHandler = InnerHandler(this)
        layoutInflater.inflate(R.layout.video_component_pause, this, true)
        pauseImageView = findViewById(R.id.pauseImageView)
        pauseImageMaxView = findViewById(R.id.pauseImageMaxView)
        pauseVideoPoster = findViewById(R.id.pause_video_poster)
        tvPauseImageViewTips = findViewById(R.id.tv_pauseImageView_tips)
        pauseTextView = findViewById(R.id.pauseTextView)
        pauseTextMaxView = findViewById(R.id.pause_video_tips)
        gone()
    }

    override fun onAdRequestType(type: Int) {
        super.onAdRequestType(type)
        adType = type
    }

    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        currentPlayState = playState
    }


    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        return super.dispatchKeyEvent(event)
    }

    override fun onAdPauseError(iAdsLoadedError: String?) {
        adIndex = 0
        hide()
    }


    override fun onAdPauseImage(url: MutableList<String?>) {
        mImageUrls = url
        adIndex = 0
        showPauseImage(url.get(adIndex) ?: "")
    }

    inner class InnerHandler : Handler {
        var mWrapper: WeakReference<BaseVideoAdPauseComponent>? = null

        constructor(view: BaseVideoAdPauseComponent) {
            mWrapper = WeakReference(view);
        }

        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)

            when (msg.what) {
                MSG_HANDLER_PAUSE -> {
                    adIndex++
                    if (adIndex >= mImageUrls?.size ?: 0) {
                        adIndex = 0
                    }
                    showPauseImage(mImageUrls?.get(index = adIndex))
                }
            }
        }
    }

    fun showPauseImage(url: String?) {
        GlideApp.with(context).load(url).placeholder(pauseImageView?.getDrawable())
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    LibDeprecatedLogger.d("load pauseAd failed")
                    hide()
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any?,
                    target: Target<Drawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    pauseImageView?.setImageDrawable(resource)
                    if (pauseImageView?.visibility != VISIBLE) {
                        pauseImageView?.visible()
                    }
//                    if (mSohuVideoPlayer != null && !mSohuVideoPlayer.isPlaying()) {
//                        AppLogger.d("show pauseAd")
//                        //                            mPauseAdFlagView.setVisibility(VISIBLE);
                    tvPauseImageViewTips?.isGone(getInstance().getIsDlna())
                    if (currentPlayState == PlayerConstants.VideoState.PAUSED) {
                        pauseTextView?.visibility = VISIBLE
                        if (!hasShow()) {
                            visible()
                        }
                    } else {
                        hide()
                    }
                    AdRequestFactory.getInstants().reportPauseAd(index = adIndex)
                    val message = mInnerHandler?.obtainMessage()
                    message?.what = MSG_HANDLER_PAUSE
                    message?.let { mInnerHandler?.sendMessageDelayed(it, HIDE_PAUSE_AD_DELAY) }
                    return false
                }

            }).into(pauseImageView!!)

    }

    private fun setPauseTextTips() {
        val originalText = "按【OK】键继续播放，按【上键】关闭广告"
        val spannableString = SpannableString(originalText)

        // 定义颜色
        val color = Color.parseColor("#FF6247")

        // 替换并设置【OK】的颜色
        val okText = "【OK】"
        val okStartIndex = originalText.indexOf(okText)
        if (okStartIndex != -1) {
            spannableString.setSpan(
                ForegroundColorSpan(color),
                okStartIndex,
                okStartIndex + okText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        // 替换并设置【上键】的颜色
        val upKeyText = "【上键】"
        val upKeyStartIndex = originalText.indexOf(upKeyText)
        if (upKeyStartIndex != -1) {
            spannableString.setSpan(
                ForegroundColorSpan(color),
                upKeyStartIndex,
                upKeyStartIndex + upKeyText.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        pauseTextMaxView?.text = spannableString
    }

    override fun onAdPauseMaxImage(url: String?) {
        tvPauseImageViewTips?.gone()
        if (url.isNullOrEmpty()) {
            hide()
            return
        }
        visible()
        pauseImageMaxView?.setVisibility(VISIBLE)
        GlideApp.with(context).load(url).listener(object : RequestListener<Drawable?> {
            override fun onLoadFailed(
                e: GlideException?,
                o: Any,
                target: Target<Drawable?>,
                b: Boolean
            ): Boolean {
                LibDeprecatedLogger.e("onAdPauseMaxImage onLoadFailed:${e?.localizedMessage}")
                pauseImageMaxView?.gone()
                return false
            }

            override fun onResourceReady(
                drawable: Drawable?,
                o: Any,
                target: Target<Drawable?>,
                dataSource: DataSource,
                b: Boolean
            ): Boolean {
                AdRequestFactory.getInstants().reportPauseAd(0)
                adIndex = 0
                pauseImageMaxView?.setVisibility(VISIBLE)
                showPauseVideoPoster()
//                Advert.getInstance().reportPauseAd(mAdCommon)
                return false
            }
        }).into(pauseImageMaxView!!)
    }

    fun hide() {
        hide(true)
    }

    fun hide(isReport: Boolean = true) {
        if (hasShow()) {
            if (isReport) {
                AdRequestFactory.getInstants().reportPauseAdClose(adIndex)
            }
            gone()
            adIndex = 0
            mInnerHandler?.removeCallbacksAndMessages(null)
        }
    }

    fun hasShow(): Boolean {
        return visibility == VISIBLE
    }

    override fun onAdPauseVideo(url: String?) {
        adIndex = 0
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
    }

    fun showPauseVideoPoster() {
        pauseTextMaxView?.visible()
        setPauseTextTips()
        GlideApp.with(context).load(posterUrl)
            .transform(RoundedCorners(context.resources.getDimensionPixelOffset(R.dimen.x10)))
            .addListener(object : RequestListener<Drawable?> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable?>?,
                    isFirstResource: Boolean
                ): Boolean {
                    pauseVideoPoster?.setVisibility(GONE)
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any?,
                    target: Target<Drawable?>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    pauseVideoPoster?.setVisibility(VISIBLE)
                    return false
                }
            }).into(pauseVideoPoster!!)
    }


    override fun onAdVideoTime(time: Int) {
        super.onAdVideoTime(time)
    }


    companion object {
        const val MSG_HANDLER_PAUSE = 0xe111
        const val HIDE_PAUSE_AD_DELAY = 5000L //暂停广告轮播，5秒钟切换
    }
}