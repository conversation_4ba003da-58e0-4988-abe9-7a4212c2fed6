package com.sohuott.tv.vod.adapter;


import androidx.recyclerview.widget.RecyclerView;

import android.util.SparseArray;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/4/18.
 */


public abstract class QFMyBobusRvAdapter<T> extends RecyclerView.Adapter<QFMyBobusRvAdapter.VH> {

//    public interface OnLoadMoreListener {
//        /**
//         * 加载更多的回调方法
//         * @param isReload 是否是重新加载，只有加载失败后，点击重新加载时为true
//         */
//        void onLoadMore(boolean isReload);
//    }

    public static final int TAG_EMPTY_ITEM = 0;
    public static final int TAG_BONUS_ITEM = 1;
    public static final int TAG_CASH_ITEM = 2;
    public static final int TAG_FOOTER_ITEM = 3;

    private List<T> mDatas;

    public List<T> getDatas() {
        return mDatas;
    }

    private AtomicInteger itemCount_exist;

    private boolean mIsLoading;

    private boolean mRefreshAndLoadMoreEnable;

    private int mFooterItemCount;

    public int getFooterItemCount(){return mFooterItemCount;}

    public boolean isRefreshAndLoadMoreEnable() {
        return mRefreshAndLoadMoreEnable;
    }

    public void setRefreshAndLoadMoreEnable(boolean mRefreshAndLoadMoreEnable) {
        this.mRefreshAndLoadMoreEnable = mRefreshAndLoadMoreEnable;
    }

    public QFMyBobusRvAdapter() {
        Init();
    }

    public QFMyBobusRvAdapter(List<T> datas) {
        Init();
        this.mDatas = datas;
    }

    private void Init() {
        mRefreshAndLoadMoreEnable = false;
        mIsLoading = false;
        itemCount_exist = new AtomicInteger(0);
    }

    public void setDataSource(List<T> dataSource) {
        this.mDatas = dataSource;
    }

    public void updateDataSource(List<T> dataSource) {
        this.notifyItemRangeRemoved(0, this.getItemCount());
        this.mDatas = dataSource;
        this.notifyItemRangeInserted(0, this.getItemCount());
    }

    public void insertDataSource(List<T> dataSource) {
        if (dataSource == null)
            return;

        if (mDatas!=null) {
            if (dataSource.size()<=mDatas.size())
                return;
            this.notifyItemRangeInserted(this.mDatas.size(), dataSource.size() - this.mDatas.size());
        }
        else
            this.notifyItemRangeInserted(0, dataSource.size());

        this.mDatas = dataSource;
        this.notifyItemRangeChanged(0, this.getItemCount());
    }

//    public void updateDataSource(List<T> dataSource) {
//        if ((this.mDatas != null)&&(this.mDatas.size()>0))
//        {
//            if (dataSource!=null)
//            {
//                if(this.mDatas.size()>dataSource.size())
//                    this.notifyItemRangeRemoved(dataSource.size(), this.mDatas.size() - dataSource.size());
//                else if (this.mDatas.size()<dataSource.size())
//                    this.notifyItemRangeInserted(this.mDatas.size(), dataSource.size()- this.mDatas.size());
//            }
//            else
//                this.notifyItemRangeRemoved(0, this.getItemCount());
//        }
//        else
//        {
//            this.notifyItemRangeRemoved(0, this.getItemCount());
//        }
//
//        this.mDatas = dataSource;
//        this.notifyItemRangeChanged(0, this.getItemCount());
//    }

    public abstract int getLayoutId(int viewType);

    @Override
    public VH onCreateViewHolder(ViewGroup parent, int viewType) {
        LibDeprecatedLogger.d("onCreateViewHolder():" + itemCount_exist.getAndIncrement());
        return VH.get(parent, getLayoutId(viewType));
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public void onBindViewHolder(VH holder, int position) {

        if (mDatas == null || mDatas.size() <= 0 || position >= mDatas.size() ) {
            return;
        }

        convert(holder, mDatas.get(position), position);
    }

    @Override
    public int getItemCount() {
        if (mDatas == null)
            return 1;
        else if (mDatas.size() == 0)
            return 1;
        else
            return mDatas.size() + mFooterItemCount;
    }

    @Override
    public int getItemViewType(int position) {
        if ((this.getDatas() == null)||(this.getDatas().size()==0))
            return TAG_EMPTY_ITEM;
        else if(isFooterItem(position))
            return TAG_FOOTER_ITEM;
        else
            return TAG_BONUS_ITEM;
    }


    public boolean isFooterItem(int postion) {
        if (mFooterItemCount <= 0)
            return false;
        else if (mDatas!=null)
            return postion >= mDatas.size();
        else
            return false;
    }

    public void setIsLoading(boolean mIsLoading) {
        this.mIsLoading = mIsLoading;
        if (mFooterItemCount > 0) {
            this.notifyItemRemoved(this.getItemCount() - 1);
            mFooterItemCount = 0;
        }
    }

    public boolean isIsLoading() {
        return mIsLoading;
    }

    public void LoadMore() {
        if(!mIsLoading) {
            mIsLoading = true;
            mFooterItemCount = 1;
            this.notifyItemInserted(this.getItemCount() - 1);
        }
    }

    public abstract void convert(VH holder, T data, int position);

    public void releaseAll() {
        if(mDatas != null) {
            mDatas.clear();
            mDatas = null;
        }
    }


    public static class VH extends RecyclerView.ViewHolder {
        private SparseArray<View> mViews;

        public View getConvertView() {
            return mConvertView;
        }

        private View mConvertView;

        private VH(View v) {
            super(v);
            mConvertView = v;
            if (mViews == null)
                mViews = new SparseArray<>();
        }

        public static VH get(ViewGroup parent, int layoutId) {
            View convertView = LayoutInflater.from(parent.getContext()).inflate(layoutId, parent, false);
            return new VH(convertView);
        }

        public <T extends View> T getView(int id) {
            if (mViews == null)
                mViews = new SparseArray<>();

            if (mConvertView == null)
                return null;

            View v = mViews.get(id);

            if (v == null) {
                v = mConvertView.findViewById(id);
                mViews.put(id, v);
            }
            return (T) v;
        }

        public void setText(int id, String value) {
            TextView view = getView(id);
            view.setText(value);
        }

        @Override
        protected void finalize() throws Throwable {
            if(mViews!=null) {
                mViews.clear();
                mViews = null;
            }
            mConvertView = null;
            super.finalize();
        }


    }

}

