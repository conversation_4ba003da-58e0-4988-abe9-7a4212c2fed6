package com.sohuott.tv.vod.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.GridListActivityNew;
import com.sohuott.tv.vod.lib.model.FilterBean;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.lang.ref.WeakReference;

/**
 * Created by rita on 16-2-1.
 */
public class FilterItemAdapter extends RecyclerView.Adapter<FilterItemAdapter.ViewHolder> {
    private RecyclerView mParent;
    private FocusBorderView mFocusView;

    private FilterBean.DataEntity mFilterData;
    private int mSelectedPos = 0;

    public int getFocusPos() {
        return mFocusPos;
    }

    private int mFocusPos = -1;
    private boolean mFocus;
    private SelectItemChangeCallback mCallback;


    public FilterItemAdapter(RecyclerView recyclerView) {
        mFilterData = new FilterBean.DataEntity();
        mParent = new WeakReference<RecyclerView>(recyclerView).get();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(
                R.layout.new_filter_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        holder.listItem.setText(mFilterData.cateValues.get(position).cateValue);
        if (position == mSelectedPos) {
            holder.listItem.setSelected(true);
        } else {
            holder.listItem.setSelected(false);
        }
        if (mFocus && position == mFocusPos) {
            holder.listItem.requestFocus();
        }
    }

    @Override
    public int getItemCount() {
        if (mFilterData.cateValues == null) {
            return 0;
        }
        return mFilterData.cateValues.size();
    }

    public void setFocusView(FocusBorderView focusView) {
        this.mFocusView = focusView;
    }

    public String getItemValue() {
        return mFilterData.cateValues.get(mSelectedPos).searchKey;
    }

    public void add(FilterBean.DataEntity model) {
        mFilterData = model;
        notifyDataSetChanged();
    }

    public int getSelectPos() {
        if (mSelectedPos < 0 || mSelectedPos >= getItemCount()) {
            return 0;
        }
        return mSelectedPos;
    }

    public void setSelectPos(int selectPos) {
        if (selectPos < 0 || selectPos >= getItemCount()) {
            return;
        } else {
            this.mSelectedPos = selectPos;
        }
    }

    public void hasFocus(boolean focus) {
        mFocus = focus;
    }

    public void setOnSelectChangeListener(SelectItemChangeCallback callback) {
        mCallback = callback;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private TextView listItem;

        public ViewHolder(View v) {
            super(v);
            listItem = (TextView) itemView.findViewById(R.id.list_item);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    int pos;
//                    if (Util.isSupportTouchVersion(mParent.getContext())) {
                        pos = mParent.getChildAdapterPosition(v);
//                    } else {
//                        pos = mParent.getChildAdapterPosition(mParent.getFocusedChild());
//                    }
                    if (mSelectedPos != pos) {
                        ViewHolder viewHolder = ((ViewHolder) mParent.findViewHolderForAdapterPosition(mSelectedPos));
                        if (viewHolder != null) {
                            viewHolder.listItem.setSelected(false);
                        }
                        mSelectedPos = pos;
                        mFocusPos = pos;
                        ((GridListActivityNew) mParent.getContext()).updateFilterFocusPos((LinearLayout) mParent.getParent());
                        v.setSelected(true);
                        mCallback.onSelectChange();
                    }
                }
            });
            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if (mParent.getContext() instanceof GridListActivityNew) {
                        if (((GridListActivityNew) mParent.getContext()).isLoadingData()) {
                            return true;
                        }
                    }
                    int position = getAdapterPosition();
                    if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                        if (mFilterData.cateValues.size() != 0 && position == (getItemCount() - 1)) {
                            return true;
                        }
                    } else if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_MENU) {
                        ((GridListActivityNew) mParent.getContext()).dismissPopUpWindow();
                    }
                    //first item of every line
                    if (position == 0 && event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
                        ((GridListActivityNew) mParent.getContext()).setLeftSelectedPos();
                        return true;
                    }
                    if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
                        ((GridListActivityNew) mParent.getContext()).backFromRightItem = true;
                        ((GridListActivityNew) mParent.getContext()).setLeftSelectedPos();
                        return true;
                    }
                    if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                        //如果是第一行，则让topbar第一个item获取焦点
                        if (isFirstLine()) {
                            ((GridListActivityNew) mParent.getContext()).setFocusRoute(false);
                            ((GridListActivityNew) mParent.getContext()).focusOnTopBar();
                            return true;
                        }
                    }
                    return false;
                }

            });

        }

        private boolean isFirstLine() {
            View firstLineOfFilterView = ((GridListActivityNew) mParent.getContext()).getFirstLineOfFilterView();
            if (firstLineOfFilterView instanceof LinearLayout) {
                LinearLayout firstLineLayout = (LinearLayout) firstLineOfFilterView;
                if (firstLineLayout.getChildCount() > 0) {
                    RecyclerView recyclerView = (RecyclerView) firstLineLayout.getChildAt(1);
                    if (mParent == recyclerView) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    public interface SelectItemChangeCallback {
        void onSelectChange();
    }
}
