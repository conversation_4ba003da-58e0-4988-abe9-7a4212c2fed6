package com.sohuott.tv.vod.activity.launcher

import android.content.Context
import android.widget.LinearLayout
import android.widget.TextView
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohuott.tv.vod.R

class LauncherPlayerComponent constructor(context: Context) :
    BaseControlComponent(context) {
    private var volumeRoot: LinearLayout? = null
    private var volumeCountdownText: TextView? = null

    init {
        val view = layoutInflater.inflate(R.layout.video_component_launcher_payer, this, true)
        volumeRoot = findViewById(R.id.volume_root)
        volumeCountdownText = findViewById(R.id.volume_countdown_text)
    }

    fun setVolumeCountDown(show: Boolean, countDown: Int) {
        if (show) {
            showVolumeCountDown("$countDown")
            player?.isMute=true
        } else {
            hideVolumeCountDown()
            player?.isMute=false
        }
    }

    fun showVolumeCountDown(time: String) {
        volumeRoot?.setVisibility(VISIBLE)
        volumeCountdownText?.setText(time + "秒后播放声音")
    }

    fun hideVolumeCountDown() {
        volumeRoot?.setVisibility(GONE)
        volumeCountdownText?.setText("3秒后播放声音")
    }
}