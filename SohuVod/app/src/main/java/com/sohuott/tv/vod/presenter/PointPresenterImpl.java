package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ReportPointResult;
import com.sohuott.tv.vod.lib.model.UserPointInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.SecurityUtil;
import com.sohuott.tv.vod.view.PointView;
import com.sohu.lib_utils.StringUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by wenjingbian on 2017/12/27.
 */

public class PointPresenterImpl {

    public static final int GROUP_MEMBER = 1;
    public static final int GROUP_AUTO = 2;
    public static final int GROUP_MANUAL = 3;

    private PointView mPointView;

    private List<UserPointInfo.DataBean> mGroupOneList, mGroupTwoList, mGroupThreeList;

    public void setView(PointView pointView) {
        this.mPointView = pointView;
    }

    public void requestPointInfo(String passport) {
        if (StringUtil.isEmpty(passport)) {
            return;
        }

        NetworkApi.getUserPointInfo(passport, new DisposableObserver<UserPointInfo>() {
            @Override
            public void onNext(UserPointInfo value) {
                LibDeprecatedLogger.d("getUserPointInfo(): onNext");
                if (value != null && value.getData() != null && value.getData().size() > 0) {
                    mPointView.updateView(value);
                } else {
                    LibDeprecatedLogger.d("Illegal data");
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("onError(): " + e.getMessage());
            }

            @Override
            public void onComplete() {

            }
        });
    }

    public void classifyList(List<UserPointInfo.DataBean> list) {
        if (list == null || list.size() <= 0) {
            return;
        }

        mGroupOneList = new ArrayList<>();
        mGroupTwoList = new ArrayList<>();
        mGroupThreeList = new ArrayList<>();
        for (UserPointInfo.DataBean tmpData : list) {
            if (tmpData == null) {
                continue;
            }
            switch (tmpData.getGroupId()) {
                case GROUP_MEMBER:
                    mGroupOneList.add(tmpData);
                    break;
                case GROUP_AUTO:
                    mGroupTwoList.add(tmpData);
                    break;
                case GROUP_MANUAL:
                    mGroupThreeList.add(tmpData);
                    break;
                default:
                    break;
            }
        }

        if (mGroupTwoList != null && mGroupTwoList.size() > 0) {
            sortPointByScore(mGroupTwoList);
        }
    }

    public List<UserPointInfo.DataBean> getGroupOneList() {
        return mGroupOneList;
    }

    public List<UserPointInfo.DataBean> getGroupTwoList() {
        return mGroupTwoList;
    }

    public List<UserPointInfo.DataBean> getGroupThreeList() {
        return mGroupThreeList;
    }

    public void reportSignIn(String passport) {
        String time = String.valueOf(new Date().getTime());
        String ts = SecurityUtil.encrypt(time);
        String param = SecurityUtil.encrypt("taskId=" + Constant.USER_POINT_SIGN + "&passport=" + passport,
                time);

        NetworkApi.reportUserPoint(ts, param, new Observer<ReportPointResult>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(ReportPointResult value) {
                LibDeprecatedLogger.d("reportSignIn(): onNext().");
                if (mPointView != null) {
                    mPointView.getSignResult(value);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("reportSignIn(): onError().");
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("reportSignIn(): onComplete().");
            }
        });
    }

    private void sortPointByScore(List<UserPointInfo.DataBean> list) {
        if (list == null || list.size() <= 0) {
            return;
        }

        Collections.sort(list, new Comparator<UserPointInfo.DataBean>() {
            @Override
            public int compare(UserPointInfo.DataBean o1, UserPointInfo.DataBean o2) {
                return ((Integer) o1.getScore()).compareTo(o2.getScore());
            }
        });
    }
}
