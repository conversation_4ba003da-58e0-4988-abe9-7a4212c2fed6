package com.sohuott.tv.vod.activity

import android.content.Intent
import android.os.Bundle
import com.com.sohuott.tv.vod.base_component.NewBaseActivity
import com.lib_statistical.IMP
import com.lib_statistical.addPathAndObjectInfoEvent
import com.lib_viewbind_ext.viewBinding
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.databinding.ActivityAccountLogOffBinding
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohuott.tv.vod.lib.utils.UrlWrapper
import com.sohuott.tv.vod.utils.ActivityLauncher
import com.sohuott.tv.vod.utils.LoadQrPicture
import io.reactivex.functions.Consumer

//账号注销页面
class AccountLogOffActivity : NewBaseActivity(R.layout.activity_account_log_off) {
    private var mViewBinding: ActivityAccountLogOffBinding? = null
    private val _binding by viewBinding(onViewDestroyed = {
        mViewBinding = null
        mHelper = null
    }, ActivityAccountLogOffBinding::bind)
    private var mHelper :LoginUserInformationHelper? = null
    private var mIsFirstBoot = true
    var mLoadQrPicture: LoadQrPicture? = null;

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = _binding
        mHelper = LoginUserInformationHelper.getHelper(applicationContext)
        if (!mHelper!!.isLogin) {
            ActivityLauncher.startLoginActivity(this)
        }
        addPathAndObjectInfoEvent(10135, IMP, {
            it["pageId"] = "1034"
        }, {
            it["type"] = "page"
            it["id"] = "1034"
        })

        mViewBinding?.logOffExit?.setOnClickListener {
//            addClickPathInfoEvent(10253) {
//                it["pageId"] = "1034"
//            }
            finish()
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        if (getIntent() != null) {
            setIntent(intent)
        }
    }

    override fun onResume() {
        super.onResume()
        if (!mIsFirstBoot) {
            if (!mHelper!!.isLogin) {
                //回到页面依旧没有登录，直接退出
                finish()
            }
        }
        if (mHelper!!.isLogin) {
            //生成二维码
            showQrcode()
        }
    }

    override fun onPause() {
        super.onPause()
        mIsFirstBoot = false
    }

    override fun onDestroy() {
        super.onDestroy()
        mLoadQrPicture?.cancel()
        mLoadQrPicture = null
    }

    private fun showQrcode() {
        val qrCodeImgUrl =
            UrlWrapper.getLogOffQrCodeUrl(
                420, 420,
                mHelper!!.loginPassport
            )
        if (mLoadQrPicture == null) {
            mLoadQrPicture = LoadQrPicture(this, Consumer {

            })
        }
        mLoadQrPicture?.getPicture(qrCodeImgUrl, mViewBinding?.logOffQrcode)
    }
}