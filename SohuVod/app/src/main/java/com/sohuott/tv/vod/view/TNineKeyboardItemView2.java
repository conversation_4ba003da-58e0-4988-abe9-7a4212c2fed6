package com.sohuott.tv.vod.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.utils.FocusUtil;

/**
 * Created by feng<PERSON><PERSON> on 17-6-15.
 */

public class TNineKeyboardItemView2 extends RelativeLayout implements View.OnClickListener, View.OnFocusChangeListener {

    private int index;
    private TextView topTV;
    private TextView bottomTV;

    private String mPageName = "6_search";

    private TNineKeyboardLayout.OnClickTNineKeyboardListener mOnClickTNineKeyboardListener;

    public TNineKeyboardItemView2(Context context) {
        super(context);
        initUI(context);
    }

    public TNineKeyboardItemView2(Context context, AttributeSet attrs) {
        super(context, attrs);
        initUI(context);
    }

    public TNineKeyboardItemView2(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initUI(context);
    }

    private void initUI(Context context) {
//        setBackgroundResource(R.drawable.tnine_keyboard_item_view_selector);
        setGravity(Gravity.CENTER);
        setFocusable(true);
        setOnClickListener(this);
        setOnFocusChangeListener(this);
        LayoutInflater.from(context).inflate(R.layout.tnine_keyboard_item2_layout, this, true);
        topTV = (TextView) findViewById(R.id.topTV);
        bottomTV = (TextView) findViewById(R.id.bottomTV);
    }

    public void initData(int index, String topText, String bottomText) {
        this.index = index;
        if(topText.contains("0")) {
            topTV.setText(topText + " | " + bottomText);
            bottomTV.setVisibility(View.GONE);
        } else {
            topTV.setText(topText);
            bottomTV.setText(bottomText);
            bottomTV.setVisibility(View.VISIBLE);
        }
    }

    public boolean hasContent(String content) {
        return topTV.getText().toString().contains(content)
                || bottomTV.getText().toString().contains(content);
    }

    public void setOnClickTNineKeyboardListener(TNineKeyboardLayout.OnClickTNineKeyboardListener listener) {
        mOnClickTNineKeyboardListener = listener;
    }

    public void setPageName(String pageName){
        this.mPageName = pageName;
    }

    @Override
    public void onClick(View v) {
        if(mOnClickTNineKeyboardListener != null) {
            if(topTV.getText().toString().contains("0")) {
                mOnClickTNineKeyboardListener.onClickTNineKeyboard("01");
            } else {
                mOnClickTNineKeyboardListener.onClickTNineKeyboard(
                        topTV.getText().toString() + bottomTV.getText().toString());
            }
            RequestManager.getInstance().onClickSearchT9Item(mPageName,index + 1);
        }
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if(hasFocus) {
            if (focusBorderView != null) {
                focusBorderView.setFocusView(v);
                FocusUtil.setFocusAnimator(v, focusBorderView, 1f);
            }
        } else {
            if (focusBorderView != null) {
                focusBorderView.setUnFocusView(v);
            }
        }
    }

    protected FocusBorderView focusBorderView;

    public void setFocusBorderView(FocusBorderView focusView) {
        focusBorderView = focusView;
    }

}
