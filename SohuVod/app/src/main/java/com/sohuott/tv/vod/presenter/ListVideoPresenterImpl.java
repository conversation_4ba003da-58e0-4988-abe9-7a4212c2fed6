package com.sohuott.tv.vod.presenter;

import android.text.TextUtils;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.BaseListItemModel;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.view.ListVideoView;

import java.lang.ref.WeakReference;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by XiyingCao on 16-1-7.
 */
public class ListVideoPresenterImpl {

    private long mLabelId;
    private ListVideoView mListView;
    private boolean mIsNormal;

    public ListVideoPresenterImpl(long labelId, boolean isNormal) {
        mLabelId = labelId;
        mIsNormal = isNormal;
    }

    public void setView(ListVideoView view) {
        mListView = new WeakReference<ListVideoView>(view).get();
    }

    public void onViewResumed() {
        if (mIsNormal) {
            searchForCharacters();
        } else {
            searchForUnnormalCharacters();
        }
    }

    public void onItemSelected(int position) {
        if (position == -1) {
            position = 0;
        }
        if (mListView.getAdapter() == null || mListView.getAdapter().getItemCount() == 0 ||
                position < 0 || position >= mListView.getAdapter().getItemCount()) {
            return;
        }
        ListAlbumModel itemDetails =
                mListView.getAdapter().getItem(position);
        StringBuilder itemSubDetails = new StringBuilder();
        switch ((int) itemDetails.cateCode) {
            case 100:
                //电影
                if (!TextUtils.isEmpty(itemDetails.areaName)) {
                    itemSubDetails.append(itemDetails.areaName);
                }
                if (itemDetails.tvYear > 0) {
                    itemSubDetails.append("  |  ").append(itemDetails.tvYear);
                }
                itemSubDetails.append(getSecondCateName(itemDetails.genreName));
                if (!TextUtils.isEmpty(getActorNames(itemDetails.director)) && !itemDetails.director.equals("null")) {
                    itemSubDetails.append("  |  ").append("导演：").append(getActorNames(itemDetails.director)).append("  ");
                }
                if (!TextUtils.isEmpty(itemDetails.act) && !itemDetails.act.equals("null")) {
                    itemSubDetails.append("  |  ").append("主演：").append(getActorNames(itemDetails.act));
                }
                break;
            case 101:
                //电视剧
                if (!TextUtils.isEmpty(itemDetails.areaName)) {
                    itemSubDetails.append(itemDetails.areaName);
                }
                if (itemDetails.tvYear > 0) {
                    itemSubDetails.append("  |  ").append(itemDetails.tvYear);
                }
                itemSubDetails.append(getSecondCateName(itemDetails.genreName));
                if (!TextUtils.isEmpty(itemDetails.director) && !itemDetails.director.equals("null")) {
                    itemSubDetails.append("  |  ").append("导演：").append(getActorNames(itemDetails.director)).append("  ");
                }
                if (!TextUtils.isEmpty(itemDetails.act) && !itemDetails.act.equals("null")) {
                    itemSubDetails.append("  |  ").append("主演：").append(getActorNames(itemDetails.act));
                }
                break;
            case 106:
                //综艺
                if (!TextUtils.isEmpty(itemDetails.areaName)) {
                    itemSubDetails.append(itemDetails.areaName);
                }
                if (itemDetails.tvYear > 0) {
                    itemSubDetails.append("  |  ").append(itemDetails.tvYear);
                }
                itemSubDetails.append(getSecondCateName(itemDetails.genreName));
                if (!TextUtils.isEmpty(itemDetails.act) && !itemDetails.act.equals("null")) {
                    itemSubDetails.append("  |  ").append("主持人：").append(getActorNames(itemDetails.act));
                }
                break;
            case 115:
                //动漫
                if (!TextUtils.isEmpty(itemDetails.areaName)) {
                    itemSubDetails.append(itemDetails.areaName);
                }
                if (itemDetails.tvYear > 0) {
                    itemSubDetails.append("  |  ").append(itemDetails.tvYear);
                }
                itemSubDetails.append(getSecondCateName(itemDetails.genreName));
                if (!TextUtils.isEmpty(itemDetails.director) && !itemDetails.director.equals("null")) {
                    itemSubDetails.append("  |  ").append("导演：").append(getActorNames(itemDetails.director)).append("  ");
                }
                if (!TextUtils.isEmpty(itemDetails.act) && !itemDetails.act.equals("null")) {
                    itemSubDetails.append("  |  ").append("声优：").append(getActorNames(itemDetails.act));
                }
                break;
            default:
                if (!TextUtils.isEmpty(itemDetails.areaName)) {
                    itemSubDetails.append(itemDetails.areaName);
                }
                if (itemDetails.tvYear > 0) {
                    itemSubDetails.append("  |  ").append(itemDetails.tvYear);
                }
                itemSubDetails.append(getSecondCateName(itemDetails.genreName));
                if (!TextUtils.isEmpty(itemDetails.director) && !itemDetails.director.equals("null")) {
                    itemSubDetails.append("  |  ").append("导演：").append(getActorNames(itemDetails.director)).append("  ");
                }
                if (!TextUtils.isEmpty(itemDetails.act) && !itemDetails.act.equals("null")) {
                    itemSubDetails.append("  |  ").append("主演：").append(getActorNames(itemDetails.act));
                }
                break;
        }
        mListView.setVideoDetails(itemDetails.commont, itemDetails.tvName, itemSubDetails.toString(), itemDetails.tvDesc, itemDetails.scoreSource, itemDetails.score, itemDetails.doubanScore);
    }

    private String getSecondCateName(String genre) {
        if (TextUtils.isEmpty(genre)) {
            return "";
        }
        StringBuffer stringBuffer = new StringBuffer("  |  ");
        String[] secondCateNames = null;
        if (genre.contains(";")) {
            secondCateNames = genre.split(";");
        } else if (genre.contains(",")) {
            secondCateNames = genre.split(",");
        }

        if (secondCateNames != null && secondCateNames.length > 0) {
            int cateCount = secondCateNames.length > 2 ? 2 : secondCateNames.length;
            for (int i = 0; i < cateCount; i++) {
                if (i == cateCount - 1) {
                    stringBuffer.append(secondCateNames[i]);
                } else {
                    stringBuffer.append(secondCateNames[i]).append(" ");
                }
            }
        } else {
            return stringBuffer.append(genre).toString();
        }
        return stringBuffer.toString();
    }


    private String getActorNames(String name) {
        String actorNames;
        if (name.contains(";")) {
            actorNames = name.replace(";", "/");
        } else if (name.contains(",")) {
            actorNames = name.replace(",", "/");
        } else {
            return name;
        }
        return actorNames;
    }

    private void searchForCharacters() {
        NetworkApi.getVideoListForLabel(mLabelId, new DisposableObserver<BaseListItemModel>() {
            @Override
            public void onNext(BaseListItemModel value) {
                if (value != null && value.data !=
                        null && value.data.result != null && value.data.count > 0) {
                    mListView.setCount(value.data.count);
                    mListView.add(value.data.result.albumList);
                    if (mListView.getSelectedPos() == 0) {
                        mListView.setBackground(value.data.result.bigPicUrl);
                        mListView.setListTitle(value.data.result.name);
                        mListView.setListCategory(value.data.result.channelName);
                        mListView.showContinue(value.data.result.continuesly);
                        onItemSelected(0);
                        mListView.hideLoading();
                    }
                } else {
                    mListView.onError();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("searchForCharacters error: " + e.getMessage(), e);
                mListView.onError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.e("searchForCharacters onComplete");
            }
        });
    }

    private void searchForUnnormalCharacters() {
        NetworkApi.getVideoList(100, 2, new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                if (value != null && value.data != null
                        && value.data.result != null) {
                    mListView.setCount(value.data.count);
                    mListView.add(value.data.result);
                    if (mListView.getSelectedPos() == 0) {
                        mListView.setBackground("");
                        mListView.setListTitle("观影券专区");
                        mListView.setListCategory("会员");
                        mListView.showContinue(0);
                        onItemSelected(0);
                        mListView.hideLoading();
                    }
                } else {
                    mListView.onError();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("performDataRequest error: " + e.getMessage(), e);
                mListView.onError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("performDataRequest onComplete");
            }
        });
    }

    public void setLabelId(long labelId, boolean isNormal) {
        mIsNormal = isNormal;
        mLabelId = labelId;
    }
}
