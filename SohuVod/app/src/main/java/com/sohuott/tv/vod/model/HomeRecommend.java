package com.sohuott.tv.vod.model;

import java.util.ArrayList;

/**
 * Created by f<PERSON><PERSON><PERSON> on 16-1-11.
 */
public class HomeRecommend {

    public static final int LABEL_TYPE = 0; //标签
    public static final int ALBUM_TYPE = 1; //专辑
    public static final int ALL_TYPE = 2;   //一级分类
    public static final int WELFARE_TYPE = 4;  //活动
    public static final int VIDEO_TYPE = 5;  //视频
    public static final int POSTER_TYPE = 6;  //海报
    public static final int CATEGORY_TYPE = 7;  //二级分类
    public static final int PRODUCER_TYPE = 8;  //出品人
    public static final int PERSONAL_RECOMMEND_TYPE = 9;  //个性化推荐
    public static final int ALL_LABEL_TYPE = 10;  //全部标签
    public static final int TICKET_TYPE = 11;  //观影卷专区

    public int status;
    public String message;

    public ArrayList<HomeRecommendData> data;

    public static class HomeRecommendData {
        public long channellistId;
        public long id;
        public String name;
        public int order;
        public String type;
        public ArrayList<HomeRecommendItem> contents;
    }

    public static class HomeRecommendItem {
        public int albumId;
        public long groupId;
        public long id;
        public String name;
        public int order;
        public String picUrl;
        public String type;
        public int ottCategoryId;
        public AlbumParam albumParam;
        public String parameter;
        public boolean needLabel;
        public int dataType; //0:vrs, 1:vrs-vr, 2: pgc
    }

    public static class Parameter {
        public String albumId;
        public String tvVerId;
        public String labelId;
        public String videoId;
        public String cateCodeFirst;
        public String trackPosition;
        public int recommendStrategyId;
    }

    public static class AlbumParam {
        public String ottFee;
        public String tvIsFee;
        public String cornerType;
    }

}
