package com.sohuott.tv.vod.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.bumptech.glide.Glide;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.ott.base.lib_user.HeaderHelper;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.common.AccountException;
import com.sohuott.tv.vod.account.common.AccountService;
import com.sohuott.tv.vod.account.common.Listener;
import com.sohuott.tv.vod.account.login.CarouselLogin;
import com.sohuott.tv.vod.account.login.ConfigInfo;
import com.sohuott.tv.vod.account.login.Login;
import com.sohuott.tv.vod.account.login.LoginApi;
import com.sohuott.tv.vod.account.login.PollingLoginHelper;
import com.sohuott.tv.vod.account.login.PollingResult;
import com.sohuott.tv.vod.account.payment.PayApi;
import com.sohuott.tv.vod.activity.LoginActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.activity.TeenagerLockActivity;
import com.sohuott.tv.vod.lib.db.greendao.ChildPlayHistory;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.LoginQrModel;
import com.sohuott.tv.vod.lib.model.PermissionCheck;
import com.sohuott.tv.vod.lib.service.PlayHistoryService;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.PostHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.utils.ActivityLauncher;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;

import io.reactivex.functions.Consumer;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * @Desc scan login page
 * Created by xianrongchen on 2017-03-29.
 */
public class SohuLoginFragment extends BaseLoginFragment {


    public static SohuLoginFragment newInstance(Bundle bundle) {
        SohuLoginFragment loginFragment = new SohuLoginFragment();
        loginFragment.setArguments(bundle);
        return loginFragment;
    }

    private static final int QRCODE_PICTURE_SIZE = 5 * 100;
    private static final int MSG_CAROUSEL_SCAN_INFO = 1;
    private String mQrCodeImageUrl = "";
    private MyHandler mHandler;
    private int mCarouselTime;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
//        String text = mContext.getApplicationContext().getResources().getString(R.string.txt_activity_login_qrcode_sohu_desc2);
//        SpannableStringBuilder spannable = new SpannableStringBuilder(text);
//        CharacterStyle span = new ForegroundColorSpan(mContext.getApplicationContext().getResources().getColor(R.color.color_login_07));
//        spannable.setSpan(span, 7, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//        mQrCodeDescription.setText(spannable);
        mPollingLoginHelper = new PollingLoginHelper();
        mHandler = new MyHandler(this);
        setSubPageName("6_login_sohu");
        return mView;
    }

    PollingLoginHelper mPollingLoginHelper;

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
//        RequestManager.onEvent("6_login_sohu", "100001",
//                null, null, null, null, null);
    }

    @Override
    public void onResume() {
        super.onResume();
//        if (!LoginUserInformationHelper.getHelper(mContext).getIsLogin()) {
            getCfgInfo();
//        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            LibDeprecatedLogger.d("update qr code");
            updateView();
            initData();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        mHandler.removeMessages(MSG_CAROUSEL_SCAN_INFO);
    }


    @Override
    protected void initData() {
        getSohuLogin();
    }

    @Override
    protected void setVisibility() {
        mQrCodeDescription.setVisibility(View.VISIBLE);
        mQrCodeDescription2.setVisibility(View.VISIBLE);
    }

    Consumer<LoginQrModel> mLoadSucceedConsumer;

    private void getCfgInfo() {
        LoginApi.getCfgInfo(new Listener<ConfigInfo>() {
            @Override
            public void onSuccess(ConfigInfo configInfo) {
                LibDeprecatedLogger.d("getCfgInfo(): response = " + configInfo);
                if (null != configInfo) {
                    ConfigInfo.DataEntity data = configInfo.getData();
                    String message = configInfo.getMessage();
                    int status = configInfo.getStatus();

                    if (status == 200 && null != data) {
                        mCarouselTime = data.getRtime();
                        mHandler.removeMessages(MSG_CAROUSEL_SCAN_INFO, mCarouselTime);
                        mHandler.sendEmptyMessageDelayed(MSG_CAROUSEL_SCAN_INFO, mCarouselTime);
                    } else {
                        ToastUtils.showToast2(mContext, message);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("getCfgInfo(): onError() = " + e.toString());
            }
        });
    }

    private volatile boolean mScanSucceed;

    private void getScanInfo() {
        LoginApi.getScanInfo(mContext, mQrcode, 2, new Listener<CarouselLogin>() {
            @Override
            public void onSuccess(CarouselLogin carouselLogin) {
                mScanSucceed = true;
                LibDeprecatedLogger.d("getScanInfo success");
            }

            @Override
            public void onError(Throwable e) {
                if (e.getMessage().equals("40010")) {
                    ToastUtils.showToast(mContext, "二维码过期");
                    getSohuLogin();
                }
                LibDeprecatedLogger.e("getScanInfo(): onError() = " + e.toString());
            }
        });
    }

    @Override
    public void onScanSucceed() {
        mPollingLoginHelper.onDestroy();
        mPollingLoginHelper = new PollingLoginHelper();
        mPollingLoginHelper.startPolling(mContext, mPollingToken, mQrcode, mPollingListener);
    }

    private String mPollingToken;
    private String mQrcode;

    private void getSohuLogin() {
        mQrCodeImageUrl = UrlWrapper.getScanLoginQrCodeUrl(QRCODE_PICTURE_SIZE, QRCODE_PICTURE_SIZE, UserInfoHelper.getGid());
        new AccountService(getContext()).getQrImage(HeaderHelper.getHeaders(), mQrCodeImageUrl, new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                if (mContext == null){
                    return;
                }
                if (mContext instanceof Activity){
                    if(((Activity) mContext).isDestroyed()){
                        return;
                    }
                }
                // 如果请求成功，将ResponseBody转化为InputStream，然后使用Glide加载图片
                byte[] inputStream = null;
                try {
                    inputStream = response.body().bytes();
                    Glide.with(mContext)
                            .load(inputStream)
                            .into(mQrCodeImage);  // yourImageView是你要展示图片的ImageView
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                // 注意，在此处你需要传递正确的Context实例，如 Activity 的 this 引用

                mPollingToken = response.headers().get("code");
                mQrcode = response.headers().get("qrcode");
                mScanSucceed = false;
                if (mCarouselTime > 0) {
                    mHandler.removeMessages(MSG_CAROUSEL_SCAN_INFO);
                    mHandler.sendEmptyMessageDelayed(MSG_CAROUSEL_SCAN_INFO, mCarouselTime);
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {

            }
        });
        RequestManager.onEvent("6_login_sohu", "6_login_passcardQR", null, null, null, null, null);
    }

    Listener<Login> mPollingListener = new Listener<Login>() {
        @Override
        public void onSuccess(Login response) {
            LibDeprecatedLogger.d("passportPolling: onSuccess");
            if (null != response) {
                Login.LoginData data = response.getData();
                String message = response.getMessage();
                int status = response.getStatus();
                if (status == 200 && null != data && data.getUType() != null) {
                    PostHelper.postLoginSuccessEvent();
                    if (getActivity() != null) {
                        syncHistory();

                        HashMap pathInfo = new HashMap();
                        pathInfo.put("pageId", "1001");

                        String loginType = "";
                        if (response.getData().getUType().equals("1")) {
                            loginType = "1";
                        } else if (response.getData().getUType().equals("2")) {
                            loginType = "2";
                        } else if (response.getData().getUType().equals("3")) {
                            loginType = "3";
                        } else if (response.getData().getUType().equals("4")) {
                            loginType = "5";
                        } else if (response.getData().getUType().equals("5")) {
                            loginType = "4";
                        }
                        HashMap memoInfo = new HashMap();
                        memoInfo.put("method", loginType);
                        RequestManager.getInstance().onAllEvent(new EventInfo(10132, "slc"), pathInfo, null, memoInfo);
                    }
                } else {
                    ToastUtils.showToast2(mContext, message);
                }
            }
        }

        @Override
        public void onError(Throwable e) {
            if (e instanceof AccountException) {
                if (((AccountException) e).getErrCode() == PollingResult.TOKEN_EXPIRED) {
                    LibDeprecatedLogger.w("Token expired !!!");
                    getSohuLogin();
                }
            }
            LibDeprecatedLogger.w("passportPolling: onError, " + e.getMessage(), e);
        }
    };

    private void syncHistory() {
        ((LoginActivity) getActivity()).showSyncLoading();
        PlayHistoryService hisService = new PlayHistoryService(getActivity());
        hisService.getAllPlayHistory(new PlayHistoryService.PlayHistoryListener() {

            @Override
            public void onSuccess(List<PlayHistory> playHistoryList) {
                onSyncHistoryEnd();
            }

            @Override
            public void onFail(String reason, List<PlayHistory> playHistoryList) {
                onSyncHistoryEnd();
            }
        });
        hisService.getAllEduHistory(null);
    }


    private void onSyncHistoryEnd() {
        if (null == getActivity()) {
            return;
        }
        ((LoginActivity) getActivity()).hideSyncLoading();
        boolean normalLogin = getArguments().getBoolean("normalLogin");
        boolean fromDetail = getArguments().getBoolean("fromVideoDetail");
        boolean isTeenager = getArguments().getBoolean("isTeenager");
        final int aid = getArguments().getInt("aid");
        final int vid = getArguments().getInt("vid");
        final String videoName = getArguments().getString("videoName");
        if (isTeenager){
            ActivityLauncher.startChildLockActivity(mContext, TeenagerLockActivity.TYPE_ONE_SET_PASSWORD);
            ((LoginActivity)getActivity()).finishLoginActivity(true);
            return;
        }
        if (normalLogin) {
            if (fromDetail) {
                LoginUserInformationHelper helper = LoginUserInformationHelper.getHelper(getContext());
                if (!helper.isVip()) {
                    if (aid != 0 || vid != 0) {
                        PayApi.getFilmCheckPermission(helper.getLoginPassport(),
                                helper.getLoginToken(), aid, vid, 0, new Listener<PermissionCheck>() {
                                    @Override
                                    public void onSuccess(PermissionCheck permissionCheck) {
                                        ActivityLauncher.startPayActivity(getContext(), aid, vid, videoName, PayActivity.PAY_SOURCE_LOGIN);
                                    }

                                    @Override
                                    public void onError(Throwable e) {
                                        LibDeprecatedLogger.d("getFilmCheckPermission(): onError() = " + e.toString());
                                    }
                                });
                    }
                }
            }
        } else {
            ActivityLauncher.startPayActivity(getActivity(), PayActivity.PAY_SOURCE_LOGIN);
        }
        ToastUtils.showToast2(getActivity(), getResources().getString(R.string.txt_activity_input_login_success_tip));

        ((LoginActivity)getActivity()).finishLoginActivity(true);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mPollingLoginHelper.onDestroy();
        mHandler.removeMessages(MSG_CAROUSEL_SCAN_INFO);
    }

    private static class MyHandler extends Handler {
        private WeakReference<SohuLoginFragment> activityReference;

        public MyHandler(SohuLoginFragment ref) {
            super();
            activityReference = new WeakReference<>(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            SohuLoginFragment ref = activityReference.get();
            if (null == ref) {
                return;
            }

            int what = msg.what;
            switch (what) {
                case MSG_CAROUSEL_SCAN_INFO:
                    if (!ref.mScanSucceed) {
                        ref.getScanInfo();
                        ref.mHandler.removeMessages(MSG_CAROUSEL_SCAN_INFO);
                        ref.mHandler.sendEmptyMessageDelayed(MSG_CAROUSEL_SCAN_INFO, ref.mCarouselTime);
                    }
                    break;
                default:
                    break;
            }
        }
    }

//    public interface QrCodeService {
//        @GET
//        Call<ResponseBody> getLoginImage(@HeaderMap Map<String, String> headers, @Url String url);
//    }
}
