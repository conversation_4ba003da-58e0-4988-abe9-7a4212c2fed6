package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.PointRecordInfo;
import com.sohuott.tv.vod.view.PointRecordView;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by wenjingbian on 2018/1/5.
 */

public class PointRecordPresenterImpl {

    private PointRecordView mPointRecordView;

    public void setView(PointRecordView pointRecordView) {
        this.mPointRecordView = pointRecordView;
    }

    public void requestPointRecordData(int page, int pageSize, String passport) {
        NetworkApi.getUserPointRecord(page, pageSize, passport, new Observer<PointRecordInfo>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(PointRecordInfo value) {
                LibDeprecatedLogger.d("requestPointRecordData(): onNext().");
                if (value == null || value.getData() == null) {
                    mPointRecordView.displayErrorView();
                } else {
                    mPointRecordView.displayRecordView(value);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestPointRecordData(): onError().");
                if (mPointRecordView != null) {
                    mPointRecordView.displayErrorView();
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestPointRecordData(): onComplete().");
            }
        });
    }

}
