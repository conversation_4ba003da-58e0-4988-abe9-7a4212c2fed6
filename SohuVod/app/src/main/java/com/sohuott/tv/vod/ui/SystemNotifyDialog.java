package com.sohuott.tv.vod.ui;

import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ServerMessage;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.util.Timer;
import java.util.TimerTask;

/**
 * @version V1.0.0
 * @FileName: com.sohuott.tv.vod.ui.SystemNotifyDialog.java
 * @author: ZiJiaCui
 * @Description: ${todo}
 * @date: 2017-04-11 15:48
 */
public class SystemNotifyDialog {

    private static final int MSG_CLOSE_DIALOG = 1001;

    private static SystemNotifyDialog sInstance;

    private Context mContext;

    private LayoutInflater mInflater;
    private WindowManager mWm;

    private View mDialogView;
    private GlideImageView mBgImageView;
    private Button mCancelBtn;
    private Button mConfirmBtn;

    private ServerMessage.Data mData;

    private Timer mTimer = new Timer();

    private boolean isDialogShowing;

    private static class InnerHandler extends Handler{
        WeakReference<SystemNotifyDialog> mWrapper;
        InnerHandler(SystemNotifyDialog systemNotifyDialog){
            mWrapper = new WeakReference<>(systemNotifyDialog);
        }
        @Override
        public void handleMessage(@NonNull Message msg) {
            SystemNotifyDialog systemNotifyDialog = mWrapper.get();
            if (systemNotifyDialog == null){
                return;
            }
            if (msg.what == MSG_CLOSE_DIALOG) {
                // TODO: 2018/1/25 自动关闭
                Util.putHotspotAutoClosed(systemNotifyDialog.mContext, systemNotifyDialog.mData.id, -1);
                systemNotifyDialog.closeDialog();
            }
        }
    }
    private Handler mHandler = new InnerHandler(this);

    private SystemNotifyDialog() {
        isDialogShowing = false;
    }

    private SystemNotifyDialog(final Context context) {
        mContext = context;

        mWm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        mInflater = LayoutInflater.from(context);

        mDialogView = mInflater.inflate(R.layout.dialog_system_notify, null);

        mBgImageView = (GlideImageView) mDialogView.findViewById(R.id.bgImg);
        mCancelBtn = (Button) mDialogView.findViewById(R.id.cancelBtn);
        mConfirmBtn = (Button) mDialogView.findViewById(R.id.confirmBtn);

        mCancelBtn.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                Util.putHotspotAutoClosed(mContext, mData.id, 1);
                RequestManager.getInstance().onClickHotspotCancel();
                closeDialog();
            }
        });
        mConfirmBtn.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View view) {
                Util.putHotspotAutoClosed(mContext, mData.id, 1);

                onConfirmClick();
            }
        });
    }

    public static final SystemNotifyDialog getInstance(Context context) {
        if (null == sInstance) {
            synchronized (SystemNotifyDialog.class) {
                if (null == sInstance) {
                    sInstance = new SystemNotifyDialog(context);
                }
            }
        }
        return sInstance;
    }

    public void showDialog(ServerMessage.Data data) {
        if (null == mData || mData.id < data.id) {
            mData = data;
        }
        LibDeprecatedLogger.d("content = " + data + ", isDialogShowing = " + isDialogShowing);
        WindowManager.LayoutParams lp = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT, 0, 0,
                PixelFormat.TRANSPARENT);

        loadBgImg(mData.picUrl);
        lp.gravity = Gravity.RIGHT | Gravity.TOP;
        lp.flags = WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
        /**
         *type reference
         * https://www.liaohuqiu.net/cn/posts/android-windows-manager/
         * http://www.jianshu.com/p/167fd5f47d5c
         *    需要进行sdk版本判断（19）
         */
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            // TODO: 2017/12/19 android4.4
            lp.type = WindowManager.LayoutParams.TYPE_TOAST;
        } else {
            lp.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        }
        if (isDialogShowing) {
            mWm.updateViewLayout(mDialogView, lp);
        } else {
            mWm.addView(mDialogView, lp);
        }

        mConfirmBtn.requestFocus();
        mConfirmBtn.requestFocusFromTouch();
        isDialogShowing = true;
        if (null != mTimer) {
            mTimer.cancel();
            mTimer.purge();
        }
        mTimer = new Timer();
        mTimer.schedule(new AutoCancelTask(), 15 * 1000);

        Util.putLatestHotspotId(mContext, mData.id);
        RequestManager.getInstance().onEvent("6_hotspot_dialog", "100001",  null, null, null, null, null);
    }

    public boolean isDialogShowing() {
        return isDialogShowing;
    }

    private void loadBgImg(String bgImgUrl) {
        if (TextUtils.isEmpty(bgImgUrl)) {
            LibDeprecatedLogger.d("url is invalidate");
            return;
        }
        mBgImageView.setImageRes(bgImgUrl,mContext.getResources().getDrawable(R.drawable.vertical_default_big_poster)
                ,mContext.getResources().getDrawable(R.drawable.vertical_default_big_poster));
    }

    private void onConfirmClick() {
        RequestManager.getInstance().onClickHotspotConfirm();
        if (null == mData) {
            return;
        }
        String type = mData.type;
        try {
            ServerMessage.Parameter parameter = new Gson().fromJson(mData.parameter, ServerMessage.Parameter.class);
            switch (type) {
                case "0":
                    break;
                case "1":
                    // TODO: 2018/1/9 影片
                    int aid;
                    if (parameter.dataType == 2) {
                        aid = Integer.parseInt(parameter.videoId);
                    } else {
                        aid = Integer.parseInt(parameter.albumId);
                    }
                    ActivityLauncher.startVideoDetailWithAppSource(mContext, Constant.PAGE_HOTSPOT, aid, parameter.dataType, 2);
                    RequestManager.getInstance().onHotspot2Detail(aid+"");
                    break;
                case "2":
                    ActivityLauncher.startListVideoActivity(mContext, Integer.parseInt(parameter.labelId), true,2);
                    RequestManager.getInstance().onHotspot2ListVideo(parameter.labelId);
                    break;
                case "3":
                    // TODO: 2018/1/9 运营
                    ActivityLauncher.startPayActivity(mContext, true, PayActivity.PAY_SOURCE_HOTSPOT,2);
                    RequestManager.getInstance().onHotspot2Pay();
                    break;
                case "4":
                    RequestManager.getInstance().onHotspot2Pay();
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeDialog();
    }

    private void closeDialog() {
        isDialogShowing = false;
        mWm.removeView(mDialogView);
        sInstance = null;
        if (null != mTimer) {
            mTimer.cancel();
            mTimer.purge();
            mTimer = null;
        }
        if (null != mHandler) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }

//        mContext.stopService(new Intent(mContext, SystemDialogService.class));
//        mContext = null;
    }

    private class AutoCancelTask extends TimerTask {

        @Override
        public void run() {
            if (isDialogShowing) {
                mHandler.sendEmptyMessage(MSG_CLOSE_DIALOG);
            } else {
                mHandler.removeCallbacksAndMessages(null);
            }
        }
    }

}
