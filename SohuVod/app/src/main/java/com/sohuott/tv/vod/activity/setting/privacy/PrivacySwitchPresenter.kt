package com.sohuott.tv.vod.activity.setting.privacy

import android.content.Context
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.utils.PrivacySettingHelper
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingItem

/**
 *
 * @Description
 * @date 2022/3/22 14:39
 * <AUTHOR>
 * @Version 1.0
 */
class PrivacySwitchPresenter(private val context: Context?) :
    DefaultPresenter(R.layout.item_privacy_setting_switch_layout) {

    interface OnItemKeyEventListener {
        fun onItemKeyLeft()
    }
    private var onItemKeyEventListener: OnItemKeyEventListener? = null

    fun setOnItemKeyEventListener(onItemKeyEventListener: OnItemKeyEventListener){
        this.onItemKeyEventListener = onItemKeyEventListener
    }

    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as PrivacySettingItem
        val name = viewHolder.getView<TextView>(R.id.tv_privacy_switch_name)
        val icon = viewHolder.getView<AppCompatImageView>(R.id.iv_privacy_switch)
        val layout = viewHolder.getView<RelativeLayout>(R.id.layout_privacy_switch)
        name.text = item.name
//        var textColor:Int?
        layout.setOnFocusChangeListener { v, hasFocus ->
            name.isSelected = hasFocus
            icon.isSelected = hasFocus
            if (hasFocus) {
                v.animate().scaleX(1.1f).scaleY(1.1f).setDuration(300).start()
            } else {
                v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(300).start()
            }
        }
        var isCheck = false
        if (TextUtils.equals(item.id, "recommend_open")) {
            isCheck = PrivacySettingHelper.getRecommendIsOpen(context!!)
        }
        if (TextUtils.equals(item.id, "recommend_close")) {
            isCheck = !PrivacySettingHelper.getRecommendIsOpen(context!!)
        }

        if (TextUtils.equals(item.id, "ad_open")) {
            isCheck = PrivacySettingHelper.getAdIsOpen(context!!)
        }
        if (TextUtils.equals(item.id, "ad_close")) {
            isCheck = !PrivacySettingHelper.getAdIsOpen(context!!)
        }
        setShowIcon(icon,item.isSwitch,isCheck)

//        layout.setOnKeyListener{ view, i, keyEvent ->
//            if (i == KeyEvent.KEYCODE_DPAD_LEFT && keyEvent.action == KeyEvent.ACTION_DOWN) {
//                val finder = FocusFinder.getInstance()
//                val left = finder.findNextFocus(view.parent as ViewGroup?,view, View.FOCUS_LEFT)
//                if (left == null && onItemKeyEventListener != null) {
//                    onItemKeyEventListener?.onItemKeyLeft()
//                    true
//                }
//            }
//            false
//        }

//        layout.setOnClickListener {
//            when (item.id) {
//                "recommend_open" -> {
//                    if (context != null) {
//                        PrivacySettingHelper.setRecommend(context, true)
//                        setShowIcon(icon,item.isSwitch,true)
//                    }
//                }
//                "recommend_close" -> {
//                    if (context != null) {
//                        PrivacySettingHelper.setRecommend(context, false)
//                        setShowIcon(icon,item.isSwitch,false)
//
//                    }
//                }
//
//                "ad_open" -> {
//                    if (context != null) {
//                        PrivacySettingHelper.setAd(context, true)
//                        setShowIcon(icon,item.isSwitch,true)
//                    }
//                }
//
//                "ad_close" -> {
//                    if (context != null) {
//                        PrivacySettingHelper.setAd(context, false)
//                        setShowIcon(icon,item.isSwitch,false)
//                    }
//                }
//
//                "collect" -> {
//                    if (context != null) {
//                        ShowPrivacyWebViewActivity.actionStart(context)
//                    }
//                }
//                "share" -> {
//                    if (context != null) {
//                        ShowPrivacyWebViewActivity.actionStart(context)
//                    }
//                }
//                "permission" -> {
//                    if (context != null) {
//                        ShowPrivacyWebViewActivity.actionStart(context)
//                    }
//                }
//
//                "download" -> {
//                    if (context != null) {
//                        DownloadUserInfoActivity.actionStart(context)
//                    }
//                }
//
//                "unsubscribe" -> {
//                    if (context != null) {
//                        ActivityLauncher.startAccountLogOffActivity(context)
//                    }
//                }
//
//            }
//
//        }

    }

    private fun setShowIcon(icon:View ,isSwitch:Boolean,isCheck:Boolean){
        icon.visibility =
            if (isSwitch && isCheck) View.VISIBLE else View.GONE

    }


    override fun onCreateViewHolder(parent: ViewGroup?): ViewHolder {
        return super.onCreateViewHolder(parent)
    }

    override fun setOnClickListener(holder: ViewHolder?, listener: View.OnClickListener?) {
        super.setOnClickListener(holder, listener)
    }
}