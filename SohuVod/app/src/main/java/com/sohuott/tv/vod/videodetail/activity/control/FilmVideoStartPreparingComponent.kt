package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import com.sh.ott.video.base.component.ShPlayerConstants
import com.sh.ott.video.player.PlayerConstants
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible

class FilmVideoStartPreparingComponent constructor(context: Context) :
    BaseVideoStartPreparingComponent(context) {

    //是否是切换清晰度时
    var isChangeResolutionApp = false
    var isBuffered = false
    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        if (adType == ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_PAUSE) {
            gone()
        } else {
            when (playState) {
                PlayerConstants.VideoState.PLAYBACK_COMPLETED,
                PlayerConstants.VideoState.STOPPED,
                PlayerConstants.VideoState.ERROR,
                PlayerConstants.VideoState.PAUSED,
                PlayerConstants.VideoState.PLAYING -> {
                    isChangeResolutionApp = false
//                    if (isChangeResolutionApp && isBuffered) {
                    mPlayerLoadingView?.visibility = GONE
                    gone()
//                        isChangeResolutionApp = false
//                        isBuffered = false
//                        return
//                    }
//                    if (!isChangeResolutionApp ) {
//                        mPlayerLoadingView?.visibility = GONE
//                        gone()
//                        isBuffered = false
//                    }
                }

                PlayerConstants.VideoState.PLAYING_BACK -> {
                    if (isChangeResolutionApp) return
                    mPlayerLoadingView?.visibility = GONE
                    gone()
                }

//                PlayerConstants.VideoState.BUFFERING -> {
//                    if (isChangeResolutionApp) return
//                    if (isInitShowState) return
//                    mPlayerLoadingView?.visibility = GONE
//                    gone()
//                }
//
                PlayerConstants.VideoState.IDLE,
                PlayerConstants.VideoState.PREPARING,
                -> {
                    visible()
                    mPlayerLoadingView?.visibility = VISIBLE
                }

            }
        }
    }
}