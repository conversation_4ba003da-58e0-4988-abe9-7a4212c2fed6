package com.sohuott.tv.vod.videodetail.horlist;

import static com.sohuott.tv.vod.lib.utils.Constant.DATA_TYPE_PGC;
import static com.sohuott.tv.vod.lib.utils.Constant.DATA_TYPE_VRS;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_JUMP_TYPE_ACTOR;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_JUMP_TYPE_LABEL;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_JUMP_TYPE_LABEL_DETAIL;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_TYPE_ACTOR;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_TYPE_ALBUM_4;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_TYPE_ALBUM_6;

import android.content.res.Resources;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.HorizontalGridView;
import androidx.leanback.widget.ItemBridgeAdapter;
import androidx.leanback.widget.VerticalGridView;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.vlayout.DelegateAdapter;
import com.alibaba.android.vlayout.LayoutHelper;
import com.alibaba.android.vlayout.layout.BaseLayoutHelper;
import com.alibaba.android.vlayout.layout.LinearLayoutHelper;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.rvhelper.BaseViewHolder;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.presenter.launcher.ActorPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeThreeContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.VideoDetailTypeTwoContentPresenter;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel;
import com.sohuott.tv.vod.videodetail.vlist.VLayoutRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;

/**
 * Created by XiyingCao on 2018/1/18.
 */

public class DetailHorDelegateAdapter extends DelegateAdapter.Adapter<BaseViewHolder>
        implements View.OnClickListener, View.OnFocusChangeListener {
    private String TAG = getClass().getSimpleName();
    private VideoDetailRecommendModel.DataBean mDataBeam;
    protected FocusBorderView mFocusBorderView;
    private int mDataType;
    //Adapter内部使用，标识item type
    private int mItemType;
    //用户统计上报
    private int mAdapterType;
    private BaseLayoutHelper mLayoutHelper;
    private VLayoutRecyclerView mParent;

    private int mAdapterIndex;

    public DetailHorDelegateAdapter(VLayoutRecyclerView parent, VideoDetailRecommendModel.DataBean dataBean, int dataType, boolean last, int adapterIndex) {
        init(parent, dataBean, dataType, adapterIndex);
//        if (last) {
//            mLayoutHelper.setMarginBottom(parent.getResources().getDimensionPixelOffset(R.dimen.y100));
//        }
    }





    void init(VLayoutRecyclerView parent, VideoDetailRecommendModel.DataBean dataBean, int dataType, int adapterIndex) {
        mParent = parent;
        mDataBeam = dataBean;
        mItemType = dataBean.getType();
        mAdapterType = dataBean.getType();
        mAdapterIndex = adapterIndex;
        mDataType = dataType;
//        if (mDataType == DATA_TYPE_PGC) {
//            mLayoutHelper = new GridLayoutHelper(3);
//            ((GridLayoutHelper)mLayoutHelper).setHGap(mParent.getResources().getDimensionPixelOffset(R.dimen.x32));
//            ((GridLayoutHelper)mLayoutHelper).setVGap(mParent.getResources().getDimensionPixelOffset(R.dimen.y2));
//            ((GridLayoutHelper)mLayoutHelper).setAutoExpand(false);
//
//        } else {
            mLayoutHelper = new LinearLayoutHelper();
//        }
        mLayoutHelper.setMarginLeft(mParent.getResources().getDimensionPixelOffset(R.dimen.x92));
//        mLayoutHelper.setMarginRight(mParent.getResources().getDimensionPixelOffset(R.dimen.x92));
//        mLayoutHelper.setZIndex(mItemType);
    }

    @Override
    public LayoutHelper onCreateLayoutHelper() {
        return mLayoutHelper;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        mFocusBorderView = focusBorderView;
    }

    @Override
    public BaseViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (mDataType == DATA_TYPE_VRS) {
            return new BaseViewHolder(createHorizontalGridView(mItemType));
        } else {
            return new BaseViewHolder(createVerticalGridView());
        }
    }
    private VerticalGridView createVerticalGridView(){
        VerticalGridView verticalGridView = (VerticalGridView) LayoutInflater.from(mParent.getContext()).inflate(R.layout.detail_vertical_grid_view, null);
        verticalGridView.setClipChildren(false);
        verticalGridView.setClipToPadding(false);
        verticalGridView.setNumColumns(4);
        verticalGridView.setVerticalSpacing(48);

        ArrayObjectAdapter objectAdapter ;
        objectAdapter = new ArrayObjectAdapter(new VideoDetailTypeTwoContentPresenter());
        objectAdapter.addAll(0, mDataBeam.getContents());
        ViewGroup.MarginLayoutParams params = new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                mParent.getContext().getResources().getDimensionPixelOffset(R.dimen.y500));
        params.rightMargin = mParent.getResources().getDimensionPixelOffset(R.dimen.x40);
        params.topMargin = mParent.getResources().getDimensionPixelOffset(R.dimen.x20);
        verticalGridView.setLayoutParams(params);
        ItemBridgeAdapter itemBridgeAdapter = new ItemBridgeAdapter(objectAdapter);
        verticalGridView.setAdapter(itemBridgeAdapter);
        return verticalGridView;
    }

    private HorizontalGridView createHorizontalGridView(int mItemType){
        HorizontalGridView horGridView = new HorizontalGridView(mParent.getContext());
//        horGridView.setHorizontalSpacing(48);
        horGridView.setClipChildren(false);
        horGridView.setClipToPadding(false);
        horGridView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.right = parent.getContext().getResources().getDimensionPixelOffset(R.dimen.x48);
            }
        });
        ArrayObjectAdapter objectAdapter = null;
        int height = mParent.getContext().getResources().getDimensionPixelOffset(R.dimen.y400);
        switch (mItemType) {
            case DETAIL_VLIST_JUMP_TYPE_ACTOR:
                objectAdapter = new ArrayObjectAdapter(new ActorPresenter());
                height = mParent.getContext().getResources().getDimensionPixelOffset(R.dimen.y370);
                break;
            case DETAIL_VLIST_JUMP_TYPE_LABEL_DETAIL:
            case DETAIL_VLIST_JUMP_TYPE_LABEL:
                objectAdapter = new ArrayObjectAdapter(new VideoDetailTypeTwoContentPresenter());
                height = mParent.getContext().getResources().getDimensionPixelOffset(R.dimen.y346);
                break;
            default:
                objectAdapter = new ArrayObjectAdapter(new TypeThreeContentPresenter());
                height = mParent.getContext().getResources().getDimensionPixelOffset(R.dimen.y384);
                break;
        }
        if (mDataType == DATA_TYPE_PGC) {
            objectAdapter = new ArrayObjectAdapter(new VideoDetailTypeTwoContentPresenter());
            height = mParent.getContext().getResources().getDimensionPixelOffset(R.dimen.y300);
        }
        if (objectAdapter != null) {
            objectAdapter.addAll(0, mDataBeam.getContents());
        }
        ViewGroup.MarginLayoutParams params = new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                height);
        params.topMargin = mParent.getContext().getResources().getDimensionPixelOffset(R.dimen.y28);
        horGridView.setLayoutParams(params);
        ItemBridgeAdapter itemBridgeAdapter = new ItemBridgeAdapter(objectAdapter);
        horGridView.setAdapter(itemBridgeAdapter);
        return horGridView;
    }

    @Override
    public void onBindViewHolder(BaseViewHolder holder, int position) {
//        if (mDataType == DATA_TYPE_PGC) {
//            if (mDataBeam.getContents() == null || mDataBeam.getContents().get(position) == null) {
//                return;
//            }
//            VideoDetailRecommendModel.DataBean.ContentsBean content = mDataBeam.getContents().get(position);
//            holder.itemView.setTag(R.id.video_detail_recommend_relative_pos, position);
//            holder.itemView.setOnClickListener(this);
//            holder.itemView.setOnFocusChangeListener(this);
//
//            //update title
//            holder.setText(R.id.detail_recommend_title, content.getName());
//            holder.setEllipsize(R.id.detail_recommend_title, false);
//
//            // update poster、score、影人相关
//            GlideImageView poster = holder.getView(R.id.detail_recommend_poster);
//            switch (holder.getItemViewType()) {
//                case DETAIL_VLIST_TYPE_ACTOR:
//                    poster.setCircleImageRes(R.drawable.default_avatar);
//                    holder.itemView.setTag(R.id.video_detail_recommend_img_url, content.getHorPic());
//                    updateActorRelated(holder, position);
//                    break;
//                case DETAIL_VLIST_TYPE_ALBUM_4:
//                    poster.setImageRes(R.drawable.vertical_default_big_poster);
//                    holder.itemView.setTag(R.id.video_detail_recommend_img_url, content.getHorPic());
//                    updateScore(holder, position);
//                    holder.setVisible(R.id.detail_recommend_sub_title, false);
//                    if (mDataType == DATA_TYPE_VRS) {
//                        holder.setText(R.id.detail_recommend_sub_title, content.getAlbumParam().getComment());
//                    }
//                    break;
//                case DETAIL_VLIST_TYPE_ALBUM_6:
//                    if (TeenagersManger.isTeenager(mParent.getContext())) {
//                        poster.setImageRes(R.drawable.vertical_default_big_poster);
//                        holder.itemView.setTag(R.id.video_detail_recommend_img_url, content.getVerPic());
//                        updateScore(holder, position);
//                    } else {
//                        if (false) {
//                            // 需要跳转到二级列表
//                            poster.setImageRes(R.drawable.video_detail_recommend_more);
//                            holder.itemView.setTag(R.id.video_detail_recommend_img_url, R.drawable.video_detail_recommend_more);
//                            holder.setText(R.id.detail_recommend_title, "更多精彩");
//                            holder.setVisibility(R.id.detail_recommend_doubanIcon, View.GONE);
//                            holder.setVisibility(R.id.detail_recommend_score, View.GONE);
//                            holder.setVisibility(R.id.detail_recommend_sub_title_bg, View.GONE);
//                        } else {
//                            poster.setImageRes(R.drawable.vertical_default_big_poster);
//                            holder.itemView.setTag(R.id.video_detail_recommend_img_url, content.getVerPic());
//                            updateScore(holder, position);
//                        }
//                    }
//                    break;
//                default:
//                    poster.setImageRes(R.drawable.vertical_default_big_poster);
//                    holder.itemView.setTag(R.id.video_detail_recommend_img_url, content.getVerPic());
//                    break;
//            }
//
//            // 更新位置相关信息
//            holder.itemView.setTag(R.id.video_detail_recommend_focus_left_abs_pos, getNextLeft(position));
//            holder.itemView.setTag(R.id.video_detail_recommend_focus_right_abs_pos, getNextRight(position));
//            holder.itemView.setTag(R.id.video_detail_recommend_focus_up_abs_pos, getNextUp(position));
//            holder.itemView.setTag(R.id.video_detail_recommend_focus_down_abs_pos, getNextDown(position));
//            holder.itemView.setTag(R.id.video_detail_recommend_adapter_index, mAdapterIndex);
//        }
    }

    private int getNextLeft(int pos) {
        if (pos - 1 < 0) {
            return -1;
        } else {
            return pos - 1 + mLayoutHelper.getRange().getLower();
        }
    }

    private int getNextRight(int pos) {
        if (pos + 1 < getItemCount()) {
            return pos + 1 + mLayoutHelper.getRange().getLower();
        } else {
            return -1;
        }
    }

    private int getNextUp(int pos) {
//        int colIndex = pos / mLayoutHelper.getSpanCount();
//        if (colIndex > 0) {
//            return pos - mLayoutHelper.getSpanCount() + mLayoutHelper.getRange().getLower();
//        }
        return -1;
    }

    private int getNextDown(int pos) {
//        int colIndex = pos / mLayoutHelper.getSpanCount();
//        if (colIndex < (getItemCount() - 1) / mLayoutHelper.getSpanCount()) {
//            if (pos + mLayoutHelper.getSpanCount() < getItemCount()) {
//                return pos + mLayoutHelper.getSpanCount() + mLayoutHelper.getRange().getLower();
//            } else {
//                return getItemCount() + mLayoutHelper.getRange().getLower();
//            }
//        }
        return -1;
    }

    private void updateActorRelated(BaseViewHolder holder, int position) {
        VideoDetailRecommendModel.DataBean.ContentsBean content = mDataBeam.getContents().get(position);
        Resources resources = holder.itemView.getResources();
        if (mDataType == Constant.DATA_TYPE_VRS) {
            if (TextUtils.isEmpty(content.getRole())) {
                holder.setVisible(R.id.detail_recommend_sub_title, false);
            } else {
                holder.setVisible(R.id.detail_recommend_sub_title, true);
                String prefix = resources.getString(R.string.video_detail_role_play);
                if (content.getRole().equals("导演")) {
                    prefix = "";
                }
                holder.setText(R.id.detail_recommend_sub_title, prefix + content.getRole());
            }
        } else {
            String fanCount = content.getFanCount();
            if (TextUtils.isEmpty(fanCount)) {
                holder.setVisible(R.id.detail_recommend_sub_title, false);
            } else {
                holder.setVisible(R.id.detail_recommend_sub_title, true);
                holder.setText(R.id.detail_recommend_sub_title, resources.getString(R.string.video_detail_fans) + fanCount);
            }
        }
    }

    private void updateScore(final BaseViewHolder holder, int position) {
        if (mDataType != Constant.DATA_TYPE_VRS) {
            holder.setVisibility(R.id.detail_recommend_doubanIcon, View.GONE);
            holder.setVisibility(R.id.detail_recommend_score, View.GONE);
            if (holder.getView(R.id.detail_recommend_sub_title_bg) != null) {
                holder.setVisibility(R.id.detail_recommend_sub_title_bg, View.GONE);
            }
            return;
        }
        VideoDetailRecommendModel.DataBean.ContentsBean content = mDataBeam.getContents().get(position);
        String cateCodeStr = content.getAlbumParam().getCateCode();
        if (!TextUtils.isEmpty(cateCodeStr) && cateCodeStr.equals(Constant.EDU_CATE_CODE + "")) {
            holder.setVisibility(R.id.detail_recommend_doubanIcon, View.GONE);
            holder.setVisibility(R.id.detail_recommend_score, View.GONE);
        } else {
            if (!TextUtils.isEmpty(content.getAlbumParam().getScoreSource()) && content.getAlbumParam().getScoreSource().equals("1")) {
                holder.setVisibility(R.id.detail_recommend_doubanIcon, View.GONE);
                holder.setVisibility(R.id.detail_recommend_score, View.VISIBLE);
                holder.setText(R.id.detail_recommend_score, content.getAlbumParam().getScore());
            } else {
                holder.setVisibility(R.id.detail_recommend_doubanIcon, View.VISIBLE);
                holder.setVisibility(R.id.detail_recommend_score, View.VISIBLE);
                holder.setText(R.id.detail_recommend_score, content.getAlbumParam().getScore());
            }
        }
        if (holder.getView(R.id.detail_recommend_sub_title_bg) != null) {
            holder.setVisibility(R.id.detail_recommend_sub_title_bg, View.VISIBLE);
        }
    }

    @Override
    public int getItemCount() {
//        if (mDataType == DATA_TYPE_PGC) {
//            if (mDataBeam.getContents() == null) {
//                return 0;
//            } else if (mDataBeam.getContents().size() > ((GridLayoutHelper)mLayoutHelper).getSpanCount() * 2 - 1) {
//                return ((GridLayoutHelper)mLayoutHelper).getSpanCount() * 2;
//            } else if (mDataBeam.getContents().size() > ((GridLayoutHelper)mLayoutHelper).getSpanCount() - 1) {
//                return ((GridLayoutHelper)mLayoutHelper).getSpanCount();
//            } else {
//                return mDataBeam.getContents().size();
//            }
//        } else {
            return 1;
//        }
    }

    @Override
    public int getItemViewType(int position) {
        return mAdapterIndex;
    }

    @Override
    public void onClick(View v) {
        if (mDataBeam.getContents() != null && mDataBeam.getContents().size() > (Integer) v.getTag(R.id.video_detail_recommend_relative_pos)) {
            final int index = (Integer) v.getTag(R.id.video_detail_recommend_relative_pos);
            RequestManager.onEvent("6_info", "6_info_recommend_list", String.valueOf(index),
                    String.valueOf(mDataBeam.getOrder()), String.valueOf(mItemType), String.valueOf(mAdapterType), null);

            switch (mItemType) {
                case DETAIL_VLIST_TYPE_ALBUM_4:
                    jumpVideoDetail(index);
                    break;
                case DETAIL_VLIST_TYPE_ALBUM_6:
                    if (index == 11 && mItemType == DETAIL_VLIST_TYPE_ALBUM_6) {
                        // 需要跳转到二级列表
                        jumpSubGridList();
                    } else {
                        jumpVideoDetail(index);
                    }
                    break;
                case DETAIL_VLIST_TYPE_ACTOR:
                    jumpActor(index);
                    break;
                default:
                    break;
            }
        } else {
            Log.d(TAG, "onClick exception, the Content list exception is null" + String.valueOf(mDataBeam.getContents() == null));
        }
    }

    private void jumpActor(int index) {
        int id = mDataBeam.getContents().get(index).getId();
        if (mDataType == Constant.DATA_TYPE_VRS) {
            ActivityLauncher.startActorListActivity(mParent.getContext(), id,
                    mDataBeam.getContents().get(index).getStarType() == 2,
                    mDataBeam.getContents().get(index).getName());
        } else {
            ActivityLauncher.startProducerActivity(mParent.getContext(), id);
        }
    }

    private void jumpSubGridList() {
        ActivityLauncher.startLabelGridListActivity(mParent.getContext(), mDataBeam.getCateCode(),
                "cat=" + mDataBeam.getSecondCateCode(), mDataBeam.getName());
    }

    private void jumpVideoDetail(int index) {
        int aid = mDataBeam.getContents().get(index).getId();
        ActivityLauncher.startVideoDetailActivity(mParent.getContext(), aid, mDataType, Constant.PAGE_DETAIL);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        BaseViewHolder holder = (BaseViewHolder) mParent.getChildViewHolder(v);
        holder.setEllipsize(R.id.detail_recommend_title, hasFocus);
        switch (mItemType) {
            case DETAIL_VLIST_TYPE_ACTOR:
                holder.setVisible(R.id.detail_recommend_focus_cover, hasFocus);
                break;
            case DETAIL_VLIST_TYPE_ALBUM_4:
                if (!TextUtils.isEmpty(((TextView) holder.getView(R.id.detail_recommend_sub_title)).getText())) {
                    holder.setVisible(R.id.detail_recommend_sub_title, hasFocus);
                }
            case DETAIL_VLIST_TYPE_ALBUM_6:
            default:
                if (hasFocus) {
                    if (mFocusBorderView != null) {
                        mFocusBorderView.setFocusView(v);
                        FocusUtil.setFocusAnimator(v, mFocusBorderView);
                    }
                } else {
                    if (mFocusBorderView != null) {
                        mFocusBorderView.setUnFocusView(v);
                        FocusUtil.setUnFocusAnimator(v);
                    }
                }
                break;
        }
    }
}
