package com.sohuott.tv.vod.app.config

import GsonConverter
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.google.gson.Gson
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.lib.api.RetrofitApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders
import kotlin.coroutines.CoroutineContext

/**
 * 用于请求 配置信息
 */
class AppConfigInfoManger {

    val context: CoroutineContext = Dispatchers.Main + SupervisorJob()
    private val coroutineScope: CoroutineScope = CoroutineScope(context = context)
    fun initPlayerConfigInfo() {
        coroutineScope.launch {
            try {
                val playerConfig =
                    Get<AppPlayerConfigInfo?>("${RetrofitApi.get().retrofitHost.baseHost}common/getConfigInfo.json?key=player_config") {
                        setHeaders(HeaderHelper.getHeaders().toHeaders())
                        setClient {
                            trustSSLCertificate()
                            connectionSpecs(
                                listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                                )
                            )
                        }
                        converter = GsonConverter()
                    }.await()
                val data = playerConfig?.data
                val resolutions = data?.resolutions
                if (!resolutions.isNullOrEmpty()) {
                    AppConfigDatabase.putDefaultResolutionId(resolutions.find { it.default==true }?.id!!)
                    val putJson=Gson().toJson(data)
                    AppConfigDatabase.putAppPlayerResolutionConfig(putJson)
                    AppLogger.d("获取播放器清晰度配置成功：".plus(putJson))
                } else {
                    AppLogger.e("获取播放器清晰度配置出错使用本地默认数据")
                }
            } catch (e: Exception) {
                AppLogger.e("获取播放器清晰度配置出错：".plus(e.localizedMessage))
            }
        }
    }

    companion object {
        private var manger: AppConfigInfoManger? = null

        @JvmStatic
        fun getInstance(): AppConfigInfoManger {
            manger ?: synchronized(this) {
                manger ?: AppConfigInfoManger().also {
                    manger = it
                }
            }
            return manger!!
        }
    }
}