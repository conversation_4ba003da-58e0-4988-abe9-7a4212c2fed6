package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import androidx.leanback.widget.Presenter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.load.model.LazyHeaders;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager;
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

public class TypeThreeContentPresenter extends Presenter {
    private Context mContext;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_three_layout, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        vh.view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
                if (hasFocus) {
                    vh.mFocusRoot.setVisibility(View.VISIBLE);
                    vh.mTVFocusEpisode.setVisibility(View.VISIBLE);
                    vh.mEpisodeBg.setVisibility(View.VISIBLE);
                    vh.mNameBg.setVisibility(View.INVISIBLE);
                    vh.mTvTypeThreeName.setVisibility(View.GONE);
                    vh.mFocusPlay.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.showWaveAnimation();
                } else {
                    vh.mFocusRoot.setVisibility(View.GONE);
                    vh.mTVFocusEpisode.setVisibility(View.GONE);
                    vh.mEpisodeBg.setVisibility(View.GONE);
                    vh.mNameBg.setVisibility(View.VISIBLE);
                    vh.mTvTypeThreeName.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.setVisibility(View.GONE);
                    vh.mFocusPlay.cancelWaveAnimation();
                }
            }
        });
        if (item instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean) {
            ContentGroup.DataBean.ContentsBean.AlbumListBean albumBean = (ContentGroup.DataBean.ContentsBean.AlbumListBean) item;
            GlideUrl glideUrl = new GlideUrl(albumBean.tvVerPic, new LazyHeaders.Builder()
                    .addHeader("ImageTag", "Launcher")
                    .build());
            Glide.with(mContext)
                    .load(glideUrl)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeThreePoster);
            vh.mTvTypeThreeName.setText(albumBean.tvName);

            vh.mTVFocusEpisode.setText(Util.getHintTV(item));
            vh.mTvFocusName.setText(albumBean.tvName);
            vh.mTvFocusName.setMaxLines(4);
            vh.mTvFocusName.setEllipsize(TextUtils.TruncateAt.END);
            vh.mTvFocusDesc.setText(albumBean.tvComment);



            RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"),
                    albumBean.pathInfo,
                    albumBean.objectInfo, null);

            if (albumBean.channelType == Constant.TYPE_VIP) {
                vh.mFocusRoot.setBackgroundResource(R.drawable.item_type_one_vip_focus_selector);
                vh.mFocusView.setBackgroundResource(R.drawable.bg_vip_focus_selector);
            } else {
                vh.mIvTypeThreePoster.setCornerTypeWithType(albumBean.tvIsFee,
                        albumBean.tvIsEarly,
                        albumBean.useTicket,
                        albumBean.paySeparate,
                        albumBean.cornerType);
            }
        } else if (item instanceof VideoDetailRecommendModel.DataBean.ContentsBean) {
            VideoDetailRecommendModel.DataBean.ContentsBean contentsBean = (VideoDetailRecommendModel.DataBean.ContentsBean) item;

            if (contentsBean.getType() == 1) {
                //为你推荐
                VideoDetailRequestManager.recommendExposure(String.valueOf(contentsBean.getIndex() + 1), String.valueOf(contentsBean.getId()), contentsBean.getPdna());
            } else if (contentsBean.getType() == 6) {
                //影人剧场
                VideoDetailRequestManager.theaterExposure(String.valueOf(contentsBean.getIndex() + 1), String.valueOf(contentsBean.getId()));
            } else if (contentsBean.getType() == 5) {
                //分类
                VideoDetailRequestManager.classifyExposure(String.valueOf(contentsBean.getIndex() + 1), String.valueOf(contentsBean.getId()),
                        String.valueOf(contentsBean.getCateCode()), contentsBean.getTagName());
            }


            GlideUrl glideUrl = new GlideUrl(contentsBean.getVerPic(), new LazyHeaders.Builder()
                    .addHeader("ImageTag", "VideoDetail")
                    .build());
            Glide.with(mContext)
                    .load(glideUrl)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeThreePoster);
            vh.mTvTypeThreeName.setText(contentsBean.getName());

            vh.mTVFocusEpisode.setText(Util.getHintTV(item));
            vh.mTvFocusName.setText(contentsBean.getName());
            vh.mTvFocusName.setMaxLines(4);
            vh.mTvFocusName.setEllipsize(TextUtils.TruncateAt.END);
            if (contentsBean.getAlbumParam() != null) {
                vh.mTvFocusDesc.setText(contentsBean.getAlbumParam().getComment());
                vh.mIvTypeThreePoster.setCornerTypeWithType(Integer.parseInt(contentsBean.getAlbumParam().getTvIsFee()),
                        contentsBean.getAlbumParam().getTvIsEarly(),
                        contentsBean.getAlbumParam().getUseTicket(),
                        contentsBean.getAlbumParam().getPaySeparate(),
                        Integer.parseInt(contentsBean.getAlbumParam().getCornerType()));
            }

            vh.view.setOnClickListener(v -> {
                int columnId = 0;
                if (contentsBean.getType() == 1) {
                    VideoDetailRequestManager.recommendClick(String.valueOf(contentsBean.getIndex() + 1), String.valueOf(contentsBean.getId()), contentsBean.getPdna());
                    columnId = 2001;
                } else if (contentsBean.getType() == 6) {
                    VideoDetailRequestManager.theaterClick(String.valueOf(contentsBean.getIndex() + 1), String.valueOf(contentsBean.getId()));
                    columnId = 2004;
                } else if (contentsBean.getType() == 5) {
                    VideoDetailRequestManager.classifyClick(String.valueOf(contentsBean.getIndex() + 1), String.valueOf(contentsBean.getId()),
                            String.valueOf(contentsBean.getCateCode()), contentsBean.getTagName());
                    columnId = 2005;
                }
                ActivityLauncher.startVideoDetailActivity(mContext, contentsBean.getId(), 0, columnId, contentsBean.getPdna());
            });
        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {
        ViewHolder vh = (ViewHolder) viewHolder;
        Glide.with(vh.mIvTypeThreePoster.getContext()).clear(vh.mIvTypeThreePoster);
    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final CornerTagImageView mIvTypeThreePoster;
        private final TextView mTvTypeThreeName, mTvFocusName, mTvFocusDesc, mTVFocusEpisode;
        private LinearLayout mFocusRoot;
        private RippleDiffuse mFocusPlay;
        private View mFocusView, mEpisodeBg, mNameBg;

        public ViewHolder(View view) {
            super(view);
            mIvTypeThreePoster = (CornerTagImageView) view.findViewById(R.id.iv_type_three_poster);
            mTvTypeThreeName = (TextView) view.findViewById(R.id.tv_type_three_name);
            mFocusRoot = (LinearLayout) view.findViewById(R.id.type_three_focus_root);
            mTvFocusName = (TextView) view.findViewById(R.id.type_three_focus_name);
            mTvFocusDesc = (TextView) view.findViewById(R.id.type_three_focus_desc);
            mTVFocusEpisode = (TextView) view.findViewById(R.id.type_three_focus_episode);
            mFocusPlay = (RippleDiffuse) view.findViewById(R.id.type_three_focus_play);
            mFocusView = view.findViewById(R.id.type_three_focus);
            mEpisodeBg = view.findViewById(R.id.focus_episode_bg);
            mNameBg = view.findViewById(R.id.name_bg);
        }
    }
}

