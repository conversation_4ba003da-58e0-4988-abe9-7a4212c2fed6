package com.sohuott.tv.vod.view;

import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.utils.FocusUtil;

/**
 * ${DESC}
 *
 * <AUTHOR>
 *         created at 2017/10/16
 */
public class FocusTextView extends AppCompatTextView implements View.OnFocusChangeListener{
    private int mAniTime;

    public FocusTextView(Context context) {
        super(context);
        init(context,null);
    }

    public FocusTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public FocusTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    FocusBorderView mFocusBorderView;
    private boolean mIntercept;

    @SuppressWarnings("ResourceType")
    private void init(Context context,AttributeSet attrs) {
        super.setOnFocusChangeListener(this);
        setFocusable(true);

        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.FocusTextView);
        int viewId = typedArray.getResourceId(R.styleable.FocusTextView_focus, 0);
        if (viewId > 0&& context instanceof Activity) {
            mFocusBorderView= (FocusBorderView) ((Activity)context).findViewById(viewId);
        }
        mIntercept=typedArray.getBoolean(R.styleable.FocusTextView_focus_intercept,false);
        mAniTime = typedArray.getInteger(R.styleable.FocusTextView_focus_ani_time, 0);
        typedArray.recycle();
    }

    OnFocusChangeListener mOnFocusChangeListener;

    @Override
    public void setOnFocusChangeListener(OnFocusChangeListener l) {
        this.mOnFocusChangeListener = l;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if(!mIntercept){
            if (hasFocus) {
                if (mFocusBorderView != null) {
                    mFocusBorderView.setFocusView(v);
                    if (mAniTime != 0) {
                        FocusUtil.setFocusAnimator(v, mFocusBorderView, 1.1f, mAniTime);
                    } else {
                        FocusUtil.setFocusAnimator(v, mFocusBorderView);
                    }
                }
            } else {
                if (mFocusBorderView != null) {
                    mFocusBorderView.setUnFocusView(v);
                    if (mAniTime != 0) {
                        FocusUtil.setUnFocusAnimator(v, mAniTime);
                    } else {
                        FocusUtil.setUnFocusAnimator(v);
                    }
                }
            }
        }
        if(mOnFocusChangeListener!=null){
            mOnFocusChangeListener.onFocusChange(v,hasFocus);
        }
    }
}
