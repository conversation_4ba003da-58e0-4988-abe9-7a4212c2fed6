package com.sohuott.tv.vod.videodetail.horlist;

import android.util.TypedValue;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.TextView;

import com.alibaba.android.vlayout.DelegateAdapter;
import com.alibaba.android.vlayout.LayoutHelper;
import com.alibaba.android.vlayout.VirtualLayoutManager;
import com.alibaba.android.vlayout.layout.LinearLayoutHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.rvhelper.BaseViewHolder;

import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_TYPE_TITLE;

/**
 * Created by XiyingCao on 2018/3/5.
 */

public class VideoDetailVListTitleAdapter extends DelegateAdapter.Adapter<BaseViewHolder> {
    LinearLayoutHelper mLayoutHelper;
    String mTitle;
    private int mHelperIndex;

    public VideoDetailVListTitleAdapter(String title, LinearLayoutHelper layoutHelper, int helperIndex) {
        mTitle = title;
        mLayoutHelper = layoutHelper;
        mLayoutHelper.setZIndex(DETAIL_VLIST_TYPE_TITLE);
        mHelperIndex = helperIndex;
    }
    @Override
    public LayoutHelper onCreateLayoutHelper() {
        return mLayoutHelper;
    }

    @Override
    public BaseViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        TextView itemView = new TextView(parent.getContext());
        itemView.getPaint().setFakeBoldText(true);
        VirtualLayoutManager.LayoutParams layoutParams = new VirtualLayoutManager.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.setMargins(0, parent.getResources().getDimensionPixelOffset(R.dimen.y40), 0, 0);
        itemView.setLayoutParams(layoutParams);
        return new BaseViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(BaseViewHolder holder, int position) {
        holder.itemView.setLayoutParams(holder.itemView.getLayoutParams());
        holder.itemView.setTag(R.id.video_detail_recommend_adapter_index, mHelperIndex);
        ((TextView) holder.itemView).setText(mTitle);
        ((TextView) holder.itemView).setGravity(Gravity.LEFT);
        ((TextView) holder.itemView).setIncludeFontPadding(false);
        ((TextView) holder.itemView).setTextSize(TypedValue.COMPLEX_UNIT_PX, holder.itemView.getResources().getDimensionPixelOffset(R.dimen.y40));
        ((TextView) holder.itemView).setTextColor(holder.itemView.getResources().getColor(R.color.video_detail_list_title));
    }

    @Override
    public int getItemCount() {
        return 1;
    }

    @Override
    public int getItemViewType(int position) {
        return DETAIL_VLIST_TYPE_TITLE;
    }
}
