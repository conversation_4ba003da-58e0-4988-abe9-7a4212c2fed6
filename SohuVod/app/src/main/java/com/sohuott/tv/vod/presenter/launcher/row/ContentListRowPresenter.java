package com.sohuott.tv.vod.presenter.launcher.row;

import android.annotation.SuppressLint;

import androidx.leanback.widget.BaseOnItemViewClickedListener;
import androidx.leanback.widget.HorizontalGridView;
import androidx.leanback.widget.Presenter;
import androidx.leanback.widget.RowHeaderPresenter;
import androidx.leanback.widget.RowPresenter;

import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.base.BaseListRowPresenter;


public class ContentListRowPresenter extends BaseListRowPresenter {

    private static final String TAG = "ContentListRowPresenter";

    @SuppressLint("RestrictedApi")
    @Override
    protected void initializeRowViewHolder(final RowPresenter.ViewHolder holder) {
        super.initializeRowViewHolder(holder);

        final ViewHolder rowViewHolder = (ViewHolder) holder;
        rowViewHolder.getGridView().setHorizontalSpacing(
                rowViewHolder.getGridView().getContext().getResources().getDimensionPixelSize(R.dimen.x48));
        rowViewHolder.getGridView().setClipChildren(false);
        rowViewHolder.getGridView().setClipToPadding(false);
        RowHeaderPresenter.ViewHolder headerViewHolder = holder.getHeaderViewHolder();
        final TextView tv = (TextView) headerViewHolder.view.findViewById(R.id.row_header);
        tv.setTextColor(tv.getContext().getResources().getColor(R.color.white));
        tv.setPadding(0, 20, 0, 20);
        tv.setTextSize(rowViewHolder.getGridView().getContext().getResources().getDimensionPixelSize(R.dimen.x24));
        rowViewHolder.getGridView().setFocusScrollStrategy(HorizontalGridView.FOCUS_SCROLL_ITEM);

        setOnItemViewClickedListener(new BaseOnItemViewClickedListener() {
            @Override
            public void onItemClicked(Presenter.ViewHolder itemViewHolder,
                                      Object item, RowPresenter.ViewHolder rowViewHolder, Object row) {

            }
        });
    }
}
