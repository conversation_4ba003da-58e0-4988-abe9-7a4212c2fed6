package com.sohuott.tv.vod.presenter.launcher

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.leanback.widget.Presenter
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleEventObserver
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.sh.ott.video.view.ShVideoView
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.activity.launcher.LauncherPlayerManager
import com.sohuott.tv.vod.databinding.ItemTypeNewFilmLayoutBinding
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.ContentGroup.DataBean.ContentsBean
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.widget.HomeViewJump
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

class TypeNewFilmPresenterKt(private val lifecycle: Lifecycle? = null, private val scope: LifecycleCoroutineScope? = null): Presenter(){
    private var context: WeakReference<Context>? = null
    private var onTypeZeoPlayListener: TypeZeroContentPresenterKt.OnTypeZeroPlayListener? = null
    private var job: Job? = null
    private val launcherPlayerManager = LauncherPlayerManager()

    fun setOnTypeZeoPlayListener(onTypeZeoPlayListener: TypeZeroContentPresenterKt.OnTypeZeroPlayListener) {
        this.onTypeZeoPlayListener = onTypeZeoPlayListener
    }

    val observer = LifecycleEventObserver { source, event ->
        when(event) {
            Lifecycle.Event.ON_PAUSE -> {
                cancelDelay()
            }

            else -> {}
        }
    }

    init {
        lifecycle?.addObserver(observer)
    }

    override fun onCreateViewHolder(parent: ViewGroup?): ViewHolder {
        context = WeakReference(parent?.context)
        val binding = ItemTypeNewFilmLayoutBinding.inflate(LayoutInflater.from(parent?.context), parent, false)
        return TypeNewFilmViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder?, item: Any?) {
        val vh = viewHolder as TypeNewFilmViewHolder
        if (item is ContentsBean){
            Glide.with(context?.get()!!)
                .load(item.picUrl)
                .transform(RoundedCorners(context?.get()?.resources?.getDimensionPixelSize(R.dimen.x10)!!))
                .into(vh.binding.typeNewFilmPoster)

            Glide.with(context?.get()!!)
                .load(item.picUrl2)
                .transform(RoundedCorners(context?.get()?.resources?.getDimensionPixelSize(R.dimen.x10)!!))
                .into(vh.binding.typeNewBg)

            vh.binding.typeNewFilmRoot.setOnFocusChangeListener{v, hasFocus ->
                if (hasFocus) {
                    if (item.parameterPianhua!= null
                        && item.parameterPianhua.isNotEmpty()
                        && Util.getHuapingParams(context!!.get()) != 1
                        && Util.getDynamicVideoParams(context!!.get())) {
                        onTypeZeoPlayListener?.onTypeZeroStartPlay(vh, launcherPlayerManager)
                        startDelay(vh, item)
                    } else {
                        LibDeprecatedLogger.d("${item.name} no pianhua or huaping is 1")
                    }

                } else {
//                    onTypeZeoPlayListener?.cancelPlay(viewHolder)
                    cancelDelay()
                    vh.binding.rootPlayer.gone()
                    vh.binding.rootPlayer.removeAllViews()
                    launcherPlayerManager.releasePlayer()
                }
            }

            vh.binding.typeNewFilmRoot.setOnClickListener {

                HomeViewJump.clickAlbum(context?.get(), item,0,false,0);
                RequestManager.getInstance().onAllEvent(EventInfo(10147, "clk"),
                    item.pathInfo,
                    item?.objectInfo, null)
            }

            RequestManager.getInstance().onAllEvent(
                EventInfo(10146, "imp"),
                item.pathInfo,
                item.objectInfo, null
            )
        }
    }

    private fun startDelay(vh: TypeNewFilmViewHolder, item: ContentsBean) {
        job = scope?.launch {
            delay(2000L)
            LibDeprecatedLogger.d("2秒已经过去了")
            vh.binding.rootPlayer.visible()

            val videoView = ShVideoView(context?.get()!!)
//            videoView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            vh.binding.rootPlayer.removeAllViews()
            vh.binding.rootPlayer.addView(videoView)
            launcherPlayerManager.setPlayParamsAndPlay(context?.get(), item, vh, mShVideoView = videoView)
        }
    }

    private fun cancelDelay() {
        job?.cancel()
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder?) {
    }

    class TypeNewFilmViewHolder(val binding: ItemTypeNewFilmLayoutBinding): ViewHolder(binding.root), java.io.Serializable
}