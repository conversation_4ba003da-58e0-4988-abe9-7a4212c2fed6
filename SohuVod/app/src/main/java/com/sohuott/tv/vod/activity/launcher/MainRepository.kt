package com.sohuott.tv.vod.activity.launcher

import GsonConverter
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.model.TopInfo
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.coroutineScope
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class MainRepository {
    suspend fun fetchTopBarData(passport: String?, token: String?):Deferred<TopInfo>{
        return coroutineScope {
            Get<TopInfo>("${RetrofitApi.get().retrofitHost.baseHost}vip/headerInformation.json?company=snm") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                setQuery("passport", passport)
                setQuery("token", token)
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.MODERN_TLS,
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
            }
        }
    }
}