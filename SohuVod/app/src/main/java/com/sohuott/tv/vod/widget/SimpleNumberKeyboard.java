package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.ui.NewScaleFocusChangeListener;
import com.sohuott.tv.vod.ui.ResetPasswordDialog;
import com.sohu.lib_utils.FontUtils;

public class SimpleNumberKeyboard extends ConstraintLayout implements View.OnClickListener, View.OnFocusChangeListener, KeyboardDisplayView.InputCompleteListener, NewScaleFocusChangeListener.FocusCallback {
    KeyboardDisplayView mDisplayView;

    private OnCompleteListener mCompleteListener;

    public ResetPasswordDialog dialog;

    /**
     * 是否禁止输入
     */
    private Boolean isBanInput = false;

    public SimpleNumberKeyboard(Context context) {
        super(context);
        init(context, null);
    }

    public SimpleNumberKeyboard(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public SimpleNumberKeyboard(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        LayoutInflater.from(context).inflate(R.layout.layout_simple_number_keyboard, this, true);
        initListeners();
    }

    private void initListeners() {
        RelativeLayout topLine = (RelativeLayout) findViewById(R.id.ll_keyboard_top_line);
        mDisplayView = (KeyboardDisplayView) findViewById(R.id.displayView);
        initListeners(topLine);
        mDisplayView.setInputCompleteListener(this);
    }

    private void initListeners(ViewGroup layout) {
        int count = layout.getChildCount();
        if (count > 0) {
            for (int i = 0; i < count; i++) {
                View childView = layout.getChildAt(i);
                if (childView instanceof TextView) {
                    childView.setOnClickListener(this);
                    childView.setOnFocusChangeListener(this);
                    FontUtils.setTypeface(getContext(), (TextView) childView);
                }
            }
        }

    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_keyboard_key_reset) {//重置
            String password = TeenagersManger.getTeenagerPassword();
            if (null == password || password.isEmpty()) {
                ToastUtils.showToast(getContext(),"请先设置密码后再重置密码。");
                return;
            }
            if (dialog == null) {
                dialog = new ResetPasswordDialog(getContext());
            }
            dialog.setResetPasswordListener(mCompleteListener);
            dialog.showQrCode();
            TeenagersManger.getInstance().exposureResetPasswordClick();
            return;
        }
        if (v.getId() == R.id.tv_keyboard_key_delete) {//删除
            mDisplayView.deleteOne();
            return;
        }
        if (isBanInput) {
            return;
        }
        if (v instanceof TextView) {//数字
            String text = ((TextView) v).getText().toString();
            int number = Integer.parseInt(text);
            mDisplayView.inputOne(number);
        }
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (v instanceof TextView) {
            if (hasFocus) {
                v.animate().scaleX(1.1f).scaleY(1.1f).setDuration(300).start();
                v.bringToFront();
            } else {
                v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(300).start();
            }
        }
    }

    @Override
    public void onInputComplete(int[] numberPool) {
        String number = "" + numberPool[0] + numberPool[1] + numberPool[2] + numberPool[3];
        mCompleteListener.complete(number);
        displayReset();
    }

    public void displayReset() {
        mDisplayView.displayReset();
    }

    public interface OnCompleteListener {
        void complete(String number);

        void resetSuccess();
    }

    public void setOnCompleteListener(OnCompleteListener listener) {
        mCompleteListener = listener;
    }

    public void setIsBanInput(Boolean isBan) {
        isBanInput = isBan;
    }


    public Boolean getIsBanInput() {
        return isBanInput;
    }

}
