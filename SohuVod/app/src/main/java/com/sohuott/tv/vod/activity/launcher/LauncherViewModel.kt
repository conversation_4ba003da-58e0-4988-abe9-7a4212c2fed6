package com.sohuott.tv.vod.activity.launcher

import GsonConverter
import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.scopeNetLife
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.utils.PrivacySettingHelper.getRecommend
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.BootTipsBean
import com.sohuott.tv.vod.lib.model.ContentGroup
import com.sohuott.tv.vod.lib.model.launcher.HomeTab
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohuott.tv.vod.lib.utils.Util
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class LauncherViewModel(application: Application) : AndroidViewModel(application) {
    private val mHelper = LoginUserInformationHelper.getHelper(application)

    private var _currentPage = 1
    var isEnd = false // 没有更多的分页数据了
    var currentPage: Int
        get() = _currentPage
        set(value) {
            _currentPage = value
        }

    val ITEMS_PER_PAGE = 100  // 根据你的API返回的数据来设定
    val VISIBLE_THRESHOLD = 20  // 根据你希望在何处开始加载下一页来设定

    private var isLoading = false

    private val _moreContentData = MutableLiveData<ContentGroup.DataBean.ContentsBean>()

    val moreContentData: LiveData<ContentGroup.DataBean.ContentsBean>
        get() = _moreContentData

    private val _homeContentData = MutableLiveData<ContentGroup>()
    val homeContentData: LiveData<ContentGroup>
        get() = _homeContentData

    private val _homeChannelData = MutableLiveData<ResultData<HomeTab>>()
    val homeChannelData: LiveData<ResultData<HomeTab>>
        get() = _homeChannelData

    private val _adTipsData = MutableLiveData<ResultData<String>>()
    val adTipsString: LiveData<ResultData<String>>
        get() = _adTipsData


    var isViewTop: Boolean = false

    fun loadMore(channelId: Int) {
        if (!isLoading && !isEnd) {
            LibDeprecatedLogger.d("Launcher loadMore 真正请求")
            isLoading = true

            //http://fat-api.ott.tv.sohu.com/test/ott-api-v4/v4/common/groupListNew.json?channelListId=62&passport=1633769659176398848%40sohu.com&dis_rec=1&token=********************************************************************************************************************************************************************.wYjVjaWTHn7YBA8XNSCKKr7Yq2dfQtLPnJ7OBhmRA-4&pageNo=2&pageSize=30
            // load data
            scopeNetLife {
                val contentGroup =
                    Get<ContentGroup>("${RetrofitApi.get().retrofitHost.baseHost}common/groupListNew.json") {
                        setHeaders(HeaderHelper.getHeaders().toHeaders())
                        param("channelListId", channelId)
                        param("passport", mHelper.loginPassport)
                        param("dis_rec", getRecommend(getApplication()))
                        param("token", mHelper.loginToken)
                        param("pageNo", _currentPage.toString())
                        param("pageSize", ITEMS_PER_PAGE.toString())
                        converter = GsonConverter()
                        setClient {
                            trustSSLCertificate()
                            connectionSpecs(
                                listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                                )
                            )
                        }
                    }.await()
                LibDeprecatedLogger.d("首页请求结束: channelListId : ${channelId} contentGroup: ${contentGroup}")


                if (contentGroup.extend != null
                    && contentGroup.extend.endIndex != 1) isEnd = true //没有更多的数据
                //不是第一页的情况下，只提取为你推荐部分返回
                if (currentPage > 1) {
                    contentGroup.data?.forEach { group ->
                        group.contents?.forEach { content ->
                            if (content?.albumList != null && (content.type == Constant.TYPE_38 || content.type == Constant.TYPE_39)) {
                                LibDeprecatedLogger.d("找到数据了, albumList.size = ${content.albumList.size}, page = $currentPage")
                                _moreContentData.value = content
                            }
                        }
                    }
                } else {
                    _homeContentData.value = contentGroup
                }

                _currentPage++
                isLoading = false

            }
        }
    }

    fun getChannelList(context: Context) {
        val startTime = System.currentTimeMillis()
        scopeNetLife {
            LibDeprecatedLogger.d("频道列表请求开始时间: ${System.currentTimeMillis() - startTime}")
            try {
                val channelList =
                    Get<HomeTab>("${RetrofitApi.get().retrofitHost.baseHost}common/channelList.json") {
                        setHeaders(HeaderHelper.getHeaders().toHeaders())
                        param("dts", Util.getDtsParams(context))
                        setClient {
                            trustSSLCertificate()
                            connectionSpecs(
                                listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                                )
                            )
                        }
                    }.await()
                _homeChannelData.value = ResultData.Success(channelList)
            } catch (e: Exception) {
                _homeChannelData.value = ResultData.Error(e)
            }




            LibDeprecatedLogger.d("频道列表请求结束时间: ${System.currentTimeMillis() - startTime}")
//            AppLogger.d("channelList: $channelList")
        }
    }

    fun requestAdSplashTips() {
        scopeNetLife {
            try {
                val data =
                    Get<BootTipsBean>("${RetrofitApi.get().retrofitHost.baseHost}common/getConfigInfo.json?key=boot_tips") {
                        setHeaders(HeaderHelper.getHeaders().toHeaders())
                        setClient {
                            trustSSLCertificate()
                            connectionSpecs(
                                listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                                )
                            )
                        }
                    }.await()
                _adTipsData.value =
                    if (data.data.content.isNullOrEmpty()) ResultData.Error(Exception("数据为空")) else ResultData.Success(
                        data.data.content
                    )
            } catch (e: Exception) {
                _adTipsData.value = ResultData.Error(e)
            }
        }
    }
}

sealed class ResultData<out T> {
    data class Success<out T>(val data: T) : ResultData<T>()
    data class Error(val exception: Throwable) : ResultData<Nothing>()
}