package com.sohuott.tv.vod.fragment;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.push.event.ScanSuccessEvent;

import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017-03-29.
 */
public class BaseLoginFragment extends BaseFragment {

    private static final int MSG_SCAN_SUCCESS = 1;
    private static final int MSG_SCAN_FAILURE = 2;

    protected Context mContext;

    private MyHandler mHandler;

    protected View mView;

    protected TextView mQrCodeDescription2;

    protected ImageView mQrCodeImage;
//    private ImageView mQrCodeScanSuccessImage;

    protected TextView mQrCodeDescription;
//    private TextView mQrCodeScanSuccessTip;
//    private TextView mQrCodeScanSuccessTip2;

    public BaseLoginFragment() {
        super();
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mHandler = new MyHandler(this);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mView = inflater.inflate(R.layout.fragment_scan_login, null, false);
        initView();
        return mView;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initData();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mView) {
            mView = null;
        }
    }

    @Override
    protected boolean isEventBusAvailable() {
        return true;
    }

    @Subscribe
    public void onEventMainThread(ScanSuccessEvent event) {
        if (null == event) {
            return;
        }
        mHandler.sendEmptyMessage(MSG_SCAN_SUCCESS);
        onScanSucceed();
    }

    public void onScanSucceed() {
    }

    protected void update() {
        LibDeprecatedLogger.d("update()");
    }

    protected void initData() {
        LibDeprecatedLogger.d("initData()");
    }

    protected void setVisibility() {
        LibDeprecatedLogger.d("setVisibility()");
    }

    protected void updateView() {
        LibDeprecatedLogger.d("updateView()");

        if (mQrCodeImage.getAlpha() != 1.0F) {
            mQrCodeImage.setAlpha(1.0F);
        }
        mQrCodeImage.setImageResource(R.drawable.bg_launcher_poster);

//        if (mQrCodeScanSuccessImage.getVisibility() == View.VISIBLE) {
//            mQrCodeScanSuccessImage.setVisibility(View.GONE);
//        }

//        if (mQrCodeScanSuccessTip.getVisibility() == View.VISIBLE) {
//            mQrCodeScanSuccessTip.setVisibility(View.GONE);
//        }
//
//        if (mQrCodeScanSuccessTip2.getVisibility() == View.VISIBLE) {
//            mQrCodeScanSuccessTip2.setVisibility(View.GONE);
//        }

        setVisibility();
    }

    private void initView() {
        LibDeprecatedLogger.d("initView()");

        mQrCodeDescription2 = (TextView) mView.findViewById(R.id.qrcode_description2);

        mQrCodeImage = (ImageView) mView.findViewById(R.id.qrcode_image);
//        mQrCodeScanSuccessImage = (ImageView) mView.findViewById(R.id.qrcode_scan_success_image);

        mQrCodeDescription = (TextView) mView.findViewById(R.id.qrcode_description);
//        mQrCodeScanSuccessTip = (TextView) mView.findViewById(R.id.scan_success_tip);
//        mQrCodeScanSuccessTip2 = (TextView) mView.findViewById(R.id.scan_success_tip2);
    }

    private static class MyHandler extends Handler {
        private WeakReference<BaseLoginFragment> reference;

        public MyHandler(BaseLoginFragment ref) {
            super();
            reference = new WeakReference<BaseLoginFragment>(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            BaseLoginFragment ref = reference.get();
            if (null == ref) {
                return;
            }

            int what = msg.what;
            switch (what) {
                case MSG_SCAN_SUCCESS:
//                    if (null != ref.mQrCodeImage) {
//                        ref.mQrCodeImage.setAlpha(0.2f);
//                    }
//                    if (null != ref.mQrCodeScanSuccessImage) {
//                        ref.mQrCodeScanSuccessImage.setVisibility(View.VISIBLE);
//                    }
//                    if (null != ref.mQrCodeScanSuccessTip) {
//                        ref.mQrCodeScanSuccessTip.setVisibility(View.VISIBLE);
//                    }
/*
                    if (null != ref.mQrCodeScanSuccessTip2) {
                        ref.mQrCodeScanSuccessTip2.setVisibility(View.VISIBLE);
                    }
*/
                    break;
                case MSG_SCAN_FAILURE:
//                    if (null != ref.mQrCodeScanSuccessImage) {
//                        ref.mQrCodeScanSuccessImage.setVisibility(View.GONE);
//                    }
//                    if (null != ref.mQrCodeScanSuccessTip) {
//                        ref.mQrCodeScanSuccessTip.setVisibility(View.GONE);
//                    }
//                    if (null != ref.mQrCodeScanSuccessTip2) {
//                        ref.mQrCodeScanSuccessTip2.setVisibility(View.GONE);
//                    }
                    break;
                default:
                    break;
            }
        }
    }

}
