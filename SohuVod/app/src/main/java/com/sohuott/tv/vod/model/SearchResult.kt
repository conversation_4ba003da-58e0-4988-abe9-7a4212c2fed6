package com.sohuott.tv.vod.model


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SearchResult(
    @SerialName("data")
    var `data`: Data = Data(),
    @SerialName("message")
    var message: String = "",
    @SerialName("status")
    var status: Int = 0
) {
    @Serializable
    data class Data(
        @SerialName("relationItems")
        var relationItems: List<RelationItem> = listOf(),
        @SerialName("searchItems")
        var searchItems: List<SearchItem> = listOf()
    ) {
        @Serializable
        data class RelationItem(
            @SerialName("act")
            var act: String = "",
//            @SerialName("actors")
//            var actors: String = "",
            @SerialName("albumExtendsPic_120_160")
            var albumExtendsPic120160: String = "",
            @SerialName("albumExtendsPic_130_75")
            var albumExtendsPic13075: String = "",
            @SerialName("albumExtendsPic_144_144")
            var albumExtendsPic144144: String = "",
            @SerialName("albumExtendsPic_160_90")
            var albumExtendsPic16090: String = "",
            @SerialName("albumExtendsPic_170_110")
            var albumExtendsPic170110: String = "",
            @SerialName("albumExtendsPic_170_225")
            var albumExtendsPic170225: String = "",
            @SerialName("albumExtendsPic_240_180")
            var albumExtendsPic240180: String = "",
            @SerialName("albumExtendsPic_240_330")
            var albumExtendsPic240330: String = "",
            @SerialName("albumExtendsPic_320_180")
            var albumExtendsPic320180: String = "",
            @SerialName("albumExtendsPic_640_360")
            var albumExtendsPic640360: String = "",
            @SerialName("albumExtendsPic_80_60")
            var albumExtendsPic8060: String = "",
            @SerialName("albumextendspic_1920_1080")
            var albumextendspic19201080: String = "",
            @SerialName("areaName")
            var areaName: String = "",
            @SerialName("cateCode")
            var cateCode: Int = 0,
            @SerialName("chased")
            var chased: Int = 0,
            @SerialName("commont")
            var commont: String? = "",
            @SerialName("cornerType")
            var cornerType: Int = 0,
            @SerialName("dayPlayCount")
            var dayPlayCount: Int = 0,
            @SerialName("director")
            var director: String = "",
//            @SerialName("directors")
//            var directors: String = "",
            @SerialName("doubanScore")
            var doubanScore: Int = 0,
            @SerialName("episodeType")
            var episodeType: Int = 0,
            @SerialName("firstFeeVid")
            var firstFeeVid: Int = 0,
            @SerialName("genreName")
            var genreName: String = "",
            @SerialName("hasTrailer")
            var hasTrailer: Boolean = false,
            @SerialName("id")
            var id: Int = 0,
            @SerialName("isAudit")
            var isAudit: Int = 0,
            @SerialName("isLiked")
            var isLiked: Boolean = false,
            @SerialName("isShowTitle")
            var isShowTitle: Int = 0,
            @SerialName("issueTime")
            var issueTime: Long = 0,
            @SerialName("kisSeriesId")
            var kisSeriesId: Int = 0,
            @SerialName("latestVideoCount")
            var latestVideoCount: String = "",
            @SerialName("likeCount")
            var likeCount: String = "",
            @SerialName("maxVideoOrder")
            var maxVideoOrder: String = "",
            @SerialName("mergeTags")
            var mergeTags: String = "",
            @SerialName("ottFee")
            var ottFee: Int = 0,
            @SerialName("paySeparate")
            var paySeparate: Int = 0,
            @SerialName("pdna")
            var pdna: String = "",
            @SerialName("pgcProducer")
            var pgcProducer: String = "",
            @SerialName("playCount")
            var playCount: Int = 0,
            @SerialName("relationOrder")
            var relationOrder: Int = 0,
            @SerialName("score")
            var score: Double = 0.0,
            @SerialName("scoreSource")
            var scoreSource: String = "",
            @SerialName("secondCategoryCode")
            var secondCategoryCode: List<Int> = listOf(),
            @SerialName("showDate")
            var showDate: String = "",
            @SerialName("soonVerPic")
            var soonVerPic: String = "",
            @SerialName("superTheatreId")
            var superTheatreId: Int = 0,
            @SerialName("syncBroadcast")
            var syncBroadcast: Int = 0,
            @SerialName("tType")
            var tType: Int = 0,
            @SerialName("trailerAppendCount")
            var trailerAppendCount: Int = 0,
            @SerialName("trailerCount")
            var trailerCount: Int = 0,
            @SerialName("trailerId")
            var trailerId: Int = 0,
            @SerialName("tvApplicationUpdateTime")
            var tvApplicationUpdateTime: Long = 0,
            @SerialName("tvAreaId")
            var tvAreaId: Int = 0,
            @SerialName("tvBigPic")
            var tvBigPic: String = "",
            @SerialName("tvComment")
            var tvComment: String = "",
            @SerialName("tvDesc")
            var tvDesc: String = "",
            @SerialName("tvEffective")
            var tvEffective: Int = 0,
            @SerialName("tvHorBigPic")
            var tvHorBigPic: String = "",
            @SerialName("tvHorSmallPic")
            var tvHorSmallPic: String = "",
            @SerialName("tvIsDownload")
            var tvIsDownload: Int = 0,
            @SerialName("tvIsEarly")
            var tvIsEarly: Int = 0,
            @SerialName("tvIsFee")
            var tvIsFee: Int = 0,
            @SerialName("tvIsIntrest")
            var tvIsIntrest: Int = 0,
            @SerialName("tvLanguage")
            var tvLanguage: Int = 0,
            @SerialName("tvName")
            var tvName: String = "",
            @SerialName("tvOnly")
            var tvOnly: Int = 0,
            @SerialName("tvPic")
            var tvPic: String = "",
            @SerialName("tvSets")
            var tvSets: Int = 0,
            @SerialName("tvSmallPic")
            var tvSmallPic: String = "",
            @SerialName("tvUpdateTime")
            var tvUpdateTime: Long = 0,
            @SerialName("tvVerBigPic")
            var tvVerBigPic: String = "",
            @SerialName("tvVerId")
            var tvVerId: Int = 0,
            @SerialName("tvVerPic")
            var tvVerPic: String = "",
            @SerialName("tvVerSmallPic")
            var tvVerSmallPic: String = "",
            @SerialName("tvYear")
            var tvYear: Int = 0,
            @SerialName("updateNotification")
            var updateNotification: String = "",
            @SerialName("useTicket")
            var useTicket: Int = 0,
            @SerialName("versionIds")
            var versionIds: List<Int> = listOf(),
            @SerialName("index")
            var index: Int = 0
        )

        @Serializable
        data class SearchItem(
            @SerialName("act")
            var act: String? = "",
//            @SerialName("actors")
//            var actors: String = "",
            @SerialName("albumExtendsPic_120_160")
            var albumExtendsPic120160: String = "",
            @SerialName("albumExtendsPic_130_75")
            var albumExtendsPic13075: String = "",
            @SerialName("albumExtendsPic_144_144")
            var albumExtendsPic144144: String = "",
            @SerialName("albumExtendsPic_160_90")
            var albumExtendsPic16090: String = "",
            @SerialName("albumExtendsPic_170_110")
            var albumExtendsPic170110: String = "",
            @SerialName("albumExtendsPic_170_225")
            var albumExtendsPic170225: String = "",
            @SerialName("albumExtendsPic_240_180")
            var albumExtendsPic240180: String = "",
            @SerialName("albumExtendsPic_240_330")
            var albumExtendsPic240330: String = "",
            @SerialName("albumExtendsPic_320_180")
            var albumExtendsPic320180: String = "",
            @SerialName("albumExtendsPic_640_360")
            var albumExtendsPic640360: String = "",
            @SerialName("albumExtendsPic_80_60")
            var albumExtendsPic8060: String = "",
            @SerialName("albumextendspic_1920_1080")
            var albumextendspic19201080: String = "",
            @SerialName("areaName")
            var areaName: String = "",
            @SerialName("cateCode")
            var cateCode: Int = 0,
            @SerialName("chased")
            var chased: Int = 0,
            @SerialName("commont")
            var commont: String? = "",
            @SerialName("cornerType")
            var cornerType: Int = 0,
            @SerialName("dayPlayCount")
            var dayPlayCount: Int = 0,
            @SerialName("director")
            var director: String = "",
//            @SerialName("directors")
//            var directors: String = "",
            @SerialName("doubanScore")
            var doubanScore: Int = 0,
            @SerialName("episodeType")
            var episodeType: Int = 0,
            @SerialName("firstFeeVid")
            var firstFeeVid: Int = 0,
            @SerialName("genreName")
            var genreName: String = "",
            @SerialName("hasTrailer")
            var hasTrailer: Boolean = false,
            @SerialName("id")
            var id: Int = 0,
            @SerialName("isAudit")
            var isAudit: Int = 0,
            @SerialName("isLiked")
            var isLiked: Boolean = false,
            @SerialName("isShowTitle")
            var isShowTitle: Int = 0,
            @SerialName("issueTime")
            var issueTime: Long = 0,
            @SerialName("kisSeriesId")
            var kisSeriesId: Int = 0,
            @SerialName("latestVideoCount")
            var latestVideoCount: String = "",
            @SerialName("likeCount")
            var likeCount: String = "",
            @SerialName("maxVideoOrder")
            var maxVideoOrder: String = "",
            @SerialName("mergeTags")
            var mergeTags: String = "",
            @SerialName("ottFee")
            var ottFee: Int = 0,
            @SerialName("paySeparate")
            var paySeparate: Int = 0,
            @SerialName("pdna")
            var pdna: String = "",
            @SerialName("pgcProducer")
            var pgcProducer: String = "",
            @SerialName("playCount")
            var playCount: Int = 0,
            @SerialName("relationOrder")
            var relationOrder: Int = 0,
            @SerialName("score")
            var score: Double = 0.0,
            @SerialName("scoreSource")
            var scoreSource: String = "",
            @SerialName("secondCategoryCode")
            var secondCategoryCode: List<Int> = listOf(),
            @SerialName("showDate")
            var showDate:String = "",
            @SerialName("soonVerPic")
            var soonVerPic: String = "",
            @SerialName("superTheatreId")
            var superTheatreId: Int = 0,
            @SerialName("syncBroadcast")
            var syncBroadcast: Int = 0,
            @SerialName("tType")
            var tType: Int = 0,
            @SerialName("trailerAppendCount")
            var trailerAppendCount: Int = 0,
            @SerialName("trailerCount")
            var trailerCount: Int = 0,
            @SerialName("trailerId")
            var trailerId: Int = 0,
            @SerialName("tvApplicationUpdateTime")
            var tvApplicationUpdateTime: Long = 0,
            @SerialName("tvAreaId")
            var tvAreaId: Int = 0,
            @SerialName("tvBigPic")
            var tvBigPic: String = "",
            @SerialName("tvComment")
            var tvComment: String = "",
            @SerialName("tvDesc")
            var tvDesc: String = "",
            @SerialName("tvEffective")
            var tvEffective: Int = 0,
            @SerialName("tvHorBigPic")
            var tvHorBigPic: String = "",
            @SerialName("tvHorSmallPic")
            var tvHorSmallPic: String = "",
            @SerialName("tvIsDownload")
            var tvIsDownload: Int = 0,
            @SerialName("tvIsEarly")
            var tvIsEarly: Int = 0,
            @SerialName("tvIsFee")
            var tvIsFee: Int = 0,
            @SerialName("tvIsIntrest")
            var tvIsIntrest: Int = 0,
            @SerialName("tvLanguage")
            var tvLanguage: Int = 0,
            @SerialName("tvName")
            var tvName: String = "",
            @SerialName("tvOnly")
            var tvOnly: Int = 0,
            @SerialName("tvPic")
            var tvPic: String = "",
            @SerialName("tvSets")
            var tvSets: Int = 0,
            @SerialName("tvSmallPic")
            var tvSmallPic: String = "",
            @SerialName("tvUpdateTime")
            var tvUpdateTime: Long = 0,
            @SerialName("tvVerBigPic")
            var tvVerBigPic: String = "",
            @SerialName("tvVerId")
            var tvVerId: Int = 0,
            @SerialName("tvVerPic")
            var tvVerPic: String = "",
            @SerialName("tvVerSmallPic")
            var tvVerSmallPic: String = "",
            @SerialName("tvYear")
            var tvYear: Int = 0,
            @SerialName("updateNotification")
            var updateNotification: String = "",
            @SerialName("useTicket")
            var useTicket: Int = 0,
            @SerialName("versionIds")
            var versionIds: List<Int> = listOf()
        )
    }
}