package com.sohuott.tv.vod.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.model.DetailLabelModel;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.util.List;

/**
 * Created by yizhang210244 on 2017/9/1.
 */

public class DetailLabelAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder>{
    private static final int ITEM_VIEW_TYPE_VIP = 1;
    private static final int ITEM_VIEW_TYPE_DTS = 2;
    private static final int ITEM_VIEW_TYPE_NORMAL = 0;
    private List<DetailLabelModel> mLabelModelList;
    private int mCateCode;
    private View.OnKeyListener mOnKeyListener;
    private int mAlbumId;
    private FocusBorderView mFocusBorderView;

    public DetailLabelAdapter(List<DetailLabelModel> list, int albumId, int cateCode) {
        this.mLabelModelList = list;
        this.mAlbumId = albumId;
        this.mCateCode = cateCode;
    }

    public void setButtonKeyListen(View.OnKeyListener listen){
        mOnKeyListener = listen;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView){
        mFocusBorderView = focusBorderView;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view;
        RecyclerView.ViewHolder viewHolder;
        if (viewType == ITEM_VIEW_TYPE_VIP) {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.detail_label_vip, parent, false);
            viewHolder = new ViewHolderVip(view);
        } else if (viewType == ITEM_VIEW_TYPE_DTS) {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.detail_label_vip, parent, false);
            viewHolder = new ViewHolderVip(view);
//            viewHolder.itemView.setFocusable(true);
//            viewHolder.itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//                @Override
//                public void onFocusChange(View v, boolean hasFocus) {
//                    if (hasFocus) {
//                        if (mFocusBorderView != null) {
//                            mFocusBorderView.setFocusView(v);
//                            FocusUtil.setFocusAnimator(v, mFocusBorderView, 1.1f, 100);
//                        }
//                    } else {
//                        if (mFocusBorderView != null) {
//                            mFocusBorderView.setUnFocusView(v);
//                            FocusUtil.setUnFocusAnimator(v, 100);
//                        }
//                    }
//                }
//            });
        } else {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.detail_label_normal, parent, false);
            viewHolder = new ViewHolder(view);
        }

        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if(holder.getItemViewType()  == ITEM_VIEW_TYPE_NORMAL){
            ViewHolder viewHolder = (ViewHolder) holder;
            viewHolder.labelName.setText(mLabelModelList.get(position).getName());
        }else if(holder.getItemViewType() == ITEM_VIEW_TYPE_VIP){
            ViewHolderVip viewHolderVip = (ViewHolderVip) holder;
            if (Constant.EDU_CATE_CODE == mCateCode) {
                viewHolderVip.logo_icon.setImageResource(R.drawable.detail_edu_vip);
            } else {
                viewHolderVip.logo_icon.setImageResource(R.drawable.detail_vip);
            }
            return;
        }else {
            ViewHolderVip viewHolderVip = (ViewHolderVip) holder;
            viewHolderVip.logo_icon.setImageResource(R.drawable.dts_details);
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(HomeData.sDtsLabelId != -1){
                        ActivityLauncher.startListVideoActivity(v.getContext(), HomeData.sDtsLabelId,true);
                    }
                }
            });
            return;
        }

        final int pos = position;
//        holder.itemView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                DetailLabelModel model = mLabelModelList.get(pos);
//
//                switch (model.getType()){
//                    case DetailLabelModel.LABEL_YEAR: //year
//                        break;
//                    case DetailLabelModel.LABEL_AREA:  //area
//                        RequestManager.getInstance().onEvent("6_info", "6_info_btn_label", String.valueOf(mAlbumId), null, null, null, null);
//                        ActivityLauncher.startLabelGridListActivity(v.getContext(), mCateCode, "area=" + model.getOttCateId(), model.getName());
//                        break;
//                    case DetailLabelModel.LABEL_CAT: //cat(genreName)
//                        RequestManager.getInstance().onEvent("6_info", "6_info_btn_label", String.valueOf(mAlbumId), null, null, null, null);
//                        ActivityLauncher.startLabelGridListActivity(v.getContext(), mCateCode, "cat=" + model.getOttCateId(), model.getName());
//                        break;
//                }
//
//            }
//        });
    }

    @Override
    public int getItemViewType(int position) {
        if(mLabelModelList.get(position).getType() == DetailLabelModel.LABEL_VIP){
            return ITEM_VIEW_TYPE_VIP; //vip
        }else if(mLabelModelList.get(position).getType() == DetailLabelModel.LABEL_DTS){
            return ITEM_VIEW_TYPE_DTS; // dts
        } else {
            return ITEM_VIEW_TYPE_NORMAL; // normal
        }
    }

    @Override
    public int getItemCount() {
        if (mLabelModelList != null && mLabelModelList.size() > 3) {
            return 4;
        } else if (mLabelModelList != null && mLabelModelList.size() > 0) {
            return mLabelModelList.size();
        } else {
            return 0;
        }
    }


    public class ViewHolder extends RecyclerView.ViewHolder{

        TextView labelName;

        public ViewHolder(View itemView) {
            super(itemView);
            labelName = (TextView) itemView.findViewById(R.id.label_name);

            if(mOnKeyListener != null){
                itemView.setOnKeyListener(mOnKeyListener);
            }

        }
    }

    public class ViewHolderVip extends RecyclerView.ViewHolder{

        ImageView logo_icon;

        public ViewHolderVip(View itemView) {
            super(itemView);
            logo_icon = (ImageView) itemView.findViewById(R.id.logo);
        }
    }

}
