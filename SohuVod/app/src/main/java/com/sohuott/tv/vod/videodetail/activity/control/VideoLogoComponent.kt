package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView.ScaleType
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.lib_dlna_core.SohuDlnaManger
import com.sh.ott.video.ad.AdTsManger
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.isInvisible
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper
import com.sohuott.tv.vod.videodetail.activity.LogoInfo


/**
 * 角标
 */
class VideoLogoComponent constructor(context: Context) :
    BaseControlComponent(context) {
    private var logoInfo: LogoInfo? = null

    private var logoImg: AppCompatImageView? = null

    private var mRefreshHandler: Handler? = null

    private var enableLogo: Boolean = false

    init {
        hide()
        initHandler()
        logoImg = AppCompatImageView(context)
        logoImg?.scaleType = ScaleType.FIT_XY
        logoImg?.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.icon_cover))
        addView(logoImg)
    }

    override fun onProgressChanged(duration: Long, position: Long) {
        super.onProgressChanged(duration, position)
        if (AdTsManger.getInstants().getCalculateTsTime(position )) {
            hide()
            player?.setScreenAspectRatioType(PlayerConstants.ScreenAspectRatio.MATCH_PARENT)
        } else {
            player?.setScreenAspectRatioType(PlaySettingHelper.getVideoViewLayoutRatioType())
            if (visibility == VISIBLE) return
            if (enableLogo == false) return
            refreshView()
        }
    }

    private fun hide() {
        gone()
    }


    private fun initHandler() {
        mRefreshHandler ?: Handler(Looper.getMainLooper()).also {
            mRefreshHandler = it
        }
    }

    fun setEnableLogo(enable: Boolean) {
        enableLogo = enable
        refreshView()
    }


    private fun refreshView() {
        isInvisible(enableLogo)
    }


    /**
     * 初始化数据时可以知道的信息 更改显示和隐藏
     */
    fun setLogoInfo(logoInfo: LogoInfo?) {
        this.logoInfo = logoInfo
        //dlna 隐藏logo
        if (SohuDlnaManger.getInstance().getIsDlna()) {
            setEnableLogo(false)
            return
        }
        //video info 中的 logoInfo 判断逻辑
        if (logoInfo?.logo == 0) {
            setEnableLogo(false)
            return
        }
        enableLogo = true
        refreshView()
    }

    /**
     * 切换清晰度时候 显示和隐藏
     */
    fun setChangeResolutionEnableLogo(enable: Boolean) {
        setEnableLogo(enable)
    }


    override fun onRenderMeasure(
        videoWidth: Int,
        videoHeight: Int,
        widthSpecSize: Int,
        heightSpecSize: Int,
        measuredWidth: Int,
        measuredHeight: Int
    ) {
        layoutParams.height = FrameLayout.LayoutParams.WRAP_CONTENT
        layoutParams.width = FrameLayout.LayoutParams.WRAP_CONTENT
        val bWidth = widthSpecSize.toFloat()
        val bHeight = heightSpecSize.toFloat()
        var realwidth = measuredWidth.toFloat()
        var realheight = measuredHeight.toFloat()
        val radio: Float = realwidth / realheight
        if (radio >= 16f / 9f) {
            realheight = bWidth / realwidth * realheight
            realwidth = bWidth
        } else {
            realwidth = (bHeight / realheight * realwidth)
            realheight = bHeight
        }
        if (logoInfo == null) {
            logoInfo = LogoInfo()
        }
        var logoW: Int = (realwidth * logoInfo!!.width).toInt()
        var logoH: Int = (realheight * logoInfo!!.height).toInt()
        val isVertical = logoInfo!!.orientation == 1 || realheight > realwidth
        if (isVertical) { //竖屏屏视频
            logoW = (realheight * logoInfo!!.height).toInt()
            logoH = (realwidth * logoInfo!!.width).toInt()
            logoInfo!!.logoSideMargin = 0f
        }
        val imgLayoutParams = logoImg?.layoutParams
        imgLayoutParams?.width = logoW
        imgLayoutParams?.height = logoH

        val marginLayoutParams = (layoutParams as? ViewGroup.MarginLayoutParams)
        val isCropRate = measuredWidth > widthSpecSize
        if (logoInfo!!.logoleft == 4) { // 居左百分比

            if (isCropRate) {
                marginLayoutParams?.leftMargin = ((videoWidth - realwidth) / 2.0).toInt()
                marginLayoutParams?.topMargin = (realheight * logoInfo!!.logoTopMargin).toInt()
            } else {
                marginLayoutParams?.leftMargin =
                    (realwidth * logoInfo!!.logoSideMargin + (bWidth - realwidth) / 2.0).toInt()
                marginLayoutParams?.topMargin =
                    (realheight * logoInfo!!.logoTopMargin + (bHeight - realheight) / 2.0).toInt()
            }
        } else { // 居右
            if (isCropRate) {
                marginLayoutParams?.leftMargin =
                    (bWidth - (bWidth - realwidth) / 2.0 - logoW).toInt()
                marginLayoutParams?.topMargin = (realheight * logoInfo!!.logoTopMargin).toInt()
            } else {
                marginLayoutParams?.leftMargin =
                    (bWidth - ((bWidth - realwidth) / 2.0) - logoW - (realwidth * logoInfo!!.logoSideMargin)).toInt()
                marginLayoutParams?.topMargin =
                    (realheight * logoInfo!!.logoTopMargin + (bHeight - realheight) / 2.0).toInt()
            }
        }
        mRefreshHandler?.post {
            logoImg?.layoutParams = imgLayoutParams
            layoutParams = marginLayoutParams
        }
    }


    override fun detachViewFromParent(child: View?) {
        super.detachViewFromParent(child)
        mRefreshHandler?.removeCallbacksAndMessages(null)
    }
}