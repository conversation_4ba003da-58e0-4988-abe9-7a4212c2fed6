package com.sohuott.tv.vod.videodetail.activity.state

import com.sohu.ott.lib_widget.SoHuVideoProgressBar
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.VideoInfoOnlySeeItem

data class OnlySeeData(
    var isFind: Boolean = false,
    var hasOnlySee: Boolean = false,
    var currentHasSelectOnlySeeMenuItem: Boolean = false,
    var videoInfoOnlySeeItem: MutableList<VideoInfoOnlySeeItem>? = null,
    var mCurrentSelectOnlySeeMenuItem: ScaleContentOnlySeeMenuItem? = null,
    var proSeeList: MutableList<SoHuVideoProgressBar.SecondListBean> = mutableListOf(),
    var onlySeeList: MutableList<ScaleContentOnlySeeMenuItem> = mutableListOf(),
    var onlySeeModel: Boolean = false,
) {

}