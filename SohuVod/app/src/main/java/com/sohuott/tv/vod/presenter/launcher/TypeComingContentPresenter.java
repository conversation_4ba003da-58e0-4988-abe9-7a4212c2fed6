package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.leanback.widget.Presenter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.load.model.LazyHeaders;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.google.gson.Gson;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.BookedRecordResult;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.lb.ScaleConstraintLayout;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;
import com.sohu.lib_utils.StringUtil;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

public class TypeComingContentPresenter extends Presenter {
    private Context mContext;
    private LoginUserInformationHelper mHelper;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    private int mComingAid;
    private boolean mIsLogin;

    private Gson mGson;
    private ContentGroup.DataBean.ContentsBean.AlbumListBean mAlbumListBean;


    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        mHelper = LoginUserInformationHelper.getHelper(mContext);
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_coming_layout, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }

        mIsLogin = mHelper.getIsLogin();
        mGson = new Gson();

        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, final Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;

        vh.mClComing.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
                if (hasFocus) {
                    v.bringToFront();
                    vh.mFocusRoot.setVisibility(View.VISIBLE);
                    vh.mTvTypeThreeName.setVisibility(View.GONE);
                    vh.mFocusPlay.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.showWaveAnimation();
                } else {
                    vh.mFocusRoot.setVisibility(View.GONE);
                    vh.mTvTypeThreeName.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.setVisibility(View.GONE);
                    vh.mFocusPlay.cancelWaveAnimation();
                }
            }
        });

        if (item instanceof ContentGroup.DataBean.ContentsBean.SubjectVideoListBean) {
            final ContentGroup.DataBean.ContentsBean.SubjectVideoListBean subjectVideoListBean = (ContentGroup.DataBean.ContentsBean.SubjectVideoListBean) item;

            GlideUrl glideUrl = new GlideUrl(subjectVideoListBean.picVerUrl, new LazyHeaders.Builder()
                    .addHeader("ImageTag", "Launcher")
                    .build());
            Glide.with(mContext)
                    .load(glideUrl)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeThreePoster);

            vh.mTvTypeThreeName.setText(subjectVideoListBean.name);

            vh.mTvFocusName.setText(subjectVideoListBean.name);

            if (!StringUtil.isEmpty(subjectVideoListBean.parameter)){
                if (mAlbumListBean == null) {
                    mAlbumListBean =
                            mGson.fromJson(subjectVideoListBean.parameter,
                                    ContentGroup.DataBean.ContentsBean.AlbumListBean.class);
                }
                if (mAlbumListBean != null){
                    if (StringUtil.isEmpty(mAlbumListBean.tvComment)) {
                        vh.mTvFocusDesc.setVisibility(View.GONE);
                    } else {
                        vh.mTvFocusDesc.setText(mAlbumListBean.tvComment);
                    }
                }
            }

            vh.mTvDate.setText(subjectVideoListBean.timeDesc);
            LibDeprecatedLogger.d("onBindViewHolder: isReserve " + subjectVideoListBean.isReserve);


            if (subjectVideoListBean.isReserve == 0) { //未预约曝光
                vh.mClTypeComingReserve.setBackgroundResource(R.drawable.coming_btn_selector);

                RequestManager.getInstance().onAllEvent(new EventInfo(10144, "imp"),
                        subjectVideoListBean.pathInfo,
                        null, subjectVideoListBean.memoInfo);
            } else {
                vh.mClTypeComingReserve.setBackgroundResource(R.drawable.coming_reserved_btn_selector);
                RequestManager.getInstance().onAllEvent(new EventInfo(10142, "imp"),
                        subjectVideoListBean.pathInfo,
                        null, subjectVideoListBean.memoInfo);
            }

            if (mIsLogin){
                vh.mTvReserve.setText(subjectVideoListBean.isReserve == 0? "预约": "已预约");
            } else {
                vh.mTvReserve.setText("预约");
                vh.mClTypeComingReserve.setBackgroundResource(R.drawable.coming_btn_selector);
            }

            vh.mClTypeComingReserve.setOnClickListener(v -> {
                if (!mIsLogin) {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10136, "clk"), subjectVideoListBean.pathInfo, null, null);

                    ActivityLauncher.startLoginActivity(mContext, Constant.LAUNCHER_SOURCE, Integer.parseInt(subjectVideoListBean.pathInfo.get("pageId").toString()));
                } else {

                    mAlbumListBean =
                            mGson.fromJson(subjectVideoListBean.parameter,
                                    ContentGroup.DataBean.ContentsBean.AlbumListBean.class);
                    if (mAlbumListBean == null) return;
                    mComingAid = mAlbumListBean.albumId;

                    if (subjectVideoListBean.isReserve == 0) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10145, "clk_vv"),
                                subjectVideoListBean.pathInfo,
                                null, subjectVideoListBean.memoInfo);
                        NetworkApi.addNewBookedVideo(mHelper.getLoginPassport(), String.valueOf(mComingAid), mHelper.getLoginToken(),new Observer<BookedRecordResult>() {
                            @Override
                            public void onSubscribe(Disposable d) {

                            }

                            @Override
                            public void onNext(BookedRecordResult value) {
                                LibDeprecatedLogger.d("addNewBookedRecord(): onNext().");
                                if (value != null && value.getData() != null && value.getData().getOperResult() != null
                                        && value.getData().getOperResult().size() > 0) {
                                    BookedRecordResult.DataBean.OperResultBean result = value.getData().getOperResult().get(0);
                                    if(result != null && result.isResult()){
                                        ToastUtils.showToast(mContext, "预约成功");
                                        vh.mClTypeComingReserve.setBackgroundResource(R.drawable.coming_reserved_btn_selector);
                                        vh.mTvReserve.setText("已预约");
                                        subjectVideoListBean.isReserve = 1;
                                    } else {
                                        ToastUtils.showToast(mContext, "预约失败");
                                    }
                                }
                            }

                            @Override
                            public void onError(Throwable e) {
                                LibDeprecatedLogger.e("addNewBookedRecord(): onError()--" + e.getMessage());
                                ToastUtils.showToast(mContext, "预约失败");
                            }

                            @Override
                            public void onComplete() {
                                LibDeprecatedLogger.d("addNewBookedRecord(): onComplete().");
                            }
                        });
                    } else {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10143, "clk_vv"),
                                subjectVideoListBean.pathInfo,
                                null, subjectVideoListBean.memoInfo);
                        NetworkApi.cancelBookedVideoById(mHelper.getLoginPassport(), String.valueOf(mComingAid), mHelper.getLoginToken(),new Observer<BookedRecordResult>() {
                            @Override
                            public void onSubscribe(Disposable d) {

                            }

                            @Override
                            public void onNext(BookedRecordResult value) {
                                LibDeprecatedLogger.d("cancelBookedVideoById(): onNext().");
                                if (value != null && value.getData() != null && value.getData().getOperResult() != null
                                        && value.getData().getOperResult().size() > 0) {
                                    BookedRecordResult.DataBean.OperResultBean result = value.getData().getOperResult().get(0);
                                    if(result != null && result.isResult()){
                                        ToastUtils.showToast(mContext, "已取消预约");
                                        vh.mTvReserve.setText("预约");
                                        subjectVideoListBean.isReserve = 0;
                                        vh.mClTypeComingReserve.setBackgroundResource(R.drawable.coming_btn_selector);

                                    }else{
                                        ToastUtils.showToast(mContext, "取消预约失败");

                                    }
                                }
                            }

                            @Override
                            public void onError(Throwable e) {
                                ToastUtils.showToast(mContext, "取消预约失败");
                                LibDeprecatedLogger.e("cancelBookedRecord(): onError()--" + e.getMessage());
                            }

                            @Override
                            public void onComplete() {
                                LibDeprecatedLogger.d("cancelBookedRecord(): onComplete().");
                            }
                        });
                    }
                }
            });

            vh.mClComing.setOnClickListener(v -> {
                mAlbumListBean =
                        mGson.fromJson(subjectVideoListBean.parameter,
                                ContentGroup.DataBean.ContentsBean.AlbumListBean.class);
                if (mAlbumListBean == null) return;
                ActivityLauncher.startVideoDetailActivity(mContext, mAlbumListBean.albumId, 0, Constant.HOME_SOURCE);
            });
        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final CornerTagImageView mIvTypeThreePoster;
        private final TextView mTvTypeThreeName, mTvFocusName, mTvFocusDesc;
        private LinearLayout mFocusRoot;
        private RippleDiffuse mFocusPlay;
        private ScaleConstraintLayout mClTypeComingReserve;
        private ConstraintLayout mClComing;
        private TextView mTvReserve;
        private TextView mTvDate;

        public ViewHolder(View view) {
            super(view);
            mIvTypeThreePoster = (CornerTagImageView) view.findViewById(R.id.iv_type_three_poster);
            mTvTypeThreeName = (TextView) view.findViewById(R.id.tv_type_three_name);
            mClTypeComingReserve = (ScaleConstraintLayout) view.findViewById(R.id.btn_type_coming_reserve);
            mClComing = (ConstraintLayout) view.findViewById(R.id.cl_coming);
            mTvReserve = (TextView) view.findViewById(R.id.tv_reserve);
            mTvDate = (TextView) view.findViewById(R.id.tv_coming_date);
            mFocusRoot = (LinearLayout) view.findViewById(R.id.type_coming_focus_root);
            mTvFocusName = (TextView) view.findViewById(R.id.type_coming_focus_name);
            mTvFocusDesc = (TextView) view.findViewById(R.id.type_coming_focus_desc);
            mFocusPlay = (RippleDiffuse) view.findViewById(R.id.type_coming_focus_play);
        }
    }
}

