package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.FontUtils;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.lib.model.UserPointInfo;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.List;

/**
 * Adapter used to RecyclerView in PointFragment
 * <p>
 * Created by wenjingbian on 2017/12/26.
 */

public class PointAdapter extends RecyclerView.Adapter<PointAdapter.PointViewHolder> {

    private static final String TAG = PointAdapter.class.getSimpleName();

    /**
     * Interface to implement callback when clicked item
     */
    public interface IOnClickItemListener {
        /**
         * automatically call when clicked item
         *
         * @param dataBean data bean bind on the clicked item
         */
        void onItemClick(UserPointInfo.DataBean dataBean, int postion);
    }

    private static final int DEFAULT_ITEM_COUNT = 3;

    private Context mContext;
    private FocusBorderView mFocusView;
    private CustomLinearRecyclerView mRecyclerView;

    private IOnClickItemListener mListener;

    private List<UserPointInfo.DataBean> mDataSource;

    public PointAdapter(Context context, CustomLinearRecyclerView recyclerView) {
        this.mContext = context;
        this.mRecyclerView = recyclerView;
    }

    @Override
    public PointViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.layout_point_group_two, parent, false);
        PointViewHolder viewHolder = new PointViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(PointViewHolder holder, int position) {
        //bind default view when data source is unavailable or the certain data is unavailable
        if (mDataSource == null || mDataSource.size() <= 0) {
            holder.tv_default.setVisibility(View.VISIBLE);
            holder.layout_ctn.setVisibility(View.GONE);
            return;
        }

        UserPointInfo.DataBean data = mDataSource.get(position);
        if (data == null) {
            holder.tv_default.setVisibility(View.VISIBLE);
            holder.layout_ctn.setVisibility(View.GONE);
            return;
        }

        //bind data for normal view
        holder.tv_default.setVisibility(View.GONE);
        holder.layout_ctn.setVisibility(View.VISIBLE);
        holder.tv_point.setText(String.valueOf(data.getCurrentScore()));
        FontUtils.setTypeface(mContext, holder.tv_point);
        AppLogger.d(TAG, "data.getCurrentValue() ? " + data.getCurrentValue());
        if (!StringUtil.isEmpty(data.getCurrentValue())) {
            holder.tv_count.setText(data.getCurrentValue());
            FontUtils.setTypeface(mContext, holder.tv_count);
        }
        AppLogger.d(TAG, "data.getTaskName() ? " + data.getTaskName());
        if (!StringUtil.isEmpty(data.getTaskName())) {
            holder.tv_title.setText(data.getTaskName());
        }
        AppLogger.d(TAG, "data.getPicUrl() ? " + data.getPicUrl());
        if(!StringUtil.isEmpty(data.getPicUrl())){
            holder.giv_bg.setImageRes(data.getPicUrl());
        }
    }

    @Override
    public int getItemCount() {
        return mDataSource != null ? mDataSource.size() : DEFAULT_ITEM_COUNT;
    }

    /**
     * Set data source to PointAdapter
     *
     * @param dataSource data list
     */
    public void setDataSource(List<UserPointInfo.DataBean> dataSource) {
        this.mDataSource = dataSource;
    }

    /**
     * Set focus view to items
     *
     * @param focusView instance of FocusBorderView to display
     */
    public void setFocusView(FocusBorderView focusView) {
        this.mFocusView = focusView;
    }

    /**
     * Set IOnClickItemListener to call when clicked item view
     *
     * @param listener instance of IOnClickItemListener who implemented task
     */
    public void setIOnClickItemListener(IOnClickItemListener listener) {
        this.mListener = listener;
    }

    /**
     * Request focus on the pointed view
     *
     * @param position view's position you want to set focus
     */
    public void requestFocusAtPos(int position) {
        if (mRecyclerView != null && mRecyclerView.findViewHolderForAdapterPosition(position) != null
                && mRecyclerView.findViewHolderForAdapterPosition(position).itemView != null) {
            mRecyclerView.findViewHolderForAdapterPosition(position).itemView.requestFocus();
        }
    }

    /**
     * Release all resources when adapter was disabled.
     */
    public void releaseAll() {
        mListener = null;
        if (mDataSource != null) {
            mDataSource.clear();
            mDataSource = null;
        }
    }

    /**
     * Custom ViewHolder
     */
    class PointViewHolder extends RecyclerView.ViewHolder {

        RelativeLayout layout_ctn;
        TextView tv_point, tv_count, tv_title, tv_default;
        GlideImageView giv_bg;

        public PointViewHolder(View itemView) {
            super(itemView);
            layout_ctn = (RelativeLayout) itemView.findViewById(R.id.layout_point_group_two);
            giv_bg = (GlideImageView) itemView.findViewById(R.id.giv_group_two);
            tv_default = (TextView) itemView.findViewById(R.id.tv_group_two_default);
            tv_title = (TextView) itemView.findViewById(R.id.tv_group_two_title);
            tv_point = (TextView) itemView.findViewById(R.id.tv_group_two_point);
            tv_count = (TextView) itemView.findViewById(R.id.tv_group_two_count);

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    int position = getAdapterPosition();
                    if (position == 0 && event.getAction() == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                        //pressed KEYCODE_DPAD_LEFT on the first item
                        ((ListUserRelatedActivity) mContext).focusLeftItem(ListUserRelatedActivity.LIST_INDEX_POINT);
                        return true;
                    } else if ((position == getItemCount() - 1) && event.getAction() == KeyEvent.ACTION_DOWN
                            && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                        //pressed KEYCODE_DPAD_RIGHT on the last item
                        v.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_x));
                        return true;
                    }
                    return false;
                }
            });
//            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//                @Override
//                public void onFocusChange(View v, boolean hasFocus) {
//                    if (hasFocus) { //Set focus view without scaled animation
//                        if (mFocusView != null && mRecyclerView != null
//                                && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
//                            mFocusView.setFocusView(v);
//                        }
//                    } else {
//                        if (mFocusView != null) {
//                            mFocusView.setUnFocusView(v);
//                        }
//                    }
//                }
//            });

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mDataSource == null || mDataSource.size() <= 0) { //Unlogin status
                        ActivityLauncher.startLoginActivity(mContext);
                        RequestManager.getInstance().onAllEvent(new EventInfo(10221, "clk"), null, null, null);
                    } else { // Login status
                        if (mListener != null) {
                            mListener.onItemClick(mDataSource.get(getAdapterPosition()), getAdapterPosition());
                        }
                    }
                }
            });
        }
    }
}
