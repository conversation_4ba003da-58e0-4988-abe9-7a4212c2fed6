package com.sohuott.tv.vod.search

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.leanback.widget.Presenter
import com.bumptech.glide.Glide
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.load.model.LazyHeaders
import com.bumptech.glide.request.RequestOptions
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.databinding.SearchResultItemBinding
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.model.SearchResult
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper
import java.lang.ref.WeakReference

class SearchResultPresenterKt: Presenter() {
    private var context: WeakReference<Context>? = null
    private var itemFocusHelper: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null


    override fun onCreateViewHolder(parent: ViewGroup?): ViewHolder {
        context = WeakReference(parent?.context)
        val binding = SearchResultItemBinding.inflate(LayoutInflater.from(parent?.context), parent, false)
        itemFocusHelper = itemFocusHelper ?: MyFocusHighlightHelper.BrowseItemFocusHighlight(
            FocusHighlight.ZOOM_FACTOR_SMALL,
            false)
        return SearchResultViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder?, item: Any?) {
        val vh = viewHolder as SearchResultViewHolder
        if (item is SearchResult.Data.SearchItem) {
            val glideUrl = GlideUrl(item.tvVerPic, LazyHeaders.Builder()
                .addHeader("ImageTag", "SearchResult")
                .build())
            Glide.with(vh.view)
                .load(glideUrl)
                .placeholder(R.drawable.bg_launcher_poster)
                .apply(RequestOptions.bitmapTransform(RoundCorner(leftTop = 5f, leftBottom = 5f)))
                .into(vh.binding.poster)

            vh.binding.focusPlay.cancelWaveAnimation()
            vh.binding.focusPlay.gone()

            vh.binding.poster.setCornerTypeWithType(item.tvIsFee,
                item.tvIsEarly,
                item.useTicket,
                item.paySeparate,
                item.cornerType
            )
            vh.binding.title.text = item.tvName
            vh.binding.line1.text = "${getVideoType(item.cateCode)}"

            vh.binding.hint.text = Util.getHint(item.cateCode, item.tvSets, item.latestVideoCount.toInt(), item.showDate)
            if (item.tvYear != 0) {
                vh.binding.line1.append(" | ${item.tvYear}")
            }

            if (item.genreName.isNotEmpty() && item.genreName.split(",")[0].isNotEmpty()) {
                vh.binding.line1.append(" | ${item.genreName.split(",")[0]}")
            }

            if(item.act?.equals("null") == false) {
                vh.binding.line2.text = "主演: ${item.act}"
            }

            vh.binding.root.setOnFocusChangeListener { v, hasFocus ->
                itemFocusHelper?.onItemFocused(v, hasFocus)

                if (hasFocus) {
                    vh.binding.focusPlay.showWaveAnimation()
                    vh.binding.focusPlay.visible()
                } else {
                    vh.binding.focusPlay.cancelWaveAnimation()
                    vh.binding.focusPlay.gone()
                }
            }
        }
    }

    private fun getVideoType(cateCode: Int): String {
        //100：电影；101：电视剧；106：综艺；107：纪录片；115：动漫；10001：美剧；
        return when(cateCode) {
            100 -> "电影"
            101 -> "电视剧"
            106 -> "综艺"
            107 -> "纪录片"
            115 -> "动漫"
            10001 -> "美剧"
            119 -> "课堂"
            else -> ""
        }
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder?) {
    }

    class SearchResultViewHolder(val binding: SearchResultItemBinding): ViewHolder(binding.root)
}