package com.sohuott.tv.vod.ui;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.lib.model.HotSearchNew;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.SearchUtil;
import com.sohuott.tv.vod.widget.GlideImageView;

/**
 * Created by fenglei on 17-6-30.
 */

public class SearchActorView extends RelativeLayout implements View.OnClickListener, View.OnFocusChangeListener {

    private GlideImageView simpleDraweeView;
    private TextView actorTV;
    private ImageView focusedIconIV;
    private int childIndex;

    public SearchActorView(Context context) {
        super(context);
        initUI(context);
    }

    public SearchActorView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initUI(context);
    }

    public SearchActorView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initUI(context);
    }

    private void initUI(Context context) {
        LayoutInflater.from(context).inflate(R.layout.search_no_input_adapter_actor_layout_item, this, true);
        simpleDraweeView = (GlideImageView) findViewById(R.id.simple_drawee_view);
        focusedIconIV = (ImageView) findViewById(R.id.serach_voice_focused_icon_iv);
        actorTV = (TextView) findViewById(R.id.hot_search_actor_name_tv);
        setFocusable(true);
        setOnClickListener(this);
        setOnFocusChangeListener(this);
    }

    public void setIndex(int index) {
        childIndex = index;
    }

    public void setUI(HotSearchNew.DataBean dataBean) {
        if(dataBean == null) {
            return;
        }
        setTag(dataBean);
        simpleDraweeView.setCircleImageRes(Uri.parse(dataBean.getPic()),
                getResources().getDrawable(R.drawable.detail_default_avatar),
                getResources().getDrawable(R.drawable.detail_default_avatar));
        SearchUtil.showSearchTitle(dataBean, actorTV);
    }

    @Override
    public void onClick(View v) {
        try {
            HotSearchNew.DataBean dataBean = (HotSearchNew.DataBean)v.getTag();
            SearchUtil.jumpSearch(v.getContext(), dataBean);
//            SearchUtil.saveSearchHistory(v.getContext(), dataBean);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT && childIndex == 0) {
            return ((SearchInputActivity)getContext()).onPressLeftKeyRequestFocus(this);
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if(hasFocus) {
            focusedIconIV.setVisibility(View.VISIBLE);
            setViewOnFocusAnim();
            setTVOnFocus(actorTV);
        } else {
            focusedIconIV.setVisibility(View.GONE);
            setViewUnFocusAnim();
            setTVUnFocus(actorTV);
        }
    }

    private void setViewOnFocusAnim() {
        ObjectAnimator scaleXAnim = ObjectAnimator.ofFloat(this, "scaleX",
                1f, FocusUtil.HOME_SCALE);
        ObjectAnimator scaleYAnim = ObjectAnimator.ofFloat(this, "scaleY",
                1f, FocusUtil.HOME_SCALE);
        AnimatorSet animSet = new AnimatorSet();
        animSet.setDuration(FocusUtil.FOCUS_ANIM_TIME);
        animSet.playTogether(scaleXAnim, scaleYAnim);
        animSet.start();
    }

    private void setViewUnFocusAnim() {
        ObjectAnimator scaleXAnim = ObjectAnimator.ofFloat(this, "scaleX",
                getScaleX(), 1f);
        ObjectAnimator scaleYAnim = ObjectAnimator.ofFloat(this, "scaleY",
                getScaleY(), 1f);
        AnimatorSet animSet = new AnimatorSet();
        animSet.setDuration(FocusUtil.FOCUS_ANIM_TIME);
        animSet.playTogether(scaleXAnim, scaleYAnim);
        animSet.start();
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

}
