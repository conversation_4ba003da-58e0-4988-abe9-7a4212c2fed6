package com.sohuott.tv.vod.utils;

import android.content.Context;

import com.sohuott.tv.vod.lib.service.PlayHistoryService;

public class SyncHistoryAndCollectionUtil {
    /**
     * 登录成功后，将本地播放记录和收藏记录同步到云端
     */
    public static void uploadLocalHistoryAndCollection(Context context) {
        PlayHistoryService playHistoryService = new PlayHistoryService(context);
        playHistoryService.uploadLocalPlayHistory2Cloud(false);
        playHistoryService.uploadLocalPlayHistory2Cloud(true);
        CollectionRecordHelper collectionRecordHelper = new CollectionRecordHelper(context, false);
        collectionRecordHelper.uploadLocalCollection2Cloud(false);
        collectionRecordHelper.uploadLocalCollection2Cloud(true);
    }

    /**
     * 退出登录或者注销账号，清除本地历史记录和收藏
     * @param context
     */
    public static void clearLocalHistoryAndCollection(Context context){
        PlayHistoryService playHistoryService = new PlayHistoryService(context);
        CollectionRecordHelper collectionRecordHelper = new CollectionRecordHelper(context, false);
        playHistoryService.deleteLocalPlayHistory(false, null);
        playHistoryService.deleteLocalPlayHistory(true, null);
        collectionRecordHelper.deleteAllLocalRecord(false);
        collectionRecordHelper.deleteAllLocalRecord(true);
    }
}
