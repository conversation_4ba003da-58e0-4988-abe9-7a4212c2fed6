package com.sohuott.tv.vod.fragment;

import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.View;

import com.sohuott.tv.vod.view.HomeBaseRecyclerView;

/**
 * Created by yizhang210244 on 2018/1/4.
 */

public class HomeWithRecyclerViewFragment extends HomeBaseFragment{
    protected HomeBaseRecyclerView mRecyclerView;

    public void scrollToLeftStart(){
        //自己重写实现
    }
    public void scrollToRightEnd(){
        //自己重写实现
    }

    public void rightEndViewFocus(){
        //自己重写实现
    }

    public void leftStartViewFocus(){
        //自己重写实现
    }

    public void attachImages(){

    }

    public void detachImages(){

    }

    public RecyclerView getRecyclerView(){
        return mRecyclerView;
    }

    public void horScroll(KeyEvent event){

    }

    public boolean getIsFocusUpPosition(){
        return false;
    }

    public void setIsFocusUpPosition(boolean isFocusUpPosition){

    }
}
