package com.sohuott.tv.vod.view;

import android.content.Context;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/4/25.
 */


public class CustomQFLinearLayoutManager extends CustomLinearLayoutManager {

    public CustomQFLinearLayoutManager(Context context) {
        super(context);
    }

    /*
    * 由外部跳入RecycleView时防止不滑动
    * By zxf 2018.05.02
    * */
    private boolean mJumpOutRecycleView = true;
    public void SetJumpOutRecycleView(Boolean flag) {
        this.mJumpOutRecycleView = flag;
    }

    @Override
    public int scrollVerticallyBy(int dy, RecyclerView.Recycler recycler, RecyclerView.State state) {

        if (mJumpOutRecycleView == true) {
            mJumpOutRecycleView = false;
            return 0;
        }
        else
            return super.scrollVerticallyBy(dy, recycler, state);
    }

    /****
     * 防止进行数据移除和数据增加时，Adapter中的数据和移除的数据不一致导致崩溃
     * @param recycler
     * @param state
     */
    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        try {
            //try catch一下
            super.onLayoutChildren( recycler, state );
        } catch (IndexOutOfBoundsException e) {
            e.printStackTrace();
        }

    }

}
