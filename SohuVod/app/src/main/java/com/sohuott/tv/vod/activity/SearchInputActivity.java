package com.sohuott.tv.vod.activity;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.SearchNoInputAdapter;
import com.sohuott.tv.vod.adapter.SearchSuggestAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.DaoSessionInstance;
import com.sohuott.tv.vod.lib.db.greendao.SearchHistory;
import com.sohuott.tv.vod.lib.db.greendao.SearchHistoryDao;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AuditDenyAids;
import com.sohuott.tv.vod.lib.model.HotSearchNew;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.HotSearchContract;
import com.sohuott.tv.vod.presenter.HotSearchPresenter;
import com.sohuott.tv.vod.search.TNinePopLayoutListener;
import com.sohuott.tv.vod.ui.SearchActorView;
import com.sohuott.tv.vod.ui.SearchVoiceDialog;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.HotSearchItemView;
import com.sohuott.tv.vod.view.HotSearchLayout;
import com.sohuott.tv.vod.view.SearchFullKeyboardLayout;
import com.sohuott.tv.vod.view.SearchInputLayoutManager;
import com.sohuott.tv.vod.view.SearchInputRecyclerView;
import com.sohuott.tv.vod.view.TNineKeyboardItemView2;
import com.sohuott.tv.vod.view.TNineKeyboardLayout;
import com.sohuott.tv.vod.view.TNineKeyboardPopLayout;
import com.sohuott.tv.vod.view.TNineKeyboardPopView;

import java.lang.ref.WeakReference;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by fenglei on 17-6-14.
 */

public class SearchInputActivity extends BaseActivity implements View.OnClickListener,
        View.OnFocusChangeListener, HotSearchContract.HotSearchView {

    private static final String TAG = SearchInputActivity.class.getSimpleName();

    protected static final int MAX_SEARCH_HISTORY = 30;

    public interface OnClickTNineKeyboardListener {
        void onClickTNineKeyboard(String content);
    }

    public interface OnClickFullKeyboardListener {
        void onClickFullKeyboard(String content);
    }



    private LoadingView loadingView;
    private ViewGroup searchNoResultLayout;
    private ViewGroup searchLeftLayout;
    private ImageView searchVoiceFocusedIV;
    private ImageView backLLFocusedIV;
    private ImageView clearLLFocusedIV;
    private ImageView searchVoiceIV;
    private TextView searchContentTV;
    private TNineKeyboardLayout tNineKeyboardLayout;
    private SearchFullKeyboardLayout searchFullKeyboardLayout;
    private TextView backspaceLL;
    private TextView clearLL;
    private Button tNineKeyboardBtn;
    private Button fullKeyboardBtn;
    private TNineKeyboardPopLayout tNineKeyboardPopLayout;
    private SearchInputRecyclerView searchNoInputRecyclerView;
    private SearchInputLayoutManager searchLinearLayoutManager;
    private SearchNoInputAdapter searchNoInputAdapter;
    private SearchSuggestAdapter searchSuggestAdapter;
    private SearchNoInputAdapter.SearchNoInputItemDecoration searchNoInputItemDecoration
            = new SearchNoInputAdapter.SearchNoInputItemDecoration();
    private SearchSuggestAdapter.SearchSuggestItemDecoration searchSuggestItemDecoration
            = new SearchSuggestAdapter.SearchSuggestItemDecoration();

    protected HotSearchContract.Presenter hotSearchPresenter;

    private SearchVoiceDialog searchVoiceDialog;
    private FocusBorderView focusBorderView;
    private FocusUtil.FocusChangeNoAnimListener focusChangeNoAnimListener;

    private boolean isOnCreate = true;

    protected void setCustomUI(){
        setContentView(R.layout.activity_new_search);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isOnCreate = true;
//        setContentView(R.layout.activity_new_search);
        setCustomUI();
        initUI();
        getHotSearch();
    }

    private void initUI() {
        setCustomPageName();
        loadingView = (LoadingView) findViewById(R.id.loadingView);
        searchNoResultLayout = (ViewGroup) findViewById(R.id.searchNoResultLayout);
        focusBorderView = (FocusBorderView) findViewById(R.id.fragment_item_focus);
        focusChangeNoAnimListener = new FocusUtil.FocusChangeNoAnimListener(focusBorderView);
        searchVoiceFocusedIV = (ImageView) findViewById(R.id.serach_voice_focused_icon_iv);
        searchVoiceIV = (ImageView) findViewById(R.id.serach_voice_iv);
        tNineKeyboardLayout.setPageName(mPageName);
        searchFullKeyboardLayout = (SearchFullKeyboardLayout) findViewById(R.id.full_keyboard_layout);
//        searchFullKeyboardLayout.setPageName(mPageName);
        tNineKeyboardBtn.setSelected(true);
        fullKeyboardBtn.setSelected(false);
        setTnineAndFullBtnFocusChangeListner();
        tNineKeyboardPopLayout.setPageName(mPageName);
        searchVoiceIV.setOnClickListener(this);
        searchVoiceIV.setOnFocusChangeListener(this);
        backspaceLL.setOnFocusChangeListener(this);
        clearLL.setOnFocusChangeListener(this);
        tNineKeyboardLayout.setFocusBorderView(focusBorderView);
//        searchFullKeyboardLayout.setFocusBorderView(focusBorderView);
        searchContentTV.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                LibDeprecatedLogger.d("beforeTextChanged, s = " + s);
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                LibDeprecatedLogger.d("onTextChanged, s = " + s);
            }

            @Override
            public void afterTextChanged(Editable s) {
                LibDeprecatedLogger.d("afterTextChanged, s = " + s);
                if(TextUtils.isEmpty(s.toString())) {
                    getHotSearch();
                } else {
                    getSearchResult(s.toString());
                }
            }
        });
//        searchFullKeyboardLayout.setOnClickFullKeyboardListener(new OnClickFullKeyboardListener() {
//            @Override
//            public void onClickFullKeyboard(String content) {
//                if (!TextUtils.isEmpty(content)) {
//                    searchContentTV.setText(searchContentTV.getText().toString() + content);
//                }
//            }
//        });
//        tNineKeyboardLayout.setOnClickTNineKeyboardListener(new OnClickTNineKeyboardListener() {
//            @Override
//            public void onClickTNineKeyboard(String content) {
//                if (!TextUtils.isEmpty(content)) {//"5jkl"
//                    tNineKeyboardPopLayout.setVisibility(View.VISIBLE);
//                    tNineKeyboardPopLayout.show(searchContentTV.getText().toString(), content);
//                }
//            }
//        });
        tNineKeyboardPopLayout.settTNinePopLayoutListener(new TNinePopLayoutListener() {
            @Override
            public void onPressBack(String content) {//'5', 'j'...
                if (tNineKeyboardPopLayout.getVisibility() == View.VISIBLE) {
                    tNineKeyboardPopLayout.setVisibility(View.GONE);
                    tNineKeyboardLayout.setFocusView(content);
                }
            }

            @Override
            public void onPressButton(String content) {//'5', 'j'...
                if (tNineKeyboardPopLayout.getVisibility() == View.VISIBLE) {
                    tNineKeyboardPopLayout.setContentTV(content);
                    tNineKeyboardPopLayout.setVisibility(View.GONE);
                    searchContentTV.setText(searchContentTV.getText().toString() + content);
                    tNineKeyboardLayout.setFocusView(content);
                }
            }
        });
        backspaceLL.setOnClickListener(this);
        clearLL.setOnClickListener(this);
        tNineKeyboardBtn.setOnClickListener(this);
        fullKeyboardBtn.setOnClickListener(this);
        tNineKeyboardLayout.setInitFocus();
        hotSearchPresenter = new HotSearchPresenter(this);
        searchLinearLayoutManager = new SearchInputLayoutManager(this);
        searchLinearLayoutManager.setLayoutSpace((int) getResources().getDimension(R.dimen.y100));
        searchNoInputRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        searchNoInputRecyclerView.setHasFixedSize(true);
        searchLinearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        searchNoInputRecyclerView.setLayoutManager(searchLinearLayoutManager);

        RequestManager.getInstance().onSearchExposureEvent(mPageName);
        //setPageName("6_search");
    }

    protected void setCustomPageName(){
        setPageName("6_search");
    }

    public void presenterGetHotSearch(){
        hotSearchPresenter.getHotSearch();
    }

    private void getHotSearch() {
        if(loadingView.getVisibility() != View.VISIBLE){
            searchNoInputRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
            searchNoInputRecyclerView.setFocusable(false);
            loadingView.show();
        }
        //hotSearchPresenter.getHotSearch();
        presenterGetHotSearch();
    }

    public void presenterGetSearchResult(String keyword){
        hotSearchPresenter.getSuggestSearch(keyword);
    }

    private void getSearchResult(String keyword) {
        if(loadingView.getVisibility() != View.VISIBLE){
            searchNoInputRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
            searchNoInputRecyclerView.setFocusable(false);
            loadingView.show();
        }
        presenterGetSearchResult(keyword);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(!isOnCreate) {
            if(TextUtils.isEmpty(searchContentTV.getText().toString())) {
                if(searchNoInputRecyclerView.getAdapter() != null
                        && searchNoInputRecyclerView.getAdapter().equals(searchNoInputAdapter)) {
                    searchNoInputAdapter.setSearchHistoryList(getSearchHistory());
                    searchNoInputRecyclerView.post(new InnerRunnable(this));
                }
            }
        }
        isOnCreate = false;
    }

    private static class InnerRunnable implements Runnable {
        private WeakReference<SearchInputActivity> mWrapper;
        InnerRunnable(SearchInputActivity searchInputActivity){
            mWrapper = new WeakReference<>(searchInputActivity);
        }
        @Override
        public void run() {
            SearchInputActivity searchInputActivity = mWrapper.get();
            if (searchInputActivity != null){
                final int focusIndex = searchInputActivity.getFocusIndex();
                final int focusPosition = searchInputActivity.getFocusPosition();
                LibDeprecatedLogger.d("focusIndex = " + focusIndex + ", focusPosition = " + focusPosition);
                LibDeprecatedLogger.d("start to recovery focus");
                searchInputActivity.recoverFocus(focusPosition, focusIndex);
            }
        }
    }


    private int getFocusPosition() {
        int position = -1;
        final View focusedView = searchNoInputRecyclerView.findFocus();
        if(focusedView != null) {
            View view = searchNoInputRecyclerView.findContainingItemView(focusedView);
            if(view != null) {
                position = ((RecyclerView.LayoutParams)view.getLayoutParams()).getViewLayoutPosition();
            }
        }
        return position;
    }

    private int getFocusIndex() {
        int index = -1;
        final View focusedView = searchNoInputRecyclerView.findFocus();
        if(focusedView != null && focusedView.getParent() != null) {
            ViewGroup parent;
            View childView = focusedView;
            if(focusedView instanceof SearchActorView
                    || focusedView instanceof HotSearchItemView) {
                childView = (View)focusedView.getParent();
                parent = (ViewGroup) focusedView.getParent().getParent();
            } else {
                parent = (ViewGroup) focusedView.getParent();
            }
            for(int i = 0; i < parent.getChildCount(); i++) {
                if(childView.equals(parent.getChildAt(i))) {
                    index = i;
                    break;
                }
            }
        }
        return index;
    }

    private void recoverFocus(int focusPosition, int focusIndex) {
        if(focusPosition != -1 && focusIndex != -1) {
            try {
                RecyclerView.ViewHolder viewHolder = searchNoInputRecyclerView
                        .findViewHolderForLayoutPosition(focusPosition);
                if(viewHolder != null && viewHolder.itemView != null) {
                    View view = viewHolder.itemView;
                    if(view instanceof HotSearchLayout) {
                        ViewGroup viewGroup = (ViewGroup) ((ViewGroup) view).getChildAt(focusIndex);
                        for(int i = 0; i < viewGroup.getChildCount(); i++) {
                            if(viewGroup.getChildAt(i).getVisibility() == View.VISIBLE) {
                                viewGroup.getChildAt(i).requestFocus();
                                break;
                            }
                        }
                    } else {
                        View focusedView = ((ViewGroup) view).getChildAt(focusIndex);
                        focusedView.requestFocus();
                    }
                    if(searchNoInputRecyclerView != null){
                        RecyclerView.Adapter adapter = searchNoInputRecyclerView.getAdapter();
                        if(adapter != null && adapter.getItemCount() > 0  && focusPosition < adapter.getItemCount() -1 ){
                            searchNoInputRecyclerView.smoothScrollToPosition(focusPosition + 1);
                        }else {
                            searchNoInputRecyclerView.smoothScrollToPosition(focusPosition);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onClick(View v) {
        if (v.equals(backspaceLL)) {
            handleClickBackspaceLL();
            RequestManager.getInstance().onClickSearchInputBacksapce(mPageName);
        } else if (v.equals(clearLL)) {
            searchContentTV.setText("");
            RequestManager.getInstance().onClickSearchInputClear(mPageName);
        } else if (v.equals(tNineKeyboardBtn)) {
            searchFullKeyboardLayout.setVisibility(View.GONE);
            tNineKeyboardLayout.setVisibility(View.VISIBLE);
            fullKeyboardBtn.setSelected(false);
            tNineKeyboardBtn.setSelected(true);
            RequestManager.getInstance().onClickSearchInputT9(mPageName);
        } else if (v.equals(fullKeyboardBtn)) {
            tNineKeyboardLayout.setVisibility(View.GONE);
            searchFullKeyboardLayout.setVisibility(View.VISIBLE);
            fullKeyboardBtn.setSelected(true);
            tNineKeyboardBtn.setSelected(false);
            RequestManager.getInstance().onClickSearchInputFullkeyboard(mPageName);
        } else if(v.equals(searchVoiceIV)) {
            //针对xiaomistore渠道做特殊处理
            if(Util.getPartnerNo(this).equals(Constant.PARTNER_NO_XIAOMI_STORE_CHANNEL)){
                ToastUtils.showToast(this, "敬请期待");
                return;
            }
            if(searchVoiceDialog == null) {
                searchVoiceDialog = new SearchVoiceDialog(this);
            }
            if(!searchVoiceDialog.isShowing()) {
                searchVoiceDialog.show();
            }
            RequestManager.getInstance().onClickSearchInputVoice();
        }
    }

    public void onTnineAndFullBtnFocusChange(View v, boolean hasFocus){

    }

    public void setTnineAndFullBtnFocusChangeListner(){
        tNineKeyboardBtn.setOnFocusChangeListener(focusChangeNoAnimListener);
        fullKeyboardBtn.setOnFocusChangeListener(focusChangeNoAnimListener);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        onTnineAndFullBtnFocusChange(v,hasFocus);
        if(hasFocus) {
            if(v.equals(searchVoiceIV)) {
                searchVoiceFocusedIV.setVisibility(View.VISIBLE);
            } else if(v.equals(backspaceLL)) {
                backLLFocusedIV.setVisibility(View.VISIBLE);
            } else if(v.equals(clearLL)) {
                clearLLFocusedIV.setVisibility(View.VISIBLE);
            }
        } else {
            if(v.equals(searchVoiceIV)) {
                searchVoiceFocusedIV.setVisibility(View.GONE);
            } else if(v.equals(backspaceLL)) {
                backLLFocusedIV.setVisibility(View.GONE);
            } else if(v.equals(clearLL)) {
                clearLLFocusedIV.setVisibility(View.GONE);
            }
        }

    }
    
    @Override
    public void showHotSearch(List<HotSearchNew.DataBean> list) {
        if(loadingView.getVisibility() == View.VISIBLE) {
            loadingView.hide();
        }
        if(TextUtils.isEmpty(searchContentTV.getText().toString())) {
            setNoInputUI(list, getSearchHistory(), false);
        }
        searchNoInputRecyclerView.scrollToPosition(0);
        searchNoInputRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        searchNoInputRecyclerView.setFocusable(true);
    }

    @Override
    public void showSearchSuggest(List<HotSearchNew.DataBean> list, String keyword) {
        if(loadingView.getVisibility() == View.VISIBLE) {
            loadingView.hide();
        }
        setSearchSuggestUI(list, keyword);
        searchNoInputRecyclerView.scrollToPosition(0);
        searchNoInputRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        searchNoInputRecyclerView.setFocusable(true);
    }

    public SearchNoInputAdapter creatSearchNoInputAdapter(List<HotSearchNew.DataBean> hotSearchList,
                                                          List<SearchHistory> searchHistoryList, boolean searchNoResult){
        AppLogger.d(TAG, "creatSearchNoInputAdapter");
        return new SearchNoInputAdapter(hotSearchList, searchHistoryList, searchNoResult);
    }

    private void setNoInputUI(List<HotSearchNew.DataBean> hotSearchList,
                              List<SearchHistory> searchHistoryList, boolean searchNoResult) {
        if((hotSearchList == null || hotSearchList.size() == 0)
                && (searchHistoryList == null || searchHistoryList.size() == 0)) {
            searchNoInputRecyclerView.setVisibility(View.GONE);
            searchNoResultLayout.setVisibility(View.VISIBLE);
            return;
        }
        searchNoInputRecyclerView.setVisibility(View.VISIBLE);
        searchNoResultLayout.setVisibility(View.GONE);
        if(searchNoInputAdapter == null) {
            searchNoInputAdapter = creatSearchNoInputAdapter(hotSearchList, searchHistoryList, searchNoResult);
            searchNoInputAdapter.setPageName(mPageName);
            searchNoInputAdapter.setFocusBorderView(focusBorderView);
            searchNoInputAdapter.setClearHistoryListener(new SearchNoInputAdapter.ClearHistoryListener() {
                @Override
                public void onClearHistory() {
                    ClearSearchHistory();
                    searchNoInputAdapter.clearSearchHistoryList();
                }
            });
            searchNoInputRecyclerView.removeItemDecoration(searchSuggestItemDecoration);
            searchNoInputRecyclerView.addItemDecoration(searchNoInputItemDecoration);
            searchNoInputRecyclerView.setAdapter(searchNoInputAdapter);
        } else {
            searchNoInputAdapter.setData(hotSearchList, searchHistoryList, searchNoResult);
            if(searchNoInputRecyclerView.getAdapter() == null
                    || !searchNoInputRecyclerView.getAdapter().equals(searchNoInputAdapter)) {
                searchNoInputRecyclerView.removeItemDecoration(searchSuggestItemDecoration);
                searchNoInputRecyclerView.addItemDecoration(searchNoInputItemDecoration);
                searchNoInputRecyclerView.setAdapter(searchNoInputAdapter);
            } else {
                searchNoInputAdapter.notifyDataSetChanged();
                searchNoInputRecyclerView.setLastFocusedView(null);
            }
        }
    }

    public SearchSuggestAdapter creatSearchSuggestAdapter(List<HotSearchNew.DataBean> searchSuggestList){
        return new SearchSuggestAdapter(searchSuggestList);
    }

    private void setSearchSuggestUI(List<HotSearchNew.DataBean> searchSuggestList, String keyword) {
        if(!searchContentTV.getText().toString().equals(keyword)) {
            return;
        }
        searchNoResultLayout.setVisibility(View.GONE);
        searchNoInputRecyclerView.setVisibility(View.VISIBLE);
        if(searchSuggestList == null || searchSuggestList.size() == 0) {
            List<HotSearchNew.DataBean> hotSearchList = null;
            if(searchNoInputAdapter != null) {
                hotSearchList = searchNoInputAdapter.getHotSearchList();
            }
            setNoInputUI(hotSearchList, null, true);
        } else {
            if(searchSuggestAdapter == null) {
                searchSuggestAdapter = creatSearchSuggestAdapter(searchSuggestList);
                searchSuggestAdapter.setPageName(mPageName);
                searchSuggestAdapter.setFocusBorderView(focusBorderView);
                searchNoInputRecyclerView.removeItemDecoration(searchNoInputItemDecoration);
                searchNoInputRecyclerView.addItemDecoration(searchSuggestItemDecoration);
                searchNoInputRecyclerView.setAdapter(searchSuggestAdapter);
            } else {
                searchSuggestAdapter.setData(searchSuggestList);
                if(searchNoInputRecyclerView.getAdapter() == null
                        || !searchNoInputRecyclerView.getAdapter().equals(searchSuggestAdapter)) {
                    searchNoInputRecyclerView.removeItemDecoration(searchNoInputItemDecoration);
                    searchNoInputRecyclerView.addItemDecoration(searchSuggestItemDecoration);
                    searchNoInputRecyclerView.setAdapter(searchSuggestAdapter);
                } else {
                    searchSuggestAdapter.notifyDataSetChanged();
                    searchNoInputRecyclerView.setLastFocusedView(null);
                }
            }
        }
    }

    public void ClearSearchHistory() {
        SearchHistoryDao searchHistoryDao = DaoSessionInstance.getDaoSession(this).getSearchHistoryDao();
        searchHistoryDao.deleteAll();
    }

    public List<SearchHistory> getSearchHistory() {
        SearchHistoryDao searchHistoryDao = DaoSessionInstance.getDaoSession(this).getSearchHistoryDao();
        List<SearchHistory> searchHistoryList = searchHistoryDao.queryBuilder().
                orderDesc(SearchHistoryDao.Properties.ClickCount).limit(MAX_SEARCH_HISTORY).list();
        StringBuffer vrsId = new StringBuffer();
        for (int i = 0; i < searchHistoryList.size(); i++) {
            vrsId.append(searchHistoryList.get(i).getAlbumId() + ",");
        }

        NetworkApi.getAuditDenyAids(vrsId.toString(), "", new Observer<AuditDenyAids>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(AuditDenyAids value) {
                if (value != null && value.getData()!= null && value.getData().size() > 0) {
                    searchNoInputAdapter.setAuditDenyAidsList(value.getData());
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
        return searchHistoryList;
    }

    private void handleClickBackspaceLL() {
        String content = searchContentTV.getText().toString();
        if (!content.isEmpty()) {
            searchContentTV.setText(content.substring(0, content.length() - 1));
        }
    }

    private long lastTimeMillis;

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (event.getRepeatCount() == 0) {
                lastTimeMillis = System.currentTimeMillis();
            } else {
                if (System.currentTimeMillis() - lastTimeMillis < 300) {
                    return true;
                } else {
                    lastTimeMillis = System.currentTimeMillis();
                }
            }
        }
        if(event.getAction() == KeyEvent.ACTION_DOWN) {
            if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                View currentFocusedView = getCurrentFocus();
                if (currentFocusedView!=null){
                if(currentFocusedView.equals(searchVoiceIV)
                        || currentFocusedView.equals(clearLL)
                        || currentFocusedView.equals(fullKeyboardBtn)
                        || isRightFullKeyboardBtn(currentFocusedView)
                        || isRightTNineKeyboardBtn(currentFocusedView)) {
                    if(searchNoInputRecyclerView.getVisibility() != View.VISIBLE) {
                        return true;
                    }
                    lastLeftFocusedView = currentFocusedView;
                    boolean result = searchNoInputRecyclerView.onPressRightKeyRequestFocus();
                    if(result) {
                        searchLeftLayout.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
                        searchLeftLayout.setFocusable(false);
                    }
                    return result;
                }
                }
            } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
                View currentFocusedView = getCurrentFocus();
                if(currentFocusedView != null) {
                    RecyclerView.ViewHolder viewHolder = searchNoInputRecyclerView.findContainingViewHolder(currentFocusedView);
                    if(viewHolder != null && viewHolder.getLayoutPosition()
                            == searchNoInputRecyclerView.getAdapter().getItemCount() - 1) {
                        return true;
                    }
                }
            } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                View currentFocusedView = getCurrentFocus();
                if(currentFocusedView != null) {
                    RecyclerView.ViewHolder viewHolder = searchNoInputRecyclerView.findContainingViewHolder(currentFocusedView);
                    if(viewHolder != null) {
                        if(searchNoInputRecyclerView.getAdapter() != null
                                && searchNoInputRecyclerView.getAdapter().equals(searchNoInputAdapter)) {
                            if(searchNoInputAdapter.getSearchNoResult()) {
                                if(viewHolder.getLayoutPosition() == 2) {
                                    return true;
                                }
                            } else if(viewHolder.getLayoutPosition() == 1) {
                                return true;
                            }
                        } else if(viewHolder.getLayoutPosition() == 1) {
                            return true;
                        }
                    }
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    private boolean isRightFullKeyboardBtn(View view) {
        if((view instanceof Button) && (!(view.getParent() instanceof TNineKeyboardPopView))) {
            String text = ((Button)view).getText().toString();
            return  text.equals("F") || text.equals("L") || text.equals("R")
                    || text.equals("X") || text.equals("4") || text.equals("0");
        }
        return false;
    }

    private boolean isRightTNineKeyboardBtn(View view) {
        if(view instanceof TNineKeyboardItemView2) {
            TNineKeyboardItemView2 itemView = (TNineKeyboardItemView2) view;
            if(itemView.hasContent("3") || itemView.hasContent("6") || itemView.hasContent("9")) {
                return true;
            }
        }
        return false;
    }

    private View lastLeftFocusedView;

    public boolean onPressLeftKeyRequestFocus(View view) {
        searchNoInputRecyclerView.setLastFocusedView(view);
        searchLeftLayout.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        searchLeftLayout.setFocusable(true);
        if(lastLeftFocusedView != null) {
            return lastLeftFocusedView.requestFocus();
        }
        return false;
    }


}
