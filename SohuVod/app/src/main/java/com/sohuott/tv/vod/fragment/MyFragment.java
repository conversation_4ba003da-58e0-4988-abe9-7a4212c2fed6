package com.sohuott.tv.vod.fragment;

import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_MY;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Html;
import android.text.Layout;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AlignmentSpan;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.FormatUtils;
import com.sohu.lib_utils.StringUtil;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.common.Listener;
import com.sohuott.tv.vod.account.login.Login;
import com.sohuott.tv.vod.account.user.UserApi;
import com.sohuott.tv.vod.account.user.UserUtil;
import com.sohuott.tv.vod.activity.ListEduUserRelatedActivity;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.activity.PayInfoActivity;
import com.sohuott.tv.vod.activity.RenewActivity;
import com.sohuott.tv.vod.activity.TeenModeDescActivity;
import com.sohuott.tv.vod.activity.detective.PlayDetectionActivity;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.activity.setting.SettingActivity;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;
import com.sohuott.tv.vod.app.App;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.Logout;
import com.sohuott.tv.vod.lib.model.WechatPublic;
import com.sohuott.tv.vod.lib.push.event.LoginSuccessEvent;
import com.sohuott.tv.vod.lib.push.event.LogoutEvent;
import com.sohuott.tv.vod.lib.push.event.RefreshUserEvent;
import com.sohuott.tv.vod.lib.push.event.RegisterEvent;
import com.sohuott.tv.vod.lib.push.event.ScanSuccessEvent;
import com.sohuott.tv.vod.lib.push.event.TicketEvent;
import com.sohuott.tv.vod.lib.push.event.UserLikeRankingEvent;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.PostHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.search.CustomScrollview;
import com.sohuott.tv.vod.ui.NewScaleFocusChangeListener;
import com.sohuott.tv.vod.ui.ScaleFocusChangeListener;
import com.sohuott.tv.vod.ui.SearchVoiceDialog;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.AssetUtils;
import com.sohuott.tv.vod.utils.SyncHistoryAndCollectionUtil;
import com.sohuott.tv.vod.view.AgreementView;
import com.sohuott.tv.vod.view.ExitLoginDialog;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.BasePopupWindow;
import com.sohuott.tv.vod.widget.GlideImageView;

import org.greenrobot.eventbus.Subscribe;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.HashMap;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by fenglei on 16-1-18.
 */
public class MyFragment extends HomeBaseFragment implements View.OnClickListener,
        NewScaleFocusChangeListener.FocusCallback, View.OnKeyListener {
    private static final String TAG = MyFragment.class.getSimpleName();
    private static final int MSG_REFRESH_USER_INFO = 1;
    private static final int MSG_REFRESH_USER_LIKE_RANK = 2;
    private static final int MSG_REFRESH_USER_TICKET_NUMBER = 3;

    private static final int TAG_VRS = 0;
    private static final int TAG_VR = 1;
    private static final int TAG_PGC = 2;

    private View membershipView;
    private View reNewManage;
    private View membershipXufeiView;
    private View mPointShop;
    private View mHuiyuanpianku;
    private View mPlayHistory;
    private View mCollectLayout;
    private View mMyReserve;
    private View mMyScore;
    private View mMyOrder;
    private View mMyClass;
    private View mMyQuestionFeedback;
    private View mAboutLayout;
    private View mTeenEnterLayout;
    private View mSettingLaout;
    private View mWeixinhao;
    private View mPayInfo;

    private View mPlayDetection;
    private Button mLoginButton;
    //    private ViewGroup mLoginLayout;
    private Button mAccountManager;
    //    private ViewGroup mAccountManagerLayout;
    private ViewGroup mUserLoginLayout;
    private ViewGroup mUserInfoLayout;
    private CustomScrollview mScrollContainer;
    private TextView mUserType;

    private SearchVoiceDialog searchVoiceDialog;
    private FocusBorderView mFocusBorderView;
    private Context mContext;
    private LoginUserInformationHelper mHelper;
    private NewScaleFocusChangeListener mScaledFocusChange;
    private String mUserPhotoUrl = "";
    private AgreementView mAgreementView;

    private BasePopupWindow mAgreementWindow;
    private BasePopupWindow mWeixinhaoWindow;
    private LayoutInflater mLayoutInflater;
    private GlideImageView qrcodeIV;
    private HashMap<String, String> mPathInfo;
    private TextView mPaizhaohao;


    private static class InnerHandler extends Handler {
        private WeakReference<MyFragment> mWrapper;

        InnerHandler(MyFragment fragment) {
            mWrapper = new WeakReference<>(fragment);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            MyFragment fragment = mWrapper.get();
            if (fragment == null) {
                return;
            }
            int what = msg.what;
            if (fragment.getActivity() == null) {
                return;
            }
            switch (what) {
                case MSG_REFRESH_USER_INFO:
                    fragment.initUser();
                    break;
                case MSG_REFRESH_USER_LIKE_RANK:
//                    initUserLikeRank();
                    break;
                case MSG_REFRESH_USER_TICKET_NUMBER:
                    break;
                default:
                    break;
            }
        }
    }

    private Handler mHandler = new InnerHandler(this);

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setSubPageName("6_home_" + mChannelId);
        mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", StringUtil.toString(mChannelId));
        mHelper = LoginUserInformationHelper.getHelper(mContext.getApplicationContext());
        mScaledFocusChange = new NewScaleFocusChangeListener();
        mScaledFocusChange.setFocusCallback(this);
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.fragment_my, container, false);
        mLayoutInflater = inflater;
        mScrollContainer = (CustomScrollview) mRootView.findViewById(R.id.scroll_container);
        mUserLoginLayout = (ViewGroup) mRootView.findViewById(R.id.user_login_layout);
        mUserInfoLayout = (ViewGroup) mRootView.findViewById(R.id.user_info_layout);
        membershipView = mRootView.findViewById(R.id.membership_view);
        reNewManage = mRootView.findViewById(R.id.auto_fee_manage);
        membershipXufeiView = mRootView.findViewById(R.id.membership_xufei_view);
        mPointShop = mRootView.findViewById(R.id.point_shop_view);
        mHuiyuanpianku = mRootView.findViewById(R.id.huiyuanpianku_view);
        mPlayHistory = mRootView.findViewById(R.id.play_history);
        mCollectLayout = mRootView.findViewById(R.id.collect_layout);
        mMyReserve = mRootView.findViewById(R.id.my_reserve);
        mMyScore = mRootView.findViewById(R.id.my_score);
        mMyOrder = mRootView.findViewById(R.id.my_order);
        mMyClass = mRootView.findViewById(R.id.my_class);
        mMyQuestionFeedback = mRootView.findViewById(R.id.question_feedback);
        mAboutLayout = mRootView.findViewById(R.id.about_layout);
        mTeenEnterLayout = mRootView.findViewById(R.id.teen_enter_layout);
        mSettingLaout = mRootView.findViewById(R.id.setting_layout);
        mWeixinhao = mRootView.findViewById(R.id.weixingongzhonghao);
        mPayInfo = mRootView.findViewById(R.id.my_pay_info);
        mPlayDetection = mRootView.findViewById(R.id.play_detection_layout);
        mLoginButton = (Button) mRootView.findViewById(R.id.login_in);
//        mLoginLayout = (ViewGroup) mRootView.findViewById(R.id.login_in_layout);
        mAccountManager = (Button) mRootView.findViewById(R.id.account_manage_button);
//        mAccountManagerLayout = (ViewGroup) mRootView.findViewById(R.id.account_manager_layout);
        mFocusBorderView = (FocusBorderView) mRootView.findViewById(R.id.fragment_item_focus);
        mScaledFocusChange.setFocusBorderView(mFocusBorderView);
        mPaizhaohao = (TextView) mRootView.findViewById(R.id.paizhaohao);
        ScaleFocusChangeListener userScaledListen = new ScaleFocusChangeListener();
        userScaledListen.setScale(1.03f);
        userScaledListen.setFocusBorderView(mFocusBorderView);
        membershipView.setOnFocusChangeListener(mScaledFocusChange);
        reNewManage.setOnFocusChangeListener(mScaledFocusChange);
        membershipXufeiView.setOnFocusChangeListener(mScaledFocusChange);
        mPointShop.setOnFocusChangeListener(mScaledFocusChange);
        mHuiyuanpianku.setOnFocusChangeListener(mScaledFocusChange);
        mPlayHistory.setOnFocusChangeListener(mScaledFocusChange);
        mCollectLayout.setOnFocusChangeListener(mScaledFocusChange);
        mMyReserve.setOnFocusChangeListener(mScaledFocusChange);
        mMyScore.setOnFocusChangeListener(mScaledFocusChange);
        mMyOrder.setOnFocusChangeListener(mScaledFocusChange);
        mMyClass.setOnFocusChangeListener(mScaledFocusChange);
        mMyQuestionFeedback.setOnFocusChangeListener(mScaledFocusChange);
        mAboutLayout.setOnFocusChangeListener(mScaledFocusChange);
        mTeenEnterLayout.setOnFocusChangeListener(mScaledFocusChange);
        mSettingLaout.setOnFocusChangeListener(mScaledFocusChange);
        mWeixinhao.setOnFocusChangeListener(mScaledFocusChange);
        mPayInfo.setOnFocusChangeListener(mScaledFocusChange);
        mPlayDetection.setOnFocusChangeListener(mScaledFocusChange);
        mLoginButton.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    showNavigationBar();
                }
            }
        });
        mAccountManager.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    showNavigationBar();
                }
            }
        });
//        mLoginLayout.setOnFocusChangeListener(mScaledFocusChange);
//        mAccountManagerLayout.setOnFocusChangeListener(mScaledFocusChange);
        membershipView.setOnClickListener(this);
        reNewManage.setOnClickListener(this);
        membershipXufeiView.setOnClickListener(this);
        mPointShop.setOnClickListener(this);
        mHuiyuanpianku.setOnClickListener(this);
        mPlayHistory.setOnClickListener(this);
        mMyReserve.setOnClickListener(this);
        mCollectLayout.setOnClickListener(this);
        mMyScore.setOnClickListener(this);
        mMyOrder.setOnClickListener(this);
        mMyClass.setOnClickListener(this);
        mMyQuestionFeedback.setOnClickListener(this);
        mAboutLayout.setOnClickListener(this);
        mTeenEnterLayout.setOnClickListener(this);
        mSettingLaout.setOnClickListener(this);
        mWeixinhao.setOnClickListener(this);
        mPayInfo.setOnClickListener(this);
        mPlayDetection.setOnClickListener(this);
        mLoginButton.setOnClickListener(this);
//        mLoginLayout.setOnClickListener(this);
        mLoginButton.setOnClickListener(this);
//        mAccountManagerLayout.setOnClickListener(this);
        mAccountManager.setOnClickListener(this);
        membershipView.setOnKeyListener(this);
        reNewManage.setOnKeyListener(this);
        membershipXufeiView.setOnKeyListener(this);
        mPointShop.setOnKeyListener(this);
        mHuiyuanpianku.setOnKeyListener(this);
        mPlayHistory.setOnKeyListener(this);
        mMyReserve.setOnKeyListener(this);
        mCollectLayout.setOnKeyListener(this);
        mMyScore.setOnKeyListener(this);
        mMyOrder.setOnKeyListener(this);
        mMyClass.setOnKeyListener(this);
        mMyQuestionFeedback.setOnKeyListener(this);
        mAboutLayout.setOnKeyListener(this);
        mTeenEnterLayout.setOnKeyListener(this);
        mSettingLaout.setOnKeyListener(this);
        mWeixinhao.setOnKeyListener(this);
        mPayInfo.setOnKeyListener(this);
        mPlayDetection.setOnKeyListener(this);
        mLoginButton.setOnKeyListener(this);
        mLoginButton.setOnKeyListener(this);
        mAccountManager.setOnKeyListener(this);

//        mLoginLayout.setOnKeyListener(this);
//        mAccountManagerLayout.setOnKeyListener(this);

        initUI();
        return mRootView;
    }

    @Override
    public void onResume() {
        super.onResume();
        AppLogger.d(TAG, "onResume");

        // 开通会员或会员续费刷新用户信息
        if (mHelper.getIsLogin() && !mHelper.getIsSinglePurchase()) {
            refreshUser();
        } else {
            initUser();
        }

    }

    @Override
    protected void onVisible() {
        super.onVisible();
        AppLogger.d(TAG, "onVisible");
        if (mScrollContainer != null) {
            mScrollContainer.scrollTo(0, 0);
        }
        exposureStatistic(10166);
        exposureStatistic(10168);
        exposureStatistic(10170);
        exposureStatistic(10172);
        exposureStatistic(10174);
        exposureStatistic(10176);
        exposureStatistic(10178);
        exposureStatistic(10162);
        exposureStatistic(10141);

        mHelper = LoginUserInformationHelper.getHelper(getContext());

        if (mHelper.getIsLogin()) {
            if (mHelper.isVip()) {
                exposureUserStatistic("1");
            } else {
                exposureUserStatistic("0");
            }
        } else {
            exposureUserStatistic("0");
        }
    }

    private void exposureUserStatistic(String isVip) {
        HashMap<String, String> memoInfo = new HashMap<>();
        memoInfo.put("isVip", isVip);
        AppLogger.d(TAG, "exposureStatistic eventId ? " + 10164);
        RequestManager.getInstance().onAllEvent(new EventInfo(10164, "imp"), mPathInfo, null,
                memoInfo);
    }

    public View getLeftUpFocusView() {
        return mCollectLayout;
    }

    public View getLeftDownFocusView() {
        return mMyQuestionFeedback;
    }


    @Override
    public void onClick(View v) {

//        else
        if (v.equals(membershipView)) {
            //开通会员
            ActivityLauncher.startPayActivity(getActivity(), PayActivity.PAY_SOURCE_MY_OPEN_VIP);
            clickStatistic(10139);
        } else if (v.equals(membershipXufeiView)) {
            //续费会员
            ActivityLauncher.startPayActivity(getActivity(), PayActivity.PAY_SOURCE_MY_OPEN_VIP);
            clickStatistic(10151);
        } else if (v.equals(mPointShop)) {
            //积分商城
            ActivityLauncher.startWelfareActivity(getContext());
            clickStatistic(10167);
        } else if (v.equals(mHuiyuanpianku)) {
            //会员片库
            ActivityLauncher.startGridListActivityWithCatecode(mContext, 31, 0,
                    false, 0, 75, 2);
            clickStatistic(10185);
        } else if (v.equals(mPlayHistory)) {
            //播放历史
            ActivityLauncher.startListUserRelatedActivity(getActivity(),
                    ListUserRelatedActivity.LIST_INDEX_HISTORY);
            clickStatistic(10138);
        } else if (v.equals(mCollectLayout)) {
            //收藏
            ActivityLauncher.startListUserRelatedActivity(getActivity(),
                    ListUserRelatedActivity.LIST_INDEX_COLLECTION);
            //点击统计
            clickStatistic(10169);
        } else if (v.equals(mMyReserve)) {
            //我的预约
            ActivityLauncher.startListUserRelatedActivity(getActivity(),
                    ListUserRelatedActivity.LIST_INDEX_BOOKED);
            clickStatistic(10171);
        } else if (v.equals(mMyScore)) {
            //我的积分
            ActivityLauncher.startListUserRelatedActivity(getActivity(),
                    ListUserRelatedActivity.LIST_INDEX_POINT);
            clickStatistic(10173);
        } else if (v.equals(mMyOrder)) {
            //我的订单
            ActivityLauncher.startListUserRelatedActivity(getActivity(),
                    ListUserRelatedActivity.LIST_INDEX_CONSUME_RECORD);
            clickStatistic(10175);
        } else if (v.equals(mMyClass)) {
            //我的课程
            ActivityLauncher.startEduUserRelatedActivity(getContext(),
                    ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD, "0");
            clickStatistic(10177);
        } else if (v.equals(mMyQuestionFeedback)) {
            //问题反馈
            issuesPush();
//            ActivityLauncher.startFeedbackActivity(getActivity());
            clickStatistic(10179);
        } else if (v.equals(mAboutLayout)) {
            //关于
            ActivityLauncher.startAboutActivity(getActivity());
            clickStatistic(10180);
        } else if (v.equals(mTeenEnterLayout)) {
            //青少年模式入口
            TeenagersManger.getInstance().exposureTeenagerClick();
            Intent intent = new Intent(getActivity(), TeenModeDescActivity.class);
            intent.putExtra(RenewActivity.PARAM_AC_TYPE, RenewActivity.ACTYPE_RENEW);
            clickStatistic(10204);
            this.startActivity(intent);
        } else if (v.equals(mSettingLaout)) {
            //设置
            AppLogger.d(TAG, "settiinglayout click");
            SettingActivity.Companion.actionStart(getContext());
            clickStatistic(10243);
        } else if (v.equals(mWeixinhao)) {
            //微信公众号
            showWeixinhaoWindow();
            clickStatistic(10183);
        } else if (v.equals(mLoginButton)) {
            //开通会员
            if (mHelper.getIsLogin()) {
                ActivityLauncher.startListUserRelatedActivity(getActivity(),
                        ListUserRelatedActivity.LIST_INDEX_MY);
            } else {
                ActivityLauncher.startLoginActivity(getActivity(), Constant.LAUNCHER_SOURCE, (int) mChannelId);
            }
        } else if (v.equals(mLoginButton)) {
            //立即登录
            if (mHelper.getIsLogin()) {
                ActivityLauncher.startListUserRelatedActivity(getActivity(),
                        ListUserRelatedActivity.LIST_INDEX_MY);
            } else {
                ActivityLauncher.startLoginActivity(getActivity(), Constant.LAUNCHER_SOURCE, (int) mChannelId);
            }
            clickStatistic(10136);
        } else if (v.equals(mAccountManager)) {
            //账号管理
            if (mHelper.getIsLogin()) {
                logOut();

            } else {
                ActivityLauncher.startLoginActivity(getActivity(), Constant.LAUNCHER_SOURCE, (int) mChannelId);
            }
            clickStatistic(10163);
        } else if (v.equals(reNewManage)) {
            //管理自动续费
            Intent intent = new Intent(getActivity(), RenewActivity.class);
            intent.putExtra(RenewActivity.PARAM_AC_TYPE, RenewActivity.ACTYPE_RENEW);
            clickStatistic(10205);
            this.startActivity(intent);
        } else if (v.equals(mPayInfo)) {
            //订购信息
            Intent intent = new Intent(getActivity(), PayInfoActivity.class);
            this.startActivity(intent);
        } else if (v.equals(mPlayDetection)) {
            //播放检测
            Intent intent = new Intent(getActivity(), PlayDetectionActivity.class);
            this.startActivity(intent);
            clickStatistic(10329);
        }
    }

    private void issuesPush() {
        App.printAppInfo();
        ActivityLauncher.startIssuesFeedbackActivity();
    }


    private void logOut() {
        ExitLoginDialog dialog = new ExitLoginDialog(this.getActivity(), new ExitLoginDialog.ExitListener() {
            @Override
            public void onExit(boolean isConfirm) {
                if (isConfirm) {
                    requestLogout();
                    RequestManager.getInstance().onMyUserLoginBtnClickEvent("Logout");
                }
            }
        });
        dialog.show();

    }

    private void requestLogout() {
        NetworkApi.getLogout(new Observer<Logout>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(Logout value) {
                LibDeprecatedLogger.d("requestLogout(): onNext()");
                if (getActivity() != null && (getActivity() instanceof ListUserRelatedActivity) && ((ListUserRelatedActivity) getActivity()).getLeftSelectedTag() != LIST_INDEX_MY) {
                    return;
                }

                if (mHelper != null && null != value) {
                    int status = value.getStatus();

                    if (status == 200) {
                        mHelper.clearLoginStatus();
                        PostHelper.postLogoutEvent();
                        initUser();
                        mLoginButton.requestFocus();
                        //clear cached data in SharedPreference.
                        RequestManager.getInstance().updatePasspost(mHelper.getLoginPassport(), "0");
                    } else {
                        ToastUtils.showToast2(getActivity(), getResources().getString(R.string.txt_fragment_my_user_logout_fail));
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                if (isVisible()) {
                    LibDeprecatedLogger.e("requestLogout(): onError()--" + e.getMessage());
                    ToastUtils.showToast2(getActivity(), getResources().getString(R.string.txt_fragment_my_user_logout_fail));
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestLogout(): onComplete()");
            }
        });
    }

    private void clickStatistic(int eventId) {
        AppLogger.d(TAG, "clickStatistic eventId ? " + eventId);
        RequestManager.getInstance().onAllEvent(new EventInfo(eventId, "clk"), mPathInfo, null,
                null);
    }

    private void exposureStatistic(int eventId) {
        AppLogger.d(TAG, "exposureStatistic eventId ? " + eventId);
        RequestManager.getInstance().onAllEvent(new EventInfo(eventId, "imp"), mPathInfo, null,
                null);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
            if (v.equals(mPlayHistory)
                    || v.equals(mCollectLayout)
                    || v.equals(mMyReserve)
                    || v.equals(mMyScore)
                    || v.equals(mMyOrder)
                    || v.equals(mMyClass)
                    || v.equals(mPayInfo)) {
                hideNavigationBar();
            } else if (v.equals(mLoginButton)
                    || v.equals(mAccountManager)
                    || v.equals(membershipView)
                    || v.equals(membershipXufeiView)
                    || v.equals(mPointShop)
                    || v.equals(mHuiyuanpianku)) {
                showNavigationBar();
            }
        }
    }


    private void showAgreementWindow() {
        if (mAgreementWindow == null) {
            mAgreementWindow =
                    new BasePopupWindow(mLayoutInflater.inflate(R.layout.dialog_agreement, null));
            mAgreementWindow.setText(R.id.tv_agreement,
                    Html.fromHtml(AssetUtils.readString(getContext(), "register_agreement.html")));
            final TextView contentText =
                    (TextView) mAgreementWindow.getContentView().findViewById(R.id.tv_agreement);
            contentText.post(new ShowAgreementWindowRunnable(this));
        }
        mAgreementWindow.showWindow(getView());
    }

    private static class ShowAgreementWindowRunnable implements Runnable {
        private WeakReference<MyFragment> mWrapper;

        ShowAgreementWindowRunnable(MyFragment myFragment) {
            mWrapper = new WeakReference<>(myFragment);
        }

        @Override
        public void run() {
            MyFragment myFragment = mWrapper.get();
            if (myFragment != null) {
                final TextView contentText =
                        (TextView) myFragment.mAgreementWindow.getContentView().findViewById(R.id.tv_agreement);
                CharSequence content = contentText.getText();
                SpannableStringBuilder builder = new SpannableStringBuilder(content);
                int offset = contentText.getLayout().getLineEnd(0);
                final AlignmentSpan.Standard standard =
                        new AlignmentSpan.Standard(Layout.Alignment.ALIGN_CENTER);
                builder.setSpan(standard, 0, offset, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                contentText.setText(builder);
            }
        }
    }

    private void showWeixinhaoWindow() {
        AppLogger.d(TAG, "showWeixinhaoWindow");
        if (mWeixinhaoWindow == null) {
            AppLogger.d(TAG, "mWeixinhaoWindow == null");
            mWeixinhaoWindow =
                    new BasePopupWindow(mLayoutInflater.inflate(R.layout.weinxinhao_popup_window,
                            null));
            qrcodeIV =
                    (GlideImageView) mWeixinhaoWindow.getContentView().findViewById(R.id.qrcode_iv);
        }
        mWeixinhaoWindow.showWindow(getView());
        getQrcode();
        RequestManager.getInstance().onSearchVoiceExposureEvent();
//        RequestManager.getInstance().onClickMy(mChannelId, "zhushou");
    }

    private void getQrcode() {
        AppLogger.d(TAG, "getQrcode");
        NetworkApi.getWechatLogin(UserInfoHelper.getGid(), Constant.TYPE_CAPTCHA_BIND,
                new DisposableObserver<WechatPublic>() {
                    @Override
                    public void onNext(WechatPublic response) {
                        AppLogger.d(TAG, "getQrcode onNext");
                        if (null != response) {
                            String data = response.getData();
                            AppLogger.d(TAG, "getQrcode data ? " + data);
                            int status = response.getStatus();
                            if (status == 200 && null != data) {
                                qrcodeIV.setImageRes(data,
                                        getContext().getResources().getDrawable(R.drawable.bg_launcher_poster),
                                        getContext().getResources().getDrawable(R.drawable.bg_launcher_poster));
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        AppLogger.d(TAG, "getQrcode onError");
                        LibDeprecatedLogger.e("getWechatLogin() Error: " + e.getMessage(), e);
                    }

                    @Override
                    public void onComplete() {
                        AppLogger.d(TAG, "getQrcode onComplete");
                    }
                });
    }


    @Override
    protected boolean isEventBusAvailable() {
        return true;
    }

    @Subscribe
    public void onEventMainThread(ScanSuccessEvent event) {
        if (null == event) {
            return;
        }
    }

    @Subscribe
    public void onEventMainThread(LoginSuccessEvent event) {
        if (null == event) {
            return;
        }
        AppLogger.d(TAG, "LoginSuccessEvent");
        mHandler.sendEmptyMessage(MSG_REFRESH_USER_INFO);
    }

    @Subscribe
    public void onEventMainThread(RefreshUserEvent event) {
        if (null == event) {
            return;
        }
        AppLogger.d(TAG, "RefreshUserEvent");
        mHandler.sendEmptyMessage(MSG_REFRESH_USER_INFO);
    }

    @Subscribe
    public void onEventMainThread(UserLikeRankingEvent event) {
        if (null == event) {
            return;
        }

        mHandler.sendEmptyMessage(MSG_REFRESH_USER_LIKE_RANK);
    }

    @Subscribe
    public void onEventMainThread(TicketEvent event) {
        if (null == event) {
            return;
        }

        mHandler.sendEmptyMessage(MSG_REFRESH_USER_TICKET_NUMBER);
    }

    @Subscribe
    public void onEventMainThread(LogoutEvent event) {
        if (null == event) {
            return;
        }
        AppLogger.d(TAG, "LogoutEvent");
        mHandler.sendEmptyMessage(MSG_REFRESH_USER_INFO);
        clearLocalHistoryAndCollection();
    }

    @Subscribe
    public void onEventMainThread(RegisterEvent event) {
        if (null == event) {
            return;
        }
        AppLogger.d(TAG, "RegisterEvent");
        mHandler.sendEmptyMessage(MSG_REFRESH_USER_INFO);
    }

    /**
     * 退出登录清除本地历史、收藏
     */
    private void clearLocalHistoryAndCollection() {
        SyncHistoryAndCollectionUtil.clearLocalHistoryAndCollection(this.getActivity().getApplicationContext());
    }

    private void initUI() {
        if (getActivity() == null) {
            return;
        }
        String gid = UserInfoHelper.getGid();
        String paihaohao = "牌照账号：SNM_" + gid;
        mPaizhaohao.setText(paihaohao);
        initUser();
//        initHistoryUI();
        mAgreementView = new AgreementView(this.getActivity(), null);
    }


    private void backToTop() {
        if (getActivity() != null && getActivity() instanceof LauncherActivity) {
            View mTabView = ((LauncherActivity) getActivity()).getHorizontalGridView();
            View mTopViewBar = ((LauncherActivity) getActivity()).getTopViewBar();
            if (mTabView != null) {
                if (mTabView != null && mTabView.getVisibility() != View.VISIBLE) {
                    mTabView.setVisibility(View.VISIBLE);
                }
                if (mTopViewBar != null && mTopViewBar.getVisibility() != View.VISIBLE) {
                    mTopViewBar.setVisibility(View.VISIBLE);
                }
                mTabView.requestFocus();
            }
            mScrollContainer.scrollTo(0, 0);
        }
    }


    @Override
    public void onDestroy() {
        AppLogger.d(TAG, "onDestroy");
        super.onDestroy();
        if (mAgreementView != null) {
            mAgreementView.release();
        }
    }

    private void initUser() {
        AppLogger.d(TAG, "initUser()");
        if (mHelper.getIsLogin()) {
            mUserLoginLayout.setVisibility(View.GONE);
            mUserInfoLayout.setVisibility(View.VISIBLE);
            if (mHelper.isVip()) {
                membershipXufeiView.setVisibility(View.VISIBLE);
                membershipView.setVisibility(View.GONE);
            } else {
                membershipXufeiView.setVisibility(View.GONE);
                membershipView.setVisibility(View.VISIBLE);
            }
            initUserData();
        } else {
            mUserPhotoUrl = "";
            mUserLoginLayout.setVisibility(View.VISIBLE);
            mUserInfoLayout.setVisibility(View.GONE);
            membershipXufeiView.setVisibility(View.GONE);
            membershipView.setVisibility(View.VISIBLE);
        }
    }

    private void initUserData() {
        AppLogger.d(TAG, "mHelper.getUtype() ? " + mHelper.getUtype());
        TextView myUserName = (TextView) mUserInfoLayout.findViewById(R.id.nick_name);
        String userName = mHelper.getNickName();
        final String avatar = mHelper.getLoginPhoto();
        GlideImageView myAvatar = (GlideImageView) mUserInfoLayout.findViewById(R.id.head_photo);
        if (null != avatar && !avatar.trim().equals("")) {
            if (mUserPhotoUrl == null || !TextUtils.equals(mUserPhotoUrl, avatar)) {
                myAvatar.setCircleImageRes(avatar,
                        getResources().getDrawable(R.drawable.default_avatar),
                        getResources().getDrawable(R.drawable.default_avatar));
                mUserPhotoUrl = avatar;
            }
        }

        GlideImageView loginTypePhoto =
                (GlideImageView) mUserInfoLayout.findViewById(R.id.login_type_photo);
        int utypeInt = Util.getSouthMediaLoginType(mHelper.getUtype());
        AppLogger.d(TAG, "utypeInt ? " + utypeInt);
        switch (utypeInt) {
            case 4:
                loginTypePhoto.setBackgroundResource(R.drawable.login_type_weibo);
                break;
            case 1:
                loginTypePhoto.setBackgroundResource(R.drawable.login_type_wechat);
                break;
            case 2:
                loginTypePhoto.setBackgroundResource(R.drawable.login_type_qq);
                break;
            case 3:
            case 6:
                loginTypePhoto.setBackgroundResource(R.drawable.login_type_sohu);
                break;
        }

        if (null != userName && !userName.trim().equals("")) {
            myUserName.setText(userName);
        }
        ViewGroup vipTimeLayout = (ViewGroup) mUserInfoLayout.findViewById(R.id.vip_time_layout);
        ViewGroup ticketNumberLayout =
                (ViewGroup) mUserInfoLayout.findViewById(R.id.ticket_number_layout);
        mUserType = (TextView) mUserInfoLayout.findViewById(R.id.user_type);
        TextView ticketNumberLable =
                (TextView) ticketNumberLayout.findViewById(R.id.tiket_number_label);
        TextView ticketNumberTextview =
                (TextView) ticketNumberLayout.findViewById(R.id.ticket_number_textview);
        if (mHelper.isVip()) {
            AppLogger.d(TAG, "initUserData mHelper.getVipTime() ? " + mHelper.getVipTime());
            AppLogger.d(TAG, "System.currentTimeMillis() ? " + System.currentTimeMillis());
            try {
                if ((System.currentTimeMillis()) > Long.valueOf(mHelper.getVipTime())) {
                    Drawable queenDrawable =
                            mContext.getResources().getDrawable(R.drawable.my_member_queen_expire);
                    queenDrawable.setBounds(0, 0, queenDrawable.getMinimumWidth(),
                            queenDrawable.getMinimumHeight());
                    myUserName.setCompoundDrawables(null, null, queenDrawable, null);

                    vipTimeLayout.setVisibility(View.GONE);
                    mUserType.setVisibility(View.VISIBLE);
                    mUserType.setText(R.string.your_membership_expired);

                    ticketNumberLable.setText(R.string.film_ticket_frezee_lable);

                } else {
                    Drawable queenDrawable =
                            mContext.getResources().getDrawable(R.drawable.my_member_queen);
                    queenDrawable.setBounds(0, 0, queenDrawable.getMinimumWidth(),
                            queenDrawable.getMinimumHeight());
                    myUserName.setCompoundDrawables(null, null, queenDrawable, null);

                    mUserType.setVisibility(View.GONE);
                    vipTimeLayout.setVisibility(View.VISIBLE);

                    TextView vipTimeTextView =
                            (TextView) vipTimeLayout.findViewById(R.id.vip_time_textview);
                    String vipDate = FormatUtils.formatDate(Long.valueOf(mHelper.getVipTime()));
                    vipTimeTextView.setText(vipDate);

                    ticketNumberLable.setText(R.string.film_ticket_lable);
                }
            } catch (Exception e) {
                AppLogger.d(TAG, "initUserData e ? " + e);
            }
            ticketNumberLayout.setVisibility(View.VISIBLE);
            ticketNumberTextview.setText(mHelper.getUserTicketNumber());
        } else {
            if (mHelper.getIsExpiredVip()) {
                Drawable queenDrawable =
                        mContext.getResources().getDrawable(R.drawable.my_member_queen_expire);
                queenDrawable.setBounds(0, 0, queenDrawable.getMinimumWidth(),
                        queenDrawable.getMinimumHeight());
                myUserName.setCompoundDrawables(null, null, queenDrawable, null);

                vipTimeLayout.setVisibility(View.GONE);
                mUserType.setVisibility(View.VISIBLE);
                mUserType.setText(R.string.your_membership_expired);

                ticketNumberLable.setText(R.string.film_ticket_frezee_lable);

            } else {
                vipTimeLayout.setVisibility(View.GONE);
                mUserType.setVisibility(View.VISIBLE);
                mUserType.setText(R.string.normal_user);
                ticketNumberLayout.setVisibility(View.INVISIBLE);
                myUserName.setCompoundDrawables(null, null, null, null);
            }

        }
    }

    private void refreshUser() {
        AppLogger.d(TAG, "refreshUser");
        UserApi.refreshUser(getActivity(), new Listener<Login>() {
            @Override
            public void onSuccess(Login login) {
                LibDeprecatedLogger.d("refreshUser(): onSuccess");
                if (null != login) {
                    Login.LoginData data = login.getData();
                    String message = login.getMessage();
                    int status = login.getStatus();
                    if (status == 200 && null != data) {
                        UserUtil.handleLoginData(getActivity(), "", login);
                        mHandler.sendEmptyMessage(MSG_REFRESH_USER_INFO);
                    } else {
//                        ToastUtils.showToast2(mContext, message);
                        AppLogger.v(message + " : " + message);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("refreshUser(): onError() = " + e.toString());
            }
        });
    }

    @Override
    public View getDefaultUpFocusView() {
//        return membershipVG;
        return null;
    }

    /**
     * Convert integer value to string value
     *
     * @param integer integer value you want to convert
     * @return string value has been converted.
     */
    private String convertIntegerToString(int integer) {
        if (integer < 0) {
            return null;
        } else if (integer < 10) {
            return "0" + integer;
        } else {
            return String.valueOf(integer);
        }
    }

    private void hideNavigationBar() {
        AppLogger.d(TAG, "hideNavigationBar()");
        if (getActivity() != null && getActivity() instanceof LauncherActivity) {
            if (((LauncherActivity) getActivity()).getHorizontalGridView().getVisibility() != View.GONE) {
                ((LauncherActivity) getActivity()).getHorizontalGridView().setVisibility(View.GONE);
            }
        }
    }

    private void showNavigationBar() {
        AppLogger.d(TAG, "showNavigationBar()");
        if (mScrollContainer != null) {
            mScrollContainer.scrollTo(0, 0);
        }
        if (getActivity() != null && getActivity() instanceof LauncherActivity) {
            if (((LauncherActivity) getActivity()).getHorizontalGridView().getVisibility() != View.VISIBLE) {
                ((LauncherActivity) getActivity()).getHorizontalGridView().setVisibility(View.VISIBLE);
            }
        }
        mHandler.postDelayed(new ShowNavigationBarRunnable(this), 300);
    }

    private static class ShowNavigationBarRunnable implements Runnable {
        private WeakReference<MyFragment> mWrapper;

        ShowNavigationBarRunnable(MyFragment myFragment) {
            mWrapper = new WeakReference<>(myFragment);
        }

        @Override
        public void run() {
            MyFragment myFragment = mWrapper.get();
            if (myFragment != null) {
                if (myFragment.mScrollContainer != null) {
                    myFragment.mScrollContainer.scrollTo(0, 0);
                }
            }
        }
    }

    /**
     * Called when a hardware key is dispatched to a view. This allows listeners to
     * get a chance to respond before the target view.
     * <p>Key presses in software keyboards will generally NOT trigger this method,
     * although some may elect to do so in some situations. Do not assume a
     * software input method has to be key-based; even if it is, it may use key presses
     * in a different way than you expect, so there is no way to reliably catch soft
     * input key presses.
     *
     * @param v       The view the key has been dispatched to.
     * @param keyCode The code for the physical key that was pressed
     * @param event   The KeyEvent object containing full information about
     *                the event.
     * @return True if the listener has consumed the event, false otherwise.
     */
    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_BACK) {
            backToTop();
            return true;
        }
        return false;
    }
}