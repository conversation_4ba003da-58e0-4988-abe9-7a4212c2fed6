package com.sohuott.tv.vod.fragment;

import android.content.Context;
import android.os.Bundle;

import androidx.constraintlayout.widget.ConstraintLayout;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.SystemUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.ui.EpisodeLayoutNew;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.VrsTextView;
import com.sohuott.tv.vod.widget.PlayingView;

import java.util.List;

/**
 * Created by fenglei on 16-7-5.
 */
public class EpisodeVrsFragmentNew extends EpisodeBaseFragmentNew {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = (ViewGroup) inflater.inflate(R.layout.fragment_episode_vrs_layout, container, false);
        mRootView.setClipChildren(false);
        mRootView.setClipToPadding(false);
        initUI();
        return mRootView;
    }

    private void setTVOnFocus(TextView textView) {
//        textView.setSelected(true);
//        textView.setMarqueeRepeatLimit(-1);
//        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
//        textView.setSelected(false);
//        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

    private void setPointsPosition(View v, String text) {
        if (mEpisodePoints == null) {
            return;
        }
        int[] location = new int[2];
        v.getLocationOnScreen(location);
        int deskWidth = v.getMeasuredWidth();
        int shadeWidth = Util.getTextViewStrWidth(text, mEpisodePoints.getPaint());
        int x;
        if (deskWidth >= shadeWidth) {
            x = location[0] + (deskWidth - shadeWidth) / 2;
        } else {
            x = location[0] - (shadeWidth - deskWidth) / 2;
        }
        AppLogger.INSTANCE.e("deskWidth : " + deskWidth + " , shadeWidth: " + shadeWidth + ", x: " + x + ", location[0]: " + location[0]);
        if (x < 0) {
            x = 0;
        }
        if (location[0] + deskWidth / 2 + shadeWidth / 2 > SystemUtils.getScreenWidth(getContext())) {
            x = SystemUtils.getScreenWidth(getContext()) - mEpisodePoints.getMeasuredWidth();
        }
        if (location[0] > SystemUtils.getScreenWidth(getContext())) {
            x = 0;
        }

        if (location[0] < 0) {
            x = SystemUtils.getScreenWidth(getContext()) - mEpisodePoints.getMeasuredWidth();
        }
//        AppLogger.INSTANCE.e("deskWidth : " + deskWidth + " , shadeWidth: " + shadeWidth + ", x: " + x + ", location[0]: " + location[0]);
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mEpisodePoints.getLayoutParams();
        layoutParams.leftMargin = x;
        mEpisodePoints.setLayoutParams(layoutParams);
    }

    private String getPoint(String pointsStr) {
        String[] points = null;
        if (pointsStr.contains("|")) {
            points = pointsStr.split("\\|");
        }
        if (points != null && points.length > 0) {
            int index = points[0].indexOf("-");
            if (index < 0) {
                return points[0];
            } else {
                if (points[0].length() > (index + 1)) {
                    return points[0].substring(index + 1);
                } else {
                    return "";
                }
            }
        } else {
            return pointsStr;
        }
    }

    private void setPointsDisplay(View view, EpisodeVideos.Video point) {
        if (point != null && !TextUtils.isEmpty(point.points)) {
            String video_point = getPoint(point.points);
            if (mEpisodePoints != null && video_point != null && !TextUtils.isEmpty(video_point)) {
                mEpisodePoints.setText(video_point);
                int screenWidth = SystemUtils.getScreenWidth(getContext());
                int[] location = new int[2];
                view.getLocationOnScreen(location);
                if (location[0] >= screenWidth) {
                    setPointsPosition(view, video_point);
                    mEpisodePoints.setVisibility(View.VISIBLE);
                    setTVOnFocus(mEpisodePoints);
                } else {
                    setPointsPosition(view, video_point);
                    mEpisodePoints.setVisibility(View.VISIBLE);
                    setTVOnFocus(mEpisodePoints);
                }

            } else {
                if (mEpisodePoints != null) {
                    mEpisodePoints.setVisibility(View.INVISIBLE);
                    setTVUnFocus(mEpisodePoints);
                }
            }
        }
    }

    @Override
    protected void initUI() {
        if (recommendVideos != null) {
            for (int i = 0; i < recommendVideos.size(); i++) {
                // 获得一个LayoutInflater的实例
                LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);

                // 使用inflater来加载布局文件
                View view = inflater.inflate(R.layout.fragment_episode_vrs_recommemd_item, null);

                // 设置View的属性
                LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                        getContext().getResources().getDimensionPixelSize(R.dimen.x333),
                        getContext().getResources().getDimensionPixelSize(R.dimen.y90)
                );
                int left;
//            if (dataSize == 0 && i == 0) {
//                left = getContext().getResources().getDimensionPixelSize(R.dimen.x92);
//            } else {
                left = getContext().getResources().getDimensionPixelSize(R.dimen.x17);
//            }
                layoutParams.setMargins(left, 0, 0, 0);

                view.setLayoutParams(layoutParams);

                // 获取父视图并将加载的布局添加到父视图中
                mRootView.addView(view);

            }

        }
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            View viewGroup = mRootView.getChildAt(i).findViewById(R.id.episode_tv);
            if (Util.getManufactureName().equalsIgnoreCase("Rockchip")) {
                viewGroup.setBackgroundResource(R.drawable.episode_vrs_item_corner);
            }
            ((VrsTextView) viewGroup).setIsMenu(isMenu);
            viewGroup.setOnFocusChangeListener((v, hasFocus) -> {
                AppLogger.INSTANCE.e("v :" + ((VrsTextView) v).getText() + " , hasFocus : " + hasFocus + " ,getUserVisibleHint()" + getUserVisibleHint());
                if (hasFocus) {
                    if (mFocusBorderView != null) {
                        mFocusBorderView.setFocusView(v);
                        FocusUtil.setFocusAnimator(v, mFocusBorderView, 1.07f, 100);
                    }
                    if (!getUserVisibleHint()) {
                        if (mEpisodePoints != null) {
                            mEpisodePoints.setVisibility(View.INVISIBLE);
                            setTVUnFocus(mEpisodePoints);
                        }
                        return;
                    }

                    EpisodeVideos.Video point = null;
                    try {
                        point = (EpisodeVideos.Video) v.getTag();
                    } catch (Exception e) {
//                            e.printStackTrace();
                    }
                    setPointsDisplay(v, point);

                } else {
                    if (mFocusBorderView != null) {
                        mFocusBorderView.setUnFocusView(v);
                        FocusUtil.setUnFocusAnimator(v, 100);
                    }
                    if (mEpisodePoints != null) {
                        mEpisodePoints.setVisibility(View.INVISIBLE);
                        setTVUnFocus(mEpisodePoints);
                    }
                }
            });
            viewGroup.setOnKeyListener(this);
            viewGroup.setOnClickListener(this);
        }
        int dataSize = mEnd - mStart + 1;
        if (dataSize < 0) dataSize = 0;
        if (dataSize < mRootView.getChildCount()) {
            for (int i = dataSize; i < 10; i++) {
                mRootView.getChildAt(i).setVisibility(View.GONE);
            }
            try {
                if (dataSize == 0) {
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mRootView.getChildAt(10).getLayoutParams();
                    layoutParams.setMargins(getContext().getResources().getDimensionPixelSize(R.dimen.x92), 0, 0, 0);
                    mRootView.getChildAt(10).setLayoutParams(layoutParams);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    @Override
    public void setUI(List<EpisodeVideos.Video> videoList) {
        LibDeprecatedLogger.d("isMenu : " + isMenu);
        if (mRootView == null) return;
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            View childView = mRootView.getChildAt(i);
            if (childView.getVisibility() != View.VISIBLE) {
                continue;
            }
            int num;
            if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
                num = mStart + i;
            } else {
                num = mEnd - i;
            }
            boolean found = false;
            VrsTextView titleView = (VrsTextView) childView.findViewById(R.id.episode_tv);

            //推荐
            if (videoList == null || i >= videoList.size() || videoList.get(i) == null) {
                try {
                    titleView.setText(recommendVideos.get(i % 10).getAlbumName());
                    titleView.setBadge(R.drawable.episode_item_recommend);
                    titleView.setTag(recommendVideos.get(i % 10));
                    found = true;
                }catch (Exception e) {
                    e.printStackTrace();
                }
            } else  {
                //非推荐
                if (videoList.get(i).tvStype == 38) { //番外彩蛋
                    titleView.setText(videoList.get(i).tvName);
                } else {
                    titleView.setText(String.valueOf(num));
                }
                titleView.setEnabled(true);
                titleView.setTextColorEnable(true);
                for (int j = 0; j < videoList.size(); j++) {
                    //针对更多分类，音乐频道做的处理，根据服务端反馈,只有tvStype == 2  tvFormalOrder才有用
                    if (videoList.get(j).tvStype != 2 && videoList.get(j).tvStype != 38) {
                        videoList.get(j).tvStype = 1;
                    }
                    if (((videoList.get(j).tvStype == 38 ||videoList.get(j).tvStype == 1) && videoList.get(j).videoOrder == num)
                            || (videoList.get(j).tvStype != 1 && videoList.get(j).tvFormalOrder == num)) {
                        if (videoList.get(j).tvStype != 1 && videoList.get(j).tvStype != 38) {
                            titleView.setBadge(R.drawable.episode_item_trailer);
                        } else if (videoList.get(j).tvSetIsFee == 1) {
                            if (videoList.get(j).isSyncBroadcast == 1) {
//                            if (isFanWai) {
                                titleView.setBadge(R.drawable.episode_item_vip);
//                            } else {
//                                titleView.setBadge(R.drawable.episode_item_forestall);
//                            }
                            } else {
                                if (mCateCode == Constant.EDU_CATE_CODE) { // 教育测试环境
                                    titleView.setBadge(R.drawable.episode_item_fee);
                                } else {
                                    titleView.setBadge(R.drawable.episode_item_vip);
                                }
                            }
                        }

                        if (((videoList.get(j).tvStype == 1 || videoList.get(j).tvStype == 38 ) && videoList.get(j).videoOrder == mVideoOrder && mEpisodeIsSelected)
                                || (videoList.get(j).tvStype != 1 && videoList.get(j).tvStype != 38 && videoList.get(j).tvFormalOrder == mVideoOrder && mEpisodeIsSelected)) {
                            childView.setSelected(true);
                            if (mRootView.hasFocus()) {
                                titleView.requestFocus();
                            }
                            mPlayingView = (PlayingView) childView.findViewById(R.id.episode_playing_view);
                            if (mPlayingView != null) {
                                mPlayingView.show();
                            }
                        }
                        titleView.setTag(videoList.get(j));
                        childView.setEnabled(true);
                        if (childView.isFocused()) {
                            EpisodeVideos.Video point = videoList.get(j);
                            setPointsDisplay(childView, point);
                        }
                        found = true;
                        break;
                    }
                }
            }

            if (!found) {
                childView.setTag(Constant.EPISODE_OFFLINE);
                childView.setEnabled(true);
                titleView.setTextColorEnable(false);
            }
        }
    }

    @Override
    public void onPageScrollStateStop() {
        if (!isVisible()) {
            return;
        }
        if (mRootView != null) {
            View focusView = mRootView.getFocusedChild();
            if (focusView != null) {
                EpisodeVideos.Video point = null;
                try {
                    point = (EpisodeVideos.Video) focusView.findViewById(R.id.episode_tv).getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                setPointsDisplay(focusView, point);
            }
            recoverFocus(focusView);
        }
    }
}
