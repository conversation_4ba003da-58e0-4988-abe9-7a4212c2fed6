package com.sohuott.tv.vod.view.scalemenu.presenter

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.bumptech.glide.Glide
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.getResDrawable
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.scaleXY
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.customview.CircleImageView
import com.sohuott.tv.vod.view.scalemenu.PileLayout
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem


/**
 * Description: 只看TA
 * Created by cuipengyu on 2023/7/6.
 * Last Modified: 2023/7/6
 * 前端根据V4接口是否下发"只看TA"，判断是否展示“只看TA”入口。
 * 选择只看TA明星片段后，"只看TA"入口名称显示为已选明星名称。
 * 如选择只看张若昀片段，则入口显示“只看张若昀”。
 * 如选择只看张若昀&李现&李某片段，则入口显示“只看张若昀..."
 * 如果恢复为“观看完整版”，则入口名称显示“只看TA"。
 */
class ScaleContentMenuOnlySeePresenter constructor(private val context: Context) :
        DefaultPresenter(R.layout.item_sacle_menu_only_see) {
    override fun defaultBindViewHolder(
            viewHolder: LeanBackViewHolder,
            item: Any?,
            payloads: MutableList<Any>?
    ) {
        item as ScaleContentOnlySeeMenuItem
        val layout = viewHolder.getView<PileLayout>(R.id.layout_only_see_img)
        val clOnlySeeAll = viewHolder.getView<ConstraintLayout>(R.id.cl_only_see_all)
        val tvContent = viewHolder.getView<TextView>(R.id.tv_only_see_name)
        val tvAllContent = viewHolder.getView<TextView>(R.id.tv_only_see_all_name)
        val tvTime = viewHolder.getView<TextView>(R.id.tv_only_see_all_time)


        //判断是否是观看全部 隐藏相应控件
        layout.removeAllViews()
        if (item.id == "0") {
            tvContent.gone()
            layout.gone()
            tvTime.gone()
            tvAllContent.visible()
            tvAllContent.text = item.name
        } else {
            tvContent.visible()
            layout.visible()
            tvTime.visible()
            tvAllContent.gone()
            tvContent.text = item.name
            tvTime.text = item.allTime
            tvContent.setPadding(tvContent.left, tvContent.top, tvContent.right, 0)
            tvTime.setPadding(tvTime.left, 0, tvTime.right, tvTime.bottom)
            item.imagesUrl.forEach {
                val imageView: CircleImageView =
                        LayoutInflater.from(context)
                                .inflate(R.layout.item_praise, layout, false) as CircleImageView
                Glide.with(context).load(it).into(imageView)
                layout.addView(imageView);
            }
        }

        clOnlySeeAll.setOnFocusChangeListener { v, hasFocus ->
            //实现选中时 跑马灯效果
            if (hasFocus) {
                //跑马灯效果
                tvContent.marqueeRepeatLimit=-1
                tvContent.isSelected = true
                tvContent.ellipsize = TextUtils.TruncateAt.MARQUEE
            } else {
                //省略号
                tvContent.isSelected = false
                tvContent.ellipsize = TextUtils.TruncateAt.END
            }
            if (hasFocus) {
                tvContent.setTextColor(
                        ContextCompat.getColor(
                                context, R.color.tv_color_e8e8ff
                        )
                )
                tvAllContent.setTextColor(
                        ContextCompat.getColor(
                                context, R.color.tv_color_e8e8ff
                        )
                )
                tvTime.setTextColor(
                        ContextCompat.getColor(
                                context, R.color.tv_color_e8e8ff
                        )
                )
                clOnlySeeAll.background =
                        context.getResDrawable(R.drawable.bg_select_focus_e4705c_radius_11)
                v.scaleXY(1.1f)
            } else {
                if (item.hasCurrentSelected) {
                    tvContent.setTextColor(
                            ContextCompat.getColor(
                                    context, R.color.tv_color_ff6247
                            )
                    )
                    tvTime.setTextColor(
                            ContextCompat.getColor(
                                    context, R.color.tv_color_ff6247
                            )
                    )
                    tvAllContent.setTextColor(
                            ContextCompat.getColor(
                                    context, R.color.tv_color_ff6247
                            )
                    )
                } else {
                    tvContent.setTextColor(
                            ContextCompat.getColor(
                                    context, R.color.tv_color_e8e8ff
                            )
                    )
                    tvTime.setTextColor(
                            ContextCompat.getColor(
                                    context, R.color.tv_color_cce8e8ff
                            )
                    )
                    tvAllContent.setTextColor(
                            ContextCompat.getColor(
                                    context, R.color.tv_color_e8e8ff
                            )
                    )
                }
                clOnlySeeAll.background =
                        context.getResDrawable(R.drawable.bg_radius_10_color_2effffff)
                v.scaleXY(1f)
            }
        }


        if (item.hasCurrentSelected) {
            tvContent.setTextColor(
                    ContextCompat.getColor(
                            context, R.color.tv_color_ff6247
                    )
            )
            tvAllContent.setTextColor(
                    ContextCompat.getColor(
                            context, R.color.tv_color_ff6247
                    )
            )
            tvTime.setTextColor(
                    ContextCompat.getColor(
                            context, R.color.tv_color_ff6247
                    )
            )
        } else {
            tvContent.setTextColor(
                    ContextCompat.getColor(
                            context, R.color.tv_color_e8e8ff
                    )
            )
            tvTime.setTextColor(
                    ContextCompat.getColor(
                            context, R.color.tv_color_cce8e8ff
                    )
            )
            tvAllContent.setTextColor(
                    ContextCompat.getColor(
                            context, R.color.tv_color_e8e8ff
                    )
            )
        }
    }
}