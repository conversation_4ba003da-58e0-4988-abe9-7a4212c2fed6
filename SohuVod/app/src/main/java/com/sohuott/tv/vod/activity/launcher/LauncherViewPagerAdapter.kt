package com.sohuott.tv.vod.activity.launcher

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.sohuott.tv.vod.fragment.HomeAllCategoryFragmentNew
import com.sohuott.tv.vod.fragment.MyFragment
import com.sohuott.tv.vod.fragment.lb.HomeContentFragment
import com.sohuott.tv.vod.lib.model.launcher.HomeTab
import com.sohuott.tv.vod.lib.utils.Constant
import java.lang.ref.WeakReference

class LauncherViewPagerAdapter : FragmentStateAdapter {

    private val fragments = HashMap<Int, WeakReference<Fragment>>()
    private var dataBeans: MutableList<HomeTab.TabItem?>? = null

    constructor  (fragmentActivity: FragmentActivity) :
            super(fragmentActivity.supportFragmentManager, fragmentActivity.lifecycle)


    constructor(fragment: Fragment) :
            super(fragment.childFragmentManager, fragment.lifecycle)


    override fun getItemCount(): Int {
        return dataBeans?.size ?: 0
    }

    override fun createFragment(position: Int): Fragment {
        Log.e("LauncherAdapter", "createFragment position:$position")
        val type = dataBeans!![position]?.type
        var fragment: Fragment? = null
        val bundle = Bundle()
        if (type == Constant.TYPE_MY) {
            fragment = MyFragment()
        } else if (type == Constant.TYPE_CLASSIFY) {
            fragment = HomeAllCategoryFragmentNew()
        } else if (type == Constant.TYPE_CAROUSEL) {
//            fragment = new HomeCarouselFragment();
        } else {
//            if (fragments.get(position) != null) {
//                fragment = fragments.get(position).get();
//                if (fragment != null) {
//                    return fragment;
//                }
//            }
            fragment = HomeContentFragment()
        }
        bundle.putInt("type", type ?: -1)
        bundle.putInt("ottCategoryId", dataBeans!![position]?.ottCategoryId ?: 0)
        bundle.putLong("cateCode", dataBeans!![position]?.cateCode ?: 0)
        bundle.putInt("subClassifyId", dataBeans!![position]?.subClassifyId ?: 0)
        bundle.putLong("id", dataBeans!![position]?.id ?: 0)
        bundle.putInt("position", position)
        fragment!!.arguments = bundle
        fragments[position] = WeakReference(fragment)
        return fragment!!
    }

    fun getFragment(position: Int): Fragment? {
        return if (fragments == null || fragments[position] == null) {
            createFragment(position)
        } else fragments[position]!!.get()
    }

    fun setData(dataBeans: MutableList<HomeTab.TabItem?>?) {
        Log.e("LauncherAdapter", "setData dataBeans:${dataBeans.toString()}")
        this.dataBeans = dataBeans
    }
}