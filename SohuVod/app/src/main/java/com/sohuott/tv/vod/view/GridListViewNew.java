package com.sohuott.tv.vod.view;

import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.lib.model.MenuListBean;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wenjingbian on 2017/5/31.
 */

public interface GridListViewNew {

    /**
     * Update left list view
     *
     * @param menuDataList data list
     */
    void updateLeftListView(List<MenuListBean.MenuDate> menuDataList);

    /**
     * Display error view when failed to request left list data
     */
    void displayLeftErrorView();

    /**
     * Update video list view on the right content
     *
     * @param dataEntity  video data list
     * @param fromFilter  is from filter
     * @param subCateCode the subCateCode when requesting
     */
    void updateGridListView(VideoGridListBean.DataEntity dataEntity, boolean fromFilter, int subCateCode);

    /**
     * Add video data to the exist video data list
     *
     * @param dataEntity the newest video data list
     */
    void addGridListItems(VideoGridListBean.DataEntity dataEntity);

    void addGridListItemsError();

    /**
     * Display error view when failed to request video data
     *
     * @param errorStr string resource
     */
    void displayGridListErrorView(String errorStr);

    /**
     * Display error view when failed to request video data with filter params
     *
     * @param errorStr string resource
     */
    void displayGridListFilterErrorView(String errorStr);

    /**
     * Display loading view while requesting video data
     */
    void displayGridListLoadingView();

    /**
     * Display loading view while requesting video data with filter params
     */
    void displayGridListFilterLoadingView();

    /**
     * Set header data list when requested header data successfully
     *
     * @param labelItemArrayList header data list
     */
    void setGridListHeaderData(ArrayList<AllLabel.LabelItem> labelItemArrayList);

    /**
     * Catch error when failed to request header data
     */
    void catchGridListHeaderDataError();

    void onRequestFilterListDone();

}
