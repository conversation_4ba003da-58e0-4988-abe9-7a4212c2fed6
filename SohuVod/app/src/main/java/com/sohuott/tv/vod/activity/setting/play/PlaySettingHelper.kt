package com.sohuott.tv.vod.activity.setting.play

import android.annotation.SuppressLint
import android.content.Context
import com.lib_dlna_core.SohuDlnaManger
import com.sh.ott.video.player.PlayerConstants
import com.sohu.lib_utils.PrefUtil
import com.sohu.ott.base.lib_user.UserConfigHelper
import com.sohu.ott.base.lib_user.UserConstants
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.videodetail.activity.state.ResolutionApp

@SuppressLint("StaticFieldLeak")
object PlaySettingHelper {
    private var context: Context? = null
    private var playType: Int = 0

    private var mHelper: LoginUserInformationHelper? = null

    @JvmStatic
    fun setPlayType(type: Int) {
        playType = type
    }

    fun getPlayType(): Int {
        return playType
    }

    @JvmStatic
    fun initContext(context: Context) {
        this.context = context
        mHelper = LoginUserInformationHelper.getHelper(context)
    }

    fun getIsLogin(): Boolean {
        return mHelper?.isLogin == true
    }

    /**
     * 设置是否设置片头片尾
     */
    @JvmStatic
    fun setNeedSkipHeaderAndEnd(skip: Boolean) {
        if (SohuDlnaManger.getInstance().getIsOttSohuVideo()) {
            PrefUtil.putBoolean("dlna_skip", skip)
        } else {
            PrefUtil.putBoolean("skip", skip)
        }
    }

    /**
     * 获取是否设置片头片尾
     */
    @JvmStatic
    fun getNeedSkipHeaderAndEnd(): Boolean {
        if (SohuDlnaManger.getInstance().getIsOttSohuVideo()) {
            return PrefUtil.getBoolean("dlna_skip", true)
        } else {
            return PrefUtil.getBoolean("skip", true)
        }
    }

    /**
     * 设置画面尺寸
     */
    @JvmStatic
    fun setVideoViewLayoutRatioType(type: Int) {
        if (SohuDlnaManger.getInstance().getIsOttSohuVideo()) {
            PrefUtil.putInt("dlna_Ratio", type)
        } else {
            PrefUtil.putInt("Ratio_type", type)

        }
    }

    /**
     * 获取画面尺寸
     */
    @JvmStatic
    fun getVideoViewLayoutRatioType(): Int {
        if (SohuDlnaManger.getInstance().getIsOttSohuVideo()) {

            return PrefUtil.getInt("dlna_Ratio", PlayerConstants.ScreenAspectRatio.DEFAULT)
        } else {
            return PrefUtil.getInt("Ratio_type", PlayerConstants.ScreenAspectRatio.DEFAULT)

        }
    }

    /**
     * 设置播放器是否更改过
     */
    @JvmStatic
    fun setPlayTypeHasChange(isChange: Boolean) {
        PrefUtil.putBoolean("play_type_change", isChange)
    }

    /**
     * 获取播放器是否更改过
     */
    @JvmStatic
    fun getPlayTypeHasChange(): Boolean {
        return PrefUtil.getBoolean("play_type_change", false)
    }

    /**
     * 设置倍速开关
     */
    @JvmStatic
    fun setPlaySpeedIsOpen(isOpen: Boolean) {
        PrefUtil.putBoolean("play_speed_open", isOpen)
    }

    /**
     * 获取倍速开关
     */
    @JvmStatic
    fun getPlaySpeedIsOpen(): Boolean {
        return PrefUtil.getBoolean("play_speed_open", true)
    }

    @JvmStatic
    fun setPlaySpeed(speed: Float) {
        if (SohuDlnaManger.getInstance().getIsOttSohuVideo()) {

            PrefUtil.putFloat("dlna_play_speed", speed)
        } else {
            PrefUtil.putFloat("play_speed", speed)

        }
    }

    @JvmStatic
    fun getPlaySpeed(): Float {
        if (SohuDlnaManger.getInstance().getIsOttSohuVideo()) {
            return PrefUtil.getFloat("dlna_play_speed", UserConstants.PE_PLAY_RATE_NORMAL)
        } else {
            return PrefUtil.getFloat("play_speed", UserConstants.PE_PLAY_RATE_NORMAL)
        }
    }

    /**
     * 获取设置的清晰度
     */
    @JvmStatic
    fun getPlayClarity(): Int {
        if (SohuDlnaManger.getInstance().getIsDlna()) {
            return PrefUtil.getInt("dlna_play_clarity", getDefaultClarity())
        } else {
            return PrefUtil.getInt("play_clarity", getDefaultClarity())
        }
//        val clarity = if (getIsLogin()){
//            if (is265) Constant.DEFINITION_SUPER265 else Constant.DEFINITION_SUPER
//        }else{
//            if (is265) Constant.DEFINITION_HIGH265 else Constant.DEFINITION_HIGH
//        }
    }

    @JvmStatic
    fun getDefaultClarity(): Int {
        val clarity = if (getIsLogin() && !getClarityHasChange()) {
            getDefaultResolutionApp().appValue
        } else {
            getDefaultResolutionApp().appValue
        }
        return clarity
    }

    fun getDefaultResolutionApp(): ResolutionApp {
        val clarity = if (getIsLogin() && !getClarityHasChange()) {
            ResolutionApp.APP_SUPER
        } else {
            ResolutionApp.APP_HIGH
        }
        return clarity
    }

    fun setDlnaDefaultClarity() {

    }

    /**
     * 设置播放器是否更改过
     */
    @JvmStatic
    fun setClarityHasChange(isChange: Boolean) {
        PrefUtil.putBoolean("play_clarity_change", isChange)
    }

    /**
     * 获取播放器是否更改过
     */
    @JvmStatic
    fun getClarityHasChange(): Boolean {
        return PrefUtil.getBoolean("play_clarity_change", false)
    }


    private val is265
        get() = Util.getH265(context) == 1


    /**
     * 设置清晰度
     */
    @JvmStatic
    fun setPlayClarity(clarity: Int) {
        if (SohuDlnaManger.getInstance().getIsDlna()) {
            setDlnaPlayClarity(clarity)
        } else {
            PrefUtil.putInt("play_clarity", clarity)
        }
    }

    @JvmStatic
    fun setDlnaPlayClarity(clarity: Int) {
        PrefUtil.putInt("dlna_play_clarity", clarity)
//        SohuDlnaManger.getInstance().mCurrentPlayDefinition = clarity

//                            SohuDlnaManger.getInstance().setMCurrentPlayDefinition(current);
//        getInstance().sendPlayDefinition(clarity)

    }

    /**
     * 设置是否开启自动清晰度
     */
    @JvmStatic
    fun setPlayAutoClarityIsOpen(isOpen: Boolean) {
        PrefUtil.putBoolean("play_auto_clarity", isOpen)
    }

    @JvmStatic
    fun getPlayAutoClarityIsOpen(): Boolean {
        return PrefUtil.getBoolean("play_auto_clarity", false)
    }

    @JvmStatic
    fun getSohuDlnaDefinition(definition: String?): Int {
        if (definition.isNullOrEmpty()) {
            return Constant.DEFINITION_HIGH
        }
        return definition.toInt()
    }


    /**
     * 设置播放器是否更改过
     */
    @JvmStatic
    fun setH265HasChange(isChange: Boolean) {
        UserConfigHelper.setH265HasChange(isChange)
    }

    /**
     * 获取播放器是否更改过
     */
    @JvmStatic
    fun getH265HasChange(): Boolean {
        return UserConfigHelper.getH265HasChange()
    }


}