package com.sohuott.tv.vod.data;

import android.content.Context;
import android.util.SparseArray;

import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AdvertisingCopyModel;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.model.LocationConfigInfo;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohu.lib_utils.PrefUtil;
import com.sohuott.tv.vod.model.HomeDataMode;
import com.sohuott.tv.vod.task.IDownloadListener;
import com.sohuott.tv.vod.utils.DownloadUtil;

import java.io.File;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by fenglei on 16-4-5.
 */
public class HomeData {
    public static final int HOME_ITEM_EPISODE_LABEL = 0;
    public static final int HOME_ITEM_CATEGORY_SMALL = 1;
    public static final int HOME_ITEM_HOR_ALBUM = 2;
    public static final int HOME_ITEM_CATEGORY_BIG = 3;
    public static final int HOME_ITEM_VERTICAL_ALBUM = 4;
    public static final int HOME_ITEM_PGC_PRODUCER = 5;
    public static final int HOME_ITEM_WHEELVIEW = 6;
    public static final int HOME_ITEM_EPISODE_SMALL_ITEM_BLOCK = 7;

    public static final String HOME_LEFT_UP_EDGE_ITEM = "home_left_up_edge_item";
    public static final String HOME_LEFT_DOWN_EDGE_ITEM = "home_left_down_edge_item";
    public static final String HOME_RIGHT_UP_EDGE_ITEM = "home_right_up_edge_item";
    public static final String HOME_RIGHT_DOWN_EDGE_ITEM = "home_right_down_edge_item";
    public static final String HOME_LEFT_AND_RIGHT_EDGE_ITEM = "home_left_and_right_edge_item";
    //中立的，如更多分类第一个ITEM
    public static final String HOME_LEFT_MIDDLE_EDGE_ITEM = "home_left_middle_edge_item";
    public static final String HOME_NORMAL_RECYCLERVIEW_ITEM = "home_normal_recyclerview_item";
    private static final String TAG = "HomeTabData";
    private static AdvertisingCopyModel sAdvertisingCopyModel;
    private static LocationConfigInfo.DataBean sLocationConfigInfo;
    private static volatile HomeData instance = null;
    public static int sDtsLabelId = -1;
    public static boolean sBootOrHomeIsStarted = false;
    public static boolean sIsHomeCreate = false;
    private SparseArray<HomeDataMode> mHomeDataModeSparseArray = new SparseArray<>();

    private HomeData() {
    }

    public static HomeData getInstance() {
        if(instance == null) {
            synchronized (HomeData.class) {
                if(instance == null) {
                    instance = new HomeData();
                }
            }
        }
        return instance;
    }

    public HomeDataMode getHomeFragmentData(int key){
        return mHomeDataModeSparseArray.get(key);
    }

    public void setHomeFragmentData(int key, int type, boolean isUpdate,HomeRecommendBean value){
        HomeDataMode homeDataMode = new HomeDataMode();
        homeDataMode.setType(type);
        homeDataMode.setRecommendBean(value);
        homeDataMode.setUpdate(isUpdate);
        mHomeDataModeSparseArray.append(key,homeDataMode);
    }

    public void setHomeFragmentData(int key, int type, boolean isUpdate,VideoGridListBean value){
        HomeDataMode homeDataMode = new HomeDataMode();
        homeDataMode.setType(type);
        homeDataMode.setVideoGridListBean(value);
        homeDataMode.setUpdate(isUpdate);
        mHomeDataModeSparseArray.append(key,homeDataMode);
    }

    public void setHomeFragmentData(int key, HomeDataMode value){
        mHomeDataModeSparseArray.append(key,value);
    }

    public void clearAllHomeFragmentData(){
        mHomeDataModeSparseArray.clear();
    }

    public static AdvertisingCopyModel getAdvertisingCopy(){
        return sAdvertisingCopyModel;
    }

    public static void setAdvertisingCopy(AdvertisingCopyModel model){
        sAdvertisingCopyModel = model;
    }

    public static LocationConfigInfo.DataBean getLocationConfigInfo(final Context context){
        if(context != null){
            LibDeprecatedLogger.d(context.getFilesDir().toString() + File.separator + Constant.JS_BUNDLE_LOCAL_FILE);
        }
        if(sLocationConfigInfo != null){
            return sLocationConfigInfo;
        }else {
            NetworkApi.getLocationConfigInfo(new DisposableObserver<LocationConfigInfo>() {
                @Override
                public void onNext(final LocationConfigInfo value) {
                    if(value != null && value.getData() != null ){
                        sLocationConfigInfo = value.getData();
                        if(context == null){
                            return;
                        }
                        PrefUtil.putString("config", "coming_id", value.getData().ComingSoonId);
                        File file = new File(context.getFilesDir(), Constant.JS_BUNDLE_LOCAL_FILE);
                        String preRnMD5 = PrefUtil.getString("config","rn_md5","");
                        if (preRnMD5.equals("") || !preRnMD5.equals(value.getData().getLuckydraw().getMd5()) || !file.exists()) {
                            new Thread(new Runnable() {
                                @Override
                                public void run() {
                                    DownloadUtil.downloadFile(new IDownloadListener() {
                                        @Override
                                        public void setNetworkResponseCode(int networkResponseCode) {
                                            LibDeprecatedLogger.d(networkResponseCode + "");
                                        }

                                        @Override
                                        public void updateProgress(int progress) {
                                            if (progress == 100) {
                                                LibDeprecatedLogger.d("download finish");
                                                PrefUtil.putString("config","rn_md5",value.getData().getLuckydraw().getMd5());
                                            }

                                        }
                                    },value.getData().getLuckydraw().getFile(),context.getFilesDir().toString() + File.separator + Constant.JS_BUNDLE_LOCAL_FILE);
                                }
                            }).start();
                        }

                        if (value != null && value.getData() != null && !StringUtil.isEmpty(value.getData().getTimeinteval())) {
                            PrefUtil.putString( "config", "time_interval", value.getData().getTimeinteval());
                        } else {
                            PrefUtil.putString( "config", "time_interval", "");
                        }
                    }
                }

                @Override
                public void onError(Throwable e) {

                }

                @Override
                public void onComplete() {

                }
            });
            return null;
        }
    }

}
