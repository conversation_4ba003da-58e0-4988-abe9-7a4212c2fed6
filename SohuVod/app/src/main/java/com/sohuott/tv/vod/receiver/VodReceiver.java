package com.sohuott.tv.vod.receiver;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;

import com.sohu.lib_utils.Md5Utils;
import com.sohu.lib_utils.NetworkUtils;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.activity.NewNetworkDialogActivity;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.ActivityManagerUtil;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.CancelInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActionUtil;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.SyncHistoryAndCollectionUtil;
import com.sohuott.tv.vod.utils.UserPayStatusHelper;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by yizhang210244 on 2017/11/23.
 */

public class VodReceiver extends BroadcastReceiver{
    private LoginUserInformationHelper mHelper;
    @Override
    public void onReceive(Context context, Intent intent) {
        if (mHelper == null) {
            mHelper = LoginUserInformationHelper.getHelper(context);
        }
        if (intent.getAction().equals(ConnectivityManager.CONNECTIVITY_ACTION)||
                intent.getAction().equalsIgnoreCase(ActionUtil.ACTION_NET_CHANGED)) {
            LibDeprecatedLogger.d("CONNECTIVITY_ACTION");
            if (!NetworkUtils.isConnected(context)) {
                String runningActivityName =  "";
                Activity activity = ActivityManagerUtil.getTopActivity();
                if(activity != null){
                    runningActivityName = activity.getClass().getName();
                }
                if(!NewNetworkDialogActivity.class.getName().equals(runningActivityName)
                        && !LauncherActivity.class.getName().equals(runningActivityName)
                        && !Util.isAppIsInBackground(context)){
                    LibDeprecatedLogger.d("showDialog");
                    showDialog(context);
                }else {
                    LibDeprecatedLogger.d("do nothing");
                }
            } else {
                LibDeprecatedLogger.d("hideDialog");
                hideDialog();
            }
        }else if(intent.getAction().equals(Constant.RECEIVER_TOKEN_ACTION)){
            AppLogger.v("md5: " + mHelper.getUserId() + "76e9fb60b529a758614bfc9c7f1d8827" + "..." + Md5Utils.crypt(mHelper.getUserId() + "76e9fb60b529a758614bfc9c7f1d8827", "UTF-8"));

            NetworkApi.getCancelInfo(mHelper.getUserId(), Md5Utils.crypt(mHelper.getUserId() + "76e9fb60b529a758614bfc9c7f1d8827", "UTF-8"), new Observer<CancelInfo>() {
                @Override
                public void onSubscribe(Disposable d) {

                }

                @Override
                public void onNext(CancelInfo value) {
                    if (value.getStatus() == -10) {
                        logoutAndToast(context, "您的账号已注销");
                        //clear local data
                        SyncHistoryAndCollectionUtil.clearLocalHistoryAndCollection(context.getApplicationContext());
                    } else if (value.getStatus() == 0) {
                        logoutAndToast(context, "用户信息失效，请重新登录。");
                    }
                }

                @Override
                public void onError(Throwable e) {

                }

                @Override
                public void onComplete() {

                }
            });

        }else if(intent.getAction().equals(Constant.RECEIVER_INVALID_ACTION)){
            logoutAndToast(context, "您的账号已注销");
            //clear local data
            SyncHistoryAndCollectionUtil.clearLocalHistoryAndCollection(context.getApplicationContext());
        }else if(intent.getAction().equals(Constant.RECEIVER_FREEZE_ACTION)){
            logoutAndToast(context, "您的账号已注销");
        }
    }

    private void logoutAndToast(Context context, String content) {
        UserPayStatusHelper.requestLogout();
        ToastUtils.showToast(context, content);
        ActivityLauncher.startLoginActivityWithNewTask(context);
    }



    private void showDialog(Context context) {
        Intent intent = new Intent(context, NewNetworkDialogActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    private void hideDialog() {
        Activity activity = ActivityManagerUtil.getTopActivity();
        if(activity != null && activity instanceof NewNetworkDialogActivity){
            activity.finish();
        }
    }
}
