package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.presenter.launcher.BigPicPresenter;

/**
 * Created by fenglei on 16-6-28.
 */
public class EpisodeBigFragment extends BaseEpisodePicFragment {

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,Bundle savedInstanceState) {
        mRootView = (ViewGroup) inflater.inflate(R.layout.fragment_episode_common_layout, container, false);
        initUI();
        if (mVideoOrder != -1){
            currentPage();
            currentOrder();
            getData(mPage);
        }
        return mRootView;
    }

    @Override
    protected void initUI() {
        super.initUI();
        ViewGroup.LayoutParams params = horizontalGridView.getLayoutParams();
        params.height = getResources().getDimensionPixelOffset(R.dimen.y80);
        horizontalGridView.setLayoutParams(params);
    }

    @Override
    public Presenter getPresenter() {
        return new BigPicPresenter();
    }

}
