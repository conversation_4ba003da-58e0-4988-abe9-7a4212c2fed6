package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.MenuListBean;
import com.sohuott.tv.vod.view.TempletView;

import java.lang.ref.WeakReference;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by music on 17-9-6.
 */

public class TempletPresenterImpl implements TempletPresenter {
    private TempletView mTempletView;
    private int mOttCateId;

    private Observable mObservable;
    public TempletPresenterImpl(TempletView mTempletView, int ottCateId) {
        this.mOttCateId = ottCateId;
        this.mTempletView = new WeakReference<TempletView>(mTempletView).get();
        requestLeftList();
    }

    /**
     * Request left list data
     */
    public void requestLeftList() {
        NetworkApi.getMenuList(mOttCateId, new Observer<MenuListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(MenuListBean value) {
                LibDeprecatedLogger.d("requestLeftList() onNext");
                if (value != null && value.data != null) {
                    List<MenuListBean.MenuDate> mList = value.data;
                    if (mList.size() > 1) {
                        mList.remove(0);
                    }
                    mTempletView.addLeftData(mList);
                } else {
                    if (mTempletView != null) {
//                        mGridListViewNew.displayLeftErrorView();
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("requestLeftList() error, error = " + e.getMessage());
//                if (mGridListViewNew != null) {
//                    mGridListViewNew.displayLeftErrorView();
//                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestLeftList() onComplete");
            }
        });
    }

}
