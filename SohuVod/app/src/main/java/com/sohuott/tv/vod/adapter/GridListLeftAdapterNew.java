package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.content.res.ColorStateList;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.GridListActivityNew;
import com.sohuott.tv.vod.activity.GridListTagActivityNew;
import com.sohuott.tv.vod.lib.model.GridListTagMenuModel;
import com.sohuott.tv.vod.lib.model.MenuListBean;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;

import java.util.List;

/**
 * Created by wenjingbian on 2017/5/31.
 * <p>
 * Adapter for the left vertical recycler view
 */

public class GridListLeftAdapterNew extends RecyclerView.Adapter<GridListLeftAdapterNew.GridListViewViewHolder> {

    /**
     * Interface to monitor selected item
     */
    public interface IGridListLeftListener {
        /**
         * callback when changed selected item
         *
         * @param subCateId subCateId of selected item
         */
        void onChangedTabListener(int subCateId);
    }

    private Context mContext;
    private CustomLinearRecyclerView mRecyclerView;

    private IGridListLeftListener mListener;

    private List<?> mMenuDataList;//data source of left items
    private int mSelectedPos; //currently selected position(default value: 0-->has filter, 1--> no filter)
    private boolean isEnabledRightKey = true;

    public GridListLeftAdapterNew(Context context, CustomLinearRecyclerView recyclerView) {
        this.mContext = context;
        this.mRecyclerView = recyclerView;
        if (context instanceof GridListActivityNew) {
            mSelectedPos = -1;
        }
    }

    @Override
    public GridListViewViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_grid_left_list, parent, false);
        GridListViewViewHolder viewHolder = new GridListViewViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(GridListViewViewHolder holder, int position) {
        //bind text
        if (mContext instanceof GridListActivityNew) {
            holder.textView.setText(((MenuListBean.MenuDate) mMenuDataList.get(holder.getAdapterPosition())).name);
        } else if (mContext instanceof GridListTagActivityNew) {
            holder.textView.setText(((GridListTagMenuModel.DataEntity) mMenuDataList.get(holder.getAdapterPosition())).name);
        }
        ColorStateList colorStateList = mContext.getResources().getColorStateList(R.color.item_hfc_left_text_selector);
        holder.textView.setTextColor(colorStateList);
        holder.textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimensionPixelOffset(R.dimen.x36));
        //update selected item's UI
        if (holder.getAdapterPosition() == mSelectedPos) {
            if (Util.isSupportTouchVersion(mContext)) {
                holder.itemView.setSelected(true);
            } else {
                holder.itemView.requestFocus();
            }
        }
    }

    @Override
    public int getItemCount() {
        return mMenuDataList != null ? mMenuDataList.size() : 0;
    }

    public void setGridListLeftListener(IGridListLeftListener listener) {
        this.mListener = listener;
    }

    /**
     * Set data source
     *
     * @param list data list for adapter
     */
    public void setDataList(List<?> list) {
        if (mContext instanceof GridListTagActivityNew) {
            this.mMenuDataList = list;
        } else if (mContext instanceof GridListActivityNew) {
            if (list != null && list.size() > 1) {
                this.mMenuDataList = list.subList(1, list.size());
            } else {
                this.mMenuDataList = list;
            }
        }
    }

    public List<?> getDataList() {
        return this.mMenuDataList;
    }

    /**
     * Set selected item's position (position == adapterPosition == index in data source)
     *
     * @param position selected item's position
     */
    public void setSelectedPos(int position) {
        this.mSelectedPos = position;
    }

    public int getSelectedPos() {
        return mSelectedPos;
    }

    public void setEnabledRightKey(boolean isEnabledRightKey) {
        this.isEnabledRightKey = isEnabledRightKey;
    }

    public void releaseAll() {
        mContext = null;
        mRecyclerView = null;
        mListener = null;
        if (mMenuDataList != null) {
            mMenuDataList.clear();
            mMenuDataList = null;
        }
    }

    /**
     * Custom ViewHolder
     */
    public class GridListViewViewHolder extends RecyclerView.ViewHolder {

        public TextView textView;

        public GridListViewViewHolder(final View itemView) {
            super(itemView);
            textView = (TextView) itemView.findViewById(R.id.tv_grid_left);

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (mRecyclerView == null) {
                        return;
                    }

                    if (hasFocus) {
                        //set focused item's UI
                        itemView.setSelected(false);
                        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimensionPixelOffset(R.dimen.x40));
                        int focusPos = mRecyclerView.getChildAdapterPosition(v);
                        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                        textView.setMarqueeRepeatLimit(-1);

                        if (focusPos != -1 && focusPos != mSelectedPos) {
                            //updateRightCtn
                            if (mMenuDataList != null && focusPos >= 0 && mMenuDataList.get(focusPos) != null) {
                                if (mContext instanceof GridListActivityNew) {
                                    mListener.onChangedTabListener(((MenuListBean.MenuDate) mMenuDataList.get(focusPos)).id);
                                } else if (mContext instanceof GridListTagActivityNew) {
                                    mListener.onChangedTabListener(((GridListTagMenuModel.DataEntity) mMenuDataList.get(focusPos)).id);
                                }
                            }
                            //update selected item position
                            mSelectedPos = focusPos;
                        }
                    } else {
                        //set unfocused item's UI
                        textView.setEllipsize(TextUtils.TruncateAt.END);
                    }

                }
            });

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    int focusPos = mRecyclerView.getChildAdapterPosition(v);
                    if (focusPos != -1 && focusPos != mSelectedPos) {
                        //update left ui
                        if (mSelectedPos == -1) {
                            ((GridListActivityNew) mContext).setFilterItemSelected(false);
                        } else {
                            RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(mSelectedPos);
                            if (viewHolder != null && viewHolder.itemView != null) {
                                viewHolder.itemView.setSelected(false);
                            }
                        }
                        v.setSelected(true);

                        //notify selected item change to execute certain actions
                        if (mMenuDataList != null && focusPos >= 0 && mMenuDataList.get(focusPos) != null) {
                            if (mContext instanceof GridListActivityNew) {
                                mListener.onChangedTabListener(((MenuListBean.MenuDate) mMenuDataList.get(focusPos)).id);
                            } else if (mContext instanceof GridListTagActivityNew) {
                                mListener.onChangedTabListener(((GridListTagMenuModel.DataEntity) mMenuDataList.get(focusPos)).id);
                            }
                        }
                        //update selected item position
                        mSelectedPos = focusPos;
                    }
                }
            });

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && event.getAction() == KeyEvent.ACTION_DOWN) { //RIGHT key to focus on item in the right content
                        if (!isEnabledRightKey) {
                            return true;
                        }
                        if (mRecyclerView != null) {
                            mSelectedPos = getAdapterPosition();
                        }
                        if (mContext instanceof GridListActivityNew) {
                            boolean rightViewFocused = ((GridListActivityNew) mContext).focusRightView();
                            itemView.setSelected(rightViewFocused);
                        } else if (mContext instanceof GridListTagActivityNew) {
                            itemView.setSelected(((GridListTagActivityNew) mContext).focusTagList());
                        }
                        return true;
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) { //DOWN key to the last item
                        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimensionPixelOffset(R.dimen.x36));
                        if (getAdapterPosition() == mMenuDataList.size() - 1) {
                            v.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_y));
                            return true;
                        } else {
                            isEnabledRightKey = false;
                        }
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) { //UP key to the first item
                        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mContext.getResources().getDimensionPixelOffset(R.dimen.x36));
                        if (getAdapterPosition() == 0) {
                            if (mContext instanceof GridListActivityNew) {
                                mSelectedPos = -1;
                                ((GridListActivityNew) mContext).isFromDown = true;
                            } else if (mContext instanceof GridListTagActivityNew) {
                                mSelectedPos = 0;
                                ((GridListTagActivityNew) mContext).setFocusRoute(true);
                                itemView.setSelected(true);
                            }
                        } else {
                            isEnabledRightKey = false;
                        }

                    }
                    return false;
                }
            });
        }
    }
}
