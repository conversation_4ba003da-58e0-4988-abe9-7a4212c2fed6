package com.sohuott.tv.vod.utils;

import android.content.Context;
import android.text.TextUtils;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.ChildCollection;
import com.sohuott.tv.vod.lib.db.greendao.ChildCollectionDao;
import com.sohuott.tv.vod.lib.db.greendao.Collection;
import com.sohuott.tv.vod.lib.db.greendao.CollectionDao;
import com.sohuott.tv.vod.lib.db.greendao.DaoSessionInstance;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AlbumInfo;
import com.sohuott.tv.vod.lib.model.CancelChasePlayModel;
import com.sohuott.tv.vod.lib.model.ChasePlayModel;
import com.sohuott.tv.vod.lib.model.EduCollectionModel;
import com.sohuott.tv.vod.lib.service.ThirdPartyService;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohu.lib_utils.StringUtil;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.query.WhereCondition;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

public class CollectionRecordHelper {

    /**
     * Custom Listener that will callback in the certain moment
     */
    public interface ICollectionRecordListener {

        /**
         * onAdd() will be call when finished to insert or replace a record item
         *
         * @param aid       Album ID just inserted or replaced record
         * @param isSuccess added result, true stands for success, false stands for failure
         */
        void onAdd(int aid, boolean isSuccess);

        /**
         * onDelete() will be called when finished to delete an appointed record or all records
         *
         * @param aid       Album ID just deleted record. If delete all items, Album ID is -1(ALL_DELETED_RECORDS_ID)
         * @param isSuccess deleted result, true stands for success, false stands for failure
         */
        void onDelete(int aid, boolean isSuccess);

        /**
         * onRequestData() will be called when finished to request all records
         *
         * @param dataList all record items just got from the remote server or the local database
         */
        void onRequestData(List<?> dataList);

        void onRequestMoreData(List<?> dataList);

        /**
         * onRequestData() will be called when finished to select child collection record by aid
         *
         * @param isCollectRecordInChildVersion true means there is the certain record, and false means there is not the certain record.
         */
        void onSelectRecord(boolean isCollectRecordInChildVersion);
    }

    //ID value for ALL_DELETED_RECORDS_ID
    public static final int ALL_DELETED_RECORDS_ID = -1;
    public static final int COLLECTION_SOURCE_VOD = 0;
    private static final int MAX_COLLECTION_NUM = 50;

    private Context mContext;

    private CollectionDao mCollectionDao;
    private ChildCollectionDao mChildCollectionDao;
    private LoginUserInformationHelper mHelper;
    private ICollectionRecordListener mListener;

    //identify whether already login or not
    private boolean isLogin;
    private boolean isChildVersion;
    private volatile int mCurrentPage = 1;
    private volatile boolean hasMoreData = true;
    private volatile boolean isLoadingData = false;

    public CollectionRecordHelper(Context context, boolean isChildVersion) {
        this.mContext = context;
        this.isChildVersion = isChildVersion;
        init();
    }

    /**
     * Set ICollectionRecordListener
     *
     * @param listener instance who implements ICollectionRecordListener and overrides all related functions
     */
    public void setICollectionListener(ICollectionRecordListener listener) {
        this.mListener = listener;
    }

    /**
     * Request all collection records
     * If user has login, get data from the remote server, otherwise, get data from the local database
     */
    public void requestRecordList(boolean isEdu) {
        isLogin = mHelper.getIsLogin();
        if (isEdu) {
            if (isLogin) {
                requestEduRecordListFromCloud();
            } else {
                deleteOverCollectionFromDB();
                requestEduRecordListFromDb();
            }
            return;
        }
        if (isLogin) {
            requestRecordFromCloud(mHelper.getLoginPassport(), mHelper.getLoginToken());
        } else {
            deleteOverCollectionFromDB();
            requestRecordFromDB(UserInfoHelper.getGid(), false);
        }
    }



    /**
     * Check the appointed video whether has been chased or not(only available for the local record)
     *
     * @param aid Album ID of the appointed video
     * @return true stands for video has been chased, otherwise, false stands for video has not been chased
     */
    public boolean requestRecordById(int aid) {
        try {
            if (isChildVersion) {
                ChildCollection childCollection = mChildCollectionDao.queryBuilder()
                        .where(ChildCollectionDao.Properties.Passport.eq(UserInfoHelper.getGid()),
                                ChildCollectionDao.Properties.AlbumId.eq(aid)).unique();
                if (childCollection != null) {
                    return true;
                } else {
                    return false;
                }
            } else {
                Collection collection = mCollectionDao.queryBuilder()
                        .where(CollectionDao.Properties.Passport.eq(UserInfoHelper.getGid()),
                                CollectionDao.Properties.AlbumId.eq(aid)).unique();
                if (collection != null) {
                    return true;
                } else {
                    return false;
                }
            }
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in requestRecordById(): " + e.getMessage());
        }
        return false;
    }



    public void uploadLocalCollection2Cloud(boolean isEdu) {
        List<Collection> collectionList = null;
        deleteOverCollectionFromDB();
        if (isEdu) {
            collectionList = mCollectionDao.queryBuilder().where(
                    getEduCondition(true),
                    CollectionDao.Properties.IsCommit.eq(0)
            ).orderDesc(CollectionDao.Properties.CollectionTime).list();
        } else {
            collectionList = mCollectionDao.queryBuilder().where(
                    getEduCondition(false),
                    CollectionDao.Properties.IsCommit.eq(0))
                    .orderAsc(CollectionDao.Properties.CollectionTime).list();//服务端会根据同步上去的顺序倒序显示，所以这里正序取
        }
        if (collectionList == null || collectionList.size() < 1) {
            return;
        }
        if (isEdu) {
            addEduRecordsToCloud(collectionList);
        } else {
            addRecordsToCloud(collectionList);
        }

    }

    /**
     * Add a new record to the remote server or the local database
     *
     * @param collection instance of Collection you want to add
     */
    public synchronized void addRecord(Collection collection) {
        if (collection == null) {
            return;
        }
        isLogin = mHelper.getIsLogin();
        if (Constant.EDU_CATE_CODE == collection.getCateCode()) {//edu
            if (isLogin) {
                operateEduToCloud(collection.getAlbumId(), 1);
            } else {
                addRecordToDB(collection, true);
            }
            return;
        }

        if (isLogin) {
            addRecordToCloud(collection.getAlbumId());
        } else {
            addRecordToDB(collection, false);
        }
        reportActionToThirdPartner(collection.getAlbumId(), false, collection);
    }



    /**
     * Delete the appointed record from the remote server or the local database
     *
     * @param id       Album ID of video you want to delete
     * @param isChased true stands for video is under updating, false stands for video has finished to update
     */
    public void deleteRecordById(int id, boolean isChased, boolean isEdu) {
        isLogin = mHelper.getIsLogin();
        if (isEdu) {
            if (isLogin) {
                deleteEduRecordFromCloud(id);
            } else {
                deleteEduRecordFromDB(id, isChased);
            }
            return;
        }
        if (isLogin) {
            deleteRecordFromCloud(mHelper.getLoginPassport(), id, mHelper.getLoginToken(), isChased);
        } else {
            deleteRecordFromDB(id, isChased);
        }
    }

    /**
     * Delete all collection records from the remote server or the local database
     */
    public void deleteAllRecord(boolean isEdu) {
        isLogin = mHelper.getIsLogin();
        if (isEdu) {
            if (isLogin) {
                deleteAllEduRecordsFromCloud();
            } else {
                deleteAllEduRecordsFromDB(UserInfoHelper.getGid());
            }
            return;
        }

        if (isLogin) {
            deleteAllRecordsFromCloud(mHelper.getLoginPassport(), mHelper.getLoginToken());
        } else {
            deleteAllRecordsFromDB(UserInfoHelper.getGid(), false);
        }
    }

    public synchronized void deleteAllLocalRecord(boolean isEdu) {
        try {
            List<Collection> collectionList = mCollectionDao.queryBuilder()
                    .where(getEduCondition(isEdu)).list();
            if (collectionList != null && collectionList.size() > 0) {
                for (int i = 0; i < collectionList.size(); i++) {
                    mCollectionDao.delete(collectionList.get(i));
                }

                if (mListener != null) {
                    mListener.onDelete(ALL_DELETED_RECORDS_ID, true);
                }
            } else {
//                if (mListener != null) {
//                    mListener.onDelete(ALL_DELETED_RECORDS_ID, false);
//                }
            }

            reportActionToThirdPartner(ALL_DELETED_RECORDS_ID, false, null);
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in deleteAllRecordFromDB");
        }
    }



    /**
     * Release all resources
     */
    public void releaseAll() {
        mHelper = null;
        mCollectionDao = null;
        mChildCollectionDao = null;
        mListener = null;
        mContext = null;
    }

    /**
     * Initialize parameters
     */
    private void init() {
        if (!isChildVersion) {
            if (mCollectionDao == null) {
                try {
                    mCollectionDao = DaoSessionInstance.getDaoSession(mContext).getCollectionDao();
                } catch (Exception e) {
                    LibDeprecatedLogger.e("Exception when created DaoSessionInstance: " + e.getMessage());
                }
            }
        } else {
            if (mChildCollectionDao == null) {
                try {
                    mChildCollectionDao = DaoSessionInstance.getDaoSession(mContext).getChildCollectionDao();
                } catch (Exception e) {
                    LibDeprecatedLogger.e("Exception when created DaoSessionInstance: " + e.getMessage());
                }
            }
        }

        mHelper = LoginUserInformationHelper.getHelper(mContext);
    }

    /**
     * Query all records from the local database
     *
     * @param passport gid
     */
    private void requestRecordFromDB(String passport, boolean isEdu) {
        try {
            if (isChildVersion) {
                List<ChildCollection> childCollectionList = mChildCollectionDao.queryBuilder().where(
                        ChildCollectionDao.Properties.Passport.eq(passport), getEduCondition(isEdu))
                        .orderDesc(ChildCollectionDao.Properties.CollectionTime)
                        .limit(MAX_COLLECTION_NUM).list();
                if (mListener != null) {
                    mListener.onRequestData(childCollectionList);
                }
            } else {
                List<Collection> collectionList = mCollectionDao.queryBuilder().where(
                        CollectionDao.Properties.Passport.eq(passport), getEduCondition(isEdu))
                        .orderDesc(CollectionDao.Properties.CollectionTime)
                        .limit(MAX_COLLECTION_NUM).list();
                if (mListener != null) {
                    mListener.onRequestData(collectionList);
                }
            }
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in requestRecordFromDB(): " + e.getMessage());
        }
    }

    /**
     * Request all records from the remote server
     *
     * @param passport passport value
     */
    private void requestRecordFromCloud(final String passport, String token) {
        //左侧"收藏记录"上焦点时调用此方法，视为初始化方法，复位page
        mCurrentPage = 1;
        hasMoreData = true;
        NetworkApi.getChasePlayList(passport, token,MAX_COLLECTION_NUM / 2, 1, new Observer<ChasePlayModel>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(ChasePlayModel value) {
                LibDeprecatedLogger.d("requestCollectionList(): onNext()");
                if (value != null && value.data != null) {
                    List<Collection> collectionList = parseData(value, passport);
                    mListener.onRequestData(collectionList);
                    if (collectionList != null && !collectionList.isEmpty()) {
                        mCurrentPage++;
                        hasMoreData = true;
                    } else {
                        hasMoreData = false;
                    }
                } else {
                    mListener.onRequestData(null);
                    hasMoreData = false;
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestCollectionList(): onError()--" + e.getMessage());
                mListener.onRequestData(null);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestCollectionList(): onComplete()");
            }
        });
    }

    public void requestMoreRecordListFromCloud() {
        if (!mHelper.getIsLogin()) {
            return;
        }
        if (isLoadingData) {
            return;
        }
        if (!hasMoreData) {
            return;
        }
        final String passport = mHelper.getLoginPassport();
        final String token = mHelper.getLoginToken();
        NetworkApi.getChasePlayList(passport, token, MAX_COLLECTION_NUM / 2, mCurrentPage, new Observer<ChasePlayModel>() {
            @Override
            public void onSubscribe(Disposable d) {
                isLoadingData = true;
            }

            @Override
            public void onNext(ChasePlayModel value) {
                isLoadingData = false;
                LibDeprecatedLogger.d("requestMoreRecordListFromCloud(): onNext()");
                if (value != null && value.data != null) {
                    List<Collection> collectionList = parseData(value, passport);
                    mListener.onRequestMoreData(collectionList);
                    if (collectionList != null && !collectionList.isEmpty()) {
                        mCurrentPage++;
                        hasMoreData = true;
                    } else {
                        hasMoreData = false;
                    }
                } else {
                    hasMoreData = false;
                }
            }

            @Override
            public void onError(Throwable e) {
                isLoadingData = false;
                LibDeprecatedLogger.e("requestMoreRecordListFromCloud(): onError()--" + e.getMessage());
                mListener.onRequestData(null);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestCollectionList(): onComplete()");
            }
        });
    }

    public List<Collection> parseData(ChasePlayModel value, String passport) {
        List<Collection> collectionList = new ArrayList<>();
        for (AlbumInfo.DataEntity tmpData : value.data) {
            if (tmpData == null) {
                continue;
            }
            Collection collection = new Collection();
            collection.setPassport(passport);
            collection.setAlbumId(tmpData.id);
            collection.setCateCode(tmpData.cateCode);
            collection.setLatestVideoCount(TextUtils.isEmpty(tmpData.latestVideoCount)
                    ? "" : tmpData.latestVideoCount);
            collection.setTvName(TextUtils.isEmpty(tmpData.tvName) ? "" : tmpData.tvName);
            collection.setTvSets(TextUtils.isEmpty(tmpData.tvSets) ? "" : tmpData.tvSets);
            collection.setLatestVideoCount(TextUtils.isEmpty(tmpData.latestVideoCount) ? "" : tmpData.latestVideoCount);
            collection.setTvVerPic(TextUtils.isEmpty(tmpData.tvVerPic) ? "" : tmpData.tvVerPic);
            collection.setIsAudit(tmpData.isAudit);
            collection.setAlbumExtendsPic_640_360(tmpData.albumExtendsPic_640_360);
            collection.setOttFee(tmpData.ottFee);
            collection.setTvIsFee(tmpData.tvIsFee);
            collection.setCornerType(tmpData.cornerType);
            collection.setTvIsEarly(tmpData.tvIsEarly);
            collection.setUseTicket(tmpData.useTicket);
            collection.setPaySeparate(tmpData.paySeparate);
            collectionList.add(collection);
        }
        return collectionList;
    }

    /**
     * 获取本地教育记录列表
     */
    private void requestEduRecordListFromDb() {
        String passport = UserInfoHelper.getGid();
        List<Collection> collectionList = mCollectionDao.queryBuilder().where(
                CollectionDao.Properties.Passport.eq(passport), getEduCondition(true))
                .orderDesc(CollectionDao.Properties.CollectionTime)
                .limit(MAX_COLLECTION_NUM).list();
        if (mListener != null) {
            mListener.onRequestData(collectionList);
        }
    }

    /**
     * 在线获取教育收藏记录列表
     */
    private void requestEduRecordListFromCloud() {
        final String passport;
        if (mHelper.getIsLogin()) {
            passport = mHelper.getLoginPassport();
        } else {
//            return;
            passport = UserInfoHelper.getGid();
        }
        final List<Collection> collectionList = new ArrayList<>();
        NetworkApi.getEduCollectionList(passport, new Observer<EduCollectionModel>() {
            @Override
            public void onSubscribe(Disposable d) {
            }

            @Override
            public void onNext(EduCollectionModel value) {
                if (mListener == null) {
                    return;
                }
                if (value == null) {
                    mListener.onRequestData(null);
                } else if (value.getData() == null || value.getData().size() <= 0) {
                    mListener.onRequestData(collectionList);
                } else {
                    for (EduCollectionModel.DataBean data : value.getData()) {
                        if (data == null) {
                            continue;
                        }
                        Collection collection = new Collection();
                        collection.setPassport(passport);
                        collection.setAlbumId(data.getAlbumId());
                        collection.setCateCode(data.getCategoryCode());
                        collection.setLatestVideoCount(data.getLatestVideoCount() + "");
                        collection.setTvName(TextUtils.isEmpty(data.getTvName()) ? "" : data.getTvName());
                        collection.setTvSets(data.getTvSets() + "");
                        collection.setTvVerPic(TextUtils.isEmpty(data.getAlbumPic_480_660()) ? "" : data.getAlbumPic_480_660());
                        collection.setAlbumExtendsPic_640_360(data.getAlbumPic_640_360());
                        collection.setOttFee(data.getOttFee());
                        collection.setTvIsFee(data.getFee());
                        collection.setCornerType(data.getCornerType());
                        collection.setCollectionTime(data.getCollectionTime());
                        collection.setTvIsEarly(data.getTvIsEarly());
                        collection.setUseTicket(data.getUseTicket());
                        collection.setPaySeparate(data.getPaySeparate());
                        collectionList.add(collection);
                    }
                    mListener.onRequestData(collectionList);
                }
            }

            @Override
            public void onError(Throwable e) {
                if (mListener != null) {
                    mListener.onRequestData(null);
                }
            }

            @Override
            public void onComplete() {
            }
        });
    }


    /**
     * Insert or replace record item to the local database
     *
     * @param collection collection instance you want to insert or replace
     */
    private void addRecordToDB(Collection collection, boolean isEdu) {
        if (collection == null) {
            if (mListener != null) {
                mListener.onAdd(collection.getAlbumId(), false);
            }
            return;
        }
        try {
            collection.setPassport(UserInfoHelper.getGid());
            collection.setIsCommit(0);
            if (isEdu) {
                collection.setCateCode(Constant.EDU_CATE_CODE);
            }
            Collection preCollection = mCollectionDao.queryBuilder().where(
                    CollectionDao.Properties.AlbumId.eq(collection.getAlbumId())).unique();
            //record item has exist, replace its previous one
            if (preCollection != null) {
                collection.setId(preCollection.getId());
                mCollectionDao.update(collection);
            } else {
                //insert a new item
                mCollectionDao.insert(collection);
            }

            if (mListener != null) {
                mListener.onAdd(collection.getAlbumId(), true);
            }
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in addRecordToDB(): " + e.getMessage());
        }
    }

    private void addChildRecordToDB(ChildCollection childCollection) {
        if (childCollection == null) {
            if (mListener != null) {
                mListener.onAdd(childCollection.getAlbumId(), false);
            }
            return;
        }
        try {
            childCollection.setPassport(UserInfoHelper.getGid());
            ChildCollection preCollection = mChildCollectionDao.queryBuilder().where(
                    ChildCollectionDao.Properties.AlbumId.eq(childCollection.getAlbumId())).unique();
            //record item has exist, replace its previous one
            if (preCollection != null) {
                childCollection.setId(preCollection.getId());
                mChildCollectionDao.update(childCollection);
            } else {
                //insert a new item
                mChildCollectionDao.insert(childCollection);
            }

            if (mListener != null) {
                mListener.onAdd(childCollection.getAlbumId(), true);
            }
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in addRecordToDB(): " + e.getMessage());
        }
    }

    /**
     * 批量收藏，用于登录后同步本地收藏记录
     *
     * @param collections
     */
    private void addRecordsToCloud(final List<Collection> collections) {
        if (collections == null || collections.isEmpty()) {
            return;
        }
        StringBuilder aids = new StringBuilder();
        for (int i = 0; i < collections.size(); i++) {
            Collection collection = collections.get(i);
            aids.append(collection.getAlbumId());
            if (i != collections.size() - 1) {
                aids.append(",");
            }
        }
        NetworkApi.chasePlayItems(mHelper.getLoginPassport(), aids.toString(), mHelper.getLoginToken(), new Observer<CancelChasePlayModel>() {
            @Override
            public void onSubscribe(Disposable disposable) {

            }

            @Override
            public void onNext(CancelChasePlayModel value) {
                LibDeprecatedLogger.d("addRecordsToCloud(): onNext()");
                if (value != null && value.data != null && !TextUtils.isEmpty(value.data.result)
                        && value.data.result.equals("SUCCESS")) {
                    if (mListener != null) {
                        for (Collection collection : collections) {
                            updateCommit(collection.getAlbumId(), 1);
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable throwable) {

            }

            @Override
            public void onComplete() {

            }
        });
    }

    /**
     * Add a new record to the remote server
     *
     * @param aid Album ID of video you want to add
     */
    private void addRecordToCloud(final int aid) {
        NetworkApi.chasePlayItems(mHelper.getLoginPassport(), aid + "", mHelper.getLoginToken(),new Observer<CancelChasePlayModel>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(CancelChasePlayModel value) {
                LibDeprecatedLogger.d("addRecordToCloud(): onNext()");
                if (value != null && value.data != null && !TextUtils.isEmpty(value.data.result)
                        && value.data.result.equals("SUCCESS")) {
                    if (mListener != null) {
                        updateCommit(aid, 1);
                        mListener.onAdd(aid, true);
                    }
                } else {
                    if (mListener != null) {
                        mListener.onAdd(aid, false);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("addRecordToCloud(): onError() -- " + e.getMessage());
                if (mListener != null) {
                    mListener.onAdd(aid, false);
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("addRecordToCloud(): onComplete()");
            }
        });
    }

    /**
     * 本地课堂收藏记录同步，用于登录后同步收藏
     *
     * @param collections
     */
    private void addEduRecordsToCloud(final List<Collection> collections) {
        if (collections == null || collections.isEmpty()) {
            return;
        }
        StringBuilder stringBuilder;
        stringBuilder = new StringBuilder("[");
        for (int i = 0; i < collections.size(); i++) {
            Collection collection = collections.get(i);
            stringBuilder.append("{\"albumId\":");
            stringBuilder.append(collection.getAlbumId());
            stringBuilder.append("}");
            if (i != collections.size() - 1) {
                stringBuilder.append(",");
            } else {
                stringBuilder.append("]");
            }
        }
        String passport = mHelper.getLoginPassport();
        NetworkApi.manageEduCollection(passport, stringBuilder.toString(), 1, new Observer<CancelChasePlayModel>() {
            @Override
            public void onSubscribe(Disposable disposable) {

            }

            @Override
            public void onNext(CancelChasePlayModel value) {
                if (value != null && value.status == 0) {
                    for (Collection collection : collections) {
                        updateCommit(collection.getAlbumId(), 1);
                    }
                }
            }

            @Override
            public void onError(Throwable throwable) {

            }

            @Override
            public void onComplete() {

            }
        });
    }

    /**
     * 操作(添加、删除、清空)教育收藏记录
     *
     * @param aid
     * @param op  1：添加； 2：删除; 3:清空
     */
    private void operateEduToCloud(final int aid, final int op) {
        StringBuilder stringBuilder;
        if (op == 3) {
            stringBuilder = new StringBuilder();
        } else {
            stringBuilder = new StringBuilder("[{\"albumId\":");
            stringBuilder.append(aid);
            stringBuilder.append("}]");
        }
        String passport = mHelper.getLoginPassport();

        NetworkApi.manageEduCollection(passport, stringBuilder.toString(), op, new Observer<CancelChasePlayModel>() {
            @Override
            public void onSubscribe(Disposable d) {
            }

            // value != null && value.status == 0
            @Override
            public void onNext(CancelChasePlayModel value) {
                LibDeprecatedLogger.d("Operate Edu record success! " + op);
                if (value != null && value.status == 0) {
                    if (mListener != null) {
                        if (op == 1) {
                            updateCommit(aid, 1);
                            mListener.onAdd(aid, true);
                        } else if (op == 2) {
                            mListener.onDelete(aid, true);
                        } else {
                            mListener.onDelete(ALL_DELETED_RECORDS_ID, true);
                        }
                    }
                } else {
                    if (mListener != null) {
                        if (op == 1) {
                            mListener.onAdd(aid, false);
                        } else if (op == 2) {
                            mListener.onDelete(aid, false);
                        } else {
                            mListener.onDelete(ALL_DELETED_RECORDS_ID, false);
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("Operate Edu record fail! " + op, e);
                if (mListener != null) {
                    if (op == 1) {
                        mListener.onAdd(aid, false);
                    } else if (op == 2) {
                        mListener.onDelete(aid, false);
                    } else {
                        mListener.onDelete(ALL_DELETED_RECORDS_ID, false);
                    }
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("Operate Edu record complete! " + op);
            }
        });
    }

    private void updateCommit(int aid, int isCommit) {
        Collection preCollection = mCollectionDao.queryBuilder().where(
                CollectionDao.Properties.AlbumId.eq(aid)).unique();
        if (preCollection != null) {
            preCollection.setIsCommit(isCommit);
            try {
                mCollectionDao.update(preCollection);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * Delete the appointed record item from the local database
     *
     * @param aid      Album ID of video you want to delete
     * @param isChased true stands for video is under updating, false stands for video has finished to update
     */
    private void deleteRecordFromDB(int aid, boolean isChased) {
        try {
            Collection collection = mCollectionDao.queryBuilder()
                    .where(CollectionDao.Properties.AlbumId.eq(aid),
                            CollectionDao.Properties.Passport.eq(UserInfoHelper.getGid())).unique();
            if (collection != null) {
                mCollectionDao.delete(collection);
                if (mListener != null) {
                    mListener.onDelete(aid, true);
                }
            } else {
                if (mListener != null) {
                    mListener.onDelete(aid, false);
                }
            }
            reportActionToThirdPartner(aid, isChased, null);
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in deleteRecordFromDB: " + e.getMessage());
        }
    }

    private void deleteEduRecordFromDB(int aid, boolean isChased) {
        try {
            Collection collection = mCollectionDao.queryBuilder()
                    .where(CollectionDao.Properties.AlbumId.eq(aid),
                            CollectionDao.Properties.Passport.eq(UserInfoHelper.getGid()), getEduCondition(true)).unique();
            if (collection != null) {
                mCollectionDao.delete(collection);
                if (mListener != null) {
                    mListener.onDelete(aid, true);
                }
            } else {
                if (mListener != null) {
                    mListener.onDelete(aid, false);
                }
            }
            reportActionToThirdPartner(aid, isChased, null);
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in deleteRecordFromDB: " + e.getMessage());
        }
    }

    /**
     * Delete the appointed record item from the remote server
     *
     * @param passport passport value
     * @param aid      Album ID of video you want to delete
     * @param isChased true stands for video is under updating, false stands for video has finished to update
     */
    private void deleteRecordFromCloud(String passport, int aid, String token, boolean isChased) {
        NetworkApi.cancelChasePlayItems(passport, aid, token, new ChasePlayDeleteListener(aid, isChased));
    }

    private void deleteEduRecordFromCloud(int id) {
        this.operateEduToCloud(id, 2);
    }

    private void deleteAllEduRecordsFromDB(String passport) {
        deleteAllRecordsFromDB(passport, true);
    }

    /**
     * Delete all records from the local database
     *
     * @param passport gid
     * @param isEdu
     */
    private void deleteAllRecordsFromDB(String passport, boolean isEdu) {
        try {
            List<Collection> collectionList = mCollectionDao.queryBuilder()
                    .where(CollectionDao.Properties.Passport.eq(passport), getEduCondition(isEdu)).list();
            if (collectionList != null && collectionList.size() > 0) {
                for (int i = 0; i < collectionList.size(); i++) {
                    mCollectionDao.delete(collectionList.get(i));
                }

                if (mListener != null) {
                    mListener.onDelete(ALL_DELETED_RECORDS_ID, true);
                }
            } else {
//                if (mListener != null) {
//                    mListener.onDelete(ALL_DELETED_RECORDS_ID, false);
//                }
            }

            reportActionToThirdPartner(ALL_DELETED_RECORDS_ID, false, null);
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in deleteAllRecordFromDB");
        }
    }

    private void deleteAllEduRecordsFromCloud() {
        operateEduToCloud(0, 3);
    }

    /**
     * Delete all records from the remote server
     *
     * @param passport passport value
     */
    private void deleteAllRecordsFromCloud(String passport, String token) {
        NetworkApi.cancelAllChasePlay(passport, token, new Observer<CancelChasePlayModel>() {

            @Override
            public void onSubscribe(Disposable d) {
            }

            @Override
            public void onNext(CancelChasePlayModel value) {
                LibDeprecatedLogger.d("clearAllCollectionData(): onNext().");
                if (value != null && value.data != null && !TextUtils.isEmpty(value.data.result)
                        && value.data.result.equals("SUCCESS")) {
                    LibDeprecatedLogger.d("Cancel all collection data successfully.");
                    mListener.onDelete(ALL_DELETED_RECORDS_ID, true);
                    reportActionToThirdPartner(ALL_DELETED_RECORDS_ID, false, null);
                } else {
                    mListener.onDelete(ALL_DELETED_RECORDS_ID, false);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("clearAllCollectionData(): onError(): " + e.getMessage());
                mListener.onDelete(ALL_DELETED_RECORDS_ID, false);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("clearAllCollectionData(): onComplete()");
            }
        });
    }





    /**
     * 删除多余的记录
     */
    private void deleteOverCollectionFromDB() {
        try {
            String passport = UserInfoHelper.getGid();
            List<Collection> collectionList = mCollectionDao.queryBuilder()
                    .where(CollectionDao.Properties.Passport.eq(passport))
                    .orderDesc(CollectionDao.Properties.CollectionTime).list();
            if (collectionList != null && collectionList.size() > MAX_COLLECTION_NUM) {
                for (int i = collectionList.size() - 1; i >= MAX_COLLECTION_NUM; i--) {
                    mCollectionDao.delete(collectionList.get(i));
                }
            }
        } catch (Exception e) {
            LibDeprecatedLogger.e("Exception in deleteOverCollectionFromDB(): " + e);
        }
    }



    /**
     * Report action to the third partner(Kang Jia)
     *
     * @param aid      Album ID of video you want to report
     * @param isChased true stands for video is under updating, false stands for video has finished to update
     * @param data     If data is null, this action is delete.
     */
    private void reportActionToThirdPartner(int aid, boolean isChased, Collection data) {
        if (ThirdPartyService.getService(mContext) == null) {
            return;
        }

        LibDeprecatedLogger.d("reportActionToThirdPartner");
        if (aid == ALL_DELETED_RECORDS_ID) {
            ThirdPartyService.getService(mContext).chaseDeleteAll();
            ThirdPartyService.getService(mContext).collectDeleteAll();
            ThirdPartyService.release();
        } else if (data == null) { // delete operation
            PlayHistory playHistory = new PlayHistory();
            playHistory.setAlbumId(aid);
            playHistory.setDataType(Constant.DATA_TYPE_VRS);
            if (isChased) {
                ThirdPartyService.getService(mContext).chaseDelete(playHistory);
            } else {
                ThirdPartyService.getService(mContext).collectDelete(playHistory);
            }
            ThirdPartyService.release();
        } else if (data != null) { // add operation
            PlayHistory playHistory = new PlayHistory();
            playHistory.setAlbumId(aid);
            playHistory.setTvName(data.getTvName());
            playHistory.setVideoVerPic(data.getTvVerPic());
            playHistory.setDataType(Constant.DATA_TYPE_VRS);
//          if (isChased) {
//              ThirdPartyService.getService(mContext).chaseAdd(playHistory);
//          } else {
            ThirdPartyService.getService(mContext).collectAdd(playHistory);
//          }
            ThirdPartyService.release();
        }
    }

    /**
     * Custom Listener that implements Observer<CancelChasePlayModel> and will callback in the certain moment
     */
    private class ChasePlayDeleteListener implements Observer<CancelChasePlayModel> {

        private int aid;
        private boolean isChased;

        ChasePlayDeleteListener(int aid, boolean isChased) {
            this.aid = aid;
            this.isChased = isChased;
        }

        @Override
        public void onSubscribe(Disposable d) {

        }

        @Override
        public void onNext(CancelChasePlayModel value) {
            LibDeprecatedLogger.d("ChasePlayDeleteListener, onNext()");
            if (value != null && value.data != null && !TextUtils.isEmpty(value.data.result)
                    && value.data.result.equals("SUCCESS")) {
                mListener.onDelete(aid, true);
                reportActionToThirdPartner(aid, isChased, null);
            } else {
                mListener.onDelete(aid, false);
            }
        }

        @Override
        public void onError(Throwable e) {
            LibDeprecatedLogger.e("ChasePlayDeleteListener, onError()");
            mListener.onDelete(aid, false);
        }

        @Override
        public void onComplete() {
            LibDeprecatedLogger.d("ChasePlayDeleteListener, onComplete()");
        }
    }

    /**
     * 教育资源的DB查询条件
     *
     * @return
     */
    private WhereCondition getEduCondition(boolean isEdu) {
        WhereCondition condition;
        if (isEdu) {
            condition = CollectionDao.Properties.CateCode.eq(Constant.EDU_CATE_CODE);
        } else {
            condition = CollectionDao.Properties.CateCode.notEq(Constant.EDU_CATE_CODE);
        }
        return condition;
    }
}
