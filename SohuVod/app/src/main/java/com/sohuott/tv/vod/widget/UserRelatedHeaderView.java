package com.sohuott.tv.vod.widget;

import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.GridListActivityNew;
import com.sohuott.tv.vod.activity.GridListTagActivityNew;
import com.sohuott.tv.vod.activity.LabelGridListActivity;
import com.sohuott.tv.vod.activity.ListEduUserRelatedActivity;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.lib.base.ActivityManagerUtil;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.TopInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.videodetail.activity.VideoActivity;

import java.lang.ref.WeakReference;
import java.util.HashMap;

public class UserRelatedHeaderView extends LinearLayout implements View.OnClickListener, View.OnFocusChangeListener {
    private LinearLayout layoutHeaderHome, layoutHeaderSearch, layoutHeaderHistory, layoutHeaderMine;
    private TextView headerHomeTv;
    private ConstraintLayout layoutHeaderVip;
    private Context mContext;
    public static final int INDEX_HOME_LAYOUT = 0;
    public static final int INDEX_SEARCH_LAYOUT = 1;
    public static final int INDEX_HISTORY_LAYOUT = 2;
    public static final int INDEX_MINE_LAYOUT = 3;
    public static final int INDEX_VIP_LAYOUT = 4;

    public static final int PARENT_VIEW_HOME = 1;
    public static final int PARENT_VIEW_VIDEO = 2;
    public static final int PARENT_VIEW_TAG = 3;
    public static final int PARENT_VIEW_LABEL = 4;

    private static int mParentViewIndex;
    private boolean enableFocus = true;
    private LoginUserInformationHelper mHelper;
    TopInfo mInfo;

    public boolean isHasFocus() {
        return hasFocus;
    }

    private boolean hasFocus;

    private UserHeaderFocusChangeListener userHeaderFocusChangeListener;

    public void setUserHeaderFocusChangeListener(UserHeaderFocusChangeListener userHeaderFocusChangeListener) {
        this.userHeaderFocusChangeListener = userHeaderFocusChangeListener;
    }

    public interface UserHeaderFocusChangeListener {
        void onUserHeaderFocusChange(boolean hasFocus);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (userHeaderFocusChangeListener != null) userHeaderFocusChangeListener.onUserHeaderFocusChange(hasFocus);
        this.hasFocus = hasFocus;
        if (hasFocus && mHeaderViewFocusController != null) {
            mHeaderViewFocusController.onGetFocus(v);
        }
        v.setSelected(hasFocus);
    }

    public boolean isEnableFocus() {
        return enableFocus;
    }

    public void setEnableFocus(boolean enableFocus) {
        this.enableFocus = enableFocus;
        if (enableFocus) {
            this.setDescendantFocusability(ViewGroup.FOCUS_BEFORE_DESCENDANTS);
        } else {
            this.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
        }
    }

    public interface HeaderViewFocusController {
        boolean onFocusDown();

        void onGetFocus(View focusView);
    }

    private static HeaderViewFocusController mHeaderViewFocusController;

    public UserRelatedHeaderView(Context context) {
        super(context);
        init(context);
    }

    public UserRelatedHeaderView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public UserRelatedHeaderView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mContext = new WeakReference<>(context).get();
        LayoutInflater.from(mContext).inflate(R.layout.user_related_header_view_layout, this);
        layoutHeaderHome = (LinearLayout) findViewById(R.id.layout_header_home);
        layoutHeaderSearch = (LinearLayout) findViewById(R.id.layout_header_search);
        layoutHeaderHistory = (LinearLayout) findViewById(R.id.layout_header_history);
        layoutHeaderMine = (LinearLayout) findViewById(R.id.layout_header_mine);
        layoutHeaderVip = (ConstraintLayout) findViewById(R.id.layout_header_vip);
        headerHomeTv = findViewById(R.id.header_home_tv);
        layoutHeaderHome.setOnClickListener(this);
        layoutHeaderSearch.setOnClickListener(this);
        layoutHeaderHistory.setOnClickListener(this);
        layoutHeaderMine.setOnClickListener(this);
        layoutHeaderVip.setOnClickListener(this);
        HeaderViewKeyListener headerViewKeyListener = new HeaderViewKeyListener(mContext, this);
        layoutHeaderHome.setOnKeyListener(headerViewKeyListener);
        layoutHeaderSearch.setOnKeyListener(headerViewKeyListener);
        layoutHeaderHistory.setOnKeyListener(headerViewKeyListener);
        layoutHeaderMine.setOnKeyListener(headerViewKeyListener);
        layoutHeaderVip.setOnKeyListener(headerViewKeyListener);
        layoutHeaderHome.setOnFocusChangeListener(this);
        layoutHeaderSearch.setOnFocusChangeListener(this);
        layoutHeaderHistory.setOnFocusChangeListener(this);
        layoutHeaderMine.setOnFocusChangeListener(this);
        layoutHeaderVip.setOnFocusChangeListener(this);

        mHelper = LoginUserInformationHelper.getHelper(mContext);
        updateLoginDisplay();

        changeDisplayName();
        headerViewExposure();
    }

    private void changeDisplayName() {
        if (mContext instanceof VideoActivity) {
            headerHomeTv.setText("返回首页");
        }
    }

    private void headerViewExposure() {
        String pageId = "";
        if (mContext instanceof ListUserRelatedActivity) {//人中心(影视)
            pageId = "10028";
        } else if (mContext instanceof ListEduUserRelatedActivity) {//个人中心(课堂）
            pageId = "10029";
        } else if (mContext instanceof GridListActivityNew) {//列表页
            pageId = "1019";
        } else if (mContext instanceof VideoActivity) {
            pageId = "1041";
        }
        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", pageId);
        RequestManager.getInstance().onAllEvent(new EventInfo(10153, "imp"), pathInfo, null, null);
    }

    public void updateLoginDisplay() {
        if (layoutHeaderMine != null) {
            if (mHelper.getIsLogin()) {
                ((TextView) layoutHeaderMine.findViewById(R.id.tv_header_login)).setText(R.string.txt_user_related_header_view_mine);
            } else {
                ((TextView) layoutHeaderMine.findViewById(R.id.tv_header_login)).setText(R.string.txt_user_related_header_view_login);
            }
        }
    }

    private void updateVipDisplay() {
        if (layoutHeaderVip != null) {
            ((TextView) layoutHeaderVip.findViewById(R.id.tv_header_vip_title)).setText(mInfo.getData().getVipCommodityText().getData().getOtherText());
            ((TextView) layoutHeaderVip.findViewById(R.id.tv_header_vip_desc)).setText(mInfo.getData().getVipCommodityText().getData().getDiscountsText());
        }
    }

    public void setData(TopInfo data) {
        this.mInfo = data;
        updateVipDisplay();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.layout_header_home:
                if (ActivityManagerUtil.getSecTopActivity() != null && ActivityManagerUtil.getSecTopActivity() instanceof LauncherActivity) {
                    if (mContext instanceof VideoActivity) {
                        ((VideoActivity) mContext).finish();
                        return;
                    }
                }
                ActivityLauncher.startHomeActivityPosition(mContext, ParamConstant.PARAM_GO_HOME);
                homeClickEvent();
                break;
            case R.id.layout_header_search:
                ActivityLauncher.startSearchActivity(mContext);
                break;
            case R.id.layout_header_history:
                ActivityLauncher.startListUserRelatedActivity(mContext, ListUserRelatedActivity.LIST_INDEX_HISTORY);
                break;
            case R.id.layout_header_mine:
                gotoMine();
                break;
            case R.id.layout_header_vip:
                ActivityLauncher.startPayActivity(mContext, PayActivity.PAY_SOURCE_USER_RELATED_HEADER_VIEW);
                break;
            default:
                break;
        }
    }

    private void homeClickEvent() {
        String pageId;
        if (mContext instanceof ListUserRelatedActivity) {//人中心(影视)
            pageId = "10028";
        } else if (mContext instanceof ListEduUserRelatedActivity) {//个人中心(课堂）
            pageId = "10029";
        } else if (mContext instanceof GridListActivityNew) {//列表页
            pageId = "1019";
        } else {
            return;
        }
        HashMap<String, String> path = new HashMap<>(1);
        path.put("pageId", pageId);
        RequestManager.getInstance().onAllEvent(new EventInfo(10153, "clk"), path, null, null);
    }

    private void gotoMine() {
        if (!mHelper.getIsLogin()) {
            ActivityLauncher.startLoginActivity(mContext, Constant.LAUNCHER_SOURCE, 0);
            return;
        }

        if ((mContext instanceof VideoActivity ||
                mContext instanceof GridListActivityNew ||
                mContext instanceof ListUserRelatedActivity) && containsLauncher()) {
            for (int i = ActivityManagerUtil.getActivityList().size(); i > 0; i--) {
                Activity activity = ActivityManagerUtil.getActivityList().get(i - 1).get();
                LibDeprecatedLogger.d("gotoMine for i activity package name is " + activity.getComponentName());
                if (activity instanceof LauncherActivity) {
                    ((LauncherActivity) activity).backToMyPage();
                } else {
                    activity.finish();
                }
            }
            return;
        }
        ActivityLauncher.startHomeActivityPosition(mContext, ParamConstant.PARAM_GO_TO_MINE);
    }

    private boolean containsLauncher() {
        for (int i = 0; i < ActivityManagerUtil.getActivityList().size(); i++) {
            if (ActivityManagerUtil.getActivityList().get(i).get() instanceof LauncherActivity) {
                return true;
            }
        }
        return false;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mHeaderViewFocusController = null;
    }

    public void hideChildView(int childIndex) {
        if (childIndex < 0) {
            return;
        }
        switch (childIndex) {
            case INDEX_HOME_LAYOUT:
                layoutHeaderHome.setVisibility(View.GONE);
                break;
            case INDEX_SEARCH_LAYOUT:
                layoutHeaderSearch.setVisibility(View.GONE);
                break;
            case INDEX_HISTORY_LAYOUT:
                layoutHeaderHistory.setVisibility(View.GONE);
                break;
            case INDEX_MINE_LAYOUT:
                layoutHeaderMine.setVisibility(View.GONE);
                break;
            case INDEX_VIP_LAYOUT:
                layoutHeaderVip.setVisibility(View.GONE);
                break;
            default:
                break;
        }
    }

    public void focusChildView(int childIndex) {
        if (childIndex < 0) {
            return;
        }
        switch (childIndex) {
            case INDEX_HOME_LAYOUT:
                layoutHeaderHome.requestFocus();
                break;
            case INDEX_SEARCH_LAYOUT:
                layoutHeaderSearch.requestFocus();
                break;
            case INDEX_HISTORY_LAYOUT:
                layoutHeaderHistory.requestFocus();
                break;
            case INDEX_MINE_LAYOUT:
                layoutHeaderMine.requestFocus();
                break;
            case INDEX_VIP_LAYOUT:
                layoutHeaderVip.requestFocus();
                break;
            default:
                break;
        }
    }

    public int getChildViewIndex(View view) {
        if (view == layoutHeaderHome) {
            return INDEX_HOME_LAYOUT;
        } else if (view == layoutHeaderSearch) {
            return INDEX_SEARCH_LAYOUT;
        } else if (view == layoutHeaderHistory) {
            return INDEX_HISTORY_LAYOUT;
        } else if (view == layoutHeaderMine) {
            return INDEX_MINE_LAYOUT;
        } else if (view == layoutHeaderVip) {
            return INDEX_VIP_LAYOUT;
        } else {
            return -1;
        }
    }

    public void setHeaderViewFocusController(HeaderViewFocusController controller) {
        this.mHeaderViewFocusController = controller;
        if (mHeaderViewFocusController instanceof GridListActivityNew) {
            mParentViewIndex = PARENT_VIEW_VIDEO;
        } else if (mHeaderViewFocusController instanceof GridListTagActivityNew) {
            mParentViewIndex = PARENT_VIEW_TAG;
        } else if (mHeaderViewFocusController instanceof LabelGridListActivity) {
            mParentViewIndex = PARENT_VIEW_LABEL;
        }
    }

    private static class HeaderViewKeyListener implements OnKeyListener {
        Context mContext;
        LinearLayout mRootLayout;

        public HeaderViewKeyListener(Context context, UserRelatedHeaderView headerView) {
            mContext = new WeakReference<>(context).get();
            mRootLayout = (LinearLayout) new WeakReference<LinearLayout>(headerView).get().getChildAt(0);
        }

        @Override
        public boolean onKey(View view, int keyCode, KeyEvent event) {
            if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && event.getAction() == KeyEvent.ACTION_DOWN) {
                if (view.getId() == R.id.layout_header_home) {
                    view.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_x));
                    return true;
                }
                return focusPrevious(view.getId());
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && event.getAction() == KeyEvent.ACTION_DOWN) {
                if (view.getId() == R.id.layout_header_vip && mParentViewIndex != PARENT_VIEW_HOME) {
                    view.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_x));
                    return true;
                }
                return focusNext(view.getId());
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
                view.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_y));
                return true;
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                if (mHeaderViewFocusController != null) {
                    return mHeaderViewFocusController.onFocusDown();
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }

        private boolean focusNext(int id) {
            int count = mRootLayout.getChildCount();
            if (count > 0) {
                for (int i = 0; i < count; i++) {
                    View child = mRootLayout.getChildAt(i);
                    if (child.getId() == id) {
                        if (i <= (count - 2)) {
                            return mRootLayout.getChildAt(i + 1).requestFocus();
                        }
                        break;
                    }
                }
            }
            return false;
        }

        private boolean focusPrevious(int id) {
            int count = mRootLayout.getChildCount();
            if (count > 0) {
                for (int i = count - 1; i >= 0; i--) {
                    View child = mRootLayout.getChildAt(i);
                    if (child.getId() == id) {
                        if ((i - 1) >= 0) {
                            return mRootLayout.getChildAt(i - 1).requestFocus();
                        }
                        break;
                    }
                }
            }
            return false;
        }
    }
}
