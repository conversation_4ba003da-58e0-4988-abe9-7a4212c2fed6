package com.sohuott.tv.vod.view.scalemenu

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import com.sohuott.tv.vod.R


/**
 * Description:
 * Created by cuipengyu on 2023/7/11.
 * Last Modified: 2023/7/11
 */
open class PileLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    ViewGroup(context, attrs, defStyleAttr) {
    /**
     * 两个子控件之间的垂直间隙
     */
    protected var vertivalSpace: Float

    /**
     * 重叠宽度
     */
    protected var pileWidth: Float

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.PileLayout)
        vertivalSpace = ta.getDimension(R.styleable.PileLayout_PileLayout_vertivalSpace, dp2px(4f))
        pileWidth = ta.getDimension(R.styleable.PileLayout_PileLayout_pileWidth, dp2px(10f))
        ta.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val widthSpecMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSpecSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightSpecMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSpecSize = MeasureSpec.getSize(heightMeasureSpec)

        //AT_MOST
        var width = 0
        var height = 0
        var rawWidth = 0 //当前行总宽度
        var rawHeight = 0 // 当前行高
        var rowIndex = 0 //当前行位置
        val count = childCount
        for (i in 0 until count) {
            val child: View = getChildAt(i)
            if (child.getVisibility() === GONE) {
                if (i == count - 1) {
                    //最后一个child
                    height += rawHeight
                    width = Math.max(width, rawWidth)
                }
                continue
            }

            //这里调用measureChildWithMargins 而不是measureChild
            measureChildWithMargins(child, widthMeasureSpec, 0, heightMeasureSpec, 0)
            val lp = child.getLayoutParams() as MarginLayoutParams
            val childWidth: Int = child.getMeasuredWidth() + lp.leftMargin + lp.rightMargin
            val childHeight: Int = child.getMeasuredHeight() + lp.topMargin + lp.bottomMargin
//            if (rawWidth + childWidth - (if (rowIndex > 0) pileWidth else 0).toInt() > widthSpecSize - paddingLeft - paddingRight) {
//                //换行
//                width = Math.max(width, rawWidth)
//                rawWidth = childWidth
//                height += (rawHeight + vertivalSpace).toInt()
//                rawHeight = childHeight
//                rowIndex = 0
//            } else {
                rawWidth += childWidth
                if (rowIndex > 0) {
                    rawWidth -= pileWidth.toInt()
                }
                rawHeight = Math.max(rawHeight, childHeight)
//            }
            if (i == count - 1) {
                width = Math.max(rawWidth, width)
                height += rawHeight
            }
            rowIndex++
        }
        setMeasuredDimension(
            if (widthSpecMode == MeasureSpec.EXACTLY) widthSpecSize else width + paddingLeft + paddingRight,
            if (heightSpecMode == MeasureSpec.EXACTLY) heightSpecSize else height + paddingTop + paddingBottom
        )
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        val viewWidth = r - l
        var leftOffset = paddingLeft
        var topOffset = paddingTop
        var rowMaxHeight = 0
        var rowIndex = 0 //当前行位置
        var childView: View
        var w = 0
        val count = childCount
        while (w < count) {
            childView = getChildAt(w)
            if (childView.getVisibility() === GONE) {
                w++
                continue
            }
            val lp = childView.getLayoutParams() as MarginLayoutParams
            // 如果加上当前子View的宽度后超过了ViewGroup的宽度，就换行
            val occupyWidth: Int = lp.leftMargin + childView.getMeasuredWidth() + lp.rightMargin
//            if (leftOffset + occupyWidth + paddingRight > viewWidth) {
//                leftOffset = paddingLeft // 回到最左边
//                topOffset += (rowMaxHeight + vertivalSpace).toInt() // 换行
//                rowMaxHeight = 0
//                rowIndex = 0
//            }
            val left = leftOffset + lp.leftMargin
            val top = topOffset + lp.topMargin
            val right: Int = leftOffset + lp.leftMargin + childView.getMeasuredWidth()
            val bottom: Int = topOffset + lp.topMargin + childView.getMeasuredHeight()
            childView.layout(left, top, right, bottom)

            // 横向偏移
            leftOffset += occupyWidth
            // 试图更新本行最高View的高度
            val occupyHeight: Int = lp.topMargin + childView.getMeasuredHeight() + lp.bottomMargin
            if (rowIndex != count - 1) {
                leftOffset -= pileWidth.toInt()
            }
            rowMaxHeight = Math.max(rowMaxHeight, occupyHeight)
            rowIndex++
            w++
        }
    }

    override fun generateLayoutParams(attrs: AttributeSet?): LayoutParams {
        return MarginLayoutParams(context, attrs)
    }

    override fun generateDefaultLayoutParams(): LayoutParams {
        return MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
    }

    override fun generateLayoutParams(p: LayoutParams?): LayoutParams {
        return MarginLayoutParams(p)
    }

    fun dp2px(dpValue: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dpValue,
            resources.displayMetrics
        )
    }
}