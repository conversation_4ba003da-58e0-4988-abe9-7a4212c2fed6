package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.lib.model.CarouselChannel;
import com.sohu.lib_utils.FormatUtils;
import com.sohuott.tv.vod.widget.PlayingView;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/8/31.
 */
public class CarouselChannelListAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int MULTIPLE_BASE_OFFSET = 10000;

    private static final int TIME_INTERVAL = 1500;

    private int mCurrentPosition;
    private int mChannelOrder;
    private long mKeyRightTime = System.currentTimeMillis();

    private boolean mIsFirstRequestFocus = true;

    private Context mContext;

    private List<CarouselChannel.DataEntity.ResultEntity.LoopChannelsEntity> mList;

    private View mFocusedView;

    public CarouselChannelListAdapter(Context context, List<CarouselChannel.DataEntity.ResultEntity.LoopChannelsEntity> list) {
        mContext = context;
        mList = list;
    }


    @Override
    public int getItemCount() {
        return Integer.MAX_VALUE;
    }


    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext.getApplicationContext()).inflate(R.layout.carousel_channel, parent, false);
        CarouselChannelViewHolder vh = new CarouselChannelViewHolder(view);
        return vh;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
//        AppLogger.i("onBindViewHolder:" + position);
        final CarouselChannelViewHolder vh = (CarouselChannelViewHolder) holder;

        if (null != mList && mList.size() > 0) {
            CarouselChannel.DataEntity.ResultEntity.LoopChannelsEntity entity = mList.get(position % mList.size());
            int id = entity.getOrder();
            String name = entity.getName();
            String currentVideoName = entity.getCurrentVideoName();
            vh.channelId.setText(FormatUtils.formatNumber2(id));

            if (TextUtils.isEmpty(name) || TextUtils.isEmpty(name.trim())) {
                name = "";
            }
            vh.channelName.setText(name);

            if (TextUtils.isEmpty(currentVideoName) || TextUtils.isEmpty(currentVideoName.trim())) {
                currentVideoName = "";
            }
            vh.playingVideoName.setText(currentVideoName);

            if (mChannelOrder == id) {
                if (mIsFirstRequestFocus) {
                    mIsFirstRequestFocus = false;
                    vh.rootView.requestFocus();
                }
                vh.playingView.show();
            } else {
                vh.playingView.hide();
            }
            if(Util.isSupportTouchVersion(mContext)){
                vh.itemView.setSelected(mChannelOrder == id?true:false);
            }
        }
    }

    public void setChannelOrder(int channelOrder) {
        mChannelOrder = channelOrder;
        initChannelOrder(channelOrder);
    }


    public void setChannelList(List<CarouselChannel.DataEntity.ResultEntity.LoopChannelsEntity> list, int channelOrder) {
        mList = list;
        initChannelOrder(channelOrder);
        mIsFirstRequestFocus = true;
        notifyDataSetChanged();
    }


    public int getCurrentPosition() {
        if (mCurrentPosition < mList.size() * (MULTIPLE_BASE_OFFSET >> 1)) {
            mCurrentPosition = mCurrentPosition % mList.size() + mList.size() * MULTIPLE_BASE_OFFSET;
        }
        return mCurrentPosition;
    }

    private void setCurrentPosition(int position) {
        mCurrentPosition = position + mList.size() * MULTIPLE_BASE_OFFSET;
    }

    private void initChannelOrder(int channelOrder) {
        mChannelOrder = channelOrder;
        if (null != mList && mList.size() > 0) {
            int index = -1;
            for (int i = 0; i < mList.size(); i++) {
                CarouselChannel.DataEntity.ResultEntity.LoopChannelsEntity entity = mList.get(i);
                if (mChannelOrder == entity.getOrder()) {
                    index = i;
                    break;
                }
            }
            if (index < 0) {
                index = 0;
                while (index < mList.size()) {
                    if (mList.get(index).getVideoId() > 0) {
                        setChannelOrder(mList.get(index).getOrder());
                        setCurrentPosition(index);
                        break;
                    }
                    index++;
                }
            } else {
                setCurrentPosition(index);
            }
        }
    }

    public View getFocusedView() {
        return mFocusedView;
    }

    onChannelListItemClickListener itemClickListener;

    public void setOnItemClickListener(onChannelListItemClickListener listener) {
        itemClickListener = listener;
    }

    /**
     * record last itemView on touch mode
     */
    private View mLastView;
    private class CarouselChannelViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener, View.OnFocusChangeListener {

        private View rootView;
        private PlayingView playingView;
        private TextView channelId;
        private TextView channelName;
        private TextView playingVideoName;

        public CarouselChannelViewHolder(View itemView) {
            super(itemView);
            rootView = itemView;
            initUI();
            rootView.setOnClickListener(this);
            rootView.setOnFocusChangeListener(this);
            rootView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if (event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                            if(event.getRepeatCount()==0){
                                int tmp = Integer.valueOf(channelId.getText().toString()).intValue();
                                if (itemClickListener != null) {
                                    itemClickListener.onDpadRightClick(tmp);
                                }
                                rootView.setSelected(true);
                            }
                            return true;
                        }
                    }
                    return false;
                }
            });
        }

        @Override
        public void onClick(View view) {
            if(Util.isSupportTouchVersion(mContext)){
                if(mLastView!=null){
                    mLastView.setSelected(false);
                }
                view.setSelected(true);
                mLastView=view;
            }
            if (itemClickListener != null) {
                itemClickListener.onChannelItemClick(getAdapterPosition() % mList.size());
            }
        }

        @Override
        public void onFocusChange(View view, boolean hasFocus) {
            if (view == rootView) {
                if (hasFocus) {
                    mFocusedView = rootView;
                    rootView.setSelected(false);
                }
                playingVideoName.setSelected(hasFocus);
                playingVideoName.setEllipsize(hasFocus ? TextUtils.TruncateAt.MARQUEE : TextUtils.TruncateAt.END);
                channelName.setSelected(hasFocus);
                channelName.setEllipsize(hasFocus ? TextUtils.TruncateAt.MARQUEE : TextUtils.TruncateAt.END);
            }

        }

        private void initUI() {
            playingView = (PlayingView) rootView.findViewById(R.id.channel_playing);
            channelId = (TextView) rootView.findViewById(R.id.channel_id);
            channelName = (TextView) rootView.findViewById(R.id.channel_name);
            playingVideoName = (TextView) rootView.findViewById(R.id.playing_video_name);
            if (Util.getDeviceName().equalsIgnoreCase("rk3368-box")
                    || Util.getDeviceName().equalsIgnoreCase("db1016")
                    || Util.getDeviceName().equalsIgnoreCase("inphic_i9s1")) { // 芒果嗨Q H7 三代, 大麦 DB1016, 英菲克 i9
                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                        mContext.getApplicationContext().getResources().getDimensionPixelSize(R.dimen.x500),
                        mContext.getApplicationContext().getResources().getDimensionPixelSize(R.dimen.y162));
                rootView.setLayoutParams(params);
            }

        }

    }

    public interface onChannelListItemClickListener {
        void onChannelItemClick(int position);

        void onDpadRightClick(int order);
    }

}
