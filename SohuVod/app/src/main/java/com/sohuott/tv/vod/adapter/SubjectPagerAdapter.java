package com.sohuott.tv.vod.adapter;


import android.view.ViewGroup;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.sohuott.tv.vod.fragment.SubjectFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ComingSoonModel;

import java.lang.ref.WeakReference;
import java.util.HashMap;

/**
 * Created by y<PERSON>yin on 2018/9/25.
 */

public class SubjectPagerAdapter extends FragmentPagerAdapter {
    private HashMap<Integer, WeakReference<SubjectFragment>> mFragmengs = new HashMap<>();
    private String mPic1;
    private String mPic2;
    private String mSmallPic;
    private int mType;
    private ComingSoonModel mData;


    public SubjectPagerAdapter(FragmentManager fm, String pic1, String pic2, String smallPic) {
        super(fm);
        this.mPic1 = pic1;
        this.mPic2 = pic2;
        this.mSmallPic = smallPic;
    }

    public void setTempletData(ComingSoonModel data){
        this.mData = data;
        this.mType = data.getExtend().getTemplateType();
    }

    @Override
    public Fragment getItem(int position) {
        WeakReference<SubjectFragment> fragmentWeakReference = mFragmengs.get(new Integer(position));
        SubjectFragment subjectFragment = null;
        if (fragmentWeakReference != null){
            subjectFragment = fragmentWeakReference.get();
        }

        if (subjectFragment == null){
            subjectFragment = SubjectFragment.newInstance(position + 1, mPic1, mPic2, mSmallPic, mType);
            mFragmengs.put(new Integer(position), new WeakReference<SubjectFragment>(subjectFragment));
        }
        return subjectFragment;
    }

    @Override
    public int getCount() {
        return mData==null? 0: 2;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        LibDeprecatedLogger.d("destroyItem , fragment size = " + mFragmengs.size() + ", pos = " + position);
        super.destroyItem(container, position, object);
        mFragmengs.remove(position);
    }
}
