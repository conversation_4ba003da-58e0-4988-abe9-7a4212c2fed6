package com.sohuott.tv.vod.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.LogEventModel;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListWelfareModel;
import com.sohuott.tv.vod.lib.model.WelfareHistoryModel;
import com.sohuott.tv.vod.lib.rvhelper.BaseQuickAdapter;
import com.sohuott.tv.vod.lib.rvhelper.BaseViewHolder;
import com.sohuott.tv.vod.lib.rvhelper.util.MultiTypeDelegate;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Spanny;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.SimpleDisposableObsever;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

import io.reactivex.disposables.CompositeDisposable;

/**
 * v6.5积分商城我的兑换页
 *
 * <AUTHOR>
 *         created at 2017/12/22
 */
public class WelfareMyExchangeFragment extends BaseFragment implements
        BaseQuickAdapter.OnItemChildClickListener, BaseQuickAdapter.OnItemChildFocusChangedListener {

    //    private TextView mTvTotal;
    private TextView mTvEmpty;
    private CustomLinearRecyclerView mRvMyList;
    private BaseQuickAdapter<WelfareHistoryModel.DataEntity, BaseViewHolder> mHistoryAdapter;
    private View mFirstOrLastFocusView;
    private Button mLoginBtn;
    private FocusBorderView mFocusBorderView;
    private Group mEmptyGroup;

    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    WelfareActivityDetailFragment.OnProductClickListener mOnProductClickListener;

    public void setOnProductClickListener(WelfareActivityDetailFragment.OnProductClickListener onProductClickListener) {
        mOnProductClickListener = onProductClickListener;
    }

    private LinearLayoutManager mLayoutManager;
    @Nullable
    @Override
    public View onCreateView(final LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_welfare_my_exchange, null);
        RequestManager.onEvent(new LogEventModel("6_welfare_my", "100001"));
        setSubPageName("6_welfare_my");
        mFocusBorderView = (FocusBorderView) rootView.findViewById(R.id.v_focus);
        mTvEmpty = (TextView) rootView.findViewById(R.id.tv_empty_welfare);
        mRvMyList = (CustomLinearRecyclerView) rootView.findViewById(R.id.rv_my_list);
        mLoginBtn = (Button) rootView.findViewById(R.id.btn_login);
        mLoginBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ActivityLauncher.startLoginActivity(getContext());
            }
        });
        mEmptyGroup = (Group) rootView.findViewById(R.id.group_empty);
        mRvMyList.setLayoutManager(new LinearLayoutManager(getContext()) {
            @Override
            public View onInterceptFocusSearch(View focused, int direction) {
                if(mRvMyList.getDescendantFocusability()== ViewGroup.FOCUS_BLOCK_DESCENDANTS){
                    return focused;
                }
                if (focused != null && focused.getId() == R.id.records_tv && direction == View.FOCUS_RIGHT) {
                    View posterView = getChildAt(0).findViewById(R.id.iv_product);
                    return posterView;
                }
                return null;
            }

            @Override
            public View onFocusSearchFailed(final View focused, int focusDirection, RecyclerView.Recycler recycler, RecyclerView.State state) {
                View findFocus = super.onFocusSearchFailed(focused, focusDirection, recycler, state);
                /**
                 * fix R.id.tv_cardno focus missing,the first item cannot completely show
                 */
                if (focusDirection == View.FOCUS_DOWN
                        || focusDirection == View.FOCUS_UP) {
                    int pos = mRvMyList.findContainingViewHolder(focused).getAdapterPosition();
                    if (focusDirection == View.FOCUS_DOWN ) {
                        if(pos < mHistoryAdapter.getItemCount()-1){
                            pos++;
                        }
                    } else if (pos > 0) {
                        pos--;
                    }
                    final RecyclerView.ViewHolder viewHolder = mRvMyList.findViewHolderForAdapterPosition(pos);
                    if (viewHolder != null) {
                        if (focused.getId() != R.id.iv_product) {
                            final int newPos = pos;
                            if (pos == 0 && focused.getId() == R.id.tv_cardno) {
                                ((LinearLayoutManager) mRvMyList.getLayoutManager()).scrollToPositionWithOffset(0, 0);
                            }
                            mRvMyList.postDelayed(new ScrollToRunnable(WelfareMyExchangeFragment.this, pos, focused.getId(), newPos), 200);
                        }

                        return viewHolder.itemView.findViewById(focused.getId());
                    }

                }
                return findFocus;
            }
        });
        mLayoutManager=(LinearLayoutManager) mRvMyList.getLayoutManager();
        mRvMyList.setHasFixedSize(true);
        mRvMyList.setItemViewCacheSize(0);
        mRvMyList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                if (parent.getChildLayoutPosition(view) == 0) {
                    outRect.top = (int) getResources().getDimension(R.dimen.y90);
                } else {
                    outRect.top = (int) getResources().getDimension(R.dimen.y50);
                }
            }
        });
        mHistoryAdapter = new HistoryAdapter();
        mHistoryAdapter.setOnItemChildFocusChangeListener(this);
        mHistoryAdapter.bindToRecyclerView(mRvMyList);
        mHistoryAdapter.setOnItemChildClickListener(this);
        return rootView;
    }

    private static class ScrollToRunnable implements Runnable {
        WeakReference<WelfareMyExchangeFragment> mWrapper;
        private int pos, focusedId, newPos;
        ScrollToRunnable(WelfareMyExchangeFragment fragment, int pos, int focusedId, int newPos){
            mWrapper = new WeakReference<>(fragment);
            this.pos = pos;
            this.focusedId = focusedId;
            this.newPos = newPos;
        }
        @Override
        public void run() {
            WelfareMyExchangeFragment welfareMyExchangeFragment = mWrapper.get();
            RecyclerView.ViewHolder viewHolder = welfareMyExchangeFragment.mRvMyList.findViewHolderForAdapterPosition(pos);
            if (welfareMyExchangeFragment != null){
                if (focusedId == R.id.tv_cardno && viewHolder != null) {
                    viewHolder.itemView.findViewById(focusedId).requestFocus();
                } else {
                    if (newPos == 0) {
                        ((LinearLayoutManager) welfareMyExchangeFragment.mRvMyList.getLayoutManager()).scrollToPositionWithOffset(0, 0);
                    }
                }
            }
        }
    }

    public void setFocusViewFromKeyBack() {
        if (mFirstOrLastFocusView != null) {
            mFirstOrLastFocusView.requestFocus();
        }
    }
    public void resetDescendantFocus(){
        if(mRvMyList!=null){
            mRvMyList.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        }
    }

    /**
     * handler focus if current focusView is top/bottom
     * @param view
     * @param scrollUp
     * @return
     */
    public boolean isScrollToTopOrBottom(final View view, boolean scrollUp){
        if(mHistoryAdapter.getItemCount()>0){
            mRvMyList.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            RecyclerView.ViewHolder viewHolder= mRvMyList.findContainingViewHolder(view);
            if(viewHolder==null){
                return false;
            }
            boolean isTopOrBottom=(!scrollUp&&viewHolder.getAdapterPosition()==mHistoryAdapter.getItemCount()-1)
                    ||(scrollUp&&viewHolder.getAdapterPosition()==0);
            if(isTopOrBottom){
                mRvMyList.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
                return true;
            }
            final int newPos=scrollUp?viewHolder.getAdapterPosition()-1:viewHolder.getAdapterPosition()+1;
            int viewId = view.getId();
            RecyclerView.ViewHolder nextViewHolder=mRvMyList.findViewHolderForAdapterPosition(newPos);
            if(nextViewHolder==null){
                mRvMyList.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
                mRvMyList.scrollToPosition(newPos);
                mRvMyList.post(new FocusRunnable(this, viewId, newPos));
            }else{
                if(scrollUp){
                    View nextView = mLayoutManager.findViewByPosition(newPos);
                    Rect rect = new Rect();
                    nextView.getLocalVisibleRect(rect);
                    if(rect.top!=0){
                        ((LinearLayoutManager)mRvMyList.getLayoutManager()).scrollToPositionWithOffset(newPos,0);
                    }
                }
            }
        }
        return false;
    }

    private static class FocusRunnable implements Runnable {
        private WeakReference<WelfareMyExchangeFragment> mWrapper;
        private int viewId, newPos;
        FocusRunnable(WelfareMyExchangeFragment fragment, int viewId, int newPos){
            mWrapper = new WeakReference<>(fragment);
            this.viewId = viewId;
            this.newPos = newPos;
        }
        @Override
        public void run() {
            WelfareMyExchangeFragment welfareMyExchangeFragment = mWrapper.get();
            if (welfareMyExchangeFragment != null){
                welfareMyExchangeFragment.mRvMyList.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
                RecyclerView.ViewHolder viewHolder = welfareMyExchangeFragment.mRvMyList.findViewHolderForAdapterPosition(newPos);
                viewHolder.itemView.findViewById(viewId).requestFocus();
            }
        }
    }

    public View getFocusViewFromLeftLayout() {
        if (mFirstOrLastFocusView != null) {
            mFirstOrLastFocusView.requestFocus();
        } else if (!LoginUserInformationHelper.getHelper(getContext()).getIsLogin()
                && mEmptyGroup.getVisibility() == View.VISIBLE) {
            return mLoginBtn;
        } else if (mRvMyList.getChildCount() > 0) {
            return mRvMyList.getChildAt(0).findViewById(R.id.iv_product);
        }
        return null;
    }

    private int mTotalCount;

    private void getMyExchangeList() {
        if (LoginUserInformationHelper.getHelper(getContext()).getIsLogin()) {
            mEmptyGroup.setVisibility(View.GONE);
            mLoginBtn.setVisibility(View.GONE);
            mRvMyList.setVisibility(View.VISIBLE);
            SimpleDisposableObsever obsever = new SimpleDisposableObsever<WelfareHistoryModel>() {
                @Override
                public void onNext(WelfareHistoryModel value) {
                    if (value.status == 0 && value.data != null) {
                        if (value.extend != null) {
                            if (mTotalCount!=0&&mTotalCount == value.extend.count) {
                                LibDeprecatedLogger.d("no need update");
                                return;
                            }
                            mTotalCount = value.extend.count;
                        }
                        if (value.data.size() == 0) {
                            mFirstOrLastFocusView = null;
                            mRvMyList.removeAllViews();
                            mTvEmpty.setText(R.string.welfare_my_exchange_empty);
                            mEmptyGroup.setVisibility(View.VISIBLE);
                            mLoginBtn.setVisibility(View.GONE);
                            mRvMyList.setVisibility(View.GONE);
                        } else {
                            value.data.get(0).itemType = 2;
                            mHistoryAdapter.setNewData(value.data);
                        }
                    }
                }
            };
            NetworkApi.getWelfareHistoryList(LoginUserInformationHelper.getHelper(getContext()).getLoginPassport(), obsever);
            mCompositeDisposable.add(obsever);
        } else {
            clearData();
        }

    }
    public void clearData(){
        mFirstOrLastFocusView = null;
        mRvMyList.removeAllViews();
        mTotalCount=0;
        mHistoryAdapter.setNewData(new ArrayList<WelfareHistoryModel.DataEntity>());
        mEmptyGroup.setVisibility(View.VISIBLE);
        mLoginBtn.setVisibility(View.VISIBLE);
        mRvMyList.setVisibility(View.GONE);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (isVisible()) {
            getMyExchangeList();
        }
    }

    @Override
    public void onItemChildFocusChanged(BaseQuickAdapter adapter, final View v, int position, final boolean hasFocus) {
        LibDeprecatedLogger.d("view=" + v + ",hasFocus=" + hasFocus);
        if (hasFocus) {
            mFocusBorderView.setFocusView(v);
            FocusUtil.setFocusAnimator(v, mFocusBorderView);
        } else {
            mFocusBorderView.setUnFocusView(v);
            FocusUtil.setUnFocusAnimator(v);
        }
        if (v.getId() == R.id.tv_cardno) {
            ((TextView) v).setEllipsize(hasFocus ? TextUtils.TruncateAt.MARQUEE : TextUtils.TruncateAt.END);
        }

    }

    @Override
    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
        WelfareHistoryModel.DataEntity activityItem = (WelfareHistoryModel.DataEntity) adapter.getItem(position);
        mFirstOrLastFocusView = mRvMyList.findViewHolderForAdapterPosition(position).itemView.findViewById(R.id.iv_product);
        if (mOnProductClickListener != null) {
            mOnProductClickListener.onProductClick(activityItem.activityId, true);
        }

        LogEventModel eventModel = new LogEventModel();
        eventModel.type = "6_welfare_my";
        if (view instanceof TextView) {
            eventModel.stype = "6_welfare_my_btn_detail";
        } else {
            eventModel.stype = "6_welfare_my_btn_poster";
        }
        eventModel.expand1 = "" + activityItem.activityId;
        RequestManager.onEvent(eventModel);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            RequestManager.onEvent(new LogEventModel("6_welfare_my", "100001"));
            getMyExchangeList();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mCompositeDisposable.clear();
    }

    class HistoryAdapter extends BaseQuickAdapter<WelfareHistoryModel.DataEntity, BaseViewHolder> {
        public HistoryAdapter() {
            super(null);
            setMultiTypeDelegate(new MultiTypeDelegate<WelfareHistoryModel.DataEntity>() {
                @Override
                protected int getItemType(WelfareHistoryModel.DataEntity entity) {
                    return entity.itemType;
                }
            });
            getMultiTypeDelegate()
                    .registerItemType(WelfareHistoryModel.DataEntity.ITEM_TYPE_CONTENT, R.layout.item_welfare_my_exchange)
                    .registerItemType(WelfareHistoryModel.DataEntity.ITEM_TYPE_HEADER, R.layout.header_my_exchange);
        }

        @Override
        protected void convert(BaseViewHolder helper, WelfareHistoryModel.DataEntity item) {
            switch (helper.getItemViewType()) {
                case WelfareHistoryModel.DataEntity.ITEM_TYPE_HEADER:
                    SpannableString spannableString = new SpannableString(
                            getString(R.string.welfare_my_exchange_count, "" + mTotalCount));
                    spannableString.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.item_welfare_count))
                            , 7, spannableString.length() - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                    helper.setText(R.id.tv_total, spannableString);
                case WelfareHistoryModel.DataEntity.ITEM_TYPE_CONTENT:
                    helper.setText(R.id.tv_product_name, item.activityName);
                    Spanny otherInfo = new Spanny(getString(R.string.sell_price))
                            .append(String.valueOf(item.score), new ForegroundColorSpan(getResources().getColor(R.color.welfare_user_rank)))
                            .append("分 ");
                    if (item.status == ListWelfareModel.STATUS_OVER) {
                        otherInfo.append(getString(R.string.welfare_my_exchange_offline));
                        helper.setText(R.id.tv_product_other, otherInfo);
                    } else {
                        int percent = 0;
                        if (item.totalCount > 0) {
                            percent = (int) (((item.totalCount - item.leftCount) * 1.0f / item.totalCount) * 100);
                        }
                        otherInfo.append(getString(R.string.sell))
                                .append(String.valueOf(percent))
                                .append(getString(R.string.percent));
                        helper.setText(R.id.tv_product_other, otherInfo);
                    }
                    ((TextView) helper.getView(R.id.tv_cardno)).setText(getString(R.string.welfare_my_exchange_cardno) + item.cardno);
                    ((GlideImageView) helper.getView(R.id.iv_product)).setImageRes(item.poster);
                    helper.addOnClickListener(R.id.iv_product)
                            .addOnClickListener(R.id.tv_detail)
                            .addOnFocusChangeListener(R.id.iv_product)
                            .addOnFocusChangeListener(R.id.tv_cardno)
                            .addOnFocusChangeListener(R.id.tv_detail);
                    break;
            }
        }
    }
}
