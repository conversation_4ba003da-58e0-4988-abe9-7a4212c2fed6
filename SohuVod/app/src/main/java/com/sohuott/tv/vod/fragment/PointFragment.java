package com.sohuott.tv.vod.fragment;

import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_POINT;

import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.ScaleAnimation;
import android.view.animation.TranslateAnimation;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.FontUtils;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.activity.PointTaskLayout;
import com.sohuott.tv.vod.adapter.PointAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ReportPointResult;
import com.sohuott.tv.vod.lib.model.UserPointInfo;
import com.sohuott.tv.vod.lib.model.WechatPublic;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.PointPresenterImpl;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.PointRecordLayout;
import com.sohuott.tv.vod.view.PointView;
import com.sohuott.tv.vod.widget.BasePopupWindow;
import com.sohuott.tv.vod.widget.GlideImageView;
import com.sohuott.tv.vod.widget.PointManualLayout;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import io.reactivex.observers.DisposableObserver;

/**
 * My point added 6.5.1
 * Created by wenjingbian on 2017/12/22.
 */

public class PointFragment extends BaseFragment implements PointView, PointAdapter.IOnClickItemListener {

    private static final String TAG = PointFragment.class.getSimpleName();
    private TextView tv_nickname, tv_rank, tv_rank_pre, tv_rank_suf, tv_point_year, tv_point_year_title,
            tv_point_quarter, tv_point_quarter_title, tv_point_month, tv_point_month_title, tv_group_two_title,
            tv_point_continue_buy_title, tv_point_continue_buy;
    private Button btn_point_sum, btn_point_welfare;
    private CustomLinearRecyclerView rv_point_group_two;
    private PointManualLayout layout_point_sign, layout_point_login, layout_point_wechat;
    private LinearLayout layout_point_member, mErrorView, mPointView;
    private FrameLayout mFrameLayout;
    private LoadingView mLoadingView;
    private FocusBorderView mFocusView;
    private PopupWindow mPointWindow;

    private PointAdapter mAdapter;
    private CustomLinearLayoutManager mLayoutManager;
    private PointPresenterImpl mPresenterImpl;
    private LoginUserInformationHelper mHelper;

    private List<UserPointInfo.DataBean> mGroupOneList, mGroupTwoList, mGroupThreeList;
    private BasePopupWindow mWeixinhaoWindow;
    private LayoutInflater mLayoutInflater;
    private GlideImageView qrcodeIV;
    private HashMap<String, String> memoMap;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View scoreView = inflater.inflate(R.layout.fragment_point, container, false);
        mLayoutInflater = inflater;
        initView(scoreView);
        RequestManager.getInstance().onPointExposureEvent();
        setSubPageName("6_user_point");
        AppLogger.d(TAG, "onCreateView");
        HashMap pathInfo = new HashMap<>();
        pathInfo.put("pageId", "1013");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), pathInfo, null,
                null);

        return scoreView;
    }

    @Override
    public void onResume() {
        super.onResume();
        initData();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        //release all resources
        if (mAdapter != null) {
            mAdapter.releaseAll();
            mAdapter = null;
        }
        if (mGroupOneList != null) {
            mGroupOneList.clear();
            mGroupOneList = null;
        }

        if (mGroupTwoList != null) {
            mGroupTwoList.clear();
            mGroupTwoList = null;
        }

        if (mGroupThreeList != null) {
            mGroupThreeList.clear();
            mGroupThreeList = null;
        }
        mHelper = null;
        mPresenterImpl = null;
    }

    @Override
    public void updateView(UserPointInfo userPointInfo) {
        if (getActivity() == null || ((ListUserRelatedActivity) getActivity()).getLeftSelectedTag()
                != LIST_INDEX_POINT) {
            return;
        }

        mLoadingView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.GONE);

        if (userPointInfo == null || userPointInfo.getData() == null || userPointInfo.getExtend() == null
                || userPointInfo.getStatus() != 0) {
            displayErrorView();
            return;
        }

        //update view
        mPointView.setVisibility(View.VISIBLE);
        tv_rank.setVisibility(View.VISIBLE);
        tv_rank_suf.setVisibility(View.VISIBLE);
        tv_rank_pre.setText("全国排行");

        mPresenterImpl.classifyList(userPointInfo.getData());
        mGroupOneList = mPresenterImpl.getGroupOneList();
        mGroupTwoList = mPresenterImpl.getGroupTwoList();
        mGroupThreeList = mPresenterImpl.getGroupThreeList();

        tv_nickname.setText(mHelper.getNickName());
        tv_rank.setText(String.valueOf(userPointInfo.getExtend().getRank()));

        SpannableString sStr = new SpannableString(userPointInfo.getExtend().getTotalScore() + " 分");
        AppLogger.d(TAG, "sStr ? " + sStr);
        if (sStr != null && sStr.length() > 2) {
            AppLogger.d(TAG, "sStr.length() ? " + sStr.length());
            sStr.setSpan(new AbsoluteSizeSpan(getResources().getDimensionPixelSize(R.dimen.x50)), 0, sStr.length()-2, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            btn_point_sum.setText(sStr);
        }
        updateGroupOneView();
        updateGroupTwoView();
        updateGroupThreeView();
    }

    @Override
    public void displayErrorView() {
        mLoadingView.setVisibility(View.GONE);
        mPointView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.VISIBLE);
    }

    @Override
    public void onItemClick(UserPointInfo.DataBean dataBean, int postion) {
        PointTaskLayout pointTaskLayout = new PointTaskLayout(getContext(), dataBean.getCurrentValue(),
                dataBean.getCurrentScore() + "分", dataBean.getTaskId(), mHelper.getLoginPassport());
        showPopUpWindow(pointTaskLayout);
        AppLogger.d(TAG, "onItemClick postion ? " + postion);
        if (postion == 0) {
            RequestManager.getInstance().onAllEvent(new EventInfo(10225, "clk"), null, null, null);
        } else if (postion == 1) {
            RequestManager.getInstance().onAllEvent(new EventInfo(10226, "clk"), null, null, null);
        } else if (postion == 2) {
            RequestManager.getInstance().onAllEvent(new EventInfo(10227, "clk"), null, null, null);
        }
    }

    @Override
    public void getSignResult(final ReportPointResult result) {
        if (result == null) {
            return;
        }

        switch (result.getStatus()) {
            case 0: //report successful
                if (result.getExtend() == null) {
                    return;
                }
                final TextView textView = new TextView(getContext());
                FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                textView.setTextColor(getResources().getColor(R.color.txt_grid_left_focused));
                textView.setTextSize(getResources().getDimensionPixelSize(R.dimen.x24));
                textView.setText("+2");
                layoutParams.setMargins(getResources().getDimensionPixelOffset(R.dimen.x222),
                        getResources().getDimensionPixelOffset(R.dimen.y728), 0, 0);
                mFrameLayout.addView(textView, layoutParams);

                ScaleAnimation scaleAnimation = new ScaleAnimation(1.95f, 1f, 1.95f, 1f);
                scaleAnimation.setDuration(500);
                TranslateAnimation translateAnimation = new TranslateAnimation(textView.getX(),
                        textView.getX() + getResources().getDimensionPixelOffset(R.dimen.x10), textView.getY(),
                        textView.getY() + getResources().getDimensionPixelOffset(R.dimen.y105));
                translateAnimation.setFillAfter(true);
                translateAnimation.setDuration(500);
                AnimationSet animationSet = new AnimationSet(true);
                animationSet.addAnimation(translateAnimation);
                animationSet.addAnimation(scaleAnimation);
                animationSet.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {

                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        layout_point_sign.setClickable(true);
                        mFrameLayout.removeView(textView);
                        ToastUtils.showToast(getContext(), "签到成功！");
                        btn_point_sum.setText(result.getExtend().getTotalScore() + "分");
                        tv_rank.setText(String.valueOf(result.getExtend().getRank()));
                        layout_point_sign.updateView(false, Constant.USER_POINT_SIGN);
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });
                textView.startAnimation(animationSet);
                break;
            case 50051: //repeated report
                layout_point_sign.setClickable(true);
                ToastUtils.showToast(getContext(), "你已经完成签到！");
                break;
            default: //other error
                layout_point_sign.setClickable(true);
                ToastUtils.showToast(getContext(), result.getMessage());
                break;
        }
    }

    /**
     * Set focus on the default view
     */
    public void requestFocusToDefaultPos() {
        if (btn_point_sum != null){
            btn_point_sum.requestFocus();
        }
    }

    private void setTypeface(View view) {
        FontUtils.setTypeface(getContext(), view);
    }

    private void initView(View view) {
        mFrameLayout = (FrameLayout) view;
        mFocusView = (FocusBorderView) view.findViewById(R.id.focus_border_view);
        mLoadingView = (LoadingView) view.findViewById(R.id.detail_loading_view);
        mErrorView = (LinearLayout) view.findViewById(R.id.err_view);
        mPointView = (LinearLayout) view.findViewById(R.id.layout_point_ctn);
        layout_point_member = (LinearLayout) view.findViewById(R.id.layout_point_member);

        tv_nickname = (TextView) view.findViewById(R.id.tv_point_nickname);
        tv_rank = (TextView) view.findViewById(R.id.tv_point_rank);
        setTypeface(tv_rank);
        tv_rank_pre = (TextView) view.findViewById(R.id.tv_point_rank_pre);
        tv_rank_suf = (TextView) view.findViewById(R.id.tv_point_rank_suf);
        tv_point_continue_buy_title = (TextView) view.findViewById(R.id.tv_point_continue_buy_title);
        tv_point_continue_buy = (TextView) view.findViewById(R.id.tv_point_continue_buy);
        setTypeface(tv_point_continue_buy);
        tv_point_year = (TextView) view.findViewById(R.id.tv_point_group_one_year);
        setTypeface(tv_point_year);
        tv_point_quarter = (TextView) view.findViewById(R.id.tv_point_group_one_quarter);
        setTypeface(tv_point_quarter);
        tv_point_month = (TextView) view.findViewById(R.id.tv_point_group_one_month);
        setTypeface(tv_point_month);
        tv_point_year_title = (TextView) view.findViewById(R.id.tv_point_year_title);
        tv_point_quarter_title = (TextView) view.findViewById(R.id.tv_point_quarter_title);
        tv_point_month_title = (TextView) view.findViewById(R.id.tv_point_month_title);
        tv_group_two_title = (TextView) view.findViewById(R.id.tv_point_group_two_title);
        btn_point_sum = (Button) view.findViewById(R.id.btn_point_sum);
        setTypeface(btn_point_sum);
        btn_point_welfare = (Button) view.findViewById(R.id.btn_point_welfare);

        layout_point_sign = (PointManualLayout) view.findViewById(R.id.layout_point_group_three_sign);
        layout_point_sign.updateView(true, Constant.USER_POINT_SIGN);
        layout_point_login = (PointManualLayout) view.findViewById(R.id.layout_point_group_three_login);
        layout_point_login.updateView(true, Constant.USER_POINT_FIRST_LOGIN);
        layout_point_wechat = (PointManualLayout) view.findViewById(R.id.layout_point_group_three_wechat);
        layout_point_wechat.updateView(true, Constant.USER_POINT_WECHAT);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyy/MM/dd");
        tv_group_two_title.setText("悦厅帮你赚积分（" + dateFormat.format(new Date()) + ")");
        PointOnClickListener clickListener = new PointOnClickListener();
        btn_point_sum.setOnClickListener(clickListener);
        btn_point_welfare.setOnClickListener(clickListener);
        layout_point_member.setOnClickListener(clickListener);
        layout_point_sign.setOnClickListener(clickListener);
        layout_point_login.setOnClickListener(clickListener);
        layout_point_wechat.setOnClickListener(clickListener);
        PointOnKeyListener keyListener = new PointOnKeyListener();
        btn_point_sum.setOnKeyListener(keyListener);
        btn_point_welfare.setOnKeyListener(keyListener);
        layout_point_member.setOnKeyListener(keyListener);
        layout_point_sign.setOnKeyListener(keyListener);
        layout_point_login.setOnKeyListener(keyListener);
        layout_point_wechat.setOnKeyListener(keyListener);
        PointOnFocusListener focusListener = new PointOnFocusListener();
//        layout_point_member.setOnFocusChangeListener(focusListener);
//        layout_point_sign.setOnFocusChangeListener(focusListener);
//        layout_point_login.setOnFocusChangeListener(focusListener);
//        layout_point_wechat.setOnFocusChangeListener(focusListener);

        rv_point_group_two = (CustomLinearRecyclerView) view.findViewById(R.id.rv_point_group_two);
        mLayoutManager = new CustomLinearLayoutManager(getContext());
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        rv_point_group_two.setLayoutManager(mLayoutManager);
        rv_point_group_two.setOnScrollListener(new PointOnScrollListener());
        rv_point_group_two.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                if (parent.indexOfChild(view) != 0) {
                    outRect.left = getResources().getDimensionPixelOffset(R.dimen.x48);
                }
            }
        });

        if(Util.getPartnerNo(getContext()).equals("80151104")){
            tv_point_continue_buy_title.setVisibility(View.GONE);
            tv_point_continue_buy.setVisibility(View.GONE);
            view.findViewById(R.id.divider_continue_buy).setVisibility(View.GONE);
        }
    }

    private void initData() {
        mHelper = LoginUserInformationHelper.getHelper(getContext());
        mPresenterImpl = new PointPresenterImpl();
        mPresenterImpl.setView(this);
        memoMap = new HashMap<>(1);
        if (mHelper.getIsLogin()) {
            memoMap.put("isLogin", "1");
        } else {
            memoMap.put("isLogin", "0");
        }

        RequestManager.getInstance().onAllEvent(new EventInfo(10220, "imp"), null, null, memoMap);

        if (mHelper.getIsLogin()) {
            mPresenterImpl.requestPointInfo(mHelper.getLoginPassport());
        } else {
            displayUnloginView();
        }

        if (mAdapter == null) {
            mAdapter = new PointAdapter(getContext(), rv_point_group_two);
            mAdapter.setFocusView(mFocusView);
            mAdapter.setIOnClickItemListener(this);
            rv_point_group_two.setAdapter(mAdapter);
        }
    }

    private void displayUnloginView() {
        mLoadingView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.GONE);
        mPointView.setVisibility(View.VISIBLE);
        tv_nickname.setText("请先登录");
        btn_point_sum.setText("得分");
        tv_rank.setVisibility(View.GONE);
        tv_rank_suf.setVisibility(View.GONE);
        tv_rank_pre.setText("赚积分赢礼品");
    }

    private void updateGroupOneView() {
        if (mGroupOneList != null && mGroupOneList.size() > 0) {
            for (UserPointInfo.DataBean tmpData : mGroupOneList) {
                if (tmpData == null) {
                    continue;
                }

                //update text by taskId
                switch (tmpData.getTaskId()) {
                    case Constant.USER_POINT_BUY_MONTH_VIP:
                        if (tmpData.getCurrentScore() <= 0) {
                            tv_point_month_title.setText("月会员");
                            setBigTextScore(tv_point_month, getResources().getDimensionPixelSize(R.dimen.x40), String.valueOf(tmpData.getScore()));
                        } else {
                            tv_point_month_title.setText("月会员(" + tmpData.getCurrentCount() + ")");
                            setBigTextScore(tv_point_month, getResources().getDimensionPixelSize(R.dimen.x40), "获赠" + tmpData.getScore());
                        }
                        break;
                    case Constant.USER_POINT_BUY_QUARTER_VIP:
                        if (tmpData.getCurrentScore() <= 0) {
                            tv_point_quarter_title.setText("季会员");
                            setBigTextScore(tv_point_quarter, getResources().getDimensionPixelSize(R.dimen.x40), String.valueOf(tmpData.getScore()));
                        } else {
                            tv_point_quarter_title.setText("季会员(" + tmpData.getCurrentCount() + ")");
                            setBigTextScore(tv_point_quarter, getResources().getDimensionPixelSize(R.dimen.x40), "获赠" + tmpData.getScore());
                        }
                        break;
                    case Constant.USER_POINT_BUY_YEAR_VIP:
                        if (tmpData.getCurrentScore() <= 0) {
                            tv_point_year_title.setText("年会员");
                            setBigTextScore(tv_point_year, getResources().getDimensionPixelSize(R.dimen.x40), String.valueOf(tmpData.getScore()));
                        } else {
                            tv_point_year_title.setText("年会员(" + tmpData.getCurrentCount() + ")");

                            setBigTextScore(tv_point_year, getResources().getDimensionPixelSize(R.dimen.x40), "获赠" + tmpData.getScore());
                        }
                        break;
                    case Constant.USER_POINT_CONTINUE_BUY_VIP:
                        if (tmpData.getCurrentScore() <= 0) {
                            tv_point_continue_buy_title.setText("连续包月");
                            setBigTextScore(tv_point_continue_buy, getResources().getDimensionPixelSize(R.dimen.x40), String.valueOf(tmpData.getScore()));

                        } else {
                            tv_point_continue_buy_title.setText("连续包月(" + tmpData.getCurrentCount() + ")");
                            setBigTextScore(tv_point_continue_buy, getResources().getDimensionPixelSize(R.dimen.x40), "获赠" + tmpData.getScore());
                        }
                        break;
                    default:
                        break;
                }

            }
        }
    }

    private void setBigTextScore(TextView textScore, int pixelSize, String ScoreText) {
        SpannableString sStr = new SpannableString(ScoreText + " 分");
        AppLogger.d(TAG, "sStr ? " + sStr);
        if (sStr != null && sStr.length() > 2) {
            AppLogger.d(TAG, "sStr.length() ? " + sStr.length());
            sStr.setSpan(new AbsoluteSizeSpan(pixelSize), 0, sStr.length()-2, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            textScore.setText(sStr);
        }
    }

    private void updateGroupTwoView() {
        mAdapter.setDataSource(mGroupTwoList);
        mAdapter.notifyDataSetChanged();
    }

    private void updateGroupThreeView() {
        if (mGroupThreeList == null || mGroupThreeList.size() <= 0) {
            return;
        }

        //iterator view and update view by its taskId
        for (UserPointInfo.DataBean tmpData : mGroupThreeList) {
            if (tmpData == null) {
                continue;
            }
            switch (tmpData.getTaskId()) {
                case Constant.USER_POINT_SIGN:
                    if (tmpData.getCurrentScore() > 0) {
                        layout_point_sign.updateView(false, tmpData.getTaskId());
                    } else {
                        layout_point_sign.updateView(true, tmpData.getTaskId());
                    }
                    break;
                case Constant.USER_POINT_FIRST_LOGIN:
                    if (tmpData.getCurrentScore() > 0) {
                        layout_point_login.updateView(false, tmpData.getTaskId());
                    } else {
                        layout_point_login.updateView(true, tmpData.getTaskId());
                    }
                    break;
                case Constant.USER_POINT_WECHAT:
                    if (tmpData.getCurrentScore() > 0) {
                        layout_point_wechat.updateView(false, tmpData.getTaskId());
                    } else {
                        layout_point_wechat.updateView(true, tmpData.getTaskId());
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * Show PopupWindow to display point record view or task detail view
     *
     * @param view target view you want to display on the PopUpWindow
     */
    private void showPopUpWindow(View view) {
        mPointWindow = new PopupWindow(view, getResources().getDimensionPixelOffset(R.dimen.x1920),
                getResources().getDimensionPixelOffset(R.dimen.y1080));
        mPointWindow.setFocusable(true);
        mPointWindow.setBackgroundDrawable(new BitmapDrawable());
        mPointWindow.update();
        mPointWindow.showAtLocation(getView(), Gravity.LEFT | Gravity.BOTTOM, 0, 0);
        mFocusView.setVisibility(View.GONE);
        mPointWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                mFocusView.setVisibility(View.VISIBLE);
            }
        });
    }

    /**
     * Custom OnClickListener for PointFragment
     */
    private class PointOnClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {

            //Action Report
            switch (v.getId()) {
                case R.id.btn_point_sum:
                    RequestManager.getInstance().onAllEvent(new EventInfo(10228, "clk"), null, null, memoMap);
                    break;
                case R.id.btn_point_welfare:
                    RequestManager.getInstance().onAllEvent(new EventInfo(10229, "clk"), null, null, memoMap);
                    break;
                case R.id.layout_point_member:
                    RequestManager.getInstance().onAllEvent(new EventInfo(10230, "clk"), null, null, memoMap);
                    break;
                case R.id.layout_point_group_three_sign:
                    RequestManager.getInstance().onPointManualClickEvent(Constant.USER_POINT_SIGN);
                    RequestManager.getInstance().onAllEvent(new EventInfo(10222, "clk"), null, null, memoMap);
                    break;
                case R.id.layout_point_group_three_login:
                    RequestManager.getInstance().onPointManualClickEvent(Constant.USER_POINT_FIRST_LOGIN);
                    RequestManager.getInstance().onAllEvent(new EventInfo(10223, "clk"), null, null, memoMap);
                    break;
                case R.id.layout_point_group_three_wechat:
                    RequestManager.getInstance().onPointManualClickEvent(Constant.USER_POINT_WECHAT);
                    RequestManager.getInstance().onAllEvent(new EventInfo(10224, "clk"), null, null, memoMap);
                    break;
                default:
                    break;
            }

            //Unlogin status
            if (!mHelper.getIsLogin()) {
                ActivityLauncher.startLoginActivity(getContext());
                return;
            }

            //Login status
            switch (v.getId()) {
                case R.id.btn_point_sum:
                    PointRecordLayout pointRecordLayout = new PointRecordLayout(getContext(), mHelper.getLoginPassport());
                    showPopUpWindow(pointRecordLayout);
                    break;
                case R.id.btn_point_welfare:
                    ActivityLauncher.startWelfareActivity(getContext());
                    return;
                case R.id.layout_point_member:
                    ActivityLauncher.startPayActivity(getContext(), PayActivity.PAY_SOURCE_POINT_FRAGMENT);
                    break;
                case R.id.layout_point_group_three_sign:
                    mPresenterImpl.reportSignIn(mHelper.getLoginPassport());
                    layout_point_sign.setClickable(false);
                    break;
                case R.id.layout_point_group_three_login:
                    ToastUtils.showToast(getContext(), "你已经登录成功！");
                    break;
                case R.id.layout_point_group_three_wechat:
                    if (layout_point_wechat.isEnabled()) {
//                        ActivityLauncher.startTvHelperActivity(getContext());
                        showWeixinhaoWindow();
                    } else {
                        ToastUtils.showToast(getContext(), "你已经成功关注公众号！");
                    }
                    break;
                default:
                    return;
            }
        }
    }

    private void showWeixinhaoWindow() {
        AppLogger.d(TAG, "showWeixinhaoWindow");
        if (mWeixinhaoWindow == null) {
            AppLogger.d(TAG, "mWeixinhaoWindow == null");
            mWeixinhaoWindow = new BasePopupWindow(mLayoutInflater.inflate(R.layout.weinxinhao_popup_window, null));
            qrcodeIV = (GlideImageView) mWeixinhaoWindow.getContentView().findViewById(R.id.qrcode_iv);
        }
        mWeixinhaoWindow.showWindow(getView());
        getQrcode();
        RequestManager.getInstance().onSearchVoiceExposureEvent();
//        RequestManager.getInstance().onClickMy(mChannelId, "zhushou");
    }

    private void getQrcode() {
        AppLogger.d(TAG, "getQrcode");
        NetworkApi.getWechatLogin(UserInfoHelper.getGid(), Constant.TYPE_CAPTCHA_BIND,
                new DisposableObserver<WechatPublic>() {
                    @Override
                    public void onNext(WechatPublic response) {
                        AppLogger.d(TAG, "getQrcode onNext");
                        if (null != response) {
                            String data = response.getData();
                            AppLogger.d(TAG, "getQrcode data ? " + data);
                            int status = response.getStatus();
                            if (status == 200 && null != data) {
                                qrcodeIV.setImageRes(data,getContext().getResources().getDrawable(R.drawable.bg_launcher_poster),
                                        getContext().getResources().getDrawable(R.drawable.bg_launcher_poster));
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        AppLogger.d(TAG, "getQrcode onError");
                        LibDeprecatedLogger.e("getWechatLogin() Error: " + e.getMessage(), e);
                    }

                    @Override
                    public void onComplete() {
                        AppLogger.d(TAG, "getQrcode onComplete");
                    }
                });
    }

    /**
     * Custom OnKeyListener for PointFragment when pressed down rather than up
     */
    private class PointOnKeyListener implements View.OnKeyListener {

        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if (event.getAction() == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                //pressed KEYCODE_DPAD_LEFT on the closet item to left view, focus on the pointed tab
                switch (v.getId()) {
                    case R.id.btn_point_sum:
                    case R.id.layout_point_group_three_sign:
                    case R.id.layout_point_member:
                        ((ListUserRelatedActivity) PointFragment.this.getActivity())
                                .focusLeftItem(ListUserRelatedActivity.LIST_INDEX_POINT);
                        return true;
                    default:
                        break;
                }
            } else if (event.getAction() == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                //pressed KEYCODE_DPAD_RIGHT on btn_point_member and layout_point_member, jump to the first item on the next line
                switch (v.getId()) {
                    case R.id.layout_point_member:
                        if (mAdapter != null && mLayoutManager != null) {
                            mAdapter.requestFocusAtPos(mLayoutManager.findFirstCompletelyVisibleItemPosition());
                        }
                        return true;
                    case R.id.btn_point_welfare:
                        layout_point_member.requestFocus();
                        return true;
                    case R.id.layout_point_group_three_wechat:
                        return true;
                }
            } else if (event.getAction() == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                switch (v.getId()) {
                    //pressed KEYCODE_DPAD_DOWN on layout_point_member, jump to the first item on the next line
                    case R.id.layout_point_member:
                        if (mAdapter != null && mLayoutManager != null) {
                            mAdapter.requestFocusAtPos(mLayoutManager.findFirstCompletelyVisibleItemPosition());
                        }
                        return true;
                    //do nothing when pressed KEYCODE_DPAD_DOWN on the last section
                    case R.id.layout_point_group_three_sign:
                    case R.id.layout_point_group_three_login:
                    case R.id.layout_point_group_three_wechat:
                        return true;
                }
            }
            return false;
        }
    }

    /**
     * Custom OnFocusListener for PointFragment
     */
    private class PointOnFocusListener implements View.OnFocusChangeListener {

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (mFocusView == null) {
                return;
            }

            switch (v.getId()) {
                case R.id.layout_point_member:
                case R.id.layout_point_group_three_sign:
                case R.id.layout_point_group_three_login:
                case R.id.layout_point_group_three_wechat:
                    if (hasFocus) {
                        mFocusView.setFocusView(v);
                    } else {
                        mFocusView.setUnFocusView(v);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private class PointOnScrollListener extends RecyclerView.OnScrollListener {

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);

            if (rv_point_group_two == null || mFocusView == null) {
                return;
            }
            if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                return;
            }

            View focusedView = rv_point_group_two.getFocusedChild();
            if (focusedView != null) {
                mFocusView.setFocusView(focusedView);
            }

        }
    }

}
