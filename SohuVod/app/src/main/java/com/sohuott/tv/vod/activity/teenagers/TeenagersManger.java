package com.sohuott.tv.vod.activity.teenagers;

import android.content.Context;
import android.os.Handler;
import android.os.Message;

import com.sohuott.tv.vod.activity.TeenagerLockActivity;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.PrefUtil;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohu.lib_utils.TimeCheckUtil;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 青少年管理类
 * @date 2022/1/12 2:26 下午
 * @Version 1.0
 */
public class TeenagersManger {
    private static TeenagersManger mInstance;

    /**
     * 青少年密码
     */
    public static final String PRE_KEY_TEENAGER = "teenager_password";
    /**
     * 青少年40分钟超时
     */
    public static final String PRE_KEY_TEENAGER_TIME = "teenager_time";
    /**
     * 青少年时间段 用于是否已经输入密码判断
     */
    public static final String PRE_KEY_TEENAGER_INTERVAL_TIME = "teenager_interval_time";

    /**
     * 线程池
     */
    private ScheduledExecutorService mScheduledExecutorService;

    /**
     * 时间段Future
     */
    private ScheduledFuture mScheduledFutureInterval;

    /**
     * 40分钟超时Handler
     */
    private final TimeOutHandler timeOutHandler;

    /**
     * 40分钟超时 what
     */
    private static final int TIME_OUT_KEY = 0xe101;

    /**
     * 时间段 任务线程
     */
    private final TimeIntervalThread mTimeIntervalThread;

    private WeakReference<Context> mContext;

    private final Long mDelayTime = 1L;

    private final Long mTime = 40L;

    private Long curr;

    private Long end;

    private Long start;

    public String mIntervalStart = "";

    TeenagersManger() {
        mScheduledExecutorService = Executors.newScheduledThreadPool(3);
//        mTimeThread = new TimeThread();
        timeOutHandler = new TimeOutHandler();
        mTimeIntervalThread = new TimeIntervalThread(this);
    }

    public static TeenagersManger getInstance() {
        if (mInstance == null) {
            mInstance = new TeenagersManger();
        }
        return mInstance;
    }


    /**
     * 开启时间段延迟任务
     *
     * @param context
     * @param time    返回的服务器时间
     */
    public void startTimeInterval(Context context, Long time) {
        this.mContext = new WeakReference<>(context);
        curr = time;
        int year = TimeCheckUtil.getYear(time);
        int month = TimeCheckUtil.getMonth(time);
        int day = TimeCheckUtil.getDay(time);
        int hour = TimeCheckUtil.getHour(time);
        int startHours = 22;
        // 是否需要加一天
        Boolean isInc;
        //如果当前时间小于6点开始时间减一天  否则加一天
        if (hour < 6) {
            isInc = false;
            long beforeTime = time - 86400000;
            int beforeYear = TimeCheckUtil.getYear(beforeTime);
            int beforeMonth = TimeCheckUtil.getMonth(beforeTime);
            int beforeDay = TimeCheckUtil.getDay(beforeTime);
//             hour = TimeCheckUtil.getHour(beforeTime);
//            startHours = 22;
            start = TimeCheckUtil.getHoursMillis(beforeYear, beforeMonth, beforeDay, startHours);
        } else {
            isInc = true;
//            startHours = 6;
            start = TimeCheckUtil.getHoursMillis(year, month, day, startHours);
        }
        mIntervalStart = TimeCheckUtil.getDate(time);
        if (isTeenagerInterval(context, mIntervalStart)) {
            return;
        }
        Long currAfter;
        if (isInc) {
            currAfter = time + 86400000;
        } else {
            currAfter = time;
        }
        end = TimeCheckUtil.getHoursMillis(TimeCheckUtil.getYear(currAfter), TimeCheckUtil.getMonth(currAfter), TimeCheckUtil.getDay(currAfter), 6);
        LibDeprecatedLogger.d("startTime" + start);
        LibDeprecatedLogger.d("endTime" + end);
        if (!checkTimeInterval()) {
            Long delayTime = start - curr;
            LibDeprecatedLogger.d("delayTime" + delayTime);
            mScheduledFutureInterval = mScheduledExecutorService.schedule(mTimeIntervalThread, delayTime, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 时间段限制任务
     */
    private static final class TimeIntervalThread implements Runnable {
        WeakReference<TeenagersManger> mWrapper;
        TimeIntervalThread(TeenagersManger teenagersManger){
            mWrapper = new WeakReference<>(teenagersManger);
        }
        @Override
        public void run() {
            TeenagersManger teenagersManger = mWrapper.get();
            if (teenagersManger != null){
                teenagersManger.runTimeIntervalFinish();
            }
        }
    }

    /**
     * 检查是否在限制时间段
     *
     * @return true false
     */
    private boolean checkTimeInterval() {
        if (curr >= start && curr < end) {
            runTimeIntervalFinish();
            return true;
        }
        return false;
    }

    /**
     * 结束时间段限制任务并跳转到解锁页面
     */
    private void runTimeIntervalFinish() {
        stopTimeInterval();
        ActivityLauncher.startChildLockActivity(mContext.get(), TeenagerLockActivity.TYPE_SEE_TIME_OUT_1);
    }

    /**
     * 40分钟任务
     *
     * @param context
     */
    public void startTimeOutTask(Context context) {
        this.mContext = new WeakReference<>(context);
        stopTimeOut();
        Long oldTime = getTimeOut();
        check(oldTime);
    }

    /**
     * 40分钟超时任务
     */

    private void sendTimeOutMessage() {
        timeOutHandler.sendEmptyMessageDelayed(TIME_OUT_KEY, mDelayTime * 1_000 * 60);
    }

    /**
     * 检查40分是否超时
     *
     * @param time
     */
    private void checkTimeOut(Long time) {
        Long oldTime = getTimeOut();
        Long newTime = oldTime + time;
        LibDeprecatedLogger.d("oldTime" + oldTime);
        LibDeprecatedLogger.d("newTime" + newTime);
        check(newTime);
        putTimeout(newTime, mContext.get());
    }

    private void check(Long checkTime) {
        if (checkTime >= mTime) {
            stopTimeOut();
            ActivityLauncher.startChildLockActivity(mContext.get(), TeenagerLockActivity.TYPE_SEE_TIME_OUT);
        } else {
            sendTimeOutMessage();
        }
    }

    /**
     * 停止40分钟轮询
     */
    public void stopTimeOut() {
        timeOutHandler.removeMessages(TIME_OUT_KEY);

    }

    /**
     * 取消时间范围限制的定时任务
     */
    public void stopTimeInterval() {
        if (mScheduledFutureInterval != null)
            mScheduledFutureInterval.cancel(true);
    }

    /**
     * 观看时间储存到本地
     *
     * @param time
     */
    public void putTimeout(Long time, Context context) {
        PrefUtil.putLong(PRE_KEY_TEENAGER_TIME, time);
    }

    /**
     * 获取已观看时间
     *
     * @return
     */
    private Long getTimeOut() {
        return PrefUtil.getLong(PRE_KEY_TEENAGER_TIME, 0L);
    }

    /**
     * 是否是青少年模式
     *
     * @return true是/false不是
     */
    public static Boolean isTeenager() {
        return !getTeenagerPassword().isEmpty();
    }


    /**
     * 获取密码
     *
     * @return 密码
     */
    public static String getTeenagerPassword() {
        return PrefUtil.getString( PRE_KEY_TEENAGER, "");
    }

    /**
     * 设置
     *
     * @param password 密码
     */
    public static void putTeenagerPassword(Context context, String password) {
        PrefUtil.putString( PRE_KEY_TEENAGER, password);
    }

    /**
     * 清空密码
     *
     * @param context
     */
    public static void clearTeenagerPassword(Context context) {
        putTeenagerPassword(context, "");
    }

    public static boolean isTeenagerInterval(Context context, String start) {
        String oldStart = getTeenagerIntervalTime(context);
        if (oldStart.isEmpty() || !oldStart.equals(start)) {
            return false;
        }
        return true;
    }

    public static String getTeenagerIntervalTime(Context context) {
        return PrefUtil.getString( PRE_KEY_TEENAGER_INTERVAL_TIME, "");
    }

    public static void putTeenagerIntervalTime(Context context, String start) {
        PrefUtil.putString( PRE_KEY_TEENAGER_INTERVAL_TIME, start);
    }

    private final class TimeOutHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == TIME_OUT_KEY) {
                checkTimeOut(mDelayTime);
            }
        }
    }

    /**
     * 青少年页面曝光
     */
    public void exposureTeenagerPage() {
        HashMap<String, String> map = new HashMap<>(1);
        map.put("pageId", "1025");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), map, null, null);
    }

    /**
     * 重新设置密码页面
     */
    public void exposureResetPasswordPage() {
        HashMap<String, String> map = new HashMap<>(1);
        map.put("pageId", "1024");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), map, null, null);
    }


    /**
     * 设置密码曝光
     */
    public void exposureSetPassword() {
        HashMap<String, String> map = new HashMap<>(1);
        map.put("pageId", "1023");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), map, null, null);
    }

    /**
     * 青少年引导页曝光
     */
    public void exposureTeenagerModePage() {
        HashMap<String, String> map = new HashMap<>(1);
        map.put("pageId", "1022");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), map, null, null);
    }

    /**
     * 青少年固定入口点击上报
     */
    public void exposureTeenagerClick() {
        exposureEventClk(10204);
    }


    /**
     * 40分钟超时
     */
    public void exposureTimeOut() {
        HashMap<String, String> map = new HashMap<>(1);
        map.put("reason", "1");
        RequestManager.getInstance().onAllEvent(new EventInfo(10219, "imp"), null, null, map);
    }

    /**
     * 禁播时间段
     */
    public void exposureInterval() {
        HashMap<String, String> map = new HashMap<>(1);
        map.put("reason", "2");
        RequestManager.getInstance().onAllEvent(new EventInfo(10219, "imp"), null, null, map);
    }


    /**
     * 重新设置密码成功曝光
     */
    public void exposureResetPasswordSuccess() {
        RequestManager.getInstance().onAllEvent(new EventInfo(10218, "slc"), null, null, null);
    }

    /**
     * 重新设置密码点击曝光
     */
    public void exposureResetPasswordClick() {
        exposureEventClk(10217);
    }

    /**
     * 显示青少年协议弹窗
     */
    public void exposureShowDecDialog() {
        HashMap<String, String> pathInfo = new HashMap<>(1);
        pathInfo.put("pageId", "1021");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), pathInfo, null, null);
    }

    /**
     * 青少年协议弹窗 我知道了
     */
    public void exposureShowDecDialogClickKnow() {
        exposureEventClk(10216);
    }

    /**
     * 青少年协议弹窗 开启
     */
    public void exposureShowDecDialogClickOpen() {
        exposureEventClk(10215);
    }

    /**
     * 青少年退出按钮上报
     */
    public void exposureTeenagerExitClick() {
        HashMap<String, String> map = new HashMap<>(1);
        map.put("pageId", "1025");
        RequestManager.getInstance().onAllEvent(new EventInfo(10234, "clk"), map, null, null);
    }


    /**
     * 按钮点击
     *
     * @param eventId id
     */
    private void exposureEventClk(int eventId) {
        RequestManager.getInstance().onAllEvent(new EventInfo(eventId, "clk"), null, null, null);
    }

    /**
     * 曝光
     *
     * @param eventId id
     */
    private void exposureEventImp(int eventId) {
        RequestManager.getInstance().onAllEvent(new EventInfo(eventId, "imp"), null, null, null);
    }

}
