package com.sohuott.tv.vod.activity.setting.privacy

import androidx.appcompat.widget.AppCompatTextView
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingTipItem

/**
 *
 * @Description
 * @date 2022/3/22 14:41
 * <AUTHOR>
 * @Version 1.0
 */
class PrivacyTipPresenter : DefaultPresenter(R.layout.item_privacy_setting_tip_layout) {
    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as PrivacySettingTipItem
        val name = viewHolder.getView<AppCompatTextView>(R.id.tv_privacy_tip)
        name.text = item.tip
//        name.setPadding(0, viewHolder.view.resources.getDimension(R.dimen.x24).toInt(), 0, 0)
    }
}