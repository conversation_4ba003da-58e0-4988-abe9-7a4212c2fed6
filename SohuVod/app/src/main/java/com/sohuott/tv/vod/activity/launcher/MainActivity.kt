package com.sohuott.tv.vod.activity.launcher

import android.animation.ValueAnimator
import android.content.Intent
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.KeyEvent
import android.view.View
import android.view.animation.TranslateAnimation
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.DiffCallback
import androidx.leanback.widget.HorizontalGridView
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.lib_viewbind_ext.viewBinding
import com.sh.ott.video.ad.AdRequestFactory
import com.sh.ott.video.base.component.ShDataSource
import com.sh.ott.video.base.component.ShPlayerConstants
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.PlayerLogger
import com.sh.ott.video.player.base.OnStateChangeListener
import com.sh.ott.video.player.base.SystemPlayerFactory
import com.sh.ott.video.view.ShVideoView
import com.sohu.lib_utils.StringUtil
import com.sohu.ott.ads.sdk.SdkFactory
import com.sohu.ott.ads.sdk.iterface.ILoader
import com.sohu.ott.ads.sdk.model.RequestComponent
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohuott.tv.base_room.manager.ImageInfoManager
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.app.SohuAppUtil
import com.sohuott.tv.vod.base_router.RouterPath
import com.sohuott.tv.vod.databinding.ActivityMainBinding
import com.sohuott.tv.vod.fragment.LauncherTabBridgeAdapter
import com.sohuott.tv.vod.fragment.lb.HomeContentFragment
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.launcher.HomeTab
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohuott.tv.vod.presenter.launcher.TitlePresenter
import com.sohuott.tv.vod.ui.ExitAppDialogNew
import com.sohuott.tv.vod.videodetail.activity.control.AdStartImageControlView
import com.sohuott.tv.vod.videodetail.activity.control.OnAdStartControlCallBack
import com.sohuott.tv.vod.widget.lb.TopViewBar
import com.sohuott.tv.vod.worker.UploadHandler
import java.lang.ref.WeakReference

@Route(path = RouterPath.Home.MAIN_ACTIVITY)
class MainActivity : AppCompatActivity(), TopViewBar.OnTopViewBarInteractionListener,
        OnAdStartControlCallBack, HomeContentFragment.OnFragmentInteractionListener {

    private val mViewBinding: ActivityMainBinding by viewBinding(ActivityMainBinding::bind)

    val horizontalGridView: HorizontalGridView?
        get() = mViewBinding.hgTitle
    val topViewBar: TopViewBar
        get() = mViewBinding.topBar

    private val mViewModel: MainViewModel by viewModels()

    private lateinit var mUser: LoginUserInformationHelper


    private var mTabData: MutableList<HomeTab.TabItem?>? = null

    private var mTabArrayObjectAdapter: ArrayObjectAdapter? = null
    private var mTabBridgeAdapter: LauncherTabBridgeAdapter? = null

    private var mViewPagerAdapter: LauncherViewPagerAdapter? = null

    private var mViewPagerOnPageChangeCallback: ViewPagerOnPageChangeCallback? = null

    private var drawableCrossFadeFactory: DrawableCrossFadeFactory =
            DrawableCrossFadeFactory.Builder(300).setCrossFadeEnabled(true).build()
    var isSkipTab = false
        private set

    private var mOldPosition = -1
    private var mOldTime: Long = 0
    private var mCurrentPosition = 0
    private var mLastPostion = 0

    private var mCurrentPageIndex = 0

    private var mIsGoToMine = false

    private var mCurrentTabType = 0

    //获取当前的tabcode
    var currentTabCode: Long = 0

    //频道皮肤配置
    private val mSkinMaps = HashMap<Int, String?>()

    //频道置灰配置
    private val mGrayMaps = HashMap<Int, Boolean>()


    private var mAdStartControlView: AdStartImageControlView? = null

    private var mNewAnim: ValueAnimator? = null

    private var mMainHandler: MainHandler? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        init()
        adPreLoad()
    }

    private fun init() {
        mUser = LoginUserInformationHelper.getHelper(applicationContext)
        mNewAnim = ValueAnimator.ofInt(0, 255)
        mViewPagerOnPageChangeCallback = ViewPagerOnPageChangeCallback()
        mMainHandler = MainHandler(this)
        initVideoView()
        initTabView()
        requestTopData()
        requestChannel()
        startImageInfoUpload()
    }

    private fun initVideoView() {
        mAdStartControlView = AdStartImageControlView(this)
        val videoParams = ShDataSource()
        videoParams.gid = UserInfoHelper.getGid() ?: ""
        videoParams.adType = ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_OPEN
        mViewBinding.splashVideoView.setDataSource(videoParams)
        mAdStartControlView?.setAdStartCallBack(this)
        mViewBinding.splashVideoView.addAdVideoControlComponent(mAdStartControlView!!)
        mViewBinding.splashVideoView.prepareAsync()
    }


    private fun initTabView() {
        mTabArrayObjectAdapter = ArrayObjectAdapter(TitlePresenter())
        mTabBridgeAdapter = LauncherTabBridgeAdapter(mTabArrayObjectAdapter)
        mViewBinding.hgTitle.adapter = mTabBridgeAdapter
        mViewPagerAdapter = LauncherViewPagerAdapter(this)
        initListener()
    }

    private fun initListener() {
        mViewBinding!!.hgTitle.addOnChildViewHolderSelectedListener(
                onChildViewHolderSelectedListener
        )
    }


    /**
     * 请求 top bar 数据
     */
    private fun requestTopData() {
        mViewModel.getTopBarData(mUser.loginPassport, mUser.loginToken)
        mViewModel.topData.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    LibDeprecatedLogger.d("requestTopData  success" + it.data.toString())
                    mViewBinding.topBar.setData(it.data)
                }

                Status.ERROR -> {
                    LibDeprecatedLogger.e("requestTopData  error" + it.message)
                }

                else -> {
                    LibDeprecatedLogger.e("requestTopData  else ")
                }
            }
        }
    }


    private fun requestChannel() {
        mViewModel.getChannelList()
        mViewModel.homeChannelData.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    LibDeprecatedLogger.d("频道列表: SUCCESS ${it.data?.data} }")
                    setTabData(it.data?.data)
                }

                Status.ERROR -> {
                    LibDeprecatedLogger.d("频道列表: ERROR }")
                }

                else -> {
                    LibDeprecatedLogger.d("频道列表: else }")
                }
            }
        }

    }

    private fun startImageInfoUpload() {
        LibDeprecatedLogger.e("musicyy startImageInfoUpload")
        val imageUploader = UploadHandler(this)
        imageUploader.startWork()
    }

    //广告预加载
    private fun adPreLoad() {
        try {
            val loader = SdkFactory.getInstance().createOralAdLoader(this)
            val component = RequestComponent()
            component.tuv = UserInfoHelper.getGid()
            component.site = "1"
            loader.adTs(component)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置tab layout
     */
    private fun setTabData(tabList: MutableList<HomeTab.TabItem?>?) {
        mTabData = tabList
        //查找联播type 并移除
        tabList?.find {
            it?.type == Constant.TYPE_CAROUSEL
        }?.let {
            mTabData?.remove(it)
        }
        //查找我的页面和 展示首页Type 位置
        mTabData?.indices?.forEach { index ->
            val data = mTabData?.get(index)
            if (data?.isFirstPage == 1) {
                Constant.TAG_FEATURE_POSITION = index
                data.isSelected = true
                return@forEach
            }
            if (data?.type == 107) {
                Constant.TAG_MY_POSITION = index
            }
        }
        mViewPagerAdapter!!.setData(mTabData)
        mTabArrayObjectAdapter?.addAll(0, mTabData)
        mViewBinding.viewpagerContent.adapter = mViewPagerAdapter
        mViewPagerOnPageChangeCallback?.let {
            mViewBinding.viewpagerContent.registerOnPageChangeCallback(
                    it
            )
        }
        val flag: Int = if (mIsGoToMine) {
            Constant.TAG_MY_POSITION
        } else {
            Constant.TAG_FEATURE_POSITION
        }
        if (mTabData!!.size > flag) {
            mViewBinding.hgTitle.selectedPosition = flag
            LibDeprecatedLogger.v("setUI setCurrentItemPosition : $flag, lastPos : $flag ")
            setCurrentItemPosition(flag)
            mCurrentTabType = mTabData!![flag]!!.type
            currentTabCode = mTabData!![flag]!!.id
            if (java.lang.Boolean.TRUE == mGrayMaps.get(currentTabCode.toInt())) {
                changeGray(true)
            }
        } else if (mTabData!!.size > 0) {
            mViewBinding.hgTitle.setSelectedPositionSmooth(0)
        }
        mIsGoToMine = false

    }

    private fun changeGray(flag: Boolean) {
        val paint = Paint()
        val cm = ColorMatrix()
        if (flag) {
            cm.setSaturation(0f) //灰度效果
            paint.colorFilter = ColorMatrixColorFilter(cm)
            window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, paint)
        } else {
            window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        }
    }

    private fun setCurrentItemPosition(position: Int) {
        mCurrentPageIndex = position
        mViewBinding.viewpagerContent.setCurrentItem(position, false)
    }


    override fun onTopViewBarInteraction(uri: Uri?) {
        if (uri.toString() == Constant.URI_CLICK_MY) {
            if (Constant.TAG_MY_POSITION != -1) {
                mViewBinding.hgTitle.setSelectedPositionSmooth(Constant.TAG_MY_POSITION)
                mViewBinding.hgTitle.requestFocus()
                refreshTabLayout()
                setCurrentItemPosition(Constant.TAG_MY_POSITION);
            }
        }
    }

    fun backToFirstPage() {
        LibDeprecatedLogger.d("backFirstPage")
        setCurrentItemPosition(Constant.TAG_FEATURE_POSITION)
    }

    fun backToMyPage() {
        LibDeprecatedLogger.d("backToMyPage")
        backToPosition(Constant.TAG_MY_POSITION)
    }


    fun setBackground(id: Int, url: String?) {
        mSkinMaps[id] = url
        if (id.toLong() == currentTabCode) {
            showNewWithAnim(id)
        }
    }

    fun backToPosition(pos: Int) {
        if (mViewBinding.hgTitle!!.visibility != View.VISIBLE) {
            mViewBinding.hgTitle!!.visibility = View.VISIBLE
        }
        for (i in mTabData!!.indices) {
            if (i != pos) {
                mTabData!![i]!!.isSelected = false
            } else {
                mTabData!![pos]!!.isSelected = true
            }
        }
        for (i in mTabData!!.indices) {
            mTabData!![i]!!.index = i
        }
        mViewBinding.hgTitle!!.selectedPosition = pos
        mViewBinding.hgTitle!!.requestFocus()
        refreshTabLayout()
        if (mViewBinding.topBar.isZoomOut) {
            mViewBinding.topBar.zoomOut()
        }
    }

    fun upToTopBar() {
        mViewBinding.topBar.zoomIn()
    }

    override fun adStart(topViewPath: String?, isTopView: Boolean) {
        mTopViewPath = topViewPath
    }

    override fun adSuccess() {
    }

    override fun adError() {
        removeAd()
    }

    override fun adFinish() {
        removeAd()
    }

    override fun adStartDetail(isPayAd: Boolean, isShown: Boolean, aid: Int, dataType: Int) {

    }

    private fun removeAd() {
        eventPermissionPage()
    }

    //请求权限
    private fun eventPermissionPage() {
        if (Build.VERSION.SDK_INT < 23 || XXPermissions.isGranted(
                        this,
                        *Permission.Group.STORAGE
                )
        ) {
            startTopView()
            //有权限
        } else {
            ARouter.getInstance().build(RouterPath.Permissions.STORAGE_PERMISSION_ACTIVITY)
                    .navigation(this, ACTIVITY_REQUEST_CODE_PERMISSION)
        }
        mViewBinding.splashVideoView.gone()
        mViewBinding.splashVideoView.release()
    }

    private var mTopVideoView: ShVideoView? = null

    /**
     * 初始化 Top view  播放地址
     */

    private var mTopViewPath: String? = null

    private fun startTopView() {
        if (mTopViewPath.isNullOrEmpty()) {
            PlayerLogger.i("initTopView mTopVideoView video path  is null")
            return
        }
        if (mTopVideoView != null) {
            PlayerLogger.i("initTopView mTopVideoView not is  null")
            return
        }
        if (mAdStartControlView?.isTopView == false) {
            PlayerLogger.i("initTopView mTopVideoView  is top view false")
            return
        }
        val animation = TranslateAnimation(
                0f,
                0f,
                0f,
                resources.getDimensionPixelSize(R.dimen.y688).toFloat()
        )
        animation.duration = 1000
        animation.fillAfter = true
        mViewBinding.viewpagerContent.startAnimation(animation)
        mViewBinding.cardTopVideoView.visible()
        mTopVideoView = ShVideoView(this)
        mTopVideoView?.setFilmPlayerFactory(SystemPlayerFactory.create())
        mTopVideoView?.setFilmScreenAspectRatioType(PlayerConstants.ScreenAspectRatio.MATCH_PARENT)
        mTopVideoView?.setDataSource(ShDataSource().also {
            it.url = mTopViewPath
        })
        mViewBinding.cardTopVideoView.addView(mTopVideoView)
        mTopVideoView?.addOnStateChangeListener(object : OnStateChangeListener {
            override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
                when (playState) {
                    PlayerConstants.VideoState.PREPARED -> {
                        AdRequestFactory.getInstants()
                                .reportStartPageTopViewAd(ILoader.TopViewState.PLAY_START)
                    }
                    /**
                     *  播放完成/出错
                     */
                    PlayerConstants.VideoState.PLAYBACK_COMPLETED,
                    PlayerConstants.VideoState.ERROR -> {
                        //上报
                        AdRequestFactory.getInstants()
                                .reportStartPageTopViewAd(ILoader.TopViewState.PLAY_END)
                        finishTopVideoView()
                    }
                }
            }
        })
        mTopVideoView?.prepareAsync()
    }

    private fun finishTopVideoView() {
        //隐藏/release view
        mViewBinding.cardTopVideoView.gone()
        mTopVideoView?.release()
        mViewBinding.cardTopVideoView.removeAllViews()
        //开启结束动画并刷新数据
        val animation =
                TranslateAnimation(
                        0f,
                        0f,
                        resources.getDimensionPixelSize(R.dimen.y688).toFloat(),
                        0f
                )
        animation.duration = 1000 // 设定动画的时长为1000毫秒(1秒)

        // 设定动画结束后的位置保持为动画的终点，不然动画结束后会回到初始位置
        animation.fillAfter = true
        mViewBinding.viewpagerContent.startAnimation(animation)
        if (mTabData != null && mViewPagerAdapter!!.getFragment(mCurrentPageIndex) != null && mViewPagerAdapter!!.getFragment(
                        mCurrentPageIndex
                ) is HomeContentFragment
        ) {
            (mViewPagerAdapter!!.getFragment(mCurrentPageIndex) as HomeContentFragment).refreshDataContent()
        }
    }

    private fun refreshTabLayout() {
        mTabArrayObjectAdapter!!.setItems(mTabData, object : DiffCallback<HomeTab.TabItem>() {
            override fun areItemsTheSame(
                    oldItem: HomeTab.TabItem,
                    newItem: HomeTab.TabItem
            ): Boolean {
                return true
            }

            override fun areContentsTheSame(
                    oldItem: HomeTab.TabItem,
                    newItem: HomeTab.TabItem
            ): Boolean {
//                        AppLogger.d("return name"+ ((HomeTab.TabItem)oldItem).name + " " + !(((HomeTab.TabItem)oldItem).index == mCurrentPosition || ((HomeTab.TabItem) oldItem).index == mLastPostion));
                return !(oldItem.index == mCurrentPosition || oldItem.index == mLastPostion)
            }
        })
    }

    fun showNewWithAnim(id: Int) {
        if (mSkinMaps[id] == null) {
            return
        }
        Glide.with(this).load(mSkinMaps[id])
                .transition(DrawableTransitionOptions.with(drawableCrossFadeFactory)).into(
                        mViewBinding.launcherBg
                )
        mViewBinding.launcherBg.visibility = View.VISIBLE
        mNewAnim!!.duration = 700
        mNewAnim!!.addUpdateListener { animation: ValueAnimator ->
            val currentValue = animation.animatedValue as Int
            mViewBinding.launcherBg.imageAlpha = currentValue
            mViewBinding.launcherBg.requestLayout()
        }
        mNewAnim!!.start()
    }

    fun hintNew() {
        mNewAnim!!.cancel()
        mViewBinding.launcherBg.visibility = View.GONE
    }


    inner class ViewPagerOnPageChangeCallback : ViewPager2.OnPageChangeCallback() {

        override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
        ) {
            super.onPageScrolled(position, positionOffset, positionOffsetPixels)
        }


        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            LibDeprecatedLogger.d("onPageSelected position: $position, lastPos : $mLastPostion , currentPos : $mCurrentPosition")
            isSkipTab = true
            if (position == mLastPostion) {
                //从viewpager中滑动的
                refreshTabLayout()
            }
            if (position != mCurrentPageIndex) {
                mViewBinding.hgTitle.selectedPosition = position
            }
            if (mSkinMaps[mTabData!![position]!!.type] == null) {
                if (mTabData!![position]!!.type == Constant.TYPE_VIP) {
                    mViewBinding.root.rootView?.setBackgroundResource(R.drawable.launcher_vip_bg)
                } else {
                    mViewBinding.root.rootView?.setBackgroundResource(R.drawable.launcher_bg)
                }
                hintNew()
            } else {
                showNewWithAnim(mTabData!![position]!!.type)
            }
            if (java.lang.Boolean.TRUE == mGrayMaps[mTabData!![position]!!.id.toInt()]) {
                changeGray(true)
            } else {
                changeGray(false)
            }
            if (mOldPosition == -1) {
                val pathInfo = HashMap<String, String>()
                pathInfo["pageId"] = StringUtil.toString(mTabData!![position]!!.id)
                val objectInfo = HashMap<String, String>()
                objectInfo["type"] = "page"
                objectInfo["id"] = StringUtil.toString(mTabData!![position]!!.id)
                val memoInfo = HashMap<String, String>()
                memoInfo["lastPage"] = "-1"
                memoInfo["stayTime"] = "0"
                RequestManager.getInstance().onAllEvent(
                        EventInfo(10135, "imp"),
                        pathInfo,
                        objectInfo,
                        memoInfo
                )
            } else {
                val pathInfo = HashMap<String, String>()
                pathInfo["pageId"] = StringUtil.toString(mTabData!![position]!!.id)
                val objectInfo = HashMap<String, String>()
                objectInfo["type"] = "page"
                objectInfo["id"] = StringUtil.toString(mTabData!![position]!!.id)
                val memoInfo = HashMap<String, String>()
                memoInfo["lastPage"] = StringUtil.toString(mTabData!![mOldPosition]!!.id)
                memoInfo["stayTime"] =
                        StringUtil.toString(System.currentTimeMillis() - mOldTime)
                RequestManager.getInstance().onAllEvent(
                        EventInfo(10135, "imp"),
                        pathInfo,
                        objectInfo,
                        memoInfo
                )
            }
            mOldTime = System.currentTimeMillis()
            mOldPosition = position
        }

        override fun onPageScrollStateChanged(state: Int) {
            super.onPageScrollStateChanged(state)
        }
    }

    private val onChildViewHolderSelectedListener: OnChildViewHolderSelectedListener =
            object : OnChildViewHolderSelectedListener() {
                override fun onChildViewHolderSelected(
                        parent: RecyclerView,
                        child: RecyclerView.ViewHolder?,
                        position: Int,
                        subposition: Int
                ) {
                    super.onChildViewHolderSelected(parent, child, position, subposition)
                    LibDeprecatedLogger.d("onChildViewHolderSelected mCurrentPosition :$mCurrentPosition mLastPostion : $mLastPostion")
                    if (position == mCurrentPosition) return
                    if (position == -1) return
                    if (mTabData == null) return
                    mLastPostion = mCurrentPosition
                    mCurrentPosition = position
                    for (i in mTabData!!.indices) {
                        if (i != mCurrentPosition) {
                            mTabData!![i]!!.isSelected = false
                        } else {
                            mTabData!![position]!!.isSelected = true
                        }
                    }
                    for (i in mTabData!!.indices) {
                        mTabData!![i]!!.index = i
                    }
                    if (mViewBinding.hgTitle.getScrollState() == RecyclerView.SCROLL_STATE_IDLE
                            && mViewBinding.hgTitle.isComputingLayout != true
                    ) {
                    }
                    mMainHandler?.removeMessages(HANDLER_MAIN_SELECT)
                    val msg = Message.obtain()
                    msg.what = HANDLER_MAIN_SELECT
                    msg.arg1 = mCurrentPosition
                    mMainHandler?.sendMessageDelayed(
                            msg,
                            HANDLER_MAIN_SELECT_DURATION.toLong()
                    )
                    if (mTabData != null) {
                        mCurrentTabType = mTabData!![position]!!.type
                        currentTabCode = mTabData!![position]!!.id
                    }
                }
            }

    /**
     * 按键处理
     */
    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (mViewBinding.splashVideoView.visibility == View.GONE) {
            if (mViewBinding.cardTopVideoView.visibility == View.VISIBLE) {
                finishTopVideoView()
                AdRequestFactory.getInstants().reportStartPageAdFinish(ILoader.PageAdState.CLOSE)
                return true
            }
            if (event?.action == KeyEvent.ACTION_DOWN && event.keyCode == KeyEvent.KEYCODE_BACK) {
                val mExitDialog = ExitAppDialogNew(this)
                if (mTabData != null && mTabData!!.size > mCurrentPageIndex) {
                    mExitDialog.setPageId(mTabData!![mCurrentPageIndex]!!.id)
                }
                mExitDialog.setExitAppListener(object : ExitAppDialogNew.ExitAppListener {
                    override fun exitApp() {
                        ImageInfoManager.saveCurrentQueue()
                        SohuAppUtil.exitApp(this@MainActivity)
                    }

                    override fun onDismiss() {
                    }
                })
                if (!mExitDialog.isShowing) {
                    mExitDialog.show()
                } else {
                    mExitDialog.dismiss()
                }
                return true
            }
        } else {
            if (event!!.keyCode == KeyEvent.KEYCODE_DPAD_CENTER || event.keyCode == KeyEvent.KEYCODE_ENTER) {
                mAdStartControlView?.startPayOrVideoDetailActivity()
                adFinish()
                return true
            }
            if (event.keyCode == KeyEvent.KEYCODE_BACK) {
                if (mAdStartControlView?.isTopView == true) {
                    AdRequestFactory.getInstants().reportStartPageAdFinish(ILoader.PageAdState.SKIP)
                }
                adFinish()
                return true
            }
            if (event.keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                if (mAdStartControlView?.falg == 0) {
                    adFinish()
                    return true
                }
            }
        }

        return super.dispatchKeyEvent(event)

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == ACTIVITY_REQUEST_CODE_PERMISSION) {
            startTopView()
        }
    }


    companion object {
        const val HANDLER_MAIN_SELECT = 0x1
        const val HANDLER_MAIN_SELECT_DURATION = 500

        const val ACTIVITY_REQUEST_CODE_PERMISSION = 1

        class MainHandler(activity: MainActivity) : Handler(Looper.getMainLooper()) {
            private val weakReference: WeakReference<MainActivity>

            init {
                weakReference = WeakReference(activity)
            }

            override fun handleMessage(msg: Message) {
                val mainActivity = weakReference.get()
                if (mainActivity != null) {
                    when (msg.what) {
                        HANDLER_MAIN_SELECT -> {
                            LibDeprecatedLogger.d("handle message : position : " + msg.arg1)
                            mainActivity.setCurrentItemPosition(msg.arg1)
                        }
                    }
                }
                super.handleMessage(msg)
            }

        }
    }

    override fun onFragmentInteraction(uri: Uri?) {

    }

}