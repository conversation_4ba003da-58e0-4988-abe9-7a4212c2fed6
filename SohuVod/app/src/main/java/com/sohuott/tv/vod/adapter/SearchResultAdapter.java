package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Message;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchResultActivity;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.SearchResult;
import com.sohuott.tv.vod.presenter.SearchResultContract;
import com.sohuott.tv.vod.presenter.SearchResultPresenter;
import com.sohuott.tv.vod.ui.SearchResultRecyclerView;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.SearchResultBigPicItemView;
import com.sohuott.tv.vod.view.SearchResultSmallPicItemView;
import java.util.List;

/**
 * Created by fenglei on 17-6-22.
 */
public class SearchResultAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements SearchResultContract.SearchResultView {
    private String mPageName = "6_search_result";

    public void setPageName(String pageName){
        this.mPageName = pageName;
    }

    public static class SearchResultItemDecoration extends RecyclerView.ItemDecoration {
        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            int position = ((RecyclerView.LayoutParams) view.getLayoutParams()).getViewLayoutPosition();
            int viewType = parent.getAdapter().getItemViewType(position);
            if (viewType == SearchResultAdapter.VIEW_TYPE_SEARCH_RESULT_TEXT_TITLE) {
                outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y82);
            } else if (viewType == SearchResultAdapter.VIEW_TYPE_SEARCH_RESULT_BIG_PIC) {
                outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y15);
                outRect.right = (int) view.getContext().getResources().getDimension(R.dimen.y18);
                if (parent.getAdapter() != null) {
                    SearchResultAdapter searchResultAdapter = (SearchResultAdapter) parent.getAdapter();
                    int bigPicCount = searchResultAdapter.calcuBigPicCount();
                    if (searchResultAdapter.getItemCount() - 1 == bigPicCount) {
                        if (position >= bigPicCount - (bigPicCount % 2 == 0 ? 1 : 0)) {
                            outRect.bottom = (int) view.getContext().getResources().getDimension(R.dimen.y60);
                        }
                    }
                }
            } else {
                if (parent.getAdapter() != null) {
                    SearchResultAdapter searchResultAdapter = (SearchResultAdapter) parent.getAdapter();
                    int bigPicCount = searchResultAdapter.calcuBigPicCount();
                    if (position > bigPicCount && position <= bigPicCount + 4) {
                        if (bigPicCount == 0) {
                            outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y15);
                        } else {
                            outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y50);
                        }
                    }
                    outRect.bottom = (int) view.getContext().getResources().getDimension(R.dimen.y60);
                }
            }
        }
    }

    class SearchResultTitleViewHolder extends RecyclerView.ViewHolder {
        public TextView titleTV;

        SearchResultTitleViewHolder(View view) {
            super(view);
            titleTV = (TextView) view;
        }

        public void bind() {
            if (dataBean != null && dataBean.getPageInfo() != null) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("搜索“").append(query).append("”结果  ");
                int length = stringBuilder.length();
//                stringBuilder.append("1").append(" / ");
                stringBuilder.append(dataBean.getPageInfo().getTotal());
                stringBuilder.append("个");
                SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(stringBuilder.toString());
                ForegroundColorSpan yellowSpan = new ForegroundColorSpan(Color.parseColor("#e8e8e8"));
                spannableStringBuilder.setSpan(yellowSpan, length, stringBuilder.length() - 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                titleTV.setText(spannableStringBuilder);
            }
        }
    }

    class SearchResultBigPicViewHolder extends RecyclerView.ViewHolder
            implements View.OnKeyListener, View.OnFocusChangeListener {

        SearchResultBigPicViewHolder(View view) {
            super(view);
            view.setOnKeyListener(this);
            view.setOnFocusChangeListener(this);
//            ((SearchResultBigPicItemView) view).setFocusBorderView(focusBorderView);
        }

        public void bind(int position) {
            SearchResultBigPicItemView bigPicItemView = (SearchResultBigPicItemView) itemView;
            if (dataBean != null && dataBean.getItems() != null) {
                int index = position - 1;
                if (index < dataBean.getItems().size()) {
                    bigPicItemView.setData(position, dataBean.getItems().get(index));
                }
            }
        }

        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if (event.getAction() == KeyEvent.ACTION_DOWN) {
                if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
//                    v.focusSearch(View.FOCUS_UP);
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
//                    v.focusSearch(View.FOCUS_DOWN);
//                    getNextPage(getAdapterPosition());
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                    View nextFocusedView = v.focusSearch(View.FOCUS_LEFT);
                    if (!(nextFocusedView instanceof SearchResultBigPicItemView)) {
                        lastFocusedView = v;
                        return ((SearchResultActivity) v.getContext()).leftBtnRequestFocus();
                    }
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    return handleRightKey(v);
                }
            }
            return false;
        }

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                if (recyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                    return;
                }
                setFocusBord(v);
                ((SearchResultBigPicItemView) v).setOnFocus();
            } else {
                setUnFocusBord(v);
                FocusUtil.setUnFocusAnimator(v, 100);
                ((SearchResultBigPicItemView) v).setUnFocus();
            }
        }
    }

    class SearchResultSmallPicViewHolder extends RecyclerView.ViewHolder
            implements View.OnKeyListener, View.OnFocusChangeListener {

        SearchResultSmallPicViewHolder(View view) {
            super(view);
            view.setOnKeyListener(this);
            view.setOnFocusChangeListener(this);
//            view.setOnFocusChangeListener(onFocusChangeListener);
//            ((SearchResultSmallPicItemView) view).setFocusBorderView(focusBorderView);
        }

        public void bind(int position) {
            SearchResultSmallPicItemView smallPicItemView = (SearchResultSmallPicItemView) itemView;
            if (dataBean != null && dataBean.getItems() != null) {
                int smallPicIndex = position - 1;
                if (smallPicIndex < dataBean.getItems().size()) {
                    smallPicItemView.setData(position, dataBean.getItems().get(smallPicIndex));
                }
            }
        }

        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if (event.getAction() == KeyEvent.ACTION_DOWN) {
                if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
//                    v.focusSearch(View.FOCUS_UP);
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
//                    v.focusSearch(View.FOCUS_DOWN);
//                    getNextPage(getAdapterPosition());
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                    View nextFocusedView = v.focusSearch(View.FOCUS_LEFT);
                    if (!(nextFocusedView instanceof SearchResultSmallPicItemView)) {
                        lastFocusedView = v;
                        return ((SearchResultActivity) v.getContext()).leftBtnRequestFocus();
                    }
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    return handleRightKey(v);
                }
            }
            return false;
        }

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                if (recyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                    return;
                }
                setFocusBord(v);
                ((SearchResultSmallPicItemView) v).setOnFocus();
            } else {
                setUnFocusBord(v);
                FocusUtil.setUnFocusAnimator(v, 100);
                ((SearchResultSmallPicItemView) v).setUnFocus();
            }
        }
    }

    protected void setFocusBord(View v){
        if (focusBorderView != null) {
            focusBorderView.setFocusView(v);
            FocusUtil.setFocusAnimator(v, focusBorderView, FocusUtil.HOME_SCALE, 100);
        }
    }

    protected void setUnFocusBord(View v){
        if (focusBorderView != null) {
            focusBorderView.setUnFocusView(v);
        }
    }

    private boolean handleRightKey(View currentView) {
        GridLayoutManager.LayoutParams currentLayoutParams;
        currentLayoutParams = (GridLayoutManager.LayoutParams) currentView.getLayoutParams();
        int currentViewPosition = currentLayoutParams.getViewAdapterPosition();
        if (currentLayoutParams.getViewAdapterPosition() == getItemCount() - 1) {
            return true;
        }
        int spanIndex = currentLayoutParams.getSpanSize() + currentLayoutParams.getSpanIndex();
        if (spanIndex == gridLayoutManager.getSpanCount()) {
            View nextView = gridLayoutManager.findViewByPosition(currentViewPosition + 1);
            if (nextView != null && nextView.isFocusable()) {
                return nextView.requestFocus();
            }
        }
        View nextFocusedView = currentView.focusSearch(View.FOCUS_RIGHT);
        if (nextFocusedView == null) {
            return true;
        }
        GridLayoutManager.LayoutParams nextFocusedViewLayoutParams =
                (GridLayoutManager.LayoutParams) nextFocusedView.getLayoutParams();
        int nextGroupIndex = gridLayoutManager.getSpanSizeLookup().
                getSpanGroupIndex(nextFocusedViewLayoutParams.getViewAdapterPosition(), gridLayoutManager.getSpanCount());
        int currentGroupIndex = gridLayoutManager.getSpanSizeLookup().
                getSpanGroupIndex(currentLayoutParams.getViewAdapterPosition(), gridLayoutManager.getSpanCount());
        LibDeprecatedLogger.d("nextGroupIndex = " + nextGroupIndex + ", currentGroupIndex = " + currentGroupIndex);
        if (nextGroupIndex != currentGroupIndex) {
            return true;
        }
        return false;
    }

//    private void getNextPage(int position) {
//        int lastGroupIndex = gridLayoutManager.getSpanSizeLookup().
//                getSpanGroupIndex(getItemCount() - 1, gridLayoutManager.getSpanCount());
//        int currentGroupIndex = gridLayoutManager.getSpanSizeLookup().
//                getSpanGroupIndex(position, gridLayoutManager.getSpanCount());
//        Logger.d(TAG, "position = " + position + ", lastGroupIndex = " + lastGroupIndex
//                + ", currentGroupIndex = " + currentGroupIndex);
//        if (currentGroupIndex == lastGroupIndex - 2) {
//            if (dataBean != null && dataBean.getPageInfo() != null) {
//                int total = dataBean.getPageInfo().getTotal();
//                int totalPage = total / PAGE_SIZE + (total % PAGE_SIZE != 0 ? 1 : 0);
//                if (currentPage < totalPage) {
//                    searchResultPresenter.getSearchResult(query, type, ++currentPage, PAGE_SIZE);
//                }
//            }
//        }
//    }

    protected void getSearchResult(String query, int type, int page, int pageSize){
        if (searchResultPresenter!=null)
            searchResultPresenter.getSearchResult(query, type, page, pageSize);
    }

    @Override
    public void showSearchResult(SearchResult.DataBean dataBean, int type) {
        if (type != this.type) {
            searchResultPresenter.cancel(type);
            return;
        }
        if (dataBean != null && dataBean.getItems().size() > 0) {
            int positionStart = getItemCount();
            int itemCount = dataBean.getItems().size();
            this.dataBean.getItems().addAll(dataBean.getItems());
            ((SearchResultActivity) context).updateBtnListWithData(dataBean);
            notifyItemRangeInserted(positionStart, itemCount);
        }
        isLoadingMore = false;
    }

    public class SearchResultSpinSizeLookUp extends GridLayoutManager.SpanSizeLookup {
        @Override
        public int getSpanSize(int position) {
            if (position == 0) {
                return SPAN_COUNT;
            }
            int bigPicCount = calcuBigPicCount();
            if (position < bigPicCount) {
                return SPAN_COUNT / 2;
            } else if (position == bigPicCount) {
                if (bigPicCount % 2 == 0) {
                    return SPAN_COUNT / 2;
                } else {
                    return SPAN_COUNT;
                }
            } else {
                return 1;
            }
        }
    }

    public static final int VIEW_TYPE_SEARCH_RESULT_TEXT_TITLE = 0;
    public static final int VIEW_TYPE_SEARCH_RESULT_BIG_PIC = 1;
    public static final int VIEW_TYPE_SEARCH_RESULT_SMALL_PIC = 2;

    public static final int PAGE_SIZE = 10;
    public static final int SPAN_COUNT = 4;

    private Context context;
    private SearchResultRecyclerView recyclerView;
    private GridLayoutManager gridLayoutManager;
    private String query;
    private int type;
    private int currentPage;
    private SearchResult.DataBean dataBean;
    protected SearchResultContract.Presenter searchResultPresenter;
    private boolean isLoadingMore;

    //message.what for beginning to load images
    private static final int MSG_IMG_LOAD = 1;
    private ImgLoadHandler myImgLoadHandler;
//    private View.OnFocusChangeListener onFocusChangeListener = new ViewOnFocusChangeListener();

    public class SearchScrollListener extends RecyclerView.OnScrollListener {
        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
            int lastVisibleItemPosition = gridLayoutManager.findLastVisibleItemPosition();
            if (!isLoadingMore && lastVisibleItemPosition >= getItemCount() - 4) {
                if (dataBean != null && dataBean.getPageInfo() != null) {
                    int total = dataBean.getPageInfo().getTotal();
                    int totalPage = total / PAGE_SIZE + (total % PAGE_SIZE != 0 ? 1 : 0);
                    if (currentPage < totalPage) {
                        isLoadingMore = true;
                        //searchResultPresenter.getSearchResult(query, type, ++currentPage, PAGE_SIZE,1);
                        getSearchResult(query, type, ++currentPage, PAGE_SIZE);
                    }
                }
            }
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                return;
            }
            if (recyclerView == null || focusBorderView == null || recyclerView.getFocusedChild() == null) {
                return;
            }
            RecyclerView.ViewHolder viewHolder = recyclerView.getChildViewHolder(
                    recyclerView.getFocusedChild());
            if (viewHolder != null && viewHolder.itemView != null) {
                setFocusBord(viewHolder.itemView);
//                focusBorderView.setFocusView(viewHolder.itemView);
//                FocusUtil.setFocusAnimator(viewHolder.itemView, focusBorderView, FocusUtil.HOME_SCALE, 100);
                if(viewHolder.itemView instanceof SearchResultBigPicItemView) {
                    ((SearchResultBigPicItemView) viewHolder.itemView).setOnFocus();
                } else if(viewHolder.itemView instanceof SearchResultSmallPicItemView) {
                    ((SearchResultSmallPicItemView) viewHolder.itemView).setOnFocus();
                }
            }

            //send message to bind and load image
            myImgLoadHandler.removeCallbacksAndMessages(MSG_IMG_LOAD);
            myImgLoadHandler.sendEmptyMessageDelayed(MSG_IMG_LOAD, 500);
        }
    }

    public SearchResultAdapter(Context context, SearchResultRecyclerView recyclerView,
                               GridLayoutManager gridLayoutManager, SearchResult.DataBean dataBean, int type) {
        this.context = context;
        this.recyclerView = recyclerView;
        this.gridLayoutManager = gridLayoutManager;
        this.dataBean = dataBean;
        this.type = type;
        this.currentPage = 1;
        searchResultPresenter = new SearchResultPresenter(this);
        myImgLoadHandler = new ImgLoadHandler();
//        initImageLoaderOptions(context);
    }

    public void setData(SearchResult.DataBean dataBean, int type, int currentPage, String query) {
        this.dataBean = dataBean;
        this.type = type;
        this.currentPage = currentPage;
        this.query = query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public SearchResultBigPicItemView creatSearchResultBigPicItemView(Context context){
        return new SearchResultBigPicItemView(context);
    }

    public SearchResultSmallPicItemView creatSearchResultSmallPicItemView(Context context){
        return new SearchResultSmallPicItemView(context);
    }

    public View customSearchResultRecyclerviewTitle(ViewGroup parent){
        return LayoutInflater.from(parent.getContext()).inflate(
                R.layout.search_result_recyclerview_title, parent, false);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v;
        RecyclerView.ViewHolder viewHolder = null;
        if (viewType == VIEW_TYPE_SEARCH_RESULT_TEXT_TITLE) {
            v = customSearchResultRecyclerviewTitle(parent);
            viewHolder = new SearchResultTitleViewHolder(v);
        } else if (viewType == VIEW_TYPE_SEARCH_RESULT_BIG_PIC) {
            SearchResultBigPicItemView bigPicItemView = creatSearchResultBigPicItemView(parent.getContext());
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(
                    (int) parent.getContext().getResources().getDimension(R.dimen.x658),
                    (int) parent.getContext().getResources().getDimension(R.dimen.y360)
            );
            bigPicItemView.setLayoutParams(layoutParams);
            bigPicItemView.setAlbumResize(
                    parent.getContext().getResources().getDimensionPixelSize(R.dimen.x262),
                    parent.getContext().getResources().getDimensionPixelSize(R.dimen.y360)
            );
            bigPicItemView.setPageName(mPageName);
            viewHolder = new SearchResultBigPicViewHolder(bigPicItemView);
        } else if (viewType == VIEW_TYPE_SEARCH_RESULT_SMALL_PIC) {
            SearchResultSmallPicItemView smallPicItemView = creatSearchResultSmallPicItemView(parent.getContext());
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(
                    (int) parent.getContext().getResources().getDimension(R.dimen.x320),
                    (int) parent.getContext().getResources().getDimension(R.dimen.y260)
            );
            smallPicItemView.setLayoutParams(layoutParams);
            smallPicItemView.setAlbumResize(
                    parent.getContext().getResources().getDimensionPixelSize(R.dimen.x320),
                    parent.getContext().getResources().getDimensionPixelSize(R.dimen.y213)
                    );
            smallPicItemView.setPageName(mPageName);
            viewHolder = new SearchResultSmallPicViewHolder(smallPicItemView);
        }
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        int viewType = holder.getItemViewType();
        if (viewType == VIEW_TYPE_SEARCH_RESULT_TEXT_TITLE) {
            SearchResultTitleViewHolder titleViewHolder = (SearchResultTitleViewHolder) holder;
            titleViewHolder.bind();
        } else if (viewType == VIEW_TYPE_SEARCH_RESULT_BIG_PIC) {
            SearchResultBigPicViewHolder bigPicViewHolder = (SearchResultBigPicViewHolder) holder;
            bigPicViewHolder.bind(position);
        } else if (viewType == VIEW_TYPE_SEARCH_RESULT_SMALL_PIC) {
            SearchResultSmallPicViewHolder smallPicViewHolder = (SearchResultSmallPicViewHolder) holder;
            smallPicViewHolder.bind(position);
        }
    }

    @Override
    public int getItemCount() {
        if (dataBean == null || dataBean.getItems() == null
                || dataBean.getItems().size() == 0) {
            return 0;
        }
        int totalPage = 0;
        if (dataBean.getPageInfo() != null) {
            int total = dataBean.getPageInfo().getTotal();
            totalPage = total / PAGE_SIZE + (total % PAGE_SIZE != 0 ? 1 : 0);
        }
        int bigPicCount = calcuBigPicCount();
        if (bigPicCount == dataBean.getItems().size()) {
            if (currentPage < totalPage) {
                return 1 + bigPicCount / 2 * 2;
            } else {
                return 1 + dataBean.getItems().size();
            }
        } else {
            if (currentPage < totalPage) {
                return 1 + bigPicCount + (dataBean.getItems().size() - bigPicCount) / 4 * 4;
            } else {
                return 1 + dataBean.getItems().size();
            }
        }
//        return dataBean.getItems().size() + 1;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return VIEW_TYPE_SEARCH_RESULT_TEXT_TITLE;
        }
        int bigPicCount = calcuBigPicCount();
        if (position <= bigPicCount) {
            return VIEW_TYPE_SEARCH_RESULT_BIG_PIC;
        } else {
            return VIEW_TYPE_SEARCH_RESULT_SMALL_PIC;
        }
    }

    private int calcuBigPicCount() {
        int bigPicCount = 0;
        if (dataBean != null && dataBean.getItems() != null) {
            List<SearchResult.DataBean.ItemsBean> itemsBeanList = dataBean.getItems();
            for (int i = 0; i < itemsBeanList.size(); i++) {
                if(itemsBeanList.get(i).getDataType().equals("star")){
                    bigPicCount++;
                   continue;
                }
                if (!itemsBeanList.get(i).getDataType().equals("album")) {
                    break;
                }
                bigPicCount++;
            }
        }
        return bigPicCount;
    }

//    private DisplayImageOptions displayImageOptions;
//
//    private void initImageLoaderOptions(Context context) {
//        DisplayImageOptions.Builder builder = new DisplayImageOptions.Builder();
//        builder.cacheOnDisk(true);
//        builder.extraForDownloader(Util.getHeaders(context));
//        builder.showImageForEmptyUri(R.drawable.vertical_default_big_poster);
//        builder.showImageOnFail(R.drawable.vertical_default_big_poster);
//        builder.displayer(new FadeInBitmapDisplayer(300));
//        displayImageOptions = builder.build();
//    }

    private View lastFocusedView;

    public boolean rightKeyRequestFocus() {
        if (lastFocusedView != null) {
            return lastFocusedView.requestFocus();
        }
        return false;
    }

    public void resetLastFocusedView() {
        lastFocusedView = null;
    }

    protected FocusBorderView focusBorderView;

    public void setFocusBorderView(FocusBorderView focusView) {
        focusBorderView = focusView;
    }

//    public class ViewOnFocusChangeListener implements View.OnFocusChangeListener {
//        @Override
//        public void onFocusChange(View v, boolean hasFocus) {
//            if (hasFocus) {
//                if (recyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
//                    return;
//                }
//                if (focusBorderView != null) {
//                    focusBorderView.setFocusView(v);
//                    FocusUtil.setFocusAnimator(v, focusBorderView, FocusUtil.HOME_SCALE, 100);
//                }
//            } else {
//                if (focusBorderView != null) {
//                    focusBorderView.setUnFocusView(v);
//                }
//                FocusUtil.setUnFocusAnimator(v, 100);
//            }
//        }
//    }

    /**
     * Bind and load image to the visible ImageViews
     */
    private void bindImage() {
        int firstPos = gridLayoutManager.findFirstVisibleItemPosition();
        int lastPos = gridLayoutManager.findLastVisibleItemPosition();

        RecyclerView.ViewHolder viewHolder;
        for (int i = firstPos; i <= lastPos; i++) {
            viewHolder = recyclerView.findViewHolderForLayoutPosition(i);
            if (viewHolder == null) {
                continue;
            }
            if (viewHolder instanceof SearchResultBigPicViewHolder) {
                ((SearchResultBigPicViewHolder) viewHolder).bind(i);
            }else if(viewHolder instanceof SearchResultSmallPicViewHolder) {
                ((SearchResultSmallPicViewHolder) viewHolder).bind(i);
            }
        }
    }

    /**
     * Custom Handler to delay to bind and load image to the certain ImageView
     */
    private class ImgLoadHandler extends Handler {
        @Override
        public void dispatchMessage(Message msg) {
            super.dispatchMessage(msg);
            if (msg.what == MSG_IMG_LOAD) {
                bindImage();
            }
        }
    }

    public void releaseAll() {
        if (myImgLoadHandler != null) {
            myImgLoadHandler.removeCallbacksAndMessages(null);
            myImgLoadHandler = null;
        }
    }


}
