package com.sohuott.tv.vod.videodetail.activity;

import com.sohuott.tv.vod.lib.model.AlbumInfo;
import com.sohuott.tv.vod.videodetail.BasePresenter;
import com.sohuott.tv.vod.videodetail.BaseView;

import io.reactivex.Observable;

/**
 * Created by XiyingCao on 2018/1/11.
 */

public interface VideoDetailContract {
    interface View extends BaseView<Presenter> {
        void addAlbumData(AlbumInfo model);

        void onAlbumError(int status);

        void showLoading();

        void hideLoading();

        void getServiceTimeSuccess(Long time);

        void getServiceTimeFail();
    }

    interface Presenter extends BasePresenter {
        int getCheckUserStatus();

        String getPassport();

        int getAid();

        Observable<Integer> getPlayVid(boolean isChildVersion);

        int getVideoType();

        boolean isDts();

        void setCheckUserStatus(int status);

        void setIsFirstInit(boolean isFirstInit);

        int getPageSource();

        void getServiceTime();
    }
}
