package com.sohuott.tv.vod.activity.setting.privacy

import android.content.Context
import androidx.leanback.widget.Presenter
import androidx.leanback.widget.PresenterSelector
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingHeaderItem
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingItem

/**
 *
 * @Description
 * @date 2022/3/23 10:06
 * <AUTHOR>
 * @Version 1.0
 */
class PrivacySettingTabListSelector(private val context: Context) : PresenterSelector() {
    override fun getPresenter(item: Any?): Presenter {
        when (item) {
            is PrivacySettingItem -> {
                return PrivacySettingTabItemPresenter(context)
            }
            is PrivacySettingHeaderItem -> {
                return PrivacySettingTabHeaderPresenter()
            }
        }
        throw RuntimeException("PrivacySettingTabListSelector is not found this item ")
    }

}