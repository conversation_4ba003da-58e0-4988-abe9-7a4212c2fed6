package com.sohuott.tv.vod.activity.setting.play

data class SettingItem(
    /**
     * 展示类型
     * @see SETTING_TYPE_HEADER
     * @see SETTING_TYPE_ITEM
     * @see SETTING_TYPE_TIPS
     * @see SETTING_TYPE_ITEM_IMG_TIPS
     */
    val type: Int = -1,
    /**
     * 分组名
     */
    val groupName: String? = null,
    /**
     * item内容
     */
    val content: String? = null,
    /**
     * 是否已经被选中
     */
    val isSelected: Boolean = false,
    /**
     * 提示
     */
    val tips: String? = null,

    /**
     * 是否显示图片
     */
    var isShowImgTips: Boolean = false,

    /**
     * 图片资源
     */
    var imgTipsResId: Int = -1,

    /**
     * 唯一标识
     */
    var id: String? = null,

    var focusable: Boolean = true,

    /**
     * 向下事件拦截
     * true 不拦截
     * false 拦截
     */
    var nextKewDown: Boolean = true
) {

}

//分组名
const val SETTING_TYPE_HEADER = 0

//正常item
const val SETTING_TYPE_ITEM = 1

//提示
const val SETTING_TYPE_TIPS = 2

//图片角标item
const val SETTING_TYPE_ITEM_IMG_TIPS = 3
