package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.NinePatchDrawable;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.bumptech.glide.Priority;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.ImageViewTarget;
import com.bumptech.glide.request.target.Target;
import com.sohuott.tv.vod.GlideApp;
import com.sohuott.tv.vod.GlideRequest;
import com.sohuott.tv.vod.GlideRequests;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RoundedCornersTransformation;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohu.lib_utils.StringUtil;

import static com.bumptech.glide.request.RequestOptions.bitmapTransform;

import androidx.annotation.Nullable;

/**
 * Custom ImageView for loading network image by Glide
 * <p>
 * Created by wenjingbian on 2018/2/28.
 */

public class GlideImageView extends ImageView {

    private static final int DEFAULT_PLACEHOLDER = R.drawable.vertical_default_big_poster;
    private static final int DEFAULT_ERROR = R.drawable.vertical_default_big_poster;
    private static final int DEFAULT_PLACEHOLDER_CIRCLE = R.drawable.default_avatar;
    private static final int DEFAULT_ERROR_CIRCLE = R.drawable.default_avatar;


    //placeholder image resource of current ImageView
    private Drawable mPlaceHolderRes;
    private Drawable mResDrawable;
    //Target used to request network image by Glide of current ImageView
    private Target mTarget;
    private GlideRequests mGlideRequests;

    private boolean isClearWhenDetached = true;
    private boolean isDetached;

    private Object mPreRes;

    public GlideImageView(Context context) {
        super(context);
    }

    public GlideImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public GlideImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        isDetached = true;
        if (isClearWhenDetached) {
            //when detached from window, all requests will be cancel related to this ImageView.
            if (mGlideRequests != null) {
                mGlideRequests.clear(this);
            }
            //release all
            mPlaceHolderRes = null;
            mTarget = null;
            mGlideRequests = null;
            mPreRes = null;
        }
        mResDrawable = null;
    }

    public boolean isDetachedFromWindow() {
        return isDetached;
    }

    /**
     * Set ImageView's resource
     *
     * @param url resource's url with any Object instance
     */
    public void setImageRes(Object url) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, null, null, false, false, true, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, null, null, false, false, true, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource
     * c
     *
     * @param url            resource's url with any Object instance
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     */
    public void setImageRes(Object url, boolean isCachedInMemo) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, null, null, false, false, isCachedInMemo, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, null, null, false, false, isCachedInMemo, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource
     *
     * @param url            resource's url with any Object instance
     * @param placeHolderRes custom placeHolder resource, you can set a Drawable parameter
     * @param errorRes       custom error resource, you can set a Drawable parameter
     */
    public void setImageRes(Object url, Drawable placeHolderRes, Drawable errorRes) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, false, false, true, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, placeHolderRes, errorRes, false, false, true, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource
     *
     * @param url            resource's url with any Object instance
     * @param placeHolderRes custom placeHolder resource, you can set a Drawable parameter
     * @param errorRes       custom error resource, you can set a Drawable parameter
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     */
    public void setImageRes(Object url, Drawable placeHolderRes, Drawable errorRes, boolean isCachedInMemo) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, false, false, isCachedInMemo, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, placeHolderRes, errorRes, false, false, isCachedInMemo, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource
     *
     * @param url            resource's url with any Object instance
     * @param placeHolderRes custom placeHolder resource, you can set a Drawable parameter
     * @param errorRes       custom error resource, you can set a Drawable parameter
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     * @param target         Custom ImageViewTarget to monitor image loading process, if you don't need it, please set null directly
     */
    public void setImageRes(Object url, Drawable placeHolderRes, Drawable errorRes, boolean isCachedInMemo, ImageViewTarget target) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, false, false, isCachedInMemo, getHeight(), getWidth(), target);
        } else {
            setImageRes(url, placeHolderRes, errorRes, false, false, isCachedInMemo, 0, 0, target);
        }
    }

    /**
     * Set ImageView's resource with circular picture
     *
     * @param url resource's url with any Object instance
     */
    public void setCircleImageRes(Object url) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, null, null, true, false, true, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, null, null, true, false, true, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource with circular picture
     *
     * @param url            resource's url
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     */
    public void setCircleImageRes(Object url, boolean isCachedInMemo) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, null, null, true, false, isCachedInMemo, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, null, null, true, false, isCachedInMemo, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource with circular picture
     *
     * @param url            resource's url with any Object instance
     * @param placeHolderRes custom placeHolder resource
     * @param errorRes       custom error resource
     */
    public void setCircleImageRes(Object url, Drawable placeHolderRes, Drawable errorRes) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, true, false, true, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, placeHolderRes, errorRes, true, false, true, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource with circular picture
     *
     * @param url            resource's url
     * @param placeHolderRes custom placeHolder resource, you can set a Drawable parameter
     * @param errorRes       custom error resource, you can set a Drawable parameter
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     */
    public void setCircleImageRes(Object url, Drawable placeHolderRes, Drawable errorRes, boolean isCachedInMemo) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, true, false, isCachedInMemo, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, placeHolderRes, errorRes, true, false, isCachedInMemo, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource with circular picture
     *
     * @param url            resource's url
     * @param placeHolderRes custom placeHolder resource, you can set a Drawable parameter
     * @param errorRes       custom error resource, you can set a Drawable parameter
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     * @param target         Custom ImageViewTarget to monitor image loading process, if you don't need it, please set null directly
     */
    public void setCircleImageRes(Object url, Drawable placeHolderRes, Drawable errorRes, boolean isCachedInMemo, ImageViewTarget target) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, true, false, isCachedInMemo, getHeight(), getWidth(), target);
        } else {
            setImageRes(url, placeHolderRes, errorRes, true, false, isCachedInMemo, 0, 0, target);
        }
    }

    //cxy

    /**
     * Set ImageView's resource with round cornet picture
     *
     * @param url resource's url with any Object instance
     */
    public void setRoundCornerImageRes(Object url) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, null, null, false, true, true, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, null, null, false, true, true, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource with round cornet picture
     *
     * @param url            resource's url
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     */
    public void setRoundCornerImageRes(Object url, boolean isCachedInMemo) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, null, null, false, true, isCachedInMemo, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, null, null, false, true, isCachedInMemo, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource with round cornet picture
     *
     * @param url            resource's url with any Object instance
     * @param placeHolderRes custom placeHolder resource
     * @param errorRes       custom error resource
     */
    public void setRoundCornerImageRes(Object url, Drawable placeHolderRes, Drawable errorRes) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, false, true, true, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, placeHolderRes, errorRes, false, true, true, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource with round cornet picture
     *
     * @param url            resource's url
     * @param placeHolderRes custom placeHolder resource, you can set a Drawable parameter
     * @param errorRes       custom error resource, you can set a Drawable parameter
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     */
    public void setRoundCornerImageRes(Object url, Drawable placeHolderRes, Drawable errorRes, boolean isCachedInMemo) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, false, true, isCachedInMemo, getHeight(), getWidth(), null);
        } else {
            setImageRes(url, placeHolderRes, errorRes, false, true, isCachedInMemo, 0, 0, null);
        }
    }

    /**
     * Set ImageView's resource with round cornet picture
     *
     * @param url            resource's url
     * @param placeHolderRes custom placeHolder resource, you can set a Drawable parameter
     * @param errorRes       custom error resource, you can set a Drawable parameter
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     * @param target         Custom ImageViewTarget to monitor image loading process, if you don't need it, please set null directly
     */
    public void setRoundCornerImageRes(Object url, Drawable placeHolderRes, Drawable errorRes, boolean isCachedInMemo, ImageViewTarget target) {
        if (getScaleType() == ScaleType.FIT_XY) {
            setImageRes(url, placeHolderRes, errorRes, false, true, isCachedInMemo, getHeight(), getWidth(), target);
        } else {
            setImageRes(url, placeHolderRes, errorRes, false, true, isCachedInMemo, 0, 0, target);
        }
    }
    //cxy

    /**
     * Reset image resource to default, just like placeholder resource
     * <p>
     * the method will pause all request you have been loaded for this view
     */
    public void resetImageRes() {
        LibDeprecatedLogger.d("resetImageRes");
        if (mTarget != null) {
            if (mGlideRequests == null) {
                mGlideRequests = GlideApp.with(getContext());
            }
            mGlideRequests.clear(mTarget);
        }
        setImageDrawable(mPlaceHolderRes);
        mPreRes = null;
    }

    /**
     * Cancel all request related to this ImageView and set null as its image resource.
     */
    public void clearImageMemo() {
        if (mGlideRequests == null) {
            mGlideRequests = GlideApp.with(getContext());
        }
        mGlideRequests.clear(this);
        setImageDrawable(null);
        mPreRes = null;
    }

    /**
     * only cancel request
     */
    public void clearImage() {
        if (mGlideRequests == null) {
            mGlideRequests = GlideApp.with(getContext());
        }
        mGlideRequests.clear(this);
        mPreRes = null;
    }

    /**
     * Set value to detect whether clear ImageView's resource when detached from window
     *
     * @param isClearWhenDetached true if clear resource, default value is true.
     */
    public void setClearWhenDetached(boolean isClearWhenDetached) {
        this.isClearWhenDetached = isClearWhenDetached;
    }

    /**
     * Set ImageView's resource
     *
     * @param obj            image resource with any Object instance
     * @param placeHolderRes custom placeHolder resource, you can set a Drawable parameter
     * @param errorRes       custom error resource, you can set a Drawable parameter
     * @param isCircle       if you want to set image with circular picture, set true, else set false. Default value is false.
     * @param roundCorner    if you want to set image with round corner picture, set true, else set false. Default value is false.
     * @param isCachedInMemo if you don't want to cache image in memory, set false, else set true. Default value is true.
     * @param height         height of image resource, only set it when scaleType is FIT_XY
     * @param width          width of image resource, only set it when scaleType is FIT_XY
     * @param target         Custom ImageViewTarget to monitor image loading process
     */
    private void setImageRes(final Object obj, Drawable placeHolderRes, Drawable errorRes, boolean isCircle, boolean roundCorner,
                             boolean isCachedInMemo, int height, int width, ImageViewTarget target) {
        LibDeprecatedLogger.d("Glide image view set!");
        //if url is unavailable, reset image resource to its default.
        if (obj == null || (obj instanceof String && StringUtil.isEmpty((String) obj))) {
            LibDeprecatedLogger.e("Failed to set image resource to certain ImageView, since url is unavailable.");
            resetImageRes();
            return;
        }

        //same image when mPre was local resource.
        if (mPreRes != null && !(mPreRes instanceof String) && mPreRes == obj) {
            LibDeprecatedLogger.d("same image when mPre was local resource");
            return;
        }

        //same image when mPre was remote resource with Url
        if (mPreRes != null && mPreRes instanceof String && !StringUtil.isEmpty((String) mPreRes)
                && obj instanceof String && ((String) mPreRes).equals((String) obj)) {
            if (target != null) {
                target.onResourceReady(mResDrawable == null ? getDrawable() : mResDrawable, null);
            }
            LibDeprecatedLogger.d("same image when mPre was remote resource with Url");
            return;
        }

        //Only for local .9 png
        if (obj instanceof Integer && setNinePatchDrawable((Integer) obj)) {
            LibDeprecatedLogger.d("Only for local .9 png");
            return;
        }

        //set placeholder image and error image
        if (isCircle) {
            if (placeHolderRes == null) {
                placeHolderRes = getResources().getDrawable(DEFAULT_PLACEHOLDER_CIRCLE);
            }
            if (errorRes == null) {
                errorRes = getResources().getDrawable(DEFAULT_ERROR_CIRCLE);
            }
        } else if (!roundCorner) {
            if (placeHolderRes == null) {
                placeHolderRes = getResources().getDrawable(DEFAULT_PLACEHOLDER);
            }
            if (errorRes == null) {
                errorRes = getResources().getDrawable(DEFAULT_ERROR);
            }
        }
        mPlaceHolderRes = placeHolderRes;
        try {
            RequestOptions requestOptions = new RequestOptions().priority(Priority.LOW);
            if (mPlaceHolderRes != null) {
                requestOptions.placeholder(placeHolderRes).error(errorRes);
            }

            if (isCircle) {
                requestOptions = requestOptions.circleCrop();
            }

            if (roundCorner) {
                requestOptions = requestOptions.transform(
                        new RoundedCornersTransformation(getResources().getDimensionPixelSize(R.dimen.child_round_img_radius), 0));
            }
            if (!isCachedInMemo) {
                requestOptions = requestOptions.skipMemoryCache(true);
            }
            //reset image's height and width value when scaleType was FIT_XY
            if (height > 0 || width > 0) {
                requestOptions = requestOptions.override(width, height);
            }
            if (mGlideRequests == null) {
                mGlideRequests = GlideApp.with(getContext());
            }

            mGlideRequests.clear(mTarget);
            GlideRequest glideRequest;

            glideRequest = mGlideRequests.load(obj).apply(requestOptions).listener(new RequestListener<Drawable>() {
                @Override
                public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                    LibDeprecatedLogger.d("[GlideImageView]Failed to load image by url, url is " + obj);
                    mResDrawable = null;
                    return false;
                }

                @Override
                public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                    LibDeprecatedLogger.d("[GlideImage]Successful load image resource."
                            + (resource == null ? "null" : resource.getBounds().toString()));
                    mResDrawable = resource;
                    mPreRes = obj;
                    return false;
                }
            });
            if (target == null) {
                mTarget = glideRequest.into(this);
            } else {
                mTarget = glideRequest.into(target);
            }

        } catch (Resources.NotFoundException e) {
            LibDeprecatedLogger.e("[GlideImageView] Resource cannot be found: " + e.getMessage());
        }
    }

    /**
     * Set .9 png resource
     *
     * @param drawableRes int value for local .9 png
     */
    private boolean setNinePatchDrawable(int drawableRes) {
        try {
            Drawable drawable = getResources().getDrawable(drawableRes);
            if (drawable != null && drawable instanceof NinePatchDrawable) {
                setImageDrawable(drawable);
                mPreRes = drawableRes;
                return true;
            }
        } catch (Resources.NotFoundException e) {
            LibDeprecatedLogger.e("[GlideImage] Failed to find drawable resource from int value: " + e.getMessage());
        }
        return false;
    }


    public void setCornerImageRes(String url, int radius, final int placeHolderRes, final int errorRes) {
        GlideApp.with(getContext())
                .load(url)
                .apply(bitmapTransform(new RoundedCornersTransformation(radius, 0,
                        RoundedCornersTransformation.CornerType.ALL)).placeholder(placeHolderRes).error(errorRes))
                .into(this);
    }

}
