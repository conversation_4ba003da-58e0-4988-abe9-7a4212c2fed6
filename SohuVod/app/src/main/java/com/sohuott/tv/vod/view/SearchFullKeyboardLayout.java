package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TableLayout;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

/**
 * Created by fenglei on 17-6-16.
 */

public class SearchFullKeyboardLayout extends TableLayout implements View.OnClickListener, View.OnFocusChangeListener, View.OnKeyListener {

    private OnClickFullKeyboardListener onClickFullKeyboardListener;
    private OnFocusDownListener onFocusDownListener;
    public MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
            if(onFocusDownListener != null) {
                onFocusDownListener.onFocusDown();
                return true;
            }
        }
        return false;
    }

//    private String mPageName = "6_search";

    public interface OnFocusDownListener {
        void onFocusDown();
    }
    public interface OnClickFullKeyboardListener {
        void onClickFullKeyboard(String content);
    }

    public SearchFullKeyboardLayout(Context context) {
        super(context);
        initUI(context);
    }

    public SearchFullKeyboardLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initUI(context);
    }

    public void requestFirstFocus(){
        findViewById(R.id.o).requestFocus();
    }

    private void initUI(Context context) {
        LayoutInflater.from(context).inflate(R.layout.search_full_keyboard_layout, this, true);
        for(int i = 0; i < getChildCount(); i++) {
            ViewGroup viewGroup = (ViewGroup) getChildAt(i);
            for(int j = 0; j < viewGroup.getChildCount(); j++) {
                if(viewGroup.getChildAt(j) instanceof Button) {
                    viewGroup.getChildAt(j).setOnClickListener(this);
                    if(!Util.isSupportTouchVersion(context)){
                        viewGroup.getChildAt(j).setOnFocusChangeListener(this);
                    }else{
                        viewGroup.getChildAt(j).setFocusableInTouchMode(false);
                    }
                }
            }
        }

        findViewById(R.id.button_5).setOnKeyListener(this);
        findViewById(R.id.button_6).setOnKeyListener(this);
        findViewById(R.id.button_7).setOnKeyListener(this);
        findViewById(R.id.button_8).setOnKeyListener(this);
        findViewById(R.id.button_9).setOnKeyListener(this);
        findViewById(R.id.button_0).setOnKeyListener(this);

        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false);
        }
    }

//    public void setPageName(String pageName){
//        this.mPageName = pageName;
//    }

    @Override
    public void onClick(View v) {
        if(onClickFullKeyboardListener != null) {
            onClickFullKeyboardListener.onClickFullKeyboard(((Button)v).getText().toString());
//            RequestManager.getInstance().onClickSearchFullkeyboardItem(mPageName,((Button)v).getText().toString());
        }
    }

    public void setOnClickFullKeyboardListener(OnClickFullKeyboardListener listener) {
        onClickFullKeyboardListener = listener;
    }

    public void setOnFocusDownListener(OnFocusDownListener listener) {
        onFocusDownListener = listener;
    }


    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
    }



    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (hasFocus() && event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            if(onClickFullKeyboardListener != null) {
                onClickFullKeyboardListener.onClickFullKeyboard("back");
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }
}
