package com.sohuott.tv.vod.view.scalemenu.presenter

import android.content.Context
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.getResDrawable
import com.sohuott.tv.vod.activity.base.scaleXY
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentSpeedMenuItem

/**
 * 倍速
 */
class ScaleContentMenuSpeedPresenter constructor(private val context: Context) :
    DefaultPresenter(R.layout.item_sacle_menu_speed_layout) {
    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as ScaleContentSpeedMenuItem
        val layout = viewHolder.getView<ConstraintLayout>(R.id.cl_scale_menu_speed)
        val tvContent = viewHolder.getView<TextView>(R.id.tv_scale_menu_speed)
        tvContent.text = item.content
        layout.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                tvContent.setTextColor(
                    ContextCompat.getColor(
                        context, R.color.tv_color_e8e8ff
                    )
                )
                layout.background = context.getResDrawable(R.drawable.bg_select_focus_e4705c_radius_11)
                v.scaleXY(1.1f)
            } else {
                if (item.hasCurrentSelected) {
                    tvContent.setTextColor(
                        ContextCompat.getColor(
                            context, R.color.tv_color_ff6247
                        )
                    )
                } else {
                    tvContent.setTextColor(
                        ContextCompat.getColor(
                            context, R.color.tv_color_e8e8ff
                        )
                    )
                }
                layout.background = context.getResDrawable(R.drawable.bg_radius_10_color_2effffff)
                v.scaleXY(1f)
            }
        }

        if (item.hasCurrentSelected) {
            tvContent.setTextColor(
                ContextCompat.getColor(
                    context, R.color.tv_color_ff6247
                )
            )
        } else {
            tvContent.setTextColor(
                ContextCompat.getColor(
                    context, R.color.tv_color_e8e8ff
                )
            )
        }

    }

}