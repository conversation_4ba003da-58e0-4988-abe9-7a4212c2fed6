package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.leanback.widget.Presenter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;


public class TypeRecommendContentPresenter extends Presenter {
    public Context mContext;
    public MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    private static final String TAG = "TypeRecommendContentPresenter";

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_recommend_layout, parent, false);
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppLogger.d("TypeOneContentPresenter:onBindViewHolder -> onclick");
            }
        });
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(view);
    }


    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, final Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        vh.view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (item instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean) {
                    mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
                }
                if (hasFocus) {
                    vh.mFocusPlay.setVisibility(View.VISIBLE);
//                    vh.name_bg.setVisibility(View.INVISIBLE);
                    vh.mFocusPlay.showWaveAnimation();
                } else {
                    vh.mFocusPlay.setVisibility(View.GONE);
                    vh.name_bg.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.cancelWaveAnimation();
                }
            }
        });
        if (item instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean){
            ContentGroup.DataBean.ContentsBean.AlbumListBean albumListBean = (ContentGroup.DataBean.ContentsBean.AlbumListBean) item;
            Glide.with(mContext)
                    .load(albumListBean.albumExtendsPic_640_360)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeTwoPoster);
            vh.mTvTypeRecommendEpisode.setText(Util.getHintTV(albumListBean));
            vh.mTvTypeRecommendName.setText(albumListBean.tvName);
            vh.mTvTypeRecommendDesc.setText(albumListBean.tvComment);

            vh.mIvTypeTwoPoster.setCornerTypeWithType(
                    albumListBean.tvIsFee,
                    albumListBean.tvIsEarly,
                    albumListBean.useTicket,
                    albumListBean.paySeparate,
                    albumListBean.cornerType);

            switch (albumListBean.cateCode) {
                case 100:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_100);
                    break;
                case 101:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_101);
                    break;
                case 106:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_106);
                    break;
                case 107:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_107);
                    break;
                case 115:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_115);
                    break;
                case 119:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_119);
                    break;
            }

            RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"),
                    albumListBean.pathInfo,
                    albumListBean.objectInfo,
                    albumListBean.memoInfo);

        } else if (item instanceof VideoDetailRecommend.DataEntity) {
            final VideoDetailRecommend.DataEntity dataEntity = (VideoDetailRecommend.DataEntity) item;
            Glide.with(mContext)
                    .load(dataEntity.albumExtendsPic_640_360)
                    .transform(new RoundedCorners(5))
                    .into(vh.mIvTypeTwoPoster);

            vh.mTvTypeRecommendEpisode.setText(Util.getHintTV(item));
            vh.mTvTypeRecommendName.setText(dataEntity.tvName);
            vh.mTvTypeRecommendDesc.setText(dataEntity.tvComment);

            vh.mIvTypeTwoPoster.setCornerTypeWithType(
                    dataEntity.tvIsFee,
                    dataEntity.tvIsEarly,
                    dataEntity.useTicket,
                    dataEntity.paySeparate,
                    dataEntity.cornerType);

            switch (dataEntity.cateCode) {
                case 100:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_100);
                    break;
                case 101:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_101);
                    break;
                case 106:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_106);
                    break;
                case 107:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_107);
                    break;
                case 115:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_115);
                    break;
                case 119:
                    vh.mIvRecommendCorner.setBackgroundResource(R.drawable.item_corner_119);
                    break;
            }

            vh.view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ActivityLauncher.startVideoDetailActivity(mContext, dataEntity.id, Constant.HOME_SOURCE);
                }
            });
        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final ImageView mIvRecommendCorner;
        private final RippleDiffuse mFocusPlay;
        private final CornerTagImageView mIvTypeTwoPoster;
        private final View name_bg;
        private final TextView mTvTypeRecommendName, mTvTypeRecommendDesc, mTvTypeRecommendEpisode;

        public ViewHolder(View view) {
            super(view);
            mIvTypeTwoPoster = (CornerTagImageView) view.findViewById(R.id.iv_type_two_poster);
            mIvRecommendCorner = (ImageView) view.findViewById(R.id.img_type_recommend_corner);
            mTvTypeRecommendName = (TextView) view.findViewById(R.id.tv_type_recommend_name);
            mTvTypeRecommendDesc = (TextView) view.findViewById(R.id.tv_type_recommend_desc);
            mTvTypeRecommendEpisode = (TextView) view.findViewById(R.id.tv_type_two_episode);
            mFocusPlay = (RippleDiffuse) view.findViewById(R.id.type_recommend_focus_play);
            name_bg = (View) view.findViewById(R.id.name_bg);
        }
    }
}
