package com.sohuott.tv.vod.activity.setting.play

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.DiffCallback
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.ListRow
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.recyclerview.widget.RecyclerView
import com.base_leanback.persenter.BaseListRowPresenter
import com.com.sohuott.tv.vod.base_component.NewBaseFragment
import com.lib_dlna_core.SohuDlnaManger
import com.lib_statistical.CLICK
import com.lib_statistical.addPushEvent
import com.lib_statistical.getInfoEvent
import com.lib_viewbind_ext.viewBinding
import com.sh.ott.video.player.PlayerConstants
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.databinding.FragmentPlaySettingLayoutBinding
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohu.lib_utils.PrefUtil
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.utils.ActivityLauncher
import com.sohuott.tv.vod.videodetail.activity.setPlayerChange
import com.sohuott.tv.vod.videodetail.activity.state.ResolutionApp

class PlaySettingFragment : NewBaseFragment(R.layout.fragment_play_setting_layout) {
    private val _binding by viewBinding(
        FragmentPlaySettingLayoutBinding::bind,
        onViewDestroyed = {
            mOldData.clear()
            mViewBinding = null
            mAdapter = null
            mArrayAdapter = null
            subDevicesAdapter = null
            subClarityAdapter = null
            subVideoSkipAdapter = null
            subVideoSizeAdapter = null
            subVideoSettingAdapter = null
            subVideoSpeedAdapter = null
            mLoginHelper = null
        })
    private var mViewBinding: FragmentPlaySettingLayoutBinding? = null
    private var mArrayAdapter: ArrayObjectAdapter? = null
    private var mAdapter: ItemBridgeAdapter? = null

    //设备名称
    private var subDevicesAdapter: ArrayObjectAdapter? = null
    private var subDevicesData = mutableListOf<Any>()

    //清晰度
    private var subClarityAdapter: ArrayObjectAdapter? = null
    private var subClarityData = mutableListOf<Any>()

    //跳过片头
    private var subVideoSkipAdapter: ArrayObjectAdapter? = null
    private var subVideoSkipData = mutableListOf<Any>()

    //画面大小
    private var subVideoSizeAdapter: ArrayObjectAdapter? = null
    private var subVideoSizeData = mutableListOf<Any>()

    //播放设置
    private var subVideoSettingAdapter: ArrayObjectAdapter? = null
    private var subVideoSettingData = mutableListOf<Any>()

    //倍速
    private var subVideoSpeedAdapter: ArrayObjectAdapter? = null
    private var subVideoSpeedData = mutableListOf<Any>()

    //H265
    private var subVideoH265Adapter: ArrayObjectAdapter? = null
    private var subVideoH265Data = mutableListOf<Any>()

    private var mOldData = mutableListOf<Any>()

    private var isPayBack = false

    private var isLoginBack = false;

    private var selectClarity = 0;

    private var mLoginHelper = LoginUserInformationHelper.getHelper(context?.applicationContext);


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewBinding = _binding
        initAdapter()
        setData()
        mViewBinding?.playLeanback?.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {

                AppLogger.i(
                    "PlaySettingFragment onChildViewHolderSelected position:$position view focus:${parent?.findFocus()}"
                )

                super.onChildViewHolderSelected(parent, child, position, subposition)
            }

            override fun onChildViewHolderSelectedAndPositioned(
                parent: RecyclerView,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                AppLogger.i(
                    "PlaySettingFragment onChildViewHolderSelectedAndPositioned position:$position view focus:${parent?.findFocus()}"
                )
                super.onChildViewHolderSelectedAndPositioned(parent, child, position, subposition)
            }
        })
    }

    private fun initAdapter() {
        mArrayAdapter = ArrayObjectAdapter(SettingItemSelector(requireContext()))
        mAdapter = ItemBridgeAdapter(mArrayAdapter)
        mViewBinding!!.playLeanback.adapter = mAdapter
        onClickItem()
    }

    override fun onResume() {
        super.onResume()
        if (isLoginBack || isPayBack) {
            when (selectClarity) {
                ResolutionApp.APP_SUPER.appValue
                -> {
                    if (mLoginHelper?.isLogin == true) {
                        PlaySettingHelper.setPlayClarity(
                            ResolutionApp.APP_SUPER.appValue
                        )
                        updateClarity()
                        PlaySettingHelper.setClarityHasChange(true)

                    }
                }

                ResolutionApp.APP_ORIGINAL_HDR.appValue
                -> {
                    if (getIsVip()) {
                        PlaySettingHelper.setPlayClarity(
                            ResolutionApp.APP_ORIGINAL_HDR.appValue
                        )
                        updateClarity()
                        PlaySettingHelper.setClarityHasChange(true)

                    }
                }


                ResolutionApp.MEDIA_BLUE.appValue
                -> {
                    if (getIsVip()) {
                        PlaySettingHelper.setPlayClarity(
                            ResolutionApp.MEDIA_BLUE.appValue
                        )
                        updateClarity()
                        PlaySettingHelper.setClarityHasChange(true)

                    }
                }

            }
        }

    }

    private fun getIsVip(): Boolean {
        return mLoginHelper.isVip() && System.currentTimeMillis() < java.lang.Long.valueOf(
            mLoginHelper.getVipTime()
        )
    }

    /**
     * 设置数据
     */
    private fun setData() {
        mOldData.clear()
//        initDevices()
//        initClarityData()
//        initVideoSizeData()
        initVideoSettingData()
        initVideoH265Data()
        initVideoSkipData()
        initVideoSpeedData()
        mArrayAdapter?.setItems(mOldData, object : DiffCallback<Any>() {
            override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
                return oldItem == newItem;
            }

            override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
                return true
            }
        })
    }

    /**
     * 设备名称
     */
    private fun initDevices() {
        mOldData.add(
            SettingItem(
                type = SETTING_TYPE_HEADER, groupName = "设备名称", id = "设备名称"
            )
        )
        if (subDevicesAdapter == null) {
            subDevicesAdapter = ArrayObjectAdapter(SettingItemPresenter(requireContext()))
        }
        updateDevice()
        mOldData.add(ListRow(subDevicesAdapter))
    }

    /**
     * 默认画质
     */
    private fun initClarityData() {
        val current = PlaySettingHelper.getPlayClarity()
        AppLogger.v("PlaySettingHelper.getPlayClarity${current}")
        if ((current == ResolutionApp.APP_ORIGINAL_HDR.appValue) || (current == ResolutionApp.MEDIA_BLUE.appValue)
        ) {
            if (!getIsVip()) {
                PlaySettingHelper.setPlayClarity(current)
            }
        }
        if ((current == ResolutionApp.APP_SUPER.appValue)
        ) {
            if (!mLoginHelper.isLogin) {
                PlaySettingHelper.setPlayClarity(current)
            }
        }
        mOldData.add(SettingItem(type = SETTING_TYPE_HEADER, groupName = "默认画质"))
        if (subClarityAdapter == null) {
            subClarityAdapter = ArrayObjectAdapter(SettingImgTipsItemPresenter(requireContext()))
        }
        updateClarity()
        mOldData.add(ListRow(subClarityAdapter))
    }

    /**
     * 片头片尾
     */
    private fun initVideoSkipData() {

        mOldData.add(SettingItem(type = SETTING_TYPE_HEADER, groupName = "片头片尾"))
        if (subVideoSkipAdapter == null) {
            subVideoSkipAdapter = ArrayObjectAdapter(SettingItemPresenter(requireContext()))
        }
        updateSkip()
        mOldData.add(ListRow(subVideoSkipAdapter))
        mOldData.add(
            SettingItem(
                type = SETTING_TYPE_TIPS,
                tips = "跳过片头片尾需要“播放器设置”成为”自研播放器”",
                focusable = false, id = "片头片尾"
            )
        )
    }

    /**
     * 画面尺寸
     */
    private fun initVideoSizeData() {

        mOldData.add(SettingItem(type = SETTING_TYPE_HEADER, groupName = "画面尺寸"))
        if (subVideoSizeAdapter == null) {
            subVideoSizeAdapter = ArrayObjectAdapter(SettingItemPresenter(requireContext()))
        }
        updateSize()
        mOldData.add(ListRow(subVideoSizeAdapter))

    }

    /**
     * 播放设置
     */
    private fun initVideoSettingData() {

        mOldData.add(SettingItem(type = SETTING_TYPE_HEADER, groupName = "播放设置"))
        if (subVideoSettingAdapter == null) {
            subVideoSettingAdapter = ArrayObjectAdapter(SettingItemPresenter(requireContext()))
        }
        updatePlaySetting()
        mOldData.add(ListRow(subVideoSettingAdapter))
    }


    /**
     * 倍速播放
     */
    private fun initVideoSpeedData() {

        mOldData.add(SettingItem(type = SETTING_TYPE_HEADER, groupName = "倍速播放"))
        if (subVideoSpeedAdapter == null) {
            subVideoSpeedAdapter = ArrayObjectAdapter(SettingItemPresenter(requireContext()))
        }
        updateSpeed()
        mOldData.add(ListRow(subVideoSpeedAdapter))
        mOldData.add(
            SettingItem(
                type = SETTING_TYPE_TIPS,
                tips = "倍速播放需要“播放器设置”成为“自研播放器”",
                focusable = false, id = "倍速播放"
            )
        )

    }

    private fun initVideoH265Data() {
        mOldData.add(SettingItem(type = SETTING_TYPE_HEADER, groupName = "编码设置"))
        if (subVideoH265Adapter == null) {
            subVideoH265Adapter = ArrayObjectAdapter(SettingItemPresenter(requireContext()))
        }
        updateH265()
        mOldData.add(ListRow(subVideoH265Adapter))
        mOldData.add(
            SettingItem(
                type = SETTING_TYPE_TIPS,
                tips = "H264设备兼容好。H265省带宽，减少卡顿。",
                focusable = false, id = "编码设置"
            )
        )
    }

    private fun updateH265() {
        subVideoH265Adapter?.apply {
            subVideoH265Data.clear()
            subVideoH265Data.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = !PlaySettingHelper.getH265HasChange(),
                    content = "默认设置",
                    id = "编码设置",
                    nextKewDown = Util.getPlayParams(context) == 0

                )
            )
            subVideoH265Data.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = PlaySettingHelper.getH265HasChange() && Util.getH265(context) == 0,
                    content = "H264(兼容好)",
                    id = "编码设置",
                    nextKewDown = Util.getPlayParams(context) == 0
                )
            )
            subVideoH265Data.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected =
                    PlaySettingHelper.getH265HasChange() && Util.getH265(context) == 1,
                    content = "H265(省带宽)",
                    id = "编码设置",
                    nextKewDown = Util.getPlayParams(context) == 0
                )
            )
            setItems(subVideoH265Data, object : DiffCallback<SettingItem>() {
                override fun areItemsTheSame(oldItem: SettingItem, newItem: SettingItem): Boolean {
                    return TextUtils.equals(
                        oldItem.content,
                        newItem.content
                    )
                }

                override fun areContentsTheSame(
                    oldItem: SettingItem,
                    newItem: SettingItem
                ): Boolean {
                    return oldItem.isSelected == newItem.isSelected && newItem.nextKewDown == oldItem.nextKewDown
                }

            })
        }
    }


    /**
     * 点击事件
     */
    private fun onClickItem() {

        mAdapter?.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
            override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder?) {
                super.onCreate(viewHolder)
                isLoginBack = false
                isPayBack = false
                AppLogger.v("setAdapterListener")
                when (viewHolder?.presenter) {
                    is BaseListRowPresenter -> {
                        val listRowPresenter = viewHolder.presenter as BaseListRowPresenter
                        listRowPresenter.setOnItemViewClickedListener { itemViewHolder, item, rowViewHolder, row ->
                            item as SettingItem
                            AppLogger.v("${item.id}")
                            when (item.id) {
                                "设备名称" -> {
                                    // 重新设置设备名
//                                    if (TextUtils.equals(item.content,"书房悦厅TV")){
//                                        SohuDlnaManger.getInstance().renameDevice("悦厅TV")
//                                    }else{
//                                        SohuDlnaManger.getInstance().renameDevice(item.content)
//                                    }
                                    SohuDlnaManger.getInstance().renameDevice(item.content)
                                    updateDevice()
                                    //options：1=客厅悦厅TV；2=书房悦厅TV；3=卧室悦厅TV
                                    addPushEvent(10302, CLICK, pathInfo = getInfoEvent {
                                        it["pageId"] = "1046"
                                    }, null, memoInfo = getInfoEvent {
//                                        it["options"] = option
                                    })
                                }

                                "片头片尾" -> {
                                    AppLogger.v("片头片尾")
                                    var option = 1
                                    when (item.content) {
                                        "跳过" -> {
                                            option = 1
                                            PlaySettingHelper.setNeedSkipHeaderAndEnd(true)
                                        }

                                        "不跳过" -> {
                                            option = 2
                                            PlaySettingHelper.setNeedSkipHeaderAndEnd(false)
                                        }

                                    }
                                    updateSkip()
                                    addPushEvent(10300, CLICK, pathInfo = getInfoEvent {
                                        it["pageId"] = "1046"
                                    }, null, memoInfo = getInfoEvent {
                                        it["跳过片头片尾"] = option
                                    })
                                }

                                "画面尺寸" -> {
                                    var option = 1
                                    AppLogger.v("画面尺寸")
                                    when (item.content) {

                                        "默认" -> {
                                            option = 1

                                            PlaySettingHelper.setVideoViewLayoutRatioType(
                                                PlayerConstants.ScreenAspectRatio.DEFAULT
                                            )
                                        }

                                        "满屏" -> {
                                            option = 2
                                            PlaySettingHelper.setVideoViewLayoutRatioType(
                                                PlayerConstants.ScreenAspectRatio.CENTER_CROP
                                            )
                                        }

                                        "拉伸" -> {
                                            option = 3
                                            PlaySettingHelper.setVideoViewLayoutRatioType(
                                                PlayerConstants.ScreenAspectRatio.MATCH_PARENT
                                            )
                                        }
                                    }
                                    updateSize()
                                    //options：1=默认；2=满屏；3=拉伸
                                    addPushEvent(10303, CLICK, pathInfo = getInfoEvent {
                                        it["pageId"] = "1046"
                                    }, null, memoInfo = getInfoEvent {
                                        it["options"] = option
                                    })
                                }

                                "播放设置" -> {
                                    var option = 1
                                    when (item.content) {
                                        "默认设置" -> {
                                            PlaySettingHelper.setPlayTypeHasChange(false)
                                            setPlayerChange(context!!,Util.getDefaultPlayParams(context))
                                        }

                                        "系统播放器" -> {
                                            option = 1
                                            PlaySettingHelper.setPlayTypeHasChange(true)
                                            setPlayerChange(context!!,1)
//                                            PlaySettingHelper.setPlayTypeHasChange(true)
//                                            PlaySettingHelper.setPlaySpeedIsOpen(false)
//                                            PlaySettingHelper.setNeedSkipHeaderAndEnd(false)
//                                            Util.setPlayParams(context, 1)
                                        }

                                        "自研播放器" -> {
                                            option = 2
                                            PlaySettingHelper.setPlayTypeHasChange(true)
                                            setPlayerChange(context!!,0)
//                                            PlaySettingHelper.setPlayTypeHasChange(true)
//                                            PlaySettingHelper.setPlaySpeedIsOpen(true)
//                                            PlaySettingHelper.setNeedSkipHeaderAndEnd(true)
//                                            Util.setPlayParams(context, 0)
                                        }
                                    }
                                    updatePlaySetting()
                                    updateSkip()
                                    updateSpeed()
                                    updateH265()
                                    addPushEvent(10304, CLICK, pathInfo = getInfoEvent {
                                        it["pageId"] = "1046"
                                    }, null, memoInfo = getInfoEvent {
                                        it["options"] = option
                                    })
                                }

                                "编码设置" -> {
                                    when (item.content) {
                                        "默认设置" -> {
                                            PlaySettingHelper.setH265HasChange(false)
                                            Util.setH265(context, Util.getDefaultH265(context))
                                        }

                                        "H264(兼容好)" -> {
                                            PlaySettingHelper.setH265HasChange(true)
                                            Util.setH265(context, 0)
                                        }

                                        "H265(省带宽)" -> {
                                            PlaySettingHelper.setH265HasChange(true)
                                            Util.setH265(context, 1)
                                        }
                                    }
                                    updateH265()
                                }

                                "倍速播放" -> {
                                    var option = 1
                                    when (item.content) {
                                        "开" -> {
                                            option = 1
                                            PlaySettingHelper.setPlaySpeedIsOpen(true)
                                        }

                                        "关" -> {
                                            option = 2
                                            PlaySettingHelper.setPlaySpeedIsOpen(false)

                                        }

                                    }
                                    updateSpeed()
                                    addPushEvent(10305, CLICK, pathInfo = getInfoEvent {
                                        it["pageId"] = "1046"
                                    }, null, memoInfo = getInfoEvent {
                                        it["options"] = option
                                    })
                                }

                                "默认画质" -> {
                                    var option = 1

                                    val is265 = Util.getH265(context) == 1
                                    when (item.content) {
                                        "炫彩HDR" -> {
                                            option = 1
                                            selectClarity = ResolutionApp.APP_ORIGINAL_HDR.appValue

                                            if (!getIsVip()) {
                                                isPayBack = true

                                                ActivityLauncher.startPayActivity(context)
                                                addPushClarityEvent(option)
                                                return@setOnItemViewClickedListener
                                            } else {
                                                PlaySettingHelper.setPlayAutoClarityIsOpen(false)

                                                PlaySettingHelper.setPlayClarity(selectClarity)
                                            }

                                        }

                                        "蓝光1080P" -> {
                                            option = 2
                                            selectClarity = ResolutionApp.MEDIA_BLUE.appValue

                                            if (!getIsVip()) {
                                                isPayBack = true
                                                ActivityLauncher.startPayActivity(context)
                                                addPushClarityEvent(option)
                                                return@setOnItemViewClickedListener
                                            } else {
                                                PlaySettingHelper.setPlayAutoClarityIsOpen(false)

                                                PlaySettingHelper.setPlayClarity(selectClarity)
                                            }

                                        }

                                        "超清" -> {
                                            option = 3
                                            selectClarity = ResolutionApp.APP_SUPER.appValue

                                            if (mLoginHelper?.isLogin == false) {
                                                isLoginBack = true
                                                ActivityLauncher.startLoginActivity(context)
                                                addPushClarityEvent(option)
                                                return@setOnItemViewClickedListener
                                            } else {
                                                PlaySettingHelper.setPlayAutoClarityIsOpen(false)
                                                PlaySettingHelper.setPlayClarity(selectClarity)
                                            }

                                        }

                                        "高清" -> {
                                            option = 4

                                            PlaySettingHelper.setPlayAutoClarityIsOpen(false)
                                            PlaySettingHelper.setPlayClarity(
                                                ResolutionApp.APP_HIGH.appValue
                                            )
                                        }

                                        "标清" -> {
                                            option = 5

                                            PlaySettingHelper.setPlayAutoClarityIsOpen(false)
                                            PlaySettingHelper.setPlayClarity(
                                                ResolutionApp.APP_NORMAL.appValue
                                            )
                                        }

                                        "自动清晰度" -> {
                                            PlaySettingHelper.setPlayAutoClarityIsOpen(true)
                                        }
                                    }
                                    updateClarity()
                                    addPushClarityEvent(option)
                                    PlaySettingHelper.setClarityHasChange(true)
                                }
                            }
//                            setData()
                        }
                    }
                }
            }

        })


    }


    private fun addPushClarityEvent(option: Int) {
        addPushEvent(10294, CLICK, pathInfo = getInfoEvent {
            it["pageId"] = "1046"
        }, null, memoInfo = getInfoEvent {
            if (!PlaySettingHelper.getPlayAutoClarityIsOpen()) {
                it["options"] = option
            }
            //：1=登录；0=未登录
            it["is_login"] = if (mLoginHelper.isLogin) 1 else 0
            it["is_vip"] =
                if (mLoginHelper.isVip && System.currentTimeMillis() < java.lang.Long.valueOf(
                        mLoginHelper.getVipTime()
                    )
                ) 1 else 0
            //是否自动：0=自动；1=手动
            it["is_automatic"] =
                if (PlaySettingHelper.getPlayAutoClarityIsOpen()) 0 else 1
        })
    }

    private fun updateSpeed() {
        subVideoSpeedAdapter?.apply {
            subVideoSpeedData.clear()
            subVideoSpeedData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = PlaySettingHelper.getPlaySpeedIsOpen(),
                    content = "开",
                    focusable = Util.getPlayParams(context) == 0, id = "倍速播放"
                )
            )
            subVideoSpeedData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected =
                    !PlaySettingHelper.getPlaySpeedIsOpen(),
                    content = "关",
                    focusable = Util.getPlayParams(context) == 0, id = "倍速播放"
                )
            )
            setItems(subVideoSpeedData, object : DiffCallback<SettingItem>() {
                override fun areItemsTheSame(oldItem: SettingItem, newItem: SettingItem): Boolean {
                    return TextUtils.equals(
                        oldItem.content,
                        newItem.content
                    )
                }

                override fun areContentsTheSame(
                    oldItem: SettingItem,
                    newItem: SettingItem
                ): Boolean {
                    return oldItem.isSelected == newItem.isSelected && oldItem.focusable == newItem.focusable
                }

            })
        }

    }


    private fun updatePlaySetting() {
        subVideoSettingAdapter?.apply {
            subVideoSettingData.clear()
            subVideoSettingData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = !PlaySettingHelper.getPlayTypeHasChange(),
                    content = "默认设置",
                    id = "播放设置",
//                    nextKewDown = Util.getPlayParams(context) == 0

                )
            )
            subVideoSettingData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = PlaySettingHelper.getPlayTypeHasChange() && Util.getPlayParams(
                        context
                    ) == 1,
                    content = "系统播放器",
                    id = "播放设置",
//                    nextKewDown = Util.getPlayParams(context) == 0

                )
            )
            subVideoSettingData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = PlaySettingHelper.getPlayTypeHasChange() && Util.getPlayParams(
                        context
                    ) == 0,
                    content = "自研播放器",
                    id = "播放设置",
//                    nextKewDown = Util.getPlayParams(context) == 0

                )
            )
            setItems(subVideoSettingData, object : DiffCallback<SettingItem>() {
                override fun areItemsTheSame(oldItem: SettingItem, newItem: SettingItem): Boolean {
                    return TextUtils.equals(
                        oldItem.content,
                        newItem.content
                    )
                }

                override fun areContentsTheSame(
                    oldItem: SettingItem,
                    newItem: SettingItem
                ): Boolean {
                    return TextUtils.equals(
                        oldItem.content,
                        newItem.content
                    ) && oldItem.isSelected == newItem.isSelected && newItem.nextKewDown == oldItem.nextKewDown
                }

            })
        }

    }

    private fun updateSkip() {
        subVideoSkipAdapter?.apply {
            subVideoSkipData.clear()
            subVideoSkipData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = PlaySettingHelper.getNeedSkipHeaderAndEnd(),
                    content = "跳过",
                    id = "片头片尾",
                    focusable = Util.getPlayParams(context) == 0
                )
            )
            subVideoSkipData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = !PlaySettingHelper.getNeedSkipHeaderAndEnd(),
                    content = "不跳过",
                    id = "片头片尾",
                    focusable = Util.getPlayParams(context) == 0
                )
            )

            setItems(subVideoSkipData, object : DiffCallback<SettingItem>() {
                override fun areItemsTheSame(oldItem: SettingItem, newItem: SettingItem): Boolean {
                    return TextUtils.equals(
                        oldItem.content,
                        newItem.content
                    )
                }

                override fun areContentsTheSame(
                    oldItem: SettingItem,
                    newItem: SettingItem
                ): Boolean {
                    return oldItem.isSelected == newItem.isSelected && oldItem.focusable == newItem.focusable
                }

            })
        }

    }

    private fun updateSize() {
        subVideoSizeAdapter?.apply {
            subVideoSizeData.clear()
            subVideoSizeData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.DEFAULT,
                    content = "默认",
                    id = "画面尺寸"
                )
            )
            subVideoSizeData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.CENTER_CROP,
                    content = "满屏",
                    id = "画面尺寸"
                )
            )
            subVideoSizeData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.MATCH_PARENT,
                    content = "拉伸",
                    id = "画面尺寸"
                )
            )
            setItems(subVideoSizeData, object : DiffCallback<SettingItem>() {
                override fun areItemsTheSame(oldItem: SettingItem, newItem: SettingItem): Boolean {
                    return TextUtils.equals(
                        oldItem.content,
                        newItem.content
                    )
                }

                override fun areContentsTheSame(
                    oldItem: SettingItem,
                    newItem: SettingItem
                ): Boolean {
                    return oldItem.isSelected == newItem.isSelected
                }

            })
        }

    }

    private fun updateDevice() {
        subDevicesAdapter?.apply {
            subDevicesData.clear()
            subDevicesData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = TextUtils.equals(
                        "客厅悦厅TV",
                        PrefUtil.getString(
                            "Device_current_name",
                            "客厅悦厅TV"
                        )
                    ),
                    content = "客厅悦厅TV",
                    id = "设备名称"
                )
            )
            subDevicesData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = TextUtils.equals(
                        "卧室悦厅TV",
                        PrefUtil.getString(
                            "Device_current_name",
                            "客厅悦厅TV"
                        )
                    ),
                    content = "卧室悦厅TV",
                    id = "设备名称"
                )
            )
            subDevicesData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM,
                    isSelected = TextUtils.equals(
                        "书房悦厅TV",
                        PrefUtil.getString(
                            "Device_current_name",
                            "客厅悦厅TV"
                        )
                    ),
                    content = "书房悦厅TV",
                    id = "设备名称"
                )
            )
            setItems(subDevicesData, object : DiffCallback<SettingItem>() {
                override fun areItemsTheSame(oldItem: SettingItem, newItem: SettingItem): Boolean {
                    return true
                }

                override fun areContentsTheSame(
                    oldItem: SettingItem,
                    newItem: SettingItem
                ): Boolean {
                    return oldItem.isSelected == newItem.isSelected && TextUtils.equals(
                        oldItem.content,
                        newItem.content
                    )
                }

            })

        }
    }

    private fun updateClarity() {

        val isAuto = PlaySettingHelper.getPlayAutoClarityIsOpen()
        var clarity = PlaySettingHelper.getPlayClarity()
        if (clarity == ResolutionApp.APP_ORIGINAL_HDR.appValue || clarity == ResolutionApp.MEDIA_BLUE.appValue) {
            if (!getIsVip()) {
                clarity = PlaySettingHelper.getDefaultClarity()
                PlaySettingHelper.setPlayClarity(clarity)
            }
        }
        if (clarity == ResolutionApp.APP_SUPER.appValue) {
            if (!mLoginHelper.isLogin) {
                clarity = PlaySettingHelper.getDefaultClarity()
                PlaySettingHelper.setPlayClarity(clarity)
            }
        }

        subClarityAdapter?.apply {
            subClarityData.clear()
            subClarityData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM_IMG_TIPS,
                    isSelected = (clarity == ResolutionApp.APP_ORIGINAL_HDR.appValue) && !isAuto,
                    content = "炫彩HDR",
                    isShowImgTips = true,
                    imgTipsResId = R.mipmap.ic_members_tips, id = "默认画质"
                )
            )
            subClarityData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM_IMG_TIPS,
                    isSelected = (clarity == ResolutionApp.MEDIA_BLUE.appValue) && !isAuto,
                    content = "蓝光1080P",
                    isShowImgTips = true,
                    imgTipsResId = R.mipmap.ic_members_tips, id = "默认画质"
                )
            )
            subClarityData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM_IMG_TIPS,
                    isSelected = (clarity == ResolutionApp.APP_SUPER.appValue) && !isAuto,
                    content = "超清",
                    isShowImgTips = !mLoginHelper.isLogin,
                    imgTipsResId = R.mipmap.ic_login_tips, id = "默认画质"
                )
            )
            subClarityData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM_IMG_TIPS,
                    isSelected = (clarity == ResolutionApp.APP_HIGH.appValue) && !isAuto,
                    content = "高清",
                    id = "默认画质"
                )
            )
            subClarityData.add(
                SettingItem(
                    type = SETTING_TYPE_ITEM_IMG_TIPS,
                    isSelected = (clarity == ResolutionApp.APP_NORMAL.appValue) && !isAuto,
                    content = "标清",
                    id = "默认画质"
                )
            )
            val mPartnerNo = Util.getPartnerNo(context)
            //针对长虹机型判断不初始化cornet库 所以需不展示清晰度设置
//            if (!TextUtils.equals("80151103", mPartnerNo)) {
//                subClarityData.add(
//                    SettingItem(
//                        type = SETTING_TYPE_ITEM_IMG_TIPS,
//                        isSelected = PlaySettingHelper.getPlayAutoClarityIsOpen(),
//                        content = "自动清晰度",
//                        id = "默认画质"
//                    )
//                )
//            }
            setItems(subClarityData, object : DiffCallback<SettingItem>() {
                override fun areItemsTheSame(oldItem: SettingItem, newItem: SettingItem): Boolean {
                    return true
                }

                override fun areContentsTheSame(
                    oldItem: SettingItem,
                    newItem: SettingItem
                ): Boolean {
                    return oldItem.isShowImgTips == newItem.isShowImgTips && oldItem.isSelected == newItem.isSelected && TextUtils.equals(
                        oldItem.content,
                        newItem.content
                    )
                }

            })
        }

    }

    override fun onDestroyView() {
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
    }


}