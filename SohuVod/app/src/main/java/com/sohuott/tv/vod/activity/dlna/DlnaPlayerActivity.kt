package com.sohuott.tv.vod.activity.dlna

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.lib_dlna_core.SohuDlnaManger
import com.lib_dlna_core.center.MediaControlBrocastFactory
import com.lib_dlna_core.center.MediaControlBrocastFactory.IMediaControlListener
import com.lib_statistical.IMP
import com.lib_statistical.addPushEvent
import com.lib_statistical.getInfoEvent
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.base_router.RouterPath
import com.sohuott.tv.vod.videodetail.activity.VideoActivity

@Route(path = RouterPath.BizPlay.DLNA_PLAY_ACTIVITY)
class DlnaPlayerActivity : VideoActivity() {


    private var mediaControlBrocastFactory: MediaControlBrocastFactory? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        SohuDlnaManger.getInstance().setIsDlna(true)
        super.onCreate(savedInstanceState)
        pushDlnaPlayEvent()
        initDlnaControlFactory()
    }

    override fun onResume() {
        SohuDlnaManger.getInstance().setIsDlna(true)
        super.onResume()
    }


    //dlna 播放控制监听
    private fun initDlnaControlFactory() {
        if (mediaControlBrocastFactory != null) return
        mediaControlBrocastFactory =
            MediaControlBrocastFactory(this)
        mediaControlBrocastFactory
            ?.register(object : IMediaControlListener {
                override fun onPlayCommand() {
                    startVideo()
//                    mScreenView.post(Runnable {
//                        if (PlayerManager.getInstance().isPlaying) {
//                            return@Runnable
//                        }
//                        AppLogger.v("onPlayCommand")
//                        mScreenView.postDelayed({
//                            PlayerManager.getInstance().play()
//                        }, 600)
//                    })
                }

                override fun onPauseCommand() {
                    pauseVideo()
//                    mScreenView.post {
//                        AppLogger.v("onPauseCommand")
//                        PlayerManager.getInstance().pause()
//                    }
                }

                override fun onStopCommand() {
//                    mScreenView.post {
//                        AppLogger.v("onStopCommand")
                    SohuDlnaManger.finishPlayingVideoView()
//                    }
                }

                override fun onSeekCommand(time: Int) {
//                    mScreenView.showController()
//                    mScreenView.hideDelayed()
                    seekTo(time.toLong())
//                    if (PlayerManager.getInstance().isPlaying) {
//                        PlayerManager.getInstance().seekTo(time)
//                    } else {
//                        mScreenView.postDelayed({ PlayerManager.getInstance().seekTo(time) }, 500)
//                    }
                }
            })
    }

    override fun onPause() {
        SohuDlnaManger.getInstance().setIsDlna(false)
        SohuDlnaManger.getInstance().setIsSohuVideo(false)
        super.onPause()
    }

    private fun pushDlnaPlayEvent() {
        val pageId = 1058

        addPushEvent(10135, IMP, pathInfo = getInfoEvent {
            it["pageId"] = pageId
        }, null, memoInfo = getInfoEvent {
            if (!mDlnaMode?.plat.isNullOrEmpty()) {
                it["is_sohu"] = mDlnaMode!!.plat
            }
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mediaControlBrocastFactory != null) {
            mediaControlBrocastFactory?.unregister()
            mediaControlBrocastFactory = null
        }
    }

}