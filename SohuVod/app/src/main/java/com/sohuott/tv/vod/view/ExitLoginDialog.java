package com.sohuott.tv.vod.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;

import java.util.HashMap;

/**
 * Created by yizhang210244 on 2017/5/12.
 */

public class ExitLoginDialog extends Dialog implements View.OnFocusChangeListener, View.OnClickListener {

    private static final String TAG = ExitLoginDialog.class.getSimpleName();

    private Context mContext;


    private Button btnPositive, btnNegative;
    private ExitListener mExitListener;
    HashMap<String, String> mPathInfo;

    /**
     * Called when a view has been clicked.
     *
     * @param v The view that was clicked.
     */
    @Override
    public void onClick(View v) {
        if (v == btnPositive) {
            RequestManager.getInstance().onAllEvent(new EventInfo(10186, "clk"), mPathInfo, null,
                    null);
            mExitListener.onExit(true);
            this.dismiss();
        } else if (v == btnNegative) {
            RequestManager.getInstance().onAllEvent(new EventInfo(10187, "clk"), mPathInfo, null,
                    null);
            mExitListener.onExit(false);
            this.dismiss();
        }
    }

    public interface ExitListener {
        void onExit(boolean isConfirm);
    }



    public ExitLoginDialog(Context context, ExitListener exitListener) {
        super(context, R.style.UpdateDialogNew);
        this.mContext = context;
        this.mExitListener = exitListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.dialog_exit_login);

        WindowManager.LayoutParams lp=this.getWindow().getAttributes();
        lp.dimAmount=0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        btnPositive = (Button) findViewById(R.id.btn_dialog_positive);
        btnNegative = (Button) findViewById(R.id.btn_dialog_negative);
        btnPositive.setOnFocusChangeListener(this);
        btnNegative.setOnFocusChangeListener(this);
        btnPositive.setOnClickListener(this);
        btnNegative.setOnClickListener(this);
        mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", "10027");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), mPathInfo, null,
                null);
    }




    /**
     * Called when the focus state of a view has changed.
     *
     * @param v        The view whose state has changed.
     * @param hasFocus The new focus state of v.
     */
    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (v != null && v instanceof Button) {
            Button button = (Button) v;
            if (hasFocus) {
                button.setTextColor(0xFFE8E8FF);
                button.setScaleX(1.1f);
                button.setScaleY(1.1f);
            } else {
                button.setTextColor(0xB3E8E8FF);
                button.setScaleX(1.0f);
                button.setScaleY(1.0f);
            }
        }

    }




}