package com.sohuott.tv.vod.activity.setting.privacy

import android.annotation.SuppressLint
import android.content.Context
import android.view.ViewGroup
import androidx.leanback.widget.HorizontalGridView
import androidx.leanback.widget.RowPresenter
import com.base_leanback.persenter.BaseListRowPresenter
import com.lib_statistical.addClickPathInfoEvent
import com.sohuott.tv.vod.activity.setting.DownloadUserInfoActivity
import com.sohuott.tv.vod.activity.setting.ShowPrivacyWebViewActivity
import com.sohuott.tv.vod.utils.PrivacySettingHelper
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingItem
import com.sohuott.tv.vod.utils.ActivityLauncher

/**
 *
 * @Description
 * @date 2022/3/23 17:47
 * <AUTHOR>
 * @Version 1.0
 */
class PrivacySwitchListRowPresenter(private val mContext: Context?) : BaseListRowPresenter() {

//    private var mContext: Context? = null

    override fun createRowViewHolder(parent: ViewGroup?): RowPresenter.ViewHolder {
        val viewHolder = super.createRowViewHolder(parent)

        return viewHolder
    }

    @SuppressLint("RestrictedApi")
    override fun initializeRowViewHolder(holder: RowPresenter.ViewHolder?) {
        super.initializeRowViewHolder(holder)
        holder as ViewHolder
        holder.gridView.focusScrollStrategy = HorizontalGridView.FOCUS_SCROLL_ITEM
//        mContext = holder.gridView.context
        setOnItemViewClickedListener { _, item, _, _ ->
            if (item is PrivacySettingItem) {

                when (item.id) {
                    "recommend_open" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10244) {
                                it["pageId"] = "1032"
                            }
                            PrivacySettingHelper.setRecommend(mContext, true)
                            holder.gridView.adapter?.notifyDataSetChanged()
                        }
                    }
                    "recommend_close" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10262) {
                                it["pageId"] = "1032"
                            }
                            holder.gridView.adapter?.notifyDataSetChanged()
                            PrivacySettingHelper.setRecommend(mContext, false)
                        }
                    }

                    "ad_open" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10245) {
                                it["pageId"] = "1032"
                            }
                            PrivacySettingHelper.setAd(mContext, true)
                            holder.gridView.adapter?.notifyDataSetChanged()

                        }
                    }

                    "ad_close" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10263) {
                                it["pageId"] = "1032"
                            }
                            PrivacySettingHelper.setAd(mContext, false)
                            holder.gridView.adapter?.notifyDataSetChanged()
                        }
                    }

                    "collect" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10246) {
                                it["pageId"] = "1032"
                            }
                            ShowPrivacyWebViewActivity.actionStart(
                                mContext!!,
                                ShowPrivacyWebViewActivity.COLLECT
                            )
                            ShowPrivacyWebViewActivity.actionStart(mContext,ShowPrivacyWebViewActivity.COLLECT)
                        }
                    }
                    "share" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10247) {
                                it["pageId"] = "1032"
                            }
                            ShowPrivacyWebViewActivity.actionStart(
                                mContext!!,
                                ShowPrivacyWebViewActivity.SHARE
                            )
                            ShowPrivacyWebViewActivity.actionStart(mContext,ShowPrivacyWebViewActivity.SHARE)
                        }
                    }
                    "permission" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10248) {
                                it["pageId"] = "1032"
                            }
                            ShowPrivacyWebViewActivity.actionStart(
                                mContext!!,
                                ShowPrivacyWebViewActivity.PERMISSION
                            )
                            ShowPrivacyWebViewActivity.actionStart(mContext,ShowPrivacyWebViewActivity.PERMISSION)
                        }
                    }

                    "download" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10249) {
                                it["pageId"] = "1032"
                            }
                            PrivacySettingHelper.userInfoContent = item.content
                            DownloadUserInfoActivity.actionStart(mContext)
                        }
                    }

                    "unsubscribe" -> {
                        if (mContext != null) {
                            addClickPathInfoEvent(10264) {
                                it["pageId"] = "1032"
                            }
                            ActivityLauncher.startAccountLogOffActivity(mContext)
                        }
                    }

                }


            }
        }
    }
}