package com.sohuott.tv.vod.search


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SearchAssociate(
    @SerialName("data")
    var `data`: Data = Data(),
    @SerialName("message")
    var message: String = "",
    @SerialName("status")
    var status: Int = 0
) {
    @Serializable
    data class Data(
        @SerialName("suggests")
        var suggests: List<Suggest> = listOf()
    ) {
        @Serializable
        data class Suggest(
            @SerialName("highlight")
            var highlight: String = "",
            @SerialName("keyword")
            var keyword: String = "",
            @SerialName("showType")
            var showType: Int = 0
        )
    }
}