package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatTextView;

/**
 * Created by rita on 17-6-7.
 */

public class ScrollingTextView extends AppCompatTextView {
    boolean mSelfFocus = false;
    public ScrollingTextView(Context context) {
        super(context);
    }

    public ScrollingTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ScrollingTextView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean isFocused() {
        return mSelfFocus;
    }

    public void setSelfFocus(boolean selfFocus) {
        mSelfFocus = selfFocus;
        onWindowFocusChanged(selfFocus);
    }
}
