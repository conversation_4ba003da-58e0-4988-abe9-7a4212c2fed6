package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.ListEduRecordAdapter;
import com.sohuott.tv.vod.adapter.MyEmptyViewAdapter;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;

import static com.sohuott.tv.vod.activity.ListEduUserRelatedActivity.LIST_INDEX_COLLECTION;
import static com.sohuott.tv.vod.activity.ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD;
import static com.sohuott.tv.vod.activity.ListEduUserRelatedActivity.LIST_INDEX_HISTORY;

public class EduEmptyView extends RelativeLayout implements MyEmptyViewAdapter.IEmptyViewFocus {

    public static final int TAG_LOGIN = 1;
    public static final int TAG_COMING_SOON = 2;

    private Context mContext;

    //Message text
    private TextView tv_my_empty_view;
    //Login button
    private Button btn_my_empty_view;

    private ListEduRecordAdapter.FocusController mEduFocusController;

    //Selected item id on the left list
    private int mParentTag;
    private int mSubjectId;

    public EduEmptyView(Context context) {
        super(context);
        this.mContext = context;
        initView();
        initListeners();
    }

    public EduEmptyView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        initView();
        initListeners();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mEduFocusController = null;
        mContext = null;
        FocusUtil.clearAnimation();
    }

    @Override
    public void onEmptyViewFocus() {
        if (btn_my_empty_view != null && btn_my_empty_view.getVisibility() == VISIBLE) {
            btn_my_empty_view.requestFocus();
        }
    }

    /**
     * Set selected item id on the left list
     *
     * @param parentTag tag value of the selected item
     */
    public void setParentTag(int parentTag) {
        LoginUserInformationHelper helper = LoginUserInformationHelper.getHelper(mContext);
        boolean isLogin = helper.getIsLogin();
        this.mParentTag = parentTag;

        //Set message text according to the selected item id
        switch (parentTag) {
            case LIST_INDEX_HISTORY:
                if (isLogin) {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_edu_history_empty_msg));
                } else {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_edu_history_empty_msg));
                }
                break;
            case LIST_INDEX_COLLECTION:
                if (isLogin) {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_edu_collection_empty_msg));
                } else {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_edu_collection_empty_msg));
                }
                break;
            case LIST_INDEX_CONSUME_RECORD:
                if (isLogin) {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_edu_consume_record_empty_msg));
                } else {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_edu_consume_record_nologin_msg));
                }
                break;
            default:
                break;
        }
    }

    /**
     * Set focus for MyEmptyView
     * <p>
     * If button is visible, button will request focus
     * Else the first item of promote list will request focus
     */
    public void focusAtPos() {
        if (btn_my_empty_view != null && btn_my_empty_view.getVisibility() == VISIBLE) {
            btn_my_empty_view.requestFocus();
        }
    }

    /**
     * Set button's visibility
     *
     * @param isVisible true means VISIBLE, false means GONE or INVISIBLE
     */
    public void setBtnVisibility(boolean isVisible) {
        if (btn_my_empty_view == null) {
            return;
        }

        RelativeLayout.LayoutParams layoutParams = (LayoutParams) tv_my_empty_view.getLayoutParams();
        if (isVisible) {
            btn_my_empty_view.setVisibility(VISIBLE);
            layoutParams.setMargins(0, 0, 0, getContext().getResources().getDimensionPixelOffset(R.dimen.y297));
        } else {
            btn_my_empty_view.setVisibility(GONE);
            layoutParams.setMargins(0, 0, 0, getContext().getResources().getDimensionPixelOffset(R.dimen.y204));
        }
        tv_my_empty_view.setLayoutParams(layoutParams);
    }

    public void setBtnText(String text) {
        if (!TextUtils.isEmpty(text)) {
            btn_my_empty_view.setText(text);
        }
    }

    public void setBtnListener(final int tag) {
        btn_my_empty_view.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (tag == TAG_LOGIN) {
                    ActivityLauncher.startLoginActivity(mContext);
                    //Action event
                    RequestManager.getInstance().onUserRelatedLoginBtnClickEvent(mParentTag);
                } else if (tag == TAG_COMING_SOON) {
                    ActivityLauncher.startCommingSoonActivity(mContext, mSubjectId);
                    RequestManager.getInstance().onBookedGoToComingSoonClickEvent();
                }
            }
        });
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
    }

    public void setFocusController(ListEduRecordAdapter.FocusController focusController) {
        this.mEduFocusController = focusController;
    }

    /**
     * Initialize view
     */
    private void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.layout_edu_empty_view, this, true);
        //init views
        tv_my_empty_view = (TextView) findViewById(R.id.tv_edu_empty_view);
        btn_my_empty_view = (Button) findViewById(R.id.btn_edu_empty_view);
    }

    /**
     * Initialize listeners on the child views
     */
    private void initListeners() {
        btn_my_empty_view.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_BACK) {
                        if (mEduFocusController != null) {
                            mEduFocusController.onFocusSelected(mParentTag);
                            return true;
                        }
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                        return true;
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                        return true;
                    }
                }
                return false;
            }
        });
        btn_my_empty_view.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    FocusUtil.setFocusAnimator(v, 1.07f, 300);
                } else {
                    FocusUtil.setUnFocusAnimator(v);
                }
            }
        });
    }

    /**
     * Set message text
     *
     * @param msgStr message text you want to set
     */
    private void setMsgTxt(String msgStr) {
        if (tv_my_empty_view == null || TextUtils.isEmpty(msgStr)) {
            return;
        }
        tv_my_empty_view.setText(msgStr);
    }

}
