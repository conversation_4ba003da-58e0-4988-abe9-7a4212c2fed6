package com.sohuott.tv.vod.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/**
 * @version V1.0.0
 * @FileName: com.sohuott.tv.vod.receiver.WakeReceiver.java
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: ${todo}
 * @date: 2018-03-09 10:59
 */


public class WakeReceiver extends BroadcastReceiver {

    public static final String ACTION_WAKE = "action_wake_guard";

    @Override
    public void onReceive(Context context, Intent intent) {
//        AppLogger.d("onReceive");
//        if(null!=intent&&ACTION_WAKE.equalsIgnoreCase(intent.getAction())){
//            if(Util.isServiceRunning(context, SystemDialogService.class.getName())&&
//                    SystemDialogService.isServiceRunning()){
//                AppLogger.d("service is running");
//            }
//            else{
//                AppLogger.d("service is died");
//                SystemDialogService.startSystemDialogService(context,false);
//            }
//        }
    }
}
