package com.sohuott.tv.vod.activity;

import android.content.Context;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.model.PointTaskInfo;
import com.sohuott.tv.vod.presenter.PointTaskPresenterImpl;
import com.sohu.lib_utils.FontUtils;
import com.sohuott.tv.vod.view.PointTaskView;

/**
 * Created by wenjingbian on 2018/1/4.
 */

public class PointTaskLayout extends FrameLayout implements PointTaskView {

    private LoadingView mLoadingView;
    private LinearLayout mErrorView, mPointTaskView;
    private ScrollView mScrollView;
    private TextView tv_task_title, tv_task_sum, tv_task_count, tv_task_desc;

    private PointTaskPresenterImpl mPresenterImpl;

    private String mPassport;
    private String mCurrCount;
    private String mCurrScore;
    private int mTaskId;
    private Context mContext;

    public PointTaskLayout(@NonNull Context context, String currCount, String currScore, int taskId, String passport) {
        super(context);
        mContext = context;
        this.mCurrCount = currCount;
        this.mCurrScore = currScore;
        this.mTaskId = taskId;
        this.mPassport = passport;
        LayoutInflater.from(context).inflate(R.layout.layout_point_task, this, true);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        initView();
        initData();
    }

    @Override
    public void displayView(PointTaskInfo.DataBean dataBean) {
        if (dataBean == null) {
            displayErrorView();
        } else {
            mLoadingView.setVisibility(View.GONE);
            mErrorView.setVisibility(View.GONE);
            mScrollView.setVisibility(View.VISIBLE);

            mPointTaskView.requestFocus();
            tv_task_title.setText(dataBean.getTaskName());
            FontUtils.setTypeface(mContext, tv_task_sum);
            FontUtils.setTypeface(mContext, tv_task_count);

            if (mCurrScore != null && mCurrScore.length() > 0) {
                SpannableString sStr = new SpannableString(mCurrScore);
                sStr.setSpan(new AbsoluteSizeSpan(getResources().getDimensionPixelSize(R.dimen.x30)), mCurrScore.length() -1, mCurrScore.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                tv_task_sum.setText(sStr);

            }
            if (mCurrCount != null && mCurrCount.length() > 0) {
                SpannableString sStr = new SpannableString(mCurrCount);
                if (mCurrCount.contains("小时")) {
                    sStr.setSpan(new AbsoluteSizeSpan(getResources().getDimensionPixelSize(R.dimen.x30)), mCurrCount.length() -2, mCurrCount.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                } else {
                    sStr.setSpan(new AbsoluteSizeSpan(getResources().getDimensionPixelSize(R.dimen.x30)), mCurrCount.length() - 1, mCurrCount.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                tv_task_count.setText(sStr);

            }
            tv_task_desc.setText(dataBean.getTaskDesc());
        }
    }

    @Override
    public void displayErrorView() {
        mLoadingView.setVisibility(View.GONE);
        mScrollView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.VISIBLE);
    }

    private void initView() {
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mErrorView = (LinearLayout) findViewById(R.id.err_view);
        mScrollView = (ScrollView) findViewById(R.id.layout_point_task_scroll);
        mPointTaskView = (LinearLayout) findViewById(R.id.layout_point_task);

        tv_task_title = (TextView) findViewById(R.id.tv_task_title);
        tv_task_sum = (TextView) findViewById(R.id.tv_task_sum);
        tv_task_count = (TextView) findViewById(R.id.tv_task_count);
        tv_task_desc = (TextView) findViewById(R.id.tv_task_desc);
    }

    private void initData() {
        mPresenterImpl = new PointTaskPresenterImpl();
        mPresenterImpl.setView(this);
        mPresenterImpl.requestTaskData(mTaskId, mPassport);
    }
}
