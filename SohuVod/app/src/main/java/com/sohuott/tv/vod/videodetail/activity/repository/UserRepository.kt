package com.sohuott.tv.vod.videodetail.activity.repository

import GsonConverter
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.account.payment.EducationPrivilege
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.model.PermissionCheck
import com.sohuott.tv.vod.lib.model.TopInfo
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.coroutineScope
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class UserRepository {

    suspend fun fetchTopBarData(passport: String?, token: String?): Deferred<TopInfo> {
        return coroutineScope {
            Get<TopInfo>("${RetrofitApi.get().retrofitHost.baseHost}vip/headerInformation.json?company=snm") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())

                setQuery("passport", passport)
                setQuery("token", token)
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                            listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                            )
                    )
                }
                converter = GsonConverter()
            }
        }
    }

    suspend fun userVideoPermission(aid: Int, vid: Int): Deferred<PermissionCheck> {
        return coroutineScope {
            Get<PermissionCheck>("${RetrofitApi.get().retrofitHost.userHost}api/security/film/checkpermission.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader(HeaderHelper.getCurlOpenKey(),"true")

                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                            listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                            )
                    )
                }
                converter = GsonConverter()
                param("passport", UserLoginHelper.getInstants().getLoginPassport())
                param("auth_token", UserLoginHelper.getInstants().getLoginToken())
                param("aid", aid)
                param("vid", vid)
            }
        }
    }


    suspend fun loadEducationPrivilegeInfo(aid: Int, vid: Int): Deferred<EducationPrivilege> {
        return coroutineScope {
            Get<EducationPrivilege>("${RetrofitApi.get().retrofitHost.userHost}/api/v1/pay/education/privilege.json?") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader(HeaderHelper.getCurlOpenKey(),"true")

                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                            listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                            )
                    )
                }
                converter = GsonConverter()
                setQuery("passport", UserLoginHelper.getInstants().getLoginPassport())
                setQuery("token", UserLoginHelper.getInstants().getLoginToken())
                setQuery("aid", aid)
                setQuery("vid", vid)
            }
        }
    }

}
