package com.sohuott.tv.vod.view;

import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.lib.model.FilterBean;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.MenuListBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by XiyingCao on 16-1-7.
 */
public interface GridListView {
    void add(List<ListAlbumModel> models);

    void setHeader(ArrayList<AllLabel.LabelItem> result);

    void setSlidingMenu(List<MenuListBean.MenuDate> list);

    void setFilter(ArrayList<FilterBean.DataEntity> result);

    void showLoading();

    void hideLoading();

    void activateLastItemViewListener();

    void disableLastItemViewListener();

    void onError(boolean isFilter);

    void onHeaderError();

    void onSlidingMenuError();

    RecyclerView.Adapter getAdapter();

    void setCount(int count);

    void updateCountText(int mSelctedPos);
}
