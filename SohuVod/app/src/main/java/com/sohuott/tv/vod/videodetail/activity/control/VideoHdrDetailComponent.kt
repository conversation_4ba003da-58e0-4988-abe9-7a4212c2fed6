package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.view.KeyEvent
import android.widget.TextView
import com.lib_dlna_core.SohuDlnaManger.Companion.getInstance
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.PayActivity
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper.getPlayAutoClarityIsOpen
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper.setPlayAutoClarityIsOpen
import com.sohuott.tv.vod.app.config.ResolutionInfo
import com.sohuott.tv.vod.utils.ActivityLauncher
import com.sohuott.tv.vod.utils.ParamConstant
import com.sohuott.tv.vod.videodetail.activity.control.VideoPlayMenuConfigComponent.PlayMenuConfigListener
import com.sohuott.tv.vod.videodetail.activity.state.ResolutionApp
import com.sohuott.tv.vod.videodetail.activity.state.VideoPlayResolutionInfo

/**
 * 解释hdr
 */
class VideoHdrDetailComponent constructor(context: Context) :
    BaseControlComponent(context) {
    private var goPay: TextView? = null
    private var mPlayMenuConfigListener: PlayMenuConfigListener? = null

    private var aid = 0
    private var vid = 0


    init {
        gone()
        layoutInflater.inflate(R.layout.layout_hdr_detail_view, this, true)
        goPay = findViewById(R.id.tv_hdr_go_pay)
    }


    fun show(aid: Int = 0, vid: Int = 0) {
        this.aid = aid
        this.vid = vid
        visible()
        goPay?.requestFocus()
    }

    fun hide() {
        gone()
    }


    private var resolutionInfo: ResolutionInfo? = null

    fun setCurrentClarity(info: ResolutionInfo?) {
        resolutionInfo = info
    }


    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.action == KeyEvent.ACTION_DOWN) {
            when (event?.keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    if (visibility == VISIBLE) {
                        gone()
                        return true
                    }
                }

                KeyEvent.KEYCODE_ENTER,
                KeyEvent.KEYCODE_DPAD_CENTER -> {
                    gone()
                    val map = HashMap<String, String>(1)
                    if (getInstance().getIsDlna()) {
                        map["pageId"] = "1061"
                    } else {
                        map["pageId"] = "1047"
                    }
                    RequestManager.getInstance()
                        .onAllEvent(EventInfo(10296, "clk"), map, null, null)
                    if (getPlayAutoClarityIsOpen()) {
                        setPlayAutoClarityIsOpen(false)
                    }
                    if (getInstance().getIsDlna()) {
                        return true
                    }
                    if (!UserLoginHelper.getInstants().isVip()) {
                        ActivityLauncher.startPayActivityWithAidVid(context, aid, vid, ParamConstant.PAGE_SOURCE_PLAYER,
                            PayActivity.PAY_SOURCE_DETAIL,false)
                        return true
                    }
                    if (resolutionInfo == null) return true
                    mPlayMenuConfigListener?.onClickResolution(
                        resolutionInfo!!
                    )
                }

            }
        }
        return super.dispatchKeyEvent(event)
    }

    fun setPlayMenuConfigListener(listener: PlayMenuConfigListener) {
        mPlayMenuConfigListener = listener
    }

}