package com.sohuott.tv.vod.view;

import com.sohuott.tv.vod.lib.db.greendao.Collection;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.model.BookedRecord;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.model.VideoFavorListBean;

import java.util.List;

/**
 * Created by wenjingbian on 2017/4/1.
 */

public interface HfcRecordView {

    /**
     * Update RecordRecyclerView with history records
     */
    void updateHistoryRecordView(List<PlayHistory> historyList, boolean onlyVRS);

    void onAddMoreHistoryRecord(List<PlayHistory> historyList, boolean onlyVRS);

    /**
     * Update RecordRecyclerView with collection records
     */
    void updateCollectionRecordView(List<Collection> collectionList);

    void onAddMoreCollectionRecord(List<Collection> collectionList);

    /**
     * Update RecordRecyclerView with favor records
     *
     * @param favorList request data list
     */
    void updateFavorRecordView(List<VideoFavorListBean.DataEntity.ResultEntity> favorList);

    void updateBookedRecordView(List<BookedRecord.DataBean> dataList);

    /**
     * Display certain error view by the selected item id
     *
     * @param tag tag value of the selected item view in left list
     */
    void showChildErrorView(int tag);

    /**
     * Update PromoteRecyclerView in HistoryFavorCollectionActivity
     *
     * @param trackEntry            string value to identify entry when request personal recommend data
     * @param personalRecommendList request data list
     */
    void updatePersonalRecommendView(String trackEntry, List<VideoDetailRecommend.DataEntity> personalRecommendList);

    /**
     * Update item view of the appointed history record
     *
     * @param playHistory the latest PlayHistory
     */
    void updateHistoryItemView(PlayHistory playHistory);

    /**
     * Update history record view when deleted the appointed item successfully
     *
     * @param id the deleted history record's i
     */
    void updateHistoryRecordView(int id);

    /**
     * Update history record view when failed to delete the appointed item
     *
     * @param id the deleted history record's id
     */
    void updateHistoryRecordFailedView(int id);

    /**
     * Update collection record view when canceled the appointed item successfully
     *
     * @param id the canceled collection record's id
     */
    void updateCollectionRecordView(int id);

    /**
     * Update collection record view when failed to cancel the appointed item
     *
     * @param id the canceled collection record's id
     */
    void updateCollectionRecordFailedView(int id);

    void updateBookedRecordView(String id);

    void updateBookedRecordFailedView(String id);

    void setSubjectIdInfo(String subjectId);

    void showParentLoadingView();

    void hideParentLoadingView();

    void showEmptyView();
}
