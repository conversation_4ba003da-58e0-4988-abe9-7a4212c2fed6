package com.sohuott.tv.vod.widget.lb;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.Log;
import android.view.FocusFinder;
import android.view.KeyEvent;
import android.view.SoundEffectConstants;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.lib.utils.ToastUtils;


public class TabViewPager extends ViewPager {
    public static final String TAG = "TabViewPager";
    private Animation mShakeX;
    private final Rect mTempRect = new Rect();
    private Context mContext;

    private long clickTime = 0; // 第一次点击的时间

    public TabViewPager(@NonNull Context context) {
        super(context);
        this.mContext = context;
    }

    public TabViewPager(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return super.dispatchKeyEvent(event) || executeKeyEvent(event);
    }

    public boolean executeKeyEvent(@NonNull KeyEvent event) {
        boolean handled = false;
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            switch (event.getKeyCode()) {
                case KeyEvent.KEYCODE_DPAD_LEFT:
                    handled = arrowScroll(FOCUS_LEFT);
                    break;
                case KeyEvent.KEYCODE_DPAD_RIGHT:
                    handled = arrowScroll(FOCUS_RIGHT);
                    break;
            }
        }
        return handled;
    }

    public boolean arrowScroll(int direction) {
        Log.e(TAG, "arrowScroll direction: " + direction);
        boolean handled = false;
        View currentFocused = findFocus();
        if (currentFocused == this) {
            currentFocused = null;
        } else if (currentFocused != null) {
            boolean isChild = false;
            for (ViewParent parent = currentFocused.getParent(); parent instanceof ViewGroup;
                 parent = parent.getParent()) {
                if (parent == this) {
                    isChild = true;
                    break;
                }
            }
            if (!isChild) {
                // This would cause the focus search down below to fail in fun ways.
                final StringBuilder sb = new StringBuilder();
                sb.append(currentFocused.getClass().getSimpleName());
                for (ViewParent parent = currentFocused.getParent(); parent instanceof ViewGroup;
                     parent = parent.getParent()) {
                    sb.append(" => ").append(parent.getClass().getSimpleName());
                }
                currentFocused = null;
            }
        }


        View nextFocused = FocusFinder.getInstance().findNextFocus(this, currentFocused,
                direction);
        if (nextFocused != null && nextFocused != currentFocused) {
            if (direction == FOCUS_LEFT) {
                // If there is nothing to the left, or this is causing us to
                // jump to the right, then what we really want to do is page left.
                final int nextLeft = getChildRectInPagerCoordinates(mTempRect, nextFocused).left;
                final int currLeft = getChildRectInPagerCoordinates(mTempRect, currentFocused).left;
                if (currentFocused != null && nextLeft >= currLeft) {
                    handled = pageLeft();
                } else {
                    handled = nextFocused.requestFocus();
                }
            } else if (direction == FOCUS_RIGHT) {
                // If there is nothing to the right, or this is causing us to
                // jump to the left, then what we really want to do is page right.
                final int nextLeft = getChildRectInPagerCoordinates(mTempRect, nextFocused).left;
                final int currLeft = getChildRectInPagerCoordinates(mTempRect, currentFocused).left;
                if (currentFocused != null && nextLeft <= currLeft) {
                    handled = pageRight();
                } else {
                    handled = nextFocused.requestFocus();
                }
            }
        } else if (direction == FOCUS_LEFT) {
            // Trying to move left and nothing there; try to page.
            if (getCurrentItem() == 0) {
                shakeX(currentFocused);
                handled = true;
            } else {
                if (System.currentTimeMillis() - clickTime > 1000) {
                    clickTime = System.currentTimeMillis();
                    ToastUtils.showToast(getContext(), "连续点击跳转到下一个页面");
                    shakeX(currentFocused);
                    handled = true;
                } else {
                    handled = pageLeft();
                }
            }
        } else if (direction == FOCUS_RIGHT) {
            // Trying to move right and nothing there; try to page.
            if (getAdapter() != null && getCurrentItem() == getAdapter().getCount() - 1) {
                shakeX(currentFocused);
                handled = true;
            } else {
                if (System.currentTimeMillis() - clickTime > 1000) {
                    clickTime = System.currentTimeMillis();
                    ToastUtils.showToast(getContext(), "连续点击跳转到下一个页面");
                    shakeX(currentFocused);
                    handled = true;
                } else {
                    handled = pageRight();
                }
            }
        }
        if (handled) {
            playSoundEffect(SoundEffectConstants.getContantForFocusDirection(direction));
        }
        return handled;
    }

    private Rect getChildRectInPagerCoordinates(Rect outRect, View child) {
        if (outRect == null) {
            outRect = new Rect();
        }
        if (child == null) {
            outRect.set(0, 0, 0, 0);
            return outRect;
        }
        outRect.left = child.getLeft();
        outRect.right = child.getRight();
        outRect.top = child.getTop();
        outRect.bottom = child.getBottom();

        ViewParent parent = child.getParent();
        while (parent instanceof ViewGroup && parent != this) {
            final ViewGroup group = (ViewGroup) parent;
            outRect.left += group.getLeft();
            outRect.right += group.getRight();
            outRect.top += group.getTop();
            outRect.bottom += group.getBottom();

            parent = group.getParent();
        }
        return outRect;
    }

    boolean pageLeft() {
        if (getCurrentItem() > 0) {
            if (mContext instanceof LauncherActivity) {
                ((LauncherActivity) mContext).backToPosition(getCurrentItem() - 1);
            }
//            setCurrentItem(getCurrentItem() - 1, true);
            return true;
        }
        return false;
    }

    boolean pageRight() {
        if (getAdapter() != null && getCurrentItem() < (getAdapter().getCount() - 1)) {
            if (mContext instanceof LauncherActivity) {
                ((LauncherActivity) mContext).backToPosition(getCurrentItem() + 1);
            }
//            setCurrentItem(getCurrentItem() + 1, true);
            return true;
        }
        return false;
    }

    private void shakeX(View currentFocused) {
        if (currentFocused != null) {
            if (mShakeX == null) {
                mShakeX = AnimationUtils.loadAnimation(getContext(), R.anim.shake_x);
            }
            currentFocused.clearAnimation();
            currentFocused.startAnimation(mShakeX);
        }
    }
}
