package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.graphics.Rect;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.lib.db.greendao.SearchHistory;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.AuditDenyAids;
import com.sohuott.tv.vod.lib.model.HotSearchNew;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.SearchUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.HotSearchLayout;
import com.sohuott.tv.vod.view.SearchInputRecyclerView;
import com.sohu.lib_utils.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by fenglei on 17-6-16.
 */

public class SearchNoInputAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private String mPageName = "6_search";

    public void setPageName(String pageName){
        this.mPageName = pageName;
    }

    public static class SearchNoInputItemDecoration extends RecyclerView.ItemDecoration {
        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            int position = ((RecyclerView.LayoutParams) view.getLayoutParams()).getViewLayoutPosition();
            int viewType = parent.getAdapter().getItemViewType(position);
            if (viewType == SearchNoInputAdapter.VIEW_TYPE_HOT_SEARCH_TEXT_TITLE) {
                if (position == 0) {
                    outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y82);
                } else {
                    outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y50);
                }
            } else if (viewType == SearchNoInputAdapter.VIEW_TYPE_HOT_SEARCH_WITH_PIC) {
                outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y20);
            } else if (viewType == SearchNoInputAdapter.VIEW_TYPE_HOT_SEARCH_NO_PIC) {
                if (position == 2) {
                    outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y50);
                } else {
                    outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y20);
                }
            } else if (viewType == SearchNoInputAdapter.VIEW_TYPE_SEARCH_HISTORY) {
                outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y20);
            } else if(viewType == SearchNoInputAdapter.VIEW_TYPE_SEARCH_NO_RESULT) {
                outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y105);
            }
            if (position == parent.getAdapter().getItemCount() - 1) {
                outRect.bottom = (int) view.getContext().getResources().getDimension(R.dimen.y20);
            }
        }
    }

    public static final int VIEW_TYPE_HOT_SEARCH_TEXT_TITLE = 0;
    public static final int VIEW_TYPE_HOT_SEARCH_WITH_PIC = 1;
    public static final int VIEW_TYPE_HOT_SEARCH_NO_PIC = 2;
    public static final int VIEW_TYPE_SEARCH_HISTORY = 3;
    public static final int VIEW_TYPE_SEARCH_NO_RESULT = 4;

    class TitleViewHolder extends RecyclerView.ViewHolder {
        public TextView titleTV;
        public TextView clearHistory;

        TitleViewHolder(View view) {
            super(view);
            titleTV = (TextView) view.findViewById(R.id.title_of_layout);
            clearHistory = view.findViewById(R.id.clear_history);
            clearHistory.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (mClearHistoryListener != null) {
                        mClearHistoryListener.onClearHistory();
                        HashMap<String, String> path = new HashMap<>(1);
                        path.put("pageId", "1040");
                        RequestManager.getInstance().onAllEvent(new EventInfo(10251, "clk"), path, null, null);
                    }
                }
            });
        }

        public void bind(int position) {
            if(searchNoResult) {
                titleTV.setText("热门搜索");
                clearHistory.setVisibility(View.GONE);
                titleTV.setFocusable(false);
            } else if (hotSearchList == null || hotSearchList.size() == 0) {
                titleTV.setText("搜索历史");
                clearHistory.setVisibility(View.VISIBLE);
                titleTV.setFocusable(true);
                titleTV.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View view, boolean b) {
                        if (b) {
                            clearHistory.requestFocus();
                        }
                    }
                });
            } else {
                if (position == 0) {
                    titleTV.setText("热门搜索");
                    clearHistory.setVisibility(View.GONE);
                    titleTV.setFocusable(false);
                } else {
                    titleTV.setText("搜索历史");
                    clearHistory.setVisibility(View.VISIBLE);
                    titleTV.setFocusable(true);
                    titleTV.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                        @Override
                        public void onFocusChange(View view, boolean b) {
                            if (b) {
                                clearHistory.requestFocus();
                            }
                        }
                    });
                }
            }
        }
    }

    class HotSearchWithPicViewHolder extends RecyclerView.ViewHolder {
        public HotSearchLayout hotSearchLayout;

        HotSearchWithPicViewHolder(View view) {
            super(view);
            hotSearchLayout = (HotSearchLayout) view;
            hotSearchLayout.setType(HotSearchLayout.HOT);
            hotSearchLayout.setPageName(mPageName);
        }

        public void bind() {
            hotSearchLayout.setSearchUI(hotSearchList, calcuAlbumCount());
        }
    }

    //少儿页面override用，改变corner大小
    public void setCustomFocusBordonFocusChange(View v){
        focusBorderView.setFocusView(v);
    }

    protected void onClickHotSearchNoPicViewHolder(Context context,String textStr){
        ActivityLauncher.startSearchResultActivity(context, textStr);
    }

    class HotSearchNoPicViewHolder extends RecyclerView.ViewHolder
            implements View.OnClickListener, View.OnFocusChangeListener, View.OnKeyListener {

        public LinearLayout hotSearchNoPicLayout;

        public List<ViewGroup> viewGroupList = new ArrayList<>();
        public List<TextView> numTVList = new ArrayList<>();
        public List<TextView> titleTVList = new ArrayList<>();

        HotSearchNoPicViewHolder(View view) {
            super(view);
            hotSearchNoPicLayout = (LinearLayout) view;
            for (int i = 0; i < hotSearchNoPicLayout.getChildCount(); i++) {
                ViewGroup viewGroup = (ViewGroup) hotSearchNoPicLayout.getChildAt(i);
                numTVList.add((TextView) viewGroup.findViewById(R.id.numTV));
                titleTVList.add((TextView) viewGroup.findViewById(R.id.titleTV));
                viewGroupList.add(viewGroup);
                viewGroup.setOnClickListener(this);
                viewGroup.setOnFocusChangeListener(this);
                viewGroup.setOnKeyListener(this);
                viewGroup.setTag(i);
            }
        }

        public void bind(int position) {
            int base = 1;
            if(searchNoResult) {
                base = base + 1;
            }
            if(calcuAlbumCount() != 0) {
                base = base + 1;
            }
            try {
                int index = 2 * (position - base);
                int dataIndex = index + calcuAlbumCount();
                for(int i = 0; i < numTVList.size(); i++) {
                    if(dataIndex + i < hotSearchList.size()) {
                        numTVList.get(i).setText(String.valueOf(index + 1 + i));
                        HotSearchNew.DataBean dataBean = hotSearchList.get(dataIndex + i);
                        SearchUtil.showSearchTitle(dataBean, titleTVList.get(i));
                        viewGroupList.get(i).setVisibility(View.VISIBLE);
                    } else {
                        viewGroupList.get(i).setVisibility(View.GONE);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onClick(View v) {
            try {
                TextView textView = (TextView) v.findViewById(R.id.titleTV);
                if(textView != null) {
                    onClickHotSearchNoPicViewHolder(v.getContext(), textView.getText().toString());
                    onClickLog(v);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private void onClickLog(View v) {
            try {
                int pos = Integer.parseInt(((TextView)(v.findViewById(R.id.numTV))).getText().toString());
                int base = 1;
                if(searchNoResult) {
                    base = base + 1;
                }
                if(calcuAlbumCount() != 0) {
                    base = base + 1;
                }
                int index = 2 * (getLayoutPosition() - base);
                int dataIndex = index + calcuAlbumCount();
                HotSearchNew.DataBean dataBean = hotSearchList.get(dataIndex + (int)v.getTag());
                RequestManager.getInstance().onClickSearchHotNoPicItem(mPageName,pos, dataBean.getAid());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                if (focusBorderView != null) {
                    //focusBorderView.setFocusView(v);
                    setCustomFocusBordonFocusChange(v);
                    FocusUtil.setFocusAnimator(v, focusBorderView);
                    setTVOnFocus(titleTVList.get((int)v.getTag()));
                }
            } else {
                if (focusBorderView != null) {
                    focusBorderView.setUnFocusView(v);
                    FocusUtil.setUnFocusAnimator(v);
                    setTVUnFocus(titleTVList.get((int)v.getTag()));
                }
            }
        }

        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if(event.getAction() == KeyEvent.ACTION_DOWN) {
                if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
                    int index = (int)v.getTag();
                    if(index == 0) {
                        ((SearchInputRecyclerView)(itemView.getParent())).setLastFocusedView(v);
                        return ((SearchInputActivity)v.getContext()).onPressLeftKeyRequestFocus(v);
                    }
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
//                    v.focusSearch(View.FOCUS_DOWN);
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
//                    v.focusSearch(View.FOCUS_UP);
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    int index = (int)v.getTag();
                    if(index == 1) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    protected void onClickSearchHistoryViewHolder(Context context, Integer TvType,Integer AlbumId,String AlbumTitle){
        if(TvType == Constant.DATA_TYPE_SEARCH_ACTOR){
            ActivityLauncher.startActorListActivity(context, AlbumId, false, AlbumTitle);
        }else {
            ActivityLauncher.startVideoDetailActivity(context, AlbumId, TvType, Constant.PAGE_SEARCH);
        }
    }

    class SearchHistoryViewHolder extends RecyclerView.ViewHolder
            implements View.OnClickListener, View.OnFocusChangeListener, View.OnKeyListener {

        public LinearLayout hotSearchNoPicLayout;

        public List<ViewGroup> viewGroupList = new ArrayList<>();
        public List<ImageView> dotIVList = new ArrayList<>();
        public List<TextView> titleTVList = new ArrayList<>();

        SearchHistoryViewHolder(View view) {
            super(view);
            hotSearchNoPicLayout = (LinearLayout) view;
            for (int i = 0; i < hotSearchNoPicLayout.getChildCount(); i++) {
                ViewGroup viewGroup = (ViewGroup) hotSearchNoPicLayout.getChildAt(i);
                viewGroupList.add(viewGroup);
                viewGroup.setOnClickListener(this);
                viewGroup.setOnFocusChangeListener(this);
                viewGroup.setOnKeyListener(this);
                viewGroup.setTag(i);
                dotIVList.add((ImageView) viewGroup.findViewById(R.id.dotIV));
                titleTVList.add((TextView) viewGroup.findViewById(R.id.titleTV));
            }
        }

        public void bind(int position) {
            int searchHistoryStartPos = getSearchHistoryStartPos();
            int index = 2 * (position - searchHistoryStartPos);
            for(int i = 0; i < titleTVList.size(); i++) {
                if(index + i < searchHistoryList.size()) {
                    titleTVList.get(i).setText(searchHistoryList.
                            get(index + i).getAlbumTitle());
                    viewGroupList.get(i).setVisibility(View.VISIBLE);
                } else {
                    viewGroupList.get(i).setVisibility(View.GONE);
                }
            }
        }

        @Override
        public void onClick(View v) {
            for(int i = 0; i < viewGroupList.size(); i++) {
                if(v.equals(viewGroupList.get(i))) {
                    try {
                        int searchHistoryPos = 2 * (getAdapterPosition() - getSearchHistoryStartPos()) + i;
                        if(searchHistoryPos < searchHistoryList.size()) {
                            SearchHistory searchHistory = searchHistoryList.get(searchHistoryPos);
                            if(searchHistory != null) {
                                if (auditDenyAidsList != null) {
                                    for (int j = 0; j < auditDenyAidsList.size(); j++) {
                                        if (auditDenyAidsList.get(j).getAid().equals(StringUtil.toString(searchHistory.getAlbumId()))) {
                                            ToastUtils.showToast(v.getContext(), "该片已下线，请观看其他影片。");
                                            return;
                                        }
                                    }
                                }
                                onClickSearchHistoryViewHolder(v.getContext(), searchHistory.getTvType(), searchHistory.getAlbumId(), searchHistory.getAlbumTitle());
                                RequestManager.getInstance().onClickSearchHistory(mPageName,i + 1, searchHistory.getAlbumId());
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                if (focusBorderView != null) {
                    //focusBorderView.setFocusView(v);
                    setCustomFocusBordonFocusChange(v);
                    FocusUtil.setFocusAnimator(v, focusBorderView);
                    setTVOnFocus(titleTVList.get((int)v.getTag()));
                }
            } else {
                if (focusBorderView != null) {
                    focusBorderView.setUnFocusView(v);
                    FocusUtil.setUnFocusAnimator(v);
                    setTVUnFocus(titleTVList.get((int)v.getTag()));
                }
            }
        }

        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if(event.getAction() == KeyEvent.ACTION_DOWN) {
                if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
                    int index = (int)v.getTag();
                    if(index == 0) {
                        ((SearchInputRecyclerView)(itemView.getParent())).setLastFocusedView(v);
                        return ((SearchInputActivity)v.getContext()).onPressLeftKeyRequestFocus(v);
                    }
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
//                v.focusSearch(View.FOCUS_DOWN);
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
//                v.focusSearch(View.FOCUS_UP);
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    int index = (int)v.getTag();
                    if(index == 1) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    class SearchNoResultViewHolder extends RecyclerView.ViewHolder {
        SearchNoResultViewHolder(View view) {
            super(view);
        }
    }

    private List<HotSearchNew.DataBean> hotSearchList;
    private List<SearchHistory> searchHistoryList;
    private List<AuditDenyAids.DataBean> auditDenyAidsList;
    private boolean searchNoResult = false;

    public SearchNoInputAdapter(List<HotSearchNew.DataBean> hotSearchList,
                                List<SearchHistory> searchHistoryList, boolean searchNoResult) {
        this.hotSearchList = hotSearchList;
        this.searchHistoryList = searchHistoryList;
        this.searchNoResult = searchNoResult;
    }

    public void setData(List<HotSearchNew.DataBean> hotSearchList,
                        List<SearchHistory> searchHistoryList, boolean searchNoResult) {
        this.hotSearchList = hotSearchList;
        this.searchHistoryList = searchHistoryList;
        this.searchNoResult = searchNoResult;
    }

    public void setAuditDenyAidsList(List<AuditDenyAids.DataBean> auditDenyAidsList) {
        this.auditDenyAidsList = auditDenyAidsList;
    }

    public List<HotSearchNew.DataBean> getHotSearchList() {
        return hotSearchList;
    }

    public boolean getSearchNoResult() {
        return searchNoResult;
    }

    public void setSearchHistoryList(List<SearchHistory> searchHistoryList) {
        this.searchHistoryList = searchHistoryList;
        notifyDataSetChanged();
    }

    public void clearSearchHistoryList() {
        if (this.searchHistoryList != null) {
            this.searchHistoryList.clear();
        }
        notifyDataSetChanged();
    }

    private ClearHistoryListener mClearHistoryListener;

    public void setClearHistoryListener(ClearHistoryListener clearHistoryListener) {
        mClearHistoryListener = clearHistoryListener;
    }

    public interface ClearHistoryListener {
        void onClearHistory();
    }


    protected View customSearchNoInputAdapterTitleLayout(ViewGroup parent){
        return LayoutInflater.from(parent.getContext()).inflate(
                R.layout.search_no_input_clear_title_layout, parent, false);
    }

    protected HotSearchLayout creatHotSearchLayout(Context context){
        return new HotSearchLayout(context);
    }

    protected View customSearchHotNoPicLayout(ViewGroup parent){
        return LayoutInflater.from(parent.getContext()).inflate(
                R.layout.search_hot_no_pic_layout, parent, false);
    }

    protected View customSearchHistoryLayout(ViewGroup parent){
        return LayoutInflater.from(parent.getContext()).inflate(
                R.layout.search_history_layout, parent, false);
    }

    protected View customSearchNoResultLayout(ViewGroup parent){
        return LayoutInflater.from(parent.getContext()).inflate(
                R.layout.search_no_result_layout, parent, false);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v;
        RecyclerView.ViewHolder viewHolder = null;
        if (viewType == VIEW_TYPE_HOT_SEARCH_TEXT_TITLE) {
            v = customSearchNoInputAdapterTitleLayout(parent);
            viewHolder = new TitleViewHolder(v);
        } else if (viewType == VIEW_TYPE_HOT_SEARCH_WITH_PIC) {
            v = creatHotSearchLayout(parent.getContext());
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    (int) parent.getContext().getResources().getDimension(R.dimen.y410)
            );
            v.setLayoutParams(layoutParams);
            ((HotSearchLayout)v).setFocusBorderView(focusBorderView);
            viewHolder = new HotSearchWithPicViewHolder(v);
        } else if (viewType == VIEW_TYPE_HOT_SEARCH_NO_PIC) {
            v = customSearchHotNoPicLayout(parent);
            viewHolder = new HotSearchNoPicViewHolder(v);
        } else if (viewType == VIEW_TYPE_SEARCH_HISTORY) {
            v = customSearchHistoryLayout(parent);
            viewHolder = new SearchHistoryViewHolder(v);
        } else if(viewType == VIEW_TYPE_SEARCH_NO_RESULT) {
            v = customSearchNoResultLayout(parent);
            viewHolder = new SearchNoResultViewHolder(v);
        }
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        int viewType = holder.getItemViewType();
        switch (viewType) {
            case VIEW_TYPE_HOT_SEARCH_TEXT_TITLE:
                TitleViewHolder titleViewHolder = (TitleViewHolder) holder;
                titleViewHolder.bind(position);
                break;
            case VIEW_TYPE_HOT_SEARCH_WITH_PIC:
                HotSearchWithPicViewHolder hotSearchWithPicViewHolder
                        = (HotSearchWithPicViewHolder) holder;
                hotSearchWithPicViewHolder.bind();
                break;
            case VIEW_TYPE_HOT_SEARCH_NO_PIC:
                HotSearchNoPicViewHolder hotSearchNoPicViewHolder = (HotSearchNoPicViewHolder) holder;
                hotSearchNoPicViewHolder.bind(position);
                break;
            case VIEW_TYPE_SEARCH_HISTORY:
                SearchHistoryViewHolder searchHistoryViewHolder = (SearchHistoryViewHolder) holder;
                searchHistoryViewHolder.bind(position);
                break;
            case VIEW_TYPE_SEARCH_NO_RESULT:
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        int hotSearchItemCount = 0;
        if(searchNoResult) {
            hotSearchItemCount = 1;
        }
        if (hotSearchList != null && hotSearchList.size() > 0) {
            hotSearchItemCount += 1;
            int albumCount = calcuAlbumCount();
            if(albumCount > 0) {
                hotSearchItemCount += 1;
            }
            hotSearchItemCount += (hotSearchList.size() - albumCount) / 2
                    + (hotSearchList.size() - albumCount) % 2;
        }
        int searchHistoryItemCount = 0;
        if (searchHistoryList != null && searchHistoryList.size() > 0) {
            searchHistoryItemCount += searchHistoryList.size() / 2 + searchHistoryList.size() % 2 + 1;
        }
        return hotSearchItemCount + searchHistoryItemCount;
    }

    @Override
    public int getItemViewType(int position) {
        if(searchNoResult) {
            if(position == 0) {
                return VIEW_TYPE_SEARCH_NO_RESULT;
            }
        }
        int searchNoResultBase = searchNoResult ? 1 : 0;
        if (position == searchNoResultBase) {
            return VIEW_TYPE_HOT_SEARCH_TEXT_TITLE;
        }
        if (hotSearchList == null || hotSearchList.size() == 0) {
            return VIEW_TYPE_SEARCH_HISTORY;
        }
        int albumCount = calcuAlbumCount();
        if (albumCount > 0) {
            if (position == searchNoResultBase + 1) {
                return VIEW_TYPE_HOT_SEARCH_WITH_PIC;
            }
        }
        int base = albumCount > 0 ? searchNoResultBase + 2 : searchNoResultBase + 1;
        int hotSearchSize = base + (hotSearchList.size() - albumCount) / 2
                + (hotSearchList.size() - albumCount) % 2;
        if(position < hotSearchSize) {
            return VIEW_TYPE_HOT_SEARCH_NO_PIC;
        } else if (position == hotSearchSize) {
            return VIEW_TYPE_HOT_SEARCH_TEXT_TITLE;
        } else {
            return VIEW_TYPE_SEARCH_HISTORY;
        }
    }

    private int calcuAlbumCount() {
        int albumCount = 0;
        if(hotSearchList != null) {
            for(int i = 0; i < hotSearchList.size(); i++) {
                HotSearchNew.DataBean dataBean = hotSearchList.get(i);
                if(dataBean != null && SearchUtil.isVRS(dataBean.getCid())) {
                    albumCount++;
                    if(albumCount > 3) {
                        break;
                    }
                } else {
                    break;
                }
            }
        }
        return albumCount;
    }

    private int getSearchHistoryStartPos() {
        int searchHistoryStartPos;
        if(hotSearchList == null || hotSearchList.size() == 0) {
            searchHistoryStartPos = 1;
        } else if(hotSearchList.size() <= 4) {
            searchHistoryStartPos = 3;
        } else {
            searchHistoryStartPos = 3 + (hotSearchList.size() - 4) / 2 + (hotSearchList.size() - 4) % 2;
        }
        return searchHistoryStartPos;
    }

    protected FocusBorderView focusBorderView;

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.focusBorderView = focusBorderView;
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

}
