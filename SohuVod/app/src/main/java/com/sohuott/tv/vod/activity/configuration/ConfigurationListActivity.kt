package com.sohuott.tv.vod.activity.configuration

import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.KeyEvent
import androidx.appcompat.app.AppCompatActivity
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.ListRow
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory
import com.bumptech.glide.request.transition.Transition
import com.lib_statistical.IMP
import com.lib_statistical.addPushEvent
import com.lib_statistical.getInfoEvent
import com.lib_viewbind_ext.viewBinding
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.ListHelper
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.base_router.RouterPath
import com.sohuott.tv.vod.databinding.ActivityConfigurationListBinding
import com.sohuott.tv.vod.lib.api.NetworkApi
import com.sohuott.tv.vod.lib.model.ContentGroup
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohuott.tv.vod.presenter.launcher.selector.ContentPresenterSelector
import io.reactivex.Observer
import io.reactivex.disposables.Disposable

@Route(path = RouterPath.ListView.CONFIGURATION_LIST_ACTIVITY)
class ConfigurationListActivity : AppCompatActivity() {

    @JvmField
    @Autowired
    var mBackgroundImage: String? = null

    @JvmField
    @Autowired
    var mHeaderImageUrl: String? = null

    @JvmField
    @Autowired
    var mChannelId: String? = null

    /**
     * 用户帮助类
     */
    private var mHelper: LoginUserInformationHelper? = null

    /**
     * adapter
     */
    private var mAdapter: ArrayObjectAdapter? = null

    private var itemBridgeAdapter: ItemBridgeAdapter? = null

    /**
     * 数据列表帮助类
     * 来源[HomeContentFragment]
     */
    private var mListHelper: ListHelper? = null

    private val drawableCrossFadeFactory =
        DrawableCrossFadeFactory.Builder(300).setCrossFadeEnabled(true).build()


    private val mBinding: ActivityConfigurationListBinding by viewBinding(
        ActivityConfigurationListBinding::bind
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ARouter.getInstance().inject(this)
        setContentView(R.layout.activity_configuration_list)
        initView()
        initAdapter()
        mHelper = LoginUserInformationHelper.getHelper(this)
        requestData()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        initView()
        initAdapter()
        requestData()
    }

    private fun initView() {
        updateViewState(ACTIVITY_TYPE_LOAD)
        Glide.with(this).asDrawable().load(mBackgroundImage).error(R.drawable.launcher_bg)
            .placeholder(R.drawable.launcher_bg)
            .transition(DrawableTransitionOptions.with(drawableCrossFadeFactory))
            .into(object : CustomTarget<Drawable?>() {
                override fun onResourceReady(
                    resource: Drawable,
                    transition: Transition<in Drawable?>?
                ) {
                    mBinding.parentLayout.background = resource
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    mBinding.parentLayout.background = placeholder
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    mBinding.parentLayout.background = errorDrawable

                    super.onLoadFailed(errorDrawable)
                }

            })


    }


    private val onScrollListener: RecyclerView.OnScrollListener =
        object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                when (newState) {
                    RecyclerView.SCROLL_STATE_DRAGGING, RecyclerView.SCROLL_STATE_SETTLING -> Glide.with(
                        this@ConfigurationListActivity
                    ).pauseRequests()
                    RecyclerView.SCROLL_STATE_IDLE -> Glide.with(this@ConfigurationListActivity)
                        .resumeRequests()
                }
            }
        }

    override fun onBackPressed() {
        if (mBinding.leanbackConfigList.selectedPosition != 1) {
            mBinding.leanbackConfigList.selectedPosition = 1
            return
        }
        super.onBackPressed()
    }

    override fun onStop() {
        super.onStop()
        mBinding.leanbackConfigList.removeOnScrollListener(onScrollListener)
    }

    /**
     * 初始化列表适配器
     */
    private fun initAdapter() {
        val presenterSelector = ContentPresenterSelector()
        mAdapter = ArrayObjectAdapter(presenterSelector)
        itemBridgeAdapter = ItemBridgeAdapter(mAdapter)
        mBinding.leanbackConfigList.addOnScrollListener(onScrollListener)
        mBinding.leanbackConfigList.adapter = itemBridgeAdapter
        //设置间距
        mBinding.leanbackConfigList.verticalSpacing = 48
        //设置动画
        mBinding.leanbackConfigList.itemAnimator = null
    }

    private fun requestData() {
        addPushEvent(10135, IMP, pathInfo = getInfoEvent {
            it["pageId"] = "1050"
        },null,null)
        if (mChannelId?.isEmpty() == true) {
            updateViewState(ACTIVITY_TYPE_ERROR)
            return
        }
        NetworkApi.getHomeContent(
            mChannelId!!.toLong(),
            mHelper!!.loginPassport,
            mHelper!!.loginToken,
            1,
            30,
            object : Observer<ContentGroup> {
                override fun onSubscribe(d: Disposable?) {
                }

                override fun onNext(value: ContentGroup?) {
                    if (value == null || value?.data?.isNullOrEmpty() == true) {
                        updateViewState(ACTIVITY_TYPE_ERROR)
                        return
                    }
                    mAdapter?.clear()
                    if (mListHelper == null) {
                        mListHelper = ListHelper(
                            this@ConfigurationListActivity,
                            mChannelId?.toInt() ?: -1,
                            mBinding.leanbackConfigList,
                            mAdapter
                        )
                        mListHelper!!.setMode(false)
                        mListHelper!!.setIsShowFooterButton(false)
                    }
                    mListHelper!!.assemblyListData(value)
                    mAdapter?.add(
                        0,
                        ListRow(ArrayObjectAdapter(ConfigurationPresenter(this@ConfigurationListActivity)).apply {
                            add(mHeaderImageUrl)
                        })
                    )
                    updateViewState(ACTIVITY_TYPE_NORMAL)
                }

                override fun onError(e: Throwable?) {
                    updateViewState(ACTIVITY_TYPE_ERROR)
                }

                override fun onComplete() {
                }

            })

    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_DPAD_UP && mHeaderImageUrl?.isNotEmpty() == true && mBinding.leanbackConfigList.selectedPosition == 1) {
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    /**
     * 更新页面展示状态
     */
    private fun updateViewState(mViewType: Int) {
        AppLogger.v("当前页面展示状态 mViewType：${mViewType}")
        when (mViewType) {
            ACTIVITY_TYPE_LOAD -> {
                mBinding.leanbackConfigList.gone()
                mBinding.tvError.gone()
                mBinding.pbLoading.visible()
            }
            ACTIVITY_TYPE_NORMAL -> {
                mBinding.tvError.gone()
                mBinding.pbLoading.gone()
                mBinding.leanbackConfigList.visible()
                mBinding.leanbackConfigList.requestFocus()
                if ((mAdapter?.size() ?: 0) >= 1) {
                    mBinding.leanbackConfigList.selectedPosition = 1
                }
            }
            ACTIVITY_TYPE_ERROR -> {
                mBinding.pbLoading.gone()
                mBinding.leanbackConfigList.gone()
                mBinding.tvError.visible()
            }
        }

    }


    companion object {
        const val ACTIVITY_TYPE_NORMAL = 1
        const val ACTIVITY_TYPE_LOAD = 2
        const val ACTIVITY_TYPE_ERROR = 3
    }
}