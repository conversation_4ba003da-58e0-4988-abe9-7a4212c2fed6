package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.MyMessageActivity;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.PushMessageData;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ServerMessage;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohu.lib_utils.NetworkUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohu.lib_utils.FormatUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by fenglei on 17-3-1.
 */

public class HomeMessageView extends RelativeLayout {

    private static final String TAG = HomeMessageView.class.getSimpleName();

    private ImageView iconIV;
    private TextView messageTV;
    private ImageView newMsgIV;


    public HomeMessageView(Context context) {
        super(context);
        init(context);
    }

    public HomeMessageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public HomeMessageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        setFocusable(true);
//        setBackgroundResource(R.drawable.home_message_view_bg);
        setGravity(Gravity.CENTER);
        LayoutInflater.from(context).inflate(R.layout.activity_home_message_view, this, true);
        iconIV = (ImageView) findViewById(R.id.iconIV);
        messageTV = (TextView) findViewById(R.id.messageTV);
        newMsgIV = (ImageView) findViewById(R.id.newMsgIV);
        newMsgIV.setVisibility(View.GONE);
        setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
//                    setBackgroundResource(R.drawable.home_message_view_bg_focused);
                    iconIV.setBackgroundResource(R.drawable.home_message_focused);
                    messageTV.setSelected(true);
                    messageTV.setMarqueeRepeatLimit(-1);
                    messageTV.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                } else {
//                    setBackgroundResource(R.drawable.home_message_view_bg);
                    iconIV.setBackgroundResource(R.drawable.home_message_unfocused);
                    messageTV.setSelected(false);
                    messageTV.setEllipsize(TextUtils.TruncateAt.END);
                }
            }
        });
        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getContext() != null) {
                    if(!NetworkUtils.isConnected(getContext())){
                        ActivityLauncher.startNetworkDialogActivity(getContext());
                        return;
                    }
                    Intent intent = new Intent(getContext(), MyMessageActivity.class);
                    getContext().startActivity(intent);
                    RequestManager.getInstance().onTopBarMessageClickEvent();
                }
            }
        });
    }

    public void showMessageData() {
        NetworkApi.getMessageData(1, 1, new DisposableObserver<ServerMessage>() {
            @Override
            public void onNext(ServerMessage value) {
                LibDeprecatedLogger.d("value = " + value);
                handleServerMessageNew(value);
            }

            @Override
            public void onError(Throwable e) {
                handleServerMessageNew(null);
                LibDeprecatedLogger.d("onError = " + e.toString());
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("onComplete");
            }
        });
    }

    public void showNewPushMessage(PushMessageData myMessage){
        if(myMessage != null){
            setMessageUINew(myMessage,true);
        }
    }


    private void handleServerMessageNew(ServerMessage serverMessage) {
        AppLogger.v(serverMessage.toString());
        List<PushMessageData> messageList = new ArrayList<>();
        ServerMessage.Data userMsg = LoginUserInformationHelper.getHelper(getContext()).getUserMsg();
        ArrayList<ServerMessage.Data> serverMsgDataList = null;
        /**
         * 该方法只处理返回最新一条消息的消息列表，所以如果有用户消息，该消息覆盖服务端获取的消息。
         */
        if (userMsg != null) {
            LibDeprecatedLogger.d("User msg is newest! ");
            serverMsgDataList = new ArrayList<ServerMessage.Data>();
            serverMsgDataList.add(userMsg);
        } else if (serverMessage != null && serverMessage.status == 0
                && serverMessage.data != null && serverMessage.data.size() > 0) {
            LibDeprecatedLogger.d("There are a lot of msg!");
            serverMsgDataList = serverMessage.data;
        }

        if (serverMsgDataList != null) {
            for (int i = 0; i < serverMsgDataList.size(); i++) {
                PushMessageData myMessage = new PushMessageData();
                myMessage.setMsgId(serverMsgDataList.get(i).id);
                myMessage.setCover(serverMsgDataList.get(i).picUrl);
                myMessage.setTitle(serverMsgDataList.get(i).name);
                Date date = FormatUtils.strToDate(serverMsgDataList.get(i).createTime);
                long expire = 0;
                if(date != null){
                    expire = date.getTime();
                }
                myMessage.setExpire(expire);
                messageList.add(myMessage);
                LibDeprecatedLogger.d("Tobbar msg:" + serverMsgDataList.get(i).toString());
            }
        }

        boolean isNewMessage = Util.hasNewMsg(getContext());
        String messageInfo = Util.getNewMsgInfo(getContext(),"");
        LibDeprecatedLogger.d("Tobbar messageInfo:" + messageInfo);


        if(!TextUtils.isEmpty(messageInfo)){
            Gson gson = new Gson();
            PushMessageData save_message = null;
            try {
                save_message = gson.fromJson(messageInfo,PushMessageData.class);
            }catch (Exception e){
                e.printStackTrace();
            }
            if(messageList != null && messageList.size() > 0){
                PushMessageData server_message = messageList.get(0);
                if(save_message == null){
                    setMessageUINew(server_message,true);
                }else {
                    long server_date = server_message.getExpire();
                    long save_date = save_message.getExpire();
                    if(server_date > save_date){
                        setMessageUINew(server_message,true);
                    }else if(server_date == save_date){
                        setMessageUINew(server_message,isNewMessage);
                    } else{
                        setMessageUINew(save_message,isNewMessage);
                    }
                }
            }else {
                setMessageUINew(save_message,isNewMessage);
            }
        }else {
            if(messageList != null && messageList.size() > 0){
                setMessageUINew(messageList.get(0),true);
            }else {
                setMessageUINew(null,false);
            }
        }

    }

    private void setMessageUINew(PushMessageData myMessage, boolean isNew) {
        if(myMessage != null ) {
            if(isNew){
                if (myMessage.getMsgId() != -1) {
                    // 如果此条消息是用户即将付费消息，不显示新消息小图标
                    newMsgIV.setVisibility(View.VISIBLE);
                    Util.setNewMsg(getContext(),true);
                }
                Gson gson = new Gson();
                String message_str = "";
                try {
                    message_str = gson.toJson(myMessage);
                }catch (Exception e){
                    e.printStackTrace();
                }
//                if (myMessage.getMsgId() != -1) {
                    Util.setNewMsgInfo(getContext(), message_str);
//                }
            }else {
                newMsgIV.setVisibility(View.GONE);
            }
            messageTV.setText(myMessage.getTitle());
            setVisibility(View.VISIBLE);
        } else {
            setVisibility(View.GONE);
        }
    }

}
