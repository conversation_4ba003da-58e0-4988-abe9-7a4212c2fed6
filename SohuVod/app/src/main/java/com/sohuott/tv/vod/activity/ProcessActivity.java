package com.sohuott.tv.vod.activity;


import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.Nullable;

import com.lib_statistical.manager.RequestManager;
import com.sh.ott.video.ad.AdTsManger;
import com.sohu.lib_utils.PrefUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.app.AppConstants;
import com.sohuott.tv.vod.app.SohuAppUtil;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ConfigInfoBatch;
import com.sohuott.tv.vod.lib.model.PrivacyInfo;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.partner.SohuAidlService;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.videodetail.activity.VideoActivity;
import com.sohuott.tv.vod.view.PrivacyDialog;

import io.reactivex.observers.DisposableObserver;

public class ProcessActivity extends Activity {

    private static final String TAG = ProcessActivity.class.getSimpleName().toString();
    public static final String VIDEO_DETAIL = "/videodetail";
    public static final String VIDEO_LIST = "/listvideo";
    public static final String GRID_LIST = "/gridlist";
    public static final String LIST_USER = "/listuserrelated";
    public static final String SEARCH = "/search";
    public static final String PLAYER = "/player";
    public static final String PAY = "/pay";

    public static final String VIDEO_DETAIL_ACTION = "com.sohuott.tv.vod.action.DETAIL";
    public static final String VIDEO_LIST_ACTION = "com.sohuott.tv.vod.action.LISTVIDEO";
    public static final String GRID_LIST_ACTION = "com.sohuott.tv.vod.action.GRIDLIST";
    public static final String LIST_USER_ACTION = "com.sohuott.tv.vod.action.LISTUSER";
    public static final String SEARCH_ACTION = "com.sohuott.tv.vod.action.SEARCH";
    public static final String PLAY_ACTION = "com.sohuott.tv.vod.action.PLAY";
    public static final String PAY_ACTION = "com.sohuott.tv.vod.action.PAY";

    public static final String VIDEO_DETAIL_XIAOMI_ACTION = "com.sohuott.tv.vod.xiaomi.action.DETAIL";
    public static final String PLAY_XIAOMI_ACTION = "com.sohuott.tv.vod.xiaomi.action.PLAY";
    public static final String PAY_XIAOMI_ACTION = "com.sohuott.tv.vod.xiaomi.action.PAY";

    String action;
    String path;
    Uri uri;

    private String mPartnerNo;
    private String mPlatformCode;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_process);

        mPartnerNo = Util.getPartnerNo(this);
        mPlatformCode = Util.getPlatformCode(this);

        requestPrivacyDialog();

    }

    private void requestPrivacyDialog() {
        NetworkApi.getConfigInfoBatch(new DisposableObserver<ConfigInfoBatch>() {
            @Override
            public void onNext(ConfigInfoBatch value) {
                if (value != null && value.getData() != null) {
                    int privacyVersion = PrefUtil.getInt( AppConstants.KEY_ALERT_PRIVACY_VERSION, 0);
                    PrivacyInfo privacyInfo = value.getData().getPrivacy_agreement();
                    AppLogger.d(TAG, "privacyVersion ? " + privacyVersion);
                    AppLogger.d(TAG, "value.data.version ? " + privacyInfo.version);
                    AppLogger.d(TAG, "value.data.isAlert ? " + privacyInfo.is_alert);
                    AppLogger.d(TAG, "value.data.user_agreement_btn ? " + privacyInfo.user_agreement_btn);
                    AppLogger.d(TAG, "value.data.privacy_btn ? " + privacyInfo.privacy_btn);
                    AppLogger.d(TAG, "value.data.collect_info_btn ? " + privacyInfo.collect_info_btn);
                    AppLogger.d(TAG, "value.data.third_info_btn ? " + privacyInfo.third_info_btn);
                    AppLogger.d(TAG, "value.data.third_info_btn ? " + privacyInfo.third_info_btn);
                    if (privacyInfo.is_alert == 1 && privacyInfo.version > privacyVersion) {
                        showPrivacyDialog(privacyInfo);
                    } else {
                        continueCreate();
                    }
                } else {
                    continueCreate();
                }

            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("onError in getAboutInfo()error: " + e.getMessage(), e);
                continueCreate();
            }

            @Override
            public void onComplete() {

            }
        },"privacy_agreement");
    }

    private void showPrivacyDialog(final PrivacyInfo privacyInfo) {
        AppLogger.d("EasyPermissions", "showPrivacyDialog ");
        PrivacyDialog dialog = new PrivacyDialog(this, new PrivacyDialog.ExitListener() {
            @Override
            public void onExit(boolean isConfirm) {
                if (isConfirm) {
                    AppLogger.d("EasyPermissions", "PrivacyDialog isConfirm");
                    PrefUtil.putInt( AppConstants.KEY_ALERT_PRIVACY_VERSION, privacyInfo.version);
                    continueCreate();
                } else {
                    AppLogger.d("EasyPermissions", "showPrivacyDialog isConfirm false");
                    SohuAppUtil.exitApp(ProcessActivity.this);
                }
            }
        });
        dialog.setPrivacyInfo(privacyInfo);
        dialog.show();
    }

    private void continueCreate() {
        Util.sHasShowPrivacyDialog = true;
        try {
            SohuAppUtil.init(getApplicationContext());
            Util.uploadDeviceInfo(this.getApplicationContext());
            AdTsManger.getInstants().initContext(this.getApplicationContext());
            if (getIntent() != null) {
                String enterId = Util.getEnterId(0, getLocalClassName(), mPartnerNo);
                RequestManager.getInstance().updateEnterId(enterId);
                RequestManager.getInstance().updateParnerId(mPartnerNo, mPlatformCode);
                RequestManager.getInstance().onMccEvent("1002", "0");
                com.sh.ott.video.ad.AdTsManger.getInstants().initContext(this);

            }
            startService();
            // startAdPreDownloadService();
            sendBroadcastToThirdPartner();

            try {
                distribute();
            } catch (Exception e) {
                Log.e(TAG, "分发异常");
            }
        } catch (Throwable throwable) {
            AppLogger.e("EasyPermissions", "READ_PHONE_STATE"+throwable.getLocalizedMessage());
        }

    }

    private void sendBroadcastToThirdPartner() {
        LibDeprecatedLogger.d("send broadcast when started app");
        Intent intent = new Intent();
        intent.setAction("com.sohuott.tv.vod.START_APP");
        sendBroadcast(intent);
    }

    private void startService() {

        //Start service named SohuAidlService only for jd
        if (mPartnerNo.equals("1080021986")
                || mPartnerNo.equals("1080032710")) {
            Intent aidlIntent = new Intent(this, SohuAidlService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(aidlIntent);
            } else {
                startService(aidlIntent);
            }
        }
    }

    private void distribute() {
        uri = getIntent().getData();
        action = getIntent().getAction();
        AppLogger.e(TAG, getIntent().getAction() + " = action");
        AppLogger.e(TAG, getIntent().getData() + " = uri");
        if (uri != null) {

            //url部分
            AppLogger.e(TAG, "uri -----> " + uri);

            // scheme部分
            String scheme = uri.getScheme();
            AppLogger.e(TAG, "scheme -----> " + scheme);

            // host部分
            String host = uri.getHost();
            AppLogger.e(TAG, "host -----> " + host);

            // 访问路劲
            path = uri.getPath();
            AppLogger.e(TAG, "path -----> " + path);

            // Query部分
            String query = uri.getQuery();
            AppLogger.e(TAG, "query -----> " + query);


            if (path != null && !path.isEmpty()) {
                controlActivity(path, uri);
                finish();
            }

        } else if (action != null) {
            controlActivity(action);
            finish();
        } else {
            finish();
        }
    }

    private void controlActivity(String action) {
        Intent intent = new Intent();

        if (action.equals(VIDEO_DETAIL_ACTION) || action.equals(VIDEO_DETAIL_XIAOMI_ACTION) ||
                action.equals(PLAY_ACTION) || action.equals(PLAY_XIAOMI_ACTION)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.setClass(this, VideoActivity.class);
        } else if (action.equals(VIDEO_LIST_ACTION)) {
            intent.setClass(this, ListVideoActivity.class);
        } else if (action.equals(GRID_LIST_ACTION)) {
            intent.setClass(this, GridListActivityNew.class);
        } else if (action.equals(LIST_USER_ACTION)) {
            intent.setClass(this, ListUserRelatedActivity.class);
        } else if (action.equals(SEARCH_ACTION)) {
            intent.setClass(this, SearchResultActivity.class);
        } else if (action.equals(PAY_ACTION) || action.equals(PAY_XIAOMI_ACTION)) {
            intent.setClass(this, PayActivity.class);
        }
        intent.putExtras(getIntent().getExtras());
        intent.putExtra(ParamConstant.PARAM_IS_FROM_BOOTACTIVITY, true);
        startActivity(intent);
    }

    /**
     * 根据Path确定开启哪个Activity
     *
     * @param path
     * @param uri
     */
    private void controlActivity(String path, Uri uri) {
        Intent intent = new Intent();
        Uri.Builder builder = new Uri.Builder().scheme("yt").appendPath("sohu.tv").encodedQuery(uri.getQuery());

        if (path.equals(VIDEO_DETAIL)) {
            intent.setClass(this, VideoActivity.class);
            builder.path("/videodetail.internal");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK|Intent.FLAG_ACTIVITY_CLEAR_TOP);
        } else if (path.equals(VIDEO_LIST)) {
            intent.setClass(this, ListVideoActivity.class);
            builder.path("/listvideo.internal");
        } else if (path.equals(GRID_LIST)) {
            intent.setClass(this, GridListActivityNew.class);
            builder.path("/gridlist.internal");
        } else if (path.equals(LIST_USER)) {
            intent.setClass(this, ListUserRelatedActivity.class);
            builder.path("/listuserrelated.internal");
        } else if (path.equals(SEARCH)) {
            intent.setClass(this, SearchResultActivity.class);
            builder.path("/search.internal");
        } else if (path.equals(PLAYER)) {
            intent.setClass(this, VideoActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            builder.path("/videodetail.internal");
        } else if (path.equals(PAY)) {
            intent.setClass(this, PayActivity.class);
            builder.path("/pay.internal");
        }

        intent.setData(builder.build());
        intent.putExtra(ParamConstant.PARAM_IS_FROM_BOOTACTIVITY, true);
        startActivity(intent);
    }
}
