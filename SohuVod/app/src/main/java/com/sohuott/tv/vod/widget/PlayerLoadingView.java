package com.sohuott.tv.vod.widget;


import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.RelativeLayout;


import com.nineoldandroids.animation.Animator;
import com.nineoldandroids.animation.AnimatorListenerAdapter;
import com.nineoldandroids.animation.AnimatorSet;
import com.nineoldandroids.animation.ObjectAnimator;
import com.sohuott.tv.vod.R;

/**
 * Created by rita on 16-11-22.
 */
public class PlayerLoadingView extends RelativeLayout {
    private ImageView mLoadingView;
    private AnimatorSet bouncer;

    public PlayerLoadingView(Context context) {
        super(context);
        init(context);
    }

    public PlayerLoadingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public PlayerLoadingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.player_loading, this, true);
//        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
//        inflater.inflate(R.layout.player_loading, this);
        mLoadingView = (ImageView) findViewById(R.id.player_loading_center);
        setBackgroundResource(R.color.transparent);
        /*add zhangyi
            去掉针对设备适配，采用nineoldandroids适配库
         */
//        int scaled=1;
//        /**
//         * ValueAnimator.sDurationScale=0.5 in Judou/IDER Box
//         * the best method is to modify property by reflection
//         */
//        if(Build.MANUFACTURER.equalsIgnoreCase("rockchip")
//                &&(Build.MODEL.equals("Judou J1")||Build.MODEL.equals("IDER_BBA41"))){
//            scaled=2;
//        }
        final ObjectAnimator rotate = ObjectAnimator.ofFloat(mLoadingView, "rotation", 0, 360);
        long duration=550;
        rotate.setDuration(duration);
        ObjectAnimator transUp = ObjectAnimator.ofFloat(mLoadingView, "translationY", 0f, -getResources().getDimensionPixelSize(R.dimen.y46));
        transUp.setDuration(duration/2);

        ObjectAnimator transDown = ObjectAnimator.ofFloat(mLoadingView, "translationY", -getResources().getDimensionPixelSize(R.dimen.y46), 0f);
        transDown.setDuration(duration/2);
        bouncer = new AnimatorSet();
        bouncer.play(transUp).with(rotate);
        bouncer.play(transDown).after(transUp);
        bouncer.addListener(new AnimatorListenerAdapter() {

            @Override
            public void onAnimationEnd(Animator animation) {
                bouncer.start();
            }
        });
    }

    @Override
    public void setVisibility(int visibility) {
        super.setVisibility(visibility);
        if (visibility == VISIBLE) {
            if (bouncer != null && !bouncer.isRunning()) {
                bouncer.start();
            }
        } else {
            if (bouncer != null && bouncer.isRunning()) {
                bouncer.end();
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if(bouncer!=null){
            bouncer.cancel();
            bouncer.end();
            bouncer.removeAllListeners();
        }
    }
}
