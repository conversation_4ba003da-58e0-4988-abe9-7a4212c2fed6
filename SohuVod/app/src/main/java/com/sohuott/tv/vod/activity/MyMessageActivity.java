package com.sohuott.tv.vod.activity;

import android.graphics.Rect;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.lib_statistical.manager.RequestManager;
import com.sohu.lib_utils.FormatUtils;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.MyMessageListAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.DaoSessionInstance;
import com.sohuott.tv.vod.lib.db.greendao.PushMessageData;
import com.sohuott.tv.vod.lib.db.greendao.PushMessageDataDao;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ServerMessage;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableObserver;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by fenglei on 16-1-25.
 */
public class MyMessageActivity extends BaseActivity {

    private static final String TAG = "MyMessageActivity";

    private static final int PAGE = 1;
    private static final int PAGE_SIZE = 40;

    private LoadingView mLoadingView;
    private TextView mNoMsgTV;
    private RecyclerView mListView;
    private MyMessageListAdapter mAdapter;
    private LoginUserInformationHelper mHelper;
    private PushMessageDataDao mPushMessageDataDao;
    List<ServerMessage.Data> mDbMessageList = new ArrayList<>();
    private SimpleDateFormat mDateFormat;
    private FocusBorderView mFocusBorderView;
    private String mSaveMessageStr = "";
    CompositeDisposable compositeDisposable = new CompositeDisposable();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_my_message);
        mDateFormat = new SimpleDateFormat("yyy-MM-dd HH:mm:ss");
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mNoMsgTV = (TextView) findViewById(R.id.no_msg_tv);
        mListView = (RecyclerView) findViewById(R.id.listview_message);
        mListView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mListView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y46);
            }
        });
        mListView.setOnScrollListener(new FinishScrollListener());
        mFocusBorderView = (FocusBorderView) findViewById(R.id.focus_border_view);
        Util.setNewMsg(this, false);
        mSaveMessageStr = Util.getNewMsgInfo(this,"");
        mHelper = LoginUserInformationHelper.getHelper(getApplicationContext());
        mPushMessageDataDao = DaoSessionInstance.getDaoSession(this).getPushMessageDataDao();
        initMsgFormDb();
        RequestManager.getInstance().onMessageExposureEvent();
        setPageName("6_message");
    }

    private void getMessage() {
        NetworkApi.getMessageData(PAGE, PAGE_SIZE, new DisposableObserver<ServerMessage>() {
            @Override
            public void onNext(ServerMessage value) {
                ServerMessage.Data userMsg = LoginUserInformationHelper.getHelper(getApplicationContext()).getUserMsg();
                ArrayList<ServerMessage.Data> msgList = null;
                if (value != null && value.status == 0 && value.data != null
                        && value.data.size() > 0) {
                    msgList = value.data;
                    LibDeprecatedLogger.d("There are other data!");
                    if (userMsg != null) {
                        LibDeprecatedLogger.d("There is a User msg!");
                        msgList.add(0, userMsg);
                    }
                } else if (userMsg != null) {
                    LibDeprecatedLogger.d("There's only one User msg!");
                    msgList = new ArrayList<ServerMessage.Data>();
                    msgList.add(userMsg);
                }
                if (mDbMessageList != null && mDbMessageList.size() > 0) {
                    if (msgList != null && msgList.size() > 0) {
                        mDbMessageList.addAll(msgList);
                        sortDataAndInitUI();
                    }
                }else {
                    // initMsg(value);
                    initMsgUI(msgList);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("onErrorResponse, error = " + e);
                if(mDbMessageList != null && mDbMessageList.size() > 0){
                    initMsgUI(mDbMessageList);
                }else {
                    initMsgUI(null);
                }
            }

            @Override
            public void onComplete() {

            }
        });
    }

    private void initMsg(ServerMessage serverMessage) {
        if (serverMessage != null && serverMessage.status == 0 && serverMessage.data != null
                && serverMessage.data.size() > 0) {
            initMsgUI(serverMessage.data);
        } else {
            initMsgUI(null);
        }
    }

    private void convertMessage(PushMessageData msg){
        if(msg != null){
            ServerMessage.Data data = new ServerMessage.Data();
            data.content = msg.getDesc();
            Date date = new Date(msg.getExpire());
            data.createTime = mDateFormat.format(date);
            data.id = msg.getMsgId();
            data.name = msg.getTitle();
            data.picUrl = msg.getCover();
            data.type = "1";
            ServerMessage.Parameter parameter = new ServerMessage.Parameter();
            parameter.dataType = 0;
            parameter.albumId = ""+msg.getAid();
            Gson gson = new Gson();
            data.parameter = gson.toJson(parameter);
            mDbMessageList.add(data);
        }
    }
    private void initMsgFormDb() {
        Observable observable = Observable.create(new ObservableOnSubscribe<String>() {
            @Override
            public void subscribe(ObservableEmitter e) throws Exception {
                LibDeprecatedLogger.d("initMsgFormDb, subscribe ");
                List<PushMessageData> messageList = mPushMessageDataDao.queryBuilder().
                        orderDesc(PushMessageDataDao.Properties.MsgId).list();
                if(messageList != null && messageList.size() > 0){
                    for (int i = 0; i < messageList.size(); i++) {
                        PushMessageData msg = messageList.get(i);
                        long currentTime = System.currentTimeMillis();
                        long msgTime = msg.getExpire();
                        if(currentTime >= msgTime){
                            int gap = (int) ((currentTime - msgTime)/(60*60*1000*24));
                            if(gap > 30){
                                mPushMessageDataDao.delete(msg);
                            }else {
                                convertMessage(msg);
                            }
                        }else {
                            convertMessage(msg);
                        }
                    }
                }
                e.onNext("next");
            }
        });

        DisposableObserver disposableObserver = new DisposableObserver <String>() {
            @Override
            public void onNext(String value) {
                LibDeprecatedLogger.d("initMsgFormDb, onNext ");
                getMessage();
            }

            @Override
            public void onError(Throwable e) {
                getMessage();
            }

            @Override
            public void onComplete() {

            }
        };
        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(disposableObserver);
        compositeDisposable.add(disposableObserver);
    }

    private void initMsgUI(List<ServerMessage.Data> messageList) {
        mLoadingView.setVisibility(View.GONE);
        if (messageList != null && messageList.size() > 0) {
            if(!TextUtils.isEmpty(mSaveMessageStr)) {
                Gson gson = new Gson();
                PushMessageData save_message = null;
                try {
                    save_message = gson.fromJson(mSaveMessageStr, PushMessageData.class);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if(save_message != null){
                    ServerMessage.Data data = messageList.get(0);
                    if(save_message.getMsgId() != messageList.get(0).id){
                        PushMessageData myServerMessage = new PushMessageData();
                        myServerMessage.setMsgId(data.id);
                        myServerMessage.setCover(data.picUrl);
                        myServerMessage.setTitle(data.name);
                        Date date = FormatUtils.strToDate(data.createTime);
                        long expire = 0;
                        if(date != null){
                            expire = date.getTime();
                        }
                        myServerMessage.setExpire(expire);
                        Gson gson_save = new Gson();
                        String message_str = "";
                        try {
                            message_str = gson_save.toJson(myServerMessage);
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                        // user pay msg is -1.
//                        if (data.id != -1) {
                            Util.setNewMsgInfo(this,message_str);
//                        }
                    }
                }
            }
            if (mAdapter != null) {
                mAdapter.setMessageList(messageList);
                mAdapter.notifyDataSetChanged();
            } else {
                LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
                linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
                mListView.setLayoutManager(linearLayoutManager);
                mAdapter = new MyMessageListAdapter(this,mListView, messageList);
                mAdapter.setFocusBorderView(mFocusBorderView);
                mAdapter.setOnItemListen(new MyMessageListAdapter.OnItemListen() {
                    @Override
                    public void onClick(int position) {
                        clickItem(position);
                    }
                });
                mListView.setAdapter(mAdapter);
            }
            mNoMsgTV.setVisibility(View.GONE);
            mListView.setVisibility(View.VISIBLE);
        } else {
            mNoMsgTV.setVisibility(View.VISIBLE);
            mListView.setVisibility(View.GONE);
        }
    }

    private void clickItem(int pos) {
        List<ServerMessage.Data> messageList = mAdapter.getMessageList();
        if(messageList != null && messageList.size() > 0 && pos < messageList.size()) {
            ServerMessage.Data data = messageList.get(pos);
            AppLogger.d(TAG, "data.content ? " + data.content);
            AppLogger.d(TAG, "data.toString ? " + data.toString());
            String type = messageList.get(pos).type;
            AppLogger.d(TAG, "type ? " + type);
            Gson parameeterObj = new Gson();
            ServerMessage.Parameter parameter = parameeterObj.fromJson(
                    messageList.get(pos).parameter, ServerMessage.Parameter.class);
            try {
                if(type != null) {
                    if(type.equals("0") || type.equals("-1")) {
                        showDetailDialog(data.name, data.content);

                    } else if(type.equals("1") || type.equals("5")) {
                        int aid;
                        if(parameter.dataType == 2) {
                            aid = Integer.parseInt(parameter.videoId);

                        } else {
                            aid = Integer.parseInt(parameter.albumId);
                        }
                        ActivityLauncher.startVideoDetailActivity(this, aid, parameter.dataType, Constant.PAGE_MY_MESSAGE);
                        RequestManager.getInstance().onClickMessageDetail(pos, String.valueOf(aid));
                    } else if(type.equals("2")) {
                        ActivityLauncher.startListVideoActivity(this, Integer.parseInt(parameter.labelId));
                        RequestManager.getInstance().onClickMessageLabel(pos, parameter.labelId);
                    }else if(type.equals("3")){
                        ActivityLauncher.startPayActivity(MyMessageActivity.this,PayActivity.PAY_SOURCE_MESSAGE_ACTIVITY);
                        RequestManager.getInstance().onClickMessageVip();
                    }else if(type.equals("4")){
                        RequestManager.getInstance().onClickMessageVip();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private PopupWindow mAgreementContentWindow;

    private void showDetailDialog(String title, String detail) {
        View contentView = LayoutInflater.from(this.getApplicationContext()).inflate(R.layout.dialog_more_detail, null);
        if (mAgreementContentWindow == null) {
            mAgreementContentWindow = new PopupWindow(contentView, ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT, true);

            // 如果不设置PopupWindow的背景，无论是点击外部区域还是Back键都无法dismiss弹框
            mAgreementContentWindow.setBackgroundDrawable(getApplicationContext().getResources().getDrawable(R.drawable.dialog_more_detail_bg));
            mAgreementContentWindow.setTouchable(true);
            mAgreementContentWindow.setFocusable(true);
            mAgreementContentWindow.setOutsideTouchable(true);
            mAgreementContentWindow.setAnimationStyle(R.style.PopupAnimation);
            mAgreementContentWindow.setContentView(contentView);
        }
        TextView mAgreementTextView = (TextView) mAgreementContentWindow.getContentView().findViewById(R.id.more_detail_text);
        TextView mTitleTextView = (TextView) mAgreementContentWindow.getContentView().findViewById(R.id.more_detail_title);
        mAgreementTextView.setText(detail);
        mTitleTextView.setText(title);
        mAgreementContentWindow.showAtLocation(mListView, Gravity.LEFT | Gravity.BOTTOM, 0, 0);
    }


    private class FinishScrollListener extends RecyclerView.OnScrollListener {

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                if (mListView == null) {
                    return;
                }
                if (mListView.getFocusedChild() == null) {
                    return;
                }



                RecyclerView.ViewHolder viewHolder = mListView.getChildViewHolder(
                        mListView.getFocusedChild());
                if (viewHolder != null && viewHolder.itemView != null) {
                    mFocusBorderView.setFocusView(viewHolder.itemView);
                    FocusUtil.setFocusAnimator(viewHolder.itemView, mFocusBorderView);
                }
            }
        }
    }

    private void sortDataAndInitUI(){
        Observable observable = Observable.create(new ObservableOnSubscribe <String>() {
            @Override
            public void subscribe(ObservableEmitter e) throws Exception {
                LibDeprecatedLogger.d("sortDataAndInitUI subscribe");
                Collections.sort(mDbMessageList, new Comparator<ServerMessage.Data>() {
                    @Override
                    public int compare(ServerMessage.Data data, ServerMessage.Data t1) {
                        Date date1 = FormatUtils.strToDate(data.createTime);
                        Date date2 = FormatUtils.strToDate(t1.createTime);
                        return date2.compareTo(date1);
                    }
                });
                e.onNext("next");
            }
        });

        DisposableObserver disposableObserver = new DisposableObserver <String>() {
            @Override
            public void onNext(String value) {
                LibDeprecatedLogger.d("sortDataAndInitUI onNext");
                initMsgUI(mDbMessageList);
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("sortDataAndInitUI onError");
                initMsgUI(mDbMessageList);
            }

            @Override
            public void onComplete() {
            }
        };
        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(disposableObserver);
        compositeDisposable.add(disposableObserver);
    }


}
