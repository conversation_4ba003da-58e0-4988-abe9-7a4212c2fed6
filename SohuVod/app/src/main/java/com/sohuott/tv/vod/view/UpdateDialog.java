package com.sohuott.tv.vod.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.sohuott.tv.vod.R;

/**
 * Created by wenjingbian on 2016/3/16.
 */
public class UpdateDialog extends Dialog {

    private Context mContext;

    private Button btnPositive, btnNegative;

    private TextView mTvTitle, mTvMsg;

    public UpdateDialog(Context context) {
        super(context, R.style.UpdateDialog);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.dialog_update);

        btnPositive = (Button) findViewById(R.id.btn_dialog_positive);
        btnNegative = (Button) findViewById(R.id.btn_dialog_negative);
        mTvTitle = (TextView) findViewById(R.id.tv_dialog_title);
        mTvMsg = (TextView) findViewById(R.id.tv_dialog_msg);
    }

    private void setTitle(String title) {
        mTvTitle.setText(title);
    }

    private void setMessage(String msg) {
        if (msg != null) {
            mTvMsg.setVisibility(View.VISIBLE);
            mTvMsg.setText(msg);
        }
    }

    private void setPositiveText(String positiveText) {
        if (positiveText == null || positiveText.equals(""))
            btnPositive.setVisibility(View.GONE);
        else
            btnPositive.setText(positiveText);
    }

    private void setNegativeText(String negativeText) {
        if (negativeText == null || negativeText.equals(""))
            btnNegative.setVisibility(View.GONE);
        else
            btnNegative.setText(negativeText);
    }

    private void setPositiveListener(View.OnClickListener btnPositiveListener) {
        btnPositive.setOnClickListener(btnPositiveListener);
    }

    private void setNegativeListener(View.OnClickListener btnNegativeListener) {
        btnNegative.setOnClickListener(btnNegativeListener);
    }

    public static class Builder {
        public String mMsg, mTitle, mBtnPositiveText, mBtnNegativeText;
        public View.OnClickListener mBtnPositiveListener, mBtnNegativeListener;
        public OnCancelListener mCancelListener;

        private Context context;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setTitle(int titleRes) {
            this.mTitle = context.getString(titleRes);
            return this;
        }

        public Builder setTitle(String title) {
            this.mTitle = title;
            return this;
        }

        public Builder setMsg(String msg) {
            this.mMsg = msg;
            return this;
        }

        public Builder setMsg(int msgRes) {
            this.mMsg = context.getString(msgRes);
            return this;
        }

        public Builder setPositiveButton(String positiveText, View.OnClickListener btnPositiveListener) {
            this.mBtnPositiveText = positiveText;
            this.mBtnPositiveListener = btnPositiveListener;
            return this;
        }

        public Builder setPositiveButton(int positiveTextRes, View.OnClickListener btnPositiveListener) {
            this.mBtnPositiveText = context.getString(positiveTextRes);
            this.mBtnPositiveListener = btnPositiveListener;
            return this;
        }

        public Builder setNegativeButton(String negativeText, View.OnClickListener btnNegativeListener) {
            this.mBtnNegativeText = negativeText;
            this.mBtnNegativeListener = btnNegativeListener;
            return this;
        }

        public Builder setNegativeButton(int negativeTextRes, View.OnClickListener btnNegativeListener) {
            this.mBtnNegativeText = context.getString(negativeTextRes);
            this.mBtnNegativeListener = btnNegativeListener;
            return this;
        }

        public Builder setCancelListener(OnCancelListener cancelListener) {
            this.mCancelListener = cancelListener;
            return this;
        }

        public UpdateDialog create() {
            UpdateDialog dialog = new UpdateDialog(context);
            dialog.show();
            dialog.setTitle(mTitle);
            dialog.setMessage(mMsg);
            dialog.setPositiveText(mBtnPositiveText);
            dialog.setNegativeText(mBtnNegativeText);
            dialog.setPositiveListener(mBtnPositiveListener);
            dialog.setNegativeListener(mBtnNegativeListener);
            dialog.setOnCancelListener(mCancelListener);
            return dialog;
        }

        public UpdateDialog show() {
            return create();
        }
    }
}
