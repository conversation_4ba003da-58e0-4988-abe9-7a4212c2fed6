package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.leanback.widget.Presenter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.widget.PlayingView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

public class SmallPicPresenter extends Presenter {
    public Context mContext;
    public MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.fragment_episode_small_pic_item, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        ViewHolder vh = (ViewHolder) viewHolder;
        if (item instanceof EpisodeVideos.Video) {
            EpisodeVideos.Video video = (EpisodeVideos.Video) item;
            if (video.type == 1) {
                //推荐视频
                Glide.with(mContext)
                        .load(video.videoExtendsPic_640_360)
                        .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                        .into(vh.posterView);
                vh.titleView2.setText(video.tvName);
                vh.titleView1.setVisibility(View.GONE);
                vh.tagView.setVisibility(View.VISIBLE);
                vh.tagView.setImageResource(R.drawable.episode_item_recommend);
                vh.playingView.hide();

            } else {
                //普通视频
                Glide.with(mContext)
                        .load(video.videoExtendsPic_640_360)
                        .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                        .into(vh.posterView);
                if (video.tvStype != 1 && video.tvStype != 38) {
                    if (!video.isPgc) {
                        vh.tagView.setVisibility(View.VISIBLE);
                        vh.tagView.setImageResource(R.drawable.episode_item_trailer);
                    }
                } else if (video.tvSetIsFee == 1) {
                    vh.tagView.setVisibility(View.VISIBLE);
                    if (video.isSyncBroadcast == 1) {
                        vh.tagView.setImageResource(R.drawable.episode_item_forestall);
                    } else {
                        if (video.categoryId == Constant.EDU_CATE_ID_API) {
                            vh.tagView.setImageResource(R.drawable.item_corner_23);
                        } else {
                            vh.tagView.setImageResource(R.drawable.episode_item_vip);
                        }
                    }
                } else {
                    if (video.categoryCode== Constant.CATECODE_MOVIE) {
                        vh.tagView.setVisibility(View.VISIBLE);
                        vh.tagView.setImageResource(R.drawable.episode_item_feature);
                    } else {
                        vh.tagView.setVisibility(View.GONE);
                    }
                }

                if (StringUtil.isEmpty(video.tvSubName)) {
                    vh.errorView.setText("数据缺失");
                    vh.errorView.setVisibility(View.VISIBLE);
                    vh.tagView.setVisibility(View.GONE);
                    vh.titleView1.setVisibility(View.GONE);
                    vh.titleView2.setVisibility(View.GONE);
                    vh.nameBgView.setVisibility(View.GONE);
                    vh.posterView.setBackground(ContextCompat.getDrawable(mContext, R.drawable.episode_sml_pic_item_corner));
                    vh.errorLogoView.setVisibility(View.VISIBLE);
                } else {
                    vh.titleView1.setText(Util.formatVideoLength(video.tvLength));
                    vh.titleView2.setText(video.tvSubName);
                    vh.titleView1.setVisibility(View.VISIBLE);
                    vh.titleView2.setVisibility(View.VISIBLE);
                    vh.errorLogoView.setVisibility(View.GONE);
                    vh.errorView.setVisibility(View.GONE);
                }
            }
            setSelected(video.isSelected, vh);
            vh.view.setOnFocusChangeListener((view, b) -> {
                if (b) {
//                        vh.titleView2.setTextColor(mContext.getResources().getColor(R.color.tv_color_e6e8e8ff));
                } else {
                    setSelected(video.isSelected, vh);
                }
            });
        }
    }

    private void setSelected(boolean isSelected, ViewHolder vh){
        LibDeprecatedLogger.d("isSelected : " + isSelected + " , vh.titleView2 : " + vh.titleView2.getText());
        if (isSelected) {
            if (vh.view.isFocused()) {
                return;
            }
//            AppLogger.INSTANCE.v("isSelected");
            vh.playingView.show();
            vh.titleView2.setTextColor(mContext.getResources().getColor(R.color.tv_color_ff6247));
        } else {
            vh.playingView.hide();
            vh.titleView2.setTextColor(mContext.getResources().getColor(R.color.tv_color_e6e8e8ff));
        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public class ViewHolder extends Presenter.ViewHolder{
        ImageView posterView;
        PlayingView playingView;
        ImageView tagView, errorLogoView;
        TextView titleView1, titleView2, lengthView, errorView;
        View nameBgView;


        public ViewHolder(View view) {
            super(view);
            posterView = view.findViewById(R.id.episode_poster);
            playingView = view.findViewById(R.id.on_play_icon);
            tagView = view.findViewById(R.id.episode_btn_logo);
            titleView1 = view.findViewById(R.id.episode_title1);
            titleView2 = view.findViewById(R.id.episode_title2);
            lengthView = view.findViewById(R.id.episode_video_length);
            errorView = view.findViewById(R.id.episode_err);
            nameBgView = view.findViewById(R.id.name_bg);
            errorLogoView = view.findViewById(R.id.error_logo);
        }
    }
}
