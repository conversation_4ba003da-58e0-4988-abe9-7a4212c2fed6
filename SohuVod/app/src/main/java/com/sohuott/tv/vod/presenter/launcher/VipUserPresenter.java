package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.model.VipUserState;
import com.sohuott.tv.vod.widget.lb.VipUserView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

import java.util.HashMap;


public class VipUserPresenter extends Presenter implements View.OnFocusChangeListener {
    private static final String TAG = "VipUserPresenter";
    private Context mContext;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;
    private HashMap<String, String> pathInfo;
    private LoginUserInformationHelper mHelper;




    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_vip_user, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        mHelper = LoginUserInformationHelper.getHelper(mContext);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        ViewHolder vh = (ViewHolder) viewHolder;
        if (item instanceof VipUserState) {

            vh.mUserView.setPathInfo(((VipUserState) item).pathInfo);

            HashMap<String, String> memoInfo = new HashMap<>();
            if (mHelper.getIsLogin()) {
                memoInfo.put("isLogin", "1");
                if (mHelper.isVip()) {
                    memoInfo.put("isVip", "1");
                } else {
                    memoInfo.put("isVip", "0");
                    if (!TextUtils.isEmpty(mHelper.getUserTicketNumber()) && !TextUtils.equals(mHelper.getUserTicketNumber(), "0张")) {
                        memoInfo.put("isExpire", "1");
                    }
                }
            } else {
                memoInfo.put("isLogin", "0");
            }


            RequestManager.getInstance().onAllEvent(new EventInfo(10150, "imp"),
                    ((VipUserState) item).pathInfo, null, memoInfo);
        }

        vh.mOpenVip.setOnFocusChangeListener(this);
        vh.mLogin.setOnFocusChangeListener(this);


        vh.mUserView.refresh();
    }



    @Override
    public void setOnClickListener(Presenter.ViewHolder holder, View.OnClickListener listener) {
        super.setOnClickListener(holder, listener);
        Log.d(TAG, "setOnClickListener: ");
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private Button mLogin, mOpenVip;
        private VipUserView mUserView;

        ViewHolder(View view) {
            super(view);
            mLogin = (Button) view.findViewById(R.id.vip_header_login);
            mOpenVip = (Button) view.findViewById(R.id.vip_header_open_vip);
            mUserView = (VipUserView) view;
        }
    }
}
