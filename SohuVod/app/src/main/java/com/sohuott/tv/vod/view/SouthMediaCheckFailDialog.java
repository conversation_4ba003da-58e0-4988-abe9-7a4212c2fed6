package com.sohuott.tv.vod.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;

/**
 * Created by yizhang210244 on 2017/12/26.
 */

public class SouthMediaCheckFailDialog extends Dialog{
    private Button mOkButton;
    private TextView mUserGid;
    public SouthMediaCheckFailDialog(@NonNull Context context) {
        super(context, R.style.UpdateDialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.south_media_dialog_failed);
        mOkButton = (Button) findViewById(R.id.ok);
        mUserGid = (TextView) findViewById(R.id.user_gid);
        mUserGid.setText("GID : "+ UserInfoHelper.getGid());
    }

    public void setOkButtonListener(View.OnClickListener onClickListener) {
        mOkButton.setOnClickListener(onClickListener);
    }
}
