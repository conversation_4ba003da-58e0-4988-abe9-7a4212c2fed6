package com.sohuott.tv.vod.utils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/3/4.
 */
public class ParamConstant {
    public static final String PARAM_CHILD = "param_child";
    public static final String PARAM_AID = "aid";
    public static final String PARAM_VID = "vid";
    public static final String PARAM_CATEGORY_ID = "category_id";
    public static final String PARAM_TAGID = "tagid";
    public static final String PARAM_TAG_POS = "tag_pos";
    public static final String PARAM_SOURCE = "source";
    public static final String PARAM_SOURCEID = "source_id";
    public static final String PARAM_ALBUM_TITLE = "album_title";
    public static final String PARAM_ALBUM_POSTER = "album_poster";
    public static final String PARAM_LABEL_ID = "label_id";
    public static final String PARAM_LABEL_NORMAL = "label_normal";
    public static final String PARAM_CATE_ID = "cate_id";
    public static final String PARAM_CATECODE_FIRST = "catecode_first";
    public static final String PARAM_CATE_SHOW_HEADER = "cate_show_header";
    public static final String PARAM_ACTOR_ID = "actor_id";
    public static final String PARAM_DIRECTOR_ID = "director_id";
    public static final String PARAM_ACTOR_NAME = "actor_name";
    public static final String PARAM_AULBUM_INFO = "album_info";
    public static final String PARAM_RECOMMEND_DATABEAN = "PARAM_RECOMMEND_DATABEAN";
    public static final String PARAM_PRODUCER_ID = "producer_id";
    public static final String PARAM_PRODUCER_SUB_ID = "producer_sub_id";
    public static final String PARAM_PAGE_SOURCE = "page_source";
    public static final String PARAM_CHANNEL_ID = "channel_id";
    // judge whether the video is PGC: 0, VRS; 1, VR; 2, PGC
    public static final String PARAM_VIDEO_TYPE = "video_type";
    public static final String PARAM_VIDEO_ORDER = "video_order";
    public static final String PARAM_IS_FROM_CAROUSEL = "is_from_carousel";
    public static final String PARAM_IS_FROM_BOOTACTIVITY = "is_from_bootactivity";
    public static final String PARAM_CAROUSEL_EPISODE_VID = "carousel_vid";
    public static final String PARAM_EVENT_ID = "event_id";

    public static final String PAGE_SOURCE_VIDEO_DETAIL = "videoDetailPage";
    public static final String PAGE_SOURCE_PLAYER = "playerPage";
    public static final String PAGE_SOURCE_TEENAGER = "teenager";

    public static final String PARAM_SUBCATE = "subcate";

    public static final String PARAM_SEARCH_TXT = "search_txt";
    public static final String PARAM_IS_VOICE_SEARCH = "is_voice_search";

    public static final String PARAM_IS_NORMAL_LOGIN = "is_normal_login";

    public static final String PARAM_SUBJECT_ID = "subject_id";

    public static final String PARAM_SUBJECT_PIC1 = "subject_pic1";
    public static final String PARAM_SUBJECT_PIC2 = "subject_pic2";
    public static final String PARAM_SUBJECT_SMALL_PIC = "subject_small_pic";


    public static final String PARAM_LEFT_INDEX = "left_index";

    public static final String PARAM_VIP = "is_vip";

    public static final String PARAM_LOOP_CHANNEL_ID = "loop_channel_id";

    public static final String PARAM_FROM_PAGE = "new_video_detail_from";

    public static final String PARAM_VIDEO_FILTER = "video_filter";
    public static final String PARAM_VIDEO_FILTER_KEY = "video_filter_key";
    public static final String PARAM_VIDEO_FILTER_VALUE = "video_filter_value";
    public static final String PARAM_VIDEO_CATE_CODE = "video_cate_code";

    public static final String PARAM_CHANNEL_LIST_ID = "channel_list_id";

    //0:续费会员，开通会员。 1:只显示单片购买。 2:续费会员和单片购买。3,for ticket
    public static final String PARAM_PAY_TYPE = "pay_type";

    public static final String PARAM_IS_DTS = "is_dts";
    public static final String PARAM_IS_POPUP_WINDOW = "is_from_popup_window";

    //支付页面启动来源
    public static final String PARAM_PAY_SOURCE_COME_FROM = "pay_source_come_from";

    //判断是否从重启
    public static final String PARAM_IS_BOOT="is_boot";
    //添加延迟参数
    public static final String PARAM_DELAY="param_delay";

    public static final String PARAM_LIVE_TYPE = "live_type";

    public static final String PARAM_LIVE_ROOM_ID = "live_room_id";
    public static final String PARAM_LIVE_ANCHOR_ID = "live_anchor_id";

    public static final String PARAM_CHILD_HC_TYPE = "history_collection_type";

    public static final String PARAM_LIVE_TV_URL = "live_tv_url";
    public static final String PARAM_LIVE_TV_URL_BAKUP = "live_tv_url_bakup";
    public static final String PARAM_LIVE_TV_PIC = "live_tv_pic";

    public static final String  PARAM_SELECT_TAB = "select_tab";
    public static final String PARAM_GO_HOME = "go_home";
    public static final String PARAM_GO_TO_MINE = "go_to_mine";
    public static final String PARAM_IS_SHOW_UPDATE = "is_show_update";
}
