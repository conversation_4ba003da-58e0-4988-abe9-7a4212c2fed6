package com.sohuott.tv.vod.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.TempletItemAdapter;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by music on 17-9-9.
 */

public class TempletFragment extends BaseFragment  {
    private static final String ARG_SECTION_NUMBER = "section_number";
    private static final String ARG_SUM_NUMBER = "sum_number";
    private static final String ARG_SUB_CATECODE = "sub_catecode";
    private static final String ARG_IS_PGC = "is_pgc";

    private int number;
    private int sumNumber;
    private int subCateCode;
    private int mScrollToPosition;
    private boolean mIsNeedScroll;
    private boolean mIsPgc = true;

    /**
     * 目标项是否在最后一个可见项之后
     */
    private boolean mShouldScroll;
    /**
     * 记录目标项位置
     */
    private int mToPosition;

    private View root;
    private FocusBorderView focusBorderView;

    private TempletItemAdapter templetItemAdapter;
    private CustomLinearRecyclerView customLinearRecyclerView;
    private CustomLinearLayoutManager customLinearLayoutManager;
    private Observable mObservable;

    private TempletItemAdapter.OnKeyChange mOnKeyChange;


    public static TempletFragment newInstance(int sumNumber, int sectionNumber, int subCateCode, boolean isPgc) {
        TempletFragment fragment = new TempletFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_SECTION_NUMBER, sectionNumber);
        args.putInt(ARG_SUB_CATECODE, subCateCode);
        args.putInt(ARG_SUM_NUMBER, sumNumber);
        args.putBoolean(ARG_IS_PGC, isPgc);
        fragment.setArguments(args);
        return fragment;
    }

    public void setOnkeyChange(TempletItemAdapter.OnKeyChange onkeyChange) {
        this.mOnKeyChange = onkeyChange;
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        number = getArguments().getInt(ARG_SECTION_NUMBER);
        sumNumber = getArguments().getInt(ARG_SUM_NUMBER);
        subCateCode = getArguments().getInt(ARG_SUB_CATECODE);
        mIsPgc = getArguments().getBoolean(ARG_IS_PGC);
        if (!Util.getSystemModel().equals("MiTV")) {
            root = inflater.inflate(R.layout.fragment_templet, container, false);
        } else {
            root = inflater.inflate(R.layout.fragment_templet_xiaomi, container, false);
        }

        focusBorderView = (FocusBorderView) root.findViewById(R.id.fragment_item_focus);
        customLinearRecyclerView = (CustomLinearRecyclerView) root.findViewById(R.id.list);
        customLinearRecyclerView.setItemViewCacheSize(0);
        customLinearLayoutManager = new CustomLinearLayoutManager(getActivity());
        customLinearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        customLinearRecyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.bottom = getResources().getDimensionPixelOffset(R.dimen.y40);
            }
        });
        customLinearLayoutManager.setCustomPadding(2 * getResources().getDimensionPixelSize(R.dimen.y158),
                2 * getResources().getDimensionPixelSize(R.dimen.y158));
        templetItemAdapter = new TempletItemAdapter(getActivity(), number, sumNumber, mIsPgc);
        templetItemAdapter.setOnKeyChange(mOnKeyChange);
        templetItemAdapter.setFocusBorderView(focusBorderView);
        templetItemAdapter.setCustomLinearRecyclerView(customLinearRecyclerView);
        customLinearRecyclerView.setLayoutManager(customLinearLayoutManager);
        customLinearRecyclerView.setAdapter(templetItemAdapter);

        customLinearRecyclerView.setOnScrollListener(new TempletOnScrollListener());


        requestVideoGridList(subCateCode);

        RequestManager.getInstance().onEvent("6_templet_videlist", "100001", subCateCode + "",
                null, null, null, null);

        return root;
    }


    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            RequestManager.getInstance().onEvent("6_templet_videolist", "100001", subCateCode + "",
                    null, null, null, null);
        }
    }

    /**
     * Request video list data
     *
     * @param subCateCode 0 if get all video data
     */
    public void requestVideoGridList(final int subCateCode) {
        //reset current page number
//        mCurrSize = 0;
        //display loading view on the riht view
//        mGridListViewNew.displayGridListLoadingView();
        //cancel network request
        if (mObservable != null) {
            mObservable.unsubscribeOn(Schedulers.io());
        }
        //request network to get grid list items of certain tab
        mObservable = NetworkApi.getVideoList(mIsPgc?UrlWrapper.getPgcListForMenu(subCateCode, 15, 1):UrlWrapper.getVideoListForMenu(subCateCode, 15, 1), new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                LibDeprecatedLogger.d("requestVideoGridList() onNext()");
                if (value == null || value.extend == null) {
                    return;
                }

                templetItemAdapter.setmListData(value.data.result);
                templetItemAdapter.notifyDataSetChanged();
                //update video list view if subCateCode equals the selected tab's subCateCode
//                if ((mSubCateId == 0 && (isPgc ? value.extend.subClassifyId == -1 : value.extend.id == -1))
//                        || mSubCateId == (isPgc ? value.extend.subClassifyId : value.extend.id)) {
//                    if (value.data != null && value.data.result != null && value.data.result.size() > 0) {
//                        mGridListViewNew.updateGridListView(value.data);
//                        mCurrSize = value.data.result.size();
//                        isRequestData = isRequestData(value.data.count);
//                    } else {
//                        if (mGridListViewNew != null) {
//                            mGridListViewNew.displayGridListErrorView(mContext.getResources().getString(R.string.data_empty));
//                        }
//                    }
//                    RequestManager.getInstance().onGridListNewSubViewExposureEvent(mSubCateId);
//                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("requestVideoGridList() onError(), e == " + e);
//                if (mGridListViewNew != null) {
//                    mGridListViewNew.displayGridListErrorView(mContext.getResources().getString(R.string.data_err));
//                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestVideoGridList() onComplete()");
            }
        });
    }

    class TempletOnScrollListener extends RecyclerView.OnScrollListener{

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
            if (templetItemAdapter.mLastPlayingSelected != -1) {
                if (customLinearRecyclerView.findViewHolderForAdapterPosition(templetItemAdapter.mLastPlayingSelected) != null &&
                        customLinearRecyclerView.findViewHolderForAdapterPosition(templetItemAdapter.mLastPlayingSelected).itemView != null) {
                    templetItemAdapter.setNotPlayingUI(((TempletItemAdapter.TempletItemHolder) customLinearRecyclerView.findViewHolderForAdapterPosition(templetItemAdapter.mLastPlayingSelected)));
                }
            }

            if (templetItemAdapter.mPlayingSelected != -1) {
                if (customLinearRecyclerView.findViewHolderForAdapterPosition(templetItemAdapter.mPlayingSelected) != null &&
                        customLinearRecyclerView.findViewHolderForAdapterPosition(templetItemAdapter.mPlayingSelected).itemView != null) {
                    templetItemAdapter.setPlayingUI(((TempletItemAdapter.TempletItemHolder) customLinearRecyclerView.findViewHolderForAdapterPosition(templetItemAdapter.mPlayingSelected)));
                }
            }
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            LibDeprecatedLogger.d(newState+"," + mIsNeedScroll);
            if (mShouldScroll) {
                mShouldScroll = false;
                smoothMoveToPosition(customLinearRecyclerView, mToPosition);
            }
            if (mIsNeedScroll) {
                templetItemAdapter.setSelected(mScrollToPosition);
                if (newState == RecyclerView.SCROLL_STATE_IDLE){
                    customLinearRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
                    LibDeprecatedLogger.d("scrolltoposition end"+customLinearLayoutManager.findLastVisibleItemPosition());
                    if (customLinearRecyclerView.getLayoutManager().findViewByPosition(mScrollToPosition) != null) {
                        customLinearRecyclerView.getLayoutManager().findViewByPosition(mScrollToPosition).requestFocus();
                        if (!Util.getSystemModel().equals("MiTV")) {
                            focusBorderView.setFocusView(customLinearRecyclerView.getLayoutManager().findViewByPosition(mScrollToPosition));
                        }
                    }
                }
            } else {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    LibDeprecatedLogger.d(number + "");
                    if (customLinearRecyclerView.getFocusedChild() != null) {
                        customLinearRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
                        if (!Util.getSystemModel().equals("MiTV")) {
                            focusBorderView.setFocusView(customLinearRecyclerView.getChildViewHolder(customLinearRecyclerView.getFocusedChild()).itemView);
                        }
                    }
                }
            }
        }
    }

    public void setSelected() {
        templetItemAdapter.setSelected();
    }

    public void clearSelected() {
        templetItemAdapter.clearSelected();
    }

    public void scrollToTop() {
//        customLinearRecyclerView.scrollToPosition(0);
        customLinearLayoutManager.scrollToPositionWithOffset(0,0);
    }

    public void scrollToPlayingPosition() {//滚动到指定位置
        mIsNeedScroll = false;
        if (templetItemAdapter.getPlayingItemPosition() != -1) { //当前页面有正在播放的item
            if (templetItemAdapter.getPlayingItemPosition() > 1) {
                //滚到中间
                if (templetItemAdapter.getPlayingItemPosition() > templetItemAdapter.getItemCount()-4) {
                    smoothMoveToPosition(customLinearRecyclerView, templetItemAdapter.getItemCount() - 1);
                } else {
                    smoothMoveToPosition(customLinearRecyclerView, templetItemAdapter.getPlayingItemPosition() - 2);

                }
            } else {
                smoothMoveToPosition(customLinearRecyclerView, templetItemAdapter.getPlayingItemPosition());

            }
        }
        mScrollToPosition = templetItemAdapter.getPlayingItemPosition();
    }

    /**
     * 滑动到指定位置
     *
     * @param mRecyclerView
     * @param position
     */
    private void smoothMoveToPosition(RecyclerView mRecyclerView, final int position) {
        // 第一个可见位置
        int firstItem = mRecyclerView.getChildLayoutPosition(mRecyclerView.getChildAt(0));
        // 最后一个可见位置
        int lastItem = mRecyclerView.getChildLayoutPosition(mRecyclerView.getChildAt(mRecyclerView.getChildCount() - 1));

        if (position < firstItem) {
            // 如果跳转位置在第一个可见位置之前，就smoothScrollToPosition可以直接跳转
            mRecyclerView.smoothScrollToPosition(position);
        } else if (position <= lastItem) {
            // 跳转位置在第一个可见项之后，最后一个可见项之前
            // smoothScrollToPosition根本不会动，此时调用smoothScrollBy来滑动到指定位置
            int movePosition = position - firstItem;
            if (movePosition >= 0 && movePosition < mRecyclerView.getChildCount()) {
                int top = mRecyclerView.getChildAt(movePosition).getTop();
                mRecyclerView.smoothScrollBy(0, top);
            }
        } else {
            // 如果要跳转的位置在最后可见项之后，则先调用smoothScrollToPosition将要跳转的位置滚动到可见位置
            // 再通过onScrollStateChanged控制再次调用smoothMoveToPosition，执行上一个判断中的方法
            mRecyclerView.smoothScrollToPosition(position);
            mToPosition = position;
            mShouldScroll = true;
        }
    }

    public void scrollTo(boolean isNeedScroll, int scrollToPosition) {
        mIsNeedScroll = isNeedScroll?true:false;
        mScrollToPosition= scrollToPosition > 0 ? customLinearRecyclerView.getAdapter().getItemCount() - 1 : 0;
        if (isNeedScroll) {
            LibDeprecatedLogger.d("scrolltoposition start");
            customLinearRecyclerView.smoothScrollToPosition(mScrollToPosition);
            customLinearRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);

            //需要滚到最底 且 当前就在最底
            if (mScrollToPosition >0 && customLinearLayoutManager.findLastVisibleItemPosition() == templetItemAdapter.getItemCount() -1){
                customLinearRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);

            }
            //需要滚动到最顶 且 当前就在最顶
            if (mScrollToPosition == 0 && customLinearLayoutManager.findFirstVisibleItemPosition() == 0){
                customLinearRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            }
        } else {
        }
    }

    public boolean requestNextPosition(int position) {
        if (position > templetItemAdapter.getItemCount() - 1) {
            return false;
        } else {
            templetItemAdapter.requestNextFocus(position);
            return true;
        }
    }

    public void setPlayingPage(int page) {
        templetItemAdapter.setmPlayingPage(page);
    }

    //设置正在播放的item的位置，小于0，表示没有正在播放的item，顺便清除last的值
    public void setPlayingPosition(int pos) {
            LibDeprecatedLogger.d(pos + "," + templetItemAdapter.mPlayingSelected + "," + templetItemAdapter.mLastPlayingSelected);
        if (pos < 0) {

//            if (customLinearRecyclerView.findViewHolderForAdapterPosition(templetItemAdapter.mPlayingSelected) != null) {
//                templetItemAdapter.setNotPlayingUI((TempletItemAdapter.TempletItemHolder) customLinearRecyclerView.findViewHolderForAdapterPosition(templetItemAdapter.mPlayingSelected));
//            } else {
                templetItemAdapter.notifyDataSetChanged();
//            }

            templetItemAdapter.mPlayingSelected = pos;
            templetItemAdapter.mLastPlayingSelected = pos;
        } else {
            templetItemAdapter.mLastPlayingSelected = templetItemAdapter.mLastPlayingSelected;
            templetItemAdapter.mLastPlayingSelected = pos;
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        setOnkeyChange(null);
        templetItemAdapter.releaseAll();
    }
}
