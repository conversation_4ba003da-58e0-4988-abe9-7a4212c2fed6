package com.sohuott.tv.vod.videodetail.activity.repository

import GsonConverter
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.model.AlbumChaseStatus
import com.sohuott.tv.vod.lib.model.EduCollectionAndPlayHistoryResult
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.coroutineScope
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

/**
 * 收藏
 */
class CollectRepository {

    /**
     * 教育收藏
     */
    suspend fun edCollect(aid:Int):Deferred<EduCollectionAndPlayHistoryResult> {
        return coroutineScope {
            Get<EduCollectionAndPlayHistoryResult>("${RetrofitApi.get().retrofitHost.baseHost}user/isCollected.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                            listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                            )
                    )
                }
                converter = GsonConverter()
                addQuery("passport", UserLoginHelper.getInstants().getLoginPassport())
                addQuery("albumId", aid)
                addQuery("categoryId", 21)
            }
        }
    }


    suspend fun collect(aid:Int):Deferred<AlbumChaseStatus> {
        return coroutineScope {
            Get<AlbumChaseStatus>("${RetrofitApi.get().retrofitHost.baseHost}user/isChasePlay.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                            listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                            )
                    )
                }
                converter = GsonConverter()
                addQuery("passport", UserLoginHelper.getInstants().getLoginPassport())
                addQuery("aid", aid)
                addQuery("token", UserLoginHelper.getInstants().getLoginToken())
            }
        }
    }

}
