package com.sohuott.tv.vod.activity;

import static com.sohuott.tv.vod.widget.TopBar.INDEX_SEARCH_BUTTON;

import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.LabelGridListAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.LabelGridListPresenterImpl;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.CustomRecyclerViewNew;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.LabelGridListView;
import com.sohuott.tv.vod.widget.TopBar;

/**
 * Created by wenjingbian on 2017/9/19.
 */

public class LabelGridListActivity extends BaseActivity implements LabelGridListView,
        TopBar.TopBarFocusController, ViewTreeObserver.OnGlobalFocusChangeListener {

    private static final int SPAN_COUNT = 6;

    private LinearLayout mErrorView;
    private LoadingView mLoadingView;
    private RelativeLayout layout_label_grid_list;
    private TopBar top_bar;
    private TextView tv_label_title, tv_label_sum_line, tv_label_curr_line;
    private CustomRecyclerViewNew rv_label_video;
    private FocusBorderView mFocusView;

    private LabelGridListPresenterImpl mPresenterImpl;
    private LabelGridListAdapter mAdapter;
    private CustomGridLayoutManager mLayoutManager;

    private String mFilterStr;
    private String mFilterValue;
    private int mCateCode;
    private int mLastFocusedViewPos;
    private int mColumnNum; //focused item's column number when single and short clicked
    private int mCurrLineNum; //currently focused line number
    private boolean isLongPressed;
    private boolean isEnableScrollListener;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_label_grid_list);

        mCateCode = getIntent().getIntExtra(ParamConstant.PARAM_VIDEO_CATE_CODE, 0);
        mFilterStr = getIntent().getStringExtra(ParamConstant.PARAM_VIDEO_FILTER);
        mFilterValue = getIntent().getStringExtra(ParamConstant.PARAM_VIDEO_FILTER_VALUE);

        initView();
        initData();

        RequestManager.getInstance().onLabelGridListExposureEvent();
        setPageName("6_label_grid_list");
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateTopBar();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //release objects
        top_bar = null;
        if (mAdapter != null) {
            mAdapter.releaseAll();
            mAdapter = null;
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);

        mCateCode = intent.getIntExtra(ParamConstant.PARAM_VIDEO_CATE_CODE, 0);
        mFilterStr = intent.getStringExtra(ParamConstant.PARAM_VIDEO_FILTER);
        mFilterValue = intent.getStringExtra(ParamConstant.PARAM_VIDEO_FILTER_VALUE);

        initView();
        initData();

        RequestManager.getInstance().onLabelGridListExposureEvent();
    }

    @Override
    public void updateLabelGridListView(VideoGridListBean.DataEntity data) {
        if (mAdapter == null) {
            return;
        }

        mLoadingView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.GONE);
        layout_label_grid_list.setVisibility(View.VISIBLE);

        if (data != null && data.result != null && data.result.size() > 0) {
            mAdapter.setDataSource(data.result);
            mAdapter.notifyDataSetChanged();
            tv_label_sum_line.setText(data.count % SPAN_COUNT == 0 ? data.count / SPAN_COUNT + "行" : (data.count / SPAN_COUNT + 1) + "行");
            isEnableScrollListener = true;
        } else {
            displayErrorView();
        }

        RequestManager.getInstance().onTopBarExposureEvent();
    }

    @Override
    public void displayErrorView() {
        mLoadingView.setVisibility(View.GONE);
        layout_label_grid_list.setVisibility(View.GONE);
        mErrorView.setVisibility(View.VISIBLE);
    }

    @Override
    public void addVideoItems(VideoGridListBean.DataEntity data) {
        if (mAdapter == null || data == null || data.count <= 0) {
            return;
        }

        isEnableScrollListener = true;
        if (data.result != null && data.result.size() > 0) {
            if (rv_label_video != null && mAdapter != null) {
                mAdapter.addItems(data.result);
            }
        }
    }

    @Override
    public void addVideoItemsError() {
        isEnableScrollListener = true;
        ToastUtils.showToast(this, "获取新数据失败，请稍后重试！");
    }

    @Override
    public boolean onFocusDown() {
        if (rv_label_video == null) {
            return false;
        }

        RecyclerView.ViewHolder viewHolder = rv_label_video.findViewHolderForAdapterPosition(0);
        if (viewHolder != null && viewHolder.itemView != null) {
            viewHolder.itemView.requestFocus();
            setFocusability(false);
            mColumnNum = 0;
            return true;
        }
        return false;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if ((keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT
                || keyCode == KeyEvent.KEYCODE_DPAD_UP) && event.getRepeatCount() > 1
                && rv_label_video.indexOfChild(getCurrentFocus()) >= 0 && !isLongPressed) {
            isLongPressed = true;
            rv_label_video.isLongPressed(true);
            setFocusability(false);
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            this.finish();
            return true;
        } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_UP) {
            if (isLongPressed) {
                rv_label_video.isLongPressed(false);
                rv_label_video.stopScroll();
            }
        } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
            if (isLongPressed) {
                rv_label_video.isLongPressed(false);
            }
        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    public void onGlobalFocusChanged(View oldFocus, View newFocus) {
        if (newFocus == null) {
            rv_label_video.smoothScrollToPosition(mLastFocusedViewPos + SPAN_COUNT);
            RecyclerView.ViewHolder viewHolder = rv_label_video.findViewHolderForAdapterPosition(mLastFocusedViewPos + SPAN_COUNT);
            if (viewHolder != null && viewHolder.itemView != null) {
                viewHolder.itemView.requestFocus();
            } else {
                viewHolder = rv_label_video.findViewHolderForAdapterPosition(mLastFocusedViewPos);
                if (viewHolder != null && viewHolder.itemView != null) {
                    viewHolder.itemView.requestFocus();
                }
            }
        } else {
            if (newFocus.getParent() instanceof RecyclerView) {
                mLastFocusedViewPos = rv_label_video.getChildAdapterPosition(newFocus);
            }
        }
    }

    public void setCurrLine(boolean isVisible, int focusedPos) {
        //Hide current line number in touch mode
        if (Util.isSupportTouchVersion(this)) {
            tv_label_curr_line.setVisibility(View.GONE);
            return;
        }

        if (!isVisible) {
            tv_label_curr_line.setText("");
            tv_label_curr_line.setVisibility(View.INVISIBLE);
            mCurrLineNum = 0;
        } else {
            if (focusedPos >= 0) {
                String txt = String.valueOf(focusedPos / SPAN_COUNT + 1) + "/";
                SpannableStringBuilder ssb = new SpannableStringBuilder(txt);
                ssb.setSpan(new ForegroundColorSpan(getResources().getColor(
                        R.color.new_filter_item_text_color_normal)), txt.length() - 1,
                        txt.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                tv_label_curr_line.setText(ssb);
                tv_label_curr_line.setVisibility(View.VISIBLE);
                mCurrLineNum = focusedPos / SPAN_COUNT;
            }
        }
    }

    public void focusOnTopBar() {
        if (top_bar != null) {
            setFocusability(true);
            top_bar.focusChildView(INDEX_SEARCH_BUTTON);
        }
    }

    public void setFocusability(boolean isFocusable) {
        if (isFocusable) {
            top_bar.setDescendantFocusability(ViewGroup.FOCUS_BEFORE_DESCENDANTS);
        } else {
            top_bar.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
        }
    }

    /**
     * Check equality of mColumnNum and current column number
     *
     * @param position current focused position
     * @return true if mColumnNum equals current column number, false if mColumnNum doesn't equal current column number
     */
    public boolean checkColumnNum(int position) {
        if (mAdapter.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN && position < mAdapter.getItemCount() - 1) {
            int columnNum = position % SPAN_COUNT;
            if (columnNum != mColumnNum) {
                int targetPos = position / SPAN_COUNT * SPAN_COUNT + mColumnNum;
                RecyclerView.ViewHolder viewHolder = rv_label_video.findViewHolderForAdapterPosition(targetPos);
                if (viewHolder != null && viewHolder.itemView != null) {
                    viewHolder.itemView.requestFocus();
                }
                return false;
            }
        }
        return true;
    }

    public void setColumnNum(int columnNum) {
        this.mColumnNum = columnNum;
    }

    private void initView() {
        mErrorView = (LinearLayout) findViewById(R.id.err_view);
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mFocusView = (FocusBorderView) findViewById(R.id.focus_border_view);
        layout_label_grid_list = (RelativeLayout) findViewById(R.id.layout_label_grid_list);
        top_bar = (TopBar) findViewById(R.id.top_bar);
        tv_label_title = (TextView) findViewById(R.id.tv_label_title);
        tv_label_title.setText(TextUtils.isEmpty(mFilterValue) ? "" : mFilterValue);
        tv_label_curr_line = (TextView) findViewById(R.id.tv_label_curr_line);
        tv_label_sum_line = (TextView) findViewById(R.id.tv_label_sum_line);
        rv_label_video = (CustomRecyclerViewNew) findViewById(R.id.rv_label_video);
        mLayoutManager = new CustomGridLayoutManager(this, SPAN_COUNT);
        rv_label_video.setLayoutManager(mLayoutManager);
        rv_label_video.setOnScrollListener(new LabelVideoOnScrollListener());
        rv_label_video.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y15);
            }
        });
        rv_label_video.setPadding(0, getResources().getDimensionPixelSize(R.dimen.y20),
                0, getResources().getDimensionPixelSize(R.dimen.y6));

        top_bar.setTopBarFocusListener(this);
        layout_label_grid_list.getViewTreeObserver().addOnGlobalFocusChangeListener(this);
    }

    private void initData() {
        mAdapter = new LabelGridListAdapter(this, rv_label_video);
        mAdapter.setFocusView(mFocusView);
        rv_label_video.setAdapter(mAdapter);

        mPresenterImpl = new LabelGridListPresenterImpl(mCateCode, mFilterStr);
        mPresenterImpl.setView(this);
        mPresenterImpl.requestVideoData(false);
    }

    /**
     * Update items on the topBar
     */
    private void updateTopBar() {
        top_bar.updateUserView();
        top_bar.updateVipImage();
        top_bar.updateVipText();
        top_bar.updateMessageView();
    }

    private class LabelVideoOnScrollListener extends RecyclerView.OnScrollListener {
        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
            if (!isEnableScrollListener) {
                return;
            }

            int lastVisibleItemPosition = mLayoutManager.findLastVisibleItemPosition() + 1;
            int modelsCount = mAdapter.getItemCount();

            if (lastVisibleItemPosition + 19 >= modelsCount) {
                isEnableScrollListener = false;
                mPresenterImpl.requestMoreVideoData(isLongPressed);
            }
            if (rv_label_video.getFocusedChild() == null) {
                return;
            }
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                return;
            }
            if (recyclerView == null) {
                return;
            }
            if (rv_label_video == null || mFocusView == null) {
                return;
            }

            View focusedView = rv_label_video.getFocusedChild();
            //if currently focused item is null, request focus for previously focused item.
            if (isLongPressed && focusedView == null) {
                int focusPos = mColumnNum + mCurrLineNum * SPAN_COUNT;
                RecyclerView.ViewHolder viewHolder = rv_label_video.findViewHolderForAdapterPosition(focusPos);
                if (viewHolder != null && viewHolder.itemView != null) {
                    viewHolder.itemView.requestFocus();
                }
                if (isLongPressed) {
                    isLongPressed = false;
                    rv_label_video.isLongPressed(false);
                    setFocusability(true);
                }
                return;
            }

            if (mAdapter != null) {
                mAdapter.loadImage();
            }

            //reset isLongPressed
            if (isLongPressed) {
                isLongPressed = false;
                rv_label_video.isLongPressed(false);
                setFocusability(true);
            }

            int firstPos = mLayoutManager.findFirstCompletelyVisibleItemPosition();
            int lastPos = mLayoutManager.findLastCompletelyVisibleItemPosition();
            int currFocused = focusedView != null ? rv_label_video.getChildAdapterPosition(focusedView) : -1;
            //if focused item is invisible completely, request focus on the certain item on the certain column of the first line.
            if (currFocused < firstPos || currFocused > lastPos) {
                firstPos = mLayoutManager.findFirstCompletelyVisibleItemPosition();
                RecyclerView.ViewHolder viewHolder = rv_label_video.findViewHolderForAdapterPosition(firstPos + mColumnNum);
                if (viewHolder != null && viewHolder.itemView != null) {
                    viewHolder.itemView.requestFocus();
                }
            } else {
                if (focusedView != null) {
                    RecyclerView.ViewHolder viewHolder = rv_label_video.getChildViewHolder(focusedView);
                    if (checkColumnNum(rv_label_video.getChildAdapterPosition(rv_label_video.getFocusedChild()))
                            && viewHolder != null && viewHolder.itemView != null) {
                        mFocusView.setFocusView(viewHolder.itemView);
                        FocusUtil.setFocusAnimator(viewHolder.itemView, mFocusView, FocusUtil.HOME_SCALE, 100);
                    }
                    setCurrLine(true, viewHolder.getAdapterPosition());
                }
            }
        }
    }
}
