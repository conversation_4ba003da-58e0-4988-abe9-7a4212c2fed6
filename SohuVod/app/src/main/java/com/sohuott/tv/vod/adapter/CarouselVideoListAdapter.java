package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.lib.model.CarouselVideo;
import com.sohuott.tv.vod.utils.ActivityLauncher;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/9/7.
 */
public class CarouselVideoListAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {


    private int mCurrentPosition;
    private long mCurrentTime;
    private boolean mHasPlayingVideo;
    private boolean mIsFirstRequestFocus = true;

    private Context mContext;

    private List<CarouselVideo.DataEntity> mList;

    public CarouselVideoListAdapter(Context context, List<CarouselVideo.DataEntity> list) {
        mContext = context;
        mList = list;
    }

    @Override
    public int getItemCount() {
        return (null != mList && mList.size() > 0) ? mList.size() : 0;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext.getApplicationContext()).inflate(R.layout.carousel_video, parent, false);
        CarouselVideoViewHolder vh = new CarouselVideoViewHolder(view);
        return vh;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        CarouselVideoViewHolder vh = (CarouselVideoViewHolder) holder;

        if (null != mList && mList.size() > 0) {
            CarouselVideo.DataEntity entity = mList.get(position);

            String name = entity.getName();
            long startTime = entity.getStartTime();
            long endTime = entity.getEndTime();

            if (TextUtils.isEmpty(name) || TextUtils.isEmpty(name.trim())) {
                name = "";
            } else {
                name = name.trim();
            }
            vh.videoName.setText(name);
            vh.startTime.setText("");

            if (endTime < mCurrentTime || startTime > mCurrentTime) {//startTime < mCurrentTime &&
                boolean beforeTime = endTime < mCurrentTime;
                int tipTextId = R.string.txt_activity_carousel_player_video_tip;
                if (!beforeTime) {
                    tipTextId = R.string.txt_activity_carousel_player_video_tip3;
                }
                vh.tip.setText(mContext.getApplicationContext().getResources().getString(tipTextId));
                vh.tip.setVisibility(View.GONE);
                if (mIsFirstRequestFocus && !mHasPlayingVideo && position == 0) {
                    mIsFirstRequestFocus = false;
                    vh.rootView.requestFocus();
                    vh.tip.setVisibility(View.VISIBLE);
                }
            } else if (startTime <= mCurrentTime && endTime >= mCurrentTime) {
                vh.tip.setText(mContext.getApplicationContext().getResources().getString(R.string.txt_activity_carousel_player_video_tip2));
                vh.tip.setVisibility(View.VISIBLE);

                if (mHasPlayingVideo) {
                    mHasPlayingVideo = false;
                    mIsFirstRequestFocus = false;
                    vh.rootView.requestFocus();
                }
            }
        }
    }

    public void setVideoList(List<CarouselVideo.DataEntity> list) {
        mList = list;
        mHasPlayingVideo = false;
        mIsFirstRequestFocus = true;
        setCurrentPosition();
        notifyDataSetChanged();
    }

    public void setCurrentTime(long currentTime) {
        mCurrentTime = currentTime;
    }

    public int getCurrentPosition() {
        return mCurrentPosition;
    }

    public void setCurrentPosition() {
        if (null != mList && mList.size() > 0) {
            for (int i = 0; i < mList.size(); i++) {
                CarouselVideo.DataEntity entity = mList.get(i);

                long endTime = entity.getEndTime();
                long startTime = entity.getStartTime();

                if (startTime <= mCurrentTime && mCurrentTime <= endTime) {
                    mCurrentPosition = i;
                    mHasPlayingVideo = true;
                    break;
                }
            }
        }
    }

    OnCarouselVideoItemClickListener onCarouselVideoItemClickListener;

    public void setOnCarouselVideoItemClickListener(OnCarouselVideoItemClickListener onCarouselVideoItemClickListener) {
        this.onCarouselVideoItemClickListener = onCarouselVideoItemClickListener;
    }

    private class CarouselVideoViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener, View.OnFocusChangeListener {

        private View rootView;

        private TextView videoName;
        private TextView startTime;
        private TextView tip;

        public CarouselVideoViewHolder(View itemView) {
            super(itemView);

            rootView = itemView;
            initUI();
            rootView.setOnClickListener(this);
            rootView.setOnFocusChangeListener(this);

            rootView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    int position = getAdapterPosition();

                    if (event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                            if (position == 0) {
                                rootView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.out_to_up));
                                return true;
                            }
                        } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {

                            if (null != mList && mList.size() > 0 && position == (mList.size() - 1)) {
                                rootView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.out_to_up));
                                return true;
                            }
                        }else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT){
                            LibDeprecatedLogger.d("consume right key");
                            return true;
                        }
                    }
                    return false;
                }
            });
        }

        @Override
        public void onClick(View view) {
            int position = getAdapterPosition();
            if (null != mList && mList.size() > 0 && position < mList.size() && position >= 0) {
                CarouselVideo.DataEntity entity = mList.get(position);
                int aid = entity.getAlbumId();
                int vid = entity.getVideoId();
                int cid = entity.getLoopChannelId();
                int videoType = entity.getDataType();
                int videoOrder = entity.getVideoOrder();

                if (videoType == Constant.DATA_TYPE_PGC) {
                    aid = vid;
                }
                RequestManager.getInstance().onEvent("5_carousel_video_list_show", "5_carousel_video_item_click",
                        "carouselPlayer", String.valueOf(cid), String.valueOf(aid), null, null);
                ActivityLauncher.startVideoDetailFromCarousel(mContext, Constant.PAGE_CAROUSEL, aid, videoType, vid);

                if (onCarouselVideoItemClickListener != null) {
                    onCarouselVideoItemClickListener.onCarouselVideoItemClick(position);
                }
            }
        }

        @Override
        public void onFocusChange(View view, boolean hasFocus) {
            if (view == rootView) {
                if (hasFocus || tip.getText().toString().equals(mContext.getApplicationContext().getResources().getString(R.string.txt_activity_carousel_player_video_tip2))) {
                    tip.setVisibility(View.VISIBLE);
                } else {
                    tip.setVisibility(View.GONE);
                }
                videoName.setSelected(hasFocus);
                videoName.setEllipsize(hasFocus ? TextUtils.TruncateAt.MARQUEE : TextUtils.TruncateAt.END);
            }
        }

        private void initUI() {
            videoName = (TextView) rootView.findViewById(R.id.video_name);
            startTime = (TextView) rootView.findViewById(R.id.start_time);
            tip = (TextView) rootView.findViewById(R.id.tip);

            if (Util.getDeviceName().equalsIgnoreCase("rk3368-box")
                    || Util.getDeviceName().equalsIgnoreCase("db1016")
                    || Util.getDeviceName().equalsIgnoreCase("inphic_i9s1")) { // 芒果嗨Q H7 三代, 大麦 DB1016, 英菲克 i9
                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                        mContext.getApplicationContext().getResources().getDimensionPixelSize(R.dimen.x510),
                        mContext.getApplicationContext().getResources().getDimensionPixelSize(R.dimen.y162));

                rootView.setLayoutParams(params);
            }
        }

    }

    public interface OnCarouselVideoItemClickListener {
        void onCarouselVideoItemClick(int position);
    }

}
