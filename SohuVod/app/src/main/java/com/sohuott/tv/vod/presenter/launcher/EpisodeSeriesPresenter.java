package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.leanback.widget.Presenter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;


public class EpisodeSeriesPresenter extends Presenter {
    private Context mContext;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.fragment_episode_vrs_variety_item, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final Presenter.ViewHolder viewHolder, final Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        vh.view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
                if (hasFocus) {
                    vh.mFocusRoot.setVisibility(View.VISIBLE);
                    vh.mTVFocusEpisode.setVisibility(View.VISIBLE);
                    vh.mEpisodeBg.setVisibility(View.VISIBLE);
                    vh.mNameBg.setVisibility(View.INVISIBLE);
                    vh.mTvTypeTwoName.setVisibility(View.GONE);
                    vh.mFocusPlay.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.showWaveAnimation();
                } else {
                    vh.mFocusRoot.setVisibility(View.GONE);
                    vh.mTVFocusEpisode.setVisibility(View.GONE);
                    vh.mEpisodeBg.setVisibility(View.GONE);
                    vh.mNameBg.setVisibility(View.VISIBLE);
                    vh.mTvTypeTwoName.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.setVisibility(View.GONE);
                    vh.mFocusPlay.cancelWaveAnimation();
                }
            }
        });
        if (item instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean) {
            ContentGroup.DataBean.ContentsBean.AlbumListBean albumBean = (ContentGroup.DataBean.ContentsBean.AlbumListBean) item;
            Glide.with(mContext)
                    .load(albumBean.albumExtendsPic_640_360)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeTwoPoster);
            vh.mTvTypeTwoName.setText(albumBean.tvName);

            vh.mTVFocusEpisode.setText(Util.getHintTV(item));
            vh.mTvFocusName.setText(albumBean.tvName);
            vh.mTvFocusDesc.setText(albumBean.tvComment);

            VideoDetailRequestManager.seriesExposure(String.valueOf(albumBean.id));

            if (albumBean.channelType == Constant.TYPE_VIP) {
                vh.mFocusRoot.setBackgroundResource(R.drawable.item_type_one_vip_focus_selector);
                vh.mFocusView.setBackgroundResource(R.drawable.bg_vip_focus_selector);
            } else {
                vh.mIvTypeTwoPoster.setCornerTypeWithType(albumBean.tvIsFee,
                        albumBean.tvIsEarly,
                        albumBean.useTicket,
                        albumBean.paySeparate,
                        albumBean.cornerType);
            }

            vh.view.setOnClickListener(v -> {
                VideoDetailRequestManager.tabClick(10284, String.valueOf(albumBean.id), "0",false, -1);
                ActivityLauncher.startVideoDetailActivity(mContext, albumBean.id, 0, Constant.PAGE_DETAIL);
            });
        }
    }


    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final CornerTagImageView mIvTypeTwoPoster;
        private final TextView mTvTypeTwoName, mTvFocusName, mTvFocusDesc, mTVFocusEpisode;
        private LinearLayout mFocusRoot;
        private RippleDiffuse mFocusPlay;
        private View mFocusView, mEpisodeBg, mNameBg;

        public ViewHolder(View view) {
            super(view);
            mIvTypeTwoPoster = (CornerTagImageView) view.findViewById(R.id.iv_type_two_poster);
            mTvTypeTwoName = (TextView) view.findViewById(R.id.tv_type_two_name);
            mFocusRoot = (LinearLayout) view.findViewById(R.id.type_two_focus_root);
            mTvFocusName = (TextView) view.findViewById(R.id.type_two_focus_name);
            mTvFocusDesc = (TextView) view.findViewById(R.id.type_two_focus_desc);
            mTVFocusEpisode = (TextView) view.findViewById(R.id.type_two_focus_episode);
            mFocusPlay = (RippleDiffuse) view.findViewById(R.id.type_two_focus_play);
            mFocusView = view.findViewById(R.id.type_two_focus);
            mEpisodeBg = view.findViewById(R.id.focus_episode_bg);
            mNameBg = view.findViewById(R.id.name_bg);
        }
    }
}

