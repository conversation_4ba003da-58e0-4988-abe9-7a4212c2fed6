package com.sohuott.tv.vod.presenter.launcher

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.leanback.widget.Presenter
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleEventObserver
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.sh.ott.video.view.ShVideoView
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.activity.launcher.LauncherPlayerManager
import com.sohuott.tv.vod.databinding.ItemTypeRecommendLayoutBigBinding
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.ContentGroup.DataBean.ContentsBean.AlbumListBean
import com.sohuott.tv.vod.lib.utils.Util
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.Serializable
import java.lang.ref.WeakReference

class TypeRecommendContentPresenterKt(private val lifecycle: Lifecycle? = null, private val scope: LifecycleCoroutineScope? = null): Presenter() {
    private var context: WeakReference<Context>? = null
    private var job: Job? = null
    private val launcherPlayerManager = LauncherPlayerManager()
    private var onTypeZeoPlayListener: TypeZeroContentPresenterKt.OnTypeZeroPlayListener? = null
    private var albumListBean: AlbumListBean? = null

    private val delayStart = 1000L //默认上焦点一秒钟，开始播放

    fun setOnTypeZeoPlayListener(onTypeZeoPlayListener: TypeZeroContentPresenterKt.OnTypeZeroPlayListener) {
        this.onTypeZeoPlayListener = onTypeZeoPlayListener
    }


    val observer = LifecycleEventObserver { source, event ->
        when(event) {
            androidx.lifecycle.Lifecycle.Event.ON_PAUSE -> {
                cancelDelay()
            }

            else -> {}
        }
    }

    init {
        lifecycle?.addObserver(observer)
    }

    private fun cancelDelay() = job?.cancel()

    override fun onCreateViewHolder(parent: ViewGroup?): ViewHolder {
        context = WeakReference(parent?.context)
        val binding = ItemTypeRecommendLayoutBigBinding.inflate(LayoutInflater.from(parent?.context), parent, false)
        return TypeRecommendViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder?, item: Any?) {
        val vh = viewHolder as TypeRecommendViewHolder

        if (item is AlbumListBean) {
            vh.binding.root.setOnFocusChangeListener { v, hasFocus ->
                if (hasFocus) {
                    vh.binding.playingIcon.visible()
                    vh.binding.playingIcon.showWaveAnimation()
                    //判断是否开始准备播放视频
                    if (item.goodTrailerId != 0) {
                        LibDeprecatedLogger.d("${item.tvName} 有片花 准备开始播放")
                        albumListBean = item
                        startDelay(vh)
                        onTypeZeoPlayListener?.onTypeZeroStartPlay(vh, launcherPlayerManager)
                    } else {
                        LibDeprecatedLogger.d("${item.tvName} 没有片花")
                    }
                } else {
                    vh.binding.posterVer.gone()
                    vh.binding.playingIcon.gone()
                    vh.binding.playingIcon.cancelWaveAnimation()
                    vh.binding.nameBg.visible()
                    vh.binding.rootPlayer.removeAllViews()
                    vh.binding.rootPlayer.gone()
                    vh.binding.groupPlayComplete.gone()
                    launcherPlayerManager.releasePlayer()
                    cancelDelay()
                }
            }

            context?.get()?.let {
                Glide.with(it)
                    .load(item.albumExtendsPic_640_360)
                    .transform(RoundedCorners(it.resources.getDimensionPixelOffset(R.dimen.x10)))
                    .thumbnail(0.5f)
                    .into(vh.binding.ivTypeTwoPoster)

                Glide.with(it)
                    .load(item.tvVerPic)
                    .transform(RoundedCorners(it.resources.getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.binding.posterVer)
            }
            vh.binding.tvTypeTwoEpisode.text = Util.getHintTV(item)
            vh.binding.tvTypeRecommendName.text = item.tvName
            vh.binding.tvTypeRecommendDesc.text = item.tvComment

            vh.binding.ivTypeTwoPoster.setCornerTypeWithType(item.tvIsFee, item.tvIsEarly, item.useTicket, item.paySeparate, item.cornerType)

            when(item.cateCode){
                100 -> vh.binding.imgTypeRecommendCorner.setBackgroundResource(R.drawable.item_corner_100)
                101 -> vh.binding.imgTypeRecommendCorner.setBackgroundResource(R.drawable.item_corner_101)
                106 -> vh.binding.imgTypeRecommendCorner.setBackgroundResource(R.drawable.item_corner_106)
                107 -> vh.binding.imgTypeRecommendCorner.setBackgroundResource(R.drawable.item_corner_107)
                115 -> vh.binding.imgTypeRecommendCorner.setBackgroundResource(R.drawable.item_corner_115)
                119 -> vh.binding.imgTypeRecommendCorner.setBackgroundResource(R.drawable.item_corner_119)
            }

            RequestManager.getInstance().onAllEvent(EventInfo(10146, "imp"), item.pathInfo, item.objectInfo, item.memoInfo)
        }
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder?) {
        val vh = viewHolder as TypeRecommendViewHolder
        context?.get()?.let {
            Glide.with(it).clear(vh.binding.ivTypeTwoPoster)
        }


    }

    public fun startDelay(vh: TypeRecommendViewHolder) {
        job = scope?.launch {
            delay(delayStart)
            LibDeprecatedLogger.d("${albumListBean?.tvName}准备播放： 过去 ${delayStart / 1000} 秒了")
            context?.get()?.let {
                val videoView = ShVideoView(it)

                vh.binding.rootPlayer.visible()
                vh.binding.rootPlayer.removeAllViews()
//                videoView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                vh.binding.rootPlayer.addView(videoView)
                launcherPlayerManager.setPlayParamsAndPlayRecommend(it, albumListBean, vh, mShVideoView = videoView)
                vh.binding.playingIcon.gone()
                vh.binding.playingIcon.cancelWaveAnimation()
            }


        }
    }


    class TypeRecommendViewHolder(val binding: ItemTypeRecommendLayoutBigBinding): ViewHolder(binding.root), Serializable
}