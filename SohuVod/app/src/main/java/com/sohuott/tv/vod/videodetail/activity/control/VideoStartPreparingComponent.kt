package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.widget.TextView
import com.sh.ott.video.contor.ShControlComponent
import com.sh.ott.video.contor.ShVideoViewController
import com.sh.ott.video.player.PlayerConstants
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.widget.PlayerLoadingView

class VideoStartPreparingComponent @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ShControlComponent(context, attrs, defStyleAttr) {
    private var mIncludeScalePreparingLoadingContent: TextView? = null

    private var mScreenMode = PlayerConstants.ScreenMode.NORMAL

    private var mPlayerLoadingView: PlayerLoadingView? = null

    //是否是切换清晰度时
    var isChangeResolutionApp = false

    //暂停广告 max 视频忽略准备播放状态
    var isPauseAd = false

    private var controller: ShVideoViewController? = null

    init {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.layout_sacle_play_preparing_view, this, true)
        mIncludeScalePreparingLoadingContent =
            view.findViewById(R.id.tv_scale_play_preparing_loading_content)
        mPlayerLoadingView = view.findViewById(R.id.scale_play_preparing_loading)
        setShowPreparingTextSize()
    }

    override fun attachController(controller: ShVideoViewController) {
        this.controller = controller
    }

    override fun onAdPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        Log.d("VideoControlComponent", " onAdPlayStateChanged playState:$playState extras:$extras")
        if (isPauseAd) {
            hide()
            return
        }
        when (playState) {
            PlayerConstants.VideoState.PLAYBACK_COMPLETED,
            PlayerConstants.VideoState.STOPPED,
            PlayerConstants.VideoState.ERROR,
            PlayerConstants.VideoState.PAUSED,
            PlayerConstants.VideoState.PLAYING_BACK,
            PlayerConstants.VideoState.PLAYING -> {
                hide()
            }

            PlayerConstants.VideoState.IDLE,
                -> {
                show()
            }
        }
    }

    override fun onFilmPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        Log.d(
            "VideoControlComponent",
            "onFilmPlayStateChanged  playState:$playState extras:$extras"
        )

        when (playState) {
            PlayerConstants.VideoState.PLAYBACK_COMPLETED,
            PlayerConstants.VideoState.STOPPED,
            PlayerConstants.VideoState.ERROR,
            PlayerConstants.VideoState.PAUSED,
            PlayerConstants.VideoState.PLAYING -> {
                isChangeResolutionApp = false
                mPlayerLoadingView?.visibility = GONE
                hide()
            }

            PlayerConstants.VideoState.PLAYING_BACK -> {
                if (isChangeResolutionApp) return
                mPlayerLoadingView?.visibility = GONE
                hide()
            }

            PlayerConstants.VideoState.IDLE,
            PlayerConstants.VideoState.PREPARING,
                -> {
                show()
                mPlayerLoadingView?.visibility = VISIBLE
            }

        }
    }

    override fun onScreenModeChanged(screenMode: Int) {
        Log.d("VideoControlComponent", "onScreenModeChanged  screenMode:$screenMode ")
        mScreenMode = screenMode
        setShowPreparingTextSize()
    }

    fun show() {
        controller?.showComponent(this)
        mPlayerLoadingView?.visibility = VISIBLE
    }

    fun hide() {
        gone()
        mPlayerLoadingView?.visibility = GONE
    }

    private fun setShowPreparingTextSize() {
        if (mScreenMode == PlayerConstants.ScreenMode.FULL) {
            mIncludeScalePreparingLoadingContent?.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                resources.getDimensionPixelSize(R.dimen.y40).toFloat()
            )
        } else if (mScreenMode == PlayerConstants.ScreenMode.TINY) {
            mIncludeScalePreparingLoadingContent?.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                resources.getDimensionPixelSize(R.dimen.y26).toFloat()
            )
        } else {
            mIncludeScalePreparingLoadingContent?.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                resources.getDimensionPixelSize(R.dimen.y32).toFloat()
            )
        }
    }

    /**
     * 设置即将播放文案
     */
    fun setTipText(tip: String) {
        val tipsText = "即将播放  ${tip ?: ""}"
        mIncludeScalePreparingLoadingContent?.text = tipsText
        mIncludeScalePreparingLoadingContent?.text
        AppLogger.d("$tipsText ")
        setShowPreparingTextSize()
    }
}