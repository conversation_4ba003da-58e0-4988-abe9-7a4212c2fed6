package com.sohuott.tv.vod.activity.setting.play

import android.content.Context
import androidx.leanback.widget.ListRow
import androidx.leanback.widget.Presenter
import androidx.leanback.widget.PresenterSelector
import com.base_leanback.persenter.BaseListRowPresenter

class SettingItemSelector(private val context: Context) : PresenterSelector() {
    override fun getPresenter(item: Any?): Presenter {
        if (item is SettingItem) {

            when (item.type) {
                SETTING_TYPE_HEADER -> {
                    return SettingHeaderPresenter()
                }
                SETTING_TYPE_TIPS -> {
                    return SettingTipPresenter(context)
                }
            }

        }
        if (item is ListRow) {
            return BaseListRowPresenter().apply {
                selectEffectEnabled=false
                isKeepChildForeground=false
            }
        }
        throw RuntimeException("SettingItemSelector is not found this item ")
    }
}