package com.sohuott.tv.vod.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.RelativeLayout;

/**
 * Created by fengle<PERSON> on 16-6-13.
 */
public class ExitPlayerRelativeLayout extends RelativeLayout {

    public interface AttachedToWindowListener {
        public void onAttachedToWindow();
    }

    private AttachedToWindowListener mListener;

    public ExitPlayerRelativeLayout(Context context) {
        super(context);
    }

    public ExitPlayerRelativeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public ExitPlayerRelativeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setAttachedToWindowListener(AttachedToWindowListener listener) {
        mListener = listener;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if(mListener != null) {
            mListener.onAttachedToWindow();
        }
    }
}
