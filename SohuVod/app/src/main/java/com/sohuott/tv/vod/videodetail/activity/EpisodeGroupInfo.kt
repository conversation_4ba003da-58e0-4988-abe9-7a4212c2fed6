package com.sohuott.tv.vod.videodetail.activity

import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel
import com.sohuott.tv.vod.lib.model.ContentGroup.DataBean.ContentsBean.AlbumListBean

data class EpisodeGroupInfo(
    var aid: Int = 0,
    var vid: Int = 0,
    var trailerId: Int = 0,
    var dateType: Int = 0,
    // cateCode - 100：电影；101：电视剧；106：综艺；107：纪录片；115：动漫；10001：美剧；
    var cateCode: Int = 0,
    var sortOrder: Int = 0,
    var tvIsEarly: Int = 0,
    var albumEpisodeType: Int = 0,
    var tvIsIntrest: Int = 0,
    var videoCount: Int = 0,
    var trailerCount: Int = 0,
    var latestVideoCount: String? = "",
    //mAlbumInfo.data.tvIsIntrest != 0
    var isTrailer: Boolean = false,
    //mAlbumInfo.data.hasTrailer
    var hasTrailer: Boolean = false,
    var updateNotification: String? = null,
    var tvSets: String? = null,
    var maxVideoOrder: String? = null,
    //mAlbumInfo.extend.albumSeries
    var albumSeries: MutableList<AlbumListBean?>? = null,
    var isShowTitle: Int = 0,
    var episodePlayingVideoOrder: Int = 0,
    var recommendList: MutableList<AlbumInfoRecommendModel>? = null,
    var isExtendNull:Boolean=false,
    ) {
}