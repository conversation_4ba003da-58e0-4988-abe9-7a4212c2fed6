package com.sohuott.tv.vod.activity.detective

import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.View
import android.view.View.OnClickListener
import com.com.sohuott.tv.vod.base_component.NewBaseActivity
import com.drake.net.Get
import com.drake.net.utils.scopeNetLife
import com.lib_statistical.addClickEvent
import com.lib_viewbind_ext.viewBinding
import com.sh.ott.video.base.component.ShDataSource
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.base.OnStateChangeListener
import com.sh.ott.video.player.base.SystemPlayerFactory
import com.sh.ott.video.player.sofa.SofaPlayerFactory
import com.sh.ott.video.vv.VvPushManger
import com.sohu.lib_utils.PrefUtil
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.databinding.ActivityPlayDetectionBinding
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.ToastUtils
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.model.PlayDetection
import com.sohuott.tv.vod.service.LoggerUpload
import com.sohuott.tv.vod.service.LoggerUpload.Companion.FEEDBACK_UPLOAD_ERROR_NETWORK
import com.sohuott.tv.vod.ui.NewScaleFocusChangeListener
import com.sohuott.tv.vod.videodetail.activity.control.FilmVideoStartPreparingComponent
import okhttp3.Headers.Companion.toHeaders


//播放检测页面
class PlayDetectionActivity : NewBaseActivity(R.layout.activity_play_detection), OnClickListener,
    LoggerUpload.LoggerUploadCallBack, OnStateChangeListener {
    private var mViewBinding: ActivityPlayDetectionBinding? = null
    private val _binding by viewBinding(onViewDestroyed = {
        mViewBinding = null
    }, ActivityPlayDetectionBinding::bind)

    private var currentStatus: PlayDetectionStatus = PlayDetectionStatus.START
//    private var screenViewNew: SimplifyScaleScreenViewNew? = null


    private var countdownTimer: CountDownTimer? = null
    private var remainingMillis: Long = 0 // 存储剩余时间
    private val countDownTime: Long = 11000 //倒计时10秒


    private var titleMap = mapOf(
        PlayDetectionStatus.PLAYING_STAGE_1 to "系统播放器1080P H264播放检测",
        PlayDetectionStatus.PLAYING_STAGE_2 to "自研播放器1080P H264播放检测",
        PlayDetectionStatus.PLAYING_STAGE_3 to "系统播放器1080P H265播放检测",
        PlayDetectionStatus.PLAYING_STAGE_4 to "自研播放器1080P H265播放检测"
    )
    private var playMap: MutableMap<PlayDetectionStatus, String>? = mutableMapOf()


    private var upLoadContent = "" //上传日志内容
    private var reportTime = 0L //上传日志时间

//    private var myService: TimingServiceKt? = null

    private var mScaledFocusChange: NewScaleFocusChangeListener? = null //放大动画


//    private val serviceConnection: ServiceConnection = object : ServiceConnection {
//        override fun onServiceConnected(name: ComponentName, service: IBinder) {
//            val binder: TimingServiceKt.MyBinder = service as TimingServiceKt.MyBinder
//            myService = binder.service
//
//            // 传递参数给服务
//            myService?.setIsPlayDetectionAndContent(true, upLoadContent)
//        }
//
//        override fun onServiceDisconnected(name: ComponentName) {
//            myService = null;
//        }
//    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = _binding


        mScaledFocusChange = NewScaleFocusChangeListener()

        reportTime = System.currentTimeMillis() //记录logId
        LibDeprecatedLogger.d("PlayDetection ReportTime : $reportTime")
        PrefUtil.putLong(Constant.REPORT_TIME, reportTime)

//        initView()

        initListener()

        mViewBinding?.playDetectionStartBtn?.requestFocus()

    }

//    private fun initView() {
//        screenViewNew = SimplifyScaleScreenViewNew(
//            this@PlayDetectionActivity,
//            mViewBinding?.playDetectionVideoView
//        ).also {
//            it.setSimplifyPlayerCallback(this@PlayDetectionActivity)
//            it.setIsRecommendVideo(true)
//            it.setCirculate(false)
//        }
//    }

    override fun onResume() {
        super.onResume()
    }

    override fun onPause() {
        super.onPause()

    }

    override fun onDestroy() {
        super.onDestroy()
        mViewBinding?.playShDetectionVideoView?.destroy()
//        release()
    }

    private fun release() {
        LibDeprecatedLogger.d(
            "==================================播放检测结束"
                    + currentStatus.description
                    + "=================================="
        )
        onVvClose()
        mViewBinding?.playShDetectionVideoView?.release()
        if (currentStatus != PlayDetectionStatus.START) {
//            myService?.setIsPlayDetectionAndContent(true, upLoadContent)
//            unbindService(serviceConnection)
//            SohuAppUtil.stopTimingService(this@PlayDetectionActivity)
            pushLogger()
        }
//        screenViewNew?.also {
//            it.stopPlay()
//            screenViewNew = null
//        }
    }

    private fun pushLogger() {
        LoggerUpload.getInstants().release()
        LoggerUpload.getInstants().push(
            System.currentTimeMillis(),
            LoggerUpload.FEEDBACK_UPLOAD_USER,
            LoggerUpload.FEEDBACK_SOURCE_PLAY,
            upLoadContent,
            this
        )
    }

    override fun onError(errorType: Int) {
        if (errorType == FEEDBACK_UPLOAD_ERROR_NETWORK) {
            ToastUtils.showToast(this.applicationContext, "设备访问频繁");
        } else {
            ToastUtils.showToast(this.applicationContext, "日志上传失败");
        }
    }

    override fun onSuccess() {
        ToastUtils.showToast(this.applicationContext, "日志已上传，正在提交反馈");
    }

    private fun findNextStatus(currentStatus: PlayDetectionStatus): PlayDetectionStatus {
        return when (currentStatus) {
            PlayDetectionStatus.PLAYING_STAGE_1 -> PlayDetectionStatus.PLAYING_STAGE_2
            PlayDetectionStatus.PLAYING_STAGE_2 -> PlayDetectionStatus.PLAYING_STAGE_3
            PlayDetectionStatus.PLAYING_STAGE_3 -> PlayDetectionStatus.PLAYING_STAGE_4
            PlayDetectionStatus.PLAYING_STAGE_4 -> PlayDetectionStatus.END // 假设这是最后一个状态，没有下一个
            PlayDetectionStatus.START -> PlayDetectionStatus.PLAYING_STAGE_1
            PlayDetectionStatus.END -> PlayDetectionStatus.PLAYING_STAGE_1
        }
    }

    private fun requestData() {
        scopeNetLife {
//            val url=if (UrlWrapper.DEBUG) "http://fat-api.ott.tv.snmsohu.aisee.tv/test/ott-api-v4/v4/" else "https://api.ott.tv.snmsohu.aisee.tv/ott-api-v4/v4/"
            val playDetectionData =
                Get<PlayDetection>("${RetrofitApi.get().retrofitHost.baseHost}common/getTestPlayUrl.json") {
                    setHeaders(Util.getHeaders(this@PlayDetectionActivity).toHeaders())
                }.await().data
            playMap?.put(PlayDetectionStatus.PLAYING_STAGE_1, playDetectionData.systemH264Url)
            playMap?.put(PlayDetectionStatus.PLAYING_STAGE_2, playDetectionData.privateH264Url)
            playMap?.put(PlayDetectionStatus.PLAYING_STAGE_3, playDetectionData.systemH265Url)
            playMap?.put(PlayDetectionStatus.PLAYING_STAGE_4, playDetectionData.privateH265Url)
            LibDeprecatedLogger.d("PlayDetection address：${playDetectionData}")
            //开始第一阶段播放播放
            nextStatusPlay()

        }
    }


    private fun initListener() {
        mViewBinding?.playDetectionStartBtn?.setOnClickListener(this)
        mViewBinding?.playDetectionStartExitBtn?.setOnClickListener(this)
        mViewBinding?.playDetectionEndExitBtn?.setOnClickListener(this)
        mViewBinding?.playDetectionExitContinueBtn?.setOnClickListener(this)
        mViewBinding?.playDetectionExitBtn?.setOnClickListener(this)
        mViewBinding?.playDetectionNormalBtn?.setOnClickListener(this)
        mViewBinding?.playDetectionUnnormalBtn?.setOnClickListener(this)

        mViewBinding?.playDetectionStartBtn?.onFocusChangeListener = mScaledFocusChange
        mViewBinding?.playDetectionStartExitBtn?.onFocusChangeListener = mScaledFocusChange
        mViewBinding?.playDetectionEndExitBtn?.onFocusChangeListener = mScaledFocusChange
        mViewBinding?.playDetectionExitContinueBtn?.onFocusChangeListener = mScaledFocusChange
        mViewBinding?.playDetectionExitBtn?.onFocusChangeListener = mScaledFocusChange
        mViewBinding?.playDetectionNormalBtn?.onFocusChangeListener = mScaledFocusChange
        mViewBinding?.playDetectionUnnormalBtn?.onFocusChangeListener = mScaledFocusChange
    }

    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.keyCode == KeyEvent.KEYCODE_BACK
            && event.action == KeyEvent.ACTION_DOWN
        ) {
            //返回键
            if (currentStatus == PlayDetectionStatus.START ||
                currentStatus == PlayDetectionStatus.END
            ) {
                release()
                finish()

            } else if (mViewBinding?.playDetectionExitLayout?.visibility == View.VISIBLE) {
                //如果退出检测窗口显示，隐藏自己
                if (mViewBinding?.playDetectionNormalLayout?.visibility == View.GONE) {
                    mViewBinding?.playShDetectionVideoView?.start()
//                    screenViewNew?.pausePlay()
                    resumeCountdown()
                }
                mViewBinding?.playDetectionExitLayout?.gone()
            } else {
                //如果退出窗口不在
                if (mViewBinding?.playDetectionNormalLayout?.visibility != View.VISIBLE) {
                    mViewBinding?.playShDetectionVideoView?.pause()
//                    screenViewNew?.pausePlay()
                    pauseCountdown()
                }
                mViewBinding?.playDetectionExitLayout?.visible()
                mViewBinding?.playDetectionExitContinueBtn?.requestFocus()
            }
            return true
        }

        return super.dispatchKeyEvent(event)
    }

    private fun nextStatusPlay() {
        countdownTimer = null
        currentStatus = findNextStatus(currentStatus)
        if (currentStatus == PlayDetectionStatus.END) {
            mViewBinding?.playDetectionVideoLayout?.gone()
            mViewBinding?.playDetectionEndLayout?.visible()
            mViewBinding?.playDetectionEndExitBtn?.requestFocus()
        } else {
            when (currentStatus.name) {
                PlayDetectionStatus.PLAYING_STAGE_1.toString() -> {
                    mViewBinding?.playDetectionNum?.setBackgroundResource(R.drawable.play_detection_num_1)
                }

                PlayDetectionStatus.PLAYING_STAGE_2.toString() -> {
                    mViewBinding?.playDetectionNum?.setBackgroundResource(R.drawable.play_detection_num_2)
                }

                PlayDetectionStatus.PLAYING_STAGE_3.toString() -> {
                    mViewBinding?.playDetectionNum?.setBackgroundResource(R.drawable.play_detection_num_3)
                }

                PlayDetectionStatus.PLAYING_STAGE_4.toString() -> {
                    mViewBinding?.playDetectionNum?.setBackgroundResource(R.drawable.play_detection_num_4)
                }
            }
            startPlay()
        }
    }

    private fun startPlay() {
        remainingMillis = countDownTime;
        mViewBinding?.playDetectionVideoLayout?.visible()
        mViewBinding?.playDetectionVideoTitle?.text = titleMap[currentStatus]
//        screenViewNew?.setPlayDetectionParams(
//            playMap?.get(currentStatus),
//            currentStatus == PlayDetectionStatus.PLAYING_STAGE_1 ||
//                    currentStatus == PlayDetectionStatus.PLAYING_STAGE_3
//        )
        if (currentStatus == PlayDetectionStatus.PLAYING_STAGE_1 ||
            currentStatus == PlayDetectionStatus.PLAYING_STAGE_3
        ) {
            mViewBinding?.playShDetectionVideoView?.setFilmPlayerFactory(SystemPlayerFactory.create())
        } else {
            mViewBinding?.playShDetectionVideoView?.setFilmPlayerFactory(SofaPlayerFactory.create())
        }
        mViewBinding?.playShDetectionVideoView?.release(false, false)
        mViewBinding?.playShDetectionVideoView?.addFilmVideoControlComponent(
            FilmVideoStartPreparingComponent(this)
        )
        mViewBinding?.playShDetectionVideoView?.removeFilmOnStateChangeListener(this)
        mViewBinding?.playShDetectionVideoView?.addFilmOnStateChangeListener(this)
        val mShDataSource = ShDataSource().also {
            it.adSkip = true
            it.url = playMap?.get(currentStatus)
        }
        mViewBinding?.playShDetectionVideoView?.setDataSource(mShDataSource)
        VvPushManger.getInstance().onVVCreate(mShDataSource)
        mViewBinding?.playShDetectionVideoView?.prepareAsync()
        startCountdown(countDownTime)
    }

    override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
        if (playState == PlayerConstants.VideoState.PLAYING) {
            /**
             * 开始播放视频内容时发送（剧集播放1秒后发送）
             */
            VvPushManger.getInstance().onStart()
        }
    }


    /**
     * 关闭时发送
     */
    private fun onVvClose() {
        VvPushManger.getInstance().onClose()
    }

    override fun onClick(v: View?) {
        when (v) {
            mViewBinding?.playDetectionStartBtn -> {
                addClickEvent(10330)
                LibDeprecatedLogger.d(
                    "==================================播放检测开始"
                            + currentStatus.description
                            + "=================================="
                )
                //开始检测
                mViewBinding?.playDetectionStartLayout?.gone()
                requestData()

//                SohuAppUtil.startTimingService(this@PlayDetectionActivity)
//                val intent = Intent(this, TimingServiceKt::class.java)
//                bindService(intent, serviceConnection, BIND_AUTO_CREATE)
//                requestIpInfo()
            }

            mViewBinding?.playDetectionEndExitBtn,
            mViewBinding?.playDetectionStartExitBtn,
            mViewBinding?.playDetectionExitBtn -> {
                LibDeprecatedLogger.d("PlayDetection content：\n $upLoadContent")
                if (v == mViewBinding?.playDetectionEndExitBtn) {
                    addClickEvent(10332)
                    //退出检测
                } else if (v == mViewBinding?.playDetectionStartExitBtn) {
                    addClickEvent(10331)
                    //先不检测
                } else {
                    //退出
                    addClickEvent(10335)
                }
                //退出检测
                release()
                finish()
            }

            mViewBinding?.playDetectionNormalBtn -> {
                addClickEvent(10334)
                pauseCountdown()
                //播放正常
                mViewBinding?.playDetectionNormalLayout?.gone()
                upLoadContent += if (upLoadContent.isEmpty()) {
                    "${titleMap[currentStatus]} : 正常"
                } else {
                    " 、 ${titleMap[currentStatus]} : 正常"
                }
                LibDeprecatedLogger.d("PlayDetection $upLoadContent")
                nextStatusPlay()
            }

            mViewBinding?.playDetectionUnnormalBtn -> {
                addClickEvent(10333)
                pauseCountdown()
                //播放不正常
                mViewBinding?.playDetectionNormalLayout?.gone()
                upLoadContent += if (upLoadContent.isEmpty()) {
                    "${titleMap[currentStatus]} : 异常"
                } else {
                    " 、 ${titleMap[currentStatus]} : 异常"
                }
                LibDeprecatedLogger.d("PlayDetection $upLoadContent")

                nextStatusPlay()
            }
            //继续检测
            mViewBinding?.playDetectionExitContinueBtn -> {
                addClickEvent(10336)
                mViewBinding?.playDetectionExitLayout?.gone()
//                screenViewNew?.pausePlay()
                mViewBinding?.playShDetectionVideoView?.start()
                resumeCountdown()
            }

        }
    }


    private fun startCountdown(duration: Long) {
        LibDeprecatedLogger.d("startCountdown : $duration countdownTimer : $countdownTimer remainingMillis : $remainingMillis")
        remainingMillis = duration
        if (countdownTimer == null) {
            countdownTimer = object : CountDownTimer(remainingMillis, 500) {
                override fun onTick(millisUntilFinished: Long) {
                    remainingMillis = millisUntilFinished
                    updateCountdownText(millisUntilFinished.toInt())
                }

                override fun onFinish() {

                }
            }
            countdownTimer?.start()
        } else {
            pauseCountdown()
        }
    }


    private fun pauseCountdown() {
        LibDeprecatedLogger.d("pauseCountdown")
        countdownTimer?.cancel()
        countdownTimer = null
    }

    private fun resumeCountdown() {
        LibDeprecatedLogger.d("resumeCountdown $remainingMillis")
        startCountdown(remainingMillis) // 使用剩余时间重新启动倒计时
    }

    private fun updateCountdownText(timeLeft: Int) {
        with(mViewBinding?.playDetectionVideoCountDown) {
            mViewBinding?.playDetectionVideoCountDown?.text = "${timeLeft / 1000}s"
            LibDeprecatedLogger.d("PlayDetection current: ${titleMap[currentStatus]} $timeLeft s")
            if (timeLeft < 1000) {
                pauseCountdown()
                mViewBinding?.playShDetectionVideoView?.pause()
                onVvClose()
//                screenViewNew?.pausePlay()
                mViewBinding?.playDetectionNormalLayout?.visible()
                mViewBinding?.playDetectionNormalBtn?.requestFocus()
                mViewBinding?.playDetectionVideoCountDown?.text = "10s"
            }
        }
    }
}


enum class PlayDetectionStatus(
    val description: String
) {
    START("检测开始"),
    PLAYING_STAGE_1("检测第一阶段"),
    PLAYING_STAGE_2("检测第二阶段"),
    PLAYING_STAGE_3("检测第三阶段"),
    PLAYING_STAGE_4("检测第四阶段"),
    END("检测结束");

}
