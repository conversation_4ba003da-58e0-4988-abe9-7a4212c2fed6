package com.sohuott.tv.vod.search

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout

class SearchKeyBoardLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    interface OnSearchKeyBoardFocusChangeListener {
        fun onSearchKeyBoardFocusRight()
    }

    var mOnSearchKeyBoardFocusChangeListener: OnSearchKeyBoardFocusChangeListener? = null

    fun setOnSearchKeyBoardFocusChangeListener(listener: OnSearchKeyBoardFocusChangeListener) {
        mOnSearchKeyBoardFocusChangeListener = listener
    }

    private var autoFocus = false

    fun setAutoFocus(autoFocus: Boolean) {
        this.autoFocus = autoFocus
    }

    override fun focusSearch(focused: View?, direction: Int): View? {
        val nextFocus = super.focusSearch(focused, direction)
        if (direction == FOCUS_RIGHT && !isDescendantView(nextFocus) && !autoFocus) {
            mOnSearchKeyBoardFocusChangeListener?.onSearchKeyBoardFocusRight()
            return null
        }
//        return if (nextFocus == null || focused == null ||!isDescendantView(nextFocus)) {
//            // 如果下一个焦点为空，或者当前聚焦的 View 为空，或者下一个焦点不是当前布局的子视图或子视图的子视图，
//            // 则返回当前聚焦的 View，以防止焦点传递到外部。
//            focused
//        } else {
//            // 否则，返回下一个可聚焦的 View
//            nextFocus
//        }
        return super.focusSearch(focused, direction)
    }

    private fun isDescendantView(view: View): Boolean {
        var currentView: View? = view
        while (currentView != null) {
            if (currentView.parent === this) {
                return true
            }
            currentView = currentView.parent as? View
        }
        return false
    }
}