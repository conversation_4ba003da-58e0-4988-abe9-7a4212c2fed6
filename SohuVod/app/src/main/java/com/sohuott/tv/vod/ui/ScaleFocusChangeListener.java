package com.sohuott.tv.vod.ui;

import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;

/**
 * Created by fenglei on 16-1-13.
 */
public class ScaleFocusChangeListener implements View.OnFocusChangeListener {

    public interface FocusCallback {
        void onFocusChange(View v, boolean hasFocus);
    }

    private FocusCallback mFocusCallback;

    public FocusBorderView mFocusBorderView;

    public float mScale = FocusUtil.HOME_SCALE;

    private TextView mScrollTextView;

    public TextView getScrollTextView() {
        return mScrollTextView;
    }

    public void setScrollTextView(TextView scrollTextView) {
        mScrollTextView = scrollTextView;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (v.getTag() != null && v.getTag() instanceof TextView) {
            ((TextView)v.getTag()).setSelected(hasFocus);
            if (hasFocus) {
                ((TextView)v.getTag()).setMarqueeRepeatLimit(-1);
                ((TextView)v.getTag()).setEllipsize(TextUtils.TruncateAt.MARQUEE);
                if(mScrollTextView != null){
                    mScrollTextView.setMarqueeRepeatLimit(-1);
                    mScrollTextView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                }
            } else {
                ((TextView)v.getTag()).setEllipsize(TextUtils.TruncateAt.END);
                if(mScrollTextView != null){
                    mScrollTextView.setEllipsize(TextUtils.TruncateAt.END);
                }
            }
        }
        if (hasFocus) {
            if (mFocusBorderView != null) {
                mFocusBorderView.setFocusView(v);
            }
            FocusUtil.setFocusAnimator(v, mFocusBorderView, mScale);
        } else {
            if (mFocusBorderView != null) {
                mFocusBorderView.setUnFocusView(v);
            }
            FocusUtil.setUnFocusAnimator(v);
        }
        if (mFocusCallback != null) {
            mFocusCallback.onFocusChange(v, hasFocus);
        }
    }

    public void setScale(float scale) {
        mScale = scale;
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        mFocusBorderView = focusView;
    }

    public FocusBorderView getFocusBorderView() {
        return mFocusBorderView;
    }

    public void setFocusCallback(FocusCallback focusCallback) {
        mFocusCallback = focusCallback;
    }

}
