package com.sohuott.tv.vod.activity.launcher;

import static com.sohuott.tv.vod.lib.utils.Constant.TAG_MY_POSITION;

import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.PersistableBundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.TranslateAnimation;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.DiffCallback;
import androidx.leanback.widget.HorizontalGridView;
import androidx.leanback.widget.OnChildViewHolderSelectedListener;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory;
import com.google.gson.Gson;
import com.hjq.permissions.IPermissionInterceptor;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.lib_dlna_core.SohuDlnaManger;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sh.apm.trace.canary.AppStartTimeManager;
import com.sh.ott.video.ad.AdRequestFactory;
import com.sh.ott.video.base.OnProgressChangedListener;
import com.sh.ott.video.base.component.ShDataSource;
import com.sh.ott.video.player.PlayerConstants;
import com.sh.ott.video.player.base.OnStateChangeListener;
import com.sh.ott.video.view.ShVideoView;
import com.sohu.lib_utils.FormatUtils;
import com.sohu.lib_utils.PrefUtil;
import com.sohu.lib_utils.StringUtil;
import com.sohu.ott.ads.sdk.SdkFactory;
import com.sohu.ott.ads.sdk.iterface.ILoader;
import com.sohu.ott.ads.sdk.iterface.IOralAdLoader;
import com.sohu.ott.ads.sdk.model.RequestComponent;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.base_room.manager.ImageInfoManager;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.user.UserApi;
import com.sohuott.tv.vod.activity.BaseFragmentActivity;
import com.sohuott.tv.vod.activity.RenewActivity;
import com.sohuott.tv.vod.activity.TeenModeDescActivity;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;
import com.sohuott.tv.vod.adapter.lb.ContentViewPagerAdapter;
import com.sohuott.tv.vod.app.AppConstants;
import com.sohuott.tv.vod.app.SohuAppUtil;
import com.sohuott.tv.vod.base_router.RouterPath;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.databinding.ActivityLauncherBinding;
import com.sohuott.tv.vod.fragment.BootFragment;
import com.sohuott.tv.vod.fragment.LauncherTabBridgeAdapter;
import com.sohuott.tv.vod.fragment.lb.HomeContentFragment;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AboutInfo;
import com.sohuott.tv.vod.lib.model.CateCode;
import com.sohuott.tv.vod.lib.model.GrayInfo;
import com.sohuott.tv.vod.lib.model.LauncherConfig;
import com.sohuott.tv.vod.lib.model.PlayListId;
import com.sohuott.tv.vod.lib.model.ServerMessage;
import com.sohuott.tv.vod.lib.model.TopInfo;
import com.sohuott.tv.vod.lib.model.UpdateInfo;
import com.sohuott.tv.vod.lib.model.launcher.HomeTab;
import com.sohuott.tv.vod.lib.model.launcher.TeenModePopDialogBean;
import com.sohuott.tv.vod.lib.push.event.LoginSuccessEvent;
import com.sohuott.tv.vod.lib.push.event.LogoutEvent;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.DateUtils;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.StringUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.UpdatePresenter;
import com.sohuott.tv.vod.presenter.UpdatePresenterImpl;
import com.sohuott.tv.vod.presenter.launcher.TitlePresenter;
import com.sohuott.tv.vod.ui.ExitAppDialogNew;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.utils.SouthMediaUtil;
import com.sohuott.tv.vod.utils.SyncHistoryAndCollectionUtil;
import com.sohuott.tv.vod.utils.UpdateHelper;
import com.sohuott.tv.vod.view.AboutView;
import com.sohuott.tv.vod.widget.lb.TopViewBar;
import com.sohuott.tv.vod.worker.UploadHandler;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by music on 2021/8/31.
 * Leanback框架首页
 */

@Route(path = RouterPath.Home.LAUNCHER_ACTIVITY)
public class LauncherActivity extends BaseFragmentActivity implements
        HomeContentFragment.OnFragmentInteractionListener,
        TopViewBar.OnTopViewBarInteractionListener,
        AboutView,
        UpdateHelper.ExistListener,
        BootFragment.OnFragmentInteractionListener {

    private static final String TAG = LauncherActivity.class.getSimpleName();

//    private AdCommon adCommon;

    private static final int PAGE = 1;
    private static final int PAGE_SIZE = 40;

    private LauncherHandler mLauncherHandler;

    private ActivityLauncherBinding mBinding;
    private LauncherViewModel launcherViewModel;

    private int mCurrentTabType;
    private long mCurrentTabCode;
    private int mOldPosition = -1;
    private long mOldTime;
    private int mCurrentPosition;
    private int mLastPosition;
    private boolean mIsFirst = true;
    private boolean mIsGoToMine = false;
    private View mRootView;
    private ArrayObjectAdapter mArrayObjectAdapter;
    private LauncherTabBridgeAdapter mItemBridgeAdapter;
    private ContentViewPagerAdapter mViewPagerAdapter;
    private List<com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem> dataBeans;
    private ValueAnimator mNewAnim;

    private int mCurrentPageIndex = 0;
    private boolean isSkipTabFromViewPager = false;

    private LoginUserInformationHelper mHelper;
    private UpdateHelper mUpdateHelper;
    private UpdatePresenter mUpdatePresenter;
    private LinearLayout mLlPermissionsTips;

    private Boolean isShowUpdate = true;
    private com.sohuott.tv.vod.view.TeenModePopDialog mTeenModePopDialog;
    private com.sohuott.tv.vod.view.MsgPopDialog mMsgPopDialog;

    //频道皮肤配置
    private HashMap<Integer, String> mSkinMaps = new HashMap<>();
    //频道置灰配置
    private HashMap<Integer, Boolean> mGrayMaps = new HashMap<>();

    private boolean isOpenDlna = true;

    private boolean isDialogShowing = true;

    DrawableCrossFadeFactory drawableCrossFadeFactory
            = new DrawableCrossFadeFactory.Builder(300).setCrossFadeEnabled(true).build();

    public boolean getDialogShowing() {
        return isDialogShowing;
    }

    public View getRootView() {
        return mRootView;
    }

    public ArrayObjectAdapter getArrayObjectAdapter() {
        return mArrayObjectAdapter;
    }

    public HorizontalGridView getHorizontalGridView() {
        return mBinding.hgTitle;
    }

    public TopViewBar getTopViewBar() {
        return mBinding.topBar;
    }

    public boolean getIsSkipTab() {
        return isSkipTabFromViewPager;
    }

    //获取当前的tabcode
    public long getCurrentTabCode() {
        return mCurrentTabCode;
    }

    public boolean getIsFirst() {
        return mIsFirst;
    }

    public void setIsFirst(boolean first) {
        this.mIsFirst = first;
    }

    private void hideBootFragment() {
        try {

            BootFragment fragment = (BootFragment) getSupportFragmentManager().findFragmentById(R.id.fragment_container);
            if (fragment != null) {
                FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction()
                        .remove(fragment);
                if (getSupportFragmentManager().isStateSaved()) {
                    fragmentTransaction.commit();
                } else {
                    fragmentTransaction.commitAllowingStateLoss();
                }
                LibDeprecatedLogger.d("hide bootfragment");
            } else {
                LibDeprecatedLogger.d("no need hide bootfragment");
                return;
            }
        } catch (Exception e) {
            AppLogger.d("hide bootfragment error :" + e.getMessage());
        }
    }


    private boolean isTopView = false;
    private String topViewUrl = null;

    @Override
    public void onHideFragment(boolean isTopView, String topViewUrl) {
        this.isTopView = isTopView;
        this.topViewUrl = topViewUrl;
        if ((!requestPermission() && isTopView) || !isTopView && TextUtils.isEmpty(topViewUrl)) {
            forceRefresh = true;
            initData();
        }
        hideBootFragment();
        SouthMediaUtil.southNewMediaCheck(this);
        if (isTopView) {
            LibDeprecatedLogger.d("start topview");
        } else {
            requestUpdateDialog();
        }
        adPreLoad();
    }

    @Override
    public void homeDataPreLoad(boolean isTopView, String topViewUrl) {
        this.isTopView = isTopView;
        this.topViewUrl = topViewUrl;
        //有权限，不是第一次进入App，在获取广告数据后，提前加载首页
        if (XXPermissions.isGranted(this, Permission.Group.STORAGE)
                && topViewUrl != null
                && isTopView) {
            initData();
        }
    }

    //广告预加载
    private void adPreLoad() {
        try {
            IOralAdLoader loader = SdkFactory.getInstance().createOralAdLoader(this);
            RequestComponent component = new RequestComponent();
            component.setTuv(UserInfoHelper.getGid());
            component.setSite("1");
            loader.adTs(component);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public void restoreFragment() {
        mBinding.videoRoot.removeAllViews();
        TranslateAnimation animation = new TranslateAnimation(0, 0, getResources().getDimensionPixelSize(R.dimen.y688), 0);
        animation.setDuration(1000); // 设定动画的时长为1000毫秒(1秒)

        // 设定动画结束后的位置保持为动画的终点，不然动画结束后会回到初始位置
        animation.setFillAfter(true);

        mBinding.vpContent.startAnimation(animation);
        if (dataBeans != null && mViewPagerAdapter.getFragment(mCurrentPageIndex) != null && mViewPagerAdapter.getFragment(mCurrentPageIndex) instanceof HomeContentFragment) {
            forceRefresh = true;
            ((HomeContentFragment) mViewPagerAdapter.getFragment(mCurrentPageIndex)).refreshDataContent();
        }

        requestUpdateDialog();
    }

    private void finishTopViewOnLauncher() {
        if (mBinding.videoRoot.getVisibility() == View.GONE) {
            return;
        }
        mBinding.videoRoot.setVisibility(View.GONE);
        restoreFragment();

    }

//    @Override
//    public void onCompletion(MediaPlayer mediaPlayer) {
//        AppLogger.d("topview onCompletion " + adCommon);
//        if (adCommon != null)
//            Advert.getInstance().reportStartPageTopViewAd(adCommon, ILoader.TopViewState.PLAY_END);
//        finishTopViewOnLauncher();
//        releasePlayer();
//    }

    private void releasePlayer() {
        if (shVideoView != null) {
            shVideoView.getFilmVideoController().stopUpdateProgress();
            shVideoView.destroy();
        }

    }


    private void startCountDown() {
        if (isStartCountDown) return;
        isStartCountDown = true;
        if (shVideoView != null) {
            mBinding.videoRoot.setBackgroundResource(R.drawable.transparent);
        }

    }


    private static class LauncherHandler extends Handler {
        public static final int SELECT = 0x1;
        public static final int SELECT_DURATION = 500;

        private WeakReference<LauncherActivity> weakReference;

        public LauncherHandler(LauncherActivity activity) {
            weakReference = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            LauncherActivity launcherActivity = weakReference.get();
            if (launcherActivity != null) {
                switch (msg.what) {
                    case SELECT:
                        LibDeprecatedLogger.d("handle message : position : " + msg.arg1);
                        launcherActivity.setCurrentItemPosition(msg.arg1);
                        break;
                }
            }
            super.handleMessage(msg);
        }
    }

    @Override
    protected void onStart() {
        super.onStart();

    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppLogger.d("SohuAppNew", "LauncherActivity onCreate  before");

        mBinding = ActivityLauncherBinding.inflate(getLayoutInflater());
        launcherViewModel = new ViewModelProvider(this).get(LauncherViewModel.class);

        setContentView(mBinding.getRoot());

        // 检查是否已经添加了Fragment，例如在配置更改后
        if (savedInstanceState == null) {
            getSupportFragmentManager().beginTransaction()
                    .replace(R.id.fragment_container, new BootFragment())
                    .commit();
        }

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        initIntent();
        initView();
        initListener();
        EventBus.getDefault().register(this);
        AppLogger.d("SohuAppNew", "LauncherActivity onCreate");

    }


    private void startImageInfoUpload() {
        UploadHandler imageUploader = new UploadHandler(this);
        imageUploader.startWork();
        new Thread(() -> ImageInfoManager.INSTANCE.init(LauncherActivity.this)).start();
    }


    private void requestLauncherConfig() {
        NetworkApi.getLauncherConfig(new DisposableObserver<LauncherConfig>() {
            @Override
            public void onNext(LauncherConfig value) {
                LibDeprecatedLogger.d(value.toString());

                mBinding.topBar.setBeiAn(value.getData().getBeian());
                PrefUtil.putString("bei_an", value.getData().getBeian());
                HashMap<Integer, Boolean> mCateCodeMaps = new HashMap<>();
                HashMap<Integer, Boolean> mPlayListIdMaps = new HashMap<>();

                isOpenDlna = value.getData().isOpenDlna();
                setIsOpenDlna();
                Util.setDynamicVideoParams(getApplicationContext(), value.getData().isOpenDynamicVideo());
                for (GrayInfo grayInfo : value.getData().getGrayList()) {
                    mGrayMaps.put(grayInfo.getChannel(), true);
//                    if (mCurrentTabCode == grayInfo.getChannel()) {
//                        changeGray(true);
//                    }
                }

                for (PlayListId playListId : value.getData().getJumpFull().getPlaylistidList()) {
                    mPlayListIdMaps.put(playListId.getPlaylistid(), true);
                }

                for (CateCode cateCode : value.getData().getJumpFull().getCatecodeList()) {
                    mCateCodeMaps.put(cateCode.getCatecode(), true);
                }

                LauncherManager.getInstance().setCateCodeMap(mCateCodeMaps);
                LauncherManager.getInstance().setPlayListIdMap(mPlayListIdMaps);
            }

            @Override
            public void onError(Throwable e) {
                HashMap<Integer, Boolean> mCateCodeMaps = new HashMap<>();
                HashMap<Integer, Boolean> mPlayListIdMaps = new HashMap<>();
                LauncherManager.getInstance().setCateCodeMap(mCateCodeMaps);
                LauncherManager.getInstance().setPlayListIdMap(mPlayListIdMaps);
                setIsOpenDlna();
            }

            @Override
            public void onComplete() {


            }
        });
    }

    private void setIsOpenDlna() {
        SohuDlnaManger.getInstance().setOpen(isOpenDlna);
        SohuDlnaManger.getInstance().initDlna(LauncherActivity.this.getApplicationContext());
    }

    private boolean requestPermission() {
        if (Build.VERSION.SDK_INT < 23 || (XXPermissions.isGranted(this, Permission.Group.STORAGE))) {
            mLlPermissionsTips.setVisibility(View.GONE);
            LibDeprecatedLogger.d("startTopViewVideo 1");
            startTopViewVideo();
            return true;
        } else {
            XXPermissions.with(this).permission(Permission.Group.STORAGE).interceptor(new IPermissionInterceptor() {
                @Override
                public void requestPermissions(Activity activity, List<String> allPermissions, OnPermissionCallback callback) {
                    LibDeprecatedLogger.d("XXPermissions requestPermissions");
                    if (!PrefUtil.getBoolean("XXPermissions_never", false)) {
                        mLlPermissionsTips.setVisibility(View.VISIBLE);
                    }
                    IPermissionInterceptor.super.requestPermissions(activity, allPermissions, callback);
                }

                @Override
                public void grantedPermissions(Activity activity, List<String> allPermissions, List<String> grantedPermissions, boolean all, OnPermissionCallback callback) {
                    LibDeprecatedLogger.d("XXPermissions grantedPermissions");
                    LibDeprecatedLogger.d("startTopViewVideo 2");
                    startTopViewVideo();
                    IPermissionInterceptor.super.grantedPermissions(activity, allPermissions, grantedPermissions, all, callback);
                }

                @Override
                public void deniedPermissions(Activity activity, List<String> allPermissions, List<String> deniedPermissions, boolean never, OnPermissionCallback callback) {
                    LibDeprecatedLogger.d("XXPermissions deniedPermissions");
                    LibDeprecatedLogger.d("startTopViewVideo 3");
                    startTopViewVideo();
                    IPermissionInterceptor.super.deniedPermissions(activity, allPermissions, deniedPermissions, never, callback);
                }
            }).request(new OnPermissionCallback() {
                @Override
                public void onGranted(List<String> permissions, boolean all) {
                    LibDeprecatedLogger.d("XXPermissions onGranted is all " + all);
                    mLlPermissionsTips.setVisibility(View.GONE);
                }

                @Override
                public void onDenied(List<String> permissions, boolean never) {
                    LibDeprecatedLogger.d("XXPermissions onDenied is never:" + never);
                    PrefUtil.putBoolean("XXPermissions_never", never);
                    mLlPermissionsTips.setVisibility(View.GONE);
                }
            });
        }
        return false;
    }

    private void changeGray(Boolean flag) {
        Paint paint = new Paint();
        ColorMatrix cm = new ColorMatrix();
        if (flag) {
            cm.setSaturation(0);//灰度效果
            paint.setColorFilter(new ColorMatrixColorFilter(cm));
            getWindow().getDecorView().setLayerType(View.LAYER_TYPE_HARDWARE, paint);
        } else {
            getWindow().getDecorView().setLayerType(View.LAYER_TYPE_HARDWARE, null);
        }
    }


    private void requestUpdateDialog() {
        if (!isShowUpdate) {
            return;
        }
        if (!Util.getPartnerNo(this).equals(Constant.PARTNER_NO_XIAOMI_STORE_CHANNEL)) {
            HomeData.sIsHomeCreate = true;
            mUpdatePresenter = new UpdatePresenterImpl(this, this);
            mUpdateHelper = new UpdateHelper(this, mUpdatePresenter);
            mUpdateHelper.setExistListener(this);
            mUpdatePresenter.getUpdateInfo();
        }
    }

    private void requestTeenModePopDialog() {

        NetworkApi.getWithCache(this, new Observer<TeenModePopDialogBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(TeenModePopDialogBean value) {
                if (value != null && value.getData() != null) {
                    String lastAlertTeenModeDate = PrefUtil.getString(AppConstants.KEY_LAST_ALERT_TEEN_MODE_DATE, "");
                    AppLogger.d(TAG, "lastAlertTeenModeDate ? " + lastAlertTeenModeDate);
                    AppLogger.d(TAG, "value.data.isAlert ? " + value.getData().getFrequency());
                    //CMS后台配置控制青少年模式弹框展示次数，3 ： 每天都弹，2 ： 两周弹1次，1 ：每周弹1次 ，0：不弹；默认展示级别为3。
                    int days = 15; //默认没有弹过，等效超过两周的天数
                    if (StringUtils.isNotEmptyStr(lastAlertTeenModeDate)) {
                        Date date = new Date();
                        DateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
                        String todayStr = dft.format(date);
                        AppLogger.d(TAG, "todayStr ? " + todayStr);
                        try {
                            Date startDay = dft.parse(lastAlertTeenModeDate);//开始时间
                            Date endDay = dft.parse(todayStr);//结束时间
                            days = DateUtils.getDays(startDay, endDay);
                            AppLogger.d(TAG, "try days ? " + days);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                    }
                    AppLogger.d(TAG, "days ? " + days);
//                    CMS后台配置控制青少年模式弹框展示次数，3 ： 每天都弹，2 ： 两周弹1次，1 ：每周弹1次 ，0：不弹；默认展示级别为3。
                    if (value.getData().getFrequency_2() == 3) {
                        if (days >= 1) {
                            showTeenModePopDialog(value);
                        } else {
                            requestMsgDialogData();
                        }
                    } else if (value.getData().getFrequency_2() == 2) {
                        if (days >= 14) {
                            showTeenModePopDialog(value);
                        } else {
                            requestMsgDialogData();
                        }
                    } else if (value.getData().getFrequency_2() == 1) {
                        if (days >= 7) {
                            showTeenModePopDialog(value);
                        } else {
                            requestMsgDialogData();
                        }
                    } else if (value.getData().getFrequency_2() == 0) {
                        //不弹
                        requestMsgDialogData();
                    } else if (value.getData().getFrequency_2() == 4) {
                        //每次都弹
                        showTeenModePopDialog(value);
                    } else {
                        if (days >= 1) {
                            showTeenModePopDialog(value);
                        } else {
                            requestMsgDialogData();
                        }
                    }
                } else {
                    requestMsgDialogData();
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        }, NetworkApi.networkInterface.getTeenModePopupData(), TeenModePopDialogBean.class);
    }


    private void showTeenModePopDialog(final TeenModePopDialogBean teenModePopup) {
        mTeenModePopDialog = new com.sohuott.tv.vod.view.TeenModePopDialog.Builder(this)
                .setTitle(teenModePopup.getData().getTitle())
                .setMsg(teenModePopup.getData().getPop_text())
                .setCancelListener(new DialogInterface.OnCancelListener() {
                    @Override
                    public void onCancel(DialogInterface dialog) {
                        dialog.dismiss();
                        mTeenModePopDialog = null;
                        requestMsgDialogData();
                    }
                })
                .setPositiveButton(R.string.open_immediately,
                        new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                Intent intent = new Intent(LauncherActivity.this, TeenModeDescActivity.class);
                                intent.putExtra(RenewActivity.PARAM_AC_TYPE, RenewActivity.ACTYPE_RENEW);
                                LauncherActivity.this.startActivity(intent);
                                if (mTeenModePopDialog != null) {
                                    mTeenModePopDialog.dismiss();
                                    mTeenModePopDialog = null;
                                }
                                TeenagersManger.getInstance().exposureShowDecDialogClickOpen();
                                requestMsgDialogData();
                            }
                        })
                .setNegativeButton(R.string.iknow,
                        new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (mTeenModePopDialog != null) {
                                    mTeenModePopDialog.dismiss();
                                    mTeenModePopDialog = null;
                                }
                                TeenagersManger.getInstance().exposureShowDecDialogClickKnow();
                                requestMsgDialogData();
                            }
                        }).show();
        Date date = new Date();
        DateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
        String todayStr = dft.format(date);
        AppLogger.d(TAG, "before put todayStr ? " + todayStr);
        PrefUtil.putString(AppConstants.KEY_LAST_ALERT_TEEN_MODE_DATE, todayStr);
        AppLogger.d(TAG, "after put todayStr ? " + todayStr);
    }

    private void requestMsgDialogData() {
        NetworkApi.getMessageData(PAGE, PAGE_SIZE, new DisposableObserver<ServerMessage>() {
            @Override
            public void onNext(ServerMessage value) {
                try {
                    ArrayList<ServerMessage.Data> msgList = null;
                    if (value != null && value.status == 0 && value.data != null
                            && value.data.size() > 0) {
                        msgList = value.data;
                        LibDeprecatedLogger.d("There are other data!");
                    }
                    ArrayList<ServerMessage.Data> popupMsgList = new ArrayList<>();
                    if (msgList != null && msgList.size() > 0) {
                        for (ServerMessage.Data msgData : msgList) {
                            try {
                                //CMS后台消息类型type=0（默认），并勾选弹窗展示选项
                                String type = msgData.type;
                                AppLogger.d(TAG, "type ? " + type);
                                if ("0".equals(type)) {
                                    Gson parameeterObj = new Gson();
                                    ServerMessage.Parameter parameter = parameeterObj.fromJson(
                                            msgData.parameter, ServerMessage.Parameter.class);
                                    if (parameter.isPopup) {
                                        popupMsgList.add(msgData);
                                    }
                                }
                            } catch (Exception e) {
                                AppLogger.d(TAG, "e ? " + e);
                            }
                        }
                    }
                    if (popupMsgList.size() > 0) {
                        Collections.sort(popupMsgList, new Comparator<ServerMessage.Data>() {
                            @Override
                            public int compare(ServerMessage.Data data1, ServerMessage.Data data2) {
                                Date date1 = FormatUtils.strToDate(data1.createTime);
                                Date date2 = FormatUtils.strToDate(data2.createTime);
                                return date2.compareTo(date1);
                            }
                        });
                        ServerMessage.Data latestPupupMsg = popupMsgList.get(0);
                        AppLogger.d(TAG, "latestPupupMsg.createTime" + latestPupupMsg.createTime);
                        AppLogger.d(TAG, "latestPupupMsg.name" + latestPupupMsg.name);
                        AppLogger.d(TAG, "latestPupupMsg.content" + latestPupupMsg.content);
                        String lastMsgDatetime = PrefUtil.getString(AppConstants.LAST_MSG_DATETIME, "");
                        if (StringUtils.isEmptyStr(lastMsgDatetime)) {
                            showMsgPopDialog(latestPupupMsg);
                        } else {
                            AppLogger.d(TAG, "lastMsgDatetime" + lastMsgDatetime);
                            AppLogger.d(TAG, "latestPupupMsg.createTime" + latestPupupMsg.createTime);
                            Date date1 = FormatUtils.strToDate(lastMsgDatetime);
                            Date date2 = FormatUtils.strToDate(latestPupupMsg.createTime);
                            if (date2.compareTo(date1) > 0) {
                                showMsgPopDialog(latestPupupMsg);
                            } else {
                                isDialogShowing = false;
//                                if (!showTransImg) {
//                                    AppLogger.d("startTopViewVideo 3");
//                                    startTopViewVideo();
//                                }
                                LibDeprecatedLogger.d("There is no new msg! isDialogShowing = " + isDialogShowing);
                            }
                        }

                    }
                } catch (Exception e) {
                    AppLogger.d(TAG, "e ? " + e);
                }

            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("onErrorResponse, error = " + e);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("getMessage() onComplete");
            }
        });
    }

    private void showMsgPopDialog(final ServerMessage.Data latestPupupMsg) {
        mMsgPopDialog = new com.sohuott.tv.vod.view.MsgPopDialog.Builder(this)
                .setTitle(latestPupupMsg.name)
                .setMsg(latestPupupMsg.content)
                .setCancelListener(dialog -> {
                    dialog.dismiss();
                    mMsgPopDialog = null;
                    isDialogShowing = false;

//                    if (!showTransImg) {
//                        AppLogger.d("startTopViewVideo 4");
//                        startTopViewVideo();
//                    }
                })
                .setPositiveButton(R.string.see_immediately,
                        v -> {
                            ActivityLauncher.startMyMessageActivity(LauncherActivity.this);
                            HashMap<String, String> path = new HashMap<>(1);
                            path.put("pageId", "1038");
                            RequestManager.getInstance().onAllEvent(new EventInfo(10252, "clk"), path, null, null);
                        })
                .setNegativeButton(R.string.close,
                        v -> {
                            mMsgPopDialog.dismiss();
                            mMsgPopDialog = null;
                            isDialogShowing = false;

//                            if (!showTransImg) {
//                                AppLogger.d("startTopViewVideo 5");
//                                startTopViewVideo();
//                            }

                            HashMap<String, String> path = new HashMap<>(1);
                            path.put("pageId", "1038");
                            RequestManager.getInstance().onAllEvent(new EventInfo(10253, "clk"), path, null, null);
                        }).show();
        PrefUtil.putString(AppConstants.LAST_MSG_DATETIME, latestPupupMsg.createTime);
    }

    boolean isStartCountDown = false;

    long millisUntilFinished = 0L;

    public void startTopViewVideo() {
        LibDeprecatedLogger.d("startTopViewVideo adcommon ");
        if (isTopView && !topViewUrl.isEmpty() && shVideoView == null) {
            TranslateAnimation animation = new TranslateAnimation(0, 0, 0, getResources().getDimensionPixelSize(R.dimen.y688));
            animation.setDuration(1000);
            animation.setFillAfter(true);

            mBinding.vpContent.startAnimation(animation);


            mBinding.videoRoot.setVisibility(View.VISIBLE);

//            videoView = new SplashVideoView(this);
//            videoView.setBackgroundResource(R.drawable.launcher_bg);
//            mBinding.videoRoot.addView(videoView);
//            videoView.setOnCompletionListener(this);
//            videoView.setOnErrorListener(this);
//            videoView.setOnPreparedListener(this);
//            videoView.setOnInfoListener(this);
//            videoView.setVideoPath(adCommon.getFocusVideo());
            shVideoView = new ShVideoView(this);
            mBinding.videoRoot.addView(shVideoView);
            mBinding.videoRoot.setBackgroundResource(R.drawable.launcher_bg);
            shVideoView.setFilmScreenAspectRatioType(PlayerConstants.ScreenAspectRatio.MATCH_PARENT);
            mBinding.countDown.setVisibility(View.GONE);
            ShDataSource dataSource = new ShDataSource();
            dataSource.setAdSkip(true);
            dataSource.setUrl(topViewUrl);
            shVideoView.addFilmProgressChangedListener(new OnProgressChangedListener() {
                @Override
                public void onVideoProgressChanged(long duration, long position) {
                    LibDeprecatedLogger.d("topview shVideoView onVideoProgressChanged  duration:" + duration + "  position : " + position);
                    millisUntilFinished = (duration - position) / 1000;
                    if (millisUntilFinished < 1) {
                        mBinding.countDown.setVisibility(View.GONE);
                    } else {
                        if (mBinding.countDown.getVisibility() != View.VISIBLE) {
                            mBinding.countDown.setVisibility(View.VISIBLE);
                        }
                        mBinding.countDown.setText(Util.getBootCountDownBackText(millisUntilFinished));
                    }
                    AdRequestFactory.getInstants().reportStartPageAdPlayTime((int) (position) / 1000, ILoader.TopViewSource.VIDEO_SECOND);
//                    Advert.getInstance().reportPageAdPlayTime(adCommon, (int) (position) / 1000, ILoader.TopViewSource.VIDEO_SECOND);
                }
            });
            shVideoView.addFilmOnStateChangeListener(new OnStateChangeListener() {
                @Override
                public void onScreenModeChanged(int mode) {

                }

                @Override
                public void onPlayerStateChanged(int playState, @NonNull HashMap<String, Object> hashMap) {

                    switch (playState) {
                        case PlayerConstants.VideoState.PLAYBACK_COMPLETED:
                            AdRequestFactory.getInstants().reportStartPageTopViewAd(ILoader.TopViewState.PLAY_END);
                            finishTopViewOnLauncher();
                            releasePlayer();
                            break;
                        case PlayerConstants.VideoState.PLAYING:
                            mBinding.videoRoot.setBackgroundResource(R.drawable.transparent);
                            break;

                        case PlayerConstants.VideoState.ERROR:
                            mBinding.videoRoot.setVisibility(View.GONE);
                            AdRequestFactory.getInstants().reportStartPageTopViewAd(ILoader.TopViewState.PLAY_END);
                            finishTopViewOnLauncher();
                            releasePlayer();
                            break;
                        case PlayerConstants.VideoState.PREPARED:
                            AdRequestFactory.getInstants().reportStartPageTopViewAd(ILoader.TopViewState.PLAY_START);
                            startCountDown();
                            shVideoView.getFilmVideoController().startUpdateProgress();
                            break;
                    }

                }
            });
            shVideoView.setDataSource(dataSource);
            shVideoView.prepareAsync();
        }
    }

    private ShVideoView shVideoView;

    @Subscribe
    public void onEventMainThread(LogoutEvent event) {
        if (null == event) {
            return;
        }
        AppLogger.d(TAG, "LogoutEvent");
        getTopData();
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        initIntent();
        String selectTab = intent.getStringExtra(ParamConstant.PARAM_SELECT_TAB);
        if (TextUtils.isEmpty(selectTab)) {
            return;
        }
        if (ParamConstant.PARAM_GO_TO_MINE.equals(selectTab)) {
            getHorizontalGridView().setSelectedPosition(TAG_MY_POSITION);
            getHorizontalGridView().requestFocus();
            refreshTabLayout();
            getTopViewBar().zoomOut();
        } else if (ParamConstant.PARAM_GO_HOME.equals(selectTab)) {
            handleTitleVisible(true);
            refreshTabLayout();
            getHorizontalGridView().setSelectedPositionSmooth(Constant.TAG_FEATURE_POSITION);
        }
    }

    private void initIntent() {
        isShowUpdate = getIntent().getBooleanExtra(ParamConstant.PARAM_IS_SHOW_UPDATE, true);
        String selectTab = getIntent().getStringExtra(ParamConstant.PARAM_SELECT_TAB);
        if (ParamConstant.PARAM_GO_TO_MINE.equals(selectTab)) {
            mIsGoToMine = true;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        LibDeprecatedLogger.d("onResume");


        if (XXPermissions.isGranted(this, Permission.Group.STORAGE)) {
            getTopData();
            if (mHelper.getIsLogin()) {
                //refreshUser
                UserApi.getUserAdPayMsg(this);
            }
            mBinding.topBar.refreshTopData();
        }
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        AppLogger.d("onSaveInstanceState");
        outState.putInt("launcher_tab_position", mCurrentPosition);
    }

    int restorePosition = -1;

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        AppLogger.d("onRestoreInstanceState");

        restorePosition = savedInstanceState.getInt("launcher_tab_position", -1);
        initData();
    }

    private void initView() {
        mBinding.vpContent.setOffscreenPageLimit(2);

        mArrayObjectAdapter = new ArrayObjectAdapter(new TitlePresenter());
        mItemBridgeAdapter = new LauncherTabBridgeAdapter(mArrayObjectAdapter);
        mViewPagerAdapter = new ContentViewPagerAdapter(getSupportFragmentManager());
        mBinding.hgTitle.setAdapter(mItemBridgeAdapter);

        mHelper = LoginUserInformationHelper.getHelper(getApplicationContext());

        mRootView = ((ViewGroup) findViewById(android.R.id.content)).getChildAt(0);

        mNewAnim = ValueAnimator.ofInt(0, 255);

        mLauncherHandler = new LauncherHandler(this);
        mLlPermissionsTips = (LinearLayout) findViewById(R.id.ll_permissions_tips);


    }

    private void initData() {
        startImageInfoUpload();
        requestLauncherConfig();
        launcherViewModel.getChannelList(this);
        try {
            launcherViewModel.getHomeChannelData().observe(this, result -> {
                if (result instanceof ResultData.Success) {
                    HomeTab channelList = ((ResultData.Success<HomeTab>) result).getData();
                    if (channelList == null || channelList.getData() == null || channelList.getData().size() < 1) {
//                ToastUtils.showToast(LauncherActivity.this, "错误码: " + channelList.getStatus());
                        LibDeprecatedLogger.d("错误码: " + channelList.getStatus());
                        setErrorTVVisible();
                        return;
                    }

                    com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem tabItem = null;
                    dataBeans = channelList.getData();
                    for (com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem data : channelList.getData()) {
                        if (data.getType() == Constant.TYPE_CAROUSEL) {
                            tabItem = data;
                        }
                    }

                    if (tabItem != null) {
                        dataBeans.remove(tabItem);
                    }

                    for (com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem data : channelList.getData()) {
                        if (data.isFirstPage() == 1) {
                            Constant.TAG_FEATURE_POSITION = channelList.getData().indexOf(data);
                            data.setSelected(true);
                        }
                        if (data.getType() == 107) {
                            TAG_MY_POSITION = channelList.getData().indexOf(data);
                        }
                    }

                    markStartEndPositions(channelList.getData());

                    //导航类型之间间隔调整
                    getHorizontalGridView().addItemDecoration(new RecyclerView.ItemDecoration() {
                        @Override
                        public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                            int itemPosition = parent.getChildAdapterPosition(view);
                            if (dataBeans != null && itemPosition < dataBeans.size()) {
                                if (dataBeans.get(itemPosition).getPosition() == START) {
                                    outRect.left = getResources().getDimensionPixelSize(R.dimen.x23);
                                }
                            }
                        }
                    });
                    setUI();

                    adjustTabMargin();
                    getTopData();
                } else if (result instanceof ResultData.Error) {
                    setErrorTVVisible();
                }

            });
        } catch (Exception e) {
            setErrorTVVisible();
        }


    }

    private void adjustTabMargin() {
        for (int i = 0; i < getHorizontalGridView().getChildCount(); i++) {
            if (dataBeans != null && dataBeans.size() > 0 && dataBeans.size() == getHorizontalGridView().getChildCount()) {
                if (dataBeans.get(i).getPosition() == START) {
                    View view = getHorizontalGridView().getChildAt(i);
                    ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();

                    int newMarginLeft = getResources().getDimensionPixelSize(R.dimen.x30); // 设置新的左边 margin 大小
                    layoutParams.leftMargin += newMarginLeft;
                    view.setLayoutParams(layoutParams);
                }
            }
        }
    }

    public static final int SINGLE = 2;
    public static final int START = 1;
    public static final int MIDDLE = 0;
    public static final int END = -1;

    public void markStartEndPositions(List<com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem> tabItems) {
        int startPosition = END;

        for (int i = 0; i < tabItems.size() - 1; i++) {
            com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem currentItem = tabItems.get(i);
            if (i + 1 < tabItems.size()) {
                com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem nextItem = tabItems.get(i + 1);
                // 检查当前项和下一项的feeType是否不同
                if (currentItem.getFeeType() != nextItem.getFeeType()) {
                    //不同，标记当前项为结束位置
                    if (startPosition == END) {
                        //只有一个设置当前状态为2
                        tabItems.get(i).setPosition(SINGLE);
                    } else {
                        tabItems.get(i).setPosition(END);
                        startPosition = END;
                    }
                } else {
                    //相同
                    if (startPosition == END) {
                        //起始位置未标记，标记当前项为起始位置
                        tabItems.get(i).setPosition(START);
                        startPosition = i;
                    } else {
                        //起始位置已标记，标记当前项为中间位置
                        tabItems.get(i).setPosition(MIDDLE);
                    }
                }
            } else {
                //最后一个，设置为结尾
                tabItems.get(i).setPosition(END);
            }
        }
    }

    private void initListener() {
        mBinding.hgTitle.addOnChildViewHolderSelectedListener(onChildViewHolderSelectedListener);
    }

    private void getTopData() {
        NetworkApi.getTopData(new DisposableObserver<TopInfo>() {
            @Override
            public void onNext(TopInfo value) {
                LibDeprecatedLogger.d("onNext: " + value.toString());
                mBinding.topBar.setData(value);
            }

            @Override
            public void onError(Throwable e) {
                e.printStackTrace();
                LibDeprecatedLogger.d("onError: " + e);
            }

            @Override
            public void onComplete() {

            }
        }, mHelper.getLoginPassport(), mHelper.getLoginToken());
    }

    private void setUI() {
        ArrayObjectAdapter adapter = getArrayObjectAdapter();
        if (adapter != null) {
            adapter.addAll(0, dataBeans);
            initViewPager(dataBeans);
            HorizontalGridView horizontalGridView = getHorizontalGridView();
            int flag;
            if (mIsGoToMine) {
                flag = TAG_MY_POSITION;
            } else if (restorePosition != -1) {
                flag = restorePosition;
            } else {
                flag = Constant.TAG_FEATURE_POSITION;
            }
            if (dataBeans.size() > flag) {
                if (horizontalGridView != null) {
                    horizontalGridView.setSelectedPosition(flag);
                    setCurrentItemPosition(flag);

                    mCurrentTabType = dataBeans.get(flag).getType();
                    mCurrentTabCode = dataBeans.get(flag).getId();

                    if (Boolean.TRUE.equals(mGrayMaps.get(mCurrentTabCode))) {
                        changeGray(true);
                    }

                }
            } else if (dataBeans.size() > 0) {
                if (getHorizontalGridView() != null) {
                    horizontalGridView.setSelectedPositionSmooth(0);
                }
            }
            mIsGoToMine = false;
            restorePosition = -1;
        }
    }


    private void initViewPager(final List<com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem> dataBeans) {
        mViewPagerAdapter.setData(dataBeans);
        mBinding.vpContent.setAdapter(mViewPagerAdapter);

        mBinding.vpContent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                LibDeprecatedLogger.d("onPageSelected position: " + position + ", lastPos : " + mLastPosition + " , currentPos : " + mCurrentPosition);


                isSkipTabFromViewPager = true;

                if (position == mLastPosition) {
                    //从viewpager中滑动的
                    refreshTabLayout();
                }

                if (position != mCurrentPageIndex) {
                    mBinding.hgTitle.setSelectedPosition(position);
                }

                if (mSkinMaps.get(dataBeans.get(position).getType()) == null) {
                    if (dataBeans.get(position).getType() == Constant.TYPE_VIP) {
                        getRootView().setBackgroundResource(R.drawable.launcher_vip_bg);
                    } else {
                        getRootView().setBackgroundResource(R.drawable.launcher_bg);
                    }
                    hintNew();
                } else {
                    showNewWithAnim(dataBeans.get(position).getType());
                }

                if (Boolean.TRUE.equals(mGrayMaps.get((int) dataBeans.get(position).getId()))) {
                    changeGray(true);
                } else {
                    changeGray(false);
                }

                if (mOldPosition == -1) {
                    HashMap<String, String> pathInfo = new HashMap<String, String>();
                    pathInfo.put("pageId", StringUtil.toString(dataBeans.get(position).getId()));

                    HashMap<String, String> objectInfo = new HashMap<String, String>();
                    objectInfo.put("type", "page");
                    objectInfo.put("id", StringUtil.toString(dataBeans.get(position).getId()));

                    HashMap<String, String> memoInfo = new HashMap<String, String>();
                    memoInfo.put("lastPage", "-1");
                    memoInfo.put("stayTime", "0");

                    RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"),
                            pathInfo,
                            objectInfo,
                            memoInfo);

                } else {
                    HashMap<String, String> pathInfo = new HashMap<String, String>();
                    pathInfo.put("pageId", StringUtil.toString(dataBeans.get(position).getId()));

                    HashMap<String, String> objectInfo = new HashMap<String, String>();
                    objectInfo.put("type", "page");
                    objectInfo.put("id", StringUtil.toString(dataBeans.get(position).getId()));

                    HashMap<String, String> memoInfo = new HashMap<String, String>();
                    memoInfo.put("lastPage", StringUtil.toString(dataBeans.get(mOldPosition).getId()));
                    memoInfo.put("stayTime", StringUtil.toString(System.currentTimeMillis() - mOldTime));

                    RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"),
                            pathInfo,
                            objectInfo,
                            memoInfo);
                }

                mOldTime = System.currentTimeMillis();
                mOldPosition = position;

            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });

    }

    public void setErrorTVVisible() {
        if (mBinding.tvError.getVisibility() == View.INVISIBLE) {
            mBinding.tvError.setVisibility(View.VISIBLE);
            mBinding.tvError.setText(getString(R.string.home_loading_error));
        }
    }


    private void refreshTabLayout() {

        if (dataBeans != null) {
            getArrayObjectAdapter().setItems(dataBeans, new DiffCallback<HomeTab.TabItem>() {

                @Override
                public boolean areItemsTheSame(@NonNull com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem oldItem, @NonNull com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem newItem) {
                    return true;
                }

                @Override
                public boolean areContentsTheSame(@NonNull HomeTab.TabItem oldItem, @NonNull HomeTab.TabItem newItem) {
//                        AppLogger.d("return name"+ ((HomeTab.TabItem)oldItem).name + " " + !(((HomeTab.TabItem)oldItem).index == mCurrentPosition || ((HomeTab.TabItem) oldItem).index == mLastPosition));
                    return !(((HomeTab.TabItem) oldItem).getIndex() == mCurrentPosition || ((HomeTab.TabItem) oldItem).getIndex() == mLastPosition);
                }
            });
        }
    }

    private final OnChildViewHolderSelectedListener onChildViewHolderSelectedListener
            = new OnChildViewHolderSelectedListener() {
        @Override
        public void onChildViewHolderSelected(RecyclerView parent, RecyclerView.ViewHolder child, int position, int subposition) {
            super.onChildViewHolderSelected(parent, child, position, subposition);
            LibDeprecatedLogger.d("onChildViewHolderSelected mCurrentPosition :" + mCurrentPosition + " mLastPosition : " + mLastPosition);
            if (position == mCurrentPosition || position == -1 || dataBeans == null || position >= dataBeans.size()) {
                return;
            }
            mLastPosition = mCurrentPosition;
            mCurrentPosition = position;


            for (int i = 0; i < dataBeans.size(); i++) {
                if (i != mCurrentPosition) {
                    dataBeans.get(i).setSelected(false);
                } else {
                    dataBeans.get(position).setSelected(true);
                }
            }

            for (int i = 0; i < dataBeans.size(); i++) {
                dataBeans.get(i).setIndex(i);
            }

            if (getHorizontalGridView().getScrollState() == RecyclerView.SCROLL_STATE_IDLE
                    && (!getHorizontalGridView().isComputingLayout())) {
            }

            mLauncherHandler.removeMessages(LauncherHandler.SELECT);

            Message msg = Message.obtain();
            msg.what = LauncherHandler.SELECT;
            msg.arg1 = mCurrentPosition;
            mLauncherHandler.sendMessageDelayed(msg, LauncherHandler.SELECT_DURATION);

            if (dataBeans != null) {
                mCurrentTabType = dataBeans.get(position).getType();
                mCurrentTabCode = dataBeans.get(position).getId();
            }

        }
    };

    private void setCurrentItemPosition(int position) {
        LibDeprecatedLogger.v("setCurrentItemPosition : " + position + ", lastPos : " + mLastPosition + " ， currentPos : " + mCurrentPosition);
        if (position != mLastPosition) {

            mCurrentPageIndex = position;
            mBinding.vpContent.setCurrentItem(position);
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        BootFragment fragment = (BootFragment) getSupportFragmentManager().findFragmentById(R.id.fragment_container);
        LibDeprecatedLogger.d("onKeyDown" +
                " mBinding.videoRoot.getVisibility() " + mBinding.videoRoot.getVisibility() +
                " bootFragment " + fragment);

        //bootfragment is visible 启动广告响应按键
        if (fragment != null) {
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_CENTER || event.getKeyCode() == KeyEvent.KEYCODE_ENTER) {
                fragment.startPayOrVideoDetailActivity();
                return true;
            }

            if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
                LibDeprecatedLogger.d("onKeyDown KEYCODE_BACK fragment.flag " + fragment.flag + " fragment.isCanBack " + fragment.isCanBack);
                if (fragment.isCanBack) {
                    LibDeprecatedLogger.d("onHideFragment startLauncher 1");
                    AdRequestFactory.getInstants().reportStartPageAdFinish(ILoader.PageAdState.SKIP);
                    fragment.releaseVideoView();
                    onHideFragment(isTopView, topViewUrl);
//                    Advert.getInstance().reportPageAdFinish(fragment.mAdCommon, ILoader.PageAdState.SKIP);
                    if (launcherViewModel != null && dataBeans != null && mViewPagerAdapter.getFragment(mCurrentPageIndex) != null
                            && mViewPagerAdapter.getFragment(mCurrentPageIndex) instanceof HomeContentFragment) {
                        ((HomeContentFragment) mViewPagerAdapter.getFragment(mCurrentPageIndex)).refreshFirstScreenContent();
                    }
                }
                //在广告页面用户不能主动退出app
                return true;
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                if (fragment.flag == 0) {
                    onHideFragment(isTopView, topViewUrl);
                    LibDeprecatedLogger.d("onHideFragment startLauncher 2");
                }
                return true;
            }
        } else {
            //Launcher响应
            //当前topview广告正在播放，响应所有按键
            if (mBinding.videoRoot.getVisibility() == View.VISIBLE) {
                AdRequestFactory.getInstants().reportStartPageAdFinish(ILoader.PageAdState.CLOSE);
                releasePlayer();
                finishTopViewOnLauncher();
//                Advert.getInstance().reportPageAdFinish(adCommon, ILoader.PageAdState.CLOSE);
                return true;
            } else {
                if (event.getAction() == KeyEvent.ACTION_DOWN && keyCode == event.KEYCODE_BACK) {

                    ExitAppDialogNew mExitDialog = new ExitAppDialogNew(this);
                    if (dataBeans != null && dataBeans.size() > mCurrentPageIndex) {
                        mExitDialog.setPageId(dataBeans.get(mCurrentPageIndex).getId());
                    }
                    mExitDialog.setExitAppListener(new ExitAppDialogNew.ExitAppListener() {
                        @Override
                        public void exitApp() {
                            ImageInfoManager.INSTANCE.saveCurrentQueue();
//                    moveTaskToBack(true);
                            SohuAppUtil.exitApp(LauncherActivity.this);
                        }

                        @Override
                        public void onDismiss() {
//                                mExitDialog = null;
                        }
                    });
                    if (!mExitDialog.isShowing()) {
                        mExitDialog.show();
                    } else {
                        mExitDialog.dismiss();
                    }
                    return true;
                }
            }
        }

        return super.onKeyDown(keyCode, event);
    }


    public void upToTopBar() {
        getTopViewBar().zoomIn();
    }

    @Override
    public void onFragmentInteraction(Uri uri) {
        switch (uri.toString()) {
            case Constant.URI_HIDE_TITLE:
                handleTitleVisible(false);
                break;
            case Constant.URI_SHOW_TITLE:
                handleTitleVisible(true);
                break;
        }
    }

    private void handleTitleVisible(boolean isShow) {
        if (isShow) {
            if (mBinding.hgTitle.getVisibility() != View.VISIBLE) {
                mBinding.hgTitle.setVisibility(View.VISIBLE);
            }
        } else {
            if (mBinding.hgTitle.getVisibility() != View.GONE) {
                mBinding.hgTitle.setVisibility(View.GONE);
            }
        }
    }


    @Override
    public void onTopViewBarInteraction(Uri uri) {
        if (uri.toString().equals(Constant.URI_CLICK_MY)) {
            if (TAG_MY_POSITION != -1) {
                //此处刷新数据是为了  修复 OOTPRO-1071
                for (int i = 0; i < dataBeans.size(); i++) {
                    if (i != TAG_MY_POSITION) {
                        dataBeans.get(i).setSelected(false);
                    } else {
                        dataBeans.get(TAG_MY_POSITION).setSelected(true);
                    }
                }

                getHorizontalGridView().setSelectedPositionSmooth(TAG_MY_POSITION);
                getHorizontalGridView().requestFocus();
                refreshTabLayout();
//                setCurrentItemPosition(Constant.TAG_MY_POSITION);
            }
        }
    }

    public void setBackground(int id, String url) {
        mSkinMaps.put(id, url);
        if (id == mCurrentTabCode) {
            showNewWithAnim(id);
        }
    }

    public void showNewWithAnim(int id) {

        if (mSkinMaps.get(id) == null) {
            return;
        }
        Glide.with(LauncherActivity.this).
                load(mSkinMaps.get(id)).
                transition(DrawableTransitionOptions.with(drawableCrossFadeFactory)).
                into(mBinding.launcherBg);
        mBinding.launcherBg.setVisibility(View.VISIBLE);
        mNewAnim.setDuration(700);
        mNewAnim.addUpdateListener(animation -> {
            int currentValue = (Integer) animation.getAnimatedValue();
            mBinding.launcherBg.setImageAlpha(currentValue);
            mBinding.launcherBg.requestLayout();
        });
        mNewAnim.start();
    }

    public void hintNew() {
        mNewAnim.cancel();
        mBinding.launcherBg.setVisibility(View.GONE);
    }

    public void backToPosition(int pos) {
        if (getHorizontalGridView().getVisibility() != View.VISIBLE) {
            getHorizontalGridView().post(() -> {
                getHorizontalGridView().setVisibility(View.VISIBLE);
            });
        }
        for (int i = 0; i < dataBeans.size(); i++) {
            if (i != pos) {
                dataBeans.get(i).setSelected(false);
            } else {
                dataBeans.get(pos).setSelected(true);
            }
        }

        for (int i = 0; i < dataBeans.size(); i++) {
            dataBeans.get(i).setIndex(i);
        }
        getHorizontalGridView().post(() -> {
            getHorizontalGridView().setSelectedPosition(pos);
            getHorizontalGridView().requestFocus();
            refreshTabLayout();
            if (getTopViewBar().isZoomOut()) {
                getTopViewBar().zoomOut();
            }
        });

    }

    public void backToFirstPage() {
        LibDeprecatedLogger.d("backFirstPage");
        setCurrentItemPosition(Constant.TAG_FEATURE_POSITION);

    }

    public void backToMyPage() {
        LibDeprecatedLogger.d("backToMyPage");
        backToPosition(TAG_MY_POSITION);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Subscribe
    public void onEventMainThread(LoginSuccessEvent event) {
        //Receive EventBus about login and reset user image to custom picture
        LibDeprecatedLogger.d("onEventMainThread(LoginSuccessEvent event)");

        if (event == null) {
            return;
        }
        SyncHistoryAndCollectionUtil.uploadLocalHistoryAndCollection(getApplicationContext());
    }

    @Override
    public void addUpdateEvent(UpdateInfo updateInfo) {
        mUpdateHelper.showUpdateDialog(this, updateInfo, false);
        if (updateInfo == null) {
            AppLogger.d(TAG, "updateInfo == null");
            return;
        }
        boolean hasClickUpdateButton = PrefUtil.getBoolean(AppConstants.KEY_HAS_CLICK_UPDATE_BUTTON, false);
        if (hasClickUpdateButton) {
            if (updateInfo.data == null || updateInfo.data.status == 0) {
                //已经是最新版本
                AppLogger.d(TAG, "already is newversion");
                RequestManager.getInstance().onAllEvent(new EventInfo(10212, "slc"), null, null,
                        null);
                PrefUtil.putBoolean(AppConstants.KEY_HAS_CLICK_UPDATE_BUTTON, false);
            } else {
                //有新版版本
                AppLogger.d(TAG, "updateInfo.data.status ? " + updateInfo.data.status);
                RequestManager.getInstance().onAllEvent(new EventInfo(10213, "slc"), null, null,
                        null);
                PrefUtil.putBoolean(AppConstants.KEY_HAS_CLICK_UPDATE_BUTTON, false);
            }
        }
    }

    @Override
    public void updateDownloadProgress(int value) {
        mUpdateHelper.updateProgress(value);
    }

    @Override
    public void responseDownloadResult(boolean result, int updateStatus, int networkResponseCode) {
        mUpdateHelper.dealDownloadResult(this, result, updateStatus, networkResponseCode);
    }

    @Override
    public void initAboutUI(AboutInfo aboutInfo) {

    }

    @Override
    public void onExist() {
        LibDeprecatedLogger.d("onExist requestTeenModePopDialog");
        requestTeenModePopDialog();
    }

    boolean forceRefresh;

    //是否为TOPView
    public boolean isTopView() {
        if (isTopView) {
            if (forceRefresh) {
                return false;
            }
        }
        return isTopView;
    }
}
