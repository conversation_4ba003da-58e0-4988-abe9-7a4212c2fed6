package com.sohuott.tv.vod.worker

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.MessageQueue
import com.drake.net.Post
import com.drake.net.okhttp.trustSSLCertificate
import com.drake.net.utils.scopeNet
import com.google.gson.Gson
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohuott.tv.base_room.entity.ImageInfo
import com.sohuott.tv.base_room.manager.ImageInfoManager
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.Util
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import okhttp3.ConnectionSpec
import okhttp3.RequestBody.Companion.toRequestBody

class UploadHandler(val context: Context): MessageQueue.IdleHandler {
    val gson = Gson()

    private val scope = CoroutineScope(Dispatchers.IO)

    private val handler = Handler(Looper.getMainLooper())
    private val intervalMillis: Long = 10 * 60 * 1000  // 30分钟


    private val runnable = Runnable {
        scope.launch {
            ImageInfoManager.getAllAndClear().fold(
                onSuccess = { imageInfos ->
                    LibDeprecatedLogger.d("UploadWorker imageInfos=${imageInfos.size}")
                    uploadToServer(imageInfos)
                },
                onFailure = {
                }
            )
        }

        try {
            // 等待下一次空闲时执行
            Looper.myQueue().addIdleHandler(this@UploadHandler)
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    override fun queueIdle(): Boolean {

        handler.postDelayed(runnable, intervalMillis)
        return false
    }

    fun startWork() {
        try {
            Looper.myQueue().addIdleHandler(this)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

//    fun stopWork() {
//        scope.cancel()
//        handler.removeCallbacks(runnable)
//        Looper.myQueue().removeIdleHandler(this)
//    }

    private suspend fun uploadToServer(imageInfos: List<ImageInfo>): Boolean {
        if (imageInfos.isEmpty()) return true
        // 将imageInfos转换为适当的JSON结构
        val reportList = imageInfos.map { info ->
            mapOf(
                "transferSize" to info.size,
                "duration" to info.duration,
                "initiatorType" to "img",
                "type" to "resource",
                "entryType" to "VideoDetail",
                "startTime" to System.currentTimeMillis(), // 或其他适当的值
                "url" to info.url
            )
        }

        val requestBody = mapOf(
            "reportList" to reportList,
            "timestamp" to System.currentTimeMillis(),
            "type" to "perf",
            "referer" to "https://api.ott.tv.sohu.com",
            "uuid" to  UserInfoHelper.getGid()// 或其他适当的值
        )


        // 在这里实现上传逻辑
        scopeNet {
            Post<String>("${RetrofitApi.get().retrofitHost.apiHost}img/report"){
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(listOf(
                        ConnectionSpec.MODERN_TLS,
                        ConnectionSpec.COMPATIBLE_TLS,
                        ConnectionSpec.CLEARTEXT))
                }
                body = gson.toJson(requestBody).toRequestBody()
                addHeader("api_key", Util.getSohuApiKey(context))
                addHeader("gid", UserInfoHelper.getGid()?:"")
                addHeader("Service_version", Constant.SERVICE_VERSION)
                addHeader("referer", "https://api.ott.tv.sohu.com")
            }.await()
        }
        return true
    }
}