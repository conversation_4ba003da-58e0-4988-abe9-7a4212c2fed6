package com.sohuott.tv.vod.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by fenglei on 17-6-15.
 */

public class TNineKeyboardLayout extends RelativeLayout implements View.OnFocusChangeListener, View.OnKeyListener {

    private String mPageName = "6_search";
    public MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    private  static final String[] KEYBOARD_NUM = {"0", "1", "2", "ABC", "3", "DEF",
            "4", "GHI", "5", "JKL", "6", "MNO", "7", "PQRS", "8", "TUV", "9", "WXYZ"};

    private List<TNineKeyboardItemView2> tNineKeyboardItemView2List;
    private OnClickTNineKeyboardListener mOnClickTNineKeyboardListener;
    private OnFocusDownListener mOnFocusDownListener;

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
            if(mOnFocusDownListener != null) {
                mOnFocusDownListener.onFocusDown();
                return true;
            }
        }
        return false;
    }

    public interface OnClickTNineKeyboardListener {
        void onClickTNineKeyboard(String content);
    }

    public interface OnFocusDownListener {
        void onFocusDown();
    }

    public TNineKeyboardLayout(Context context) {
        super(context);
        init(context);
    }

    public TNineKeyboardLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public TNineKeyboardLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public void setPageName(String pageName){
        this.mPageName = pageName;
        for(int i = 0; i < getChildCount(); i++) {
            TNineKeyboardItemView2 tNineKeyboardItemView2 = (TNineKeyboardItemView2) getChildAt(i);
            tNineKeyboardItemView2.setPageName(mPageName);
        }
    }

    protected void setCustomLayout(Context context){
        LayoutInflater.from(context).inflate(R.layout.tnine_keyboard_layout, this, true);
    }

    private void init(Context context) {
        //LayoutInflater.from(context).inflate(R.layout.tnine_keyboard_layout, this, true);
        setCustomLayout(context);
        tNineKeyboardItemView2List = new ArrayList<>();
        for(int i = 0; i < getChildCount(); i++) {
            TNineKeyboardItemView2 tNineKeyboardItemView2 = (TNineKeyboardItemView2) getChildAt(i);
            tNineKeyboardItemView2.initData(i, KEYBOARD_NUM[i * 2], KEYBOARD_NUM[i * 2 + 1]);
            tNineKeyboardItemView2.setOnClickTNineKeyboardListener(mOnClickTNineKeyboardListener);
            tNineKeyboardItemView2.setOnFocusChangeListener(this);
            tNineKeyboardItemView2List.add(tNineKeyboardItemView2);
        }

        TNineKeyboardItemView2 tNineKeyboardItemView7 = findViewById(R.id.view7);
        TNineKeyboardItemView2 tNineKeyboardItemView8 = findViewById(R.id.view8);
        TNineKeyboardItemView2 tNineKeyboardItemView9 = findViewById(R.id.view9);

        tNineKeyboardItemView7.setOnKeyListener(this);
        tNineKeyboardItemView8.setOnKeyListener(this);
        tNineKeyboardItemView9.setOnKeyListener(this);

        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false);
        }
    }

    public void setInitFocus() {
        getChildAt(4).requestFocus();
    }

    public void setFocusView(String content) {
        for(int i = 0; i < getChildCount(); i++) {
            TNineKeyboardItemView2 tNineKeyboardItemView2 = (TNineKeyboardItemView2) getChildAt(i);
            if(tNineKeyboardItemView2.hasContent(content)) {
                tNineKeyboardItemView2.requestFocus();
                break;
            }
        }
    }

    public void setOnFocusDownListener(OnFocusDownListener listener) {
        mOnFocusDownListener = listener;
    }

    public void setOnClickTNineKeyboardListener(OnClickTNineKeyboardListener listener) {
        mOnClickTNineKeyboardListener = listener;
        for(int i = 0; i < getChildCount(); i++) {
            TNineKeyboardItemView2 tNineKeyboardItemView2 = (TNineKeyboardItemView2) getChildAt(i);
            tNineKeyboardItemView2.setOnClickTNineKeyboardListener(listener);
        }
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        for(int i = 0; i < getChildCount(); i++) {
            TNineKeyboardItemView2 tNineKeyboardItemView2 = (TNineKeyboardItemView2) getChildAt(i);
            tNineKeyboardItemView2.setFocusBorderView(focusView);
        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        //press back
        if(event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            if(mOnClickTNineKeyboardListener != null) {
                mOnClickTNineKeyboardListener.onClickTNineKeyboard("back");
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    public void requestBottomLayout() {
        findViewById(R.id.view8).requestFocus();
    }
}
