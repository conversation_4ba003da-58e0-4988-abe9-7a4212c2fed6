package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.load.model.LazyHeaders;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.FontUtils;
import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by XiyingCao on 16-3-14.
 */
public class ListActorAdapter extends RecyclerView.Adapter<ListActorAdapter.ViewHolder> {

    private RecyclerView mParent;
    private FocusBorderView mFocusBorderView;
    private int mSelctedPos = 0;
    private ArrayList<ListAlbumModel> mModels;
    private int mActorId;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;
    private Context mContext;
    HashMap<String, String> mPathInfo;

    public ListActorAdapter(int id, RecyclerView recyclerView, HashMap<String, String> pathInfo) {
        mModels = new ArrayList<>();
        mActorId = id;
        mParent = new WeakReference<>(recyclerView).get();
        mContext = recyclerView.getContext().getApplicationContext();
        mPathInfo = pathInfo;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View modelView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_list_actor, viewGroup, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(modelView);
    }

    @Override
    public void onBindViewHolder(ViewHolder viewHolder, int position) {
        ListAlbumModel model = mModels.get(position);
        int size = mModels.size();
        if (size == 0 && position >= size) {
            return;
        }
        if (position == 0) {
            if (mModels.size() > 1 && model.tvYear == mModels.get(1).tvYear) {
                setHeadTimeImg(viewHolder);
            } else {
                setWholeTimeImg(viewHolder);
            }
        } else if (position == size -1) {
            ListAlbumModel preLastModel = mModels.get(position - 1);
            if (model.tvYear == preLastModel.tvYear) {
                setTailTimeImg(viewHolder);
            } else {
                setWholeTimeImg(viewHolder);
            }
        } else {
            ListAlbumModel preModel = mModels.get(position - 1);
            ListAlbumModel NextModel = mModels.get(position + 1);
            if (model.tvYear == preModel.tvYear) {
                if (model.tvYear == NextModel.tvYear) {
                    setMiddleTimeImg(viewHolder);
                } else {
                    setTailTimeImg(viewHolder);
                }
            } else {
                if (model.tvYear == NextModel.tvYear) {
                    setHeadTimeImg(viewHolder);
                } else {
                    setWholeTimeImg(viewHolder);
                }
            }
        }

        if (position == 0 || model.tvYear != mModels.get(position - 1).tvYear) {
            if (model.tvYear == 0) {
                viewHolder.mYear.setText("暂无");
            } else {
                viewHolder.mYear.setText(new Integer(model.tvYear).toString());
                FontUtils.setTypeface(mContext, viewHolder.mYear);
            }
        } else {
            viewHolder.mYear.setText("");
        }

        final ViewHolder vh = viewHolder;
        GlideUrl glideUrl = new GlideUrl(model.tvVerPic, new LazyHeaders.Builder()
                .addHeader("ImageTag", "ListActor")
                .build());
        Glide.with(mContext)
                .load(glideUrl)
                .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                .into(vh.mIvTypeThreePoster);
        vh.mTvTypeThreeName.setText(model.tvName);

//        vh.mTVFocusEpisode.setText(Util.getHintTV(item));
        vh.mTvFocusName.setText(model.tvName);
        vh.mTvFocusDesc.setText(model.tvComment);

//        if (model.channelType == Constant.TYPE_VIP) {
//            vh.mFocusRoot.setBackgroundResource(R.drawable.item_type_one_vip_focus_selector);
//            vh.mFocusView.setBackgroundResource(R.drawable.bg_vip_focus_selector);
//        } else {
//            vh.mIvTypeThreePoster.setCornerTypeWithType(model.tvIsFee,
//                    model.tvIsEarly,
//                    model.useTicket,
//                    model.paySeparate,
//                    model.cornerType);
//        }

//        vh.mLayoutAlbum.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//            @Override
//            public void onFocusChange(View v, boolean hasFocus) {
//                mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
//                if (hasFocus) {
//                    vh.mFocusRoot.setVisibility(View.VISIBLE);
//                    vh.mTVFocusEpisode.setVisibility(View.VISIBLE);
//                    vh.mEpisodeBg.setVisibility(View.VISIBLE);
//                    vh.mNameBg.setVisibility(View.INVISIBLE);
//                    vh.mTvTypeThreeName.setVisibility(View.GONE);
//                    vh.mFocusPlay.setVisibility(View.VISIBLE);
//                    vh.mFocusPlay.showWaveAnimation();
//                } else {
//                    vh.mFocusRoot.setVisibility(View.GONE);
//                    vh.mTVFocusEpisode.setVisibility(View.GONE);
//                    vh.mEpisodeBg.setVisibility(View.GONE);
//                    vh.mNameBg.setVisibility(View.VISIBLE);
//                    vh.mTvTypeThreeName.setVisibility(View.VISIBLE);
//                    vh.mFocusPlay.setVisibility(View.GONE);
//                    vh.mFocusPlay.cancelWaveAnimation();
//                }
//            }
//        });

//        viewHolder.mTitle.setText(model.tvName);
//        viewHolder.mSubTitle.setText(model.tvComment);
//        viewHolder.mImageView.setCornerHeightRes(R.dimen.y33);
//        viewHolder.mImageView.setCornerTypeWithType(model.tvIsFee, model.tvIsEarly, model.useTicket,model.paySeparate, model.cornerType);
//        viewHolder.mImageView.setImageRes(model.albumExtendsPic_240_330);
//
//        if (mSelctedPos == position) {
//            viewHolder.mLayoutAlbum.requestFocus();
//            viewHolder.mTitle.setSelected(true);
//            viewHolder.mTitle.setMarqueeRepeatLimit(-1);
//            viewHolder.mTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
//        } else {
//            viewHolder.mTitle.setSelected(false);
//            viewHolder.mTitle.setEllipsize(TextUtils.TruncateAt.END);
//        }
    }

    private void setWholeTimeImg(ViewHolder viewHolder) {
        viewHolder.mLeftTimeImg.setVisibility(View.INVISIBLE);
        viewHolder.mTimeImg.setVisibility(View.VISIBLE);
        viewHolder.mTimeImg.setImageResource(R.drawable.time_whole);
        viewHolder.mRightTimeImg.setVisibility(View.INVISIBLE);
        viewHolder.mHeadLayout.setVisibility(View.GONE);
        viewHolder.mTailLayout.setVisibility(View.GONE);
    }

    private void setHeadTimeImg(ViewHolder viewHolder) {
        viewHolder.mLeftTimeImg.setVisibility(View.INVISIBLE);
        viewHolder.mTimeImg.setVisibility(View.GONE);
        viewHolder.mRightTimeImg.setVisibility(View.VISIBLE);
        viewHolder.mHeadLayout.setVisibility(View.VISIBLE);
        viewHolder.mTailLayout.setVisibility(View.GONE);
    }

    private void setTailTimeImg(ViewHolder viewHolder) {
        viewHolder.mLeftTimeImg.setVisibility(View.VISIBLE);
        viewHolder.mTimeImg.setVisibility(View.GONE);
        viewHolder.mRightTimeImg.setVisibility(View.INVISIBLE);
        viewHolder.mHeadLayout.setVisibility(View.GONE);
        viewHolder.mTailLayout.setVisibility(View.VISIBLE);
    }

    private void setMiddleTimeImg(ViewHolder viewHolder) {
        viewHolder.mLeftTimeImg.setVisibility(View.VISIBLE);
        viewHolder.mTimeImg.setVisibility(View.VISIBLE);
        viewHolder.mTimeImg.setImageResource(R.drawable.time_middle);
        viewHolder.mRightTimeImg.setVisibility(View.VISIBLE);
        viewHolder.mHeadLayout.setVisibility(View.GONE);
        viewHolder.mTailLayout.setVisibility(View.GONE);
    }

    @Override
    public int getItemCount() {
        if (mModels == null) {
            return 0;
        }
        return mModels.size();
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.mFocusBorderView = focusBorderView;
    }

    public void clear() {
        mModels.clear();
        notifyDataSetChanged();
    }

    public void add(ListAlbumModel model) {
        mModels.add(model);
        notifyDataSetChanged();
    }

    public void add(List<ListAlbumModel> models) {
        int size = getItemCount();
        this.mModels.addAll(models);
        notifyItemRangeInserted(size, models.size());
    }

    public void setActorId(int actorId) {
        mActorId = actorId;
    }


    public void setSelctedPos(int selctedPos) {
        mSelctedPos = selctedPos;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        View mCalander;
        TextView mYear;
        ImageView mTimeImg, mLeftTimeImg, mRightTimeImg;

        private View mLayoutAlbum;

        private final CornerTagImageView mIvTypeThreePoster;
        private final TextView mTvTypeThreeName, mTvFocusName, mTvFocusDesc;
        private LinearLayout mFocusRoot, mHeadLayout, mTailLayout;
        private RippleDiffuse mFocusPlay;
        private View mFocusView, mEpisodeBg, mNameBg;

        public ViewHolder(View view) {
            super(view);
            mCalander = view.findViewById(R.id.year_layout);
            mYear = (TextView) view.findViewById(R.id.year);
            mTimeImg = view.findViewById(R.id.time_img);
            mLeftTimeImg = view.findViewById(R.id.time_left_img);
            mRightTimeImg = view.findViewById(R.id.time_right_img);
            mHeadLayout = view.findViewById(R.id.head_layout);
            mTailLayout = view.findViewById(R.id.tail_layout);

            mLayoutAlbum = view.findViewById(R.id.album_layout);

            mIvTypeThreePoster = (CornerTagImageView) view.findViewById(R.id.iv_type_three_poster);
            mTvTypeThreeName = (TextView) view.findViewById(R.id.tv_type_three_name);
            mFocusRoot = (LinearLayout) view.findViewById(R.id.type_three_focus_root);
            mTvFocusName = (TextView) view.findViewById(R.id.type_three_focus_name);
            mTvFocusDesc = (TextView) view.findViewById(R.id.type_three_focus_desc);
            mFocusPlay = (RippleDiffuse) view.findViewById(R.id.type_three_focus_play);
            mFocusView = view.findViewById(R.id.type_three_focus);
            mEpisodeBg = view.findViewById(R.id.focus_episode_bg);
            mNameBg = view.findViewById(R.id.name_bg);

//            mTitle = (TextView) v.findViewById(R.id.video_title);
//            mSubTitle = v.findViewById(R.id.sub_title);
//            mImageView = (CornerTagImageView) v.findViewById(R.id.album_image);
//
//            mLayoutAlbum = (LinearLayout) v.findViewById(R.id.layout_album);
//            mLayoutAlbum.setTag(mTitle);
            mLayoutAlbum.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    final ListAlbumModel model =
                            mModels.get(getAdapterPosition());
                    ActivityLauncher.startVideoDetailActivity(v.getContext(), model.id, Constant.PAGE_ACTOR);

                    HashMap<String, String> objectInfo = new HashMap<>();
                    objectInfo.put("type", "video");
                    objectInfo.put("vid", String.valueOf(model.id));
                    objectInfo.put("playlistId", String.valueOf(model.tvVerId));
//                    type：视频
//                    vid：点击视频对应的id
//                    playlistId：视频所属专辑id
                    RequestManager.getInstance().onAllEvent(new EventInfo(10277, "clk"), mPathInfo, objectInfo, null);
                }
            });

            mLayoutAlbum.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
                    if (hasFocus) {
                        mFocusRoot.setVisibility(View.VISIBLE);
                        mEpisodeBg.setVisibility(View.VISIBLE);
                        mNameBg.setVisibility(View.INVISIBLE);
                        mTvTypeThreeName.setVisibility(View.GONE);
                        mFocusPlay.setVisibility(View.VISIBLE);
                        mFocusPlay.showWaveAnimation();
                    } else {
                        mFocusRoot.setVisibility(View.GONE);
                        mEpisodeBg.setVisibility(View.GONE);
                        mNameBg.setVisibility(View.VISIBLE);
                        mTvTypeThreeName.setVisibility(View.VISIBLE);
                        mFocusPlay.setVisibility(View.GONE);
                        mFocusPlay.cancelWaveAnimation();
                    }
                }
            });

//            mLayoutAlbum.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//                @Override
//                public void onFocusChange(View v, boolean hasFocus) {
//                    if (hasFocus) {
//                        mSubTitle.setVisibility(View.VISIBLE);
//                        mTitle.setSelected(true);
//                        mTitle.setMarqueeRepeatLimit(-1);
//                        mTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
//                        int pos = mParent.getChildAdapterPosition(mParent.getFocusedChild());
//                        if (pos >= 0 && pos < getItemCount()) {
//                            mSelctedPos = pos;
//                        }
//
//                        if (mParent.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
//                            if (mFocusBorderView != null) {
//                                mFocusBorderView.setFocusView(v);
//                                FocusUtil.setFocusAnimator(v, mFocusBorderView);
//                            }
//                        }
//                    } else {
//                        mSubTitle.setVisibility(View.GONE);
//                        mTitle.setSelected(false);
//                        mTitle.setEllipsize(TextUtils.TruncateAt.END);
//                        if (mFocusBorderView != null) {
//                            mFocusBorderView.setUnFocusView(v);
//                            FocusUtil.setUnFocusAnimator(v);
//                        }
//                    }
//                }
//            });
        }
    }
}

