package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.activity.ListEduUserRelatedActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.common.Listener;
import com.sohuott.tv.vod.account.login.Login;
import com.sohuott.tv.vod.account.user.UserApi;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ConsumeRecord;
import com.sohuott.tv.vod.lib.model.Logout;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.PostHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohu.lib_utils.StringUtil;
import java.util.List;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;
import static com.sohuott.tv.vod.activity.ListEduUserRelatedActivity.LIST_INDEX_MY;
import static com.sohuott.tv.vod.lib.utils.Constant.PRIVILEGE_ID_SOHU_MOVIE;

import androidx.annotation.Nullable;

/**
 * 教育我的页面
 */
public class EduUserFragment extends BaseFragment {

    private RelativeLayout mMyUserView;
    private LoadingView mLoadingView;

    private TextView tv_my_nickname, snm_account, tv_my_type, tv_my_login_method;
    private Button btn_my_login;

    private LoginUserInformationHelper mHelper;
    private int mSource;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View myView = inflater.inflate(R.layout.fragment_edu_user, container, false);
        mSource = Constant.HOME_SOURCE;

//        mIsEduVip = getArguments().getString("isEduVip", null);

        initView(myView);
        RequestManager.getInstance().onMyUserExposureEvent();
        setSubPageName("6_my_user");
        return myView;
    }

    @Override
    public void onResume() {
        super.onResume();
        initData();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mHelper = null;
    }

    private void initView(View view) {
        mMyUserView = (RelativeLayout) view.findViewById(R.id.layout_my_user);
        mLoadingView = (LoadingView) view.findViewById(R.id.detail_loading_view);

        tv_my_nickname = (TextView) view.findViewById(R.id.tv_my_nickname);
        tv_my_type = (TextView) view.findViewById(R.id.tv_my_type);
        tv_my_login_method = (TextView) view.findViewById(R.id.tv_my_login_method);
        btn_my_login = (Button) view.findViewById(R.id.btn_my_login);
        snm_account = (TextView) view.findViewById(R.id.snm_account);
        snm_account.setText("牌照账号 : SNM_" + UserInfoHelper.getGid());
        MyClickListener myClickListener = new MyClickListener();
        btn_my_login.setOnClickListener(myClickListener);

        btn_my_login.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && event.getAction() == KeyEvent.ACTION_DOWN) {
                    if (getActivity() instanceof ListEduUserRelatedActivity) {
                        ((ListEduUserRelatedActivity) getActivity()).focusLeftItem(LIST_INDEX_MY);
                    }
                    return true;
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
//                    btn_my_buy.requestFocus();
                    return true;
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                    return true;
                }
                return false;
            }
        });

    }

    private void initData() {
        mHelper = LoginUserInformationHelper.getHelper(getContext());
        if (mHelper.getIsLogin()) {
            //display loading view
            mLoadingView.setVisibility(View.VISIBLE);
            mMyUserView.setVisibility(View.GONE);
            btn_my_login.setVisibility(View.GONE);
            //request userInfo
            requestUserInfo();
        } else {
            btn_my_login.setVisibility(View.VISIBLE);
            displayUnloginView();
        }
    }

    private void requestUserInfo() {
        UserApi.refreshUser(getActivity(), new Listener<Login>() {
            @Override
            public void onSuccess(Login login) {
                LibDeprecatedLogger.d("requestUserInfo(): onNext().");
                if (mHelper == null) {
                    return;
                }
                if (login != null && login.getStatus() == 200 && login.getData() != null) {
                    Login.LoginData loginData = login.getData();
                    mHelper.putUserTicketNumber(loginData.getTicket() != null
                            ? Util.getTicketNumber(loginData.getTicket().getNumber()) : "0张");
                    List<Login.LoginData.Privilege> privilegeList = loginData.getPrivileges();
                    if (privilegeList != null && privilegeList.size() > 0) {
                        for (Login.LoginData.Privilege tmpPrivilege : privilegeList) {
                            if (tmpPrivilege != null && tmpPrivilege.getId() == PRIVILEGE_ID_SOHU_MOVIE
                                    && tmpPrivilege.getExpireIn() > 0) {
                                mHelper.putUserGrade(1);
                                if (StringUtil.isEmpty(mHelper.getUtype())) {
                                    mHelper.putUtype(loginData.getUTypeName());
                                }
                                mHelper.putVipTime(String.valueOf(tmpPrivilege.getTime()));
                                mHelper.putVipExpireIn(String.valueOf(tmpPrivilege.getExpireIn()));
                                displayVipView();
                                break;
                            }
                        }
                    }

                    if (!mHelper.isVip()) {
                        displayCommonView();
                    }
                } else {
                    loadLocalUserData();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestUserInfo(): onError()--" + e.getMessage());
                loadLocalUserData();
            }
        });

        /**
         * 获取用户是否教育用户VIP
         */
        NetworkApi.getUserConsumeRecord(Constant.EDU_CATE_ID_API, 1, mHelper.getLoginPassport(), mHelper.getLoginToken(), -1, 0, 0,
                1, new DisposableObserver<ConsumeRecord>() {
                    @Override
                    public void onNext(ConsumeRecord value) {
                        if (value == null || value.getData() == null) {
                            displayCourseVip(null);
                            return;
                        }
                        String isVip = value.getData().getIsCourseVip();
                        LibDeprecatedLogger.d("Get CourseVip info success!!!" + isVip);
                        displayCourseVip(isVip);
                    }

                    @Override
                    public void onError(Throwable e) {
                        displayCourseVip(null);
                        LibDeprecatedLogger.w("Get CourseVip info fail!!!", e);
                    }

                    @Override
                    public void onComplete() {
                    }
                });

    }


    private void loadLocalUserData() {
        if (isDetached()) {
            return;
        }
        ToastUtils.showToast(getContext(), "会员状态更新失败，请返回重试。");
        if (mHelper != null && mHelper.isVip()) {
            displayVipView();
        } else {
            displayCommonView();
        }
    }

    @Override
    public void onViewAttachedToWindow(View v) {
        super.onViewAttachedToWindow(v);
    }

    private void displayVipView() {
        if (!isAdded()) {
            return;
        }
        mLoadingView.setVisibility(View.GONE);
        mMyUserView.setVisibility(View.VISIBLE);

        tv_my_nickname.setText(mHelper.getNickName());

        tv_my_login_method.setText(mHelper.getUtype());
//        btn_my_buy.setText(getResources().getString(R.string.txt_fragment_my_user_vip_btn));
        btn_my_login.setText(getResources().getString(R.string.txt_fragment_my_user_logout_btn));
    }

    private void displayCommonView() {
        if (!isAdded()) {
            return;
        }
        mLoadingView.setVisibility(View.GONE);
        mMyUserView.setVisibility(View.VISIBLE);

        tv_my_nickname.setText(mHelper.getNickName());

        tv_my_login_method.setText(mHelper.getUtype());
        btn_my_login.setText(getResources().getString(R.string.txt_fragment_my_user_logout_btn));
        btn_my_login.setVisibility(View.GONE);
    }

    /**
     * 获取课堂会员信息后更新UI
     *
     * @param isVip
     */
    private void displayCourseVip(String isVip) {
        if (null != isVip && "1".equals(isVip)) {
            tv_my_type.setVisibility(View.VISIBLE);
            tv_my_type.setText(getResources().getString(R.string.txt_fragment_edu_user_vip_type));
        } else if (null != isVip && "0".equals(isVip)) {
            tv_my_type.setVisibility(View.VISIBLE);
            tv_my_type.setText(getResources().getString(R.string.txt_fragment_edu_user_common_type));
        } else {
            tv_my_type.setVisibility(View.GONE);
        }
    }

    private void displayUnloginView() {
        if (!isAdded()) {
            return;
        }
        mLoadingView.setVisibility(View.GONE);
        mMyUserView.setVisibility(View.VISIBLE);

        tv_my_nickname.setText("");
        tv_my_type.setVisibility(View.VISIBLE);
        tv_my_type.setText(getResources().getString(R.string.txt_fragment_edu_user_login_msg));
        tv_my_login_method.setText("");
        btn_my_login.setText(getResources().getString(R.string.txt_fragment_my_user_login_btn));
        btn_my_login.setVisibility(View.VISIBLE);
    }

    private void requestLogout() {
        NetworkApi.getLogout(new Observer<Logout>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(Logout value) {
                LibDeprecatedLogger.d("requestLogout(): onNext()");
                if (getActivity() != null && (getActivity() instanceof ListEduUserRelatedActivity) && ((ListEduUserRelatedActivity) getActivity()).getLeftSelectedTag() != LIST_INDEX_MY) {
                    return;
                }

                if (mHelper != null && null != value) {
                    int status = value.getStatus();

                    if (status == 200) {
                        mHelper.clearLoginStatus();
                        PostHelper.postLogoutEvent();
                        displayUnloginView();
                        //clear cached data in SharedPreference.
                        mHelper.clearLoginStatus();
                        RequestManager.getInstance().updatePasspost(mHelper.getLoginPassport(), "0");
                    } else {
                        ToastUtils.showToast2(getActivity(), getResources().getString(R.string.txt_fragment_my_user_logout_fail));
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                if (isVisible()) {
                    LibDeprecatedLogger.e("requestLogout(): onError()--" + e.getMessage());
                    ToastUtils.showToast2(getActivity(), getResources().getString(R.string.txt_fragment_my_user_logout_fail));
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestLogout(): onComplete()");
            }
        });
    }

    private class MyClickListener implements View.OnClickListener {

        @Override
        public void onClick(View view) {
            if (view.getId() == R.id.btn_my_login) {
                if (mHelper != null && mHelper.getIsLogin()) {
                    requestLogout();
                    RequestManager.getInstance().onMyUserLoginBtnClickEvent("Logout");
                } else {
                    ActivityLauncher.startLoginActivity(getContext(), mSource);
                    RequestManager.getInstance().onMyUserLoginBtnClickEvent("Login");
                }
            } else if (view.getId() == R.id.btn_my_buy) {
                ActivityLauncher.startPayActivity(getContext(), PayActivity.PAY_SOURCE_MY_YUE_TING_OPEN_VIP);
                if (mHelper != null && mHelper.isVip()) {
                    RequestManager.getInstance().onMyUserBuyBtnClickEvent("Renewal");
                } else {
                    RequestManager.getInstance().onMyUserBuyBtnClickEvent("Open");
                }
            }
        }
    }
}
