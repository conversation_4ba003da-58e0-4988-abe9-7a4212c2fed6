package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.model.PersonalCinemaModel;
import com.sohuott.tv.vod.view.PersonalCinemaView;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by yizhang210244 on 2017/12/22.
 */

public class PersonalCinemaPresenterImpl implements PersonalCinemaPresenter{

    private PersonalCinemaView mPersonalCinemaView;
    protected CompositeDisposable compositeDisposable=new CompositeDisposable();

    public PersonalCinemaPresenterImpl(PersonalCinemaView personalCinemaView) {
        mPersonalCinemaView = personalCinemaView;
    }

    @Override
    public void getData() {
        DisposableObserver disposableObserver = new DisposableObserver<PersonalCinemaModel>() {
            @Override
            public void onNext(PersonalCinemaModel value) {
                if(mPersonalCinemaView != null){
                    mPersonalCinemaView.showDataSuccess(value);
                }
            }

            @Override
            public void onError(Throwable e) {
                if(mPersonalCinemaView != null){
                    mPersonalCinemaView.showDataError("数据返回异常，请稍后重试！");
                }
            }

            @Override
            public void onComplete() {

            }
        };
        NetworkApi.getPersonalCinemaInfo(disposableObserver);
        compositeDisposable.add(disposableObserver);
    }

    @Override
    public void detachView() {
        mPersonalCinemaView = null;
        compositeDisposable.clear();
    }


}
