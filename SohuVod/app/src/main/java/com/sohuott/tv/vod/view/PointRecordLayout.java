package com.sohuott.tv.vod.view;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.PointRecordAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.PointRecordInfo;
import com.sohuott.tv.vod.presenter.PointRecordPresenterImpl;

import java.util.List;

/**
 * Custom Layout to display user point record
 * <p>
 * Created by wenjingbian on 2018/1/5.
 */

public class PointRecordLayout extends FrameLayout implements PointRecordView {

    //visible items' count on the view
    private static final int VISIBLE_COUNT = 6;

    private LoadingView mLoadingView;
    private LinearLayout mErrorView, mPointRecordView;
    private CustomLinearRecyclerView rv_point_record;
    private Context mContext;
    private RelativeLayout mEmptyView;

    private PointRecordAdapter mAdapter;
    private CustomLinearLayoutManager mLayoutManager;
    private PointRecordPresenterImpl mPresenterImpl;

    private String mPassport;

    public PointRecordLayout(@NonNull Context context, String passport) {
        super(context);
        this.mContext = context;
        this.mPassport = passport;
        LayoutInflater.from(context).inflate(R.layout.layout_point_record, this, true);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        initView();
        initData();
        RequestManager.getInstance().onPointRecordExposureEvent();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        //release all resource
        mPresenterImpl = null;
        mContext = null;
        if (mAdapter != null) {
            mAdapter.releaseAll();
            mAdapter = null;
        }
    }

    @Override
    public void displayRecordView(PointRecordInfo pointRecordInfo) {
        if (pointRecordInfo == null || pointRecordInfo.getData() == null || pointRecordInfo.getStatus() != 0) {
            displayErrorView();
            return;
        } else if (pointRecordInfo.getData().getCount() == 0
                || pointRecordInfo.getData().getResult().getScoreRecords().size() == 0) {
            displayEmptyView();
            return;
        }

        mLoadingView.setVisibility(GONE);
        mErrorView.setVisibility(GONE);
        mPointRecordView.setVisibility(VISIBLE);

        List<PointRecordInfo.DataBean.ResultBean.ScoreRecordsBean> list = pointRecordInfo.getData().getResult().getScoreRecords();
//        list.addAll(list);
//        list.addAll(list);
//        list.addAll(list);
        mAdapter.setDataSource(list);
        rv_point_record.setAdapter(mAdapter);
    }

    @Override
    public void displayErrorView() {
        mLoadingView.setVisibility(GONE);
        mPointRecordView.setVisibility(GONE);
        mErrorView.setVisibility(VISIBLE);
    }

    private void initView() {
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mErrorView = (LinearLayout) findViewById(R.id.err_view);
        mPointRecordView = (LinearLayout) findViewById(R.id.layout_point_record);
        mEmptyView = (RelativeLayout) findViewById(R.id.layout_empty);

        rv_point_record = (CustomLinearRecyclerView) findViewById(R.id.rv_point_record);
        mLayoutManager = new CustomLinearLayoutManager(mContext);
        mLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        rv_point_record.setLayoutManager(mLayoutManager);
        rv_point_record.requestFocus();
        rv_point_record.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (mAdapter == null || mLayoutManager == null || rv_point_record == null) {
                    return false;
                }

                if (event.getAction() != KeyEvent.ACTION_DOWN) {
                    return false;
                }

                //scroll view when pressed KEYCODE_DPAD_DOWN or KEYCODE_DPAD_UP
                if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                    int lastPos = mLayoutManager.findLastCompletelyVisibleItemPosition();
                    if (lastPos + VISIBLE_COUNT <= mAdapter.getItemCount() - 1) {
                        rv_point_record.scrollToPosition(lastPos + VISIBLE_COUNT);
                    } else {
                        rv_point_record.scrollToPosition(mAdapter.getItemCount() - 1);
                    }
                    return true;
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                    int firstPos = mLayoutManager.findFirstCompletelyVisibleItemPosition();
                    if (firstPos - VISIBLE_COUNT >= 0) {
                        rv_point_record.scrollToPosition(firstPos - VISIBLE_COUNT);
                    } else {
                        rv_point_record.scrollToPosition(0);
                    }
                    return true;
                }
                return false;
            }
        });
    }

    private void initData() {
        mPresenterImpl = new PointRecordPresenterImpl();
        mPresenterImpl.setView(this);
        mPresenterImpl.requestPointRecordData(1, 1000, mPassport);

        mAdapter = new PointRecordAdapter(mContext);
    }

    private void displayEmptyView() {
        mPointRecordView.setVisibility(VISIBLE);
        mEmptyView.setVisibility(VISIBLE);
        mErrorView.setVisibility(GONE);
        mLoadingView.setVisibility(GONE);
        rv_point_record.setVisibility(GONE);
    }

}
