package com.sohuott.tv.vod.activity.setting.privacy

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.fragment.app.activityViewModels
import androidx.leanback.widget.*
import androidx.recyclerview.widget.RecyclerView
import com.com.sohuott.tv.vod.base_component.NewBaseFragment
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.lib_viewbind_ext.viewBinding
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.goneAndVisible
import com.sohuott.tv.vod.activity.setting.SettingShareViewModel
import com.sohuott.tv.vod.activity.setting.privacy.PrivacySwitchPresenter.OnItemKeyEventListener
import com.sohuott.tv.vod.customview.LoadingView
import com.sohuott.tv.vod.databinding.FragmentPrivacySettingLayoutBinding
import com.sohuott.tv.vod.lib.api.NetworkApi
import com.sohuott.tv.vod.lib.model.privacy.*
import io.reactivex.observers.DisposableObserver

/**
 *
 * @Description 隐私设置
 * @date 2022/3/16 3:10 下午
 * <AUTHOR>
 * @Version 1.0
 */
class PrivacySettingFragment : NewBaseFragment(R.layout.fragment_privacy_setting_layout) {

    private var mViewBinding: FragmentPrivacySettingLayoutBinding? = null

    private val _binding by viewBinding(FragmentPrivacySettingLayoutBinding::bind,
        onViewDestroyed =
        {
            mViewBinding = null
            mArrayAdapter = null
            mAdapter = null
            tvError = null
            pdLoading = null
            privacyLeanback = null
        })

    private var mArrayAdapter: ArrayObjectAdapter? = null
    private var mAdapter: ItemBridgeAdapter? = null

    private var mPrivacySwitchPresenter: PrivacySwitchPresenter? = null

    private var tvError: TextView? = null
    private var pdLoading: LoadingView? = null
    private var privacyLeanback: VerticalGridView? = null

    private val mShareViewModel by activityViewModels<SettingShareViewModel>()


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewBinding = _binding
        initAdapter()
        initData()
    }

    override fun onResume() {
        super.onResume()
    }

    private fun initAdapter() {
        tvError = mViewBinding?.tvError
        pdLoading = mViewBinding?.pbLoading
        privacyLeanback = mViewBinding?.privacyLeanback
        mArrayAdapter = ArrayObjectAdapter(PrivacyPresenterSelector(context))
        mAdapter = ItemBridgeAdapter(mArrayAdapter)
        mViewBinding?.privacyLeanback?.adapter = mAdapter
        mViewBinding?.privacyLeanback?.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)

            }

            override fun onChildViewHolderSelectedAndPositioned(
                parent: RecyclerView,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelectedAndPositioned(parent, child, position, subposition)
                AppLogger.v("position" + position)
                AppLogger.v("subposition" + subposition)
            }

        })
    }


    private fun initData() {
        mPrivacySwitchPresenter = PrivacySwitchPresenter(context)
        mPrivacySwitchPresenter?.setOnItemKeyEventListener(object : OnItemKeyEventListener {
            override fun onItemKeyLeft() {
                mShareViewModel.focusChange(true)
                AppLogger.v("left")
            }
        })

        mViewBinding?.privacyLeanback?.goneAndVisible(mViewBinding?.pbLoading)
        NetworkApi.getPrivacySettingInfo(object : DisposableObserver<PrivacySettingList>() {
            override fun onNext(value: PrivacySettingList?) {
                if (value == null) {
                    return
                }
                if (value.data.isNullOrEmpty()) {
                    return
                }
                val listType = object : TypeToken<List<PrivacySettingBean>>() {}.type
                val data = Gson().fromJson<MutableList<PrivacySettingBean>>(value.data, listType)
                if (data.isNullOrEmpty()) {
                    AppLogger.v("data is null ")
                    return
                }
                assemblePrivacySettingData(data)
                tvError?.gone()
                pdLoading?.goneAndVisible(privacyLeanback)
            }

            override fun onError(e: Throwable?) {
                AppLogger.v(e.toString())
                tvError?.gone()
                privacyLeanback?.goneAndVisible(tvError)
            }

            override fun onComplete() {

            }

        })


    }

    private fun assemblePrivacySettingData(list: MutableList<PrivacySettingBean>) {
        list.forEach { setting ->
            if (setting.isShow) {
                mArrayAdapter?.add(PrivacySettingHeaderItem(setting.groupName))
                val subAdapter = ArrayObjectAdapter(mPrivacySwitchPresenter)
                setting.subList?.forEach { sub ->
                    if (sub.isShow) {
                        subAdapter.add(
                            PrivacySettingItem(
                                id = sub.id,
                                sub.name,
                                isSwitch = sub.isSwitch,
                                content = sub.content
                            )
                        )
                    }
                }
                val listRow = ListRow(subAdapter)
                mArrayAdapter?.add(listRow)
                setting.tip?.let {
                    mArrayAdapter?.add(PrivacySettingTipItem(it))
                }
            }
        }

    }

}