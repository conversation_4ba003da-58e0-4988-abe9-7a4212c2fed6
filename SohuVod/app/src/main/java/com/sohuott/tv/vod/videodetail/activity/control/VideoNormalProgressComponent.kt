package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import com.sh.ott.video.player.PlayerConstants
import com.sohu.ott.lib_widget.SoHuVideoProgressBar
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.isGone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.videodetail.activity.playerIsNotStart
import com.sohuott.tv.vod.videodetail.activity.playerIsPlaying
import com.sohuott.tv.vod.videodetail.activity.playerIsStart


/**
 * 小窗 悬浮窗 外层进度条展示
 */
class VideoNormalProgressComponent constructor(
    context: Context
) : BaseVideoProgressComponent(context) {

    private var realProgress: SoHuVideoProgressBar? = null


    private var currentPlayState = PlayerConstants.VideoState.IDLE
    private var screenMode = PlayerConstants.ScreenMode.NORMAL


    init {

        layoutInflater.inflate(R.layout.video_component_normal_progress, this, true)
        realProgress = findViewById(R.id.scaleRealProgress)

    }

    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        currentPlayState = playState
        if (currentPlayState.playerIsStart() && screenMode != PlayerConstants.ScreenMode.FULL) {
            visible()
        } else {
            gone()
        }
    }

    override fun onProgressChanged(duration: Long, position: Long) {
        super.onProgressChanged(duration, position)
        setSeekBar(mCurrentPosition, mDuration)
    }


    @Synchronized
    fun setSeekBar(position: Long, duration: Long) {
        realProgress?.setFirstBar(position.toFloat())
        realProgress?.setSecondBar(position.toFloat())
        realProgress?.maxBarPosition = duration.toFloat()
    }

    fun initProgress(){
        realProgress?.setFirstBar(0f)
        realProgress?.setSecondBar(0f)
        realProgress?.maxBarPosition = 0f
    }

    fun setProgressModel(
        onlySeeModel: Boolean,
        progressL: MutableList<SoHuVideoProgressBar.SecondListBean>
    ) {
        if (onlySeeModel) {
            realProgress?.setSecondBarModel(SoHuVideoProgressBar.MODEL_SECOND_LIST)
            realProgress?.setSecondBar(progressL)
        } else {
            realProgress?.setSecondBarModel(SoHuVideoProgressBar.MODEL_SECOND_NORMAL)
        }

    }

    override fun onScreenModeChanged(screenMode: Int) {
        super.onScreenModeChanged(screenMode)
        this.screenMode = screenMode
        if (currentPlayState.playerIsStart() && screenMode != PlayerConstants.ScreenMode.FULL) {
            visible()
        } else {
            gone()
        }
    }

}