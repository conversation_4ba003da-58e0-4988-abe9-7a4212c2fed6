package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.ui.EpisodeLayoutNew;
import com.sohuott.tv.vod.widget.GlideImageView;
import com.sohuott.tv.vod.widget.PlayingView;

import java.util.List;

import static com.sohuott.tv.vod.ui.EpisodeLayoutNew.ASC_SORT_ORDER;

/**
 * Created by fenglei on 16-7-5.
 */
public class EpisodeTrailerFragment extends EpisodeBaseFragmentNew {

    private static final String TAG = EpisodeTrailerFragment.class.getSimpleName();
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = (ViewGroup) inflater.inflate(R.layout.fragment_episode_trailer_layout, container, false);
        initUI();
        return mRootView;
    }

    @Override
    public void setUI(List<EpisodeVideos.Video> videoList) {
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            ViewGroup viewGroup = (ViewGroup) mRootView.getChildAt(i);
            if (viewGroup.getVisibility() != View.VISIBLE) {
                return;
            }
            int num;
            if (mSortOrder == ASC_SORT_ORDER) {
                num = mStart + i;
            } else {
                num = mEnd - i;
            }
            boolean found = false;
            if (videoList != null) {
                int size = videoList.size();
                for (int j = 0; j < size; j++) {
                    if (videoList.get(j).videoOrder == num) {
                        if (viewGroup.findViewById(R.id.episode_poster) != null) {
                            ((GlideImageView) viewGroup.findViewById(R.id.episode_poster)).setImageRes(videoList.get(j).videoExtendsPic_320_180);
                            ((GlideImageView) viewGroup.findViewById(R.id.episode_poster)).setClearWhenDetached(false);
                        }
                        if (viewGroup.findViewById(R.id.episode_title1) != null) {
                            ((TextView) viewGroup.findViewById(R.id.episode_title1)).setText(videoList.get(j).tvSubName);
                        }
                        if (viewGroup.findViewById(R.id.episode_video_length) != null) {
                            ((TextView) viewGroup.findViewById(R.id.episode_video_length)).setText(formatVideoLength(videoList.get(j).tvLength));
                        }
                        viewGroup.setTag(videoList.get(j));
                        viewGroup.setEnabled(true);
                        viewGroup.findViewById(R.id.episode_title1).setEnabled(true);
                        if (mVideoOrder == videoList.get(j).videoOrder) {
                            if (mIsInitFromPlayer || mEpisodeIsSelected) {
                                mPlayingView = (PlayingView) viewGroup.findViewById(R.id.on_play_icon);
                                if(mPlayingView != null){
                                    mPlayingView.show();
                                }
                                viewGroup.setSelected(true);
                                if (mRootView.hasFocus()) {
                                    viewGroup.requestFocus();
                                }
                            }
                        }
                        found = true;
                        break;
                    }
                }
            }
            if (!found) {
                if (viewGroup.findViewById(R.id.episode_title1) != null) {
                    ((TextView) viewGroup.findViewById(R.id.episode_title1)).setText("无内容");
                    viewGroup.findViewById(R.id.episode_title1).setEnabled(false);
                }
                viewGroup.setTag(Constant.EPISODE_OFFLINE);
                viewGroup.setEnabled(true);
            }
        }
    }

    private String formatVideoLength(int tvLength) {
        int sec = tvLength % 60;
        int min = tvLength / 60;
        int hour = min / 60;
        min = min % 60;
        StringBuilder stringBuilder = new StringBuilder();
        if (hour <= 9) {
            stringBuilder.append("0");
        }
        stringBuilder.append(hour).append(":");
        if (min <= 9) {
            stringBuilder.append("0");
        }
        stringBuilder.append(min).append(":");
        if (sec <= 9) {
            stringBuilder.append("0");
        }
        stringBuilder.append(sec);
        return stringBuilder.toString();
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            int index = mRootView.indexOfChild(v);
            switch (keyCode) {
                case KeyEvent.KEYCODE_DPAD_DOWN:
                    if (index > 2) {
                        if (mTabView != null) {
                            mTabView.setCurrentTabFocus();
                            return true;
                        }
                    } else if (mRootView.getChildAt(3).getVisibility() != View.VISIBLE) {
                        if (mTabView != null) {
                            mTabView.setCurrentTabFocus();
                            return true;
                        }
                    } else if (index < 3 && mRootView.getChildAt(4).getVisibility() != View.VISIBLE) {
                        mRootView.getChildAt(3).requestFocus();
                        return true;
                    }
                    break;
                case KeyEvent.KEYCODE_DPAD_UP:
                    if (index > 2) {
                        mRootView.getChildAt(index - 3).requestFocus();
                        return true;
                    }
                    return false;
                case KeyEvent.KEYCODE_DPAD_RIGHT:
                    if ((mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER && (mStart + index) == mTotalCount)
                            || (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER && (mEnd - index) == 1)
                            || (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER && mTotalCount == mEnd && index == 2)
                            || (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER && mStart == 1 && index == 2)) {
                        return true;
                    }
                    break;
                case KeyEvent.KEYCODE_DPAD_LEFT:
                    if ((mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER && (mStart + index) == 1)
                            || (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER && (mEnd - index) == mTotalCount)
                            || (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER && mStart == 1 && index == 0)
                            || (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER && mTotalCount == mEnd && index == 0)) {
                        return true;
                    }
                    break;
                default:
                    break;
            }

        }
        return false;
    }

    public interface LoadTrailerDataCallback {
        void onDataLoaded(int vid);
    }
}
