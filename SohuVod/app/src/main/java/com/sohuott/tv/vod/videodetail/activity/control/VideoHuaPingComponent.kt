package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import com.sh.ott.video.contor.ShControlComponent
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.widget.GlideImageView


class VideoHuaPingComponent @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ShControlComponent(context, attrs, defStyleAttr) {
    private var mDefaultCover: GlideImageView?

    init {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.video_component_hua_ping, this, true)
        mDefaultCover = view?.findViewById(R.id.defalut_cover);
        hide()
    }


    fun setPlayerBackground(url: String?) {
        AppLogger.d("花屏配置  url：$url")
        var defaultId: Int = R.drawable.scale_player_bg
        //如果花屏，显示背景
        if (TextUtils.isEmpty(url)) {
            mDefaultCover?.setImageRes(defaultId)
        } else {
            if (Util.getHuapingParams(context) === 1) {
                mDefaultCover?.setImageRes(
                    url, resources.getDrawable(defaultId),
                    resources.getDrawable(defaultId)
                )
            }
        }
    }

    fun show() {
        visible()
        AppLogger.d("花屏配置  visible")
    }

    fun hide() {
        gone()
        AppLogger.d("花屏配置  gone")
    }
}