package com.sohuott.tv.vod.app;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bytedance.boost_multidex.BoostMultiDex;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;

/**
 * Created by fenglei on 17-3-29.
 */

public class SohuAppNew extends Application implements Application.ActivityLifecycleCallbacks {


    private static int mResumed = 0;
    private static int mPaused = 0;
    private static KillProcessHandler mHandler = new KillProcessHandler();
    private static final int MSG_KILL = 1001;


    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        Log.d("SohuAppNew","SohuAppNew attachBaseContext name "+base.getPackageName());
        if (Util.isSameProcess(base, base.getPackageName())) {
            App.onAttach(base);
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d("SohuAppNew","SohuAppNew onCreate name "+getApplicationContext().getPackageName());

//        App.onCreate(this);
//        AppContext.init(getApplicationContext());
//        SohuAppUtil.CONFIRM_KILL = false;
        SohuAppUtil.initApplication(getApplicationContext());
        registerActivityLifecycleCallbacks(this);
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
//        SohuAppUtil.CONFIRM_KILL = false;
    }

    @Override
    public void onActivityStarted(Activity activity) {
//        SohuAppUtil.CONFIRM_KILL = false;
    }

    @Override
    public void onActivityResumed(Activity activity) {
//        SohuAppUtil.CONFIRM_KILL = false;
        mResumed++;
        LibDeprecatedLogger.d("onActivityResumed: " + activity);
    }

    @Override
    public void onActivityPaused(Activity activity) {
        mPaused++;
        LibDeprecatedLogger.d("onActivityPaused: " + activity);
    }

    @Override
    public void onActivityStopped(Activity activity) {
        //判断渠道号是TCL才kill process
        if (Util.getPartnerNo(this).equals("80151115")) {
            mHandler.removeMessages(MSG_KILL);
            mHandler.sendEmptyMessageDelayed(MSG_KILL, 15000);
        }
//        mHandler.sendEmptyMessageDelayed(MSG_KILL,3000);//这里做个延时kill process，防止频繁切换activity时误触发
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        LibDeprecatedLogger.d("onActivityDestroyed: " + activity);
    }

    private static class KillProcessHandler extends Handler {

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (mResumed <= mPaused) {
                ToastUtils.showToast(App.getAppContext(), "悦厅TV进入后台运行");
                LibDeprecatedLogger.d("Resumed <= Paused. So exit app!");
                SohuAppUtil.exitApp(App.getAppContext());
            }
        }
    }
}
