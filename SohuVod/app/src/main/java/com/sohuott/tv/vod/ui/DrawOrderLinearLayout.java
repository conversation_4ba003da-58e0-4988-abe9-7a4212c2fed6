package com.sohuott.tv.vod.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;

/**
 * Created by fengle<PERSON> on 16-3-18.
 */
public class DrawOrderLinearLayout extends LinearLayout {

    public DrawOrderLinearLayout(Context context) {
        super(context);
        init();
    }

    public DrawOrderLinearLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public DrawOrderLinearLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setChildrenDrawingOrderEnabled(true);
    }

    @Override
    protected int getChildDrawingOrder(int childCount, int i) {
//        return super.getChildDrawingOrder(childCount, i);
        int pos = -1;
        for(int index = 0; index < childCount; index++) {
            if(getChildAt(index).hasFocus()) {
                pos = index;
                break;
            }
        }
        if(pos < 0 || pos >= childCount) {
            return i;
        }
        if(i == childCount - 1) {
            return pos;
        }else if(i < pos) {
            return i;
        }else {
            return i + 1;
        }
    }



}
