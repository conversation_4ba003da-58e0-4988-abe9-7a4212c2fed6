package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.view.LittleDesc;
import com.sohuott.tv.vod.view.UpdateDialogNew;

/**
 * Created by yizhang210244 on 2017/5/15.
 */

public class UpdateLittleDescView extends RelativeLayout{

    private TextView mOrder;
    private TextView mDescTitle;
    private TextView mDescContent;

    public UpdateLittleDescView(Context context) {
        super(context);
        init(context);
    }

    public UpdateLittleDescView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public UpdateLittleDescView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context){
        LayoutInflater.from(context).inflate(R.layout.update_little_desc_view, this, true);
        mOrder = (TextView) findViewById(R.id.order);
        mDescTitle = (TextView) findViewById(R.id.desc_title);
        mDescContent = (TextView) findViewById(R.id.desc_content);
    }

    public void setData(LittleDesc desc){
        if(desc != null){
            mOrder.setText(""+desc.getOrder());
            mDescTitle.setText(desc.getTitle());
            mDescContent.setText(desc.getContent());
        }

    }

}
