package com.sohuott.tv.vod.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.ListEduUserRelatedActivity;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.adapter.HistoryFavorCollectionRecordAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.db.greendao.Collection;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.AuditDenyAids;
import com.sohuott.tv.vod.lib.model.BookedRecord;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.model.VideoFavorListBean;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.model.HistoryEvent;
import com.sohuott.tv.vod.presenter.HfcRecordViewPresenterImpl;
import com.sohuott.tv.vod.view.ClearDialog;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.CustomRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.HfcRecordView;
import com.sohuott.tv.vod.widget.MyEmptyView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_BOOKED;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_COLLECTION;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_FAVOR;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_HISTORY;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by wenjingbian on 2017/5/12.
 */

public class HistoryFavorCollectionFragment extends BaseFragment
        implements HistoryFavorCollectionRecordAdapter.FocusController, HfcRecordView {

    //String values to identify the entry when requested personal recommended list
    private static final String TRACK_ENTRY_HISTORY = "4";
    private static final String TRACK_ENTRY_COLLECTION = "5";
    private static final String TRACK_ENTRY_FAVOR = "6";
    private static final String TRACK_ENTRY_BOOKED = "9";

    private static final int SPAN_COUNT = 4;

    private LoadingView mChildLoadingView;
    private LinearLayout mRightErrorView;
    private MyEmptyView mEmptyView;
    private TextView tv_hfc_filter, tv_hfc_long, tv_hfc_all;
    private CustomRecyclerView mRecordRecyclerView;
    private ClearDialog mClearDialog;
    private FocusBorderView mFocusBorderView;

    private ListUserRelatedActivity mActivity;

    private HistoryFavorCollectionRecordAdapter mRecordAdapter;

    private CustomGridLayoutManager mRecordLayoutManager;

    //Cached record lists
    private List<PlayHistory> mHistoryList;
    private List<Collection> mCollectionList;
    private List<VideoFavorListBean.DataEntity.ResultEntity> mFavorList;
    private List<BookedRecord.DataBean> mBookedList = new ArrayList<>();

    private HfcRecordViewPresenterImpl mPresenterImpl;
    private LoginUserInformationHelper mHelper;

    private EventBus mEventBus;
    private boolean isUpdateHistoryData;
    private boolean isDeleteItem;
    private boolean isRequestFromCloud = true;
    private boolean isFirstEnter = true;
    private HistoryEvent mHistoryEvent;
    private PlayHistory mNewPlayHistory;
    private String mSubjectId;
    public boolean onlyVRS = true;
    private boolean requestAllDataWhenVrsEmpty = true;
    public boolean fromDeleteButton = false;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View hfcView = inflater.inflate(R.layout.fragment_history_favor_collection, container, false);
        mActivity = (ListUserRelatedActivity) getActivity();
        mHelper = LoginUserInformationHelper.getHelper(getContext());
        initView(hfcView);
        mPresenterImpl = new HfcRecordViewPresenterImpl(getContext());
        mPresenterImpl.setHfcRecordView(this);
        changeTab(mActivity.getLeftSelectedTag());

        mEventBus = EventBus.getDefault();
        mEventBus.register(this);
        return hfcView;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mActivity != null && mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_HISTORY) {
            return;
        }
        if (isUpdateHistoryData) {
            showRecordLoadingView();
            View currFocusView = mActivity.getCurrentFocus();
            if (currFocusView != null && currFocusView.getTag() != null && currFocusView.getTag() instanceof Integer
                    && (int) currFocusView.getTag() == LIST_INDEX_HISTORY) {
                mPresenterImpl.requestHistoryList(false, onlyVRS);
            }
            mActivity.focusLeftItem(LIST_INDEX_HISTORY);
            mPresenterImpl.requestLocalHistoryById(mHistoryEvent.getDataType(), mHistoryEvent.getId())
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<PlayHistory>() {
                        @Override
                        public void accept(PlayHistory playHistory) throws Exception {
                            mNewPlayHistory = playHistory;
                        }
                    });
            return;
        }
        if (mRecordAdapter != null && mHistoryList != null && mHistoryList.size() > 0) {
            int selectPos = -1;
            mNewPlayHistory = getSelectedPlayHistory();
            if (mNewPlayHistory != null) {
                selectPos = mHistoryList.indexOf(mNewPlayHistory);
                if (selectPos >= 0 && selectPos < mHistoryList.size()) {
                    int finalSelectPos = selectPos;
                    mPresenterImpl.requestLocalHistoryById(mNewPlayHistory.getDataType(),
                            mNewPlayHistory.getDataType() == 0 ? mNewPlayHistory.getAlbumId() : mNewPlayHistory.getVideoId())
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Consumer<PlayHistory>() {
                                @Override
                                public void accept(PlayHistory playHistory) throws Exception {
                                    if (mHistoryList!=null){
                                        mHistoryList.set(finalSelectPos, playHistory);
                                    }
                                    mRecordAdapter.notifyItemChanged(finalSelectPos);
                                }
                            });

                }
            }
        }

        updateButtonStatus();
    }

    public void updateButtonStatus() {
        if (onlyVRS) {
            tv_hfc_long.setSelected(true);
            tv_hfc_all.setSelected(false);
        } else {
            tv_hfc_all.setSelected(true);
            tv_hfc_long.setSelected(false);
        }
    }

    public void refocusFilterButton() {
        if (tv_hfc_long.getVisibility() == View.VISIBLE) {
            if (onlyVRS) {
                tv_hfc_long.requestFocus();
            } else {
                tv_hfc_all.requestFocus();
            }
            return;
        }
        mActivity.focusOnTopBar();
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mHelper = null;
        mActivity = null;
        if (mBookedList != null) {
            mBookedList.clear();
            mBookedList = null;
        }
        if (mCollectionList != null) {
            mCollectionList.clear();
            mCollectionList = null;
        }
        if (mHistoryList != null) {
            mHistoryList.clear();
            mHistoryList = null;
        }
        if (mFavorList != null) {
            mFavorList.clear();
            mFavorList = null;
        }
        if (mRecordAdapter != null) {
            mRecordAdapter.releaseAll();
            mRecordAdapter = null;
        }
        mEventBus.unregister(this);
    }

    @Override
    public void onFocusSelected(int position) {
        //onFocusSelected() in HistoryCollectionActivity
        if (mActivity != null) {
            mActivity.focusLeftItem(position);
        }
    }

    @Override
    public void showChildErrorView(int tag) {
        //Update right error view.
        if (mActivity == null || mActivity.getLeftSelectedTag() == -1) {
            return;
        }

        if (tag == mActivity.getLeftSelectedTag()) {
            mRightErrorView.setVisibility(View.VISIBLE);
            mChildLoadingView.setVisibility(View.GONE);
        }
    }

    @Override
    public void showParentLoadingView() {
        if (mActivity != null) {
            mActivity.showParentLoadingView();
        }
    }

    @Override
    public void hideParentLoadingView() {
        if (mActivity != null) {
            mActivity.hideParentLoadingView();
        }
    }

    @Override
    public void showEmptyView() {
        if (mActivity != null) {
            displayEmptyView(ListEduUserRelatedActivity.LIST_INDEX_HISTORY);
        }
    }

    @Override
    public void updateHistoryRecordView(int id) {
        if (mRecordAdapter != null) {
            mRecordAdapter.updateHistoryRecordView(id);
        }
    }

    @Override
    public void updateHistoryRecordFailedView(int id) {
        if (mRecordAdapter != null) {
            mRecordAdapter.updateHistoryRecordFailedView(id);
        }
    }

    @Override
    public void updateCollectionRecordView(int id) {
        if (mRecordAdapter != null) {
            mRecordAdapter.updateCollectionRecordView(id);
        }
    }

    @Override
    public void updateCollectionRecordFailedView(int id) {
        if (mRecordAdapter != null) {
            mRecordAdapter.updateCollectionRecordFailedView(id);
        }
    }

    @Override
    public void updateHistoryItemView(PlayHistory playHistory) {
        if (mHistoryList.contains(mRecordAdapter.getSelectedHistory())) {
            int index = mHistoryList.indexOf(mRecordAdapter.getSelectedHistory());
            mHistoryList.set(index, playHistory);
            mRecordAdapter.notifyItemChanged(index);
        }
    }

    @Override
    public void updatePersonalRecommendView(String trackEntry, List<VideoDetailRecommend.DataEntity> personalRecommendList) {
        if (mActivity == null) {
            return;
        }

        //Update data of mPromoteRecycler
        int tag = mActivity.getLeftSelectedTag();
        if ((tag == LIST_INDEX_HISTORY && trackEntry.equals(TRACK_ENTRY_HISTORY))
                || (tag == LIST_INDEX_COLLECTION && trackEntry.equals(TRACK_ENTRY_COLLECTION))
                || (tag == LIST_INDEX_FAVOR && trackEntry.equals(TRACK_ENTRY_FAVOR))
                || (tag == LIST_INDEX_BOOKED && trackEntry.equals(TRACK_ENTRY_BOOKED))) {
            mEmptyView.setListView(personalRecommendList);
        }
        RequestManager.getInstance().onPersonalRecommendListExposureEvent(trackEntry);
    }

    @Override
    public void updateHistoryRecordView(List<PlayHistory> historyList, boolean onlyVRS) {
        if (mActivity == null) {
            return;
        }
        if (!mHelper.getIsLogin()) {
            StringBuffer vrsId = new StringBuffer();
            StringBuffer pgcId = new StringBuffer();
            for (int i = 0; i < historyList.size(); i++) {
                if (historyList.get(i).getDataType() == 0) {
                    vrsId.append(historyList.get(i).getAlbumId() + ":" + historyList.get(i).getVideoId() + ",");
                } else {
                    pgcId.append(historyList.get(i).getVideoId() + ",");
                }

            }
            getAuditDenyAids(vrsId.toString(), pgcId.toString());
        }

        mHistoryList = historyList;
        if (mActivity.getLeftSelectedTag() == LIST_INDEX_HISTORY) {
            if (historyList != null && historyList.size() > 0) {
                if (onlyVRS) {//如果长视频有数据，则更改标志位
                    requestAllDataWhenVrsEmpty = false;
                }
                LibDeprecatedLogger.d("There are records, display record view.");
                displayRecordView(LIST_INDEX_HISTORY);
//                updateButtonStatus();
            } else {
                if (requestAllDataWhenVrsEmpty) {//当长视频为空时，是否要请求一次全部视频
                    requestAllDataWhenVrsEmpty = false;
                    if (onlyVRS) {//如果此次是请求onlyVrs结果为空，则再请求一次全部
                        this.onlyVRS = false;
                        mPresenterImpl.requestHistoryList(false, false);
                    } else {
                        LibDeprecatedLogger.d("No record, displaying empty view.");
                        displayEmptyView(LIST_INDEX_HISTORY);
                    }
                } else {
                    LibDeprecatedLogger.d("No record, displaying empty view.");
                    displayEmptyView(LIST_INDEX_HISTORY);
                }

            }
        }

        if (isRequestFromCloud) {
            isRequestFromCloud = false;
        }
    }

    @Override
    public void onAddMoreHistoryRecord(List<PlayHistory> historyList, boolean onlyVRS) {
        if (mRecordAdapter == null || historyList == null || historyList.isEmpty()) {
            return;
        }
        if (mActivity != null && mActivity.getLeftSelectedTag() != ListUserRelatedActivity.LIST_INDEX_HISTORY) {
            return;
        }
        mRecordAdapter.addMoreHistories(historyList);
    }

    @Override
    public void updateCollectionRecordView(List<Collection> collectionList) {
        if (mActivity == null) {
            return;
        }
        if (!mHelper.getIsLogin()) {
            StringBuffer vrsId = new StringBuffer();
            for (int i = 0; i < collectionList.size(); i++) {
                vrsId.append(collectionList.get(i).getAlbumId() + ",");
            }
            getAuditDenyAids(vrsId.toString(), "");
        }
        mCollectionList = collectionList;
        if (mActivity.getLeftSelectedTag() == LIST_INDEX_COLLECTION) {
            if (collectionList != null && collectionList.size() > 0) {
                LibDeprecatedLogger.d("There are records, display record view.");
                displayRecordView(LIST_INDEX_COLLECTION);
            } else {
                LibDeprecatedLogger.d("No record, displaying empty view.");
                displayEmptyView(LIST_INDEX_COLLECTION);
            }
        }
    }

    @Override
    public void onAddMoreCollectionRecord(List<Collection> collectionList) {
        if (mRecordAdapter == null || collectionList == null || collectionList.isEmpty()) {
            return;
        }
        if (mActivity != null && mActivity.getLeftSelectedTag() != LIST_INDEX_COLLECTION) {
            return;
        }
        mRecordAdapter.addMoreCollections(collectionList);
    }

    @Override
    public void updateFavorRecordView(List<VideoFavorListBean.DataEntity.ResultEntity> favorList) {
        if (mActivity == null) {
            return;
        }

        mFavorList = favorList;
        if (mActivity.getLeftSelectedTag() == LIST_INDEX_FAVOR) {
            if (favorList != null && favorList.size() > 0) {
                LibDeprecatedLogger.d("There are records, display record view.");
                displayRecordView(LIST_INDEX_FAVOR);
            } else {
                LibDeprecatedLogger.d("No record, displaying empty view.");
                displayEmptyView(LIST_INDEX_FAVOR);
            }
        }
    }

    @Override
    public void updateBookedRecordView(List<BookedRecord.DataBean> dataList) {
        if (mActivity == null) {
            return;
        }

        mBookedList = dataList;
        if (mActivity.getLeftSelectedTag() == LIST_INDEX_BOOKED) {
            if (dataList != null && dataList.size() > 0) {
                LibDeprecatedLogger.d("There are records, display record view.");
                displayRecordView(LIST_INDEX_BOOKED);
            } else {
                LibDeprecatedLogger.e("No record, display empty view");
                displayEmptyView(LIST_INDEX_BOOKED);
            }
        }
    }

    @Override
    public void updateBookedRecordView(String id) {
        LibDeprecatedLogger.d("Cancel booked record successfully.");
        if (mRecordAdapter != null) {
            mRecordAdapter.updateBookedRecordView(Integer.valueOf(id));
        }
    }

    @Override
    public void updateBookedRecordFailedView(String id) {
        LibDeprecatedLogger.d("failed to cancel booked record.");
        if (mRecordAdapter != null) {
            mRecordAdapter.updateBookedRecordFailedView(Integer.valueOf(id));
        }
    }

    @Override
    public void setSubjectIdInfo(String subjectId) {
        this.mSubjectId = subjectId;
    }

    @Subscribe
    public void onEventMainThread(HistoryEvent event) {
        LibDeprecatedLogger.d("Got HistoryEvent!");
        if (event != null && mRecordAdapter != null && mRecordAdapter.getLeftTag() == LIST_INDEX_HISTORY) {
            isUpdateHistoryData = true;
            mHistoryEvent = event;
        }
    }

    public void changeTab(int tag) {
        String subPageName = null;

        showRecordLoadingView();
        if (mRecordAdapter != null) {
            mRecordAdapter.isShowDeleteView(false);
        }
        switch (tag) {
            case LIST_INDEX_HISTORY:
                updateButtonStatus();
                mPresenterImpl.requestHistoryList(false, onlyVRS);
                RequestManager.getInstance().onHistoryListExposureEvent();
                subPageName = "6_list_history";
                historyExposureEvent();
                break;
            case LIST_INDEX_COLLECTION:
                mPresenterImpl.requestCollectionList(false);
                RequestManager.getInstance().onCollectionListExposureEvent();
                subPageName = "6_list_collection";
                collectionExposureEvent();
                break;
            case LIST_INDEX_FAVOR:
                mPresenterImpl.requestFavorList(mHelper.getIsLogin() ? mHelper.getLoginPassport()
                        : UserInfoHelper.getGid(), mHelper.getIsLogin());
                RequestManager.getInstance().onFavorListExposureEvent();
                subPageName = "6_list_favor";
                break;
            case LIST_INDEX_BOOKED:
                mPresenterImpl.requestSubjectId();
                if (mHelper.getIsLogin()) {
                    mPresenterImpl.requestBookedVideoList(mHelper.getLoginPassport(),
                            mHelper.getLoginToken());
                } else {
                    displayEmptyView(LIST_INDEX_BOOKED);
                }
                RequestManager.getInstance().onBookedListExposureEvent();
                subPageName = "6_list_booked";
                bookExposureEvent();
                break;
            default:
                subPageName = "";
                break;
        }
        if (isFirstEnter) {
            setSubPageName(subPageName);
        } else {
            recordSubPageEnd();
            recordSubPageStart(subPageName);
        }
        isFirstEnter = false;
    }

    private void bookExposureEvent() {
        HashMap<String, String> path = new HashMap<>(1);
        path.put("pageId", "1012");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), path, null, null);
    }

    private void collectionExposureEvent() {
        HashMap<String, String> path = new HashMap<>(1);
        path.put("pageId", "1011");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), path, null, null);
    }

    private void historyExposureEvent() {
        HashMap<String, String> path = new HashMap<>(1);
        path.put("pageId", "1010");
        HashMap<String, Boolean> memo = new HashMap<>(1);
        memo.put("longVideo", onlyVRS);
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), path, null
                , memo);
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.mFocusBorderView = focusBorderView;
    }

    public PlayHistory getSelectedPlayHistory() {
        if (mRecordAdapter != null) {
            return mRecordAdapter.getSelectedHistory();
        }
        return null;
    }

    public void resetCachedListData() {
        if (mCollectionList != null) {
            mCollectionList.clear();
            mCollectionList = null;
        }

        if (mBookedList != null) {
            mBookedList.clear();
            mBookedList = null;
        }
    }


    //ListEduUserRelatedActivity没有退出登录入口，课堂记录在这里也一并清除
    public void clearLocalHistoryAndCollection() {
        mPresenterImpl.clearLocalCollectionData(false);
        mPresenterImpl.clearLocalCollectionData(true);
        mPresenterImpl.clearLocalHistoryData(false);
        mPresenterImpl.clearLocalHistoryData(true);
    }

    public void isDeleteItem(boolean isDeleteItem) {
        this.isDeleteItem = isDeleteItem;
    }

    /**
     * Show loading icon on record view
     */
    private void showRecordLoadingView() {
        if (mActivity == null) {
            return;
        }

        //Visible loading view
        if (mChildLoadingView != null) {
            mChildLoadingView.setVisibility(View.VISIBLE);
        }
        //invisible error view
        if (mRightErrorView != null) {
            mRightErrorView.setVisibility(View.GONE);
        }
        //invisible empty view
        if (mEmptyView != null) {
            mEmptyView.setVisibility(View.GONE);
        }
        //invisible record view
        if (tv_hfc_filter != null) {
            tv_hfc_filter.setVisibility(View.GONE);
            tv_hfc_long.setVisibility(View.GONE);
            tv_hfc_all.setVisibility(View.GONE);
        }
        if (mRecordRecyclerView != null) {
            mRecordRecyclerView.setVisibility(View.GONE);
        }
    }

    public void focusChildViewAtPos() {
        if (mActivity == null || mActivity.getLeftSelectedTag() < 0) {
            return;
        }
        int selectedItemIndex = mActivity.getLeftSelectedTag();
        if (selectedItemIndex == LIST_INDEX_HISTORY && mRecordRecyclerView.getVisibility() != View.VISIBLE) {
            if (tv_hfc_long.getVisibility() == View.VISIBLE) {
                tv_hfc_long.requestFocus();
                return;
            }
        }
        if ((selectedItemIndex == LIST_INDEX_HISTORY && mHistoryList != null && mHistoryList.size() > 0)
                || (selectedItemIndex == LIST_INDEX_COLLECTION && mCollectionList != null && mCollectionList.size() > 0)
                || (selectedItemIndex == LIST_INDEX_FAVOR && mFavorList != null && mFavorList.size() > 0)
                || (selectedItemIndex == LIST_INDEX_BOOKED && mBookedList != null && mBookedList.size() > 0)) {
            moveToFirstPosition();
        } else if (selectedItemIndex == LIST_INDEX_HISTORY && (mHistoryList == null || mHistoryList.size() <= 0)
                || selectedItemIndex == LIST_INDEX_COLLECTION && (mCollectionList == null || mCollectionList.size() <= 0)
                || selectedItemIndex == LIST_INDEX_FAVOR && (mFavorList == null || mFavorList.size() <= 0)
                || selectedItemIndex == LIST_INDEX_BOOKED && (mBookedList == null || mBookedList.size() <= 0)) {
            if (mEmptyView.getVisibility() == View.VISIBLE) {
                mEmptyView.focusAtPos();
            }
        }
    }

    public void showDeleteDialog() {
        if (tv_hfc_filter == null) {
            return;
        }
        if (tv_hfc_filter.getVisibility() == View.VISIBLE) {
            if (mRecordAdapter != null) {
                if (mRecordAdapter.getLeftTag() == LIST_INDEX_HISTORY
                        || mRecordAdapter.getLeftTag() == LIST_INDEX_COLLECTION
                        || mRecordAdapter.getLeftTag() == LIST_INDEX_BOOKED) {
                    if (mRecordRecyclerView != null) {
                        int count = mRecordRecyclerView.getChildCount();
                        if (count > 0) {
                            mClearDialog = new ClearDialog(getContext());
                            mClearDialog.show();
                            mClearDialog.setClearAllListener(new OnDialogDismissListener(true, false));
                            mClearDialog.setClearItemListener(new OnDialogDismissListener(false, false));
                        }
                    }
                }
            }
        }
    }

    /**
     * Update item view of RecordRecyclerView, add or remove delete view
     *
     * @param isShowDeleteView Show delete view or not
     */
    public boolean updateRecordItemView(boolean isShowDeleteView) {
        if (tv_hfc_filter == null) {
            return false;
        }
        if (mRecordAdapter != null) {
            mRecordAdapter.isShowDeleteView(isShowDeleteView);
        }

        if (tv_hfc_filter.getVisibility() == View.VISIBLE) {
            if (mRecordAdapter != null) {
                if (mRecordAdapter.getLeftTag() == LIST_INDEX_HISTORY
                        || mRecordAdapter.getLeftTag() == LIST_INDEX_COLLECTION
                        || mRecordAdapter.getLeftTag() == LIST_INDEX_BOOKED) {
                    if (mRecordRecyclerView != null) {
                        if (isShowDeleteView) {
                            int count = mRecordRecyclerView.getChildCount();
                            if (count > 0) {
                                mClearDialog = new ClearDialog(getContext());
                                mClearDialog.show();
                                mClearDialog.setClearAllListener(new OnDialogDismissListener(true, false));
                                mClearDialog.setClearItemListener(new OnDialogDismissListener(false, false));
                                return true;
                            }
                        } else {
                            isDeleteItem = false;
                            if (updateDeleteView(isShowDeleteView)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * Update child view of mRecordRecyclerView when deleted item or canceled to delete item
     *
     * @param isShowDeleteView boolean value to identify visibility of delete icon
     * @return whether block KeyEvent or not
     */
    public boolean updateDeleteView(boolean isShowDeleteView) {
        boolean isBlockKeyEvent = false;
        if (mRecordRecyclerView != null && mRecordAdapter != null) {
            ImageView imageView;
            TextView textView;
            int first = mRecordLayoutManager.findFirstVisibleItemPosition();
            int last = mRecordLayoutManager.findLastVisibleItemPosition();
            if (last < mRecordAdapter.getItemCount() - 1) {
                mRecordAdapter.notifyItemRangeChanged(last + 1, mRecordAdapter.getItemCount() - last);
            }
            if (first > 0) {
                mRecordAdapter.notifyItemRangeChanged(0, first);
            }
            for (int i = first; i <= last; i++) {
                RecyclerView.ViewHolder viewHolder = mRecordRecyclerView.findViewHolderForAdapterPosition(i);
                if (viewHolder == null || viewHolder.itemView == null) {
                    continue;
                }
                imageView = (ImageView) viewHolder.itemView.findViewById(R.id.iv_delete_item);
                textView = (TextView) viewHolder.itemView.findViewById(R.id.tv_item_hfc_title);
                if (imageView == null || textView == null) {
                    continue;
                }
                mRecordAdapter.isShowDeleteView(isShowDeleteView);
                if (isShowDeleteView) { //if show delete icon
                    if (imageView.getVisibility() != View.VISIBLE) {
                        imageView.setVisibility(View.VISIBLE);
                        isBlockKeyEvent = true;
                        if (mActivity != null && i == 0) {
                            if (mActivity.getLeftSelectedTag() == LIST_INDEX_HISTORY) {
                                RequestManager.getInstance().onHistoryDeleteExposureEvent();
                            } else if (mActivity.getLeftSelectedTag() == LIST_INDEX_COLLECTION) {
                                RequestManager.getInstance().onCollectionCancelExposureEvent();
                            } else if (mActivity.getLeftSelectedTag() == LIST_INDEX_BOOKED) {
                                RequestManager.getInstance().onBookedCancelExposureEvent();
                            }
                        }
                    } else {
                        isBlockKeyEvent = false;
                        break;
                    }
                } else {
                    if (imageView.getVisibility() == View.VISIBLE) {
                        imageView.setVisibility(View.GONE);
                        isBlockKeyEvent = true;
                    } else {
                        isBlockKeyEvent = false;
                        break;
                    }
                }
            }
        }
        return isBlockKeyEvent;
    }

    private void displayNormalEmptyView(int tag) {
        //Hide record view and display empty view
        tv_hfc_filter.setVisibility(View.GONE);
        tv_hfc_all.setVisibility(View.GONE);
        tv_hfc_long.setVisibility(View.GONE);
        mRecordRecyclerView.setVisibility(View.GONE);
        mChildLoadingView.setVisibility(View.GONE);
        mEmptyView.setVisibility(View.VISIBLE);
        mEmptyView.setBtnVisibility(!mHelper.getIsLogin());
        if (!mHelper.getIsLogin()) {
            mEmptyView.setBtnText("登 录");
            mEmptyView.setBtnListener(MyEmptyView.TAG_LOGIN);
        }
        mEmptyView.setParentTag(tag);
    }

    /**
     * Display empty view on the right of screen
     *
     * @param tag tag value of the selected item view
     */
    public void displayEmptyView(int tag) {
        updateRecordItemView(false);
        if (tag == LIST_INDEX_HISTORY && fromDeleteButton) {
            fromDeleteButton = false;
            if (onlyVRS) {
                onlyVRS = false;
                mPresenterImpl.requestHistoryList(false, onlyVRS);
            } else {
                displayNormalEmptyView(tag);
            }
            return;
        }
        if (tag == LIST_INDEX_HISTORY && onlyVRS) {
            displayEmptyViewWithFilter(tag);
            return;
        }
        displayNormalEmptyView(tag);
//        switch (tag) {
//            case LIST_INDEX_HISTORY:
//                mPresenterImpl.requestPersonalRecommendList(TRACK_ENTRY_HISTORY);
//                break;
//            case LIST_INDEX_COLLECTION:
//                mPresenterImpl.requestPersonalRecommendList(TRACK_ENTRY_COLLECTION);
//                break;
//            case LIST_INDEX_FAVOR:
//                mPresenterImpl.requestPersonalRecommendList(TRACK_ENTRY_FAVOR);
//                break;
//            case LIST_INDEX_BOOKED:
//                mPresenterImpl.requestPersonalRecommendList(TRACK_ENTRY_BOOKED);
//                break;
//            default:
//                break;
//        }
    }

    private void displayEmptyViewWithFilter(int tag) {
        //Hide record view and display empty view
        tv_hfc_filter.setVisibility(View.VISIBLE);
        tv_hfc_all.setVisibility(View.VISIBLE);
        tv_hfc_long.setVisibility(View.VISIBLE);
        mRecordRecyclerView.setVisibility(View.GONE);
        mChildLoadingView.setVisibility(View.GONE);
        mEmptyView.setVisibility(View.VISIBLE);
        mEmptyView.setBtnVisibility(!mHelper.getIsLogin());
        if (!mHelper.getIsLogin()) {
            mEmptyView.setBtnText("登 录");
            mEmptyView.setBtnListener(MyEmptyView.TAG_LOGIN);
        }
        mEmptyView.setParentTag(tag);
    }

    public HfcRecordViewPresenterImpl getHfcRecordViewPresenterImpl() {
        return mPresenterImpl;
    }

    private void initView(View view) {
        mChildLoadingView = (LoadingView) view.findViewById(R.id.child_loading_view);
        mRightErrorView = (LinearLayout) view.findViewById(R.id.err_view);
        tv_hfc_filter = (TextView) view.findViewById(R.id.tv_hfc_filter);
        tv_hfc_long = (TextView) view.findViewById(R.id.tv_hfc_long);
        tv_hfc_all = (TextView) view.findViewById(R.id.tv_hfc_all);
        mRecordRecyclerView = (CustomRecyclerView) view.findViewById(R.id.crv_hfc_record);
        mRecordRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mRecordRecyclerView.setItemAnimator(null);
        mRecordRecyclerView.setItemViewCacheSize(0);
        mRecordRecyclerView.setPadding(getResources().getDimensionPixelSize(R.dimen.x48), getResources().getDimensionPixelSize(R.dimen.y38),
                0, getResources().getDimensionPixelSize(R.dimen.y24));
        mRecordRecyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.right = getResources().getDimensionPixelSize(R.dimen.x48);
                outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y45);
            }
        });
        mRecordRecyclerView.setChildDrawingOrderCallback(new RecyclerView.ChildDrawingOrderCallback() {
            @Override
            public int onGetChildDrawingOrder(int childCount, int i) {
                int pos = mRecordRecyclerView.indexOfChild(mRecordRecyclerView.getFocusedChild());
                if (pos < 0 || pos >= childCount - 1) {
                    return i;
                }

                if (pos == i) {
                    return i + 1;
                } else if (i == pos + 1) {
                    return i - 1;
                } else {
                    return i;
                }
            }
        });
        mRecordRecyclerView.setOnScrollListener(new FinishScrollListener());

        mEmptyView = (MyEmptyView) view.findViewById(R.id.layout_my_empty_view);
        mEmptyView.setFocusBorderView(mFocusBorderView);
        mEmptyView.setFocusController(this);
        tv_hfc_long.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean hasFocus) {
                if (hasFocus) {
                    if (!onlyVRS) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10206, "clk"), null, null, null);
                    }
                    tv_hfc_long.setSelected(true);
                    tv_hfc_all.setSelected(false);
                    onlyVRS = true;
                    mPresenterImpl.requestHistoryList(false, true);
                }
            }
        });
        tv_hfc_all.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean hasFocus) {
                if (hasFocus) {
                    if (onlyVRS) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10207, "clk"), null, null, null);
                    }
                    tv_hfc_all.setSelected(true);
                    tv_hfc_long.setSelected(false);
                    onlyVRS = false;
                    mPresenterImpl.requestHistoryList(false, false);
                }
            }
        });
        tv_hfc_long.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent keyEvent) {
                if (keyCode == KeyEvent.KEYCODE_BACK && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    mActivity.focusLeftItem();
                    return true;
                }
                if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    mActivity.focusLeftItem();
                    return true;
                }
                if (keyCode == KeyEvent.KEYCODE_DPAD_UP && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    mActivity.focusOnTopBar();
                    return true;
                }
                if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    if (mRecordRecyclerView.getVisibility() != View.VISIBLE && mEmptyView.getVisibility() == View.VISIBLE) {
                        mEmptyView.focusAtPos();
                        return true;
                    }
                }
                return false;
            }
        });
        tv_hfc_all.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent keyEvent) {
                if (keyCode == KeyEvent.KEYCODE_BACK && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    mActivity.focusLeftItem();
                    return true;
                }
                if (keyCode == KeyEvent.KEYCODE_DPAD_UP && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    mActivity.focusOnTopBar();
                    return true;
                }
                if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    return true;
                }
                if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    if (mRecordRecyclerView.getVisibility() != View.VISIBLE && mEmptyView.getVisibility() == View.VISIBLE) {
                        mEmptyView.focusAtPos();
                        return true;
                    }
                }
                return false;
            }
        });

    }


    /**
     * Request focus of RecordRecyclerView
     */
    private void focusRecordViewAtPos() {
        if (mRecordLayoutManager == null || mRecordRecyclerView == null ||
                mRecordRecyclerView.getVisibility() != View.VISIBLE) {
            return;
        }

        int focusedPosition = mRecordLayoutManager.findFirstVisibleItemPosition();
        LibDeprecatedLogger.d("First visible item position = " + focusedPosition);
        if (mRecordRecyclerView.findViewHolderForAdapterPosition(focusedPosition) != null
                && mRecordRecyclerView.findViewHolderForAdapterPosition(focusedPosition).itemView != null) {
            mRecordRecyclerView.findViewHolderForAdapterPosition(focusedPosition).itemView.requestFocus();
        }
    }

    private void moveToFirstPosition() {
        if (mRecordLayoutManager == null || mRecordRecyclerView == null ||
                mRecordRecyclerView.getVisibility() != View.VISIBLE) {
            return;
        }
        int firstVisibleItemPosition = mRecordLayoutManager.findFirstVisibleItemPosition();
        if (firstVisibleItemPosition == 0) {
            focusRecordViewAtPos();
            return;
        }
        moveToPosition(0);
        mRecordRecyclerView.postDelayed(new InnerRunnable(this), 200);
    }
    private static class InnerRunnable implements Runnable {
        private WeakReference<HistoryFavorCollectionFragment> mWrapper;
        InnerRunnable(HistoryFavorCollectionFragment historyFavorCollectionFragment){
            mWrapper = new WeakReference<>(historyFavorCollectionFragment);
        }
        @Override
        public void run() {
            HistoryFavorCollectionFragment historyFavorCollectionFragment = mWrapper.get();
            if (historyFavorCollectionFragment != null){
                historyFavorCollectionFragment.focusRecordViewAtPos();
            }
        }
    }

    public void moveToPosition(int position) {
        int firstItem = mRecordRecyclerView.getChildLayoutPosition(mRecordRecyclerView.getChildAt(0));
        int lastItem = mRecordRecyclerView.getChildLayoutPosition(mRecordRecyclerView.getChildAt(mRecordRecyclerView.getChildCount() - 1));
        if (position < firstItem || position > lastItem) {
            mRecordRecyclerView.smoothScrollToPosition(position);
        } else {
            int movePosition = position - firstItem;
            int top = mRecordRecyclerView.getChildAt(movePosition).getTop();
            mRecordRecyclerView.smoothScrollBy(0, top);
        }
    }

    /**
     * Display record view on the right of screen
     *
     * @param id ID of the selected item view
     */
    private void displayRecordView(int id) {
        //Hide empty view and child loading view, display record view
        mChildLoadingView.setVisibility(View.GONE);
        mRightErrorView.setVisibility(View.GONE);
        mEmptyView.setVisibility(View.GONE);
        mRecordRecyclerView.setVisibility(View.VISIBLE);

        //init mRecordRecycleView
        if (mRecordAdapter == null) {
            mRecordLayoutManager = new CustomGridLayoutManager(getContext(), SPAN_COUNT);
            mRecordLayoutManager.setCustomPadding(2 * getResources().getDimensionPixelSize(R.dimen.y120), 2 * getResources().getDimensionPixelSize(R.dimen.y120));
            mRecordRecyclerView.setLayoutManager(mRecordLayoutManager);
            mRecordAdapter = new HistoryFavorCollectionRecordAdapter(this, mRecordRecyclerView);
            mRecordAdapter.setFocusBorderView(mFocusBorderView);
            mRecordRecyclerView.setAdapter(mRecordAdapter);
            mRecordAdapter.setOnFocusSelected(this);
        }
        mRecordAdapter.setLeftTag(id);
        mRecordAdapter.notifyItemRangeRemoved(0, mRecordAdapter.getItemCount());

        //Update right view by the selected item id
        switch (id) {
            case LIST_INDEX_HISTORY:
                tv_hfc_filter.setVisibility(View.VISIBLE);
                tv_hfc_filter.setText(R.string.txt_activity_user_related_menu_filter_history);
                tv_hfc_long.setVisibility(View.VISIBLE);
                tv_hfc_all.setVisibility(View.VISIBLE);
                updateButtonStatus();
                mRecordAdapter.setDataSource(mHistoryList);
                mRecordAdapter.notifyItemRangeChanged(0, mHistoryList.size());
                if (isUpdateHistoryData) {
                    int selectPos = mRecordAdapter.getDataIndex(mHistoryEvent.getId());
                    if (selectPos >= 0 && selectPos < mHistoryList.size()) {
                        mHistoryList.set(selectPos, mNewPlayHistory);
                        mRecordAdapter.setDataSource(mHistoryList);
                        mRecordAdapter.notifyItemChanged(selectPos);
                    }
                    isUpdateHistoryData = false;
                }
                break;
            case LIST_INDEX_COLLECTION:
                tv_hfc_filter.setVisibility(View.VISIBLE);
                tv_hfc_filter.setText(R.string.txt_activity_user_related_menu_filter_collection);
                tv_hfc_all.setVisibility(View.INVISIBLE);
                tv_hfc_long.setVisibility(View.INVISIBLE);
                mRecordAdapter.setDataSource(mCollectionList);
                mRecordAdapter.notifyItemRangeChanged(0, mCollectionList.size());
                break;
            case LIST_INDEX_FAVOR:
                tv_hfc_filter.setVisibility(View.INVISIBLE);
                tv_hfc_long.setVisibility(View.INVISIBLE);
                tv_hfc_all.setVisibility(View.INVISIBLE);
                mRecordAdapter.setDataSource(mFavorList);
                mRecordAdapter.notifyItemRangeChanged(0, mFavorList.size());
                break;
            case LIST_INDEX_BOOKED:
                tv_hfc_filter.setVisibility(View.VISIBLE);
                tv_hfc_filter.setText(R.string.txt_activity_user_related_menu_filter_booked);
                tv_hfc_all.setVisibility(View.INVISIBLE);
                tv_hfc_long.setVisibility(View.INVISIBLE);
                mRecordAdapter.setDataSource(mBookedList);
                mRecordAdapter.notifyItemRangeChanged(0, mBookedList.size());
                break;
            default:
                break;
        }

    }

    /**
     * Custom class extended OnScrollListener to implement page loading data
     * <p>
     * Request more data of next page
     */
    private class FinishScrollListener extends RecyclerView.OnScrollListener {

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            if (mActivity == null) {
                return;
            }
            int lastVisibleItemPosition = mRecordLayoutManager.findLastVisibleItemPosition() + 1;
            int modelsCount = mRecordAdapter.getItemCount();
            int tag = mActivity.getLeftSelectedTag();
            //request more network data when the last completely visible line was the  forth line from the bottom.
            if (lastVisibleItemPosition + 4 * SPAN_COUNT >= modelsCount) {
                if (tag == LIST_INDEX_HISTORY) {
                    mPresenterImpl.requestMoreHistoryList(onlyVRS);
                } else if (tag == LIST_INDEX_COLLECTION) {
                    mPresenterImpl.requestMoreCollectionList();
                }
            }
        }

    }

    /**
     * Custom OnClickListener bind on ClearDialog
     */
    private class OnDialogDismissListener implements View.OnClickListener {

        private boolean isDeleteAll;

        private boolean isSure;

        /**
         * Constructor
         *
         * @param isDeleteAll delete all records or not
         */
        public OnDialogDismissListener(boolean isDeleteAll, boolean isSure) {
            this.isDeleteAll = isDeleteAll;
            this.isSure = isSure;
        }

        @Override
        public void onClick(View v) {
            //Dismiss ClearDialog
            mClearDialog.dismiss();

            if (isSure) {
                if (mActivity.getLeftSelectedTag() == LIST_INDEX_HISTORY) {
                    fromDeleteButton = true;
                    mPresenterImpl.clearAllHistoryData(false);
                    RequestManager.getInstance().onHistoryDeleteAllEvent();
                } else if (mActivity.getLeftSelectedTag() == LIST_INDEX_COLLECTION) {
                    mPresenterImpl.clearAllCollectionData(false);
                    RequestManager.getInstance().onCollectionCancelAllEvent();
                } else if (mActivity.getLeftSelectedTag() == LIST_INDEX_BOOKED) {
                    mPresenterImpl.clearAllBookedRecord(mHelper.getLoginPassport(), mHelper.getLoginToken());
                    RequestManager.getInstance().onBookedCancelAllEvent();
                }
                return;
            }

            if (isDeleteAll) {
                mClearDialog = new ClearDialog(getContext());
                mClearDialog.show();
                mClearDialog.setClearNetualListener(new OnDialogDismissListener(true, true));
                mClearDialog.setTitle(getResources().getString(R.string.txt_activity_user_related_delete_all));
            } else {
                //Show delete icon on item of mRecordRecyclerView
                updateDeleteView(true);
                isDeleteItem = true;
            }
        }
    }

    private void getAuditDenyAids(String vrsId, String pgcId) {
        NetworkApi.getAuditDenyAids(vrsId, pgcId, new Observer<AuditDenyAids>() {
            @Override
            public void onSubscribe(Disposable disposable) {

            }

            @Override
            public void onNext(AuditDenyAids auditDenyAids) {
                if (auditDenyAids != null && auditDenyAids.getData() != null && auditDenyAids.getData().size() > 0) {
                    mRecordAdapter.setDenyAids(auditDenyAids.getData());
                }
            }

            @Override
            public void onError(Throwable throwable) {

            }

            @Override
            public void onComplete() {

            }
        });
    }
}
