package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.sohuott.tv.vod.R;

/**
 * Created by fengle<PERSON> on 16-2-3.
 */
public class RecommendFragmentContentLayout extends RelativeLayout {

    public RecommendFragmentContentLayout(Context context) {
        super(context);
        init(context);
    }

    public RecommendFragmentContentLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public RecommendFragmentContentLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        setChildrenDrawingOrderEnabled(true);
    }

    @Override
    protected int getChildDrawingOrder(int childCount, int i) {
//        return super.getChildDrawingOrder(childCount, i);
        int pos = -1;
        for(int index = 0; index < childCount; index++) {
            if(getChildAt(index).hasFocus()) {
                pos = index;
                break;
            }
        }
        if(pos < 0 || pos >= childCount) {
            return i;
        }
        if(i == childCount - 1) {
            return pos;
        }else if(i < pos) {
            return i;
        }else {
            return i + 1;
        }
    }

}
