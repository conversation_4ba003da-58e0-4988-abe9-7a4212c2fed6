package com.sohuott.tv.vod.search

import android.content.Context
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger

class CustomLayoutManager(context: Context?, private val columns: Int) : RecyclerView.LayoutManager() {
    private val gridLayoutManager: GridLayoutManager = GridLayoutManager(context, columns)

    init {
        isAutoMeasureEnabled = true
    }

    override fun onInterceptFocusSearch(focused: View, direction: Int): View? {
        // 在这里自定义焦点搜索逻辑
        LibDeprecatedLogger.d("onInterceptFocusSearch focused $focused direction $direction")

        return super.onInterceptFocusSearch(focused, direction)
    }

    override fun generateDefaultLayoutParams(): RecyclerView.LayoutParams {
        return gridLayoutManager.generateDefaultLayoutParams()
    }

    override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State) {
        gridLayoutManager.onLayoutChildren(recycler, state)
    }

    override fun canScrollVertically(): Boolean {
        return gridLayoutManager.canScrollVertically()
    }

    override fun scrollVerticallyBy(dy: Int, recycler: RecyclerView.Recycler, state: RecyclerView.State): Int {
        return gridLayoutManager.scrollVerticallyBy(dy, recycler, state)
    }

    // 重写其他需要的方法
}
