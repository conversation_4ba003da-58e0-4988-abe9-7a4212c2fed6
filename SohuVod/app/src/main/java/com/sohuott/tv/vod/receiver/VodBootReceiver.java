package com.sohuott.tv.vod.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/**
 * @version V1.0.0
 * @FileName: com.sohuott.tv.vod.receiver.VodBootReceiver.java
 * @author: <PERSON>iJiaCui
 * @Description: ${用于监听开机广播（因厂商特殊要求，需要分渠道）}
 * @date: 2018-01-08 19:06
 */
public class VodBootReceiver extends BroadcastReceiver {

    private Context mContext;

    @Override
    public void onReceive(Context context, Intent intent) {
//        boolean isServiceRunning = Util.isServiceRunning(context, SystemDialogService.class.getName()) &&
//                SystemDialogService.isServiceRunning();
//        AppLogger.d("boot receive action = " + intent.getAction().toString() +
//                ", isServiceRunning=" + isServiceRunning + "," + ApiLog.LOG_ENABLE);
//        if (isServiceRunning) {
//            return;
//        }
//        boolean isNewsProcessRunning = Util.isNewsProcessRunning(context);
//        AppLogger.d("action = " + intent.getAction().toString() + "," + isNewsProcessRunning);
//        if (Intent.ACTION_BOOT_COMPLETED.equalsIgnoreCase(intent.getAction())) {
//            mContext = context;
//            long delay = 0;
//            if (UrlWrapper.DEBUG) {
//                delay = 20 * 1000;
//            } else {
//                delay = 10 * 60 * 1000;
//            }
//            SystemDialogService.startSystemDialogService(mContext, true, delay);
//        }
    }
}
