package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.model.SearchResult;

/**
 * Created by f<PERSON><PERSON><PERSON> on 17-6-22.
 */

public interface SearchResultContract {

    public static final int TYPE_WHOLE = 0;
    public static final int TYPE_MOVIE = 1;
    public static final int TYPE_SERIAL = 2;
    public static final int TYPE_VARIETY = 3;
    public static final int TYPE_DOCUMENTARY = 4;
    public static final int TYPE_COMIC = 5;
    public static final int TYPE_PGC = 6;

    interface SearchResultView {
        public void showSearchResult(SearchResult.DataBean dataBean, int type);
    }

    interface Presenter {
        void getSearchResult(String query, int type, int page, int pageSize);
        void cancel(int type);
    }

}
