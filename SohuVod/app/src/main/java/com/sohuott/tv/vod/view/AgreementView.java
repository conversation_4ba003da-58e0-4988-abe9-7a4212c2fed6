package com.sohuott.tv.vod.view;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AgreementModel;
import com.sohuott.tv.vod.presenter.AgreementPresenter;
import com.sohuott.tv.vod.presenter.IBasePresenter;

import java.util.List;

/**
 * 展示各种协议文本页面
 *
 * <AUTHOR>
 * @Date Created on 2020/3/16.
 */
public class AgreementView implements IBasePresenter.IDataListener<AgreementModel> {

    private PopupWindow mAgreementContentWindow;

    private Context mContext;

    private TextView mAgreementTextView;

    private TextView mTitleTextView;

    private AgreementPresenter mAgreementPresenter;

    private List<AgreementModel.AgreementDetail> mAgreementDetailList;

    /**
     * @param context
     * @param name    If name is null, load all agreement.
     */
    public AgreementView(Context context, String name, String type) {
        this.mContext = context;
        this.initView();
        this.initAgreementContent(name, type);
    }

    public AgreementView(Context context, String name) {
        this.mContext = context;
        this.initView();
        this.initAgreementContent(name, null);
    }

    private void initView() {
        View contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_more_detail, null);

        if (mAgreementContentWindow == null) {
            mAgreementContentWindow = new PopupWindow(contentView, ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT, true);

            // 如果不设置PopupWindow的背景，无论是点击外部区域还是Back键都无法dismiss弹框
            mAgreementContentWindow.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.dialog_more_detail_bg));
            mAgreementContentWindow.setTouchable(true);
            mAgreementContentWindow.setFocusable(true);
            mAgreementContentWindow.setOutsideTouchable(true);
            mAgreementContentWindow.setAnimationStyle(R.style.PopupAnimation);
            mAgreementContentWindow.setContentView(contentView);
        }
        mAgreementTextView = (TextView) mAgreementContentWindow.getContentView().findViewById(R.id.more_detail_text);
        mTitleTextView = (TextView) mAgreementContentWindow.getContentView().findViewById(R.id.more_detail_title);
    }

    private void initAgreementContent(String name, String type) {
        mAgreementPresenter = new AgreementPresenter(this, name == null ? "" : name, type);
        isLoad = false;
        mAgreementPresenter.loadData();
    }

    /**
     * 默认展示第一条协议。
     * new实例时传入了具体的name，此时只获得一条协议数据。
     * 如果new实例时未传入name，此时只获得第一条协议数据并展示。
     *
     * @param parent
     */
    public void show(View parent) {
        show(parent, null);
    }

    /**
     * 指定显示的协议。使用此方法需要new实例时name传入null，此时才会获取所有协议内容
     *
     * @param parent
     * @param name   初始化时name为null，此参数有效
     */
    public void show(View parent, String name) {
        if (mAgreementContentWindow != null && parent != null) {
            this.dataSet(name);
            mAgreementContentWindow.showAtLocation(parent, Gravity.LEFT | Gravity.BOTTOM, 0, 0);
        }
    }

    /**
     * 展示前填充数据
     *
     * @param name
     */
    private void dataSet(String name) {
        String title;
        String content = null;
        if (mAgreementDetailList == null) {
            LibDeprecatedLogger.w("Agreement is null!");
            if (isLoad) {
                title = mContext.getString(R.string.agreement_window_load_fail);
            } else {
                title = mContext.getString(R.string.agreement_window_loading_tips);
            }
        } else if (mAgreementDetailList.size() <= 0) {
            LibDeprecatedLogger.w("No data!");
            title = mContext.getString(R.string.agreement_window_nodata_tips);
        } else {
            AgreementModel.AgreementDetail detail = this.getDetail(name);
            title = detail.getTitle();
            content = detail.getContent();
        }
        if (mAgreementTextView != null && content != null) {
            mAgreementTextView.setText(content);
        }
        if (mTitleTextView != null && title != null) {
            mTitleTextView.setText(title);
        }
    }

    /**
     * 调用此方法前提: mAgreementDetailList > 0
     *
     * @param name
     * @return
     */
    private AgreementModel.AgreementDetail getDetail(String name) {
        int size = 0;
        if (name != null && !name.isEmpty()) {
            for (int i = 0; i < mAgreementDetailList.size(); i++) {
                if (name.equals(mAgreementDetailList.get(i).getName())) {
                    size = i;
                    break;
                }
            }
        }
        LibDeprecatedLogger.d("Get agreement size: " + size);
        return mAgreementDetailList.get(size);
    }

    private boolean isLoad = false;

    @Override
    public void getData(AgreementModel data) {
        isLoad = true;
        mAgreementDetailList = data.getData();
    }

    /**
     * 释放关联Listener，销毁window.
     */
    public void release() {
        if (mAgreementPresenter != null) {
            mAgreementPresenter.release();
        }
        if (mAgreementContentWindow != null) {
            mAgreementContentWindow.dismiss();
        }
        isLoad = false;
    }
}
