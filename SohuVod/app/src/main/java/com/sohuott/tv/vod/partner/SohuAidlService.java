package com.sohuott.tv.vod.partner;

import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.os.RemoteException;

import androidx.annotation.Nullable;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.service.ISohuAidlService;
import com.sohuott.tv.vod.receiver.CancelService;
import com.sohu.lib_utils.ServiceNotificationUtil;

public class SohuAidlService extends Service {

    private SohuAidlStub mStub;
    private ServiceApiHelper mServiceApiHelper;

    public SohuAidlService() {
        mServiceApiHelper = new ServiceApiHelper(this);
        mStub = new SohuAidlStub();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
            startForeground(ServiceNotificationUtil.NOTIFICATION_FOREGROUND_ID, ServiceNotificationUtil.getNotification(getApplicationContext()));
            Intent intent =  new Intent(this, CancelService.class);
            startForegroundService(intent);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        LibDeprecatedLogger.d("SohuAidlService: onStartCommand()");
        return super.onStartCommand(intent, flags, startId);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        LibDeprecatedLogger.d("SohuAidlService: onBind()");
        return mStub;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        LibDeprecatedLogger.d("SohuAidlService: onUnBind()");
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LibDeprecatedLogger.d("SohuAidlService: onDestroy()");
        //release resource
        mServiceApiHelper.release();
        mServiceApiHelper = null;
        mStub = null;
    }

    public ServiceApiHelper getServiceApiImpl() {
        return mServiceApiHelper;
    }

    /**
     * Custom SohuAidlStub to implement ISohuAidlService.Stub
     */
    public class SohuAidlStub extends ISohuAidlService.Stub {

        @Override
        public void playerOnStop() throws RemoteException {
            mServiceApiHelper.playerOnStop();
        }

        @Override
        public void playerOnResume() throws RemoteException {
            mServiceApiHelper.playerOnResume();
        }

        @Override
        public void playerOnPause() throws RemoteException {
            mServiceApiHelper.playerOnPause();
        }

        @Override
        public void playerOnForward() throws RemoteException {
            mServiceApiHelper.playerOnForward();
        }

        @Override
        public void playerOnBackward() throws RemoteException {
            mServiceApiHelper.playerOnBackward();
        }

        @Override
        public void playerOnSeekTo(int sec) throws RemoteException {
            mServiceApiHelper.playerOnSeekTo(sec);
        }

        @Override
        public void playerOnFastSeek(int sec) throws RemoteException {
            mServiceApiHelper.playerOnFastSeek(sec);
        }

        @Override
        public void playerSetFullScreen(boolean isFullScreen) throws RemoteException {
            mServiceApiHelper.playerSetFullScreen(isFullScreen);
        }

        @Override
        public void backToHome() throws RemoteException {
            mServiceApiHelper.backToHome();
        }

        @Override
        public void exitApp() throws RemoteException {
            mServiceApiHelper.exitApp();
        }

        public SohuAidlService getService() {
            return SohuAidlService.this;
        }
    }
}
