package com.sohuott.tv.vod.activity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.Util;

/**
 * Created by XiyingCao on 2018/5/9.
 */

public class BaseActivityUtil {
    public static void handleIntentSource(Intent intent, String activityName, Context context) {
        int sourceid = 0;
        if (intent.getData() != null) {
            Uri uri = intent.getData();
            sourceid = Util.convertStringToInt(uri.getQueryParameter("source_id"));
            if (sourceid == 0 && !TextUtils.isEmpty(uri.getPath()) && !uri.getPath().contains("internal")) {
                sourceid = 1;
            }
        } else {
            sourceid = getIntValueFromIntent(intent, "source_id");
        }
        if (sourceid != 0 && (!TextUtils.isEmpty(activityName) && !activityName.contains("BootActivity"))) {
            String enterId = Util.getEnterId(sourceid, activityName, Util.getPartnerNo(context));
            RequestManager.getInstance().updateEnterId(enterId);

            if (!TextUtils.isEmpty(enterId)) {
                String tmpString = enterId.substring(0, 1);
                if (TextUtils.equals(tmpString, "1") ) {
                    HomeData.sBootOrHomeIsStarted = false;
                }
            }

            if (sourceid == 1) {
                RequestManager.getInstance().updateParnerId(Util.getPartnerNo2(context), Util.getPlatformCode2(context));
            } else {
                RequestManager.getInstance().updateParnerId(Util.getPartnerNo(context), Util.getPlatformCode(context));
            }
            RequestManager.getInstance().onMccEvent("1002", "0");
        }
    }

    public static boolean getBoolValueFromIntent(Intent intent, String name) {
        if (intent == null || TextUtils.isEmpty(name)) {
            return false;
        }
        if (TextUtils.isEmpty(intent.getStringExtra(name))) {
            return intent.getBooleanExtra(name, false);
        }
        return Util.convertStringToBoolean(intent.getStringExtra(name));
    }

    public static int getIntValueFromIntent(Intent intent, String name) {
        if (intent == null || TextUtils.isEmpty(name)) {
            return 0;
        }
        try {
//            if (intent.getIntExtra(name, 0) == 0) {
                return intent.getIntExtra(name, 0);
//            }
//            return Util.convertStringToInt(intent.getStringExtra(name));
        } catch (Exception e) {
            e.printStackTrace();
            LibDeprecatedLogger.e(e.toString());
            return 0;
        }
    }
}
