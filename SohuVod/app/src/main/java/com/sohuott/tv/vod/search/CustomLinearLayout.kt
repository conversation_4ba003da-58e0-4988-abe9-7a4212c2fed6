package com.sohuott.tv.vod.search


import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import androidx.core.view.children
import com.sohuott.tv.vod.R


class CustomLinearLayout(context: Context, attrs: AttributeSet) : LinearLayout(context, attrs) {
    private var focusChildIndex = -1

    init {
        orientation = HORIZONTAL
        setWillNotDraw(true)
        isChildrenDrawingOrderEnabled = true

    }


    fun requestDefaultFocus() {
        getChildAt(0).requestFocus()
    }

    override fun bringChildToFront(child: View?) {
        bringChildToFront(this, child!!)
    }

    fun bringChildToFront(viewGroup: ViewGroup, childView: View) {
        focusChildIndex = viewGroup.indexOfChild(childView)
        if (focusChildIndex != -1) {
            viewGroup.postInvalidate()
        }
    }

    override fun getChildDrawingOrder(childCount: Int, i: Int): Int {
        var index = i
        if (focusChildIndex != -1) {
            when {
                index == childCount - 1 -> {
                    if (focusChildIndex > childCount - 1) {
                        focusChildIndex = childCount - 1
                    }
                    return focusChildIndex
                }
                index == focusChildIndex -> {
                    return childCount - 1
                }
            }
        }
        if (childCount <= index) {
            index = childCount - 1
        }
        return index
    }

}
