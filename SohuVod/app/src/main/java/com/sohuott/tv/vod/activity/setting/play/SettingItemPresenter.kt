package com.sohuott.tv.vod.activity.setting.play

import android.content.Context
import android.view.KeyEvent
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.getResDrawable


/**
 *
 * @Description
 * @date 2022/3/22 14:39
 * <AUTHOR>
 * @Version 1.0
 */
class SettingItemPresenter(private val context: Context) :
    DefaultPresenter(R.layout.item_privacy_setting_switch_layout) {

    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as SettingItem
        AppLogger.i("SettingItemPresenter itme->${item.toString()}  payloads>:${payloads.toString()} ")
        if (item.type == SETTING_TYPE_ITEM) {
            val name = viewHolder.getView<TextView>(R.id.tv_privacy_switch_name)
            val icon = viewHolder.getView<AppCompatImageView>(R.id.iv_privacy_switch)
            val layout = viewHolder.getView<RelativeLayout>(R.id.layout_privacy_switch)
            name.text = item.content

            layout.isFocusable = item.focusable
//            icon.isFocusable = item.focusable
            name.isFocusable = item.focusable
            if (item.focusable) {
                layout.background = context.getResDrawable(R.drawable.login_item_bg_selector)
            } else {
                layout.background = context.getResDrawable(R.drawable.login_item_bg_no_have_focus)
            }

            setShowIcon(icon, item.isSelected)
//
            layout.setOnKeyListener { view, i, keyEvent ->
                if (i == KeyEvent.KEYCODE_DPAD_DOWN && keyEvent.action == KeyEvent.ACTION_DOWN) {
                    return@setOnKeyListener !item.nextKewDown
                }
                false
            }
            icon.setOnFocusChangeListener { v, hasFocus ->
                AppLogger.i("setOnFocusChangeListener icon  hasFocus:$hasFocus v:$v")
            }
            layout.setOnFocusChangeListener { v, hasFocus ->
                name.isSelected = hasFocus
                icon.isSelected = hasFocus
                AppLogger.i("setOnFocusChangeListener hasFocus:$hasFocus v:$v")
                if (hasFocus) {
                    v.animate().scaleX(1.1f).scaleY(1.1f).setDuration(300).start()
                } else {
                    v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(300).start()
                }
            }
        }
    }

    private fun setShowIcon(icon: View, isSwitch: Boolean) {
        icon.visibility =
            if (isSwitch) View.VISIBLE else View.GONE

    }
}