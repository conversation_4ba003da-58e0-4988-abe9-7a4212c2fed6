package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import androidx.leanback.widget.Presenter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

public class TypeProducerPresenter extends Presenter {
    private Context mContext;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    private static final String TAG = "TypeProducerPresenter";

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_producer_layout, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        vh.view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mBrowseItemFocusHighlight.onItemFocused(vh.view, hasFocus);
                vh.mTvTypeProducerName.setSelected(hasFocus);
            }
        });
        if (item instanceof ContentGroup.DataBean.ContentsBean.ProducersListBean) {
            ContentGroup.DataBean.ContentsBean.ProducersListBean producersListBean = (ContentGroup.DataBean.ContentsBean.ProducersListBean) item;
            if (producersListBean == null) return;
            Glide.with(mContext)
                    .load(producersListBean.bigPhoto)
                    .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                    .into(vh.mIvTypeProducerAvatar);
            vh.mTvTypeProducerName.setText(producersListBean.nickName);

            RequestManager.getInstance().onAllEvent(new EventInfo(10156, "imp"),
                    producersListBean.pathInfo,
                    producersListBean.objectInfo, null);
        } else if (item instanceof VideoDetailRecommendModel.DataBean.ContentsBean) {
            VideoDetailRecommendModel.DataBean.ContentsBean contentsBean = (VideoDetailRecommendModel.DataBean.ContentsBean) item;
            Glide.with(mContext)
                    .load(contentsBean.getHorPic())
                    .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                    .into(vh.mIvTypeProducerAvatar);
            vh.mTvTypeProducerName.setText(contentsBean.getName());
            vh.view.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    ActivityLauncher.startActorListActivity(mContext, contentsBean.getId(),
                            contentsBean.getStarType() == 2,
                            contentsBean.getName());
                }
            });
        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final ImageView mIvTypeProducerAvatar;
        private final TextView mTvTypeProducerName;

        public ViewHolder(View view) {
            super(view);
            mIvTypeProducerAvatar = (ImageView) view.findViewById(R.id.iv_type_producer_avatar);
            mTvTypeProducerName = (TextView) view.findViewById(R.id.tv_type_producer_name);
        }
    }
}

