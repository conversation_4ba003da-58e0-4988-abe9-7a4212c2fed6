package com.sohuott.tv.vod.activity.launcher

import java.lang.Error

data class State<out T>(val status: Status, val data: T?, val message: Message?) {

    companion object {

        fun <T> success(data: T?): State<T> {
            return State(Status.SUCCESS, data, null)
        }

        fun <T> empty(): State<T> {
            return State(Status.SUCCESS, null, null)
        }

        fun <T> error(msg: Message?): State<T> {
            return State(Status.ERROR, null, msg)
        }

        fun <T> error(data: T?, msg: Message?): State<T> {
            return State(Status.ERROR, data, msg)
        }

        fun <T> loading(): State<T> {
            return State(Status.LOADING, null, null)
        }
    }
}

data class Message(
    var message: String? = "",
    var code: Int? = null
)

enum class Status {
    SUCCESS,
    ERROR,
    LOADING
}