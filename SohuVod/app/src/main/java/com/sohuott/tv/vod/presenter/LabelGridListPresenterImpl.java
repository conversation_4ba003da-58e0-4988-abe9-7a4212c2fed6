package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.view.LabelGridListView;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by wenjingbian on 2017/9/19.
 */

public class LabelGridListPresenterImpl {

    private static final int PAGE_SIZE = 30;

    private LabelGridListView mView;

    private int mCateCode;
    private int mCurrPage;
    private int mCurrSize;
    private boolean isRequestData = true;
    private String mFilterStr;

    public LabelGridListPresenterImpl(int cateCode, String filterStr) {
        this.mCateCode = cateCode;
        this.mFilterStr = filterStr;
    }

    public void setView(LabelGridListView view) {
        this.mView = view;
    }

    public void requestVideoData(boolean isLongPress) {
        NetworkApi.getVideoList(UrlWrapper.getVideoListForFilterByCateCode(mCateCode, getPageSize(isLongPress), mCurrPage, mFilterStr), new Observer<VideoGridListBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                LibDeprecatedLogger.d("requestVideoData():onNext().");
                if (value.data != null && value.data.result != null && value.data.result.size() > 0) {
                    mView.updateLabelGridListView(value.data);
                    mCurrSize = value.data.result.size();
                    isRequestData = isRequestData(value.data.count);
                } else {
                    if (mView != null) {
                        mView.displayErrorView();
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestVideoData()--oError(): " + e);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestVideoData():onComplete().");
            }
        });
    }

    public void requestMoreVideoData(boolean isLongPress) {
        if (!isRequestData) {
            return;
        }

        NetworkApi.getVideoList(UrlWrapper.getVideoListForFilterByCateCode(mCateCode, getPageSize(isLongPress), mCurrPage, mFilterStr), new Observer<VideoGridListBean>() {

            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(VideoGridListBean value) {
                LibDeprecatedLogger.d("requestMoreVideoData(): onNext().");
                if (value != null && value.data != null && value.data.result != null
                        && value.data.result.size() > 0) {
                    //update data source of video data if subCateCode equals the selected tab's subCateCode
                    mView.addVideoItems(value.data);
                    mCurrSize += value.data.result.size();
                    isRequestData = isRequestData(value.data.count);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestMoreVideoData(): onError()--" + e.getMessage());
                mView.addVideoItemsError();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestMoreVideoData(): onComplete().");
            }
        });

    }

    private int getPageSize(boolean isLongPress) {
        if (mCurrSize >= (2 * PAGE_SIZE) && mCurrSize % (2 * PAGE_SIZE) == 0 && isLongPress) {
            mCurrPage = mCurrSize / (2 * PAGE_SIZE) + 1;
            return 2 * PAGE_SIZE;
        } else {
            mCurrPage = mCurrSize / PAGE_SIZE + 1;
            return PAGE_SIZE;
        }
    }

    private boolean isRequestData(int count) {
        if (count <= mCurrSize) {
            return false;
        } else {
            return true;
        }
    }

}
