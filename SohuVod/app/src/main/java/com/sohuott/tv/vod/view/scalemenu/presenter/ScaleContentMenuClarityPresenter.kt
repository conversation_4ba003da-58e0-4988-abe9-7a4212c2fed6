package com.sohuott.tv.vod.view.scalemenu.presenter

import android.content.Context
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.setPadding
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.*
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentClarityMenuItem

/**
 * 清晰度
 */
class ScaleContentMenuClarityPresenter constructor(private val context: Context) :
    DefaultPresenter(R.layout.item_sacle_menu_speed_clarity) {
    private var resDrawableId: Int? = null

    private val ivTipsPadding = context.resources.getDimension(R.dimen.x8).toInt()

    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as ScaleContentClarityMenuItem
        val title = viewHolder.getView<TextView>(R.id.tv_scale_menu_clarity_title)
        val ivTip = viewHolder.getView<ImageView>(R.id.iv_scale_menu_clarity)
        val content = viewHolder.getView<TextView>(R.id.tv_scale_menu_clarity)
        val layout = viewHolder.getView<ConstraintLayout>(R.id.cl_scale_menu_clarity)
        val questionRes = context.getResDrawable(R.mipmap.ic_question)
        questionRes?.setBounds(
            0, 0, context.resources.getDimension(R.dimen.x37).toInt(),
            context.resources.getDimension(R.dimen.x37).toInt()
        )
        val questionResSelected = context.getResDrawable(R.mipmap.ic_question_selected)
        questionResSelected?.setBounds(
            0, 0, context.resources.getDimension(R.dimen.x37).toInt(),
            context.resources.getDimension(R.dimen.x37).toInt()
        )
        //标题
        title.isVisible(item.hasTitle)
        item.titleContent?.let {
            title.text = it
            title.isFocusable = true
            title.setCompoundDrawables(
                questionRes,
                null,
                null,
                null
            )
        }
        if (title.isFocusable) {
            title.setOnFocusChangeListener { v, hasFocus ->
                if (hasFocus) {
                    title.setTextColor(context.getResColor( R.color.tv_color_ff6247))
                    title.setCompoundDrawables(
                        questionResSelected,
                        null,
                        null,
                        null
                    )
                } else {
                    title.setTextColor(context.getResColor( R.color.tv_color_e8e8ff))
                    title.setCompoundDrawables(
                        questionRes,
                        null,
                        null,
                        null
                    )
                }
            }
        } else {
            title.onFocusChangeListener = null
        }


        //内容
        content.text = item.content

        setTvContentColor(content, item, false)
        setImgTips(ivTip, item, false)

        //背景
        resDrawableId =
            if (item.isVip) R.drawable.bg_video_detail_header_floating_members_gradual_change else R.drawable.bg_select_focus_e4705c_radius_11
        //焦点
        layout.setOnFocusChangeListener { v, hasFocus ->
            setTvContentColor(content, item, hasFocus)
            setImgTips(ivTip, item, hasFocus)
            if (hasFocus) {
                layout.background = context.getResDrawable(resDrawableId!!)
                v.scaleXY(1.1f)
            } else {
                layout.background = context.getResDrawable(R.drawable.bg_radius_10_color_2effffff)
                v.scaleXY(1f)
            }
        }
    }

    /**
     * 设置文本颜色
     * 情况分为 已经选择完、当前上焦点、未上焦点并且未选择   并且分为vip 和登录的情况
     */
    private fun setTvContentColor(
        content: TextView,
        item: ScaleContentClarityMenuItem,
        hasfocus: Boolean
    ) {
        if (item.hasCurrentSelected) {
            if (hasfocus){
                if (item.isVip){
                    content.setTextColor(context.getResColor(R.color.tv_color_692910))
                }else{
                    content.setTextColor(context.getResColor(R.color.tv_color_e8e8ff))
                }
            }else{
                content.setTextColor(
                    ContextCompat.getColor(
                        context, R.color.tv_color_ff6247
                    )
                )
            }
            return
        }
        if (item.isVip && hasfocus) {
            content.setTextColor(context.getResColor(R.color.tv_color_692910))
        } else if (item.isVip && !hasfocus) {
            content.setTextColor(context.getResColor(R.color.tv_color_f7c2a2))
        } else {
            content.setTextColor(context.getResColor(R.color.tv_color_e8e8ff))
        }
    }

    /**
     * 设置角标
     * 情况分为会员、登录、无
     * 上焦点角标间距增加 不上焦点没有间距
     */
    private fun setImgTips(
        ivTips: ImageView, item: ScaleContentClarityMenuItem,
        hasfocus: Boolean
    ) {
        if (!item.isShowImgTips) {
            return
        }
        val ivLayoutParams = ivTips.layoutParams
        if (item.isVip && hasfocus) {
            ivLayoutParams.width =
                context.resources.getDimension(R.dimen.x82).toInt() + ivTipsPadding
            ivLayoutParams.height =
                context.resources.getDimension(R.dimen.y40).toInt() + ivTipsPadding
            ivTips.setImageDrawable(context.getResDrawable(R.mipmap.ic_members_tips))
            ivTips.setPadding(0, ivTipsPadding, ivTipsPadding, 0)
            ivTips.visible()
        } else if (item.isVip && !hasfocus) {
            ivLayoutParams.width = context.resources.getDimension(R.dimen.x75).toInt()
            ivLayoutParams.height = context.resources.getDimension(R.dimen.y37).toInt()
            ivTips.setPadding(0)
            ivTips.setImageDrawable(context.getResDrawable(R.mipmap.ic_members_tips))
            ivTips.visible()
        } else if (item.isMustLogin && hasfocus) {
            ivLayoutParams.width =
                context.resources.getDimension(R.dimen.x82).toInt() + ivTipsPadding
            ivLayoutParams.height =
                context.resources.getDimension(R.dimen.y40).toInt() + ivTipsPadding
            ivTips.setPadding(0, ivTipsPadding, ivTipsPadding, 0)
            ivTips.setImageDrawable(context.getResDrawable(R.mipmap.ic_login_tips))
            ivTips.visible()
        } else if (item.isMustLogin && !hasfocus) {
            ivLayoutParams.width = context.resources.getDimension(R.dimen.x75).toInt()
            ivLayoutParams.height = context.resources.getDimension(R.dimen.y37).toInt()
            ivTips.setPadding(0)
            ivTips.setImageDrawable(context.getResDrawable(R.mipmap.ic_login_tips))
            ivTips.visible()
        } else {
            ivTips.gone()
        }
        ivTips.layoutParams = ivLayoutParams

    }

}