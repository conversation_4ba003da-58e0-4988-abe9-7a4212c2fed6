package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.graphics.Rect;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.lib.model.HotSearchNew;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.SearchUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.HotSearchLayout;
import com.sohuott.tv.vod.view.SearchInputRecyclerView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by fenglei on 17-6-20.
 */

public class SearchSuggestAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private String mPageName = "6_search";

    public void setPageName(String pageName){
        this.mPageName = pageName;
    }

    public static class SearchSuggestItemDecoration extends RecyclerView.ItemDecoration {
        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            int position = ((RecyclerView.LayoutParams) view.getLayoutParams()).getViewLayoutPosition();
            int viewType = parent.getAdapter().getItemViewType(position);
            if (viewType == SearchSuggestAdapter.VIEW_TYPE_SUGGEST_SEARCH_TEXT_TITLE) {
                if (position == 0) {
                    outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y82);
                } else {
                    outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y50);
                }
            } else if (viewType == SearchSuggestAdapter.VIEW_TYPE_SUGGEST_SEARCH_WITH_PIC) {
                outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y20);
            } else {
                outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y20);
            }
            if (position == parent.getAdapter().getItemCount() - 1) {
                outRect.bottom = (int) view.getContext().getResources().getDimension(R.dimen.y20);
            }
        }
    }


    public static final int VIEW_TYPE_SUGGEST_SEARCH_TEXT_TITLE = 0;
    public static final int VIEW_TYPE_SUGGEST_SEARCH_WITH_PIC = 1;
    public static final int VIEW_TYPE_SUGGEST_SEARCH_NO_PIC = 2;

    class TitleViewHolder extends RecyclerView.ViewHolder {
        public TextView titleTV;

        TitleViewHolder(View view) {
            super(view);
            titleTV = (TextView) view;
        }

        public void bind(int position) {
            if (position == 0) {
                if(calcuAlbumCount() == 0) {
                    titleTV.setText("猜你想搜");
                } else {
                    titleTV.setText("结果推荐");
                }
            } else {
                titleTV.setText("猜你想搜");
            }
        }
    }

    class SearchSuggestWithPicViewHolder extends RecyclerView.ViewHolder {
        public HotSearchLayout hotSearchLayout;

        SearchSuggestWithPicViewHolder(View view) {
            super(view);
            hotSearchLayout = (HotSearchLayout) view;
            hotSearchLayout.setType(HotSearchLayout.SUGGEST);
            hotSearchLayout.setPageName(mPageName);
        }

        public void bind() {
            hotSearchLayout.setSearchUI(searchSuggestList, calcuAlbumCount());
        }
    }

    protected void onClickSearchSuggestNoPicViewHolder(Context context, String searchStr){
        ActivityLauncher.startSearchResultActivity(context, searchStr);
    }

    //少儿override使用，改变cornersize
    public void setCustomFocusBordonFocusChange(View v){
        focusBorderView.setFocusView(v);
    }

    public class SearchSuggestNoPicViewHolder extends RecyclerView.ViewHolder
            implements View.OnClickListener, View.OnFocusChangeListener, View.OnKeyListener {

        public LinearLayout hotSearchNoPicLayout;

        public List<ViewGroup> viewGroupList = new ArrayList<>();
        public List<ImageView> dotIVList = new ArrayList<>();
        public List<TextView> titleTVList = new ArrayList<>();

        SearchSuggestNoPicViewHolder(View view) {
            super(view);
            hotSearchNoPicLayout = (LinearLayout) view;
            for (int i = 0; i < hotSearchNoPicLayout.getChildCount(); i++) {
                ViewGroup viewGroup = (ViewGroup) hotSearchNoPicLayout.getChildAt(i);
                dotIVList.add((ImageView) viewGroup.findViewById(R.id.dotIV));
                titleTVList.add((TextView) viewGroup.findViewById(R.id.titleTV));
                viewGroupList.add(viewGroup);
                viewGroup.setOnClickListener(this);
                viewGroup.setOnFocusChangeListener(this);
                viewGroup.setTag(i);
                viewGroup.setOnKeyListener(this);
            }
        }

        public void bind(int position) {
            int index;
            if(calcuAlbumCount() == 0) {
                index = 2 * (position - 1);
            } else {
                index = calcuAlbumCount() + 2 * (position - 3);
            }
            for (int i = 0; i < titleTVList.size(); i++) {
                if(index + i < searchSuggestList.size()) {
                    HotSearchNew.DataBean dataBean = searchSuggestList.
                            get(index + i);
                    SearchUtil.showSearchTitle(dataBean, titleTVList.get(i));
                    viewGroupList.get(i).setVisibility(View.VISIBLE);
                } else {
                    viewGroupList.get(i).setVisibility(View.GONE);
                }
            }
        }

        @Override
        public void onClick(View v) {
            try {
                TextView textView = (TextView) v.findViewById(R.id.titleTV);
                if(textView != null) {
                    onClickSearchSuggestNoPicViewHolder(v.getContext(), textView.getText().toString());
                    onClickLog(v);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private void onClickLog(View v) {
            try {
                int i = (int)v.getTag();
                int index;
                if(calcuAlbumCount() == 0) {
                    index = 2 * getLayoutPosition() - 1 + i;
                } else {
                    index = 2 * getLayoutPosition() - 5 + i;
                }
                int dateIndex;
                if(calcuAlbumCount() == 0) {
                    dateIndex = 2 * (getLayoutPosition() - 1);
                } else {
                    dateIndex = calcuAlbumCount() + 2 * (getLayoutPosition() - 3);
                }
                HotSearchNew.DataBean dataBean = searchSuggestList.get(dateIndex + i);
                RequestManager.getInstance().onClickSearchSuggestNoPicItem(mPageName, index, dataBean.getAid());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                if (focusBorderView != null) {
//                    focusBorderView.setFocusView(v);
                    setCustomFocusBordonFocusChange(v);
                    FocusUtil.setFocusAnimator(v, focusBorderView);
                    setTVOnFocus(titleTVList.get((int)v.getTag()));
                }
            } else {
                if (focusBorderView != null) {
                    focusBorderView.setUnFocusView(v);
                    FocusUtil.setUnFocusAnimator(v);
                    setTVUnFocus(titleTVList.get((int)v.getTag()));
                }
            }
        }

        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if(event.getAction() == KeyEvent.ACTION_DOWN) {
                if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
                    int index = (int)v.getTag();
                    if(index == 0) {
                        ((SearchInputRecyclerView)(itemView.getParent())).setLastFocusedView(v);
                        return ((SearchInputActivity)v.getContext()).onPressLeftKeyRequestFocus(v);
                    }
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
//                    v.focusSearch(View.FOCUS_DOWN);
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
//                    v.focusSearch(View.FOCUS_UP);
                } else if(event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    int index = (int)v.getTag();
                    if(index == 1) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    private List<HotSearchNew.DataBean> searchSuggestList;

    public SearchSuggestAdapter(List<HotSearchNew.DataBean> searchSuggestList) {
        this.searchSuggestList = searchSuggestList;
    }

    public void setData(List<HotSearchNew.DataBean> searchSuggestList) {
        this.searchSuggestList = searchSuggestList;
    }

    protected View creatSearchNoInputAdapterTitleLayout(ViewGroup parent){
        return LayoutInflater.from(parent.getContext()).inflate(
                R.layout.search_no_input_adapter_title_layout, parent, false);
    }

    protected View creatHotSearchLayout(Context context){
        return new HotSearchLayout(context);
    }

    protected View creatSearchHistoryLayout(ViewGroup parent){
        return LayoutInflater.from(parent.getContext()).inflate(
                R.layout.search_history_layout, parent, false);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v;
        RecyclerView.ViewHolder viewHolder = null;
        if (viewType == VIEW_TYPE_SUGGEST_SEARCH_TEXT_TITLE) {
            v = creatSearchNoInputAdapterTitleLayout(parent);
            viewHolder = new TitleViewHolder(v);
        } else if (viewType == VIEW_TYPE_SUGGEST_SEARCH_WITH_PIC) {
            v = creatHotSearchLayout(parent.getContext());
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    (int) parent.getContext().getResources().getDimension(R.dimen.y410)
            );
            v.setLayoutParams(layoutParams);
            ((HotSearchLayout)v).setFocusBorderView(focusBorderView);
            viewHolder = new SearchSuggestWithPicViewHolder(v);
        } else if (viewType == VIEW_TYPE_SUGGEST_SEARCH_NO_PIC) {
            v = creatSearchHistoryLayout(parent);
            viewHolder = new SearchSuggestNoPicViewHolder(v);
        }
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        int viewType = holder.getItemViewType();
        switch (viewType) {
            case VIEW_TYPE_SUGGEST_SEARCH_TEXT_TITLE:
                TitleViewHolder titleViewHolder = (TitleViewHolder) holder;
                titleViewHolder.bind(position);
                break;
            case VIEW_TYPE_SUGGEST_SEARCH_WITH_PIC:
                SearchSuggestWithPicViewHolder searchSuggestWithPicViewHolder
                        = (SearchSuggestWithPicViewHolder) holder;
                searchSuggestWithPicViewHolder.bind();
                break;
            case VIEW_TYPE_SUGGEST_SEARCH_NO_PIC:
                SearchSuggestNoPicViewHolder searchSuggestNoPicViewHolder
                        = (SearchSuggestNoPicViewHolder) holder;
                searchSuggestNoPicViewHolder.bind(position);
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        if (searchSuggestList == null || searchSuggestList.size() == 0) {
            return 0;
        }
        int albumCount = calcuAlbumCount();
        int keywordCount = (searchSuggestList.size() - albumCount) / 2
                + (searchSuggestList.size() - albumCount) % 2;
        if(albumCount > 0) {
            if(keywordCount > 0) {
                return 3 + keywordCount;
            } else {
                return 2;
            }
        } else {
            if(keywordCount == 0) {
                return 0;
            } else {
                return 1 + keywordCount;
            }
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return VIEW_TYPE_SUGGEST_SEARCH_TEXT_TITLE;
        }
        int albumCount = calcuAlbumCount();
        if (albumCount == 0) {
            return VIEW_TYPE_SUGGEST_SEARCH_NO_PIC;
        } else {
            if (position == 1) {
                return VIEW_TYPE_SUGGEST_SEARCH_WITH_PIC;
            } else if (position == 2) {
                return VIEW_TYPE_SUGGEST_SEARCH_TEXT_TITLE;
            } else {
                return VIEW_TYPE_SUGGEST_SEARCH_NO_PIC;
            }
        }
    }

    private int calcuAlbumCount() {
        int albumCount = 0;
        if(searchSuggestList != null) {
            for(int i = 0; i < searchSuggestList.size(); i++) {
                HotSearchNew.DataBean dataBean = searchSuggestList.get(i);
                if(dataBean != null && SearchUtil.isVRS(dataBean.getCid())) {
                    albumCount++;
                    if(albumCount > 3) {
                        break;
                    }
                } else {
                    break;
                }
            }
        }
        return albumCount;
    }

    protected FocusBorderView focusBorderView;

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.focusBorderView = focusBorderView;
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

}
