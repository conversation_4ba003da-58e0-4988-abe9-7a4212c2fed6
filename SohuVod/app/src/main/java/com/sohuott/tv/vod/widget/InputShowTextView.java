package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;

public class InputShowTextView extends LinearLayout {
    TextView tvShow;
    View underline;
    View mInputAfter;

    public InputShowTextView(Context context) {
        super(context);
        init(context, null);
    }

    public InputShowTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public InputShowTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        LayoutInflater.from(context).inflate(R.layout.layout_input_show_textview, this, true);
        tvShow = (TextView) findViewById(R.id.tv_input_show);
        underline = findViewById(R.id.underline_input_show);
        mInputAfter = findViewById(R.id.view_input_after);
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.InputShowTextView);
            String txt = typedArray.getString(R.styleable.InputShowTextView_txt_to_show);
            if (txt != null) {
                showText(txt);
            }
            typedArray.recycle();
        }
    }

    public void showText(String text) {
        tvShow.setText(text);
        tvShow.setVisibility(View.VISIBLE);
        underline.setVisibility(View.INVISIBLE);
        if (text.equals("")) {
            return;
        }
        mInputAfter.setVisibility(View.VISIBLE);
        tvShow.setVisibility(View.GONE);
    }

    public String getText() {
        return (String) tvShow.getText();
    }

    public void clearText() {
        tvShow.setText("");
        mInputAfter.setVisibility(View.GONE);
        tvShow.setVisibility(View.INVISIBLE);
        underline.setVisibility(View.VISIBLE);
    }
}
