package com.sohuott.tv.vod.utils

import android.content.Context
import com.sohu.lib_utils.PrefUtil

/**
 *
 * @Description
 * @date 2022/3/23 15:39
 * <AUTHOR>
 * @Version 1.0
 */
object PrivacySettingHelper {

    var userInfoContent:String?=null



    private const val RECOMMEND_KEY = "recommend"
    private const val AD_KEY = "ad"

    fun setRecommend(context: Context, isOpen: Boolean) {
        PrefUtil.putInt(RECOMMEND_KEY, if (isOpen) 1 else 0)
    }

    fun getRecommend(context: Context): Int {
        return PrefUtil.getInt(RECOMMEND_KEY, 1)
    }

    fun getRecommendIsOpen(context: Context): Boolean {
        return getRecommend(context) == 1
    }

    fun setAd(context: Context, isOpen: Boolean) {
        PrefUtil.putInt(AD_KEY, if (isOpen) 1 else 0)
    }

    fun getAd(context: Context): Int {
        return PrefUtil.getInt( AD_KEY, 1)
    }

    fun getAdIsOpen(context: Context): Boolean {
        return getAd(context) == 1
    }
}