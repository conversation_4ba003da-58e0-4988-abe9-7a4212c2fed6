package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

import android.text.TextUtils;
import android.view.FocusFinder;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.ListProducerAdapter;
import com.sohuott.tv.vod.adapter.ListProducerSubAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ProducerIntro;
import com.sohuott.tv.vod.lib.model.ProducerVideoList;
import com.sohuott.tv.vod.model.DividerItemDecoration;
import com.sohuott.tv.vod.presenter.ListProducerPresenterImpl;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.CustomRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.ListProducerView;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by XiyingCao on 16-3-14.
 */
public class ProducerActivity extends BaseActivity implements ListProducerView, ListProducerAdapter.FocusController {

    private static final String TAG = "ProducerActivity";
    private static final int MSG_REFRESH = 1;
    private MyHandler mHandler;
    private RelativeLayout mParentView;
    private LoadingView mLoadingView;
    private View mErrorView;
    private LoadingView mListLoadingView;
    private View mSideListErrorView;
    private View mListErrorView;

    private ImageView mHeaderBg;
    private GlideImageView mProducerIcon;
    private TextView mProducerNameView;
    private TextView mProducerFansView;
    private TextView mProducerPlayTimeView;
    private TextView mProducerIntroView;

    private View mRecyclerViewWrapper;
    private TextView mRecyclerViewIndex;
    private CustomRecyclerView mRecyclerView;
    private CustomLinearRecyclerView mAlbumListView;
    private FocusBorderView mLeftFocusView, mRightFocusView;

    private int mProducerId;
    private int mSubListPos;
    private int mLastSelectedPos;
    private ListProducerAdapter mListVideoAdapter;
    private ListProducerSubAdapter mSubListAdapter;
    private CustomLinearLayoutManager mLinearLayoutManager;
    private CustomGridLayoutManager mLayoutManager;
    private ListProducerPresenterImpl mListPresenter;
    private List<ProducerIntro.DataEntity.AlbumsEntity> mMenuNameList;
    private int mListCount;
    private boolean isEnabledScrollListener;
    private boolean isRequestChildFocus;
    private int mSelectedSubId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_list_producer);
        mHandler = new MyHandler(this);
        initView();
        mSubListPos = 0;

        mProducerId = getIntent().getIntExtra(ParamConstant.PARAM_PRODUCER_ID, Integer.MAX_VALUE);
        mSelectedSubId = getIntent().getIntExtra(ParamConstant.PARAM_PRODUCER_SUB_ID, Integer.MAX_VALUE);
        mListVideoAdapter = new ListProducerAdapter(mProducerId, this, mRecyclerView);
        mListVideoAdapter.setFocusBorderView(mRightFocusView);
        mListVideoAdapter.setFocusController(this);
        mLayoutManager = new CustomGridLayoutManager(this, 4);
        mSubListAdapter = new ListProducerSubAdapter(this, mAlbumListView);
        mSubListAdapter.setFocusBorderView(mLeftFocusView);
        mSubListAdapter.setView(this);
        mLinearLayoutManager = new CustomLinearLayoutManager(this);
        mLinearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mLinearLayoutManager.setCustomPadding(getResources().getDimensionPixelOffset(R.dimen.y170),
                getResources().getDimensionPixelOffset(R.dimen.y170));
        mListPresenter = new ListProducerPresenterImpl(mProducerId);
        mMenuNameList = new ArrayList<>();

        initializeCollectionView();
        mListPresenter.setView(this);
        mListPresenter.reloadActorRelativeDate();
        RequestManager.getInstance().onProducerVideoListExposureEvent(mProducerId);
        setPageName("5_list_producer");
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        mSubListPos = 0;
        mProducerId = getIntent().getIntExtra(ParamConstant.PARAM_PRODUCER_ID, 229540375);
        mListVideoAdapter.setActorId(mProducerId);
        mListVideoAdapter.setSelctedPos(0);
        mListVideoAdapter.clear();
        mProducerNameView.setText("");
        mProducerFansView.setText("");
        mProducerPlayTimeView.setText("");
        mProducerIntroView.setText("");
        mListPresenter.setActorId(mProducerId);
        mListPresenter.reloadActorRelativeDate();

        mParentView.setVisibility(View.GONE);
        mLoadingView.setVisibility(View.VISIBLE);
        mErrorView.setVisibility(View.GONE);
        mRecyclerViewWrapper.setVisibility(View.GONE);
        mListLoadingView.setVisibility(View.INVISIBLE);
        mListErrorView.setVisibility(View.GONE);
        mSideListErrorView.setVisibility(View.GONE);

        if (mAlbumListView != null) {
            mAlbumListView.smoothScrollToPosition(0);
        }

        if (mSubListAdapter != null) {
            mSubListAdapter.notifyItemRangeRemoved(0, mSubListAdapter.getItemCount());
            mSubListAdapter.setDataSource(null);
            mSubListAdapter.notifyDataSetChanged();
            mSubListAdapter.setIsFirst(true);
        }

        if (mRecyclerView != null) {
            mRecyclerView.smoothScrollToPosition(0);
        }

        RequestManager.getInstance().onProducerVideoListExposureEvent(mProducerId);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    private void initView() {
        mParentView = (RelativeLayout) findViewById(R.id.parent);
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mErrorView = findViewById(R.id.err_view);
        mLeftFocusView = (FocusBorderView) findViewById(R.id.left_focus_view);
        mRightFocusView = (FocusBorderView) findViewById(R.id.right_focus_view);

        mListLoadingView = (LoadingView) findViewById(R.id.list_loading_view);
        mListErrorView = findViewById(R.id.list_err_view);
        mSideListErrorView = findViewById(R.id.side_list_err_view);

        mHeaderBg = (ImageView) findViewById(R.id.producer_header_bg);
        mProducerIcon = (GlideImageView) findViewById(R.id.producer_icon);
        mProducerNameView = (TextView) findViewById(R.id.producer_name);
        mProducerFansView = (TextView) findViewById(R.id.producer_fans);
        mProducerPlayTimeView = (TextView) findViewById(R.id.producer_watch_counts);
        mProducerIntroView = (TextView) findViewById(R.id.producer_intro);

        mRecyclerViewWrapper = findViewById(R.id.recyclerview_wrapper);
        mRecyclerViewIndex = (TextView) findViewById(R.id.count_index);
        mRecyclerView = (CustomRecyclerView) findViewById(R.id.list);
        mRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mRecyclerView.setOnScrollListener(new FinishScrollListener());

        int right = getResources().getDimensionPixelSize(R.dimen.x65);
        int bottom = getResources().getDimensionPixelSize(R.dimen.y28);
        mRecyclerView.addItemDecoration(new DividerItemDecoration(0, 0, right, bottom));
        mRecyclerView.setPadding(getResources().getDimensionPixelOffset(R.dimen.x64),
                getResources().getDimensionPixelOffset(R.dimen.y10),
                getResources().getDimensionPixelOffset(R.dimen.x42),
                getResources().getDimensionPixelOffset(R.dimen.y10));
        mProducerIntroView.setText("暂无简介");
        mAlbumListView = (CustomLinearRecyclerView) findViewById(R.id.sub_list);
        mAlbumListView.setOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    if (mLeftFocusView != null && mAlbumListView.getFocusedChild() != null) {
                        //Set focus view
                        mLeftFocusView.setFocusView(mAlbumListView.getFocusedChild());
                        mLeftFocusView.setScaleUp(1);

                        //Set unselected item
                        if (mAlbumListView.indexOfChild(mAlbumListView.getFocusedChild()) == 3) {
                            mAlbumListView.getChildAt(2).setSelected(false);
                        } else if (mAlbumListView.indexOfChild(mAlbumListView.getFocusedChild()) == 0) {
                            if (mAlbumListView.getChildAt(1) != null) {
                                mAlbumListView.getChildAt(1).setSelected(false);
                            }
                        }

                        //Set selected item
                        mAlbumListView.getFocusedChild().setSelected(true);
                    }
                }
            }
        });
    }

    private void initializeCollectionView() {
        mRecyclerView.setAdapter(mListVideoAdapter);
        mRecyclerView.setLayoutManager(mLayoutManager);
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());


        mAlbumListView.setAdapter(mSubListAdapter);
        mAlbumListView.setLayoutManager(mLinearLayoutManager);
    }

    @Override
    public void add(List<ProducerVideoList.DataEntity.ResultEntity.VideoDetails> models) {
        mListVideoAdapter.add(models);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
                View focusedView = getCurrentFocus();
                View nextFocusedView = FocusFinder.getInstance().
                        findNextFocus(mParentView, focusedView, View.FOCUS_DOWN);
                if (focusedView != null) {
                    if (nextFocusedView != null) {
                        if (focusedView.getParent() == mAlbumListView) {
                            if (nextFocusedView.getParent() != mAlbumListView) {
                                return true;
                            }
                        }
                    }

                }
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                View focusedView = getCurrentFocus();
                View nextFocusedView = FocusFinder.getInstance().
                        findNextFocus(mParentView, focusedView, View.FOCUS_UP);
                if (focusedView != null) {
                    if (nextFocusedView != null) {
                        if (focusedView.getParent() == mAlbumListView) {
                            if (nextFocusedView.getParent() != mAlbumListView) {
                                return true;
                            }
                        }
                    }
                }
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
                View focusedView = getCurrentFocus();
                View nextFocusedView = FocusFinder.getInstance().
                        findNextFocus(mParentView, focusedView, View.FOCUS_LEFT);
                if (focusedView != null && focusedView.getParent() == mRecyclerView) {
                    if (nextFocusedView == null) {
                        if (mAlbumListView.findViewHolderForAdapterPosition(mSubListPos) != null
                                && mAlbumListView.findViewHolderForAdapterPosition(mSubListPos).itemView != null) {
                            mAlbumListView.findViewHolderForAdapterPosition(mSubListPos).itemView.requestFocus();
                        }
                    } else {
                        if (nextFocusedView.getParent() != mRecyclerView) {
                            if (mAlbumListView.findViewHolderForAdapterPosition(mSubListPos) != null
                                    && mAlbumListView.findViewHolderForAdapterPosition(mSubListPos).itemView != null) {
                                mAlbumListView.findViewHolderForAdapterPosition(mSubListPos).itemView.requestFocus();
                            }
                        }
                    }
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public void showLoading() {
        mParentView.setVisibility(View.GONE);
        mLoadingView.setVisibility(View.VISIBLE);
        mErrorView.setVisibility(View.GONE);

        mRecyclerViewWrapper.setVisibility(View.GONE);
        mListLoadingView.setVisibility(View.INVISIBLE);
        mListErrorView.setVisibility(View.GONE);
        mSideListErrorView.setVisibility(View.GONE);
        disableLastItemViewListener();
    }

    @Override
    public void hideLoading() {
        mParentView.setVisibility(View.VISIBLE);
        mLoadingView.setVisibility(View.INVISIBLE);
        mErrorView.setVisibility(View.GONE);

        mRecyclerViewWrapper.setVisibility(View.GONE);
        mListLoadingView.setVisibility(View.INVISIBLE);
        mListErrorView.setVisibility(View.GONE);
        mSideListErrorView.setVisibility(View.GONE);
        disableLastItemViewListener();
    }

    @Override
    public void onError() {
        mParentView.setVisibility(View.GONE);
        mLoadingView.setVisibility(View.INVISIBLE);
        mErrorView.setVisibility(View.VISIBLE);

        mRecyclerViewWrapper.setVisibility(View.GONE);
        mListLoadingView.setVisibility(View.INVISIBLE);
        mListErrorView.setVisibility(View.GONE);
        mSideListErrorView.setVisibility(View.GONE);
        disableLastItemViewListener();
    }

    @Override
    public void showListLoading() {
        mParentView.setVisibility(View.VISIBLE);
        mLoadingView.setVisibility(View.INVISIBLE);
        mErrorView.setVisibility(View.GONE);

        mRecyclerViewWrapper.setVisibility(View.GONE);
        mListLoadingView.setVisibility(View.VISIBLE);
        mListErrorView.setVisibility(View.GONE);
        mSideListErrorView.setVisibility(View.GONE);
        disableLastItemViewListener();
    }

    @Override
    public void hideListLoading() {
        mParentView.setVisibility(View.VISIBLE);
        mLoadingView.setVisibility(View.INVISIBLE);
        mErrorView.setVisibility(View.GONE);

        mRecyclerViewWrapper.setVisibility(View.VISIBLE);
        mListLoadingView.setVisibility(View.INVISIBLE);
        mListErrorView.setVisibility(View.GONE);
        mSideListErrorView.setVisibility(View.GONE);
        activateLastItemViewListener();
    }

    @Override
    public void onListError() {
        mParentView.setVisibility(View.VISIBLE);
        mLoadingView.setVisibility(View.INVISIBLE);
        mErrorView.setVisibility(View.GONE);

        mRecyclerViewWrapper.setVisibility(View.GONE);
        mListLoadingView.setVisibility(View.INVISIBLE);
        mListErrorView.setVisibility(View.VISIBLE);
        mSideListErrorView.setVisibility(View.GONE);
        disableLastItemViewListener();
    }

    @Override
    public void activateLastItemViewListener() {
        enableSearchOnFinish();
    }

    @Override
    public void disableLastItemViewListener() {
        disableSearchOnFinish();
    }

    @Override
    public void setProducerIcon(String iconUrl) {
        mProducerIcon.setCircleImageRes(iconUrl);
    }

    @Override
    public void setProducerName(String name) {
        if (!TextUtils.isEmpty(name)) {
            mProducerNameView.setText(name);
        } else {
            mProducerNameView.setText("");
        }
    }

    @Override
    public void setProducerFanCount(int fanCount) {
//        StringBuffer stringBuffer = new StringBuffer("粉丝 ");
//        stringBuffer.append(FormatUtils.formatCount(fanCount));
//        mProducerFansView.setText(stringBuffer.toString());
    }

    @Override
    public void setProducerPlayCount(long playCount) {
//        StringBuffer stringBuffer = new StringBuffer("观看次数 ");
//        stringBuffer.append(FormatUtils.formatCount(playCount));
//        mProducerPlayTimeView.setText(stringBuffer.toString());
    }

    @Override
    public void setProducerIntro(String intro) {
        if (!TextUtils.isEmpty(intro)) {
            mProducerIntroView.setText(intro);
        }
    }

    @Override
    public void showAlbumList(List<ProducerIntro.DataEntity.AlbumsEntity> videoDetailsList) {
        //reset data list
        mMenuNameList.clear();
        mMenuNameList.addAll(videoDetailsList);
        //reset data source of adapter
        mSubListAdapter.notifyItemRangeRemoved(0, mSubListAdapter.getItemCount());
        mSubListAdapter.setDataSource(videoDetailsList);
        mSubListAdapter.notifyDataSetChanged();
        mSubListAdapter.setSelectedPos(mSelectedSubId);
    }

    @Override
    public void refreshRightVideoList(int dataIndex, int viewIndex) {
        if (dataIndex >= 0 && dataIndex < mMenuNameList.size() && dataIndex != mSubListPos) {
            Message message = new Message();
            message.what = MSG_REFRESH;
            Bundle bundle = new Bundle();
            bundle.putInt("dataIndex", dataIndex);
            bundle.putInt("viewIndex", viewIndex);
            message.setData(bundle);//mes利用Bundle传递数据
            mHandler.removeMessages(MSG_REFRESH);
            mHandler.sendMessageDelayed(message, 300);
        }
    }

    @Override
    public void onSideListError() {
        mAlbumListView.setVisibility(View.GONE);
        mRecyclerViewWrapper.setVisibility(View.GONE);
        mSideListErrorView.setVisibility(View.VISIBLE);
    }

    @Override
    public void setCount(int count) {
        mListCount = count;
        mRecyclerViewIndex.setVisibility(View.INVISIBLE);
    }

    @Override
    public void updateCountText(int mSelctedPos) {
        if (mListCount > 8) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(mSelctedPos / 4 + 1).append("/").append((mListCount - 1) / 4 + 1).append("行");
            mRecyclerViewIndex.setText(stringBuilder.toString());
            mRecyclerViewIndex.setVisibility(View.VISIBLE);
        } else {
            mRecyclerViewIndex.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public RecyclerView.Adapter getAdapter() {
        return mRecyclerView.getAdapter();
    }

    @Override
    public void onNextFocusView(boolean isRequestFocus) {
        this.isRequestChildFocus = isRequestFocus;
    }

    private void enableSearchOnFinish() {
//        mRecyclerView.setOnScrollListener(new FinishScrollListener());
        isEnabledScrollListener = true;
    }

    private void disableSearchOnFinish() {
//        mRecyclerView.setOnScrollListener(null);
        isEnabledScrollListener = false;
    }

    private class FinishScrollListener extends RecyclerView.OnScrollListener {
        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            if (!isEnabledScrollListener) {
                return;
            }

            int lastVisibleItemPosition = mLayoutManager.findLastVisibleItemPosition() + 1;
            int modelsCount = mListVideoAdapter.getItemCount();

            if (lastVisibleItemPosition + 5 >= modelsCount) {
                mListPresenter.onLastItemViewed();
            }
            if (mRecyclerView.getFocusedChild() == null) {
                return;
            }
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                if (mRecyclerView == null) {
                    return;
                }

                if (mRecyclerView.getFocusedChild() == null) {
                    return;
                }

                if (isRequestChildFocus) {
                    mRecyclerView.getChildAt(2 * mLayoutManager.getSpanCount()).requestFocus();
                    isRequestChildFocus = false;
                }

                RecyclerView.ViewHolder viewHolder = mRecyclerView.getChildViewHolder(
                        mRecyclerView.getFocusedChild());
                if (viewHolder != null && viewHolder.itemView != null) {
                    mRightFocusView.setFocusView(viewHolder.itemView);
                    FocusUtil.setFocusAnimator(viewHolder.itemView, mRightFocusView);
                }
            }
        }
    }

    private class MyHandler extends Handler {
        private final WeakReference<ProducerActivity> mActivity;

        public MyHandler(ProducerActivity activity) {
            super();
            mActivity = new WeakReference<ProducerActivity>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            if (mActivity.get() == null) {
                return;
            }
            int what = msg.what;
            switch (what) {
                case MSG_REFRESH:
                    int dataIndex = msg.getData().getInt("dataIndex");
                    int viewIndex = msg.getData().getInt("viewIndex");
                    RequestManager.getInstance().onProducerVideoSubListClickEvent(mProducerId, mMenuNameList.get(dataIndex).playlistid);
                    mListVideoAdapter.clear();
                    mListPresenter.searchForProducerFirstVideo(mMenuNameList.get(dataIndex).playlistid);
                    if (mAlbumListView.findViewHolderForAdapterPosition(dataIndex) != null
                            && mAlbumListView.findViewHolderForAdapterPosition(dataIndex).itemView != null) {
                        mAlbumListView.findViewHolderForAdapterPosition(dataIndex).itemView.setSelected(true);
                    }

                    if (mAlbumListView.findViewHolderForAdapterPosition(mSubListPos) != null
                            && mAlbumListView.findViewHolderForAdapterPosition(mSubListPos).itemView != null) {
                        mAlbumListView.findViewHolderForAdapterPosition(mSubListPos).itemView.setSelected(false);
                    }

//                    if (Math.abs(mLastSelectedPos - viewIndex) == 2) {
//                        mAlbumListView.getChildAt((mLastSelectedPos + viewIndex) / 2).setSelected(false);
//                    }

                    mLastSelectedPos = viewIndex;
                    mSubListPos = dataIndex;
                    break;
                default:
                    break;
            }
        }
    }
}
