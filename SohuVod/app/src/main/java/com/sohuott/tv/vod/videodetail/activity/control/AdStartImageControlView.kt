package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Html
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.drake.net.time.Interval
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.sh.ott.video.ad.AdRequestFactory
import com.sh.ott.video.component.AdOpenControlComponent
import com.sh.ott.video.contor.ShAdVideoController
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.PlayerLogger
import com.sh.ott.video.player.controller.VideoController
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohu.ott.ads.sdk.iterface.ILoader
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.PayActivity
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.utils.ActivityLauncher
import java.util.concurrent.TimeUnit

/**
 * 开屏广告图片
 */
open class AdStartImageControlView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : BaseControlComponent(context, attrs, defStyleAttr), AdOpenControlComponent {
    private var soHuVideoController: ShAdVideoController? = null

    private var img: ImageView? = null
    private var tvAdRightEnter: TextView
    private var tvCountdown: TextView
    private var tvAdFlag: TextView

    //进入视频详情页的提示imageview 位置在右下角
    private var enterDetailHintImageView: ImageView? = null
    private var mAdEnterDetailHintLayout: ViewGroup? = null
    private var mBottomBg: View? = null
    private var mTopBg: View? = null

    private var mTimer: Interval? = null
    private val timerOut = 5L
    private val flagDefault = 3
    private val flagHide = 0
    var falg = flagDefault
    private var aid: Int = 0
    private var dataType: Int = -1
    private var isPayAd: Boolean = false
    var isTopView: Boolean = false
    var topViewPath: String? = null

    private var adType: Int = -1


    private var mAdStartCallBack: OnAdStartControlCallBack? = null
    override fun onAdOpenError() {
        super.onAdOpenError()
        mAdStartCallBack?.adError()
        AppLogger.e("AdOpenControlView", "onAdOpenError")
    }

    override fun onAdOpenVideo(url: String, extParams: MutableMap<String, Any?>) {
        super.onAdOpenVideo(url, extParams)
        onMapParams(extParams)
        mAdStartCallBack?.adStart(topViewPath, isTopView)
    }

    override fun onAdRequestType(type: Int) {
        super.onAdRequestType(type)
        adType = type
    }

    override fun onProgressChanged(duration: Long, position: Long) {
        super.onProgressChanged(duration, position)
        val curPos = (position / 1000)
        val time = (duration / 1000) - (position / 1000)
        AppLogger.d("onAdVideoTime : position:$position   duration:$duration   time:$time")
        AdRequestFactory.getInstants().reportStartPageAdPlayTime(
            curPos.toInt(),
            ILoader.TopViewSource.VIDEO_FIRST
        )
        if (time < 1 || position == 0L) {
            return
        }
        img?.visibility = GONE
        visibility = VISIBLE
        tvCountdown.visibility = VISIBLE
        tvCountdown.text = Util.getBootCountDownBackText(time)
    }

    override fun onRenderMeasure(
        videoWidth: Int,
        videoHeight: Int,
        widthSpecSize: Int,
        heightSpecSize: Int,
        measuredWidth: Int,
        measuredHeight: Int
    ) {

    }


    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        super.onPlayStateChanged(playState, extras)
        when (playState) {
            PlayerConstants.VideoState.PREPARED -> {
            }

            PlayerConstants.VideoState.PLAYING -> {
                AdRequestFactory.getInstants().reportStartPageAd(false)
                soHuVideoController?.startUpdateProgress()
                mAdStartCallBack?.adSuccess()
                //todo 开启倒计时
            }

            PlayerConstants.VideoState.PLAYBACK_COMPLETED -> {
                soHuVideoController?.stopUpdateProgress()
                mAdStartCallBack?.adFinish()
            }

            PlayerConstants.VideoState.ERROR -> {
                soHuVideoController?.stopUpdateProgress()
                mAdStartCallBack?.adError()
            }
        }
    }


    private fun onMapParams(extParams: MutableMap<String, Any?>) {
        falg = extParams[AdRequestFactory.AD_EXT_PARAMS_START_FLAG]?.let {
            it as Int
        } ?: flagDefault
        aid = extParams[AdRequestFactory.AD_EXT_PARAMS_START_AID]?.let {
            it as Int
        } ?: 0
        dataType = extParams[AdRequestFactory.AD_EXT_PARAMS_START_DATA_TYPE]?.let {
            it as Int
        } ?: -1
        isPayAd = extParams[AdRequestFactory.AD_EXT_PARAMS_START_ENABLE_PAY_AD]?.let {
            it as Boolean
        } ?: false
        if (Util.isSupportTouchVersion(context)) {
            img?.setOnClickListener { v: View? -> startPayOrVideoDetailActivity() }
        }
        isTopView = extParams[AdRequestFactory.AD_EXT_PARAMS_START_ENABLE_TOP_VIEW]?.let {
            it as Boolean
        } ?: false
        topViewPath = extParams[AdRequestFactory.AD_EXT_PARAMS_START_FOCUS_VIDEO]?.let {
            it as String
        } ?: ""


        AppLogger.d(
            "onMapParams extParams is ? ${extParams.toString()}"
        )
    }


    var isShowAdImage = false
    override fun onAdOpenImage(url: String, extParams: MutableMap<String, Any?>) {
        onMapParams(extParams)
        AppLogger.d("AdOpenControlView", "url:$url")
        try {

            Glide.with(this).load(url + "?time=" + System.currentTimeMillis())
                .placeholder(resources.getDrawable(R.drawable.ic_splash))
                .error(resources.getDrawable(R.drawable.ic_splash))
                .into(object : CustomTarget<Drawable>() {
                    override fun onLoadStarted(placeholder: Drawable?) {
                        super.onLoadStarted(placeholder)
                        visibility = VISIBLE
                        onCreateTimer()
                        mAdStartCallBack?.adStart(topViewPath, isTopView)
                        img?.setImageDrawable(placeholder)
                        AppLogger.e("AdOpenControlView", "onLoadStarted placeholder：$placeholder")
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        transition: Transition<in Drawable>?
                    ) {
                        isShowAdImage = true
                        mAdStartCallBack?.adSuccess()
                        AppLogger.e("AdOpenControlView", "onResourceReady")
                        if (falg == flagHide) {
                            tvAdRightEnter.visibility = VISIBLE
                        }
                        if (falg == flagDefault) {
                            tvAdFlag.visibility = VISIBLE
                        }
                        if (aid != 0 && dataType != -1 && !isPayAd) {
                            mAdEnterDetailHintLayout?.visibility = VISIBLE
                            enterDetailHintImageView?.setImageDrawable(resources.getDrawable(R.drawable.ok))
                            mBottomBg?.visibility = VISIBLE
                            enterDetailHintImageView?.visibility = VISIBLE
                            mTopBg?.visibility = VISIBLE
                            val pathInfo = HashMap<String, String>(1)
                            pathInfo["pageId"] = "1048"
                            RequestManager.getInstance()
                                .onAllEvent(EventInfo(10135, "imp"), pathInfo, null, null)
                        }
                        img?.setImageDrawable(resource)
                        AdRequestFactory.getInstants().reportStartPageAd(false)
                        mTimer?.start()
                        mAdStartCallBack?.adStartDetail(isPayAd, isShown, aid, dataType)
                    }

                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        super.onLoadFailed(errorDrawable)
                        onLoadImgError()
                    }

                    override fun onLoadCleared(placeholder: Drawable?) {
                        AppLogger.e("AdOpenControlView", "onLoadCleared")

                    }
                })

        } catch (e: Throwable) {
            AppLogger.e("AdOpenControlView", "onLoadFailed:${e.message}")
            onLoadImgError()
        }
    }

    private fun onLoadImgError() {
        mAdStartCallBack?.adError()
        mTimer?.cancel()
        img?.setImageDrawable(resources.getDrawable(R.drawable.ic_splash))
    }

    private fun onCreateTimer() {
        mTimer = Interval(timerOut, 1, TimeUnit.SECONDS, 0).subscribe {
            tvCountdown.visibility = VISIBLE
            val onlyTime = "${timerOut - it}"
            val time = if (isShowAdImage) "按返回键跳过 ".plus(onlyTime) else onlyTime
            tvCountdown.text = time
            AppLogger.e("AdOpenControlView", "onCreateTimer:$it")
        }.finish {
            mAdStartCallBack?.adFinish()
            AppLogger.e("AdOpenControlView", "onCreateTimer finish")
        }
    }

    fun setBootTips(bootTips: String?) {
        try {
            tvAdRightEnter.text = Html.fromHtml(bootTips)
        } catch (e: Exception) {
            AppLogger.e("setBootTips error ${e.localizedMessage}")
        }

    }

    fun setAdStartCallBack(callback: OnAdStartControlCallBack) {
        mAdStartCallBack = callback
    }


    fun startPayOrVideoDetailActivity() {
        //当有跳转URL  启动图片加载完成的时候可以跳转
        AppLogger.d(
            "startPayOrVideoDetailActivity isPayAd ? $isPayAd"
        )
//        if (isPayAd) {
//            startPayActivity()
//        } else if (mAdEnterDetailHintLayout!!.isShown) {
//            startVideoDetailActiviy()
//            val pathInfo = java.util.HashMap<String, String>(1)
//            pathInfo["pageId"] = "1048"
//            RequestManager.getInstance()
//                .onAllEvent(EventInfo(10306, "clk"), pathInfo, null, null)
//        }
    }

    private fun startVideoDetailActiviy() {
        AppLogger.d(
            "startVideoDetailActiviy aid ? $aid dataType $dataType"
        )
        if (aid != 0 && dataType != -1) {
            mTimer?.cancel()
            ActivityLauncher.startVideoDetailBackToLauncher(
                context,
                Constant.PAGE_BOOT,
                aid,
                dataType,
                true
            )
            //            getActivity().finish(); //TODO
//            onFragmentInteractionListener.onHideFragment(mAdCommon)
            AppLogger.d("onHideFragment startLauncher 3")
        }
    }


    private fun startPayActivity() {
        mTimer?.cancel()
        ActivityLauncher.startPayActivity(context, true, PayActivity.PAY_SOURCE_BOOT_ACTIVITY)
        //        getActivity().finish(); //TODO:
//        onFragmentInteractionListener.onHideFragment(mAdCommon)
        AppLogger.d("onHideFragment startLauncher 4")
    }


    override fun attachController(controller: VideoController) {
        super.attachController(controller)
        if (controller is ShAdVideoController) {
            soHuVideoController = controller
        }
    }

    init {
        layoutInflater.inflate(R.layout.layout_ad_control_open_view, this)
        visibility = GONE
        img = findViewById(R.id.bg_start_img)
        tvAdRightEnter = findViewById(R.id.ad_flag_right_enter)
        tvCountdown = findViewById(R.id.tv_countdown)
        tvAdFlag = findViewById(R.id.ad_flag)
        enterDetailHintImageView = findViewById(R.id.ad_enterdetail_hint);
        mAdEnterDetailHintLayout = findViewById(R.id.ad_enterdetail_hint_layout);
        mBottomBg = findViewById(R.id.bottom_bg);
        mTopBg = findViewById(R.id.top_bg);
    }
}

interface OnAdStartControlCallBack {
    fun adStart(topViewPath: String?, isTopView: Boolean)

    fun adSuccess()

    fun adError()

    fun adFinish()

    fun adStartDetail(isPayAd: Boolean, isShown: Boolean, aid: Int, dataType: Int)

}