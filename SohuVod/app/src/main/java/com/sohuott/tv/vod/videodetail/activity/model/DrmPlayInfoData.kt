package com.sohuott.tv.vod.videodetail.activity.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
@Parcelize
 data class DrmPlayInfoData(
    /**
     * 200	成功
     * 1000	超过每日播放限制  ，降级为marlin bb 协议
     * 1001	Mkey校验不通过
     * 1002	地域限制
     * -1	视频不存在
     */
    @SerializedName("code")
    var code: Int,
    @SerializedName("data")
    var data: DrmPlayInfo? = null,
    @SerializedName("msg")
    var msg: String? = null
): Parcelable {
}

/**
 * playUrlM3u8	m3u8索引文件	String
 * playUrlMpd	dash索引文件	String
 * license	播放的许可证	String
 */
@Parcelize
data class DrmPlayInfo(
    @SerializedName("kid")
    var kid: String? = null,
    @SerializedName("license")
    var license: String? = null,
    @SerializedName("playUrlM3u8")
    var playUrlM3u8: String? = null,
    @SerializedName("playUrlMpd")
    var playUrlMpd: String? = null,
    @SerializedName("pssh")
    var pssh: String? = null,
): Parcelable