package com.sohuott.tv.vod.search

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.widget.ScrollView
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger

class CustomScrollview(context: Context, attrs: AttributeSet) :ScrollView(context, attrs) {

    override fun scrollBy(x: Int, y: Int) {
        LibDeprecatedLogger.d("scrollBy x $x y $y")
        if (y < 0) {
            super.scrollBy(x, y)
        } else {
            super.scrollBy(x, y)
        }
    }

    override fun fling(velocityY: Int) {
        super.fling(velocityY)
    }

    override fun scrollTo(x: Int, y: Int) {
        LibDeprecatedLogger.d("scrollTo x $x y $y")
        super.scrollTo(x, y)
    }
    private val extraScrollingDistance = 200 // 提前滚动距离

    override fun computeScrollDeltaToGetChildRectOnScreen(rect: Rect?): Int {
        val scrollDelta = super.computeScrollDeltaToGetChildRectOnScreen(rect)
        LibDeprecatedLogger.d("scrollDelta $scrollDelta")
        // 根据滚动的方向，增加额外的滚动距离
        return if (scrollDelta > 0) {
            scrollDelta + extraScrollingDistance
        } else if (scrollDelta < 0) {
            scrollDelta - extraScrollingDistance
        } else {
            0
        }
    }

}