package com.sohuott.tv.vod.videodetail;

import com.lib_dlna_core.SohuDlnaManger;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;

import java.util.HashMap;

public class VideoDetailRequestManager {
    private static final String pageId = "1041";
    private static final String scalePageId = "1045";
    private static final String scaleDlnaPageId = "1059";

    //页面曝光
    public static void pageExposure(){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), mPathInfo, null,
                null);
    }

    //选集tab曝光
    public static void tabExposure(String name){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);

        HashMap mMemoInfo = new HashMap<>();
        mMemoInfo.put("tab_name", name);
        RequestManager.getInstance().onAllEvent(new EventInfo(10307, "imp"), mPathInfo, null,
                mMemoInfo);
    }
    //运营位点击上报
    public static void recommendListClick(String pageId, String aid, String index){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);

        HashMap mMemoInfo = new HashMap<>();
        mMemoInfo.put("ctype", "0");
        mMemoInfo.put("playlistId", aid);
        mMemoInfo.put("index", index);

        RequestManager.getInstance().onAllEvent(new EventInfo(10326, "clk"), mPathInfo, null,
                mMemoInfo);
    }

    //tab视频点击
    public static void tabClick(int eventId, String aid, String vid,boolean isMenu, int tvStype){
        HashMap mPathInfo = new HashMap<>();
        if (isMenu){
            if (SohuDlnaManger.getInstance().getIsDlna()&&eventId==10282){
                mPathInfo.put("pageId", scaleDlnaPageId);
            }else {
                mPathInfo.put("pageId", scalePageId);
            }
        }else {
            mPathInfo.put("pageId", pageId);
        }

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("vid", vid);
        mObjectInfo.put("playlistId", aid);

        HashMap mMemoInfo = new HashMap<>();
        if (tvStype != -1) {
            if (tvStype == 1) {
                mMemoInfo.put("type", "0");
            } else if (tvStype == 38){
                mMemoInfo.put("type", "1");
            } else {
                mMemoInfo.put("type", "2");
            }
        }
        RequestManager.getInstance().onAllEvent(new EventInfo(eventId, "clk"), mPathInfo, mObjectInfo,
                mMemoInfo);
    }

    //同系列视频曝光
    public static void seriesExposure(String aid) {
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"),
                mPathInfo,
                mObjectInfo, null);
    }

    //为你推荐曝光
    public static void recommendExposure(String index, String aid, String pdna){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2001");
        mPathInfo.put("index", index);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        HashMap mMemoInfo = new HashMap<>();
        mMemoInfo.put("pdna", pdna);

        RequestManager.getInstance().onAllEvent(new EventInfo(10285, "imp"), mPathInfo, mObjectInfo,
                mMemoInfo);
    }

    //为你推荐点击
    public static void recommendClick(String index, String aid, String pdna){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2001");
        mPathInfo.put("index", index);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        HashMap mMemoInfo = new HashMap<>();
        mMemoInfo.put("pdna", pdna);

        RequestManager.getInstance().onAllEvent(new EventInfo(10286, "clk"), mPathInfo, mObjectInfo,
                mMemoInfo);
    }

    public static void actorExposure() {
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2002");

        RequestManager.getInstance().onAllEvent(new EventInfo(10156, "imp"), mPathInfo, null,
                null);
    }

    public static void actorClick() {
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2002");

        RequestManager.getInstance().onAllEvent(new EventInfo(10133, "clk"), mPathInfo, null,
                null);
    }

    //标签曝光
    public static void labelExposure(String index, String aid, String tagName){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2003");
        mPathInfo.put("index", index);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        HashMap mMemoInfo = new HashMap<>();
        mMemoInfo.put("tag_name", tagName);

        RequestManager.getInstance().onAllEvent(new EventInfo(10287, "imp"), mPathInfo, mObjectInfo,
                mMemoInfo);
    }

    //标签点击
    public static void labelClick(String index, String aid, String tagName){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2003");
        mPathInfo.put("index", index);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        HashMap mMemoInfo = new HashMap<>();
        mMemoInfo.put("tag_name", tagName);

        RequestManager.getInstance().onAllEvent(new EventInfo(10288, "clk"), mPathInfo, mObjectInfo,
                mMemoInfo);
    }

    //影人剧场曝光
    public static void theaterExposure(String index, String aid){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2004");
        mPathInfo.put("index", index);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        RequestManager.getInstance().onAllEvent(new EventInfo(10289, "imp"), mPathInfo, mObjectInfo,
                null);
    }

    //影人剧场点击
    public static void theaterClick(String index, String aid){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2004");
        mPathInfo.put("index", index);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), mPathInfo, mObjectInfo,
                null);
    }

    //分类曝光
    public static void classifyExposure(String index, String aid,  String cateCode, String cateName){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2005");
        mPathInfo.put("index", index);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        HashMap mMemoInfo = new HashMap<>();
        mMemoInfo.put("catecode", cateCode);
        mMemoInfo.put("catename", cateName);

        RequestManager.getInstance().onAllEvent(new EventInfo(10290, "imp"), mPathInfo, mObjectInfo,
                mMemoInfo);
    }

    //分类点击
    public static void classifyClick(String index, String aid, String cateCode, String cateName){
        HashMap mPathInfo = new HashMap<>();
        mPathInfo.put("pageId", pageId);
        mPathInfo.put("columnId", "2005");
        mPathInfo.put("index", index);

        HashMap mObjectInfo = new HashMap<>();
        mObjectInfo.put("type", "视频");
        mObjectInfo.put("playlistId", aid);

        HashMap mMemoInfo = new HashMap<>();
        mMemoInfo.put("catecode", cateCode);
        mMemoInfo.put("catename", cateName);


        RequestManager.getInstance().onAllEvent(new EventInfo(10291, "clk"), mPathInfo, mObjectInfo,
                mMemoInfo);
    }

}
