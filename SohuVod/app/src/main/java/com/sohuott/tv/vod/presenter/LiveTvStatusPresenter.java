package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.LiveTvStatusModel;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * 电视直播数据处理
 *
 * <AUTHOR>
 * @Date Created on 2020/1/13.
 */
public class LiveTvStatusPresenter extends IBasePresenter implements Runnable {

    private int mRoomId;

    public LiveTvStatusPresenter(IDataListener listener, int roomId) {
        super(listener);
        this.mRoomId = roomId;
    }

    @Override
    public void loadData() {
        LibDeprecatedLogger.d("Load live status!");
        NetworkApi.requestLiveTvStatus(mRoomId, new Observer<LiveTvStatusModel>() {

            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(LiveTvStatusModel value) {
                if (value == null) {
                    LibDeprecatedLogger.w("Load live status data fail!");
                } else if (value.getData() == null) {
                    LibDeprecatedLogger.w("Load live status data error! " + value.getMessage());
                } else {
                    LibDeprecatedLogger.d("Live status data: " + value.toString());
                    //sendData(value);
                }
                sendData(value);
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("Load live status data error!", e);
            }

            @Override
            public void onComplete() {

            }
        });

    }

    private void sendData(LiveTvStatusModel data) {
        if (mListener != null) {
            try {
                mListener.getData(data);
            } catch (NullPointerException e) {
                LibDeprecatedLogger.e("This listener had be released", e);
            }
        }
    }

    @Override
    public void run() {
        loadData();
    }
}
