package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.FocusFinder;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ListWelfareModel;
import com.sohuott.tv.vod.lib.rvhelper.BaseQuickAdapter;
import com.sohuott.tv.vod.lib.rvhelper.BaseViewHolder;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.lib.widgets.ViewPager;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * use in {@link com.sohuott.tv.vod.activity.WelfareActivity}
 *
 * <AUTHOR>
 * created at 2018/1/30
 */
public class WelfareRecyclerView extends CustomLinearRecyclerView implements RecyclerView.OnChildAttachStateChangeListener {


    public static final int SPAN_COUNT = 3;
    private CustomGridLayoutManager mLayoutManager;
    private WelfareViewPager mBanner;
    private BaseQuickAdapter<ListWelfareModel.DataEntity.ActivityListEntity, BaseViewHolder> mActivityAdapter;
    private int mSmoothPos = -1;
    private FocusBorderView mFocusBorderView;
    private LinearLayout mIndicatorLayout;


    private static final int MSG_BANNER_FOCUS = 1;
    private static final int MSG_RV_FOCUS = 0;

    private static class InnerHandler extends Handler {
        WeakReference<WelfareRecyclerView> mWrapper;
        InnerHandler(WelfareRecyclerView welfareRecyclerView){
            mWrapper = new WeakReference<>(welfareRecyclerView);
        }
        @Override
        public void handleMessage(@NonNull Message msg) {
            WelfareRecyclerView welfareRecyclerView = mWrapper.get();
            if (welfareRecyclerView == null){
                return;
            }
            if (welfareRecyclerView.isShown()) {
                if (msg.what == MSG_RV_FOCUS) {
                    View view = (View) msg.obj;
                    welfareRecyclerView.mFocusBorderView.setVisibility(View.VISIBLE);
                    welfareRecyclerView.mFocusBorderView.setFocusView(view);
                    FocusUtil.setFocusAnimator(view, welfareRecyclerView.mFocusBorderView, 1, 100);
                } else if (msg.what == MSG_BANNER_FOCUS) {
                    welfareRecyclerView.mFocusBorderView.setVisibility(View.VISIBLE);
                    if (welfareRecyclerView.mBanner.hasFocus() && welfareRecyclerView.mBanner.getCurrentView() != null) {
                        welfareRecyclerView.mFocusBorderView.setFocusView(welfareRecyclerView.mBanner.getCurrentView());
                        FocusUtil.setFocusAnimator(welfareRecyclerView.mBanner.getCurrentView(), welfareRecyclerView.mFocusBorderView, 1f, 100);
                    }
                }
            }
        }
    }
    private Handler mHandler = new InnerHandler(this);


    public WelfareRecyclerView(Context context) {
        super(context);
        init(context);
    }

    public WelfareRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public WelfareRecyclerView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

    private void init(Context context) {
        addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                if (parent.getChildLayoutPosition(view) == 0) {
                    outRect.bottom = (int) getResources().getDimension(R.dimen.x32);
                } else {
                    outRect.bottom = (int) getResources().getDimension(R.dimen.x19);
                }
                outRect.right = (int) getResources().getDimension(R.dimen.x19);
            }
        });
        addOnChildAttachStateChangeListener(this);
        mLayoutManager = new CustomGridLayoutManager(getContext(), SPAN_COUNT);
        setLayoutManager(mLayoutManager);
        setHasFixedSize(true);
        addOnScrollListener(new OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                mHandler.removeMessages(MSG_RV_FOCUS);
                mHandler.removeMessages(MSG_BANNER_FOCUS);
            }

            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    View view = getFocusedChild();
                    if (view != null) {
                        LibDeprecatedLogger.d("setFocus onScrollStateChanged");
                        if (view instanceof ConstraintLayout) {
                            mHandler.sendMessageDelayed(mHandler.obtainMessage(MSG_RV_FOCUS, view), 100);
                        } else {
                            mHandler.sendMessageDelayed(mHandler.obtainMessage(MSG_BANNER_FOCUS, view), 100);
                        }
                    }
                }
            }
        });
        initHeaderAndAdapter();
    }

    private void initHeaderAndAdapter() {
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM.dd HH:mm");
        final View headerView = LayoutInflater.from(getContext()).inflate(R.layout.welfare_header_activity_list, null);
        mBanner = (WelfareViewPager) headerView.findViewById(R.id.pager_welfare);
        mIndicatorLayout = (LinearLayout) headerView.findViewById(R.id.ll_indicator);
        mBanner.setPageMargin(-(int) getResources().getDimension(R.dimen.x150));
        mBanner.setOffscreenPageLimit(3);
        mBanner.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                if (mIndicatorLayout.getChildCount() > 0) {
                    final int selectedIndex = position % mIndicatorLayout.getChildCount();
                    for (int index = 0; index < mIndicatorLayout.getChildCount(); index++) {
                        mIndicatorLayout.getChildAt(index).setSelected(index == selectedIndex);
                    }
                }
            }
        });

        mActivityAdapter = new BaseQuickAdapter<ListWelfareModel.DataEntity.ActivityListEntity, BaseViewHolder>
                (R.layout.item_welfare_activity_list) {
            @Override
            protected void convert(BaseViewHolder helper, ListWelfareModel.DataEntity.ActivityListEntity item) {
                ((GlideImageView) helper.getView(R.id.activity_list_iv_product))
                        .setImageRes(item.poster);
                helper.setText(R.id.tv_product_name, item.name);
                helper.setText(R.id.tv_product_score, String.valueOf(item.score) + getResources().getString(R.string.score));
                ((TextView) helper.getView(R.id.tv_product_time)).setText(getResources().getString(R.string.welfare_activity_time)
                        + simpleDateFormat.format(new Date(item.startTime))
                        + "-" + simpleDateFormat.format(new Date(item.endTime)));
                if (item.status != ListWelfareModel.STATUS_NOT_START) {
                    if (item.totalCount > 0) {
                        int percent = (int) (((item.totalCount - item.leftCount) * 1.0f / item.totalCount) * 100);
                        helper.setText(R.id.tv_product_sale, getResources().getString(R.string.sell)
                                + percent + getResources().getString(R.string.percent));
                    }
                } else {
                    helper.setText(R.id.tv_product_sale, R.string.welfare_will_begin);
                }
            }
        };
        mActivityAdapter.bindToRecyclerView(this);
        mActivityAdapter.addHeaderView(headerView);
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        mFocusBorderView = focusBorderView;
        mBanner.setFocusBorderView(mFocusBorderView);
    }

    public WelfareViewPager getHeaderPager() {
        return mBanner;
    }

    public void addBannerClickListener(OnClickListener onClickListener) {
        mBanner.setOnItemClickListener(onClickListener);
    }

    public void setBannerFocusChangeListener(OnFocusChangeListener onFocusChangeListener) {
        if (!Util.isSupportTouchVersion(getContext())) {
            mBanner.setOnItemFocusChangeListener(onFocusChangeListener);
        }
    }

    public void addOnItemClickListener(BaseQuickAdapter.OnItemClickListener onItemClickListener) {
        mActivityAdapter.setOnItemClickListener(onItemClickListener);

    }

    public void setOnItemFocusChangedListener(BaseQuickAdapter.OnItemFocusChangedListener onItemFocusChangedListener) {
        if (!Util.isSupportTouchVersion(getContext())) {
            mActivityAdapter.setOnItemFocusChangedListener(onItemFocusChangedListener);
        }
    }

    public void setHeaderData(List<ListWelfareModel.DataEntity.ActivityListEntity> bannerList) {
        if (bannerList.size() == 0) {
            mBanner.setVisibility(View.GONE);
            return;
        }
        mBanner.setData(bannerList);
        int width = (int) getResources().getDimension(R.dimen.x10);
        mIndicatorLayout.setVisibility(View.VISIBLE);
        for (int index = 0; index < bannerList.size(); index++) {
            ImageView imageView = new ImageView(getContext());
            imageView.setImageResource(R.drawable.welfare_indicator);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(width, width);
            if (index != 0) {
                layoutParams.leftMargin = (int) getResources().getDimension(R.dimen.x20);
            }
            layoutParams.topMargin = (int) getResources().getDimension(R.dimen.x45);
            mIndicatorLayout.addView(imageView, layoutParams);
        }
        mIndicatorLayout.getChildAt(0).setSelected(true);
    }

    public void setAdapterData(List<ListWelfareModel.DataEntity.ActivityListEntity> list) {
        mActivityAdapter.setNewData(list);
    }


    @Override
    public View focusSearch(View focused, int direction) {
        if (direction == View.FOCUS_DOWN) {
            if (focused instanceof GlideImageView) {
                mBanner.startPauseLoop(true);
            }
            if (focused.getLayoutParams() instanceof RecyclerView.LayoutParams) {
                final int curPos = mLayoutManager.getPosition(focused);
//                if (curPos == mActivityAdapter.getItemCount() - 1) {
//                    return focused;
//                }
                int p = 0;
                int count = mActivityAdapter.getItemCount()-1;
                p = curPos + SPAN_COUNT;
                if (count - p < 0) {
                    p = curPos + (SPAN_COUNT + (count - p));
                }
                mSmoothPos = p;
                View next = mLayoutManager.findViewByPosition(mSmoothPos);
                smoothScrollToPosition(mSmoothPos);
                return next;
            }
        } else if (direction == View.FOCUS_UP) {
            final View nextFocusView = FocusFinder.getInstance()
                    .findNextFocus(this, focused, direction);
            if (nextFocusView instanceof GlideImageView) {
                mBanner.startPauseLoop(false);
            }
        } else if (direction == View.FOCUS_RIGHT) {
            if (focused.getLayoutParams() instanceof RecyclerView.LayoutParams) {
                final int curPos = mLayoutManager.getPosition(focused);
                View next = mLayoutManager.findViewByPosition(curPos + 1);
                if (curPos == mActivityAdapter.getItemCount() - 1) {
                    return focused;
                } else if (curPos % SPAN_COUNT == 0) {
//                    if (next == null) {
                        mSmoothPos = curPos + 1;
                        smoothScrollToPosition(mSmoothPos);
//                    }
                    return mLayoutManager.findViewByPosition(curPos + 1);
                }else {
                    int count = mActivityAdapter.getItemCount();
                    if (curPos + 1>count){
                        return next;
                    }
                    smoothScrollToPosition(curPos + 1);
                    return mLayoutManager.findViewByPosition(curPos + 1);
                }
            }
        } else if (direction == View.FOCUS_LEFT) {
            if (focused.getLayoutParams() instanceof RecyclerView.LayoutParams) {
                final int curPos = mLayoutManager.getPosition(focused);
                View next = mLayoutManager.findViewByPosition(curPos -1);
                if (curPos == 1) {
                    return focused;
                } else if (curPos % SPAN_COUNT == 1) {
//                    if (next == null) {
                        mSmoothPos = curPos - 1;
                        smoothScrollToPosition(mSmoothPos);
//                    }
                    return mLayoutManager.findViewByPosition(curPos -1);
                }else {
                    int count = mActivityAdapter.getItemCount();
                    if (curPos - 1>0){
                        smoothScrollToPosition(curPos - 1);
                        return mLayoutManager.findViewByPosition(curPos - 1);
                    }
                    return next;
                }
            }
        }

        View next = super.focusSearch(focused, direction);
        return next;
    }


    @Override
    public CustomGridLayoutManager getLayoutManager() {
        return mLayoutManager;
    }

    @Override
    public void onChildViewAttachedToWindow(View view) {
        //while smoothScroll,requestFocus
        RecyclerView.ViewHolder viewHolder = findContainingViewHolder(view);
        if (viewHolder != null) {
            int pos = viewHolder.getAdapterPosition();
            if (pos == mSmoothPos) {
                viewHolder.itemView.requestFocus();
                mSmoothPos = -1;
            }
        }
    }

    @Override
    public void onChildViewDetachedFromWindow(View view) {

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mHandler.removeMessages(MSG_BANNER_FOCUS);
        mHandler.removeMessages(MSG_RV_FOCUS);
    }
}
