package com.sohuott.tv.vod.widget.lb;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.google.gson.Gson;
import com.sohu.ott.base.lib_user.UserConfigHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper;
import com.sohuott.tv.vod.lib.db.greendao.PushMessageData;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.TopInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohu.lib_utils.FormatUtils;

import java.lang.ref.WeakReference;
import java.util.Date;
import java.util.HashMap;

/**
 * Created by music on 2021/8/1.
 */

public class TopViewBar extends ConstraintLayout implements View.OnClickListener {

    String TAG = "TopViewBar";

    private WeakReference<Context> mContext;
    private ConstraintLayout mSearchView;
    private TopViewBar.OnTopViewBarInteractionListener mListener;
    private LoginUserInformationHelper mHelper;

    LayoutInflater inflater;

    TextView mVipTitle, mVipDesc, mVipTitleFocused, mVipDescFocused, mLoginTitle, mBeiAn;
    ConstraintLayout mSearch, mHistory, mLogin, mVip, mMessage;
    View mNewMessage;
    TopInfo mInfo;
    ImageView mLoginImg;
    HashMap<String, String> pathInfo;
    HashMap<String, String> memoInfo;
    String beiAn;

    public void setBeiAn(String beiAn) {
        this.beiAn = beiAn;
        if (mBeiAn != null) {
            mBeiAn.setText(beiAn);
        }
    }

    public void setData(TopInfo data) {
        this.mInfo = data;
        setUI();
    }

    private void setUI() {
        if (mBeiAn != null && beiAn != null) {
            mBeiAn.setText(beiAn);
        }
        if (mInfo == null || mInfo.getData() == null) return;
        if (mVipTitle != null && mVipTitle.getVisibility() == VISIBLE) {
            mVipTitle.setText(mInfo.getData().getVipCommodityText().getData().getOtherText());
            mVipDesc.setText(mInfo.getData().getVipCommodityText().getData().getDiscountsText());
        }
        if (mVipTitleFocused != null && mVipTitleFocused.getVisibility() == VISIBLE) {
            mVipTitleFocused.setText(mInfo.getData().getVipCommodityText().getData().getOtherText());
            mVipDescFocused.setText(mInfo.getData().getVipCommodityText().getData().getDiscountsText());
        }

        if (Util.hasNewMsg(mContext.get())) {
            if (mNewMessage != null) {
                mNewMessage.setVisibility(VISIBLE);
            }
        } else {
            String messageInfo = Util.getNewMsgInfo(mContext.get(), "");
            Gson gson = new Gson();
            PushMessageData lastMessageData = gson.fromJson(messageInfo, PushMessageData.class);
            if (mInfo.getData().getSystemMessage().getData().isEmpty()) {
                if (mNewMessage != null) {
                    mNewMessage.setVisibility(GONE);
                }
                return;
            }

            if (lastMessageData == null || mInfo.getData().getSystemMessage().getData().get(0).getId() != lastMessageData.getMsgId()) {
                Util.setNewMsg(mContext.get(), true);
                PushMessageData myServerMessage = new PushMessageData();
                myServerMessage.setMsgId((long) mInfo.getData().getSystemMessage().getData().get(0).getId());
                myServerMessage.setCover(mInfo.getData().getSystemMessage().getData().get(0).getPicUrl());
                myServerMessage.setTitle(mInfo.getData().getSystemMessage().getData().get(0).getName());
                Date date = FormatUtils.strToDate(mInfo.getData().getSystemMessage().getData().get(0).getCreateTime());
                long expire = 0;
                if (date != null) {
                    expire = date.getTime();
                }
                myServerMessage.setExpire(expire);
                Util.setNewMsgInfo(mContext.get(), gson.toJson(mInfo.getData().getSystemMessage().getData().get(0)));
                String message_str = "";
                try {
                    message_str = gson.toJson(myServerMessage);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                Util.setNewMsgInfo(mContext.get(), message_str);

                if (mNewMessage != null) {
                    mNewMessage.setVisibility(VISIBLE);
                }
            } else {
                if (mNewMessage != null) {
                    mNewMessage.setVisibility(GONE);
                }
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.search_view:
                RequestManager.getInstance().onAllEvent(new EventInfo(10137, "clk"), null, null, null);
                ActivityLauncher.startSearchActivity(mContext.get());
//                if (mHelper.getIsLogin()) {
//                    ActivityLauncher.startAccountLogOffActivity(mContext.get());
//                }
                break;
            case R.id.history_view:
                RequestManager.getInstance().onAllEvent(new EventInfo(10138, "clk"), null, null, null);

                ActivityLauncher.startListUserRelatedActivity(mContext.get(), ListUserRelatedActivity.LIST_INDEX_HISTORY);
                break;
            case R.id.login_view:
                if (mHelper.getIsLogin()) {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10154, "clk"), null, null, null);

                    mListener.onTopViewBarInteraction(Uri.parse(Constant.URI_CLICK_MY));

                    zoomOut();

//                    ActivityLauncher.startListUserRelatedActivity(mContext.get(), ListUserRelatedActivity.LIST_INDEX_MY);
                } else {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10136, "clk"), null, null, null);

                    ActivityLauncher.startLoginActivity(mContext.get(), Constant.LAUNCHER_SOURCE, 0);
                }

                HashMap<String, String> memoInfo = new HashMap<String, String>();
                memoInfo.put("isLogin", mHelper.getIsLogin() ? "1" : "0");


                break;
            case R.id.vip_view:
                if (mHelper.isVip()) {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10151, "clk"), null, null, null);

                } else {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10139, "clk"), null, null, null);
                }

                ActivityLauncher.startPayActivity(mContext.get(), PayActivity.PAY_SOURCE_TOP_BAR_STAT);
                break;
            case R.id.message_view:
                RequestManager.getInstance().onAllEvent(new EventInfo(10140, "clk"), null, null, null);

                ActivityLauncher.startMyMessageActivity(mContext.get());
                break;
        }
    }

    public interface OnTopViewBarInteractionListener {
        void onTopViewBarInteraction(Uri uri);
    }

    public TopViewBar(Context context) {
        super(context);
        init(context);
    }

    public TopViewBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public TopViewBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mContext = new WeakReference<>(context);
        setDescendantFocusability(FOCUS_AFTER_DESCENDANTS);

        pathInfo = new HashMap<>();
        pathInfo.put("pageId", "1030");
        memoInfo = new HashMap<>();
        memoInfo.put("CPU", Build.CPU_ABI);
        memoInfo.put("android", Build.VERSION.RELEASE);
        memoInfo.put("issw_screenblur", String.valueOf(Util.getHuapingParams()));
        memoInfo.put("isblock_drm_widevine", String.valueOf(UserConfigHelper.getUserDrmPlayParams()));
        String ish265_definition = "0";
        if (UserConfigHelper.getH265hVers().isEmpty()) {
            ish265_definition = "0";
        } else {
            ish265_definition = "1";
        }
        memoInfo.put("ish265_definition", ish265_definition);

        int ish265old = UserConfigHelper.getDefaultH265();
        if (!PlaySettingHelper.getH265HasChange()) {
            ish265old = UserConfigHelper.getH265();
        }
        memoInfo.put("ish265old", String.valueOf(ish265old));
        memoInfo.put("ishttps", String.valueOf(Util.getHttpsParams()));
        memoInfo.put("player", String.valueOf(UserConfigHelper.getUserPlayParams()));
        RequestManager.getInstance().onAllEvent(new EventInfo(10153, "imp"), pathInfo, null, memoInfo);

        inflater.from(mContext.get()).
                inflate(R.layout.layout_launcher_top_view, this, true);
        if (context instanceof TopViewBar.OnTopViewBarInteractionListener) {
            mListener = (OnTopViewBarInteractionListener) context;
        } else {
            throw new RuntimeException(context.toString() +
                    "must implement OnTopViewBarInteractionListener");
        }

        mHelper = LoginUserInformationHelper.getHelper(mContext.get());

        initView(this);
    }

    private void initView(View v) {
        mBeiAn = v.findViewById(R.id.registration);
        mVipTitle = (TextView) v.findViewById(R.id.tv_open_vip);
        mVipDesc = (TextView) v.findViewById(R.id.tv_open_vip_desc);
        mVipTitleFocused = (TextView) v.findViewById(R.id.cl_vip_zoom_title);
        mVipDescFocused = (TextView) v.findViewById(R.id.cl_vip_zoom_desc);
        mSearch = (ConstraintLayout) v.findViewById(R.id.search_view);
        mHistory = (ConstraintLayout) v.findViewById(R.id.history_view);
        mLogin = (ConstraintLayout) v.findViewById(R.id.login_view);
        mVip = (ConstraintLayout) v.findViewById(R.id.vip_view);
        mMessage = (ConstraintLayout) v.findViewById(R.id.message_view);
        mNewMessage = v.findViewById(R.id.new_message);
        mLoginImg = (ImageView) v.findViewById(R.id.user_view_img);
        mLoginTitle = (TextView) v.findViewById(R.id.tv_login_view);

        if (mSearch != null) {
            mSearch.setOnClickListener(this);
            mHistory.setOnClickListener(this);
            mLogin.setOnClickListener(this);
            mVip.setOnClickListener(this);
            mMessage.setOnClickListener(this);
            mMessage.setOnKeyListener(new OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if (event.getAction() == event.ACTION_DOWN && keyCode == event.KEYCODE_DPAD_RIGHT) {
                        return true;
                    }
                    return false;
                }
            });

            if (mHelper.getIsLogin()) {
                mLoginTitle.setText("我的");
            } else {
                mLoginTitle.setText("登录");
            }

            mVip.setOnFocusChangeListener(new OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    mVipDescFocused.setSelected(hasFocus);
                }
            });
        }
    }


    public void zoomIn() {
        LibDeprecatedLogger.d("zoomIn: ");
        mListener.onTopViewBarInteraction(Uri.parse(Constant.URI_UP_TOP_BAR));
        removeAllViews();

        HashMap<String, String> memoInfo = new HashMap<String, String>();
        memoInfo.put("isLogin", mHelper.getIsLogin() ? "1" : "0");
        RequestManager.getInstance().onAllEvent(new EventInfo(10134, "imp"), null, null, memoInfo);

        View view = LayoutInflater.from(mContext.get()).
                inflate(R.layout.layout_launcher_top_view_focused, this, true);
        mSearchView = (ConstraintLayout) view.findViewById(R.id.search_view);
        startZoomAnim(view, getResources().getDimensionPixelSize(R.dimen.y127), getResources().getDimensionPixelSize(R.dimen.y200));
        initView(view);
        setUI();
    }

    //是否为放大状态
    public boolean isZoomOut() {
        LibDeprecatedLogger.d("getheight : " + getHeight());
        return getHeight() == getResources().getDimensionPixelSize(R.dimen.y200);
    }

    public void zoomOut() {

        LibDeprecatedLogger.d("zoomOut: ");
        removeAllViews();

        RequestManager.getInstance().onAllEvent(new EventInfo(10153, "imp"), pathInfo, null, null);

        View view = LayoutInflater.from(mContext.get()).
                inflate(R.layout.layout_launcher_top_view, this, true);
        startZoomAnim(view, getResources().getDimensionPixelSize(R.dimen.y200), getResources().getDimensionPixelSize(R.dimen.y127));
        initView(view);
        setUI();
    }

    public void refreshTopData() {
        if (mLoginTitle == null) return;
        if (mHelper.getIsLogin()) {
            mLoginTitle.setText("我的");
        } else {
            mLoginTitle.setText("登录");
        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        LibDeprecatedLogger.d("dispatchKeyEvent: " + event);
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN ||
                    event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
//                setFocusStyle(false);
                zoomOut();
                mListener.onTopViewBarInteraction(Uri.parse(Constant.URI_DOWN_TOP_BAR));
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    public void startValAnim(int from, int to, ValueAnimator.AnimatorUpdateListener listener,
                             long duration) {
        ValueAnimator animator = ValueAnimator.ofInt(from, to);
        //设置动画时长
        animator.setDuration(duration);
        //设置插值器，当然你可以不用
        animator.setInterpolator(new DecelerateInterpolator());
        //回调监听
        animator.addUpdateListener(listener);

        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
                if (mSearchView != null)
                    mSearchView.requestFocus();
            }

            @Override
            public void onAnimationEnd(Animator animator) {

            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        //启动动画
        animator.start();

    }

    public <V extends View> void startZoomAnim(@NonNull final V v, int from, int to) {
        startValAnim(from, to, new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                /* 主要还是通过获取布局参数设置宽高 */
                ViewGroup.LayoutParams lp = getLayoutParams();
                //获取改变时的值
                int size = Integer.valueOf(animation.getAnimatedValue().toString());
                lp.height = size;
                setLayoutParams(lp);
            }
        }, 300);
    }


}
