package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.content.res.Resources;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/9/20.
 */
public class CarouselSettingAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final String TAG = CarouselSettingAdapter.class.getSimpleName();

    private static final int COUNT_COMMON = 3;
    private static final int COUNT_TOUCH = 2;

    private static final int POSITION_DEFINITION = 0; // definition
    private static final int POSITION_FRAME = 1;  // frame
    private static final int POSITION_KEY = 2; // key

    private boolean mIsRightKeyPressed = true;

    private Context mContext;

    private List<Integer> mDefinitionList;

    public CarouselSettingAdapter(Context context) {
        mContext = context;
    }

    private RecyclerView mRecyclerView;

    @Override
    public void onAttachedToRecyclerView(RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        mRecyclerView = recyclerView;
    }

    @Override
    public void onDetachedFromRecyclerView(RecyclerView recyclerView) {
        super.onDetachedFromRecyclerView(recyclerView);
        mRecyclerView = null;
    }

    @Override
    public int getItemCount() {
        if (Util.isSupportTouchVersion(mContext)) {
            return COUNT_TOUCH;
        } else {
            return COUNT_COMMON;
        }
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext.getApplicationContext()).inflate(R.layout.carousel_setting, parent, false);

        CarouselSettingViewHolder vh = new CarouselSettingViewHolder(view);

        return vh;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        CarouselSettingViewHolder vh = (CarouselSettingViewHolder) holder;

        Resources resources = mContext.getApplicationContext().getResources();

        if (position == POSITION_DEFINITION) {
            vh.title.setText(resources.getString(R.string.txt_activity_carousel_player_setting_definition));
            int oldDefinition = Util.getCarouselSettingDefinition(mContext);
            if (null != mDefinitionList && mDefinitionList.size() > 0) {
                int index = 0;
                for (int i = 0; i < mDefinitionList.size(); i++) {
                    Integer definition = mDefinitionList.get(i);
                    if (oldDefinition == definition.intValue()) {
                        index = i;
                        break;
                    }
                }
                Integer definition = mDefinitionList.get(index);
                vh.content.setText(defNameMap.get(definition));
                Util.putCarouselSettingDefinition(mContext, definition);
            }
        } else if (position == POSITION_FRAME) {
            vh.title.setText(resources.getString(R.string.txt_activity_carousel_player_setting_frame));
            vh.content.setText(Util.getCarouselSettingFrame(mContext) == Constant.FRAME_ORIGIN ?
                    resources.getString(R.string.txt_activity_carousel_player_setting_frame_origin)
                    : resources.getString(R.string.txt_activity_carousel_player_setting_frame_fullscreen));
        } else if (position == POSITION_KEY) {
            vh.title.setText(resources.getString(R.string.txt_activity_carousel_player_setting_key));
            vh.content.setText(Util.getCarouselSettingKey(mContext) == Constant.KEY_REVERSE ?
                    resources.getString(R.string.txt_activity_carousel_player_setting_key_reverse)
                    : resources.getString(R.string.txt_activity_carousel_player_setting_key_positive));
        }

        int tmp = Util.getCarouselSettingFocusPosition(mContext);
        if (tmp < 0) {
            tmp = 0;
        }
        if (position == tmp) {
            if(!Util.isSupportTouchVersion(mContext)){
                vh.rootView.requestFocus();
            }
        } else {
            vh.arrowLeft.setImageResource(R.drawable.icon_setting_arrow_left_default);
            vh.arrowRight.setImageResource(R.drawable.icon_setting_arrow_right_default);
        }
    }

    SparseArray<String> defNameMap;

    public void setDefinitionList(List<Integer> list, SparseArray<String> defNameMap) {
        mDefinitionList = list;
        this.defNameMap = defNameMap;
    }


    private class CarouselSettingViewHolder extends RecyclerView.ViewHolder
            implements View.OnFocusChangeListener, View.OnClickListener {

        private ViewGroup rootView;

        private ImageView arrowLeft;
        private ImageView arrowRight;

        private TextView title;
        private TextView content;

        private int nextValue;

        public CarouselSettingViewHolder(View itemView) {
            super(itemView);

            rootView = (ViewGroup) itemView;
            if(Util.isSupportTouchVersion(itemView.getContext())){
                rootView.setBackgroundResource(R.drawable.carousel_item_selector_ontouch);
            }
            initUI();
            rootView.setOnFocusChangeListener(this);

            rootView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View view, int keyCode, KeyEvent event) {
                    int position = getAdapterPosition();

                    Util.putCarouselSettingFocusPosition(mContext, position);
                    if (event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                            if (position == 0) {
                                rootView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.out_to_up));
                                return true;
                            }
                        } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
                            if (position == (getItemCount() - 1)) {
                                rootView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.out_to_up));
                                return true;
                            }
                        } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT
                                || event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                            arrowLeft.setImageResource(R.drawable.icon_setting_arrow_left_default);
                            arrowRight.setImageResource(R.drawable.icon_setting_arrow_right_default);
                            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
                                arrowLeft.setImageResource(R.drawable.icon_setting_arrow_left_focus);
                                mIsRightKeyPressed = false;
                            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                                arrowRight.setImageResource(R.drawable.icon_setting_arrow_right_focus);
                                mIsRightKeyPressed = true;
                            }
                            update(position);
                            return true;
                        }
                    }
                    return false;
                }
            });
        }

        @Override
        public void onFocusChange(View view, boolean hasFocus) {
            if (view == rootView) {
                int colorId = R.color.color_carousel_player_05;
                int arrowLeftResId = R.drawable.icon_setting_arrow_left_default;
                int arrowRightResId = R.drawable.icon_setting_arrow_right_default;

                if(!Util.isSupportTouchVersion(mContext)){
                    if (hasFocus) {
                        colorId = R.color.color_search_15;
                        if (mIsRightKeyPressed) {
                            arrowRightResId = R.drawable.icon_setting_arrow_right_focus;
                        } else {
                            arrowLeftResId = R.drawable.icon_setting_arrow_left_focus;
                        }
                    }
                    content.setTextColor(mContext.getApplicationContext().getResources().getColor(colorId));
                }
                arrowLeft.setImageResource(arrowLeftResId);
                arrowRight.setImageResource(arrowRightResId);
            }
        }

        private void initUI() {
            arrowLeft = (ImageView) rootView.findViewById(R.id.arrow_left);
            arrowRight = (ImageView) rootView.findViewById(R.id.arrow_right);
            title = (TextView) rootView.findViewById(R.id.title);
            content = (TextView) rootView.findViewById(R.id.content);
            if (Util.isSupportTouchVersion(mContext)) {
                arrowLeft.setImageResource(R.drawable.icon_setting_arrow_left_default);
                arrowRight.setImageResource(R.drawable.icon_setting_arrow_right_default);
                arrowLeft.setOnClickListener(this);
                arrowRight.setOnClickListener(this);
            }
        }

        private long mLastClickTime;

        @Override
        public void onClick(View v) {
            final long time = System.currentTimeMillis();
            if ((time - mLastClickTime) < 300) {
                return;
            }

            mLastClickTime=time;
            if (v.getId() == R.id.arrow_left) {
                mIsRightKeyPressed = false;
            } else {
                mIsRightKeyPressed = true;
            }
            int position = getAdapterPosition();
            Util.putCarouselSettingFocusPosition(mContext, position);
            update(position);
        }

        private void refreshItem(int position) {
            Resources resources = mContext.getApplicationContext().getResources();
            String text = content.getText().toString();
            if (TextUtils.isEmpty(text)) {
                return;
            }
            if (position == POSITION_DEFINITION) {
                int index = defNameMap.indexOfValue(text);
                setNextDefinition(defNameMap.keyAt(index));
            } else if (position == POSITION_FRAME) {
                if (text.equals(resources.getString(R.string.txt_activity_carousel_player_setting_frame_origin))) {
                    content.setText(resources.getString(R.string.txt_activity_carousel_player_setting_frame_fullscreen));
                    nextValue = Constant.FRAME_FULLSCREEN;
                } else if (text.equals(resources.getString(R.string.txt_activity_carousel_player_setting_frame_fullscreen))) {
                    content.setText(resources.getString(R.string.txt_activity_carousel_player_setting_frame_origin));
                    nextValue = Constant.FRAME_ORIGIN;
                }
                RequestManager.getInstance().onEvent("5_carousel_setting_menu_key_show", "5_carousel_setting_frame_click", nextValue + "", null, null, null, null);
            } else if (position == POSITION_KEY) {
                if (text.equals(resources.getString(R.string.txt_activity_carousel_player_setting_key_reverse))) {
                    content.setText(resources.getString(R.string.txt_activity_carousel_player_setting_key_positive));
                    nextValue = Constant.KEY_POSITIVE;
                } else if (text.equals(resources.getString(R.string.txt_activity_carousel_player_setting_key_positive))) {
                    content.setText(resources.getString(R.string.txt_activity_carousel_player_setting_key_reverse));
                    nextValue = Constant.KEY_REVERSE;
                }
                RequestManager.getInstance().onEvent("5_carousel_setting_menu_key_show", "5_carousel_setting_key_click", nextValue + "", null, null, null, null);
            }
        }

        private void setNextDefinition(int definition) {
            if (null != mDefinitionList && mDefinitionList.size() > 0) {
                for (int i = 0; i < mDefinitionList.size(); i++) {
                    Integer obj = mDefinitionList.get(i);
                    if (obj.intValue() == definition) {
                        int tmp;
                        if (mIsRightKeyPressed) {
                            if (i == (mDefinitionList.size() - 1)) {
                                tmp = mDefinitionList.get(0).intValue();
                            } else {
                                tmp = mDefinitionList.get(i + 1).intValue();
                            }
                        } else {
                            if (i == 0) {
                                tmp = mDefinitionList.get((mDefinitionList.size() - 1)).intValue();
                            } else {
                                tmp = mDefinitionList.get(i - 1).intValue();
                            }
                        }
                        content.setText(defNameMap.get(tmp));
                        nextValue = tmp;
                        RequestManager.getInstance().onEvent("5_carousel_setting_menu_key_show", "5_carousel_setting_denifition_click", nextValue + "", null, null, null, null);
                        break;
                    }
                }
            }
        }

        private void update(int position) {
            refreshItem(position);
            if (nextValue > 0) {
                if (position == POSITION_DEFINITION) {
                    Util.putCarouselSettingDefinition(mContext, nextValue);
                } else if (position == POSITION_FRAME) {
                    Util.putCarouselSettingFrame(mContext, nextValue);
                } else if (position == POSITION_KEY) {
                    Util.putCarouselSettingKey(mContext, nextValue);
                }

                if (onSettingItemClickListener != null) {
                    onSettingItemClickListener.onSettingItemClick(nextValue);
                }
            }
        }

    }

    OnSettingItemClickListener onSettingItemClickListener;

    public void setOnSettingItemClickListener(OnSettingItemClickListener onSettingItemClickListener) {
        this.onSettingItemClickListener = onSettingItemClickListener;
    }

    public interface OnSettingItemClickListener {
        void onSettingItemClick(int nextValue);
    }
}
