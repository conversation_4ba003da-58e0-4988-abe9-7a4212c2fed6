package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.leanback.widget.Presenter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.LocationConfigInfo;
import com.sohuott.tv.vod.lib.utils.StringUtils;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager;
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

public class ActorPresenter extends Presenter {
    private Context mContext;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;
    private LocationConfigInfo.DataBean mConfigInfo;

    private static final String TAG = "TypeProducerPresenter";

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_actor_layout, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        mConfigInfo = HomeData.getLocationConfigInfo(null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        vh.view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mBrowseItemFocusHighlight.onItemFocused(vh.view, hasFocus);
            }
        });
        if (item instanceof VideoDetailRecommendModel.DataBean.ContentsBean) {
            VideoDetailRecommendModel.DataBean.ContentsBean contentsBean = (VideoDetailRecommendModel.DataBean.ContentsBean) item;
            if (contentsBean.getIndex() == 0) {
                VideoDetailRequestManager.actorExposure();
            }
            Glide.with(mContext)
                    .load(get252ImageUrl(contentsBean))
                    .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                    .into(vh.mIvTypeProducerAvatar);
            vh.mTvActorName.setText(contentsBean.getName());
            vh.view.setOnClickListener(view -> {
                ActivityLauncher.startActorListActivity(mContext, contentsBean.getId(),
                        contentsBean.getStarType() == 2,
                        contentsBean.getName());
                VideoDetailRequestManager.actorClick();
            });
        }
    }

    private String get252ImageUrl(VideoDetailRecommendModel.DataBean.ContentsBean actorsEntity) {
        String resultUrl = actorsEntity.getVerPic();
        LibDeprecatedLogger.d("get252ImageUrl picUrl ? " + resultUrl);
        mConfigInfo.newImgDomain = "photocdntv.vod.ystyt.aisee.tv";
        if (StringUtils.isNotEmptyStr(resultUrl)) {
//            if (picUrl.contains("//photocdn.tv.snmsohu.aisee.tv/")) {
//                resultUrl = picUrl.replace("/img/", "/img/c_lfill,w_252,h_252,g_faces/");
//            } else
            if (mConfigInfo != null && StringUtil.isNotEmpty(mConfigInfo.newImgDomain)) {
                LibDeprecatedLogger.d(
                        "get252ImageUrl mConfigInfo.newImgDomain ? " + mConfigInfo.newImgDomain);
                if (actorsEntity.getVerPic().contains(mConfigInfo.newImgDomain)) {
                    resultUrl = actorsEntity.getVerPic().replace("/img/", "/img/c_lfill,w_252,h_252,g_faces/");
                }
            }
        }
        LibDeprecatedLogger.d("get252ImageUrl resultUrl ? " + resultUrl);
        return resultUrl;
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final ImageView mIvTypeProducerAvatar;
        private TextView mTvActorName;

        public ViewHolder(View view) {
            super(view);
            mIvTypeProducerAvatar = (ImageView) view.findViewById(R.id.iv_type_producer_avatar);
            mTvActorName = view.findViewById(R.id.detail_actor_name);
        }
    }
}

