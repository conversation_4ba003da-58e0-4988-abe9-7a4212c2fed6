package com.sohuott.tv.vod.search

import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager

object SearchRequestManager {

    private val NO_INPUT_PAGEID = "1065"
    private val INPUT_PAGEID = "1066"
    private val NO_INPUT_HISTORY_PAGEID = "1067"
    private val SEARCH_RESULT_PAGEID = "1068"
    private val NO_SEARCH_RESULT_PAGEID = "1069"


    fun pageExposure() {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10135, "imp"),
            hashMapOf("pageId" to NO_INPUT_PAGEID),
            null,
            null
        )
    }

    //keyboard：0：全键盘，1：T9键盘
    fun keyBoardExposure(type: String) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10311, "imp"),
            hashMapOf("pageId" to NO_INPUT_PAGEID),
            null,
            hashMapOf("type" to type)
        )
    }

    //搜索有结果爆光
    fun searchResultPageExposure(fromPage: Int?, keyword: String?) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10135, "imp"),
            hashMapOf("pageId" to SEARCH_RESULT_PAGEID),
            null,
            hashMapOf("fromPage" to fromPage.toString(), "keyword" to keyword)
        )
    }

    //搜索暂无结果页面曝光
    fun noSearchResultPageExposure(fromPage: Int?, keyword: String?) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10135, "imp"),
            hashMapOf("pageId" to NO_SEARCH_RESULT_PAGEID),
            null,
            hashMapOf("fromPage" to fromPage.toString(), "keyword" to keyword)
        )
    }

    //搜索结果查看更多点击
    fun searchResultMoreClick() {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10322, "clk"),
            null,
            null,
            null
        )
    }

    //搜索结果分类点击
    //tabname：0相关，1最新，2最热
    fun searchResultCategoryClick(tabname: String) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10321, "clk"),
            null,
            null,
            hashMapOf("tabname" to tabname)
        )
    }

    //猜你想搜联想词获取焦点
    fun guessSearchFocus(keyword: String?) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10320, "imp"),
            hashMapOf("pageId" to INPUT_PAGEID),
            null,
            hashMapOf("suggest_keyword" to keyword)
        )
    }

    //热搜词获取焦点
    fun hotSearchFocus(keyword: String?) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10324, "imp"),
            hashMapOf("pageId" to INPUT_PAGEID),
            null,
            hashMapOf("suggest_keyword" to keyword)
        )
    }


    //猜你想搜爆光
    //status：0无结果，1有结果
    // keywords：keywords不为空时上报
    fun guessSearchExposure(status: String, keyword: String, suggest_keywords: String?) {
        val memoInfo = hashMapOf("status" to status)
        if ("1" == status && !suggest_keywords.isNullOrEmpty()) {
            memoInfo["suggest_keywords"] = suggest_keywords
        }
        memoInfo["keyword"] = keyword


        RequestManager.getInstance().onAllEvent(
            EventInfo(10319, "imp"),
            hashMapOf("pageId" to INPUT_PAGEID),
            null,
            memoInfo
        )
    }

    //搜索热搜词内容点击
    fun hotSearchContentClick(keyword: String?) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10318, "clk"),
            hashMapOf("pageId" to NO_INPUT_PAGEID),
            null,
            hashMapOf("keyword" to keyword)
        )
    }

    //搜索热搜词曝光
    //搜索首页(无输入)时上报热搜，猜你想搜索无内容上报热搜
    fun hotSearchExposure(home: Boolean, keywords: String?) {
        var pathInfo = hashMapOf<String, String>()
        if (home) {
            pathInfo["pageId"] = NO_INPUT_PAGEID
        } else {
            pathInfo["pageId"] = INPUT_PAGEID
        }
        RequestManager.getInstance().onAllEvent(
            EventInfo(10317, "imp"),
            pathInfo,
            null,
            hashMapOf("keywords" to keywords)
        )
    }

    //搜索首页搜索历史内容点击
    fun searchHistoryContentClick(keyword: String?) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10316, "clk"),
            hashMapOf("pageId" to NO_INPUT_HISTORY_PAGEID),
            null,
            hashMapOf("keyword" to keyword)
        )
    }

    //搜索首页(无输入)搜索历史清空点击
    fun searchHistoryClearClick() {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10315, "clk"),
            hashMapOf("pageId" to NO_INPUT_HISTORY_PAGEID),
            null,
            null
        )
    }

    //搜索首页(无输入)搜索历史曝光
    fun searchHistoryExposure(keywords: String?) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10135, "imp"),
            hashMapOf("pageId" to NO_INPUT_HISTORY_PAGEID),
            null,
            hashMapOf("keywords" to keywords)
        )
    }

    //搜索首页键盘删除按钮点击
    fun searchHomeDeleteClick() {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10314, "clk"),
            hashMapOf("pageId" to INPUT_PAGEID),
            null,
            null
        )
    }

    //搜索首页键盘清空按钮点击
    fun searchHomeClearClick() {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10313, "clk"),
            hashMapOf("pageId" to INPUT_PAGEID),
            null,
            null
        )
    }

    //搜索结果点击上报
    fun searchResultItemClick(playlistId: String, suggest_keyword: String, fromPage: Int?) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10312, "clk"),
            hashMapOf(
                "pageId" to SEARCH_RESULT_PAGEID,
            ),
            null,
            hashMapOf(
                "playlistId" to playlistId,
                "suggest_keyword" to suggest_keyword,
                "fromPage" to fromPage.toString()
            )
        )
    }

    //搜索结果相关影片点击上报
    fun searchResultRelatedItemClick(playlistId: String, index: String) {
        RequestManager.getInstance().onAllEvent(
            EventInfo(10325, "clk"),
            hashMapOf(
                "pageId" to SEARCH_RESULT_PAGEID,
            ),
            null,
            hashMapOf(
                "playlistId" to playlistId,
                "index" to index
            )
        )
    }
}