package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.ListActorAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.ListActorPresenterImpl;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.ListActorView;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.HashMap;
import java.util.List;

/**
 * Created by XiyingCao on 16-3-14.
 */
public class ActorListActivity extends BaseActivity implements ListActorView {

    private CustomLinearRecyclerView mRecyclerView;
    private View mParentView;
    private LoadingView mLoadingView;
    private View mErrorView;

    private GlideImageView mIconView;
    private TextView mTitleView;
    private TextView mSubTitleView;
    TextView mActorDetailsView;
    private FocusBorderView mFocusBorderView;

    private boolean isEnableScrollListener;
    private int mActorId,mDirectorId;
    private String mActorName;
    private ListActorAdapter mListVideoAdapter;
    private CustomLinearLayoutManager mLayoutManager;
    private ListActorPresenterImpl mListPresenter;
    private static final String TAG = "ActorListActivity";
    private HashMap<String, String> mPathInfo;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        setContentView(R.layout.activity_list_actor);
        initView();
        setDisplay();

        mActorId = getIntent().getIntExtra(ParamConstant.PARAM_ACTOR_ID, 2);
        mDirectorId = getIntent().getIntExtra(ParamConstant.PARAM_DIRECTOR_ID, 0);
        mActorName = getIntent().getStringExtra(ParamConstant.PARAM_ACTOR_NAME);
        AppLogger.d(TAG, "mActorId ? " + mActorId);
        AppLogger.d(TAG, "mDirectorId ? " + mDirectorId);
        AppLogger.d(TAG, "mActorName ? " + mActorName);
        mPathInfo = new HashMap<>(1);
        mPathInfo.put("pageId", "1044");
        mListVideoAdapter = new ListActorAdapter(mActorId, mRecyclerView, mPathInfo);
        mListVideoAdapter.setFocusBorderView(mFocusBorderView);
        mLayoutManager = new CustomLinearLayoutManager(this);
        mListPresenter = new ListActorPresenterImpl(mActorId);

        initializeCollectionView();
        mListPresenter.setView(this);
        mListPresenter.setDirectorId(mDirectorId);
        mListPresenter.reloadActorRelativeDate();
        RequestManager.getInstance().onActorVideoListExposureEvent(mActorId);
        setPageName("5_list_actor");


        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), mPathInfo, null, null);
    }

    @Override
    public void finish() {
        super.finish();
        //在此时设置转场动画
        overridePendingTransition(R.anim.dialog_scale_in,R.anim.dialog_scale_out);
    }


    //设置窗口大小
    private void setDisplay() {
        //设置弹出窗口与屏幕对齐
        Window win = this.getWindow();
        int density = (int)(getResources().getDisplayMetrics().density);
        //设置内边距，这里设置为0
        win.getDecorView().setPadding(1 * density, 1 * density, 1 * density, 1 * density);
        WindowManager.LayoutParams lp = win.getAttributes();
        //设置窗口宽度
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        //设置窗口高度
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        //设置Dialog位置
        lp.gravity = Gravity.TOP | Gravity.LEFT;
        win.setAttributes(lp);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        mActorId = getIntent().getIntExtra(ParamConstant.PARAM_ACTOR_ID, 2);
        mDirectorId = getIntent().getIntExtra(ParamConstant.PARAM_DIRECTOR_ID, 2);
        mListVideoAdapter.setActorId(mActorId);
        mListPresenter.setDirectorId(mDirectorId);
        mListVideoAdapter.setSelctedPos(0);
        mListVideoAdapter.clear();
        mTitleView.setText("");
        mSubTitleView.setText("");
        mActorDetailsView.setText("");
        mListPresenter.setActorId(mActorId);
        mListPresenter.reloadActorRelativeDate();
        mLoadingView.setVisibility(View.VISIBLE);
        mParentView.setVisibility(View.GONE);
        RequestManager.getInstance().onActorVideoListExposureEvent(mActorId);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    private void initView() {
        mParentView = findViewById(R.id.parent);
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mErrorView = findViewById(R.id.err_view);
        mFocusBorderView = (FocusBorderView) findViewById(R.id.focus_border_view);

        mIconView = (GlideImageView) findViewById(R.id.actor_icon);
        mTitleView = (TextView) findViewById(R.id.actor_name);
        if (!TextUtils.isEmpty(mActorName)) {
            mTitleView.setText(mActorName + getResources().getString(R.string.list_actor_about_product));
        }
        mSubTitleView = (TextView) findViewById(R.id.actor_subtitle);
        mActorDetailsView = (TextView) findViewById(R.id.actor_details);
        mRecyclerView = (CustomLinearRecyclerView) findViewById(R.id.list);
        mRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
//        mRecyclerView.addItemDecoration(new BaseItemDecoration(getResources().getDimensionPixelSize(R.dimen.x45)));
        mRecyclerView.setOnScrollListener(new FinishScrollListener());

        mActorDetailsView.setText("暂无简介");
    }

    private void initializeCollectionView() {
        mRecyclerView.setAdapter(mListVideoAdapter);
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        mLayoutManager.setCustomPadding(getResources().getDimensionPixelSize(R.dimen.x370),
                getResources().getDimensionPixelSize(R.dimen.x65));
        mRecyclerView.setLayoutManager(mLayoutManager);
        mRecyclerView.setItemAnimator(new DefaultItemAnimator());
        mRecyclerView.setItemViewCacheSize(0);
    }

    @Override
    public void add(List<ListAlbumModel> models) {
        mListVideoAdapter.add(models);
    }

    @Override
    public void showLoading() {
        mLoadingView.setVisibility(View.VISIBLE);
        mErrorView.setVisibility(View.GONE);
        mParentView.setVisibility(View.INVISIBLE);
        disableLastItemViewListener();
    }

    @Override
    public void hideLoading() {
        mLoadingView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.GONE);
        mParentView.setVisibility(View.VISIBLE);
        activateLastItemViewListener();
    }

    @Override
    public void activateLastItemViewListener() {
        enableSearchOnFinish();
    }

    @Override
    public void disableLastItemViewListener() {
        disableSearchOnFinish();
    }

    @Override
    public void onError() {
        mLoadingView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.VISIBLE);
        mParentView.setVisibility(View.INVISIBLE);
    }

    @Override
    public void setListTitle(String title) {
        if (!TextUtils.isEmpty(title)) {
            mTitleView.setText(title);
        } else {
            mTitleView.setText("");
        }
    }

    @Override
    public void setListSubTitle(String subTitle) {
        if (!TextUtils.isEmpty(subTitle)) {
            String[] text = subTitle.split(",");
            StringBuffer stringBuffer = new StringBuffer();
            for (int i = 0; i < text.length; i++) {
                if (i != 0) {
                    stringBuffer.append(" | ");
                }
                stringBuffer.append(text[i]);
            }
            mSubTitleView.setText(stringBuffer.toString());
        } else {
            mSubTitleView.setText("");
        }
    }

    @Override
    public void setListCategory(String category) {
        if (!TextUtils.isEmpty(category)) {
            mActorDetailsView.setText(category);
        }
    }

    @Override
    public void setBackground(String url) {
        AppLogger.d(TAG, "setBackground url ? " + url);
        try {
            if (!this.isDestroyed()) {
                mIconView.setCircleImageRes(url);
            }
        } catch (Exception e) {
            AppLogger.d("e ? " + e);
        }
    }

    @Override
    public RecyclerView.Adapter getAdapter() {
        return mRecyclerView.getAdapter();
    }

    private void enableSearchOnFinish() {
//        mRecyclerView.setOnScrollListener(new FinishScrollListener());
        isEnableScrollListener = true;
    }

    private void disableSearchOnFinish() {
//        mRecyclerView.setOnScrollListener(null);
        isEnableScrollListener = false;
    }

    private class FinishScrollListener extends RecyclerView.OnScrollListener {
        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            if (!isEnableScrollListener) {
                return;
            }

            int lastVisibleItemPosition = mLayoutManager.findLastVisibleItemPosition() + 5;
            int modelsCount = mListVideoAdapter.getItemCount();

            if (lastVisibleItemPosition >= modelsCount) {
                mListPresenter.onLastItemViewed();
            }
            if (mRecyclerView.getFocusedChild() == null) {
                return;
            }
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                if (mRecyclerView == null) {
                    return;
                }

                if (mRecyclerView.getFocusedChild() == null) {
                    return;
                }

                if(Util.isSupportTouchVersion(ActorListActivity.this)){
                    return;
                }

//                ListActorAdapter.ViewHolder viewHolder = (ListActorAdapter.ViewHolder) mRecyclerView.getChildViewHolder(
//                        mRecyclerView.getFocusedChild());
//                if (viewHolder != null && viewHolder.mLayoutAlbum != null) {
//                    mFocusBorderView.setFocusView(viewHolder.mLayoutAlbum);
//                    FocusUtil.setFocusAnimator(viewHolder.mLayoutAlbum, mFocusBorderView);
//                }
            }
        }
    }
}
