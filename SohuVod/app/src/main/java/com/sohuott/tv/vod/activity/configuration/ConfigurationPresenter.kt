package com.sohuott.tv.vod.activity.configuration

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.View
import android.widget.ImageView
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R

class ConfigurationPresenter(val context: Context) :
    DefaultPresenter(R.layout.item_config_header_view_presenter_layout) {
    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        val image = viewHolder.getView<ImageView>(R.id.iv_config_header_image)
        val imageUrl = item as String?
//        image.background=context.getDrawable(R.drawable.activity_background)
        AppLogger.v("imageUrl:${imageUrl}")
//        Glide.with(context).load(imageUrl).error(R.drawable.activity_background).(image)
        Glide.with(context).asDrawable().load(imageUrl)
            .into(object : CustomTarget<Drawable?>() {
                override fun onResourceReady(
                    resource: Drawable,
                    transition: Transition<in Drawable?>?
                ) {
                    image.background = resource
                }

                override fun onLoadCleared(placeholder: Drawable?) {
//                    image.background = placeholder
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
//                    image.background = errorDrawable

                    super.onLoadFailed(errorDrawable)
                }

            })

    }

    override fun createViewHolderBefore(view: View) {
        view.isFocusable = false
        super.createViewHolderBefore(view)
    }
}