package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.GridListActivityNew;
import com.sohuott.tv.vod.activity.GridListTagActivityNew;
import com.sohuott.tv.vod.activity.LabelGridListActivity;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.lib.db.greendao.PushMessageData;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.push.event.TopBarMessageEvent;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohu.lib_utils.NetworkUtils;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.view.WechatView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;

/**
 * Created by wenjingbian on 2017/5/11.
 * <p>
 * Custom layout on the top of view
 */

public class TopBar extends RelativeLayout {

    public interface TopBarFocusController {
        boolean onFocusDown();
    }

    //index value for child views
    public static final int INDEX_SEARCH_BUTTON = 0;
    public static final int INDEX_USER_BUTTON = 1;
    public static final int INDEX_VIP_BUTTON = 2;
    public static final int INDEX_MESSAGE_VIEW = 3;
    public static final int INDEX_WECHAT_VIEW = 4;

    public static final int PARENT_VIEW_HOME = 1;
    public static final int PARENT_VIEW_VIDEO = 2;
    public static final int PARENT_VIEW_TAG = 3;
    public static final int PARENT_VIEW_LABEL = 4;

    private WeakReference<Context> mContext;

    private ImageButton btn_search, btn_vip_default, btn_vip, btn_userview_default, mUserview, mVipView;
    private GlideImageView btn_userview;
    private ImageView search_btn_focus_iv, user_btn_focus_iv, vip_btn_focus_iv;
    private TextView tv_vip, tv_wechat;
    private HomeMessageView homeMessageView;

    private LoginUserInformationHelper mHelper;
    private TopBarFocusController mTopBarFocusController;

    private int mParentViewIndex;

    private WechatView mWechatView;

    public TopBar(Context context) {
        super(context);
        this.mContext = new WeakReference<Context>(context);
        mHelper = LoginUserInformationHelper.getHelper(mContext.get());
        initView();
    }

    public TopBar(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.mContext = new WeakReference<Context>(context);
        mHelper = LoginUserInformationHelper.getHelper(mContext.get());

        initView();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventBus.getDefault().register(this);
        RequestManager.getInstance().onTopBarExposureEvent();

        if (mHelper == null) {
            mHelper = LoginUserInformationHelper.getHelper(mContext.get());
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        //unregister eventbus
        EventBus.getDefault().unregister(this);
        //release objects
        mHelper = null;
        mTopBarFocusController = null;
    }

    @Subscribe
    public void onEventMainThread(TopBarMessageEvent event) {
        LibDeprecatedLogger.d("onEventMainThread(TopBarMessageEvent event)");
        if (event != null && event.getPushMessageData() != null) {
            final PushMessageData pushMessageData = event.getPushMessageData();
            post(new InnerRunnable(this, pushMessageData));
        }
    }

    private static class InnerRunnable implements Runnable {
        WeakReference<TopBar> mWrapper;
        private PushMessageData pushMessageData;
        InnerRunnable(TopBar topBar, PushMessageData pushMessageData){
            mWrapper = new WeakReference<>(topBar);
            this.pushMessageData = pushMessageData;
        }
        @Override
        public void run() {
            TopBar topBar = mWrapper.get();
            if (topBar.homeMessageView != null) {
                topBar.homeMessageView.showNewPushMessage(pushMessageData);
            }
        }
    }

    /**
     * Get child view's index
     *
     * @param view target child view you want to get index
     * @return child view's index, including INDEX_SEARCH_BUTTON, INDEX_USER_BUTTON, INDEX_VIP_BUTTON, INDEX_MESSAGE_VIEW, -1
     */
    public int getChildViewIndex(View view) {
        if (view == btn_search) {
            return INDEX_SEARCH_BUTTON;
        } else if (view == mUserview || view == btn_userview || view == btn_userview_default) {
            return INDEX_USER_BUTTON;
        } else if (view == mVipView || view == btn_vip || view == btn_vip_default) {
            return INDEX_VIP_BUTTON;
        } else if (view == homeMessageView) {
            return INDEX_MESSAGE_VIEW;
        } else if (view == tv_wechat) {
            return INDEX_WECHAT_VIEW;
        } else {
            return -1;
        }
    }

    /**
     * Request child view focus
     *
     * @param childIndex child view you want to get focus
     */
    public void focusChildView(int childIndex) {
        if (childIndex < 0) {
            return;
        }
        switch (childIndex) {
            case INDEX_SEARCH_BUTTON:
                btn_search.requestFocus();
                break;
            case INDEX_USER_BUTTON:
                btn_userview.requestFocus();
                break;
            case INDEX_VIP_BUTTON:
                btn_vip.requestFocus();
                break;
            case INDEX_MESSAGE_VIEW:
                homeMessageView.requestFocus();
                break;
            case INDEX_WECHAT_VIEW:
                tv_wechat.requestFocus();
                break;
            default:
                break;
        }
    }

    /**
     * Hide message view
     */
    public void hideMessageView() {
        homeMessageView.setVisibility(GONE);
    }

    /**
     * Update text of message view
     */
    public void updateMessageView() {
        homeMessageView.showMessageData();
    }

    /**
     * Update image of user view
     */
    public void updateUserView() {
        if (mHelper != null && mHelper.getIsLogin()) {
            btn_userview_default.setVisibility(INVISIBLE);
            btn_userview.setVisibility(VISIBLE);
            String avatar = LoginUserInformationHelper.getHelper(mContext.get()).getLoginPhoto();
            if (null != avatar && !avatar.trim().equals("")) {
                btn_userview.setCircleImageRes(avatar, getResources().getDrawable(R.drawable.home_login_unfocused), getResources().getDrawable(R.drawable.home_login_unfocused));
            }
        } else {
            btn_userview.setVisibility(INVISIBLE);
            btn_userview_default.setVisibility(VISIBLE);
            String uri = "res://" + getResources().getResourcePackageName(R.drawable.home_login_unfocused)
                    + "/" + R.drawable.home_login_unfocused;
            btn_userview.setCircleImageRes(R.drawable.home_login_unfocused, getResources().getDrawable(R.drawable.home_login_unfocused), getResources().getDrawable(R.drawable.home_login_unfocused));
        }
    }

    public void updateVipText() {
        if (mHelper != null && mHelper.isVip()) {
            tv_vip.setText("续费会员");
        } else {
            tv_vip.setText("开会员");
        }
    }

    public void updateVipImage() {
        if (mHelper != null && mHelper.isVip()) {
            btn_vip.setVisibility(VISIBLE);
            btn_vip_default.setVisibility(INVISIBLE);
        } else {
            btn_vip.setVisibility(INVISIBLE);
            btn_vip_default.setVisibility(VISIBLE);
        }
    }

    public void setTopBarFocusListener(TopBarFocusController topBarFocusController) {
        this.mTopBarFocusController = topBarFocusController;

        if (mTopBarFocusController instanceof GridListActivityNew) {
            mParentViewIndex = PARENT_VIEW_VIDEO;
        } else if (mTopBarFocusController instanceof GridListTagActivityNew) {
            mParentViewIndex = PARENT_VIEW_TAG;
        } else if (mTopBarFocusController instanceof LabelGridListActivity) {
            mParentViewIndex = PARENT_VIEW_LABEL;
        }
    }

    /**
     * Initialize all views
     */
    private void initView() {
        LayoutInflater.from(mContext.get()).inflate(R.layout.layout_top_bar, this, true);

        //Buttons
        btn_search = (ImageButton) findViewById(R.id.btn_search);
        btn_userview = (GlideImageView) findViewById(R.id.btn_userview);
        btn_userview.setFocusable(false);
        mUserview = (ImageButton) findViewById(R.id.userview);
        btn_vip = (ImageButton) findViewById(R.id.btn_vip);
        btn_vip.setFocusable(false);
        btn_userview_default = (ImageButton) findViewById(R.id.btn_userview_default);
        btn_userview_default.setFocusable(false);
        btn_vip_default = (ImageButton) findViewById(R.id.btn_vip_default);
        btn_vip_default.setFocusable(false);
        mVipView = (ImageButton) findViewById(R.id.vip_view);
        TopBarClickListener clickListener = new TopBarClickListener();
        btn_search.setOnClickListener(clickListener);
        mUserview.setOnClickListener(clickListener);
        mVipView.setOnClickListener(clickListener);
        TopBarFocusListener focusListener = new TopBarFocusListener();
        btn_search.setOnFocusChangeListener(focusListener);
        mUserview.setOnFocusChangeListener(focusListener);
        mVipView.setOnFocusChangeListener(focusListener);
        TopBarKeyListener keyListener = new TopBarKeyListener();
        btn_search.setOnKeyListener(keyListener);
        mUserview.setOnKeyListener(keyListener);
        mVipView.setOnKeyListener(keyListener);

        //Focus views
        search_btn_focus_iv = (ImageView) findViewById(R.id.search_btn_focus_iv);
        user_btn_focus_iv = (ImageView) findViewById(R.id.user_btn_focus_iv);
        vip_btn_focus_iv = (ImageView) findViewById(R.id.vip_btn_focus_iv);

        tv_vip = (TextView) findViewById(R.id.tv_vip);

        //Message view
        homeMessageView = (HomeMessageView) findViewById(R.id.homeMessageView);
        homeMessageView.setOnKeyListener(keyListener);

        tv_wechat = (TextView) findViewById(R.id.tv_wechat);
        tv_wechat.setOnKeyListener(keyListener);
        tv_wechat.setOnClickListener(clickListener);
        tv_wechat.setOnFocusChangeListener(focusListener);
        mWechatView = new WechatView(mContext.get());
    }

    private class TopBarClickListener implements OnClickListener {

        @Override
        public void onClick(View view) {

            if (!NetworkUtils.isConnected(mContext.get())) {
                ActivityLauncher.startNetworkDialogActivity(mContext.get());
                return;
            }

            switch (view.getId()) {
                case R.id.btn_search:
                    ActivityLauncher.startSearchActivity(mContext.get());
                    RequestManager.getInstance().onTopBarSearchClickEvent(mParentViewIndex);
                    break;
                case R.id.btn_userview:
                case R.id.btn_userview_default:
                case R.id.userview:
                    if (mHelper != null && mHelper.getIsLogin()) {
                        ActivityLauncher.startListUserRelatedActivity(mContext.get(), ListUserRelatedActivity.LIST_INDEX_MY);
                    } else {
                        ActivityLauncher.startLoginActivity(mContext.get());
                    }
                    RequestManager.getInstance().onTopBarUserViewClickEvent(mParentViewIndex);
                    break;
                case R.id.btn_vip:
                case R.id.btn_vip_default:
                case R.id.vip_view:
                    ActivityLauncher.startPayActivity(mContext.get(), PayActivity.PAY_SOURCE_TOP_BAR_STAT);
                    RequestManager.getInstance().onTopBarMemberClickEvent(mParentViewIndex);
                    break;
                case R.id.tv_wechat:
                    if (mWechatView != null) {
                        mWechatView.show(tv_wechat);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private class TopBarFocusListener implements OnFocusChangeListener {

        @Override
        public void onFocusChange(View view, boolean hasFocus) {
            switch (view.getId()) {
                case R.id.btn_search:
                    if (hasFocus) {
                        search_btn_focus_iv.setVisibility(VISIBLE);
                    } else {
                        search_btn_focus_iv.setVisibility(GONE);
                    }
                    break;
                case R.id.btn_userview:
                case R.id.btn_userview_default:
                case R.id.userview:
                    if (hasFocus) {
                        user_btn_focus_iv.setVisibility(VISIBLE);
                    } else {
                        user_btn_focus_iv.setVisibility(GONE);
                    }
                    break;
                case R.id.btn_vip:
                case R.id.btn_vip_default:
                case R.id.vip_view:
                    if (hasFocus) {
                        vip_btn_focus_iv.setVisibility(VISIBLE);
                    } else {
                        vip_btn_focus_iv.setVisibility(GONE);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private class TopBarKeyListener implements OnKeyListener {

        @Override
        public boolean onKey(View view, int keyCode, KeyEvent event) {
            if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && event.getAction() == KeyEvent.ACTION_DOWN) {
                if (view.getId() == R.id.btn_search) {
                    view.startAnimation(AnimationUtils.loadAnimation(mContext.get(), R.anim.shake_x));
                    return true;
                } else if (view.getId() == R.id.homeMessageView) {
                    mVipView.requestFocus();
                    return true;
                } else if (view.getId() == R.id.tv_wechat) {
                    homeMessageView.requestFocus();
                    return true;
                }
                return false;
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && event.getAction() == KeyEvent.ACTION_DOWN) {
                if (view.getId() == R.id.tv_wechat && mParentViewIndex != PARENT_VIEW_HOME) {
                    view.startAnimation(AnimationUtils.loadAnimation(mContext.get(), R.anim.shake_x));
                    return true;
//                } else if (view.getId() == R.id.vip_view) {
//                    homeMessageView.requestFocus();
//                    return true;
                } else if (view.getId() == R.id.homeMessageView) {
                    tv_wechat.requestFocus();
                    return true;
                }
                return false;
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
                if (view.getId() == R.id.userview) {
                    if (btn_userview_default.getVisibility() == VISIBLE) {
                        btn_userview_default.startAnimation(AnimationUtils.loadAnimation(mContext.get(), R.anim.shake_y));
                    } else {
                        btn_userview.startAnimation(AnimationUtils.loadAnimation(mContext.get(), R.anim.shake_y));
                    }
                } else if (view.getId() == R.id.vip_view) {
                    if (btn_vip_default.getVisibility() == VISIBLE) {
                        btn_vip_default.startAnimation(AnimationUtils.loadAnimation(mContext.get(), R.anim.shake_y));
                    } else {
                        btn_vip.startAnimation(AnimationUtils.loadAnimation(mContext.get(), R.anim.shake_y));
                    }
                } else {
                    view.startAnimation(AnimationUtils.loadAnimation(mContext.get(), R.anim.shake_y));
                }
                return true;
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                if (mTopBarFocusController != null) {
                    return mTopBarFocusController.onFocusDown();
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }
    }

}
