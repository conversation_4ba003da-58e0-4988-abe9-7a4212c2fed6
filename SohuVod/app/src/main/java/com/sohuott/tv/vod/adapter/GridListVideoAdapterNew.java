package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.os.Handler;
import android.os.Message;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.GridListActivityNew;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.CustomRecyclerViewNew;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.util.List;

import static com.sohuott.tv.vod.activity.GridListActivityNew.SPAN_PGC;
import static com.sohuott.tv.vod.activity.GridListActivityNew.SPAN_VRS;


/**
 * Created by wenjingbian on 2017/6/2.
 * <p>
 * Custom Adapter for the GridListVideoView (the right content of GridListActivityNew)
 */

public class GridListVideoAdapterNew extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    protected SparseArray<View> mHeaders;//用于存储headerview，这里只考虑1个header的情况
    private static int UNIQUE_ID_HEADER = -1;
    //Message.what to load poster image
    private static final int MSG_IMG_LOAD = 1;
    private static final int MSG_DELAY_CONTINUE_CLICK = 2;

    private FocusBorderView mFocusView;
    private CustomRecyclerViewNew mRecyclerView;
    private Context mContext;
    private GridListActivityNew mActivity;

    private List<ListAlbumModel> mDataSource; // data list
    private MyHandler mHandler; //instance of custom Handler

    private boolean isEnabledUpKey = true;
    private boolean isPgc;//video type, true == pgc, false ==  vrs
    private int mKeyCode; // keyCode user just pressed
    private long mClickedTime;

    public GridListVideoAdapterNew(Context context, CustomRecyclerViewNew recyclerView, boolean isPgc) {
        this.mContext = context;
        this.mRecyclerView = recyclerView;
        this.isPgc = isPgc;
        this.mActivity = (GridListActivityNew) context;
        mHandler = new MyHandler();
    }

    @Override
    public long getItemId(int position) {
        if (getHeadersSize() == 1) {
            if (position == 0) {
                return mHeaders.valueAt(0).hashCode();
            } else {
                return mDataSource.get(position - 1).id;
            }
        }
        return mDataSource.get(position).id;
    }

    @Override
    public void onAttachedToRecyclerView(RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();

        if (layoutManager instanceof GridLayoutManager) {
            final GridLayoutManager gridManager = ((GridLayoutManager) layoutManager);
            gridManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
                @Override
                public int getSpanSize(int position) {
                    if (getItemViewType(position) < 0) {
                        return gridManager.getSpanCount();
                    } else {
                        return 1;
                    }
                }
            });
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (position >= 0 && position < size(mHeaders)) {
            return mHeaders.keyAt(position);
        }
        return 0;
    }

    protected int size(SparseArray sparseArray) {
        if (sparseArray == null) {
            return 0;
        }
        return sparseArray.size();
    }

    protected int size(List list) {
        if (list == null) {
            return 0;
        }
        return list.size();
    }

    public int updateHeader(View header) {
        if (mHeaders != null) {
            mHeaders.clear();
        }
        return addHeader(header);
    }

    public int addHeader(View header) {
        return addHeader(UNIQUE_ID_HEADER--, header);
    }

    protected int addHeader(int uniqueId, View header) {
//        if (header.getParent() != null) {
//            AppLogger.e("already has a parent,can not add again");
//            return 404;
//        }
        if (mHeaders == null) {
            mHeaders = new SparseArray<>();
        }
        mHeaders.put(uniqueId, header);
        notifyItemInserted(0);
        return uniqueId;
    }

    public View getHeader(int uniqueId) {
        if (mHeaders == null || mHeaders.size() == 0) {
            return null;
        }
        return mHeaders.get(uniqueId);
    }

    public void removeHeader(int uniqueId) {
        if (mHeaders == null || mHeaders.size() == 0) {
            return;
        }
        int position = mHeaders.indexOfKey(uniqueId);
        if (position < 0) {
            LibDeprecatedLogger.e("not Header found");
            return;
        }
        mHeaders.remove(uniqueId);
        notifyItemRemoved(position);
    }

    public void removeHeader(View view) {
        if (mHeaders == null || mHeaders.size() == 0) {
            return;
        }
        int position = mHeaders.indexOfValue(view);
        if (position < 0) {
            LibDeprecatedLogger.e("not Header found");
            return;
        }
        mHeaders.removeAt(position);
        notifyItemRemoved(position);
    }

    public int getHeadersSize() {
        return size(mHeaders);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view;
        RecyclerView.ViewHolder viewHolder;
        if (mHeaders != null && mHeaders.get(viewType, null) != null) {
            view = mHeaders.get(viewType);
            viewHolder = new HeaderViewHolder(view);
            return viewHolder;
        }
        if (isPgc) {
            view = LayoutInflater.from(mContext).inflate(R.layout.list_grid_vr_item_new, parent, false);
            viewHolder = new PgcViewHolder(view);
        } else {
            view = LayoutInflater.from(mContext).inflate(R.layout.list_grid_item_new, parent, false);
            viewHolder = new VrsViewHolder(view);
        }
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (position < size(mHeaders)) {
            return;
        }
        int pos = holder.getAdapterPosition() - size(mHeaders);
        final ListAlbumModel model = mDataSource.get(pos);
        if (model == null) {
            return;
        }

        if (isPgc) {
            if (mDataSource != null && mDataSource.size() > pos) {
                PgcViewHolder viewHolder = (PgcViewHolder) holder;
                viewHolder.ctiv_poster.resetImageRes();
                viewHolder.ctiv_poster.setImageResource(R.drawable.vertical_default_big_poster);
                viewHolder.giv_producer.resetImageRes();
                viewHolder.giv_producer.setImageResource(R.drawable.producer_icon_normal);
                if (model.cateCodeFirst != CornerTagImageView.CORNER_TYPE_SOHUCLASS) {
                    viewHolder.tv_title.setText(model.videoTitle);
                    viewHolder.tv_grid_model_focus_title.setText(model.videoTitle);
                } else {
                    viewHolder.tv_title.setText(model.ptitle);
                    viewHolder.tv_grid_model_focus_title.setText(model.ptitle);
                    viewHolder.ctiv_poster.resetImageRes();
                    viewHolder.ctiv_poster.setImageResource(R.drawable.vertical_default_big_poster);
                }

                if (model.playCount < 5000) {
                    viewHolder.tv_count.setText(mContext.getResources().getString(R.string.new_string));
                } else {
                    viewHolder.tv_count.setText("");
                }
                viewHolder.ctiv_poster.resetCornerType();
                viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ActivityLauncher.startVideoDetailActivity(v.getContext(), model.videoId,
                                model.cateCodeFirst == CornerTagImageView.CORNER_TYPE_VR ? Constant.DATA_TYPE_VR : Constant.DATA_TYPE_PGC, Constant.PAGE_GRID_LIST);
                        RequestManager.getInstance().onGridListNewSubTabViewItemClickEvent(2, model.videoId);
                    }
                });
            }
        } else {
            if (mDataSource != null && mDataSource.size() > pos) {
                VrsViewHolder viewHolder = (VrsViewHolder) holder;
                viewHolder.ctiv_poster.setImageResource(R.drawable.vertical_default_big_poster);
                viewHolder.ctiv_poster.resetCornerType();
                viewHolder.tv_title.setText(model.tvName);
                viewHolder.tv_grid_model_focus_title.setText(model.tvName);
                handleTvSets(model.cateCode, model.tvSets, TextUtils.isEmpty(model.latestVideoCount) ? 0 : Integer.parseInt(model.latestVideoCount), model.showDate, viewHolder.tv_sets);
                viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ActivityLauncher.startVideoDetailActivity(mContext, model.id, Constant.PAGE_GRID_LIST);
                        RequestManager.getInstance().onGridListNewSubTabViewItemClickEvent(1, model.id);
                    }
                });
            }
        }

        if (pos >= 0) {
            loadImage();
        }
    }

    @Override
    public int getItemCount() {
        return size(mHeaders) + size(mDataSource);
    }

    private void handleTvSets(long cateCode, int tvSets, int tvSetNow, String showDate, TextView textView) {
        if (cateCode == 100) {
            textView.setText("");
            return;
        }
        if (tvSets != 0) {
            if (tvSetNow == tvSets) {
                textView.setText(
                        tvSets + (cateCode == 106
                                ? mContext.getResources().getString(R.string.txt_activity_user_related_term_suf)
                                : mContext.getResources().getString(R.string.txt_activity_user_related_set_suf))
                                + mContext.getResources().getString(R.string.txt_activity_user_related_set_pre_all));
            } else {
                textView.setText(mContext.getResources().getString(R.string.txt_activity_user_related_set_pre_update)
                        + tvSetNow + (cateCode == 106
                        ? mContext.getResources().getString(R.string.txt_activity_user_related_term_suf)
                        : mContext.getResources().getString(R.string.txt_activity_user_related_set_suf)));
            }
        } else {
            if (cateCode == 106) {//综艺
                String text = TextUtils.isEmpty(showDate) ? "" : showDate + mContext.getResources().getString(R.string.txt_activity_user_related_term_suf);
                textView.setText(text);
            }
        }

    }

    public void setDataSource(List<ListAlbumModel> dataSource) {
        this.mDataSource = dataSource;
    }

    public void setFocusView(FocusBorderView focusView) {
        this.mFocusView = focusView;
        if (mFocusView == null) return;
        mFocusView.setDrawable(R.drawable.bg_hfc_border_focus);
    }

    public int getKeyCode() {
        return mKeyCode;
    }

    public void loadImage() {
        mHandler.removeMessages(MSG_IMG_LOAD);
        mHandler.sendEmptyMessageDelayed(MSG_IMG_LOAD, 200);
    }

    /**
     * Focus on the certain PGC itemView
     *
     * @param position adapter position of the certain view you want to focus on
     */
    public void focusOnPgcViewByPos(int position) {
        if (mRecyclerView == null || position < 0 || position >= getItemCount()) {
            return;
        }

        RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(position);
        if (viewHolder != null && viewHolder.itemView != null) {
            TextView title = (TextView) viewHolder.itemView.findViewById(R.id.grid_model_title);
            if (title != null) {
                title.setMarqueeRepeatLimit(-1);
                title.setEllipsize(TextUtils.TruncateAt.MARQUEE);
            }
            View view_count = viewHolder.itemView.findViewById(R.id.layout_count);
            ImageView iv_count = (ImageView) viewHolder.itemView.findViewById(R.id.play_count_icon);
            TextView tv_count = (TextView) viewHolder.itemView.findViewById(R.id.play_count);
            if (view_count != null) {
                view_count.setVisibility(View.VISIBLE);
            }
            if (tv_count != null) {
                tv_count.setVisibility(View.VISIBLE);
            }
            if (iv_count != null) {
                iv_count.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * Add data list to the exist data source
     *
     * @param dataSource the newest data list
     */
    public void addItems(List<ListAlbumModel> dataSource) {
        if (mDataSource != null) {
            int start = mDataSource.size();
            mDataSource.addAll(dataSource);
            notifyItemRangeInserted(start, dataSource.size());
        }
    }

    public void releaseAll() {
        mContext = null;
        mRecyclerView = null;
        mActivity = null;
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
        if (mDataSource != null) {
            mDataSource.clear();
            mDataSource = null;
        }
        if (mHeaders != null) {
            mHeaders.clear();
            mHeaders = null;
        }
        FocusUtil.clearAnimation();
    }

    private static class FocusItemRunnable implements Runnable {
        private WeakReference<RecyclerView> mWrapper;

        FocusItemRunnable(RecyclerView recyclerView) {
            mWrapper = new WeakReference<>(recyclerView);
        }

        @Override
        public void run() {
            RecyclerView recyclerView = mWrapper.get();
            if (recyclerView == null) {
                return;
            }
            RecyclerView.ViewHolder holder = recyclerView.findViewHolderForAdapterPosition(0);
            if (holder != null) {
                holder.itemView.requestFocus();
            }
        }
    }

    /**
     * Custom ViewHolder for VRS video
     */
    public class VrsViewHolder extends RecyclerView.ViewHolder {

        CornerTagImageView ctiv_poster;
        public TextView tv_title;
        public TextView tv_sets;
        TextView tv_grid_model_focus_title;
        public LinearLayout layout_grid_item_focus_desc;
        public RippleDiffuse grid_model_focus_play;
        public View grid_sets_bg;
        public RelativeLayout layout_grid_item_root;

        public VrsViewHolder(View itemView) {
            super(itemView);

            ctiv_poster = (CornerTagImageView) itemView.findViewById(R.id.grid_model_image);
            tv_title = (TextView) itemView.findViewById(R.id.grid_model_title);
            tv_sets = (TextView) itemView.findViewById(R.id.grid_model_sets);
            layout_grid_item_focus_desc = (LinearLayout) itemView.findViewById(R.id.layout_grid_item_focus_desc);
            grid_model_focus_play = (RippleDiffuse) itemView.findViewById(R.id.grid_model_focus_play);
            layout_grid_item_root = (RelativeLayout) itemView.findViewById(R.id.layout_grid_item_root);
            grid_sets_bg = itemView.findViewById(R.id.grid_sets_bg);
            tv_grid_model_focus_title = (TextView) itemView.findViewById(R.id.tv_grid_model_focus_title);

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
//                        if (!mRecyclerView.getIsLongPressed()) {
//                            //update currently selected line number
//                            mActivity.setCurrLineTxt(true, getAdapterPosition());
//                        }
//
//                        if (mRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
//                            return;
//                        }
                        handleFocus(v);
                    } else {
                        handleUnfocused(v);
                    }
                }
            });

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    int position = getAdapterPosition();
//                    if (event.getAction() == KeyEvent.ACTION_DOWN) {
//                        mKeyCode = keyCode;
//                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_UP) {
//                            if (mClickedTime == 0) {
//                                mClickedTime = System.currentTimeMillis();
//                                mActivity.setColumnNum(computeColumn(position));
//                            } else if (System.currentTimeMillis() - mClickedTime > 500
//                                    && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
//                                mClickedTime = System.currentTimeMillis();
//                                mActivity.setColumnNum(computeColumn(position));
//                                ((GridListActivityNew) mContext).setViewFocusability(true);
//                            } else {
//                                mClickedTime = System.currentTimeMillis();
//                                ((GridListActivityNew) mContext).setViewFocusability(false);
//                            }
//                        }
//                    }
                    //for the second line items to avoid continue click in short time(for XiaoMi TV)
//                    if (position / SPAN_VRS == 1 && keyCode == KeyEvent.KEYCODE_DPAD_UP
//                            && event.getAction() == KeyEvent.ACTION_DOWN) {
//                        isEnabledUpKey = false;
//                        mHandler.sendEmptyMessageDelayed(MSG_DELAY_CONTINUE_CLICK, 500);
//                    }
                    //for the second line items with filter header
                    if (getHeadersSize() == 1 && (position / SPAN_VRS == 0 || position / SPAN_VRS == 1) && keyCode == KeyEvent.KEYCODE_DPAD_UP
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        return false;
                    }
                    //for the first line items
                    if (position / SPAN_VRS == 0 && keyCode == KeyEvent.KEYCODE_DPAD_UP
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (!isEnabledUpKey) {
                            return true;
                        }
                        if (System.currentTimeMillis() != mClickedTime && (System.currentTimeMillis() - mClickedTime <= 500)) {
                            return true;
                        }
                        mActivity.setFocusRoute(false);
                        if (event.getRepeatCount() == 0 && !mActivity.displayHeaderView(true)
                                && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                            mActivity.focusOnTopBar();
                            mActivity.setCurrLineTxt(false, -1);
                        }
                        return true;
                    }
                    //for the last row of video list
                    if (isLastRow(position) && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                            return true;
                        } else if (isLastItem(position) && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                            return true;
                        }

                    }
                    //for the last item of every line with filter header
//                    if (isLastItemOfEveryLine(position) && keyCode == KeyEvent.KEYCODE_DPAD_LEFT
//                            && event.getAction() == KeyEvent.ACTION_DOWN) {
//                        return false;
//                    }
                    //for the last item of every line
                    if (isLastItemOfEveryLine(position) && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (mRecyclerView != null) {
                            RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(position + 1);
                            if (viewHolder != null && viewHolder.itemView != null) {
                                viewHolder.itemView.requestFocus();
                            }
                            return true;
                        }
                    }
                    //for the first item of every line
                    if (isFirstItemOfEveryLine(position) && keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (mRecyclerView != null && mRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                            mRecyclerView.stopScroll();
                        }
                        mActivity.setLeftSelectedPos();
                        mActivity.setCurrLineTxt(false, -1);
                        return true;
                    }
                    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (mRecyclerView != null && mRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                            mRecyclerView.stopScroll();
                        }
                        if (getHeadersSize() == 1 && position >= 1) {//有筛选header item, 当前焦点未在第一个item，则返回到筛选item第一个
                            mRecyclerView.scrollToPosition(0);
                            mActivity.backToFirstItemOfFilterView();
                            return true;
                        } else if (getHeadersSize() == 0 && position > 0) {//普通item，当前焦点未在第一个item，则返回到普通item第一个
                            mRecyclerView.scrollToPosition(0);
                            mRecyclerView.post(new FocusItemRunnable(mRecyclerView));
                            return true;
                        }
                        //当前焦点在第一个item，返回左侧
                        mActivity.backFromRightItem = true;
                        mActivity.setLeftSelectedPos();
                        mActivity.setCurrLineTxt(false, -1);
                        return true;
                    }
                    return false;
                }
            });
        }

        private boolean isLastItemOfEveryLine(int position) {
            if (getHeadersSize() == 1) {
                return position % SPAN_VRS == 0;
            } else {
                return position % SPAN_VRS == 4;
            }
        }

        private boolean isFirstItemOfEveryLine(int position) {
            if (getHeadersSize() == 1) {
                return position % SPAN_VRS == 1;
            } else {
                return position % SPAN_VRS == 0;
            }
        }

        private boolean isLastRow(int position) {
            if (getHeadersSize() == 1) {
                return ((position - 1) / SPAN_VRS == (getItemCount() - 1 - 1) / SPAN_VRS);
            } else {
                return (position / SPAN_VRS == (getItemCount() - 1) / SPAN_VRS);
            }
        }

        private boolean isLastItem(int position) {
            return position == getItemCount() - 1;
        }

        private int computeColumn(int position) {
            if (getHeadersSize() == 1) {
                return (position - 1) % SPAN_VRS;
            } else {
                return position % SPAN_VRS;
            }
        }

        private void handleFocus(View itemView) {
            layout_grid_item_root.setBackgroundResource(R.drawable.bg_hfc_focus);
            layout_grid_item_focus_desc.setVisibility(View.VISIBLE);
            grid_model_focus_play.setVisibility(View.VISIBLE);
            grid_model_focus_play.showWaveAnimation();
            tv_sets.setVisibility(View.VISIBLE);
            grid_sets_bg.setVisibility(View.VISIBLE);
            grid_sets_bg.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_item_type_one_no_radius));
            //add focusBorderView
//            if (mActivity.checkColumnNum(getAdapterPosition()) && mFocusView != null) {
//                mFocusView.setFocusView(itemView);
//                FocusUtil.setFocusAnimator(itemView, mFocusView);
//            }
            FocusUtil.setFocusAnimator(itemView, 1.07f, 200);
        }

        private void handleUnfocused(View itemView) {
            layout_grid_item_root.setBackgroundResource(R.color.transparent);
            layout_grid_item_focus_desc.setVisibility(View.INVISIBLE);
            grid_model_focus_play.cancelWaveAnimation();
            grid_model_focus_play.setVisibility(View.GONE);
            tv_sets.setVisibility(View.GONE);
            grid_sets_bg.setVisibility(View.GONE);
            grid_sets_bg.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_item_type_one));
            //remove focusBorderView
//            if (mFocusView != null) {
//                mFocusView.setUnFocusView(itemView);
//            }
            FocusUtil.setUnFocusAnimator(itemView);
        }
    }

    /**
     * Custom ViewHolder for PGC video
     */
    public class PgcViewHolder extends RecyclerView.ViewHolder {

        CornerTagImageView ctiv_poster;
        View view_count;
        LinearLayout layout_producer;
        public TextView tv_title;
        TextView tv_count;
        TextView tv_producer;
        TextView tv_grid_model_focus_title;
        ImageView iv_count;
        View grid_image_cover;
        GlideImageView giv_producer;
        public LinearLayout layout_grid_item_focus_desc;
        public RippleDiffuse grid_model_focus_play;
        public RelativeLayout layout_grid_item_root;

        public PgcViewHolder(View itemView) {
            super(itemView);

            ctiv_poster = (CornerTagImageView) itemView.findViewById(R.id.grid_model_image);
            view_count = itemView.findViewById(R.id.layout_count);
            layout_producer = (LinearLayout) itemView.findViewById(R.id.producer);
            tv_title = (TextView) itemView.findViewById(R.id.grid_model_title);
            iv_count = (ImageView) itemView.findViewById(R.id.play_count_icon);
            tv_count = (TextView) itemView.findViewById(R.id.play_count);
            tv_producer = (TextView) itemView.findViewById(R.id.producer_title);
            giv_producer = (GlideImageView) layout_producer.findViewById(R.id.producer_icon);
            layout_grid_item_focus_desc = (LinearLayout) itemView.findViewById(R.id.layout_grid_item_focus_desc);
            grid_model_focus_play = (RippleDiffuse) itemView.findViewById(R.id.grid_model_focus_play);
            tv_grid_model_focus_title = (TextView) itemView.findViewById(R.id.tv_grid_model_focus_title);
            grid_image_cover = (View) itemView.findViewById(R.id.grid_image_cover);
            layout_grid_item_root = (RelativeLayout) itemView.findViewById(R.id.layout_grid_item_root);

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
//                        if (!mRecyclerView.getIsLongPressed()) {
//                            //update current line number
//                            mActivity.setCurrLineTxt(true, getAdapterPosition());
//                        }
//
//                        if (mRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
//                            return;
//                        }

                        handleFocus(v);
                    } else {
                        handleUnfocused(v);
                    }
                }
            });

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
//                    if (!isEnabledUpKey) {
//                        return true;
//                    }

                    int position = getAdapterPosition();
//                    if (event.getAction() == KeyEvent.ACTION_DOWN) {
//                        mKeyCode = keyCode;
//                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_UP) {
//                            if (mClickedTime == 0) {
//                                mClickedTime = System.currentTimeMillis();
//                                mActivity.setColumnNum(computeColumn(position));
//                            } else if (System.currentTimeMillis() - mClickedTime > 800) {
//                                mClickedTime = System.currentTimeMillis();
//                                mActivity.setColumnNum(computeColumn(position));
//                                ((GridListActivityNew) mContext).setViewFocusability(true);
//                            } else {
//                                mClickedTime = System.currentTimeMillis();
//                                ((GridListActivityNew) mContext).setViewFocusability(false);
//                            }
//                        }
//                    }
//                    //for the second line items to avoid continue click in short time(for XiaoMi TV)
//                    if (position / SPAN_PGC == 1 && keyCode == KeyEvent.KEYCODE_DPAD_UP
//                            && event.getAction() == KeyEvent.ACTION_DOWN) {
//                        isEnabledUpKey = false;
//                        mHandler.sendEmptyMessageDelayed(MSG_DELAY_CONTINUE_CLICK, 500);
//                    }
                    //for the second line items with filter header
                    if (getHeadersSize() == 1 && (position / SPAN_PGC == 0 || position / SPAN_PGC == 1) && keyCode == KeyEvent.KEYCODE_DPAD_UP
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        return false;
                    }
                    //for the first line items
                    if (position / SPAN_PGC == 0 && keyCode == KeyEvent.KEYCODE_DPAD_UP
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        mActivity.setFocusRoute(false);
                        if (event.getRepeatCount() == 0 && !mActivity.displayHeaderView(true)
                                && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                            mActivity.focusOnTopBar();
                            mActivity.setCurrLineTxt(false, -1);
                        }
                        return true;
                    }
//
                    //for the last row of video list
                    if (isLastRow(position) && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                            return true;
                        } else if (isLastItem(position) && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                            return true;
                        }
                    }
                    //for the last item of every line with filter header
                    if (isLastItemOfEveryLine(position) && keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        return false;
                    }
                    //for the last item of every line
                    if (isLastItemOfEveryLine(position) && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (mRecyclerView != null) {
                            RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(position + 1);
                            if (viewHolder != null && viewHolder.itemView != null) {
                                viewHolder.itemView.requestFocus();
                            }
                            return true;
                        }
                    }
                    //for the first item of every line
                    if (isFirstItemOfEveryLine(position) && keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (mRecyclerView != null && mRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                            mRecyclerView.stopScroll();
                        }
                        mActivity.setLeftSelectedPos();
                        mActivity.setCurrLineTxt(false, -1);
                        return true;
                    }
                    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (mRecyclerView != null && mRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                            mRecyclerView.stopScroll();
                        }
                        if (getHeadersSize() == 1 && position >= 1) {//有筛选header item, 当前焦点未在第一个item，则返回到筛选item第一个
                            mRecyclerView.scrollToPosition(0);
                            mActivity.backToFirstItemOfFilterView();
                            return true;
                        } else if (getHeadersSize() == 0 && position > 0) {//普通item，当前焦点未在第一个item，则返回到普通item第一个
                            mRecyclerView.scrollToPosition(0);
                            mRecyclerView.post(new FocusItemRunnable(mRecyclerView));
                            return true;
                        }
                        //当前焦点在第一个item，返回左侧
                        mActivity.backFromRightItem = true;
                        mActivity.setLeftSelectedPos();
                        mActivity.setCurrLineTxt(false, -1);
                        return true;
                    }
                    return false;
                }
            });
        }

        private boolean isLastItemOfEveryLine(int position) {
            if (getHeadersSize() == 1) {
                return position % SPAN_PGC == 0;
            } else {
                return position % SPAN_PGC == 4;
            }
        }

        private boolean isFirstItemOfEveryLine(int position) {
            if (getHeadersSize() == 1) {
                return position % SPAN_PGC == 1;
            } else {
                return position % SPAN_PGC == 0;
            }
        }

        private boolean isLastRow(int position) {
            if (getHeadersSize() == 1) {
                return ((position - 1) / SPAN_PGC == (getItemCount() - 1 - 1) / SPAN_PGC);
            } else {
                return (position / SPAN_PGC == (getItemCount() - 1) / SPAN_PGC);
            }
        }

        private boolean isLastItem(int position) {
            return position == getItemCount() - 1;
        }

        private int computeColumn(int position) {
            if (getHeadersSize() == 1) {
                return (position - 1) % SPAN_PGC;
            } else {
                return position % SPAN_PGC;
            }
        }

        private void handleFocus(View itemView) {
            layout_grid_item_root.setBackgroundResource(R.drawable.bg_hfc_focus);
            layout_grid_item_focus_desc.setVisibility(View.VISIBLE);
            grid_model_focus_play.setVisibility(View.VISIBLE);
            grid_model_focus_play.showWaveAnimation();
            tv_title.setVisibility(View.GONE);
            grid_image_cover.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_item_type_one_no_radius));

            //add focusBorderView
//            if (mActivity.checkColumnNum(getAdapterPosition()) && mFocusView != null) {
//                mFocusView.setFocusView(itemView);
//                FocusUtil.setFocusAnimator(itemView, mFocusView);
//            }
            FocusUtil.setFocusAnimator(itemView, 1.07f, 200);
        }

        private void handleUnfocused(View itemView) {
            layout_grid_item_root.setBackgroundResource(R.color.transparent);
            layout_grid_item_focus_desc.setVisibility(View.INVISIBLE);
            grid_model_focus_play.cancelWaveAnimation();
            grid_model_focus_play.setVisibility(View.GONE);
            tv_title.setVisibility(View.VISIBLE);
            grid_image_cover.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_item_type_one));
            //remove focusBorderView
//            if (mFocusView != null) {
//                mFocusView.setUnFocusView(itemView);
//            }
            FocusUtil.setUnFocusAnimator(itemView);
        }
    }

    class HeaderViewHolder extends RecyclerView.ViewHolder {

        public HeaderViewHolder(View itemView) {
            super(itemView);
        }
    }

    class MyHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            int firstPos = ((CustomGridLayoutManager) mRecyclerView.getLayoutManager()).findFirstVisibleItemPosition();
            int lastPos = ((CustomGridLayoutManager) mRecyclerView.getLayoutManager()).findLastVisibleItemPosition();
            int headerSize = getHeadersSize();
            switch (msg.what) {
                case MSG_IMG_LOAD:
                    if (firstPos < 0 || lastPos >= getItemCount()) {
                        return;
                    }

                    if (isPgc) {
                        CornerTagImageView ctiv_poster;
                        PgcViewHolder pgcViewHolder;
                        GlideImageView giv_producer;
                        if (mDataSource.get(firstPos).cateCodeFirst != CornerTagImageView.CORNER_TYPE_SOHUCLASS) {
                            for (int i = firstPos; i <= lastPos; i++) {
                                if (mRecyclerView.findViewHolderForAdapterPosition(i) instanceof HeaderViewHolder) {
                                    continue;
                                }
                                int index = i - headerSize;
                                if (index < 0) {
                                    return;
                                }
                                ListAlbumModel model = mDataSource.get(index);
                                pgcViewHolder = (PgcViewHolder) mRecyclerView.findViewHolderForAdapterPosition(i);
                                giv_producer = (GlideImageView) pgcViewHolder.itemView.findViewById(R.id.producer_icon);
                                if (model == null || pgcViewHolder == null) {
                                    return;
                                }
                                handleImageWithTag(pgcViewHolder.ctiv_poster, model, model.smallCover);
                                giv_producer.setCircleImageRes(model.smallPhoto != null ? model.smallPhoto : "",
                                        mContext.getResources().getDrawable(R.drawable.producer_icon_normal), mContext.getResources().getDrawable(R.drawable.producer_icon_normal), true);
                            }
                        } else {
                            for (int i = firstPos; i <= lastPos; i++) {
                                if (mRecyclerView.findViewHolderForAdapterPosition(i) instanceof HeaderViewHolder) {
                                    continue;
                                }
                                int index = i - headerSize;
                                if (index < 0) {
                                    return;
                                }
                                ListAlbumModel model = mDataSource.get(index);

                                pgcViewHolder = (PgcViewHolder) mRecyclerView.findViewHolderForAdapterPosition(i);
                                giv_producer = (GlideImageView) pgcViewHolder.itemView.findViewById(R.id.producer_icon);
                                if (model == null || pgcViewHolder == null) {
                                    return;
                                }
                                handleImageWithTag(pgcViewHolder.ctiv_poster, model, model.plCoverImg169);
                                giv_producer.setCircleImageRes(model.smallIcon != null ? model.smallIcon : "",
                                        mContext.getResources().getDrawable(R.drawable.producer_icon_normal), mContext.getResources().getDrawable(R.drawable.producer_icon_normal), true);
                            }
                        }
                    } else {
                        VrsViewHolder vrsViewHolder;
                        for (int i = firstPos; i <= lastPos; i++) {
                            if (mRecyclerView.findViewHolderForAdapterPosition(i) instanceof HeaderViewHolder) {
                                continue;
                            }
                            vrsViewHolder = (VrsViewHolder) mRecyclerView.findViewHolderForAdapterPosition(i);
                            int index = i - headerSize;
                            if (index < 0) {
                                return;
                            }
                            ListAlbumModel model = mDataSource.get(index);
                            if (model == null || vrsViewHolder == null) {
                                return;
                            }
                            handleImageWithTag(vrsViewHolder.ctiv_poster, model, model.albumExtendsPic_240_330);
                        }
                    }
                    break;
                case MSG_DELAY_CONTINUE_CLICK:
                    isEnabledUpKey = true;
                    break;
                default:
                    break;
            }
        }

        private void handleImageWithTag(CornerTagImageView imageView, ListAlbumModel model, String uri) {
            if (isPgc) {
//                imageView.setCornerTypeWithType(0, 0, 0, 0, CornerTagImageView.CORNER_TYPE_NONE);
                imageView.setPgcCornerTypeWithType(model.cornerType);
            } else {
                imageView.setCornerHeightRes(R.dimen.y33);
                imageView.setCornerTypeWithType(model.tvIsFee, model.tvIsEarly, model.useTicket, model.paySeparate, model.cornerType);
                imageView.setCornerPaddingX(mContext.getResources().getDimensionPixelOffset(R.dimen.x8));
                imageView.setCornerPaddingY(mContext.getResources().getDimensionPixelOffset(R.dimen.y8));
            }
            Glide.with(mContext)
                    .load(TextUtils.isEmpty(uri) ? "" : uri)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(imageView);
        }
    }

}
