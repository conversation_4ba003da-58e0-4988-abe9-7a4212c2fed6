package com.sohuott.tv.vod.videodetail.vlist;

import android.content.Context;
import android.util.AttributeSet;
import android.view.FocusFinder;
import android.view.KeyEvent;

import androidx.leanback.widget.VerticalGridView;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

public class DetailVerticalGridView extends VerticalGridView {
    public DetailVerticalGridView(Context context) {
        super(context);
    }

    public DetailVerticalGridView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DetailVerticalGridView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
            if (getSelectedPosition() % 4 ==0) {
                return true;
            }
            LibDeprecatedLogger.d("left : " + FocusFinder.getInstance().findNextFocus(this, getFocusedChild(), FOCUS_LEFT));
            LibDeprecatedLogger.d("position : " + getSelectedPosition());
        }
        return super.dispatchKeyEvent(event);
    }
}
