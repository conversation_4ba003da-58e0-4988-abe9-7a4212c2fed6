package com.sohuott.tv.vod.utils;

import android.app.Instrumentation;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.KeyEvent;

import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.model.HistoryEvent;

import org.greenrobot.eventbus.EventBus;

import static com.sohuott.tv.vod.lib.utils.Constant.PAGE_H5;

/**
 * Created by wenjingbian on 2016/11/9.
 */

public class ActionUtil {

    private static final String LOG_TAG = ActionUtil.class.getSimpleName();

    /**
     * String Constants for KeyEvent to match KeyCode
     */
    private static final String KEY_EVENT_UP = "UP";
    private static final String KEY_EVENT_DOWN = "DOWN";
    private static final String KEY_EVENT_LEFT = "LEFT";
    private static final String KEY_EVENT_RIGHT = "RIGHT";
    private static final String KEY_EVENT_ENTER = "ENTER";
    private static final String KEY_EVENT_HOME = "HOME";
    private static final String KEY_EVENT_BACK = "BACK";
    private static final String KEY_EVENT_MENU = "MENU";
    private static final String KEY_EVENT_VOLUME_UP = "VOLUME_UP";
    private static final String KEY_EVENT_VOLUME_DOWN = "VOLUME_DOWN";

    /**
     * String Constants for KeyEvent to match action
     */
    private static final String KEY_ACTION_DOWN = "0";
    private static final String KEY_ACTION_UP = "1";
    private static final String KEY_ACTION_CLICK = "-2";
    private static final String KEY_ACTION_PLAY = "4";
    private static final String KEY_ACTION_PLAY_PGC = "5";

    private static final String SPLIT_CHARACTER = ",";

    /**
     * Integer Constants of action
     */
    private static final int ACTION_DEFAULT = -1;
    private static final int ACTION_CLICK = -2;
    private static final int ACTION_PLAY = 3;

    public static final String CHANNEL_NAME = "channel_name";

    private static int isPgc = 0;

    /**
     * @des 用于辅助网络变化监控
     * @auth zijiacui
     * @date 2018/1/25/13:54
     * @version V1.0.0
     */
    public static final String ACTION_NET_CHANGED="action_net_changed";

    /**
     * Deal with KeyEvent from H5
     *
     * @param context:  Caller's Context
     * @param eventStr: KeyEvent string contains action and KeyCode
     */
    public static void keyEventAction(Context context, String eventStr) {
        if (eventStr == null || eventStr.length() == 0) {
            Log.d(LOG_TAG, "Invalidate action string.");
            return;
        }
        Log.d(LOG_TAG,eventStr);

        //deal with eventStr to get actionStr and keyCodeStr
        String actionStr = null;
        String keyCodeStr = null;
        String aid = null;
        String vid = null;
        int keyCode = KeyEvent.KEYCODE_UNKNOWN;
        int action = ACTION_DEFAULT;
        String[] eventArray = eventStr.split(SPLIT_CHARACTER);
        if (eventArray.length == 0) {
            Log.d(LOG_TAG, "Unavailable message of KeyEvent.");
        } else if (eventArray.length == 2) {
            keyCodeStr = eventArray[0];
            actionStr = eventArray[1];
        } else if (eventArray.length == 3) {
            aid = eventArray[0];
            vid = eventArray[1];
            actionStr = eventArray[2];
        }

        //Get action
        if (actionStr.equals(KEY_ACTION_DOWN)) {
            action = KeyEvent.ACTION_DOWN;
        } else if (actionStr.equals(KEY_ACTION_UP)) {
            action = KeyEvent.ACTION_UP;
        } else if (actionStr.equals(KEY_ACTION_CLICK)) {
            action = ACTION_CLICK;
        } else if (actionStr.equals(KEY_ACTION_PLAY)) {
            action = ACTION_PLAY;
            keyCodeStr = KEY_ACTION_PLAY;
            isPgc = 0;
        } else if (actionStr.equals(KEY_ACTION_PLAY_PGC)) {
            action = ACTION_PLAY;
            keyCodeStr = KEY_ACTION_PLAY;
            isPgc = 2;
        } else {
            Log.d(LOG_TAG, "Unavailable action of KeyEvent.");
        }
        Intent intent;
        //Get KeyCode
        switch (keyCodeStr) {

            case KEY_EVENT_UP:
                keyCode = KeyEvent.KEYCODE_DPAD_UP;
                break;
            case KEY_EVENT_DOWN:
                keyCode = KeyEvent.KEYCODE_DPAD_DOWN;
                break;
            case KEY_EVENT_LEFT:
                keyCode = KeyEvent.KEYCODE_DPAD_LEFT;
                break;
            case KEY_EVENT_RIGHT:
                keyCode = KeyEvent.KEYCODE_DPAD_RIGHT;
                break;
            case KEY_EVENT_ENTER:
                keyCode = KeyEvent.KEYCODE_DPAD_CENTER;
                break;
            case KEY_EVENT_VOLUME_UP:
                keyCode = KeyEvent.KEYCODE_VOLUME_UP;
                break;
            case KEY_EVENT_VOLUME_DOWN:
                keyCode = KeyEvent.KEYCODE_VOLUME_DOWN;
                break;
            case KEY_EVENT_BACK:
                keyCode = KeyEvent.KEYCODE_BACK;
                break;
            case KEY_EVENT_HOME:
//                intent = new Intent(Intent.ACTION_MAIN);
//                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                intent.addCategory(Intent.CATEGORY_HOME);
//                context.startActivity(intent);
//                intent = new Intent(context, HomeActivity.class);
//                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                intent.putExtra(CHANNEL_NAME, "推荐");
//                context.startActivity(intent);
//                keyCode = -1;
                break;
            case KEY_EVENT_MENU:
                keyCode = KeyEvent.KEYCODE_MENU;
                break;
            case KEY_ACTION_PLAY:
                if (aid != null && vid != null) {
                    ActivityLauncher.startVideoDetailWithNewTask(context, Integer.parseInt(vid), PAGE_H5, isPgc);
                    keyCode = -1;
                    EventBus.getDefault().post(new HistoryEvent(isPgc, isPgc == 0 ? Integer.valueOf(vid)
                            : Integer.valueOf(aid)));
                }
                break;
            default:
                break;
        }

        //Send KeyEvent to simulate Key on Android device.
        try {
            if (keyCode > 0) {
                Instrumentation instrumentation = new Instrumentation();
                if (action >= 0) {
                    KeyEvent keyEvent = new KeyEvent(action, keyCode);
                    instrumentation.sendKeySync(keyEvent);
                    Log.d(LOG_TAG,"action > 0" + action + "," + keyCode);
                } else if (action == ACTION_CLICK) {
                    Log.d(LOG_TAG,"action == -2" + action + "," + keyCode);
                    instrumentation.sendKeyDownUpSync(keyCode);
                } else {
                    Log.d(LOG_TAG, "Cannot find Action.");
                }
            } else if (keyCode == KeyEvent.KEYCODE_UNKNOWN) {
                if (keyCodeStr.contains("超级会员")) {
//                    if (LoginUserInformationHelper.getHelper(context.getApplicationContext()).getIsLogin()) {
                    ActivityLauncher.startPayActivity(context, PayActivity.PAY_SOURCE_PHONE_REMOTE);
//                    } else {
//                        ActivityLauncher.startLoginActivity(context, false);
//                    }
                } else if (keyCodeStr.contains("频道")) {
                    String channel;
                    if (keyCodeStr.contains("联播")) {

                        channel = "联播";
                    } else if (keyCodeStr.contains("推荐")) {
                        channel = "推荐";
                    } else if (keyCodeStr.contains("电视剧")) {
                        channel = "电视剧";
                    } else if (keyCodeStr.contains("会员")) {
                        channel = "会员";
                    } else if (keyCodeStr.contains("短视频")) {
                        channel = "短视频";
                    } else if (keyCodeStr.contains("电影")) {
                        channel = "电影";
                    } else if (keyCodeStr.contains("少儿")) {
                        channel = "少儿";
                    } else if (keyCodeStr.contains("全部分类")) {
                        channel = "全部分类";
                    } else if (keyCodeStr.contains("我的")) {
                        channel = "我的";
                    } else if (keyCodeStr.contains("综艺")) {
                        channel = "综艺";
                    } else if (keyCodeStr.contains("美剧")) {
                        channel = "美剧";
                    } else {
                        channel = "推荐";
                    }
//                    intent = new Intent(context, HomeActivity.class);
//                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                    intent.putExtra(CHANNEL_NAME, channel);
//                    context.startActivity(intent);
                } else {
//                    keyCodeStr = PinyinHelper.getShortPinyin(keyCodeStr);
                    ActivityLauncher.startSearchActivity(context, keyCodeStr, true);
                }
                Log.d(LOG_TAG, keyCodeStr);
//                Runtime.getRuntime().exec("input text " + keyCodeStr);
            } else {
                Log.d(LOG_TAG, "Cannot find KeyCode.");
            }
        } catch (Exception e) {
            Log.e(LOG_TAG, "Exception in keyEventAction(): " + e);
        }
    }

}
