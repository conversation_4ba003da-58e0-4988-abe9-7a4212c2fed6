package com.sohuott.tv.vod.fragment;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.common.AccountException;
import com.sohuott.tv.vod.account.common.Listener;
import com.sohuott.tv.vod.account.login.Login;
import com.sohuott.tv.vod.account.login.PassportLogin;
import com.sohuott.tv.vod.account.payment.PayApi;
import com.sohuott.tv.vod.lib.model.PermissionCheck;
import com.sohuott.tv.vod.account.register.Captcha;
import com.sohuott.tv.vod.account.register.RegisterApi;
import com.sohuott.tv.vod.account.user.UserApi;
import com.sohuott.tv.vod.account.user.UserUtil;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.activity.TeenagerLockActivity;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.utils.SimpleDisposableObsever;
import com.sohu.lib_utils.StringUtil;

import java.util.HashMap;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by xianrongchen on 2017-03-30.
 */
public class RegisterFragment extends BaseFragment implements View.OnClickListener, View.OnFocusChangeListener {

    private View mView;

    private Context mContext;

    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    private boolean mIsChild = false;

    //New Login
    private CountDownTimer mCountDownTimer;
    private TextView mInput, mErrorTips;
    private Button mKey1, mKey2, mKey3, mKey4, mKey5, mKey6, mKey7, mKey8, mKey9, mKey0, mKeyClear, mKeyDel, mKeyGetCaptcha;
    private int mInputStatus = INPUT_STATUS_PHONE_DEFAULT;
    private String mLoginPhoneNumber;
    //0手机号初始化、1输入手机号、2验证码初始化 3、输入验证码
    private final static int INPUT_STATUS_PHONE_DEFAULT = 0;
    private final static int INPUT_STATUS_PHONE_EDITED = 1;
    private final static int INPUT_STATUS_CAPTCHA_DEFAULT = 2;
    private final static int INPUT_STATUS_CAPTCHA_EDITED = 3;


    public static RegisterFragment newInstance(Bundle bundle) {
        RegisterFragment registerFragment = new RegisterFragment();
        registerFragment.judgeLoginSource(bundle.getInt(ParamConstant.PARAM_SOURCE, 0));
        registerFragment.setArguments(bundle);
        return registerFragment;
    }

    private void judgeLoginSource(int source) {
        mIsChild = source == Constant.CHILD_SOURCE;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
    }

    //Animation mLeftInAnim, mLeftOutAnim, mRightInAnim, mRightOutAnim;
    LayoutInflater mLayoutInflater;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setSubPageName("6_register");
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mView = inflater.inflate(R.layout.fragment_register, null, false);
        mLayoutInflater = inflater;
        initView();
        setSubPageName("6_register");
        return mView;
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ViewGroup vg = (ViewGroup) view;
        vg.setClipChildren(false);
        vg.setClipToPadding(false);
    }

    private int getInputStatus() {
        return mInputStatus;
    }

    private void setInputStatus(int status) {
        switch (status) {
            case INPUT_STATUS_PHONE_DEFAULT:
                mInputStatus = INPUT_STATUS_PHONE_DEFAULT;
                mInput.setText("请输入手机号");
                break;
            case INPUT_STATUS_PHONE_EDITED:
                mInputStatus = INPUT_STATUS_PHONE_EDITED;
                break;
            case INPUT_STATUS_CAPTCHA_DEFAULT:
                mInputStatus = INPUT_STATUS_CAPTCHA_DEFAULT;
                mInput.setText("请输入验证码");
                break;
            case INPUT_STATUS_CAPTCHA_EDITED:
                mInputStatus = INPUT_STATUS_CAPTCHA_EDITED;
                break;
        }
    }

    //186 1112
    private void delInput(CharSequence inputText) {
        if (getInputStatus() == INPUT_STATUS_PHONE_EDITED || getInputStatus() == INPUT_STATUS_CAPTCHA_EDITED) {
            if (trim(inputText.toString()).length() > 1) {
                if (trim(inputText.toString()).length() == 7 || trim(inputText.toString()).length() == 3) {
                    mInput.setText(inputText.toString().substring(0, inputText.length() - 2));
                } else {
                    mInput.setText(inputText.toString().substring(0, inputText.length() - 1));
                }

            } else {
                setInputStatus(getInputStatus() - 1);
            }
        }
    }

    private String trim(String s) {
        return s.replace(" ", "");
    }


    private void checkAndInput(String key) {
        CharSequence inputText = mInput.getText();
        if (key.equals("-1")) {
            delInput(inputText);
            return;
        }
        //186 1112 7260
        if (getInputStatus() == INPUT_STATUS_PHONE_DEFAULT) {
            mInput.setText(key);
            setInputStatus(INPUT_STATUS_PHONE_EDITED);
        } else if (getInputStatus() == INPUT_STATUS_PHONE_EDITED) {
            if (trim(inputText.toString()).length() < 11) {
                if (trim(inputText.toString()).length() == 3 || trim(inputText.toString()).length() == 7) {
                    mInput.setText(inputText + " " + key);
                } else {
                    mInput.setText(inputText + key);
                }
                if (trim(inputText.toString()).length() == 10) {
                    mKeyGetCaptcha.requestFocus();
                }
            }
        } else if (getInputStatus() == INPUT_STATUS_CAPTCHA_DEFAULT) {
            mInput.setText(key);
            setInputStatus(INPUT_STATUS_CAPTCHA_EDITED);
        } else if (getInputStatus() == INPUT_STATUS_CAPTCHA_EDITED) {
            if (mInput.getText().length() < 6) {
                mInput.setText(inputText + key);
                if (mInput.getText().length() == 6) {
                    registerLogin(mInput.getText().toString());
                }
            }
        }
    }

    private boolean checkPhoneValid(String phoneNumber) {
        if (getInputStatus() == INPUT_STATUS_PHONE_DEFAULT) {
            return false;
        } else {
            return trim(phoneNumber).length() == 11;
        }
    }


    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.login_input_0:
                checkAndInput("0");
                break;
            case R.id.login_input_1:
                checkAndInput("1");
                break;
            case R.id.login_input_2:
                checkAndInput("2");
                break;
            case R.id.login_input_3:
                checkAndInput("3");
                break;
            case R.id.login_input_4:
                checkAndInput("4");
                break;
            case R.id.login_input_5:
                checkAndInput("5");
                break;
            case R.id.login_input_6:
                checkAndInput("6");
                break;
            case R.id.login_input_7:
                checkAndInput("7");
                break;
            case R.id.login_input_8:
                checkAndInput("8");
                break;
            case R.id.login_input_9:
                checkAndInput("9");
                break;
            case R.id.login_input_del:
                checkAndInput("-1");
                break;
            case R.id.login_input_clear:
                if (getInputStatus() == INPUT_STATUS_PHONE_EDITED) {
                    setInputStatus(INPUT_STATUS_PHONE_DEFAULT);
                } else if (getInputStatus() == INPUT_STATUS_CAPTCHA_EDITED) {
                    setInputStatus(INPUT_STATUS_CAPTCHA_DEFAULT);
                }
                break;
            case R.id.get_msg_captcha:
                if (getInputStatus() == INPUT_STATUS_PHONE_EDITED) {
                    if (checkPhoneValid(trim(mInput.getText().toString()))) {
                        getCaptcha(trim(mInput.getText().toString()));
                    } else {
                        ToastUtils.showToast(mContext, "手机号码不正确");
                    }
                } else if (getInputStatus() == INPUT_STATUS_CAPTCHA_DEFAULT || getInputStatus() == INPUT_STATUS_CAPTCHA_EDITED) {
                    if (checkPhoneValid(mLoginPhoneNumber)) {
                        getCaptcha(mLoginPhoneNumber);
                    }
                } else if (getInputStatus() == INPUT_STATUS_PHONE_DEFAULT) {
                    ToastUtils.showToast(mContext, "请输入手机号");
                }
                break;
            default:
                break;
        }
    }

    public View getNextFocus() {
        return mView.findViewById(R.id.login_input_1);
    }

    private void initView() {
        //new
        mErrorTips = (TextView) mView.findViewById(R.id.error_tip);
        mInput = (TextView) mView.findViewById(R.id.login_input);
        mKey1 = (Button) mView.findViewById(R.id.login_input_1);
        mKey2 = (Button) mView.findViewById(R.id.login_input_2);
        mKey3 = (Button) mView.findViewById(R.id.login_input_3);
        mKey4 = (Button) mView.findViewById(R.id.login_input_4);
        mKey5 = (Button) mView.findViewById(R.id.login_input_5);
        mKey6 = (Button) mView.findViewById(R.id.login_input_6);
        mKey7 = (Button) mView.findViewById(R.id.login_input_7);
        mKey8 = (Button) mView.findViewById(R.id.login_input_8);
        mKey9 = (Button) mView.findViewById(R.id.login_input_9);
        mKey0 = (Button) mView.findViewById(R.id.login_input_0);
        mKeyDel = (Button) mView.findViewById(R.id.login_input_del);
        mKeyClear = (Button) mView.findViewById(R.id.login_input_clear);

        mKeyGetCaptcha = (Button) mView.findViewById(R.id.get_msg_captcha);
        setListener();
    }


    private void setListener() {
        //new
        mKey1.setOnClickListener(this);
        mKey2.setOnClickListener(this);
        mKey3.setOnClickListener(this);
        mKey4.setOnClickListener(this);
        mKey5.setOnClickListener(this);
        mKey6.setOnClickListener(this);
        mKey7.setOnClickListener(this);
        mKey8.setOnClickListener(this);
        mKey9.setOnClickListener(this);
        mKey0.setOnClickListener(this);
        mKeyDel.setOnClickListener(this);
        mKeyClear.setOnClickListener(this);
        mKeyGetCaptcha.setOnClickListener(this);

        mKey1.setOnFocusChangeListener(this);
        mKey2.setOnFocusChangeListener(this);
        mKey3.setOnFocusChangeListener(this);
        mKey4.setOnFocusChangeListener(this);
        mKey5.setOnFocusChangeListener(this);
        mKey6.setOnFocusChangeListener(this);
        mKey7.setOnFocusChangeListener(this);
        mKey8.setOnFocusChangeListener(this);
        mKey9.setOnFocusChangeListener(this);
        mKey0.setOnFocusChangeListener(this);
        mKeyDel.setOnFocusChangeListener(this);
        mKeyClear.setOnFocusChangeListener(this);
        mKeyGetCaptcha.setOnFocusChangeListener(this);
    }

    private void requestSendSms() {
        if (mCountDownTimer != null) {
            return;
        } else {
            mCountDownTimer = new CountDownTimer(1000 * 60, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    mKeyGetCaptcha.setClickable(false);
                    mKeyGetCaptcha.setText((millisUntilFinished / 1000) + "s后重新获取");
                }

                @Override
                public void onFinish() {
                    mCountDownTimer = null;
                    mKeyGetCaptcha.setClickable(true);
                    mKeyGetCaptcha.setText("获取验证码");
                }
            };
        }
        mCountDownTimer.start();
    }

    DisposableObserver mCodeObserver;

    /**
     * register step 1:
     */
    private void getCaptcha(String phoneNumber) {
        mCodeObserver = RegisterApi.getSmsCode(phoneNumber,
                new Listener<Captcha>() {
                    @Override
                    public void onSuccess(Captcha captcha) {
                        mErrorTips.setText("验证码已发送到手机。");
                        mErrorTips.setTextColor(Color.parseColor("#B2E8E8FF"));
                        if (StringUtil.isEmpty(mLoginPhoneNumber)) {
                            mLoginPhoneNumber = trim(mInput.getText().toString());
                        }
                        setInputStatus(INPUT_STATUS_CAPTCHA_DEFAULT);
                        requestSendSms();
                    }

                    @Override
                    public void onError(Throwable e) {
                        mErrorTips.setText("");
                        if (e instanceof AccountException) {
                            if (((AccountException) e).getErrCode() == 40108) {
                                mErrorTips.setText("操作频繁，请1小时后再试。");
                                mErrorTips.setTextColor(Color.parseColor("#C84240"));
                            } else if (((AccountException) e).getErrCode() == 500) {
                                ToastUtils.showToast(mContext, "手机号错误，请重新输入");
                            } else {
                                ToastUtils.showToast(mContext, e.getMessage());
                            }
                        }
                        LibDeprecatedLogger.w("getCaptcha(): onError() = " + e.toString());
                    }
                });
        mCompositeDisposable.add(mCodeObserver);
    }


    private String mPassport;
    private String mAppToken;
    SimpleDisposableObsever mStep2Observer;

    /**
     * register step2:get token
     */
    private void registerLogin(String code) {
        mStep2Observer = new SimpleDisposableObsever<PassportLogin>() {
            @Override
            public void onNext(PassportLogin value) {
                mPassport = value.passport;
                mAppToken = value.appSessionToken;


                LoginUserInformationHelper.getHelper(mContext).putLoginPassport(mPassport);
                LoginUserInformationHelper.getHelper(mContext).putLoginToken(mAppToken);

                UserApi.getUserInfoAfterPassportLogin(mContext, new Listener<Login>() {
                    @Override
                    public void onSuccess(Login login) {
                        boolean result = UserUtil.handleUserData(mContext, "", login, true);
                        if (login != null && login.getData() != null) {
                            UserUtil.syncSouthMedia(mContext, 0,
                                    login.getData().getBindMobile(),
                                    login.getData().getSecMobile());
                            UserUtil.updateQianFanLogin(mContext);
                        }
                        if (result) {
                            ToastUtils.showToast2(mContext, "登录成功");

                            HashMap pathInfo = new HashMap();
                            pathInfo.put("pageId", "1002");

                            HashMap memoInfo = new HashMap();
                            pathInfo.put("method", "1");
                            RequestManager.getInstance().onAllEvent(new EventInfo(10132, "slc"), pathInfo, null, memoInfo);

                            if (getActivity() == null) {
                                return;
                            }
                            boolean normalLogin = getArguments().getBoolean("normalLogin");
                            boolean fromVideoDetail = getArguments().getBoolean("fromVideoDetail");
                            boolean isTeenager = getArguments().getBoolean("isTeenager");
                            final int aid = getArguments().getInt("aid");
                            final int vid = getArguments().getInt("vid");
                            final String videoName = getArguments().getString("videoName");

                            if (isTeenager){
                                ActivityLauncher.startChildLockActivity(mContext, TeenagerLockActivity.TYPE_ONE_SET_PASSWORD);
                                getActivity().finish();
                                return;
                            }
                            if (normalLogin) {
                                if (fromVideoDetail) {
                                    if (aid != 0 || vid != 0) {
                                        LoginUserInformationHelper helper = LoginUserInformationHelper.getHelper(getContext());
                                        PayApi.getFilmCheckPermission(helper.getLoginPassport(),
                                                helper.getLoginToken(), aid, vid, 0, new Listener<PermissionCheck>() {
                                                    @Override
                                                    public void onSuccess(PermissionCheck permissionCheck) {
                                                        ActivityLauncher.startPayActivity(getContext(),
                                                                aid, vid, videoName, PayActivity.PAY_SOURCE_LOGIN);
                                                    }

                                                    @Override
                                                    public void onError(Throwable e) {
                                                        LibDeprecatedLogger.d("getFilmCheckPermission(): onError() = " + e.toString());
                                                    }
                                                });
                                    }
                                }
                            } else {
                                ActivityLauncher.startPayActivity(getContext(), PayActivity.PAY_SOURCE_LOGIN);
                            }
                            getActivity().finish();
                        }

                    }

                    @Override
                    public void onError(Throwable e) {
                        if (e instanceof AccountException) {
                            /**
                             * code expired ,need register again
                             */
                            if (((AccountException) e).getErrCode() ==
                                    AccountException.REGISTER_TOKEN_EXPIRED) {
                            }
                            ToastUtils.showToast(mContext, e.getLocalizedMessage());
                        } else {
                            ToastUtils.showToast(mContext, "登录失败");
                        }
                    }
                });
            }

            @Override
            public void onError(Throwable e) {
                setInputStatus(INPUT_STATUS_CAPTCHA_DEFAULT);
                LibDeprecatedLogger.w("register step 2:" + e.getMessage());
                if (e instanceof AccountException) {
                    ToastUtils.showToast(mContext, e.getMessage());
                } else {
                    ToastUtils.showToast(mContext, "登录异常");
                }

            }
        };
        RegisterApi.registerLogin(mLoginPhoneNumber,
                code, mStep2Observer);
        mCompositeDisposable.add(mStep2Observer);
//        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mCompositeDisposable.clear();
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (v instanceof Button) {
            if (hasFocus) {
                v.animate().scaleX(1.1f).scaleY(1.1f).setDuration(150).start();
                v.bringToFront();
            } else {
                v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(100).start();
            }
        }
    }
}
