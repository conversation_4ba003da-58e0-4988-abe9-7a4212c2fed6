package com.sohuott.tv.vod.videodetail.activity.viewmodel

import android.text.TextUtils
import cn.gd.snmottclient.SNMOTTClient
import cn.gd.snmottclient.util.SNMOTTSDKCallBack
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.ViewModelContext
import com.google.gson.GsonBuilder
import com.sohu.lib_utils.DeviceUtils
import com.sohu.lib_utils.MetaDataHelper
import com.sohu.ott.base.lib_user.UserApp
import com.sohu.ott.base.lib_user.UserInfoHelper.getGid
import com.sohu.ott.base.lib_user.UserInfoHelper.getUserDeviceMacAddress
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.PgcEpisodeVideos
import com.sohuott.tv.vod.lib.model.SouthMediaCheckResult
import com.sohuott.tv.vod.lib.model.VrsEpisodeVideos
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.EncryUtils
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.ui.EpisodeLayoutNew
import com.sohuott.tv.vod.videodetail.activity.repository.CollectRepository
import com.sohuott.tv.vod.videodetail.activity.repository.DrmRepository
import com.sohuott.tv.vod.videodetail.activity.repository.PayRepository
import com.sohuott.tv.vod.videodetail.activity.repository.UserRepository
import com.sohuott.tv.vod.videodetail.activity.repository.VideoRepository
import com.sohuott.tv.vod.videodetail.activity.state.SOUTHMEDIA_CHECK_FAIL
import com.sohuott.tv.vod.videodetail.activity.state.SOUTHMEDIA_CHECK_SUCCESS
import com.sohuott.tv.vod.videodetail.activity.state.VideoDetailState
import com.sohuott.tv.vod.widget.CornerTagImageView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class VideoDetailMviViewModel(
    initializerState: VideoDetailState,
    val userRepository: UserRepository,
) :
    MavericksViewModel<VideoDetailState>(initializerState) {
    private val _repositoryVideo = VideoRepository()
    private val _repositoryPay = PayRepository()
    private val _repositoryCollect = CollectRepository()
    private val _drmRepository = DrmRepository()


    fun onWindowModel(isAllFull: Boolean) {
        setState {
            copy(videoWindowAlwaysFull = isAllFull)
        }
    }

    fun requestDrmPlayInfo(
        vid: Int?,
        drmType: Int,
        mkey: String?
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            _drmRepository.requestDrmPlayInfo(vid, drmType, mkey).execute {
                copy(drmPlayInfoData = it)
            }
        }
    }

    /**
     * @param videoType 视频类型 vrs、 pgc
     * 第一次进入页面时候调用初始化  用护栏、 video 信息
     */
    fun onStartLoad(
        passport: String?,
        token: String?,
        videoType: Int,
        aid: Int?,
        gid: String?
    ) {
        viewModelScope.launch(Dispatchers.IO) {

            getTopData(passport, token)
            if (videoType == Constant.DATA_TYPE_VRS) {
                val vrs = _repositoryVideo.requestVrsAlbum(aid, passport, gid)
                    .execute(retainValue = VideoDetailState::vrsAlbumInfo) {
                        copy(vrsAlbumInfo = it, videoType = videoType)
                    }

            } else {
                val pgc = _repositoryVideo.requestPgcAlbum(aid).execute {
                    copy(pgcAlbumInfo = it, videoType = videoType)
                }
            }

        }

    }

    fun getTopData(passport: String?, token: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            val topInfo = userRepository.fetchTopBarData(passport, token).execute {
                copy(topData = it)
            }
        }
    }

    fun requestUserVideoPermission(aid: Int?, vid: Int?) {
        viewModelScope.launch {
            val videoPermissionCheck = userRepository.userVideoPermission(
                aid ?: 0, vid ?: 0
            ).execute {
                copy(videoPermissionCheck = it)
            }
        }
    }


    fun requestVideo(
        videoType: Int,
        aid: Int?,
        vid: Int?,
        isDts: Boolean = false,
        isChangeVideo: Boolean = false
    ) {
        viewModelScope.launch(Dispatchers.Default) {
            if (videoType == Constant.DATA_TYPE_VRS) {
//                try {
                val videoInfo = _repositoryVideo.requestVrsVideo(
                    aid,
                    vid,
                    UserLoginHelper.getInstants().getLoginPassport(),
                    isDts
                ).execute {
                    copy(videoInfoData = it)
                }
            } else {
                //切换剧集PGC时需要重新请求pgcAlbumInfo 获取影片信息
                if (isChangeVideo) {
                    val pgc = _repositoryVideo.requestPgcAlbum(aid).execute {
                        copy(pgcAlbumInfo = it, videoType = videoType)
                    }
                } else {
                    _repositoryVideo.requestPgcVideo(
                        vid,
                        UserLoginHelper.getInstants().getLoginPassport()
                    ).execute {
                        copy(pgcPlayList = it)
                    }
                }
            }
        }
    }

    fun requestDetailRecommendAll(videoType: Int, aid: Int?, vid: Int?) {
        if (videoType == Constant.DATA_TYPE_VRS) {
            viewModelScope.launch(Dispatchers.IO) {
                _repositoryVideo.requestVrsDetailRecommendAll(
                    aid
                ).execute {
                    copy(videoDetailRecommendModel = it)
                }

            }
        } else {
            viewModelScope.launch(Dispatchers.IO) {
                _repositoryVideo.requestPgcDetailRecommendAll(
                    aid
                ).execute {
                    copy(videoDetailRecommendModel = it)
                }
            }
        }

    }


    fun requestPayInfo(
        isLogin: Boolean = false, isVip: Boolean = false, cateCode: Int, aid: Int? = 0,
        vid: Int? = 0, mUserLoginToken: String = "",
        passport: String = ""
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val payInfo = _repositoryPay.requestPayInfo(
                aid ?: 0,
                vid ?: 0,
                cateCode,
                isLogin,
                mUserLoginToken,
                passport
            ).execute {
                copy(videoDetailFilmCommodities = it)
            }
        }
    }


    fun edUserPermission(aid: Int?, vid: Int?) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.userVideoPermission(aid ?: 0, vid ?: 0).execute {
                copy(edVideoPermissionCheck = it)
            }
        }
    }

    fun loadEducationPrivilegeInfo(aid: Int?, vid: Int?) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.loadEducationPrivilegeInfo(aid ?: 0, vid ?: 0).execute {
                copy(educationPrivilege = it)
            }
        }
    }


    // 获取专辑追剧状态
    fun loadChaseStatus(cateCode: Int, aid: Int?) {
        /**
         * 教育专辑获取收藏状态
         */
        viewModelScope.launch(Dispatchers.IO) {
            if (cateCode == Constant.EDU_CATE_CODE) {
                _repositoryCollect.edCollect(aid ?: 0).execute {
                    val collectData = it.invoke()?.isData ?: false
                    copy(enableCollect = collectData)
                }
            } else {
                _repositoryCollect.collect(aid ?: 0).execute {
                    val collectData = it.invoke()?.data
                    copy(enableCollect = collectData == 1)
                }
            }
        }
    }

    /**
     * 获取片花剧集信息
     */
    fun getNextTrailerEpisodeInfo(trailerId: Int) {
        viewModelScope.launch(Dispatchers.Main) {
            val response = _repositoryVideo.getVrsEpisodeVideos(trailerId, 0, 1, 30).await()
            val episodeVideos = VrsEpisodeVideos.vrsConvert2Videos(response.data)
            setState {
                copy(trailerEpisodeVideos = episodeVideos)
            }


        }

    }

    fun southMediaCheckPermission(datatype: Int, feeType: Int, vid: Int?, aid: Int?) {
        viewModelScope.launch {
            delay(3000)
            val mGson = GsonBuilder().disableHtmlEscaping().create()
            val passport = UserLoginHelper.getInstants().getLoginPassport()
            val params = mutableMapOf<String, Any>()
            val userId = getGid()
            val account = "SNM_$userId"
            var channel = MetaDataHelper.getPartnerNo()
            if (TextUtils.isEmpty(channel)) {
                channel = ""
            }
            var mac = getUserDeviceMacAddress()
            mac = if (TextUtils.isEmpty(mac)) {
                ""
            } else {
                mac!!.replace("%3A", ":")
            }

            val appVersionName = DeviceUtils.getVersionName()
            val ip = DeviceUtils.getIpv4Addr()
            val AES_IV = "adjiganaadjigana"

            params["source"] = "snm_sohu"
            params["mac"] = EncryUtils.AesDesEncrypt(
                mac,
                UserApp.Config.SNM_AES_SECRET,
                AES_IV,
                EncryUtils.Encryption.AES,
                EncryUtils.EncryptMode.ECB
            )
            params["wifimac"] = EncryUtils.AesDesEncrypt(
                mac,
                UserApp.Config.SNM_AES_SECRET,
                AES_IV,
                EncryUtils.Encryption.AES,
                EncryUtils.EncryptMode.ECB
            )
            params["clientip"] = EncryUtils.AesDesEncrypt(
                ip,
                UserApp.Config.SNM_AES_SECRET,
                AES_IV,
                EncryUtils.Encryption.AES,
                EncryUtils.EncryptMode.ECB
            )
            params["guid"] = userId!!
            params["snmaccount"] = account
            params["loginname"] = EncryUtils.AesDesEncrypt(
                passport,
                UserApp.Config.SNM_AES_SECRET,
                AES_IV,
                EncryUtils.Encryption.AES,
                EncryUtils.EncryptMode.ECB
            )
            params["logintype"] = if ("" == passport) "" else Util.getSouthMediaLoginType(
                UserLoginHelper.getInstants().getUtype()
            ).toString()
            params["vid"] = if (datatype == 0) "VRS$vid" else "PGC$vid"
            params["cid"] = if (datatype == 0) "VRS$aid" else "PGC$aid"
            params["videotype"] = "1"
            params["feetype"] = feeType
            params["authresult"] = 1
            params["playtype"] = 1
            params["qua"] = "PT=SNMYT&CHID=$channel&APP_VER=$appVersionName"
            SNMOTTClient.getInstance().getData(
                "pauth",
                mGson.toJson(params),
                object : SNMOTTSDKCallBack {
                    override fun onSuccess(s: String?) {
                        val value = mGson.fromJson(s, SouthMediaCheckResult::class.java)
                        setState {
                            if (TextUtils.equals(value.returncode, "998")) {
                                copy(southMediaCheckResult = Success(SOUTHMEDIA_CHECK_FAIL))
                            } else {
                                copy(southMediaCheckResult = Success(SOUTHMEDIA_CHECK_SUCCESS))
                            }
                        }
                    }

                    override fun onFailure(s: String?) {
                        setState {
                            copy(southMediaCheckResult = Success(SOUTHMEDIA_CHECK_SUCCESS))
                        }
                    }

                })

        }
    }

    /**
     * 获取下一集剧集信息
     */
    fun getNextEpisodeInfo(
        aid: Int?,
        vid: Int?,
        videoType: Int,
        videoOrder: Int,
        isTrailer: Boolean,
        tvCateCode: Int,
        nextEpisodeVideoOrder: Int,
        episodeSortOrder: Int,
        pageSize: Int,
        episodeTotalCount: Int
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            if (nextEpisodeVideoOrder == -1) {
                setState {
                    copy(nextEpisodeInfo = mutableListOf(aid ?: -1))
                }
                return@launch
            }
            if (videoType == Constant.DATA_TYPE_VRS) {
                try {
                    val page: Int
                    val sortOrder: Int = episodeSortOrder
                    val pageSize: Int = 50
                    page = if (sortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
                        if (nextEpisodeVideoOrder % pageSize == 0) {
                            nextEpisodeVideoOrder / pageSize
                        } else {
                            nextEpisodeVideoOrder / pageSize + 1
                        }
                    } else {
                        (episodeTotalCount - nextEpisodeVideoOrder) / pageSize + 1
                    }
                    val response = _repositoryVideo.getVrsEpisodeVideos(
                        aid,
                        if (isTrailer) 0 else sortOrder,
                        page,
                        50
                    ).await()
                    val episodeVideos = VrsEpisodeVideos.vrsConvert2Videos(response.data)
                    if (episodeVideos.videos != null) {
                        val mNextEpisodeInfo = mutableListOf<Any>()
                        var index = -1
                        if (videoOrder != -1) {
                            for (videoIndex in episodeVideos.videos.indices) {
                                if (vid == episodeVideos.videos.get(videoIndex).tvVerId && episodeVideos.videos.size > videoIndex + 1) {
                                    index = videoIndex
                                    if (episodeVideos.videos[videoIndex + 1].tvVerId == 0) {
                                        mNextEpisodeInfo.add(-1)
                                    } else {
                                        mNextEpisodeInfo.add(episodeVideos.videos[videoIndex + 1].id)
                                        mNextEpisodeInfo.add(episodeVideos.videos[videoIndex + 1].tvVerId)
                                        mNextEpisodeInfo.add(videoType)
                                        mNextEpisodeInfo.add(videoIndex + 1)
                                        mNextEpisodeInfo.add(episodeVideos.videos[videoIndex + 1].tvName)
                                        break
                                    }
                                }

                            }
                            if (index == -1 && episodeVideos.videos.size > 0) {
                                mNextEpisodeInfo.add(episodeVideos.videos[0].id)
                                mNextEpisodeInfo.add(episodeVideos.videos[0].tvVerId)
                                mNextEpisodeInfo.add(videoType)
                                mNextEpisodeInfo.add(0)
                                mNextEpisodeInfo.add(episodeVideos.videos[0].tvName)
                            }
                            setState {
                                copy(nextEpisodeInfo = mNextEpisodeInfo)
                            }
                        } else {
                            setState {
                                copy(nextEpisodeInfo = mutableListOf(aid ?: -1))
                            }
                        }
                    } else {
                        setState {
                            copy(nextEpisodeInfo = mutableListOf(aid ?: -1))
                        }
                    }
                } catch (e: Throwable) {
                    LibDeprecatedLogger.d("getNextEpisodeInfo Throwable: ${e.localizedMessage.toString()}")
                }
            } else {
                var page: Int =
                    (episodeTotalCount - nextEpisodeVideoOrder) / EpisodeLayoutNew.PGC_PAGE_SIZE + 1
                //add zhangyi 搜狐课堂按升序，其他pgc按降序。
                var sortType = 0
                if (tvCateCode == CornerTagImageView.CORNER_TYPE_SOHUCLASS) {
                    sortType = 1
                    page = (nextEpisodeVideoOrder - 1) / EpisodeLayoutNew.PGC_PAGE_SIZE + 1
                }
                val response = _repositoryVideo.getPgcEpisodeVideos(
                    aid ?: 0, sortType,
                    page,
                    EpisodeLayoutNew.PGC_PAGE_SIZE
                ).await()
                val episodeVideos = PgcEpisodeVideos.pgcConvert2Videos(response.data)
                if (episodeVideos.videos != null) {
                    val mNextEpisodeInfo = mutableListOf<Any>()
                    val index: Int
                    val videoOrder: Int = nextEpisodeVideoOrder
                    val sortOrder: Int = episodeSortOrder
                    val pageSize: Int = pageSize
                    if (videoOrder != -1) {
                        index = if (sortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
                            (videoOrder - 1) % pageSize
                        } else {
                            (episodeTotalCount - videoOrder) % pageSize
                        }
                        if (index < episodeVideos.videos.size) {
                            mNextEpisodeInfo.add(episodeVideos.videos[index].id)
                            mNextEpisodeInfo.add(episodeVideos.videos[index].tvVerId)
                            mNextEpisodeInfo.add(videoType)
                            mNextEpisodeInfo.add(episodeVideos.videos[index].videoOrder)
                            mNextEpisodeInfo.add(episodeVideos.videos[index].tvSubName)
                        }
                        setState {
                            copy(nextEpisodeInfo = mNextEpisodeInfo)
                        }
                    } else {
                        LibDeprecatedLogger.d("getNextEpisodeInfo in afterGetPgcData return for out of episode video index")
                        mNextEpisodeInfo.add(aid ?: 0)
                        setState {
                            copy(nextEpisodeInfo = mNextEpisodeInfo)
                        }
                    }
                } else {
                    setState {
                        copy(nextEpisodeInfo = mutableListOf(aid ?: -1))
                    }
                }
            }

        }

    }

    fun loadIphoneMKey(
        passport: String?,
        authToken: String?,
        aid: Int,
        vid: Int,
        apikey: String?,
        uid: String?,
        appVersion: String?,
        gid: String?,
        app_id: String?,
        plat: String?,
        appId: String?,
        ua: String?
    ) {
        viewModelScope.launch {
            val videoPermissionCheck = _repositoryVideo.loadIphoneMKey(
                passport, authToken, aid, vid, apikey, uid, appVersion, gid, app_id, plat, appId, ua
            ).execute {
                copy(videoPermissionCheck = it)
            }
        }
    }


    companion object : MavericksViewModelFactory<VideoDetailMviViewModel, VideoDetailState> {
        override fun create(
            viewModelContext: ViewModelContext,
            state: VideoDetailState
        ): VideoDetailMviViewModel {
            return VideoDetailMviViewModel(
                initializerState = state,
                userRepository = UserRepository()
            )

        }
    }
}