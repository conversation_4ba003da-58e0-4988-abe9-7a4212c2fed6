package com.sohuott.tv.vod.activity;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.BitmapFactory;
import android.graphics.drawable.StateListDrawable;
import android.os.Bundle;

import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RoundedDrawable;
import com.sohuott.tv.vod.fragment.WelfareActivityDetailFragment;
import com.sohuott.tv.vod.fragment.WelfareActivityListFragment;
import com.sohuott.tv.vod.fragment.WelfareMyExchangeFragment;
import com.lib_statistical.manager.LogEventModel;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.lib.widgets.BaseLinearLayout;
import com.sohuott.tv.vod.lib.widgets.IFocusSearchLayout;
import com.sohuott.tv.vod.lib.widgets.OnBaseLayoutFocusSearchListener;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.GlideImageView;

import static android.view.KeyEvent.KEYCODE_ESCAPE;

import androidx.core.content.res.ResourcesCompat;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.FragmentTransaction;

/**
 * 新版积分商城(v6.5)
 *
 * <AUTHOR>
 * @date 2017/12/21
 */
public class WelfareActivity extends BaseFragmentActivity implements
        WelfareActivityDetailFragment.OnProductClickListener, View.OnClickListener
        , OnBaseLayoutFocusSearchListener, SharedPreferences.OnSharedPreferenceChangeListener {


    private RelativeLayout mLeftLayout;
    private GlideImageView mUserAvatarFiv;
    private TextView mUseNameTv;
    private TextView mUserRankTv;
    private TextView mTotalScoreTv;
    private TextView mRecordsTv;
    private TextView mGetScoreTv;
    private ImageView mScoreTipAnimView;
    private ImageView mUserAvatarFocusView;
    private LoginUserInformationHelper mUserHelper;

    private WelfareActivityListFragment mActivityListFragment;
    private WelfareMyExchangeFragment mMyExchangeFragment;
    private WelfareActivityDetailFragment mActivityDetailFragment;

    public static final String DETAIL_TAG_LIST = "listDetail";
    public static final String DETAIL_TAG_HISTORY = "historyDetail";
    private SharedPreferences mUserSp;
    private boolean mIsFirst = true;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_welfare);
        getWindow().setBackgroundDrawable(null);
        initView();
        mUserHelper = LoginUserInformationHelper.getHelper(this);
        mTotalScoreTv.requestFocus();
        mUserAvatarFiv.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mUserAvatarFocusView.setVisibility(hasFocus ? View.VISIBLE : View.INVISIBLE);
            }
        });
        mUserAvatarFiv.setOnClickListener(this);
        mGetScoreTv.setOnClickListener(this);
        if (Util.isSupportTouchVersion(this)) {
            mTotalScoreTv.setOnClickListener(this);
            mRecordsTv.setOnClickListener(this);
        }
        mActivityListFragment = new WelfareActivityListFragment();
        mActivityListFragment.setOnProductClickListener(this);
        getSupportFragmentManager().beginTransaction()
                .replace(R.id.layout_content, mActivityListFragment).commitAllowingStateLoss();
        mUserSp = getSharedPreferences(LoginUserInformationHelper.LOGIN_USER_INFORMATION, MODE_PRIVATE);
        mUserSp.registerOnSharedPreferenceChangeListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mUserHelper.getIsLogin()) {
            mUserAvatarFiv.setCircleImageRes(mUserHelper.getLoginPhoto(), getResources().getDrawable(R.drawable.welfare_default_avatar)
                    , getResources().getDrawable(R.drawable.welfare_default_avatar));
            mUseNameTv.setText(mUserHelper.getNickName());
            if (!mIsFirst) {
                mActivityListFragment.refreshList(true);
            }
            mUserRankTv.setText("NO." + mUserHelper.getUserLikeRank());
            mTotalScoreTv.setText("" + mUserHelper.getTotalScore() + "分");
        } else {
            mUserAvatarFiv.setCircleImageRes(R.drawable.welfare_default_avatar);
            mUseNameTv.setText("请登录");
            mUserRankTv.setText("NO.---");
            mTotalScoreTv.setText("---");
        }
        mIsFirst = false;
        //login in MyExchangeFragment
        if (mRecordsTv.isSelected()) {
            if (mMyExchangeFragment != null
                    && mMyExchangeFragment.isVisible()
                    && mMyExchangeFragment.getFocusViewFromLeftLayout() == null) {
                mRecordsTv.requestFocus();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mUserSp.unregisterOnSharedPreferenceChangeListener(this);
    }

    @Override
    public View onCreateView(View parent, String name, Context context, AttributeSet attrs) {
        if (parent != null && parent.getId() == android.R.id.content) {
            if (name.equals("LinearLayout")) {
                View rootView = new BaseLinearLayout(context, attrs);
                ((IFocusSearchLayout) rootView).registerBaseLayoutFocusSearchListener(this);
                return rootView;
            }
        }
        return super.onCreateView(parent, name, context, attrs);
    }

    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
        if (mUserHelper.getIsLogin()) {
            if (key.equals(LoginUserInformationHelper.KEY_TOTAL_SCORE)) {
                mTotalScoreTv.setText("" + mUserHelper.getTotalScore() + "分");
            } else if (key.equals(LoginUserInformationHelper.KEY_LIKE_RANK)) {
                mUserRankTv.setText("NO." + mUserHelper.getUserLikeRank());
            }
        } else if (mMyExchangeFragment != null) {
            mMyExchangeFragment.clearData();
        }

    }

    @Override
    public View onBaseLayoutFocusSearch(View currentFocused, int direction, View nextFocused) {
        if (currentFocused != null) {
            if (direction == View.FOCUS_RIGHT) {
                if ((currentFocused.getId() == R.id.records_tv
                        || currentFocused.getId() == R.id.get_score_tv)
                        && mMyExchangeFragment != null && mMyExchangeFragment.isVisible()) {
                    View next = mMyExchangeFragment.getFocusViewFromLeftLayout();
                    if (null != next) {
                        setCurrentSelect(mRecordsTv);
                    }
                    return next == null ? currentFocused : next;
                } else if (currentFocused.getId() == R.id.total_score_tv
                        && mActivityListFragment != null && mActivityListFragment.isVisible()) {
                    View next = mActivityListFragment.getFocusViewFromLeftLayout();
                    return next == null ? currentFocused : next;
                }
            } else if (direction == View.FOCUS_UP
                    && mMyExchangeFragment != null && mMyExchangeFragment.isVisible()) {
                if (mMyExchangeFragment.getView().hasFocus()) {
                    return currentFocused;
                }
            }

        }
        return null;
    }


    private void initView() {
        mLeftLayout = (RelativeLayout) findViewById(R.id.layout_left);
        mUserAvatarFiv = (GlideImageView) findViewById(R.id.user_avatar_fiv);
        mUserAvatarFocusView = (ImageView) findViewById(R.id.user_avatar_focus_iv);
        mUseNameTv = (TextView) findViewById(R.id.use_name_tv);
        mUserRankTv = (TextView) findViewById(R.id.user_rank_tv);
        mTotalScoreTv = (TextView) findViewById(R.id.total_score_tv);
        mRecordsTv = (TextView) findViewById(R.id.records_tv);
        mScoreTipAnimView = (ImageView) findViewById(R.id.tip_score);
        mGetScoreTv = (TextView) findViewById(R.id.get_score_tv);
        mTotalScoreTv.setSelected(true);
        RoundedDrawable focusDrawable = new RoundedDrawable(BitmapFactory.decodeResource(getResources(), R.drawable.welfare_btn_focus_bg));
        focusDrawable.setBorderColor(ResourcesCompat.getColor(getResources(), R.color.common_yellow, null));
        focusDrawable.setBorderWidth(getResources().getDimension(R.dimen.x3));
        focusDrawable.setCornerRadius(getResources().getDimension(R.dimen.x40));
        focusDrawable.setScaleType(ImageView.ScaleType.FIT_XY);
        StateListDrawable stateListDrawable = new StateListDrawable();
        stateListDrawable.addState(new int[]{android.R.attr.state_focused}, focusDrawable);
        stateListDrawable.addState(new int[]{}, ResourcesCompat.getDrawable(getResources(), R.drawable.welfare_get_score_bg, null));
        ViewCompat.setBackground(mGetScoreTv, stateListDrawable);
    }

    @Override
    public void onClick(View v) {

        if (v.getId() == R.id.user_avatar_fiv) {
            if (mUserHelper.getIsLogin()) {
                ActivityLauncher.startListUserRelatedActivity(this, ListUserRelatedActivity.LIST_INDEX_MY);
            } else {
                ActivityLauncher.startLoginActivity(this);
            }
            RequestManager.onEvent(new LogEventModel("6_welfare", "6_welfare_btn_avatar"));
        } else if (v.getId() == R.id.get_score_tv) {
            ActivityLauncher.startListUserRelatedActivity(this, ListUserRelatedActivity.LIST_INDEX_POINT);
            RequestManager.onEvent(new LogEventModel("6_welfare", "6_welfare_btn_get_score"));
        } else {
            //in touch mode
            setCurrentSelect(v);
            changeFragment(v.getId() == R.id.total_score_tv);
        }
    }


    @Override
    public void onProductClick(int activityId, boolean isHistory) {
        mActivityDetailFragment = WelfareActivityDetailFragment.newInstance(activityId);
        FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
        if (isHistory) {
            fragmentTransaction.hide(mMyExchangeFragment);
        } else {
            fragmentTransaction.hide(mActivityListFragment);
        }
        fragmentTransaction.setCustomAnimations(android.R.anim.fade_in, android.R.anim.fade_out);
        fragmentTransaction.add(R.id.layout_content, mActivityDetailFragment, isHistory ? DETAIL_TAG_HISTORY : DETAIL_TAG_LIST);
        fragmentTransaction.addToBackStack(null);
        fragmentTransaction.commitAllowingStateLoss();
    }

    @Override
    public void onShowScoreTip() {
        mScoreTipAnimView.setVisibility(View.VISIBLE);
        mScoreTipAnimView.startAnimation(AnimationUtils.loadAnimation(this, R.anim.score_tip));
    }

    long mLastKeyTime;

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        final View focusView = getCurrentFocus();
        int focusId=focusView!=null?focusView.getId():0;
        if (mLastKeyTime != 0 && (System.currentTimeMillis() - mLastKeyTime) < 300) {
            return true;
        }
        mLastKeyTime = System.currentTimeMillis();
        if (mMyExchangeFragment != null && mMyExchangeFragment.isVisible()) {
            mMyExchangeFragment.resetDescendantFocus();
        }
        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN
                || keyCode == KeyEvent.KEYCODE_DPAD_UP) {
            boolean showMyExchange = (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && focusId == R.id.total_score_tv)
                    || (keyCode == KeyEvent.KEYCODE_DPAD_UP && focusId == R.id.get_score_tv);
            final boolean showList = (keyCode == KeyEvent.KEYCODE_DPAD_UP && focusId == R.id.records_tv);
            if (showMyExchange) {
                setCurrentSelect(mRecordsTv);
                changeFragment(false);
            } else if (showList) {
                setCurrentSelect(mTotalScoreTv);
                changeFragment(true);
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                    if (focusId == R.id.user_avatar_fiv) {
                        if (mActivityListFragment.isHidden()) {
                            changeFragment(true);
                        }
                    } else if (focusId == R.id.records_tv) {
                        mScoreTipAnimView.clearAnimation();
                        mScoreTipAnimView.setVisibility(View.GONE);
                        setCurrentSelect(mGetScoreTv);
                    }
            }
            if (focusView != null && mMyExchangeFragment != null
                    && mMyExchangeFragment.isVisible()
                    && mMyExchangeFragment.getView().hasFocus()) {
                mMyExchangeFragment.isScrollToTopOrBottom(focusView, keyCode == KeyEvent.KEYCODE_DPAD_UP);
            }
            if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                if (focusView != null && mActivityListFragment != null
                        && mActivityListFragment.isVisible()
                        && mActivityListFragment.getView().hasFocus()) {
                    mActivityListFragment.scrollUp(focusView);
                }
            }

        } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
            if (focusView != null) {
                if (mActivityListFragment.isVisible()) {
                    if (focusView.getId() == R.id.item_welfare
                            && mActivityListFragment.isOnLeft(getCurrentFocus())) {
                        mTotalScoreTv.requestFocus();
                        return true;
                    }
                } else if (focusView.getId() == R.id.tv_exchange) {
                    //detailFragment exchangeBtn
                    if (mActivityDetailFragment.getTag().equals(DETAIL_TAG_LIST)) {
                        mTotalScoreTv.requestFocus();
                    } else {
                        mRecordsTv.requestFocus();
                    }
                    return true;
                } else if (mMyExchangeFragment != null
                        && mMyExchangeFragment.isVisible()
                        && focusView.getId() == R.id.iv_product) {
                    mRecordsTv.requestFocus();
                }
            }

        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        //handler focus while exit from detail page
        if (keyCode == KeyEvent.KEYCODE_BACK
                || keyCode == KEYCODE_ESCAPE) {
            if (mActivityDetailFragment != null && mActivityDetailFragment.isVisible()) {
                if (mActivityDetailFragment.getTag().equals(DETAIL_TAG_LIST)) {
                    mActivityListFragment.setFocusViewFromKeyBack();
                } else {
                    mMyExchangeFragment.setFocusViewFromKeyBack();
                }
            }
            switch (getSupportFragmentManager().getBackStackEntryCount()) {
                case 0:
                    finish();
                    break;
                default:
                    getSupportFragmentManager().popBackStack();
                    break;
            }
            return true;
        }
        return super.onKeyUp(keyCode, event);
    }

    private void setCurrentSelect(View view) {
        mTotalScoreTv.setSelected(false);
        mGetScoreTv.setSelected(false);
        mRecordsTv.setSelected(false);
        view.setSelected(true);
    }

    private void changeFragment(boolean showList) {
        if (getSupportFragmentManager().getBackStackEntryCount() > 0) {
            getSupportFragmentManager().popBackStack();
        }
        FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
        if (showList) {
            if (mMyExchangeFragment != null) {
                fragmentTransaction.hide(mMyExchangeFragment);
            }
            fragmentTransaction.show(mActivityListFragment);
        } else {
            fragmentTransaction.hide(mActivityListFragment);
            if (mMyExchangeFragment == null) {
                mMyExchangeFragment = new WelfareMyExchangeFragment();
                mMyExchangeFragment.setOnProductClickListener(this);
                fragmentTransaction.add(R.id.layout_content, mMyExchangeFragment);
            } else {
                fragmentTransaction.show(mMyExchangeFragment);
            }
        }
        fragmentTransaction.commitAllowingStateLoss();
    }


    @Override
    public void onBackPressed() {
        return;
//        if (getSupportFragmentManager().getBackStackEntryCount() > 0) {
//            getSupportFragmentManager().popBackStack();
//            return;
//        }
//        super.onBackPressed();
    }

}

