package com.sohuott.tv.vod.fragment.lb;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.ItemBridgeAdapter;
import androidx.leanback.widget.ListRow;
import androidx.leanback.widget.OnChildViewHolderSelectedListener;
import androidx.leanback.widget.Presenter;
import androidx.leanback.widget.PresenterSelector;
import androidx.lifecycle.LifecycleKt;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.sohu.lib_utils.PrefUtil;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.activity.launcher.LauncherPlayerManager;
import com.sohuott.tv.vod.activity.launcher.LauncherViewModel;
import com.sohuott.tv.vod.base.BaseLazyLoadFragment;
import com.sohuott.tv.vod.databinding.FragmentHomeContentBinding;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ComingSoonModel;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.model.Footer;
import com.sohuott.tv.vod.model.Header;
import com.sohuott.tv.vod.model.VipBanner;
import com.sohuott.tv.vod.model.VipUserState;
import com.sohuott.tv.vod.presenter.launcher.TypeComingContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeFourContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeNewFilmPresenterKt;
import com.sohuott.tv.vod.presenter.launcher.TypeOneContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeProducerPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeRecommendContentPresenterKt;
import com.sohuott.tv.vod.presenter.launcher.TypeThreeContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeTwoContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeZeroContentPresenterKt;
import com.sohuott.tv.vod.presenter.launcher.VipBannerPresenter;
import com.sohuott.tv.vod.presenter.launcher.selector.ContentPresenterSelector;
import com.sohuott.tv.vod.presenter.launcher.selector.TypeOnePresenterSelector;
import com.sohuott.tv.vod.presenter.launcher.selector.TypeVipHeaderPresenterSelector;
import com.sohuott.tv.vod.widget.lb.VipBannerView;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by music on 2021/9/1.
 */

public class HomeContentFragment extends BaseLazyLoadFragment {
    private static final String BUNDLE_KEY_POSITION = "bundleKeyPosition";
    public static final String BUNDLE_KEY_TAB_CODE = "bundleKeyTabCode";
    private static final String BUNDLE_KEY_TAB_TYPE = "bundleKeyTabType";

    private LauncherViewModel launcherViewModel;
    private FragmentHomeContentBinding mBinding;

    public static final int REFRESH_DURATION = 1000 * 60 * 30;
    public static final int UPDATE_DURATION = 200;

    private boolean mEverVisible;

    private LauncherActivity mActivity;
    private View mRootView;
    private ArrayObjectAdapter mAdapter;
    private HomeFragmentHandler fragmentHandler;

    private int mCurrentTabPosition;
    private int mCurrentTabCode;
    private int mCurrentTabType;
    private int mSubjectId;

    private boolean mNeedReplaceRecommend_3; //需要替换人工运营的推荐3时，不展示原本的为你推荐内容
    private boolean mHasHistory; //历史行是否已加载，已加载执行replace，不加载执行add

    private boolean mIsShowRecommendVideo;

    private HomeContentFragment.OnFragmentInteractionListener mListener;
    private LoginUserInformationHelper mHelper;

    private HashMap<String, String> historyPathInfo = new HashMap<>();
    private List<ContentGroup.DataBean.ContentsBean.AlbumListBean> recommendList;
    private List<ContentGroup.DataBean.ContentsBean> manualRecommendList;

    private LauncherPlayerManager mLauncherPlayerManager;

    private TypeZeroContentPresenterKt mTypeZeroContentPresenterKt;
    private TypeNewFilmPresenterKt mTypeNewFilmPresenterKt;
    private TypeRecommendContentPresenterKt mTypeRecommendContentPresenterKt;

    private TypeZeroContentPresenterKt.TypeZeroViewHolder mTypeZeroViewHolder;
    private TypeNewFilmPresenterKt.TypeNewFilmViewHolder mTypeNewFilmViewHolder;
    private TypeRecommendContentPresenterKt.TypeRecommendViewHolder mTypeRecommendViewHolder;
    private TypeVipHeaderPresenterSelector mTypeVipHeaderPresenterSelector;


    private static class HomeFragmentHandler extends Handler {
        public static final int UPDATE = 0x1;
        public static final int REFRESH = 0x3;

        private WeakReference<HomeContentFragment> weakReference;

        public HomeFragmentHandler(HomeContentFragment homeContentFragment) {
            weakReference = new WeakReference<HomeContentFragment>(homeContentFragment);
        }

        @Override
        public void handleMessage(Message msg) {
            LibDeprecatedLogger.d("handleMessage, msg.what = " + msg.what);
            HomeContentFragment homeContentFragment = weakReference.get();
            if (homeContentFragment != null) {
                switch (msg.what) {
                    case UPDATE:
                        LibDeprecatedLogger.d("handleMessage: UPDATE");
                        homeContentFragment.launcherViewModel.setCurrentPage(1);
                        homeContentFragment.loadData();
                        break;
                    case REFRESH:
                        LibDeprecatedLogger.d("handleMessage: REFRESH skip = " + (homeContentFragment.mLauncherPlayerManager != null && homeContentFragment.mLauncherPlayerManager.isPlaying()) + " code = " + homeContentFragment.mCurrentTabCode + " launcher code " + homeContentFragment.mActivity.getCurrentTabCode());
                        if (homeContentFragment.mCurrentTabCode != homeContentFragment.mActivity.getCurrentTabCode()) {
                            homeContentFragment.fragmentHandler.removeMessages(REFRESH);
                            return;
                        }

                        if (homeContentFragment.mLauncherPlayerManager != null && !homeContentFragment.mLauncherPlayerManager.isPlaying()) {
                            homeContentFragment.launcherViewModel.setCurrentPage(1);
                            homeContentFragment.loadData();
                        }

                        msg = Message.obtain();
                        msg.what = REFRESH;
                        homeContentFragment.fragmentHandler.sendMessageDelayed(msg, REFRESH_DURATION);
                        break;
                }
            }

        }
    }


    public interface OnFragmentInteractionListener {
        void onFragmentInteraction(Uri uri);
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        LibDeprecatedLogger.d("onAttach");
        if (context instanceof HomeContentFragment.OnFragmentInteractionListener) {
            mListener = (OnFragmentInteractionListener) context;
        } else {
            throw new RuntimeException(context.toString() +
                    "must implement OnFragmentInteractionListener");
        }
        mActivity = (LauncherActivity) context;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LibDeprecatedLogger.d("onCreate");
        Bundle bundle = getArguments();
        if (bundle == null) {
            return;
        }
        mCurrentTabPosition = getArguments().getInt("position");
        mCurrentTabCode = (int)getArguments().getLong("id");
        mCurrentTabType = getArguments().getInt("type");
        mSubjectId = Integer.parseInt(PrefUtil.getString( "config", "coming_id", "0"));
        AppLogger.d(" pos:" + mCurrentTabPosition, " tabCode: " + mCurrentTabCode + " tabType: " + mCurrentTabType);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        LibDeprecatedLogger.d("onCreateView");
        if (mRootView == null) {
            mBinding = FragmentHomeContentBinding.inflate(inflater, container, false);
            mRootView = mBinding.getRoot();
            fragmentHandler = new HomeFragmentHandler(this);
            initView();
            initListener();
        }
        return mRootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        launcherViewModel = new ViewModelProvider(this, new ViewModelProvider.AndroidViewModelFactory(requireActivity().getApplication())).get(LauncherViewModel.class);
        launcherViewModel.getHomeContentData().observe(getViewLifecycleOwner(), contentGroup -> {
            LibDeprecatedLogger.d("observe contentGroup ： " + contentGroup);
            if (contentGroup == null || contentGroup.data == null) {
                setErrorTVVisible();
            } else {
                //是否开启推荐流视频 1：开启 0：关闭
                if (contentGroup.extend != null) {
                    mIsShowRecommendVideo = contentGroup.extend.currentChannel.isPlayPianhua == 1;
                }
                setContent(contentGroup);
                mBinding.pbLoading.setVisibility(View.GONE);
                mBinding.vgContent.setVisibility(View.VISIBLE);
                mBinding.tvError.setVisibility(View.GONE);
            }
            notifyTopBarState(true);
        });

        launcherViewModel.getMoreContentData().observe(getViewLifecycleOwner(), contentsBean -> {
            initPathLog(contentsBean);
            for (int i = 0; i < contentsBean.albumList.size() / 2; i++) {
                addWithTryCatchInTheEnd(createListRow(contentsBean.albumList.subList(i * 2, i * 2 + 2), mTypeRecommendContentPresenterKt, ""));
            }
        });
    }

    public void refreshDataContent(){
        ContentGroup contentGroup = launcherViewModel.getHomeContentData().getValue();
        if (contentGroup == null || contentGroup.data == null) {
            setErrorTVVisible();
        } else {
            setContent(contentGroup);
            mBinding.pbLoading.setVisibility(View.GONE);
            mBinding.vgContent.setVisibility(View.VISIBLE);
        }
        notifyTopBarState(true);
    }

    public void refreshFirstScreenContent() {
        //当topView转场动画展示完毕后，只加载首屏一行数据
        ContentGroup contentGroup = launcherViewModel.getHomeContentData().getValue();
        if (contentGroup == null || contentGroup.data == null) {
            setErrorTVVisible();
        } else {
            mAdapter.clear();
            List<ContentGroup.DataBean> dataBeans = contentGroup.data;
            for (int i = 0; i < dataBeans.size(); i++) {
                ContentGroup.DataBean dataBean = dataBeans.get(i);
                if (dataBean.contents == null) {
                    continue;
                }
                addFirstScreenContent(dataBean);
                break;
            }
            mBinding.pbLoading.setVisibility(View.GONE);
            mBinding.vgContent.setVisibility(View.VISIBLE);
        }
        notifyTopBarState(true);
    }

    @Override
    public void onResume() {
        super.onResume();
        //刷新机制+时间
        updateAll(mHelper.getIsLogin());
        if (mHelper.getIsLogin()) {
            getCommingData();
        }
        LibDeprecatedLogger.d("onResume: mCurrentTabType : " + mCurrentTabType);
        //如果是会员页面，重新开始轮显
        if (mCurrentTabType == Constant.TYPE_VIP) {
            VipBannerPresenter vipBannerPresenter = (VipBannerPresenter) mTypeVipHeaderPresenterSelector.getPresenter(VipBanner.class);
            VipBannerPresenter.ViewHolder viewHolder = vipBannerPresenter.getViewHolder();
            if (viewHolder != null) {
                VipBannerView vipBannerView = (VipBannerView) viewHolder.view;
                vipBannerView.sendIndex();
            }
        }
        //尝试重新播放推荐流视频
        if (mTypeRecommendViewHolder != null && mTypeRecommendViewHolder.getBinding().getRoot().getVisibility() == View.VISIBLE) {
            mTypeRecommendViewHolder.getBinding().playingIcon.setVisibility(View.VISIBLE);
            mTypeRecommendViewHolder.getBinding().playingIcon.showWaveAnimation();
            mTypeRecommendContentPresenterKt.startDelay(mTypeRecommendViewHolder);
        }
    }

    private void updateAll(boolean isLogin) {
        for (int i = 0; i < mAdapter.size(); i++) {
            if (mAdapter.get(i) instanceof ListRow) {
                if (mAdapter.get(i) instanceof ListRow) {
                    ListRow listRow = ((ListRow) mAdapter.get(i));
                    ArrayObjectAdapter listRowAdapter = ((ArrayObjectAdapter) listRow.getAdapter());
                    if (listRowAdapter.size() > 0) {
                        //刷新推荐页面历史view
                        if (listRowAdapter.get(0) instanceof PlayHistory) {
                            if (historyPathInfo != null) {
                                listRowAdapter.replace(0, new PlayHistory(historyPathInfo));
                            }
                        }

                        if (!isLogin) {
                            //未登录，初始化即将上线预约状态
                            if (listRowAdapter.get(0) instanceof ContentGroup.DataBean.ContentsBean.SubjectVideoListBean) {
                                if (mCurrentTabType == Constant.TYPE_RECOMMED) {
                                    listRowAdapter.notifyArrayItemRangeChanged(0, listRowAdapter.size());
                                }
                            }
                        }

                        //刷新会员首屏会员view
                        if (listRowAdapter.get(0) instanceof VipUserState) {
                            if (mCurrentTabType == Constant.TYPE_VIP) {
                                listRowAdapter.notifyArrayItemRangeChanged(0, 1);
                            }
                        }
                    }
                }

            }
        }
    }

    private void getCommingData() {
        if (mCurrentTabType != Constant.TYPE_RECOMMED) {
            return;
        }
        if (mSubjectId == 0) {
            LibDeprecatedLogger.d("未在CMS后台设置即将上线ID");
        }
        NetworkApi.getComingSoonData(UrlWrapper.getComingSoonUrl(mSubjectId, mHelper.getLoginPassport(), mHelper.getLoginToken()),
                new DisposableObserver<ComingSoonModel>() {

                    @Override
                    public void onNext(ComingSoonModel value) {
                        LibDeprecatedLogger.d("loadComingSoonModel(): onNext()");
                        if (value.getData() == null ||
                                value.getData().getResult() == null ||
                                value.getData().getResult().getSubjectInfos() == null ||
                                value.getData().getResult().getSubjectInfos().size() < 1) {
                            LibDeprecatedLogger.d("loadComingSoonModel(): onNext() data is null");
                            return;
                        }
                        List<ContentGroup.DataBean.ContentsBean.SubjectVideoListBean> subjectInfoList =
                                value.getData().getResult().getSubjectInfos();
                        for (int i = 0; i < subjectInfoList.size(); i++) {

                            Gson gson = new Gson();
                            ContentGroup.DataBean.ContentsBean.AlbumListBean albumListBean =
                                    gson.fromJson(subjectInfoList.get(i).parameter,
                                            ContentGroup.DataBean.ContentsBean.AlbumListBean.class);

                            HashMap<String, String> memoInfo = new HashMap<String, String>();
                            memoInfo.put("playlistId", StringUtil.toString(albumListBean.albumId));
                            memoInfo.put("vid", StringUtil.toString(albumListBean.tvVerId));
                            memoInfo.put("catecode", StringUtil.toString(albumListBean.cateCode));

                            subjectInfoList.get(i).memoInfo = memoInfo;

                            HashMap<String, String> pathInfo = new HashMap<String, String>();
                            pathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));

                            subjectInfoList.get(i).pathInfo = pathInfo;
                        }
                        if (subjectInfoList != null && subjectInfoList.size() > 0) {
                            // 刷新即将上线
                            for (int i = 0; i < mAdapter.size(); i++) {
                                if (mAdapter.get(i) instanceof ListRow) {
                                    ListRow listRow = ((ListRow) mAdapter.get(i));
                                    ArrayObjectAdapter listRowAdapter = ((ArrayObjectAdapter) listRow.getAdapter());
                                    if (listRowAdapter.size() > 0) {
                                        if (listRowAdapter.get(0) instanceof ContentGroup.DataBean.ContentsBean.SubjectVideoListBean) {
                                            LibDeprecatedLogger.d("onNext: 111");
                                            listRowAdapter.clear();
                                            listRowAdapter.addAll(0, subjectInfoList);
                                        }
                                        LibDeprecatedLogger.d("onNext: " + listRowAdapter.get(0).toString());
                                    }
                                }
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.e("loadComingSoonModel(): onError()--" + e.getMessage());
                    }

                    @Override
                    public void onComplete() {
                        LibDeprecatedLogger.d("loadComingSoonModel(): onComplete()");
                    }
                });
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mListener = null;
    }

    private void initListener() {
        mBinding.vgContent.addOnScrollListener(onScrollListener);
        mBinding.vgContent.addOnChildViewHolderSelectedListener(onSelectedListener);
        mTypeZeroContentPresenterKt.setOnTypeZeroPlayListener(onTypeZeroPlayListener);
        mTypeNewFilmPresenterKt.setOnTypeZeoPlayListener(onTypeZeroPlayListener);
        mTypeRecommendContentPresenterKt.setOnTypeZeoPlayListener(onTypeZeroPlayListener);
        mBinding.vgContent.addOnChildViewHolderSelectedListener(onChildViewHolderSelectedListener);
    }

    private void initView() {
        mBinding.vgContent.setTabView(mActivity.getHorizontalGridView());
        mBinding.vgContent.setTopViewBar(mActivity.getTopViewBar());
        mBinding.vgContent.setVerticalSpacing(48);
        mBinding.vgContent.setItemAnimator(null);
        mBinding.vgContent.setHasFixedSize(true);
        ContentPresenterSelector presenterSelector = new ContentPresenterSelector();
        mAdapter = new ArrayObjectAdapter(presenterSelector);
        ItemBridgeAdapter itemBridgeAdapter = new ItemBridgeAdapter(mAdapter);
        mBinding.vgContent.setAdapter(itemBridgeAdapter);

        mHelper = LoginUserInformationHelper.getHelper(getContext());
        mTypeZeroContentPresenterKt = new TypeZeroContentPresenterKt(getLifecycle(), LifecycleKt.getCoroutineScope(getViewLifecycleOwner().getLifecycle()));
        mTypeNewFilmPresenterKt = new TypeNewFilmPresenterKt(getLifecycle(), LifecycleKt.getCoroutineScope(getViewLifecycleOwner().getLifecycle()));
        mTypeRecommendContentPresenterKt = new TypeRecommendContentPresenterKt(getLifecycle(), LifecycleKt.getCoroutineScope(getViewLifecycleOwner().getLifecycle()));
        mTypeVipHeaderPresenterSelector = new TypeVipHeaderPresenterSelector();

    }

    //OnChildViewHolderSelectedListener
    private OnChildViewHolderSelectedListener onChildViewHolderSelectedListener = new OnChildViewHolderSelectedListener() {
        @Override
        public void onChildViewHolderSelected(@NonNull RecyclerView parent, @Nullable RecyclerView.ViewHolder child, int position, int subposition) {
            super.onChildViewHolderSelected(parent, child, position, subposition);
            if (position <= 0) return;
            LibDeprecatedLogger.d("Launcher Child view holder at position " + position + " has been selected" + " Total size = " + mAdapter.size() + " flag = " + (position >= mAdapter.size() - launcherViewModel.getVISIBLE_THRESHOLD()));

            if (position >= mAdapter.size() - launcherViewModel.getVISIBLE_THRESHOLD()) {
                LibDeprecatedLogger.d("Launcher loadMore");
                launcherViewModel.loadMore(mCurrentTabCode);
            }
        }
    };



    private TypeZeroContentPresenterKt.OnTypeZeroPlayListener onTypeZeroPlayListener = new TypeZeroContentPresenterKt.OnTypeZeroPlayListener() {


        @Override
        public void cancelPlay(@NonNull Presenter.ViewHolder viewHolder) {
            if (viewHolder instanceof TypeZeroContentPresenterKt.TypeZeroViewHolder) {
                LibDeprecatedLogger.d("homePlay cancelPlay: TypeZeroViewHolder");
                restoreTypeZeroPlayer((TypeZeroContentPresenterKt.TypeZeroViewHolder) viewHolder);
            } else if (viewHolder instanceof TypeNewFilmPresenterKt.TypeNewFilmViewHolder){
                LibDeprecatedLogger.d("homePlay cancelPlay: TypeNewFilmViewHolder");
                ((TypeNewFilmPresenterKt.TypeNewFilmViewHolder) viewHolder).getBinding().rootPlayer.setVisibility(View.GONE);
                mLauncherPlayerManager.releasePlayer();
            }
        }

        @Override
        public void onTypeZeroStartPlay(@NonNull Presenter.ViewHolder viewHolder, LauncherPlayerManager manager) {
            mLauncherPlayerManager = manager;
            if (viewHolder instanceof TypeZeroContentPresenterKt.TypeZeroViewHolder) {
                LibDeprecatedLogger.d("homePlay onTypeZeroStartPlay: TypeZeroViewHolder");
                mTypeZeroViewHolder = (TypeZeroContentPresenterKt.TypeZeroViewHolder) viewHolder;
            } else if (viewHolder instanceof TypeNewFilmPresenterKt.TypeNewFilmViewHolder){
                LibDeprecatedLogger.d("homePlay onTypeZeroStartPlay: TypeNewFilmViewHolder");
                mTypeNewFilmViewHolder = (TypeNewFilmPresenterKt.TypeNewFilmViewHolder) viewHolder;
            } else if (viewHolder instanceof TypeRecommendContentPresenterKt.TypeRecommendViewHolder) {
                LibDeprecatedLogger.d("homePlay onTypeZeroStartPlay: TypeRecommendContentPresenterKt");
                mTypeRecommendViewHolder = (TypeRecommendContentPresenterKt.TypeRecommendViewHolder) viewHolder;
            }
        }

        @Override
        public void onTypeZeroStopPlay(Presenter.ViewHolder viewHolder) {
            LibDeprecatedLogger.d("homePlay onTypeZeroStopPlay");
            mLauncherPlayerManager.releasePlayer();
        }
    };

    private final RecyclerView.OnScrollListener onScrollListener
            = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (isAdded() && !requireActivity().isDestroyed()) { // 检查有效性
                switch (newState) {
                    //当屏幕滚动且用户使用的触碰或手指还在屏幕上，停止加载图片
                    case RecyclerView.SCROLL_STATE_DRAGGING:
                        //由于用户的操作，屏幕产生惯性滑动，停止加载图片
                    case RecyclerView.SCROLL_STATE_SETTLING:
                        Glide.with(HomeContentFragment.this).pauseRequests();
                        break;
                    case RecyclerView.SCROLL_STATE_IDLE:
                        Glide.with(HomeContentFragment.this).resumeRequests();
                }
            }

        }
    };

    private final OnChildViewHolderSelectedListener onSelectedListener
            = new OnChildViewHolderSelectedListener() {
        @Override
        public void onChildViewHolderSelected(RecyclerView parent,
                                              RecyclerView.ViewHolder child,
                                              int position, int subposition) {
            super.onChildViewHolderSelected(parent, child, position, subposition);
            AppLogger.d("onChildViewHolderSelected: " + subposition
            );

            if (mBinding == null) {
                return;
            }
            AppLogger.d("onChildViewHolderSelected: " + "　isPressUp:" + mBinding.vgContent.isPressUp()
                    + " isPressDown:" + mBinding.vgContent.isPressDown());

            if (mBinding.vgContent.isPressUp() && position == 0) {
                notifyTopBarState(true);
            } else if (mBinding.vgContent.isPressDown() && position == 1) {
                notifyTopBarState(false);
            }
        }
    };

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mBinding == null) return;
        mBinding.vgContent.removeOnScrollListener(onScrollListener);
        mBinding.vgContent.removeOnChildViewHolderSelectedListener(onSelectedListener);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mBinding == null) return;
        mBinding.vgContent.removeOnScrollListener(onScrollListener);
        mBinding.vgContent.removeOnChildViewHolderSelectedListener(onSelectedListener);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        fragmentHandler = new HomeFragmentHandler(this);
        LibDeprecatedLogger.d("setUserVisibleHint: " + isVisibleToUser + ", TabCode : " + mCurrentTabCode + ", CurrentPosition : " + mCurrentTabPosition);

        if (mCurrentTabType == Constant.TYPE_VIP && !isVisibleToUser) {
            VipBannerPresenter vipBannerPresenter = (VipBannerPresenter) mTypeVipHeaderPresenterSelector.getPresenter(VipBanner.class);
            VipBannerPresenter.ViewHolder viewHolder = vipBannerPresenter.getViewHolder();
            if (viewHolder != null) {
                VipBannerView vipBannerView = (VipBannerView) viewHolder.view;
                vipBannerView.releasePlayer();
            }
        }
        if (isVisibleToUser) {
            Message msg = Message.obtain();
            msg.what = HomeFragmentHandler.REFRESH;
            LibDeprecatedLogger.d("setUserVisibleHint true: mHandler.sendMessageDelayed code = " + mCurrentTabCode);
            fragmentHandler.sendMessageDelayed(msg, REFRESH_DURATION);
            if (!mEverVisible ) {
                msg = Message.obtain();
                msg.what = HomeFragmentHandler.UPDATE;
                fragmentHandler.sendMessageDelayed(msg, UPDATE_DURATION);
                mEverVisible = true;
            } else {
                launcherViewModel.setCurrentPage(1);
                loadData();
            }

        } else {
            LibDeprecatedLogger.d("setUserVisibleHint false: mHandler.removeMessages code : " + mCurrentTabCode);
            scrollToTop();
            fragmentHandler.removeMessages(HomeFragmentHandler.UPDATE);
            fragmentHandler.removeMessages(HomeFragmentHandler.REFRESH);
        }
    }

    private void scrollToTop() {
        if (mBinding != null) {
            mBinding.vgContent.scrollToPosition(0);
        }

        if (mActivity != null) {
            if (mActivity.getHorizontalGridView() != null && mActivity.getHorizontalGridView().getVisibility() != View.VISIBLE) {
                mActivity.getHorizontalGridView().setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public void fetchData() {

    }

    private void loadData() {
        launcherViewModel.setEnd(false);
        mNeedReplaceRecommend_3 = false;
        mHasHistory = false;
        mBinding.tvError.setVisibility(View.GONE);
        mBinding.pbLoading.setVisibility(View.VISIBLE);
        mBinding.vgContent.setVisibility(View.INVISIBLE);
        if (mCurrentTabCode == 0) {
            mBinding.pbLoading.setVisibility(View.GONE);
            return;
        }
        launcherViewModel.loadMore(mCurrentTabCode);
    }

    public void setErrorTVVisible() {
        mBinding.pbLoading.hide();
        if (isAdded()) {
            mBinding.tvError.setText(getString(R.string.home_loading_error));
            mBinding.tvError.setVisibility(View.VISIBLE);
            mBinding.vgContent.setVisibility(View.GONE);
        }

    }

    private void setContent(ContentGroup content) {
        mAdapter.clear();
        if (content == null || content.data == null) {
            return;
        }
        AppLogger.d("========HomeFragment setContent start========");
        AppLogger.d("id: " + mCurrentTabCode);
        List<ContentGroup.DataBean> dataBeans = content.data;

        for (int i = 0; i < dataBeans.size(); i++) {
            ContentGroup.DataBean dataBean = dataBeans.get(i);
            if (dataBean.contents == null) {
//                setErrorTVVisible();
//                return;
                continue;
            }

            for (int j = 0; j < dataBean.contents.size(); j++) {
                ContentGroup.DataBean.ContentsBean contentsBean = dataBean.contents.get(j);
                if (contentsBean.albumList == null &&
                        contentsBean.subjectVideoList == null &&
                        contentsBean.pgcVideoList == null &&
                        contentsBean.producersList == null) continue;
                //非首屏数据
                addOtherScreenContent(contentsBean);
                AppLogger.d("OtherScreenContent-> type: " + contentsBean.type);
                if (Objects.equals(contentsBean.type, Constant.TYPE_38) || Objects.equals(contentsBean.type, Constant.TYPE_39)) {
                    //遇到为你推荐，舍去后面的内容，将为你推荐作为列表页中最后一个item，用来分页加载
                    break;
                }
            }
            LibDeprecatedLogger.d("isTopView: " + mActivity.isTopView());
            //首屏数据
            addFirstScreenContent(dataBean);
            if (mActivity.isTopView()) {
                //是topview广告且不是第一次进入launcher，先不展示任何数据
                LibDeprecatedLogger.d("是topview广告且不是第一次进入launcher，先不展示任何数据");
                break;
            }
            AppLogger.d("FirstScreenContent-> type: " + dataBean.type);

        }
        //先不加footer，等到没有更多数据之后再加上
        addFooter();
        AppLogger.d("========HomeFragment setContent end========");
    }

    private void addFirstScreenContent(ContentGroup.DataBean dataBean) {
        initPathLog(dataBean);

        List<ContentGroup.DataBean.ContentsBean> list = dataBean.contents;
        List<ContentGroup.DataBean.ContentsBean> subList;
        switch (dataBean.type) {
            case Constant.TYPE_4://推新
                if (list == null || list.size() < 1) {
                    return;
                }
                if (list.size() > 1) {
                    subList = list.subList(0, 1);
                } else {
                    subList = list;
                }
                addWithTryCatch(createFirstPageListRow(subList, mTypeNewFilmPresenterKt));
                if (list.size() > 0 && !StringUtil.isEmpty(list.get(0).picUrl3)) {
                    if (getActivity() != null) {
                        ((LauncherActivity) getActivity()).setBackground(mCurrentTabCode, list.get(0).picUrl3);
                    }
                }
//                addNewFilm(subList);
                break;
            case Constant.TYPE_7://pgc
            case Constant.TYPE_2:
                // 裁剪 list
                if (list == null || list.size() < 2) {
                    return;
                }
                if (list.size() > 2) {
                    subList = list.subList(0, 2);
                } else {
                    subList = list;
                }
                addWithTryCatch(createFirstPageListRow(subList, mTypeZeroContentPresenterKt));
                break;
            case Constant.TYPE_5://推新
            case Constant.TYPE_8://pgc
            case Constant.TYPE_3://4个横图
                // 裁剪 list
                if (list == null || list.size() < 4) {
                    return;
                }
                if (list.size() > 4) {
                    subList = list.subList(0, 4);
                } else {
                    subList = list;
                }

                addWithTryCatch(createFirstPageListRow(subList, new TypeOneContentPresenter()));
                break;
            case Constant.TYPE_9://pgc
            case Constant.TYPE_6://6个分类
                // 裁剪 list
                if (list == null || list.size() < 6) {
                    return;
                }
                if (list.size() > 6) {
                    subList = list.subList(0, 6);
                } else {
                    subList = list;
                }

                addWithTryCatch(createFirstPageListRow(subList, new TypeFourContentPresenter()));
                break;
            case Constant.TYPE_10://vip4图轮播
                // 裁剪 list
                if (list == null) {
                    return;
                }
                if (list.size() > 4) {
                    subList = list.subList(0, 4);
                } else {
                    subList = list;
                }
                addVipHeader(subList);
                break;
            case Constant.TYPE_12: //皮肤类型
                if (list == null) {
                    return;
                }
                if (!list.isEmpty()) {
                    if (getActivity() != null && !list.get(0).picUrl.isEmpty()) {
                        LibDeprecatedLogger.d(list.get(0).picUrl + " = 皮肤url");
                        ((LauncherActivity) getActivity()).setBackground(mCurrentTabCode, list.get(0).picUrl);
                    }
                }
                break;
            case Constant.TYPE_13:
                if (list == null || list.size() < 3) {
                    return;
                }
                subList = list;
                if (list.size() > 3) {
                    subList = list.subList(0, 3);
                }
                addWithTryCatchReplace(1, createHistoryListRowManual(subList, dataBean.id, new TypeOnePresenterSelector()));
                break;
        }
    }

    private void addOtherScreenContent(ContentGroup.DataBean.ContentsBean contentsBean) {
        initPathLog(contentsBean);
        List<ContentGroup.DataBean.ContentsBean.AlbumListBean> list = contentsBean.albumList;
        List<ContentGroup.DataBean.ContentsBean.AlbumListBean> subList;
        switch (contentsBean.type) {
            // 3大+6小
            case Constant.TYPE_36:
            case Constant.TYPE_30:
            case Constant.TYPE_31:
            case Constant.TYPE_34://推新
                // 剪裁 List
                if (list == null || list.size() < 9) {
                    return;
                }
                subList = list;
                if (list.size() > 3) {
                    subList = list.subList(0, 3);
                }
                addWithTryCatch(createListRow(subList, new TypeTwoContentPresenter(), contentsBean.name));

                if (list.size() > 9) {
                    subList = list.subList(3, 9);
                } else {
                    subList = list.subList(3, list.size());
                }
                addWithTryCatch(createListRow(subList, new TypeThreeContentPresenter(), ""));
                break;
            case Constant.TYPE_40:
                if (contentsBean.producersList == null || contentsBean.producersList.size() < 6) {
                    return;
                }

                addWithTryCatch(createProducersListRow(contentsBean.producersList, new TypeProducerPresenter(), contentsBean.name));
                break;
            case Constant.TYPE_41:
                if (contentsBean.pgcVideoList == null) {
                    return;
                }

                if (contentsBean.pgcVideoList.size() < 4) {
                    return;
                } else if (contentsBean.pgcVideoList.size() >= 4 && contentsBean.pgcVideoList.size() < 8) {
                    addWithTryCatch(createPgcListRow(contentsBean.pgcVideoList.subList(0, 4), new TypeOneContentPresenter(), contentsBean.name));
                } else if (contentsBean.pgcVideoList.size() >= 8) {
                    addWithTryCatch(createPgcListRow(contentsBean.pgcVideoList.subList(0, 4), new TypeOneContentPresenter(), contentsBean.name));
                    addWithTryCatch(createPgcListRow(contentsBean.pgcVideoList.subList(4, contentsBean.pgcVideoList.size()), new TypeOneContentPresenter(), ""));
                }


                break;
            case Constant.TYPE_32: // 即将上线
                // 裁剪list 大于6 舍弃
                if (contentsBean.subjectVideoList == null || contentsBean.subjectVideoList.size() < 6) {
                    return;
                }

                if (contentsBean.subjectVideoList.size() > 6) {
                    contentsBean.subjectVideoList = contentsBean.subjectVideoList.subList(0, 6);
                }

                // 创建list row
                addWithTryCatch(createComingSoonListRow(contentsBean.subjectVideoList, new TypeComingContentPresenter(), contentsBean.name));
                break;
//            case Constant.TYPE_39: // 6小图
//                // 裁剪list 大于6 舍弃
//                if (list == null || list.size() < 6) {
//                    return;
//                }
//
//
//                subList = list;
//                subList.addAll(0, list);
//                if (list.size() > 6) {
//                    subList = list.subList(0, 6);
//                }
//
//                // 创建list row
//                addWithTryCatch(createListRow(subList, new TypeThreeContentPresenter(), contentsBean.name));
//                break;
            case Constant.TYPE_38://为你推荐40
            case Constant.TYPE_39://为你推荐6
                for (int i = 0; i < contentsBean.albumList.size() / 2; i++) {
                    String name = "";
                    if (i == 0) {
                        name = contentsBean.name;
                    }
                    addWithTryCatch(createListRow(list.subList(i * 2, i * 2 + 2), mTypeRecommendContentPresenterKt, name));
                }
                break;
            case Constant.TYPE_33://推新
            case Constant.TYPE_35:
                if (list == null || list.size() < 4) {
                    return;
                }
                subList = list;
                if (list.size() > 4) {
                    subList = list.subList(0, 4);
                }
                addWithTryCatch(createListRow(subList, new TypeOneContentPresenter(), contentsBean.name));

                if (list.size() > 7) {
                    subList = list.subList(4, 8);
                    addWithTryCatch(createListRow(subList, new TypeOneContentPresenter(), ""));
                }

                break;
            case Constant.TYPE_37:
                //1 + 3 -> 历史 + 推荐
                if (list == null || mNeedReplaceRecommend_3) {
                    return;
                }
                subList = list;
                if (list.size() > 3) {
                    subList = list.subList(0, 3);
                }
                addWithTryCatch(1, createHistoryListRow(subList, contentsBean.id, new TypeOnePresenterSelector()));
                break;
        }
    }

    private void initPathLog(ContentGroup.DataBean dataBean) {
        String type = dataBean.type;
        if (dataBean.contents != null && dataBean.contents.size() > 0) {
            for (int i = 0; i < dataBean.contents.size(); i++) {
                if (dataBean.contents.get(i) == null) return;
                ContentGroup.DataBean.ContentsBean contentsBean = dataBean.contents.get(i);
                AppLogger.v("HomeContentFragment First Page InitPathLog: mCurrentTabCode ->" + mCurrentTabCode + ", contentsBean.id -> " + dataBean.id + ",i -> " + i);


                Gson gson = new Gson();
                HomeRecommendBean.Data.Content.Parameter parameter = gson.fromJson(contentsBean.parameter,
                        HomeRecommendBean.Data.Content.Parameter.class);
                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));
                pathInfo.put("columnId", StringUtil.toString(dataBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                contentsBean.pathInfo = pathInfo;

                if (type.equals(Constant.TYPE_6) ||
                        type.equals(Constant.TYPE_9)) {
                    if (contentsBean.ottCategoryId != null) {
                        HashMap<String, String> memoInfo = new HashMap<>();
                        memoInfo.put("collectionId", contentsBean.ottCategoryId);
                        contentsBean.memoInfo = memoInfo;
                    }
                } else if (type.equals(Constant.TYPE_2)) {
                    //首页两大图，需要判断是否为动态视频类型，增加memoInfo
                    String ctype;
                    if (contentsBean.parameterPianhua != null && !contentsBean.parameterPianhua.isEmpty()) {
                        ctype = "1";
                    } else {
                        ctype = "0";
                    }

                    HashMap<String, String> memoInfo = new HashMap<>();
                    memoInfo.put("ctype", ctype);
                    contentsBean.memoInfo = memoInfo;
                }

                if (parameter == null) continue;

                HashMap<String, String> objectInfo = new HashMap<String, String>();
                objectInfo.put("type", "视频");
                objectInfo.put("vid", parameter.tvVerId);
                objectInfo.put("playlistid", parameter.albumId);

                contentsBean.objectInfo = objectInfo;

                contentsBean.channelType = mCurrentTabType;
            }
        }
    }

    private void initPathLog(ContentGroup.DataBean.ContentsBean contentsBean) {
        if (contentsBean.albumList != null) {
            for (int i = 0; i < contentsBean.albumList.size(); i++) {
                if (contentsBean.albumList.get(i) == null) return;
                ContentGroup.DataBean.ContentsBean.AlbumListBean albumListBean = contentsBean.albumList.get(i);
                //是否开启推荐流视频
                if (!mIsShowRecommendVideo) albumListBean.goodTrailerId = 0;
                AppLogger.v("HomeContentFragment initPathLog: mCurrentTabCode ->" + mCurrentTabCode + ", contentsBean.id -> " + contentsBean.id + ",i -> " + i);

                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));
                pathInfo.put("columnId", StringUtil.toString(contentsBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                albumListBean.pathInfo = pathInfo;

                HashMap<String, String> objectInfo = new HashMap<String, String>();
                objectInfo.put("type", "视频");
                objectInfo.put("vid", StringUtil.toString(albumListBean.tvVerId));
                objectInfo.put("playlistid", StringUtil.toString(albumListBean.id));

                albumListBean.objectInfo = objectInfo;


                if (!StringUtil.isEmpty(albumListBean.pdna)) {
                    HashMap<String, String> memoInfo = new HashMap<String, String>();
                    memoInfo.put("pdna", albumListBean.pdna);
                    albumListBean.memoInfo = memoInfo;
                }

                albumListBean.channelType = mCurrentTabType;
            }
        } else if (contentsBean.producersList != null) {
            for (int i = 0; i < contentsBean.producersList.size(); i++) {
                if (contentsBean.producersList.get(i) == null) return;
                ContentGroup.DataBean.ContentsBean.ProducersListBean producerBean = contentsBean.producersList.get(i);

                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));
                pathInfo.put("columnId", StringUtil.toString(contentsBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                producerBean.pathInfo = pathInfo;

                HashMap<String, String> objectInfo = new HashMap<String, String>();
                objectInfo.put("type", "user");
                objectInfo.put("id", StringUtil.toString(producerBean.uid));

                producerBean.objectInfo = objectInfo;
            }
        } else if (contentsBean.subjectVideoList != null) {
            for (int i = 0; i < contentsBean.subjectVideoList.size(); i++) {
                if (contentsBean.subjectVideoList.get(i) == null) return;
                ContentGroup.DataBean.ContentsBean.SubjectVideoListBean subjectBean = contentsBean.subjectVideoList.get(i);
                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));
                pathInfo.put("columnId", StringUtil.toString(contentsBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                subjectBean.pathInfo = pathInfo;

                Gson gson = new Gson();
                ContentGroup.DataBean.ContentsBean.AlbumListBean albumListBean =
                        gson.fromJson(subjectBean.parameter,
                                ContentGroup.DataBean.ContentsBean.AlbumListBean.class);

                HashMap<String, String> memoInfo = new HashMap<String, String>();
                memoInfo.put("playlistId", StringUtil.toString(albumListBean.albumId));
                memoInfo.put("vid", StringUtil.toString(albumListBean.tvVerId));
                memoInfo.put("catecode", StringUtil.toString(albumListBean.cateCode));

                subjectBean.memoInfo = memoInfo;
            }
        } else if (contentsBean.pgcVideoList != null) {
            for (int i = 0; i < contentsBean.pgcVideoList.size(); i++) {
                if (contentsBean.pgcVideoList.get(i) == null) return;
                PgcAlbumInfo.DataEntity dataEntity = contentsBean.pgcVideoList.get(i);

                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));
                pathInfo.put("columnId", StringUtil.toString(contentsBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                dataEntity.pathInfo = pathInfo;

                HashMap<String, String> objectInfo = new HashMap<String, String>();
                objectInfo.put("playlistId", StringUtil.toString(dataEntity.playListId));
                objectInfo.put("vid", StringUtil.toString(dataEntity.videoId));
                objectInfo.put("type", "视频");

                dataEntity.objectInfo = objectInfo;
            }
        }
    }

    private ListRow createFirstPageListRow(List<ContentGroup.DataBean.ContentsBean> list, Presenter presenter) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        return new ListRow(arrayObjectAdapter);
    }

    private ListRow createHistoryListRowManual(List<ContentGroup.DataBean.ContentsBean> list, int columnId, PresenterSelector selector) {

        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(selector);

        manualRecommendList = list;

        historyPathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));
        historyPathInfo.put("columnId", StringUtil.toString(columnId));

        arrayObjectAdapter.add(new PlayHistory(historyPathInfo));
        arrayObjectAdapter.addAll(1, list);

        return new ListRow(arrayObjectAdapter);
    }

    private ListRow createHistoryListRow(List<ContentGroup.DataBean.ContentsBean.AlbumListBean> list, int columnId, PresenterSelector selector) {

        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(selector);

        recommendList = list;

        historyPathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));
        historyPathInfo.put("columnId", StringUtil.toString(columnId));

        arrayObjectAdapter.add(new PlayHistory(historyPathInfo));
        arrayObjectAdapter.addAll(1, list);

        return new ListRow(arrayObjectAdapter);
    }

    private ListRow createListRow(List<ContentGroup.DataBean.ContentsBean.AlbumListBean> list, Presenter presenter, String title) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        ListRow listRow;
        if (!title.isEmpty()) {
            addHeader(title);
        }
        listRow = new ListRow(arrayObjectAdapter);
        return listRow;
    }

    private ListRow createPgcListRow(List<PgcAlbumInfo.DataEntity> list, Presenter presenter, String title) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        ListRow listRow;
        if (!title.isEmpty()) {
            addHeader(title);
        }
        listRow = new ListRow(arrayObjectAdapter);
        return listRow;
    }

    private ListRow createProducersListRow(List<ContentGroup.DataBean.ContentsBean.ProducersListBean> list, Presenter presenter, String title) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        ListRow listRow;
        if (!title.isEmpty()) {
            addHeader(title);
        }
        listRow = new ListRow(arrayObjectAdapter);
        return listRow;
    }


    private ListRow createComingSoonListRow(List<ContentGroup.DataBean.ContentsBean.SubjectVideoListBean> list, Presenter presenter, String title) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        ListRow listRow;
        if (!title.isEmpty()) {
            addHeader(title);
        }
        listRow = new ListRow(arrayObjectAdapter);
        return listRow;

    }

    private void addFooter() {
        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", StringUtil.toString(mCurrentTabCode));
        addWithTryCatch(new Footer(pathInfo, mCurrentTabType));
    }

    private void addHeader(String title) {
        addWithTryCatch(new Header(title, mCurrentTabType));
    }

    private void addVipHeader(List<ContentGroup.DataBean.ContentsBean> list) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(mTypeVipHeaderPresenterSelector);

        arrayObjectAdapter.add(new VipUserState(list.get(0).pathInfo));
        arrayObjectAdapter.add(new VipBanner(list));
        ListRow listRow = new ListRow(arrayObjectAdapter);
        addWithTryCatch(listRow);
    }


    private void addWithTryCatch(Object item) {
        try {
            if (!mBinding.vgContent.isComputingLayout()) {
                mAdapter.add(item);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addWithTryCatch(int index, Object item) {
        try {
            if (!mBinding.vgContent.isComputingLayout()) {
                mAdapter.add(index, item);
                mHasHistory = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addWithTryCatchReplace(int index, Object item) {
        try {
            if (!mBinding.vgContent.isComputingLayout()) {
                if (mHasHistory){
                    mAdapter.replace(index, item);
                } else {
                    mAdapter.add(index, item);
                }
                mNeedReplaceRecommend_3 = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addWithTryCatchInTheEnd(Object item) {
        try {
            if (!mBinding.vgContent.isComputingLayout()) {
                mAdapter.add(mAdapter.size() - 1, item);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过接口回调通知activity 是否显示 topBar
     *
     * @param isShow true 显示 false 隐藏
     */
    private void notifyTopBarState(boolean isShow) {
        if (mListener == null) return;
        if (isShow) {
            mListener.onFragmentInteraction(Uri.parse(Constant.URI_SHOW_TITLE));
        } else {
            mListener.onFragmentInteraction(Uri.parse(Constant.URI_HIDE_TITLE));
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mTypeZeroViewHolder != null && mTypeZeroViewHolder.getBinding().rootPlayer.getVisibility() == View.VISIBLE)
            restoreTypeZeroPlayer(mTypeZeroViewHolder);
        if (mTypeNewFilmViewHolder != null && mTypeNewFilmViewHolder.getBinding().rootPlayer.getVisibility() == View.VISIBLE) {
            mTypeNewFilmViewHolder.getBinding().rootPlayer.setVisibility(View.GONE);
            mLauncherPlayerManager.releasePlayer();
        }
        if (mTypeRecommendViewHolder != null && mTypeRecommendViewHolder.getBinding().rootPlayer.getVisibility() == View.VISIBLE) {
            mTypeRecommendViewHolder.getBinding().rootPlayer.setVisibility(View.GONE);
            mTypeRecommendViewHolder.getBinding().groupPlayComplete.setVisibility(View.GONE);
            mLauncherPlayerManager.releasePlayer();
        }
        if (mCurrentTabType == Constant.TYPE_VIP) {
            VipBannerPresenter vipBannerPresenter = (VipBannerPresenter) mTypeVipHeaderPresenterSelector.getPresenter(VipBanner.class);
            VipBannerPresenter.ViewHolder viewHolder = vipBannerPresenter.getViewHolder();
            if (viewHolder != null) {
                VipBannerView vipBannerView = (VipBannerView) viewHolder.view;
                vipBannerView.releasePlayer();
            }
        }
    }

    private void restoreTypeZeroPlayer(TypeZeroContentPresenterKt.TypeZeroViewHolder viewHolder){
        if (viewHolder.getBinding().rootPlayer.getVisibility() == View.VISIBLE){
            viewHolder.getBinding().typeZeroFocus.setVisibility(View.GONE);
            viewHolder.getBinding().typeZeroFocus.getLayoutParams().height = 346;
            viewHolder.getBinding().rootPlayer.getLayoutParams().height = 346;
            viewHolder.getBinding().rootPlayer.setVisibility(View.GONE);
            mLauncherPlayerManager.releasePlayer();
        }
    }
}
