package com.sohuott.tv.vod.adapter;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.GlideImageView;
import com.sohuott.tv.vod.widget.HomeViewJump;

import java.util.HashMap;
import java.util.List;

/**
 * Created by yizhang210244 on 2018/1/3.
 */

public class HomeAllCategoryAdapter extends RecyclerView.Adapter {
    private static final String TAG = HomeAllCategoryAdapter.class.getSimpleName();
    private final static int ITEM_FOOTER_TYPE = 0;
    private final static int ITEM_BIG_PIC_TYPE = 1;
    private final static int ITEM_ICON_TYPE = 2;
    private final static int ITEM_NULL_TYPE = 3;
    private List<HomeRecommendBean.Data.Content> mContentList;
    private long mChannelId;
    private boolean mAlbumEnable = true;
    private RecyclerView mRecyclerView;
    private int mCurrentPosition = 0;
    private boolean mIsFocusUpPosition = false;
    private Activity mActivity;
    private Context mContext;
    private View mFooterView;
    private View mNullView;

    public HomeAllCategoryAdapter(Activity activity, RecyclerView recyclerView) {
        mActivity = activity;
        if (mActivity != null) {
            mContext = mActivity.getApplicationContext();
        }
        mRecyclerView = recyclerView;
    }

    public void setAlbumEnable(boolean albumEnable) {
        mAlbumEnable = albumEnable;
    }


    public boolean isAlbumEnable() {
        return mAlbumEnable;
    }

    public void setChannelId(long channelId) {
        mChannelId = channelId;
    }

    public void setContentList(List<HomeRecommendBean.Data.Content> contentList) {
        mContentList = contentList;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
    }

    public boolean isFocusUpPosition() {
        return mIsFocusUpPosition;
    }

    public void setFocusUpPosition(boolean focusUpPosition) {
        mIsFocusUpPosition = focusUpPosition;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == getItemCount() - 1) {
            //最后一个,应该加载Footer
            return ITEM_FOOTER_TYPE;
        } else if (position < 6) {
            return ITEM_BIG_PIC_TYPE;
        } else if (position < mContentList.size()) {
            return ITEM_ICON_TYPE;
        } else {
            return ITEM_NULL_TYPE;
        }
//            else {
//            return ITEM_NULL_TYPE;
//        }
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v;
        RecyclerView.ViewHolder viewHolder = null;
        if (viewType == ITEM_FOOTER_TYPE) {
            mFooterView =
                    LayoutInflater.from(parent.getContext()).inflate(R.layout.my_fragment_footer_layout, parent, false);
            v = mFooterView;
//            mFooterView.getLayoutParams().width = ViewGroup.LayoutParams.MATCH_PARENT;
//            mFooterView.getLayoutParams().height = parent.getContext().getResources()
//            .getDimensionPixelSize(R.dimen.y184);
        } else if (viewType == ITEM_BIG_PIC_TYPE) {
            v = LayoutInflater.from(parent.getContext()).inflate(R.layout.home_all_category_item_bigpic_layout, parent, false);
        } else if (viewType == ITEM_ICON_TYPE) {
            v = LayoutInflater.from(parent.getContext()).inflate(R.layout.home_all_category_item_icon_layout, parent, false);
        } else {
            v = LayoutInflater.from(parent.getContext()).inflate(R.layout.home_all_category_item_null_layout, parent, false);
            mNullView = v;
        }
        viewHolder = new ViewHolder(v);
        return viewHolder;
    }

    private boolean mIsVisible = false;

    public void onVisible() {
        AppLogger.d(TAG, "onVisible");
        mIsVisible = true;
        if (mContentList != null) {
            for (int pos = 0; pos < 18 && pos < mContentList.size(); pos++) {
                final HomeRecommendBean.Data.Content content = mContentList.get(pos);
                if (content == null) {
                    return;
                }
                String ottcategoryId = "-1";
                if (content.getOttCategoryId() != null) {
                    ottcategoryId = String.valueOf(content.getOttCategoryId());
                }
                exposureStatistic(content.getId(), ottcategoryId, pos + 1);
            }
        }
    }

    public void onInVisible() {
        AppLogger.d(TAG, "onInVisible");
        mIsVisible = false;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        AppLogger.d(TAG, "onBindViewHolder position = " + position);
        ViewHolder viewHolder = (ViewHolder) holder;
        if (mContentList == null) {
            return;
        }
        if (position >= mContentList.size()) {
            if (position == getItemCount() - 1) {
                exposureBottomButtonStatistic(10141);
            }
            return;
        }
        final HomeRecommendBean.Data.Content content = mContentList.get(position);
        if (viewHolder != null) {
            if (content == null) {
                return;
            }
            AppLogger.d(TAG, "onBindViewHolder mAlbumEnable = " + mAlbumEnable);
            AppLogger.d(TAG, "onBindViewHolder content.getPicUrl2() = " + content.getPicUrl2());
//            viewHolder.album.setImageRes(content.getPicUrl2(), false);
            Glide.with(mContext)
                    .load(content.getPicUrl2())
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(viewHolder.album);
            viewHolder.title.setText(content.getName());
            final int pos = position;
            if (mIsVisible) {
                String ottcategoryId = "-1";
                if (content.getOttCategoryId() != null) {
                    ottcategoryId = String.valueOf(content.getOttCategoryId());
                }
                exposureStatistic(content.getId(), ottcategoryId, pos + 1);
            }
            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    AppLogger.d(TAG, "onClick pos ? " + pos);
                    HomeViewJump.clickAllCategoryItem(v.getContext(), content, mChannelId, false,
                            pos);
                    String ottcategoryId = "-1";
                    if (content.getOttCategoryId() != null) {
                        ottcategoryId = String.valueOf(content.getOttCategoryId());
                    }
                    clickStatistic(content.getId(), ottcategoryId, pos + 1);
                }
            });
        }
    }


    private void exposureStatistic(long columnId, String collectionId, int indexId) {
        AppLogger.d(TAG, "exposureStatistic columnId ? " + columnId);
        AppLogger.d(TAG, "exposureStatistic indexId ? " + indexId);
        AppLogger.d(TAG, "exposureStatistic collectionId ? " + collectionId);
        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", StringUtil.toString(mChannelId));
        pathInfo.put("columnId", StringUtil.toString(columnId));
        pathInfo.put("indexId", StringUtil.toString(indexId));
        HashMap<String, String> memoInfo = new HashMap<>();
        memoInfo.put("collectionId", collectionId);
        AppLogger.d(TAG, "exposureStatistic EventInfo ? 10159");
        RequestManager.getInstance().onAllEvent(new EventInfo(10159, "imp"), pathInfo, null, memoInfo);
    }

    private void clickStatistic(long columnId, String collectionId, int indexId) {
        AppLogger.d(TAG, "clickStatistic columnId ? " + columnId);
        AppLogger.d(TAG, "clickStatistic indexId ? " + indexId);
        AppLogger.d(TAG, "clickStatistic collectionId ? " + collectionId);
        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", StringUtil.toString(mChannelId));
        pathInfo.put("columnId", StringUtil.toString(columnId));
        pathInfo.put("indexId", StringUtil.toString(indexId));
        HashMap<String, String> memoInfo = new HashMap<>();
        memoInfo.put("collectionId", collectionId);
        RequestManager.getInstance().onAllEvent(new EventInfo(10160, "clk"), pathInfo, null, memoInfo);
    }

    private void clickBottomButtonStatistic(int eventId) {
        AppLogger.d(TAG, "clickBottomButtonStatistic eventId ? " + eventId);
        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", StringUtil.toString(mChannelId));
        RequestManager.getInstance().onAllEvent(new EventInfo(eventId, "clk"), pathInfo, null,
                null);
    }

    private void exposureBottomButtonStatistic(int eventId) {
        AppLogger.d(TAG, "exposureBottomButtonStatistic eventId ? " + eventId);
        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", StringUtil.toString(mChannelId));
        RequestManager.getInstance().onAllEvent(new EventInfo(eventId, "imp"), pathInfo, null,
                null);
    }

    @Override
    public long getItemId(int position) {
        return position + 100;
    }

    @Override
    public int getItemCount() {
        int itemCount = 0;
        if (mContentList != null && !mContentList.isEmpty()) {
            int size = mContentList.size();
            if (size < 3) {
                itemCount = 3 + 1;
            } else if (size < 6) {
                itemCount = 6 + 1;
            } else {
                if (size % 6 == 0) {
                    itemCount = size + 1;
                } else {
                    itemCount = (size / 6 + 1) * 6 + 1;
                }
            }
        }
        return itemCount;
    }


    class ViewHolder extends RecyclerView.ViewHolder {
        GlideImageView album;
        TextView title;

        public ViewHolder(View itemView) {
            super(itemView);
            if (itemView == mNullView) {
                return;
            } else if (itemView == mFooterView) {
                mFooterView.findViewById(R.id.cl_back_to_top)
                        .setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (mActivity != null && mActivity instanceof LauncherActivity) {
                                    View mTabView =
                                            ((LauncherActivity) mActivity).getHorizontalGridView();
                                    View mTopViewBar =
                                            ((LauncherActivity) mActivity).getTopViewBar();
                                    if (mTabView != null) {
                                        if (mTabView != null && mTabView.getVisibility() != View.VISIBLE) {
                                            mTabView.setVisibility(View.VISIBLE);
                                        }
                                        if (mTopViewBar != null && mTopViewBar.getVisibility() != View.VISIBLE) {
                                            mTopViewBar.setVisibility(View.VISIBLE);
                                        }
                                        mTabView.requestFocus();
                                        clickBottomButtonStatistic(10148);
                                    }
                                    mRecyclerView.scrollToPosition(0);
                                }
                            }
                        });
                mFooterView.findViewById(R.id.cl_back_to_top).setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View v, boolean hasFocus) {
                        AppLogger.d(TAG, "mFooterView onFocusChange hasFocus ? " + hasFocus);
                        AppLogger.d(TAG,
                                "mFooterView onFocusChange getItemCount() ? " + getItemCount());
                        if (hasFocus) {
                            mRecyclerView.smoothScrollToPosition(getItemCount() - 1);
                        }

                    }
                });
                mFooterView.findViewById(R.id.cl_look_around).setOnClickListener(
                        new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                ActivityLauncher.startSearchActivity(mActivity);
                                clickBottomButtonStatistic(10137);
                            }
                        });
                mFooterView.findViewById(R.id.cl_look_around).setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View v, boolean hasFocus) {
                        AppLogger.d(TAG, "mFooterView cl_look_around onFocusChange hasFocus ? " + hasFocus);
                        AppLogger.d(TAG,
                                "mFooterView cl_look_around onFocusChange getItemCount() ? " + getItemCount());
                        if (hasFocus) {
                            mRecyclerView.smoothScrollToPosition(getItemCount() - 1);
                        }

                    }
                });
            } else {
                album = (GlideImageView) itemView.findViewById(R.id.album);
                album.setClearWhenDetached(false);
                title = (TextView) itemView.findViewById(R.id.title);
                itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View v, boolean hasFocus) {
                        if (hasFocus) {
                            mCurrentPosition = getAdapterPosition();
                            AppLogger.d(TAG,
                                    "onFocusChange mCurrentPosition ? " + mCurrentPosition);
                            if (mCurrentPosition <= 2) {
                                if (mActivity != null && mActivity instanceof LauncherActivity) {
                                    if (((LauncherActivity) mActivity).getHorizontalGridView().getVisibility() != View.VISIBLE) {
                                        ((LauncherActivity) mActivity).getHorizontalGridView().setVisibility(View.VISIBLE);
                                    }
                                }
                            }
                            if (mCurrentPosition >= 3 && mCurrentPosition <= 5) {
                                if (mActivity != null && mActivity instanceof LauncherActivity) {
                                    if (((LauncherActivity) mActivity).getHorizontalGridView().getVisibility() != View.GONE) {
                                        ((LauncherActivity) mActivity).getHorizontalGridView().setVisibility(View.GONE);
                                    }
                                }
                            }
                            if (mCurrentPosition == 1) {
                                mIsFocusUpPosition = true;
                            } else if (mCurrentPosition == 2) {
                                mIsFocusUpPosition = false;
                            }
                            if (mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                                setFocusAnimator(v, FocusUtil.HOME_SCALE,
                                        FocusUtil.FOCUS_ANIM_TIME);
                            }
                        } else {
                            setUnFocusAnimator(v);
                        }
                    }
                });
            }
        }
    }


    private static AnimatorSet mAnimationSet;

    public static void setFocusAnimator(View focusView, float scale, int animTime) {
        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(focusView, "scaleX", 1f, scale)
                .setDuration(animTime);
        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(focusView, "scaleY", 1f, scale)
                .setDuration(animTime);
        if (mAnimationSet != null && mAnimationSet.isRunning()) {
            mAnimationSet.end();
        }
        mAnimationSet = new AnimatorSet();
        mAnimationSet.playTogether(scalexAnimator, scaleyAnimator);
        mAnimationSet.start();
    }

    public static void setUnFocusAnimator(View focusView) {
        setUnFocusAnimator(focusView, FocusUtil.FOCUS_ANIM_TIME);
    }

    public static void setUnFocusAnimator(View focusView, int animTime) {
        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(focusView, "scaleX",
                focusView.getScaleX(), 1f)
                .setDuration(animTime);
        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(focusView, "scaleY",
                focusView.getScaleY(), 1f)
                .setDuration(animTime);
        AnimatorSet set = new AnimatorSet();
        set.playTogether(scalexAnimator, scaleyAnimator);
        set.start();
    }


}
