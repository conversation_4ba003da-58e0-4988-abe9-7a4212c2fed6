package com.sohuott.tv.vod.search

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.scopeNetLife
import com.drake.net.Get
import com.drake.net.convert.SerializationConverter
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.lib.api.NetworkApi
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.db.greendao.SearchHistory
import com.sohuott.tv.vod.lib.db.greendao.SearchHistoryDao
import com.sohuott.tv.vod.lib.model.AuditDenyAids
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.model.SearchHot
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class SearchViewModel:ViewModel() {
    private val _hotSearchList = MutableStateFlow<List<SearchHot.Data>>(emptyList())
    val hotSearchList: StateFlow<List<SearchHot.Data>> = _hotSearchList

    private val _searchHistoryList = MutableStateFlow<List<SearchHistory>>(emptyList())
    val searchHistoryList: StateFlow<List<SearchHistory>> = _searchHistoryList

    val MAX_SEARCH_HISTORY = 6

    //清除搜索历史
    fun clearHistory(searchHistoryDatabaseProvider: SearchHistoryDatabaseProvider) {
        searchHistoryDatabaseProvider
            .provideSearchHistoryDatabase().searchHistoryDao.deleteAll()
        _searchHistoryList.value = emptyList()
    }

    //热搜榜
    fun searchHotList(context: Context) {
        scopeNetLife {
//            val url=if (UrlWrapper.DEBUG) "http://fat-api.ott.tv.snmsohu.aisee.tv/test/ott-api-v4/v4/" else "https://api.ott.tv.snmsohu.aisee.tv/ott-api-v4/v4/"
            val hotSearchList = Get<SearchHot>("${RetrofitApi.get().retrofitHost.baseHost}search/hotSearchWord.json"){
                setHeaders(HeaderHelper.getHeaders().toHeaders())
            }.await().data
            _hotSearchList.value = if (hotSearchList.size > 20) hotSearchList.subList(0, 20) else hotSearchList
        }
    }

    //搜索历史
    fun getSearchHistory(searchHistoryDatabaseProvider: SearchHistoryDatabaseProvider) {
        _searchHistoryList.value = searchHistoryDatabaseProvider
            .provideSearchHistoryDatabase()
            .searchHistoryDao
            .queryBuilder()
            .orderDesc(SearchHistoryDao.Properties.ClickCount)
            .limit(MAX_SEARCH_HISTORY).list()

        val vrsId = _searchHistoryList.value.joinToString(separator = ",") { it.albumId.toString() }

        //获取审核不通过的aid
        NetworkApi.getAuditDenyAids(vrsId, "", object : Observer<AuditDenyAids?> {
            override fun onSubscribe(d: Disposable) {}
            override fun onNext(value: AuditDenyAids?) {
                if (value != null && value.data != null && value.data.size > 0) {
                    val denyAids = value.data
                    //TODO denyAids
                }
            }

            override fun onError(e: Throwable) {}
            override fun onComplete() {}
        })
    }
}