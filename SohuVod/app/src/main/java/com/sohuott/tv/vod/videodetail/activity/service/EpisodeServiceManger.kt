package com.sohuott.tv.vod.videodetail.activity.service

class EpisodeServiceManger : EpisodeService {

    private val mServices = mutableListOf<EpisodeService>()

    fun addService(service: EpisodeService) {
        mServices.add(service)
    }

    fun removeService(service: EpisodeService) {
        mServices.remove(service)
    }

    fun getServices(): MutableList<EpisodeService> {
        return mServices
    }

    companion object {
        private var mManger: EpisodeServiceManger? = null

        @JvmStatic
        fun getInstants(): EpisodeServiceManger {
            mManger ?: synchronized(this) {
                mManger ?: also {
                    mManger = EpisodeServiceManger()
                }
            }
            return mManger!!
        }
    }

    override fun onEpisodeClickVideo(
        aid: Int,
        historyId: Int,
        albumVid: Int,
        videoType: Int,
        isTrailerTab: Boolean
    ) {
        mServices.forEach {
            it.onEpisodeClickVideo(aid, historyId, albumVid, videoType, isTrailerTab)
        }
    }

    override fun onEpisodeClickRelease() {
        mServices.forEach {
            it.onEpisodeClickRelease()
        }
    }
}