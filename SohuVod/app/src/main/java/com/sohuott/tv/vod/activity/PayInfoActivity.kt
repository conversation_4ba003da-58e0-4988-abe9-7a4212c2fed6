package com.sohuott.tv.vod.activity

import android.content.Intent
import android.os.Bundle
import com.com.sohuott.tv.vod.base_component.NewBaseActivity
import com.lib_viewbind_ext.viewBinding
import com.sohu.lib_utils.FormatUtils
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.account.common.Listener
import com.sohuott.tv.vod.account.login.CarouselLogin
import com.sohuott.tv.vod.account.payment.PayApi
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.databinding.ActivityPayInfoBinding
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.utils.ActivityLauncher
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper

class PayInfoActivity: NewBaseActivity(R.layout.activity_pay_info) {

    private var mViewBinding: ActivityPayInfoBinding? = null
    private val _binding by viewBinding(onViewDestroyed = {
        mViewBinding = null
    }, ActivityPayInfoBinding::bind)
    private var loginUserHelper: LoginUserInformationHelper? = null
    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = _binding
        loginUserHelper = LoginUserInformationHelper.getHelper(this)

        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight = MyFocusHighlightHelper.BrowseItemFocusHighlight(
                FocusHighlight.ZOOM_FACTOR_SMALL,
                false
            )
        }

        mViewBinding?.openVip?.setOnClickListener{
            //开通会员
            ActivityLauncher.startPayActivity(this)
        }
        mViewBinding?.historyOrder?.setOnClickListener {
            //历史订单
            ActivityLauncher.startListUserRelatedActivity(
                this,
                ListUserRelatedActivity.LIST_INDEX_CONSUME_RECORD
            )
        }
        mViewBinding?.autoManager?.setOnClickListener {
            //管理自动续费
            val intent = Intent(this, RenewActivity::class.java)
            intent.putExtra(RenewActivity.PARAM_AC_TYPE, RenewActivity.ACTYPE_RENEW)
            this.startActivity(intent)
        }

        mViewBinding?.openVip?.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
        }

    }
    override fun onResume() {
        super.onResume()
        refreshUserInfo()
        requestUserInfo()
    }

    private fun requestUserInfo() {
        if (loginUserHelper?.isLogin == true) {
            PayApi.getCarouselLogin(this, "", 0, loginUserHelper?.loginPassport, loginUserHelper?.loginToken, 0, 0, 0, object : Listener<CarouselLogin?>{
                override fun onSuccess(response: CarouselLogin?) {
                    LibDeprecatedLogger.d("getCarouselLogin(): response = $response")
                    val data = response?.data
                    val status = response?.status
                    if (status == 200 && null != data) {
                        val userInfo = data.userInfo
                        if (null != userInfo) {
                            if (userInfo.signInfo != null) {
                                if (userInfo.signInfo
                                        .wechat != null && userInfo
                                        .signInfo.wechat
                                        .status == "1"
                                ) {
                                    mViewBinding?.autoSign?.visible()
                                } else if (userInfo.signInfo
                                        .alipay != null && userInfo
                                        .signInfo.alipay
                                        .status == "1"
                                ) {
                                    mViewBinding?.autoSign?.visible()
                                } else {
                                    mViewBinding?.autoSign?.gone()
                                }
                            } else {
                                mViewBinding?.autoSign?.gone()
                            }
                        }
                    }

                }

                override fun onError(e: Throwable?) {

                }

            })
        }
    }


    private fun refreshUserInfo() {

        if (loginUserHelper?.isLogin == true) {
            val avatar: String? = loginUserHelper?.loginPhoto
            if (null != avatar && avatar.trim { it <= ' ' } != "") {
                mViewBinding?.avatar?.setCircleImageRes(loginUserHelper?.loginPhoto)
            }
            mViewBinding?.userName?.text = loginUserHelper?.nickName

            when (Util.getSouthMediaLoginType(loginUserHelper?.utype)) {
                4 -> mViewBinding?.loginType?.setBackgroundResource(R.drawable.login_type_weibo)
                1 -> mViewBinding?.loginType?.setBackgroundResource(R.drawable.login_type_wechat)
                2 -> mViewBinding?.loginType?.setBackgroundResource(R.drawable.login_type_qq)
                3 -> mViewBinding?.loginType?.setBackgroundResource(R.drawable.login_type_sohu)
            }

            //普通会员、已过期
            if(loginUserHelper?.isVip == true && System.currentTimeMillis() <= java.lang.Long.valueOf(loginUserHelper?.getVipTime())){

                //vip
                val vipDate: String =
                    FormatUtils.formatDate(java.lang.Long.valueOf(loginUserHelper?.getVipTime()))
                mViewBinding?.vipTime?.text = "会员有效期：${vipDate} | 观影券：${loginUserHelper?.userTicketNumber}"
                mViewBinding?.openVip?.text = "立即续费"

                if (loginUserHelper?.autoSign.equals("1")) {
                    //自动续费
                    mViewBinding?.autoSign?.visible()
                } else {
                    mViewBinding?.autoSign?.gone()
                }

            } else {
                if (loginUserHelper?.getVipTime().equals("0")) {
                    //普通用户
                    mViewBinding?.vipTime?.text = "您未开通该服务"
                    mViewBinding?.openVip?.text = "开通会员"
                    mViewBinding?.autoSign?.gone()
                } else {
                    //过期
                    mViewBinding?.vipTime?.text = "会员已过期：观影券冻结${loginUserHelper?.userTicketNumber}，续费解冻并赠新券。"
                    mViewBinding?.openVip?.text = "立即续费"
                    mViewBinding?.autoSign?.gone()
                }
            }
        } else{
            //未登录
            mViewBinding?.userName?.text = "未登录"
            mViewBinding?.vipTime?.text = "您未开通该服务"
            mViewBinding?.openVip?.text = "开通会员"
            mViewBinding?.autoSign?.gone()
        }
    }

}