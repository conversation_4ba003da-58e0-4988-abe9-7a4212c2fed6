package com.sohuott.tv.vod.activity.permission

import android.app.Activity
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import com.alibaba.android.arouter.facade.annotation.Route
import com.hjq.permissions.IPermissionInterceptor
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.sohu.lib_utils.PrefUtil
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.base_router.RouterPath
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger

/**
 * 储存权限页面
 */
@Route(path = RouterPath.Permissions.STORAGE_PERMISSION_ACTIVITY)
class StoragePermissionsActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_storage_permissions)
        setDisplay()
        requestPermissions()
    }

    private fun requestPermissions() {
        XXPermissions.with(this).permission(*Permission.Group.STORAGE)
            .interceptor(object : IPermissionInterceptor {
                override fun requestPermissions(
                    activity: Activity,
                    allPermissions: List<String>,
                    callback: OnPermissionCallback
                ) {
                    LibDeprecatedLogger.d("XXPermissions requestPermissions")
                    if (!PrefUtil.getBoolean(

                            "XXPermissions_never",
                            false
                        )
                    ) {
                    }
                    super<IPermissionInterceptor>.requestPermissions(
                        activity,
                        allPermissions,
                        callback
                    )
                }

                override fun grantedPermissions(
                    activity: Activity,
                    allPermissions: List<String>,
                    grantedPermissions: List<String>,
                    all: Boolean,
                    callback: OnPermissionCallback
                ) {
                    LibDeprecatedLogger.d("XXPermissions grantedPermissions")
                    super<IPermissionInterceptor>.grantedPermissions(
                        activity,
                        allPermissions,
                        grantedPermissions,
                        all,
                        callback
                    )
                }

                override fun deniedPermissions(
                    activity: Activity,
                    allPermissions: List<String>,
                    deniedPermissions: List<String>,
                    never: Boolean,
                    callback: OnPermissionCallback
                ) {
                    LibDeprecatedLogger.d("XXPermissions deniedPermissions")
                    super<IPermissionInterceptor>.deniedPermissions(
                        activity,
                        allPermissions,
                        deniedPermissions,
                        never,
                        callback
                    )
                }
            }).request(object : OnPermissionCallback {
                override fun onGranted(permissions: List<String>, all: Boolean) {
                    LibDeprecatedLogger.d("XXPermissions onGranted is all $all")
                    startMan()
                }

                override fun onDenied(permissions: List<String>, never: Boolean) {
                    LibDeprecatedLogger.d("XXPermissions onDenied is never:$never")
                    PrefUtil.putBoolean("XXPermissions_never", never)
                    startMan()
                }
            })
    }

    private fun startMan() {
        finish()
    }


    //设置窗口大小
    private fun setDisplay() {
        //设置弹出窗口与屏幕对齐
        val win = this.window
        val density = resources.displayMetrics.density.toInt()
        //设置内边距，这里设置为0
        win.decorView.setPadding(1 * density, 1 * density, 1 * density, 1 * density)
        val lp = win.attributes
        //设置窗口宽度
        lp.width = WindowManager.LayoutParams.MATCH_PARENT
        //设置窗口高度
        lp.height = WindowManager.LayoutParams.MATCH_PARENT
        //设置Dialog位置
        lp.gravity = Gravity.TOP or Gravity.LEFT
        win.attributes = lp
    }
}