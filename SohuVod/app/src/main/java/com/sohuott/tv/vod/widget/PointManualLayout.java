package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohu.lib_utils.FontUtils;
import com.sohu.lib_utils.StringUtil;

/**
 * Created by wenjingbian on 2017/12/26.
 */

public class PointManualLayout extends LinearLayout {

    private TextView tv_title, tv_action;
    private Context mContext;

    private boolean isEnabled;

    public PointManualLayout(Context context) {
        super(context);
        this.mContext = context;
        initView();
    }

    public PointManualLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        initView();
    }

    public PointManualLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        initView();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    public boolean isEnabled(){
        return isEnabled;
    }

    public void updateView(boolean isEnabled, int taskId) {
        this.isEnabled = isEnabled;
        String text = null;
        if (isEnabled) {
            tv_action.setText("去赚积分");
            switch (taskId) {
                case Constant.USER_POINT_SIGN:
                    text = "每日签到 +2分";
                    break;
                case Constant.USER_POINT_FIRST_LOGIN:
                    text = "首次登录 +3分";
                    break;
                case Constant.USER_POINT_WECHAT:
                    text = "关注公众号 +3分";
                    break;
                default:
                    break;
            }
            setClickable(true);
        } else {
            tv_action.setText("已赚积分");
            switch (taskId) {
                case Constant.USER_POINT_SIGN:
                    text = "每日签到 2分";
                    break;
                case Constant.USER_POINT_FIRST_LOGIN:
                    text = "首次登录 3分";
                    break;
                case Constant.USER_POINT_WECHAT:
                    text = "关注公众号 3分";
                    break;
                default:
                    break;
            }
            if (!StringUtil.isEmpty(text)) {
                tv_title.setText(text);
            }
        }
        if (!StringUtil.isEmpty(text)) {
            SpannableStringBuilder ssb = new SpannableStringBuilder(text);
            ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.txt_grid_left_normal)),
                    0, text.length() - 3, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.txt_grid_left_focused)),
                    text.length() - 3, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            tv_title.setText(ssb);
            FontUtils.setTypeface(mContext, tv_title);
        }
    }

    private void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.layout_point_group_three, this, true);
        tv_title = (TextView) findViewById(R.id.tv_group_three_title);
        tv_action = (TextView) findViewById(R.id.tv_group_three_action);
    }
}
