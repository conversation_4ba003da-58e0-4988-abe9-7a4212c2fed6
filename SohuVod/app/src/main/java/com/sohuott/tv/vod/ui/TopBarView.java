package com.sohuott.tv.vod.ui;

import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;

import java.lang.ref.WeakReference;
import java.util.Calendar;
import java.util.Date;

import de.hdodenhof.circleimageview.CircleImageView;

/**
 * Created by fenglei on 15-8-7.
 */
public class TopBarView extends RelativeLayout {

    private static final String TAG = TopBarView.class.getSimpleName();

    public static final int MSG_TIME = 0;

    private TextView mTimeTV;
    private CircleImageView mAvatarIV;

    private TopBarHandler mTopBarHandler;

    private LoginUserInformationHelper mHelper;

    static class TopBarHandler extends Handler {

        private WeakReference<TopBarView> topBarViewWeakReference;

        public TopBarHandler(TopBarView topBarView) {
            super();
            this.topBarViewWeakReference = new WeakReference<>(topBarView);
        }

        @Override
        public void handleMessage(Message msg) {
            TopBarView topBarView = topBarViewWeakReference.get();
            if(topBarView != null) {
                switch (msg.what) {
                    case MSG_TIME:
                        topBarView.updateCurrentTime();
                        topBarView.postTimeMsg();
                        break;
                    default:
                        break;
                }
            }
        }
    }

    public TopBarView(Context context, AttributeSet attrs) {
        super(context, attrs);

        mHelper = LoginUserInformationHelper.getHelper(context.getApplicationContext());


        LayoutInflater.from(context).inflate(R.layout.top_bar, this, true);
//        setBackgroundColor(getResources().getColor(R.color.list_base_item_bg_selected));
        mTimeTV = (TextView) findViewById(R.id.timeTV);
        mAvatarIV = (CircleImageView) findViewById(R.id.avatarIV);
        mTopBarHandler = new TopBarHandler(this);

        updateCurrentTime();
        postTimeMsg();

        setAvatar();
    }

    public void updateCurrentTime() {
        Calendar currentTime = Calendar.getInstance();
        currentTime.setTime(new Date());
        int sysHour = currentTime.get(Calendar.HOUR_OF_DAY);
        int sysMinute = currentTime.get(Calendar.MINUTE);
        String hourStr = String.format("%2d:", sysHour);
        String minuteStr;
        if (sysMinute < 10) {
            minuteStr = String.format("0%d", sysMinute);
        } else {
            minuteStr = String.format("%2d", sysMinute);
        }
        mTimeTV.setText(hourStr + minuteStr);
    }

    public void setAvatarVisibility(int visibility) {
        mAvatarIV.setVisibility(visibility);
    }

    public void setAvatar() {
        String avatar = mHelper.getLoginPhoto();

        if (null != avatar && !avatar.trim().equals("")) {
//            ImageLoader.getInstance().displayImage(avatar, mAvatarIV, mDisplayOption);
        } else {
            mAvatarIV.setImageResource(R.drawable.default_avatar);
        }
    }

    private void postTimeMsg() {
        Message m = mTopBarHandler.obtainMessage(MSG_TIME);
        Calendar c = Calendar.getInstance();
        int sec = c.get(Calendar.SECOND);
        long uptimeMillis = (60 - sec) * 1000;
        mTopBarHandler.sendMessageDelayed(m, uptimeMillis);
    }

}
