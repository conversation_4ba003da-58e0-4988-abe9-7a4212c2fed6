package com.sohuott.tv.vod.utils;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.task.IDownloadListener;
import com.sohuott.tv.vod.task.UpdateTask;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.SocketException;
import java.net.URL;

/**
 * Created by wenjingbian on 2016/4/1.
 */
public class DownloadUtil {

    private static final String LOG_TAG = "Vod_Download";

    private static final int REQUEST_TIMEOUT = 5000;

    /**
     * Download APK file via HttpURLConnection
     */
    public static boolean downloadFile(IDownloadListener downloadListener, String urlStr, String localPath) {
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            File downloadFile = new File(localPath);
            if (downloadFile.exists()) {
                downloadFile.delete();
            }

            URL url = new URL(urlStr);
            HttpURLConnection httpUrlConnection = (HttpURLConnection) url.openConnection();
            httpUrlConnection.setRequestMethod("GET");
            httpUrlConnection.setConnectTimeout(REQUEST_TIMEOUT);
            httpUrlConnection.setReadTimeout(REQUEST_TIMEOUT);
            httpUrlConnection.connect();
            int totalSize = httpUrlConnection.getContentLength();

            int networkResponseCode = httpUrlConnection.getResponseCode();
            if (downloadListener != null)
                downloadListener.setNetworkResponseCode(networkResponseCode);
            if (networkResponseCode == HttpURLConnection.HTTP_OK) {
                inputStream = httpUrlConnection.getInputStream();
                outputStream = new FileOutputStream(localPath);
                int bufferLength = 0;
                int downloadedSize = 0;
                byte[] buffer = new byte[1024];
                if (downloadListener instanceof UpdateTask) {
                    while ((bufferLength = inputStream.read(buffer)) > 0
                            && !((UpdateTask) downloadListener).isCancelled()) {
                        outputStream.write(buffer, 0, bufferLength);
                        downloadedSize += bufferLength;
                        if (downloadListener != null)
                            downloadListener.updateProgress((int) ((double)downloadedSize / totalSize * 100.0));
                    }
                } else {
                    while ((bufferLength = inputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, bufferLength);
                        downloadedSize += bufferLength;
                        if (downloadListener != null)
                            downloadListener.updateProgress((int) ((double)downloadedSize / totalSize * 100.0));
                    }
                }
                httpUrlConnection.disconnect();
                if (downloadedSize == totalSize)
                    return true;
            }
            httpUrlConnection.disconnect();
        } catch (SocketException e) {
            AppLogger.e(LOG_TAG, "Timeout when connect to network: " + e);
        } catch (Exception e) {
            AppLogger.e(LOG_TAG, "Exception during downloadApkFile(): " + e);
        } finally {
            try {
                if (inputStream != null)
                    inputStream.close();
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                AppLogger.e(LOG_TAG, "Exception during closing stream, " + e);
            }
        }
        return false;
    }
}
