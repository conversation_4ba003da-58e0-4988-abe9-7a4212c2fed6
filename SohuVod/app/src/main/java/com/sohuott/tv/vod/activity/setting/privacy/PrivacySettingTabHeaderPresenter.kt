package com.sohuott.tv.vod.activity.setting.privacy

import androidx.appcompat.widget.AppCompatTextView
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingHeaderItem

/**
 *
 * @Description
 * @date 2022/3/23 10:07
 * <AUTHOR>
 * @Version 1.0
 */
class PrivacySettingTabHeaderPresenter :
    DefaultPresenter(R.layout.item_privacy_setting_tab_header_layout) {
    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as PrivacySettingHeaderItem
        val name = viewHolder.getView<AppCompatTextView>(R.id.tv_privacy_tab_name)
        name.text = item.name
    }
}