package com.sohuott.tv.vod.utils;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

import io.reactivex.observers.DisposableObserver;

/**
 * <AUTHOR>
 * @date 2017/7/4
 */
public class SimpleDisposableObsever<T> extends DisposableObserver<T> {


    public SimpleDisposableObsever() {
        mFrom = "";

    }

    String mFrom;

    public SimpleDisposableObsever(String from) {
        this.mFrom = from;
    }

    @Override
    public void onNext(T value) {
    }

    @Override
    public void onError(Throwable e) {
        LibDeprecatedLogger.e(mFrom + " -- onError() = " + e.toString());

    }

    @Override
    public void onComplete() {
        LibDeprecatedLogger.d(mFrom + " -- onComplete");
    }
}
