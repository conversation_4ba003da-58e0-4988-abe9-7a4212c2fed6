package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import com.google.gson.Gson;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SubjectActivity;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.model.ComingSoonParameterModel;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.List;

/**
 * Created by y<PERSON>yin on 2018/9/20.
 */

public class SubjectFragment extends BaseFragment implements View.OnFocusChangeListener, View.OnClickListener {
    private static final String ARG_PAGE_NUMBER = "page_number";
    private static final String ARG_PAGE_PIC = "page_pic";
    private static final String ARG_PAGE_SMALL_PIC = "page_small_pic";
    private static final String ARG_PAGE_TYPE = "page_type";

    private int page;
    private int mType;
    private String mPic;
    private String mSmallPic;

    private View root;
    private GlideImageView bg;
    private GlideImageView pos1;
    private GlideImageView pos2;
    private GlideImageView pos3;
    private GlideImageView pos4;
    private GlideImageView pos5;
    private GlideImageView pos6;

    Animation mAnimation = null ;


    public static SubjectFragment newInstance(int page, String pic1, String pic2, String smallPic, int type){
        SubjectFragment subjectFragment = new SubjectFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_PAGE_NUMBER, page);
        if (page == 1) {
            args.putString(ARG_PAGE_PIC, pic1);
        } else {
            args.putString(ARG_PAGE_PIC, pic2);
        }
        args.putInt(ARG_PAGE_TYPE, type);
        args.putString(ARG_PAGE_SMALL_PIC, smallPic);
        subjectFragment.setArguments(args);
        return subjectFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        page = getArguments().getInt(ARG_PAGE_NUMBER);
        mPic = getArguments().getString(ARG_PAGE_PIC);
        mSmallPic = getArguments().getString(ARG_PAGE_SMALL_PIC);
        mType = getArguments().getInt(ARG_PAGE_TYPE);

        if (page == 1) {
            root = inflater.inflate(R.layout.fragment_subject_page1, container, false);
        } else {
            root = inflater.inflate(R.layout.fragment_subject_page2, container, false);
        }

        bg = (GlideImageView) root.findViewById(R.id.subject_bg);
        pos1 = (GlideImageView) root.findViewById(R.id.subject_btn_1);
        pos2 = (GlideImageView) root.findViewById(R.id.subject_btn_2);
        pos3 = (GlideImageView) root.findViewById(R.id.subject_btn_3);
        pos4 = (GlideImageView) root.findViewById(R.id.subject_btn_4);
        pos5 = (GlideImageView) root.findViewById(R.id.subject_btn_5);
        pos6 = (GlideImageView) root.findViewById(R.id.subject_btn_6);




        switch (mType){
            case 1:
                if (page == 1) {
                    pos2.setVisibility(View.GONE);
                } else {
                    pos6.setVisibility(View.GONE);
                }
                break;
            case 2:
                if (page == 1) {
                    pos2.setVisibility(View.GONE);
                }
                break;
            case 3:
                if (page == 1) {
                    pos1.setVisibility(View.GONE);
                } else {
                    pos6.setVisibility(View.GONE);
                }
                break;
            case 4:
                if (page == 1){
                    pos1.setVisibility(View.GONE);
                }
                break;
            case 5:
                if (page == 2){
                    pos6.setVisibility(View.GONE);
                }
                break;
            case 6:
                break;
            default:
                break;
        }

        bg.setImageRes(mPic, getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
        if (page == 1){
            if (pos1.getVisibility() == View.VISIBLE) {
                pos1.setOnFocusChangeListener(this);
                pos1.setOnClickListener(this);
                pos1.setImageRes(mSmallPic, getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
            }
            if (pos2.getVisibility() == View.VISIBLE) {
                pos2.setOnFocusChangeListener(this);
                pos2.setOnClickListener(this);
                pos2.setImageRes(mSmallPic, getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
            }
        } else {
            pos3.setImageRes(mSmallPic, getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
            pos4.setImageRes(mSmallPic, getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
            pos5.setImageRes(mSmallPic, getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
            if (pos6.getVisibility() == View.VISIBLE){
                pos6.setOnFocusChangeListener(this);
                pos6.setOnClickListener(this);
                pos6.setImageRes(mSmallPic, getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
            }
            pos3.setOnFocusChangeListener(this);
            pos4.setOnFocusChangeListener(this);
            pos5.setOnFocusChangeListener(this);

            pos3.setOnClickListener(this);
            pos4.setOnClickListener(this);
            pos5.setOnClickListener(this);

        }

        return root;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus){
            mAnimation = AnimationUtils.loadAnimation(getContext(),R.anim.scale);
            v.startAnimation(mAnimation);
        } else {
            v.setAnimation(null);
        }
    }

    private int getAlbumIdFromModel(List<ContentGroup.DataBean.ContentsBean.SubjectVideoListBean> list, int index){
        if (index <0 || index > list.size() -1) return -1;
        Gson gson = new Gson();
        ComingSoonParameterModel comingSoonParameterModel;
        comingSoonParameterModel = gson.fromJson(list.get(index).parameter, ComingSoonParameterModel.class);
        return Integer.parseInt(comingSoonParameterModel.getAlbumId());
    }

    @Override
    public void onClick(View v) {
        List<ContentGroup.DataBean.ContentsBean.SubjectVideoListBean> beanList =
                ((SubjectActivity)getActivity()).getComingSoonModel();
        int index = 0;
        switch (v.getId()){
            case R.id.subject_btn_1:
                index = 0;
                break;
            case R.id.subject_btn_2:
                if (mType == 3 || mType ==4) {
                    index = 0;
                } else {
                    index = 1;
                }
                break;
            case R.id.subject_btn_3:
                if (mType == 5 || mType ==6){
                    index = 2;
                } else {
                    index = 1;
                }
                break;
            case R.id.subject_btn_4:
                if (mType == 5 || mType ==6){
                    index = 3;
                } else {
                    index = 2;
                }
                break;
            case R.id.subject_btn_5:
                if (mType == 5 || mType == 6) {
                    index = 4;
                } else {
                    index = 3;
                }
                break;
            case R.id.subject_btn_6:
                if (mType == 5 || mType == 6){
                    index = 5;
                } else {
                    index = 4;
                }
                break;
        }
        ActivityLauncher.startVideoDetailActivity(getContext(),getAlbumIdFromModel(beanList,index), Constant.DATA_TYPE_VRS,Constant.PAGE_SUBJECT);
    }
}
