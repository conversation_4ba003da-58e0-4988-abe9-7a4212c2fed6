package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.utils.HomeUtils;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;

/**
 * Created by fenglei on 17-2-23.
 */

public class HomeCategoryItemView extends RelativeLayout implements View.OnClickListener, View.OnFocusChangeListener{

    private FocusBorderView mFocusBorderView;  //焦点框
    private GlideImageView bgIV;
    private TextView titleTV;
    private long mChannelId;
    private boolean mResizeEnable = false;
    private int mWidth,mHeight;
    private HomeRecommendBean.Data.Content mContent;
    private boolean mIsDts = false;

    public HomeCategoryItemView(Context context) {
        super(context);
        init(context);
    }

    public HomeCategoryItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public HomeCategoryItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        setBackgroundResource(R.drawable.recommend_item_selector);
        setFocusable(true);
        setOnFocusChangeListener(this);
        setOnClickListener(this);
        LayoutInflater.from(context).inflate(R.layout.home_cagtegory_item, this, true);
        bgIV = (GlideImageView) findViewById(R.id.bgIV);
        titleTV = (TextView) findViewById(R.id.titleTV);
    }

    public void setResize(int width, int height){
        mResizeEnable = true;
        mWidth = width;
        mHeight = height;
    }

    public void setDts(boolean isDts){
        mIsDts = isDts;
    }

    public void setData(HomeRecommendBean.Data.Content content, long channelId) {
        if(content == null){
            return;
        }
        mChannelId = channelId;
        mContent = content;

        String imageUrl = HomeUtils.getHomeImageUrl(content, HomeUtils.CATEGORY_HORZ_TYPE);
        if(!TextUtils.isEmpty(imageUrl)) {
            bgIV.setImageRes(imageUrl,false);
        }
        titleTV.setText(content.getName());
    }

    public void setData(HomeRecommendBean.Data.Content content, long channelId,boolean isDisplayAlbum) {
        if(content == null){
            return;
        }
        mChannelId = channelId;
        mContent = content;

        if(isDisplayAlbum){
            String imageUrl = HomeUtils.getHomeImageUrl(content, HomeUtils.CATEGORY_HORZ_TYPE);
            if(!TextUtils.isEmpty(imageUrl)) {
                bgIV.setImageRes(imageUrl,false);
            }
        }else {
            bgIV.clearImageMemo();
        }
        titleTV.setText(content.getName());
    }


    public void setAlbumVisibility(boolean value){

        if(mContent == null){
            return;
        }
        String imageUrl = HomeUtils.getHomeImageUrl(mContent, HomeUtils.CATEGORY_HORZ_TYPE);

        if(!TextUtils.isEmpty(imageUrl)){
            if(value){
                bgIV.setImageRes(imageUrl,false);
            }else {
                bgIV.clearImageMemo();
            }
        }
    }

    @Override
    public void onClick(View v) {
        HomeViewJump.clickAlbum(getContext(), mContent, mChannelId, mIsDts,-1);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
            if (mFocusBorderView != null) {
                mFocusBorderView.setFocusView(v);
            }
            FocusUtil.setFocusAnimator(v, mFocusBorderView);
        } else {
            if (mFocusBorderView != null) {
                mFocusBorderView.setUnFocusView(v);
            }
            FocusUtil.setUnFocusAnimator(v);
        }
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        mFocusBorderView = focusView;
    }

}
