package com.sohuott.tv.vod.activity.base

import android.os.SystemClock
import android.view.View
import com.sohuott.tv.vod.AppLogger;
import java.util.*

/**
 *
 * @Description
 * @date 2022/3/25 11:16
 * <AUTHOR>
 * @Version 1.0
 */

fun View.goneAndVisible(loadingView: View?) {

    loadingView?.visibility = View.VISIBLE
    this.visibility = View.GONE
}

fun View.invisibleAndVisible(loadingView: View?) {

    loadingView?.visibility = View.VISIBLE
    this.visibility = View.INVISIBLE
}

fun View.gone() {
    this.visibility = View.GONE
}

fun View.visible() {
    this.visibility = View.VISIBLE
}
fun View.invisible() {
    this.visibility = View.INVISIBLE
}

fun View.isVisible(isVisible: Boolean) {
    this.visibility = if (isVisible) View.VISIBLE else View.GONE
}
fun View.isInvisible(isVisible: Boolean) {
    this.visibility = if (isVisible) View.VISIBLE else View.INVISIBLE
}

fun View.isGone(isGone: Boolean) {
    this.visibility = if (isGone) View.GONE else View.VISIBLE
}

fun View.setTimeCountClick(count: Int, timeBetween: Long, click: () -> Unit) {
    //存储多次点击的时间戳
    val mHits = LongArray(count)
    setOnClickListener {
        System.arraycopy(
            mHits,
            1,
            mHits,
            0,
            count - 1
        );//自己拷贝自己，只不过错位拷贝【第二个元素拷贝到第一个元素，第一个元素拷贝到第零个元素】
        mHits[count - 1] = SystemClock.uptimeMillis();//给数组的最后一个元素赋值
        if (mHits[count - 1] - mHits[0] <= timeBetween) {//当第mHits[lengt-1]点击的时间戳减去mHits[0]的时间戳小于指定时间则该多击事件生效
            AppLogger.e("$timeBetween" + "毫秒内点击" + count + "次");
            Arrays.fill(mHits, 0);   //数据全部置零
            click()
        }

    }

}

fun View.scaleXY(xy: Float) {
    scaleXY(xy, xy)
}


fun View.scaleXY(x: Float, y: Float) {
    this.animate()?.scaleX(x)?.scaleY(y)?.setDuration(150)
        ?.start()
}

