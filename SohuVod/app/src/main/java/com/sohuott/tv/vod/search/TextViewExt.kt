package com.sohuott.tv.vod.search

// Import necessary libraries
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import android.text.Editable
import android.text.TextWatcher
import android.widget.TextView

// Create a function that returns a LiveData object that listens to changes in a TextView's text
fun TextView.textChanges(): LiveData<String> {
    // Create a MutableLiveData object to hold the text changes
    val liveData = MutableLiveData<String>()

    // Add a TextWatcher to the TextView
    this.addTextChangedListener(object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            // Set the value of the MutableLiveData object to the new text
            liveData.value = s.toString()
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            // Do nothing
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            // Do nothing
        }
    })

    // Return the LiveData object
    return liveData
}

