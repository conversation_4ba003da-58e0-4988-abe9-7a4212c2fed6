package com.sohuott.tv.vod

import android.content.Context
import android.util.AttributeSet
import com.drake.net.utils.scopeNetLife
import com.sh.ott.video.base.component.Resolution
import com.sh.ott.video.base.component.ShDataSource
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.base.BaseOptionModel
import com.sh.ott.video.player.base.OnStateChangeListener
import com.sh.ott.video.view.ShVideoView
import com.sh.ott.video.vv.VvConfig
import com.sh.ott.video.vv.VvPushManger
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.app.config.AppConfigDatabase
import com.sohuott.tv.vod.app.config.ResolutionInfo
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.videodetail.activity.LogoInfo
import com.sohuott.tv.vod.videodetail.activity.addPgcVideoPlayInfo
import com.sohuott.tv.vod.videodetail.activity.addVideoPlayInfo
import com.sohuott.tv.vod.videodetail.activity.appendAdTsUrl
import com.sohuott.tv.vod.videodetail.activity.control.VideoLogoComponent
import com.sohuott.tv.vod.videodetail.activity.conversionResolution
import com.sohuott.tv.vod.videodetail.activity.findPlayerResolution
import com.sohuott.tv.vod.videodetail.activity.repository.VideoRepository

open class CommonVideoView : ShVideoView, OnStateChangeListener {

    private var repository: VideoRepository = VideoRepository()

    private var logoInfo: LogoInfo? = null

    /**
     * 播放器播放信息
     */
    private var mShDataSource = ShDataSource()
    private var mBaseOptionModel = BaseOptionModel()

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(
        context,
        attrs,
        0
    ) {


    }

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int
    ) : super(context, attrs, defStyleAttr) {
        mVideoLogoComponent = VideoLogoComponent(context)
        if (!filmContainsControlComponents(mVideoLogoComponent!!)) {
            addFilmVideoControlComponent(mVideoLogoComponent!!)
        }
        addFilmOnStateChangeListener(this)
    }

    var enableAddLogoComponent = true
        set(value) {
            field = value
            if (!field) {
                if (filmContainsControlComponents(mVideoLogoComponent!!)) {
                    removeFilmVideoControlComponent(mVideoLogoComponent!!)
                }
                mVideoLogoComponent = null
            }
        }

    var dataType: Int? = null

    /**
     * 请求视频信息开始播放
     * @param aid aid专辑
     * @param vid vid
     * @param videoType 视频类型 pgc/vrs
     */
    fun startPlayVideo(vid: Int, aid: Int, videoType: Int) {
        release(false, true)
        dataType = videoType
        mShDataSource.videoAid = aid.toLong()
        mShDataSource.videoVid = vid.toLong()
        mShDataSource.videoType = videoType
        mShDataSource.gid = UserInfoHelper.getGid() ?: ""
        scopeNetLife {
            if (videoType == Constant.DATA_TYPE_VRS) {
                vrsRequest(aid, vid)
            } else {
                pgcRequest(aid, vid)
            }
        }
    }

    /**
     * 请求vrs数据
     */
    private suspend fun vrsRequest(aid: Int, vid: Int) {
        val videoInfo = repository.requestVrsVideo(
            aid,
            vid,
            UserLoginHelper.getInstants().getLoginPassport(),
            false
        ).await()
        logoInfo = LogoInfo().also {
            it.dimension = videoInfo?.data?.logoInfo?.dimension
            it.logo = videoInfo?.data?.logoInfo?.logo ?: 0
            it.logoleft = videoInfo?.data?.logoInfo?.logoleft ?: 0
            it.height = videoInfo?.data?.logoInfo?.height ?: 0f
            it.width = videoInfo?.data?.logoInfo?.width ?: 0f
            it.logoSideMargin = videoInfo?.data?.logoInfo?.side_margin ?: 0f
            it.logoTopMargin = videoInfo?.data?.logoInfo?.top_margin ?: 0f
            it.orientation = videoInfo?.data?.logoInfo?.orientation ?: 0
        }
        mShDataSource.videoLength = videoInfo.data.tvLength.toLong()
        mShDataSource.videoCateCode = videoInfo.data.categoryCode.toLong()
        videoInfo.data?.playInfo?.addVideoPlayInfo()
            ?.conversionResolution(AppConfigDatabase.getDefaultResolutionId(),
                UserLoginHelper.getInstants().isVip(),
                UserLoginHelper.getInstants().getIsLogin(),
                action = { info ->
                    conversionResolution(info)
                })
    }

    /**
     * 请求pgc数据
     */
    private suspend fun pgcRequest(aid: Int, vid: Int) {
        val pgcAlbumInfo = repository.requestPgcAlbum(
            aid
        ).await()
        logoInfo = LogoInfo().also {
            it.dimension = pgcAlbumInfo?.data?.logoInfo?.dimension
            it.logo = pgcAlbumInfo?.data?.logoInfo?.logo ?: 0
            it.logoleft = pgcAlbumInfo?.data?.logoInfo?.logoleft ?: 0
            it.height = pgcAlbumInfo?.data?.logoInfo?.height ?: 0f
            it.width = pgcAlbumInfo?.data?.logoInfo?.width ?: 0f
            it.logoSideMargin = pgcAlbumInfo?.data?.logoInfo?.side_margin ?: 0f
            it.logoTopMargin = pgcAlbumInfo?.data?.logoInfo?.top_margin ?: 0f
            it.orientation = pgcAlbumInfo?.data?.logoInfo?.orientation ?: 0
        }
        mShDataSource.videoLength = pgcAlbumInfo.data?.videoLength?.toLong()?:0L
        mShDataSource.videoCateCode = pgcAlbumInfo.data?.cateCode?.toLong()?:0L
        val videoInfo = repository.requestPgcVideo(
            vid,
            UserLoginHelper.getInstants().getLoginPassport()
        ).await()
        videoInfo.playinfo.addPgcVideoPlayInfo(pgcAlbumInfo)
            .conversionResolution(AppConfigDatabase.getDefaultResolutionId(),
                UserLoginHelper.getInstants().isVip(),
                UserLoginHelper.getInstants().getIsLogin(),
                action = { info ->
                    conversionResolution(info)
                })
    }

    //影片 LOGO 组件
    private var mVideoLogoComponent: VideoLogoComponent? = null

    /**
     * 转换清晰度数据并设置播放地址
     */
    private fun conversionResolution(
        videoPlayResolutionInfo: ResolutionInfo? = null,
    ) {
        if (dataType == Constant.DATA_TYPE_VRS) {
            videoPlayResolutionInfo?.videoPlayInfo?.hasLogo?.let {
                mVideoLogoComponent?.setChangeResolutionEnableLogo(it)
            }
        }
        onVvResolution(videoPlayResolutionInfo?.findPlayerResolution() ?: Resolution.MEDIA_HIGH)
        setPlayInfo(videoPlayResolutionInfo?.videoPlayInfo?.url)
    }

    private fun setPlayInfo(url: String?) {
        val mUrl = url.appendAdTsUrl()
        mShDataSource.url = mUrl
        mShDataSource.adSkip = true
        setDataSource(mShDataSource)
        prepareAsync()
        setVvPageId()
        //点播放按钮时发送
        VvPushManger.getInstance().onVVCreate(mShDataSource)
    }


    private fun setVvPageId() {
        VvConfig.vvParamsItem?.enterPageid = "0"
    }

    /**
     * Vv清晰度
     */
    private fun onVvResolution(resolution: Resolution) {
        VvPushManger.getInstance()
            .setVVResolution(resolution)
    }

    /**
     * 开始播放视频内容时发送（剧集播放1秒后发送）
     */
    private fun onVvStart() {
        VvPushManger.getInstance().onStart()
    }

    /**
     * 播放完发送
     */
    private fun onVvComplete() {
        VvPushManger.getInstance().onComplete()
    }

    /**
     * 关闭时发送
     */
    private fun onVvClose() {
        VvPushManger.getInstance().onClose()
    }

    override fun destroy() {
        super.destroy()
        onVvClose()
    }


    override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
        when (playState) {
            PlayerConstants.VideoState.PREPARED -> {
                onVvStart()
            }

            PlayerConstants.VideoState.PLAYBACK_COMPLETED -> {
                onVvComplete()
            }
        }
    }

}