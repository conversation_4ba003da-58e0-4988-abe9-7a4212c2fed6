package com.sohuott.tv.vod.app.config

import com.google.gson.annotations.SerializedName
import com.sohuott.tv.vod.videodetail.activity.model.VideoPlayInfo

data class AppPlayerConfig(
    @SerializedName("resolutions")
    var resolutions: MutableList<ResolutionInfo>? = null,
)

data class AppPlayerConfigInfo(
    @SerializedName("data")
    var data: AppPlayerConfig? = null
)


data class ResolutionInfo(

    /************ cms配置返回数据 start ********/
    @SerializedName("name")
    var name: String? = null,

    /**
     * 配置的清晰度集合
     */
    @SerializedName("serviceIds")
    var serviceId: MutableList<Int>? = mutableListOf(),

    /**
     * 是否是会员
     */
    @SerializedName("isVip")
    var isVip: Boolean = false,

    /**
     * 是否登录
     */
    @SerializedName("isLogin")
    var isLogin: Boolean = false,

    /**
     * 是否使用当前清晰度
     */
    @SerializedName("enable")
    var enable: Boolean = false,

    /**
     * 青少年模式是否可用
     */
    @SerializedName("teenagerEnable")
    var teenagerEnable: Boolean=false,
    /**
     * 排序 和 清晰度降级使用
     */
    @SerializedName("priority")
    var priority: Int = 0,
    /**
     * 当前264/265清晰度 优先级 只适配两个合并
     */
    @SerializedName("serviceIdPriority")
    var serviceIdPriority: MutableList<Int>? = mutableListOf(),

    @SerializedName("enableTips")
    var enableTips: Boolean = false,

    @SerializedName("tips")
    var tips: String? = "",
    /**
     * 根据本地储存清晰度  查找当前配置清晰度使用
     */
    @SerializedName("id")
    var id: Int? = 0,

    //是否是默认清晰度
    @SerializedName("default")
    var default: Boolean? = false,

    /************ cms配置返回数据 end ********/

    //自定义服务器接口 播放地址等信息
    var videoPlayInfo: VideoPlayInfo? = null
)




