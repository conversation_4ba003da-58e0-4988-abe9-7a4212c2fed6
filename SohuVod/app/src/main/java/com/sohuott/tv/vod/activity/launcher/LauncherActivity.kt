//package com.sohuott.tv.vod.activity.launcher
//
//import android.animation.ValueAnimator
//import android.app.Activity
//import android.content.DialogInterface
//import android.content.Intent
//import android.graphics.ColorMatrix
//import android.graphics.ColorMatrixColorFilter
//import android.graphics.Paint
//import android.media.MediaPlayer
//import android.net.Uri
//import android.os.Build
//import android.os.Bundle
//import android.os.CountDownTimer
//import android.os.Handler
//import android.os.Message
//import android.text.TextUtils
//import android.view.KeyEvent
//import android.view.View
//import android.view.ViewGroup
//import android.view.WindowManager
//import android.view.animation.TranslateAnimation
//import android.widget.LinearLayout
//import androidx.leanback.widget.ArrayObjectAdapter
//import androidx.leanback.widget.DiffCallback
//import androidx.leanback.widget.HorizontalGridView
//import androidx.leanback.widget.OnChildViewHolderSelectedListener
//import androidx.lifecycle.ViewModelProvider
//import androidx.recyclerview.widget.RecyclerView
//import androidx.viewpager2.widget.ViewPager2
//import com.alibaba.android.arouter.facade.annotation.Route
//import com.bumptech.glide.Glide
//import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
//import com.bumptech.glide.request.transition.DrawableCrossFadeFactory
//import com.google.gson.Gson
//import com.hjq.permissions.IPermissionInterceptor
//import com.hjq.permissions.OnPermissionCallback
//import com.hjq.permissions.Permission
//import com.hjq.permissions.XXPermissions
//import com.lib_dlna_core.SohuDlnaManger
//import com.lib_statistical.manager.RequestManager
//import com.lib_statistical.model.EventInfo
//import com.sohu.lib_utils.FormatUtils
//import com.sohu.lib_utils.PrefUtil
//import com.sohu.lib_utils.StringUtil
//import com.sohu.ott.ad.Advert
//import com.sohu.ott.ad.SplashVideoView
//import com.sohu.ott.ad.UrlFactory
//import com.sohu.ott.ads.sdk.SdkFactory
//import com.sohu.ott.ads.sdk.iterface.ILoader
//import com.sohu.ott.ads.sdk.model.AdCommon
//import com.sohu.ott.ads.sdk.model.RequestComponent
//import com.sohu.ott.ads.sdk.utils.OttHost
//import com.sohu.ott.base.lib_user.UserInfoHelper
//
//import com.sohuott.tv.base_room.manager.ImageInfoManager.saveCurrentQueue
//import com.sohuott.tv.vod.R
//import com.sohuott.tv.vod.account.user.UserApi
//import com.sohuott.tv.vod.activity.BaseFragmentActivity
//import com.sohuott.tv.vod.activity.RenewActivity
//import com.sohuott.tv.vod.activity.TeenModeDescActivity
//import com.sohuott.tv.vod.activity.teenagers.TeenagersManger
//import com.sohuott.tv.vod.app.SohuAppUtil
//import com.sohuott.tv.vod.base_router.RouterPath
//import com.sohuott.tv.vod.data.HomeData
//import com.sohuott.tv.vod.databinding.ActivityLauncherBinding
//import com.sohuott.tv.vod.fragment.BootFragment
//import com.sohuott.tv.vod.fragment.LauncherTabBridgeAdapter
//import com.sohuott.tv.vod.fragment.lb.HomeContentFragment
//import com.sohuott.tv.vod.lib.api.NetworkApi
//import com.sohuott.tv.vod.lib.log.AppLogger
//import com.sohuott.tv.vod.lib.model.AboutInfo
//import com.sohuott.tv.vod.lib.model.LauncherConfig
//import com.sohuott.tv.vod.lib.model.PrivacyInfo
//import com.sohuott.tv.vod.lib.model.ServerMessage
//import com.sohuott.tv.vod.lib.model.TopInfo
//import com.sohuott.tv.vod.lib.model.UpdateInfo
//import com.sohuott.tv.vod.lib.model.launcher.HomeTab
//import com.sohuott.tv.vod.lib.model.launcher.TeenModePopDialogBean
//import com.sohuott.tv.vod.lib.push.event.LoginSuccessEvent
//import com.sohuott.tv.vod.lib.push.event.LogoutEvent
//import com.sohuott.tv.vod.lib.utils.Constant
//import com.sohuott.tv.vod.lib.utils.DateUtils
//import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
//import com.sohuott.tv.vod.lib.utils.StringUtils
//import com.sohuott.tv.vod.lib.utils.UrlWrapper
//import com.sohuott.tv.vod.lib.utils.Util
//import com.sohuott.tv.vod.presenter.UpdatePresenter
//import com.sohuott.tv.vod.presenter.UpdatePresenterImpl
//import com.sohuott.tv.vod.presenter.launcher.TitlePresenter
//import com.sohuott.tv.vod.ui.ExitAppDialogNew
//import com.sohuott.tv.vod.ui.ExitAppDialogNew.ExitAppListener
//import com.sohuott.tv.vod.utils.ActivityLauncher
//import com.sohuott.tv.vod.utils.ParamConstant
//import com.sohuott.tv.vod.utils.SyncHistoryAndCollectionUtil
//import com.sohuott.tv.vod.utils.UpdateHelper
//import com.sohuott.tv.vod.utils.UpdateHelper.ExistListener
////import com.sohuott.tv.vod.video.component.AdStartImageControlView
//import com.sohuott.tv.vod.videodetail.activity.control.OnAdStartControlCallBack
//import com.sohuott.tv.vod.view.AboutView
//import com.sohuott.tv.vod.view.MsgPopDialog
//import com.sohuott.tv.vod.view.PrivacyDialog
//import com.sohuott.tv.vod.view.TeenModePopDialog
//import com.sohuott.tv.vod.widget.lb.TopViewBar
//import com.sohuott.tv.vod.widget.lb.TopViewBar.OnTopViewBarInteractionListener
//import com.sohuott.tv.vod.worker.UploadHandler
//import com.sohuvideo.base.config.Constants
//import com.sohuvideo.base.log.LogManager
//import com.sohuvideo.base.utils.AdTsManger.Companion.getInstants
//import io.reactivex.Observer
//import io.reactivex.disposables.Disposable
//import io.reactivex.observers.DisposableObserver
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.GlobalScope
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.withContext
//import org.greenrobot.eventbus.EventBus
//import org.greenrobot.eventbus.Subscribe
//import java.lang.ref.WeakReference
//import java.text.DateFormat
//import java.text.ParseException
//import java.text.SimpleDateFormat
//import java.util.Collections
//import java.util.Date
//import kotlin.jvm.internal.ClassBasedDeclarationContainer
//import kotlin.reflect.KClass
//
///**
// * Created by music on 2021/8/31.
// * Leanback框架首页
// */
//@Route(path = RouterPath.Home.LAUNCHER_ACTIVITY)
//class LauncherActivity : BaseFragmentActivity(), HomeContentFragment.OnFragmentInteractionListener,
//    OnTopViewBarInteractionListener, AboutView, ExistListener,
//    MediaPlayer.OnErrorListener,
//    MediaPlayer.OnPreparedListener, MediaPlayer.OnCompletionListener, MediaPlayer.OnInfoListener,
//    OnAdStartControlCallBack {
//    private var adCommon: AdCommon? = null
//    private var mLauncherHandler: LauncherHandler? = null
//    private var mBinding: ActivityLauncherBinding? = null
//    private var launcherViewModel: LauncherViewModel? = null
//    private var mCurrentTabType = 0
//
//    //获取当前的tabcode
//    var currentTabCode: Long = 0
//        private set
//    private var mOldPosition = -1
//    private var mOldTime: Long = 0
//    private var mCurrentPosition = 0
//    private var mLastPostion = 0
//    var isFirst = true
//    private var mIsGoToMine = false
//    var rootView: View? = null
//        private set
//    var arrayObjectAdapter: ArrayObjectAdapter? = null
//        private set
//    private var mItemBridgeAdapter: LauncherTabBridgeAdapter? = null
//
//    //    private var mViewPagerAdapter: ContentViewPagerAdapter? = null
//    private var mViewPagerAdapter: LauncherViewPagerAdapter? = null
//
//    private var dataBeans: MutableList<HomeTab.TabItem?>? = null
//    private var mNewAnim: ValueAnimator? = null
//    private var mCurrentPageIndex = 0
//    var isSkipTab = false
//        private set
//    private var mHelper: LoginUserInformationHelper? = null
//    private var mUpdateHelper: UpdateHelper? = null
//    private var mUpdatePresenter: UpdatePresenter? = null
//    private var mLlPermissionsTips: LinearLayout? = null
//    private var isShowUpdate = true
//    private var mTeenModePopDialog: TeenModePopDialog? = null
//    private var mMsgPopDialog: MsgPopDialog? = null
//
//    //频道皮肤配置
//    private val mSkinMaps = HashMap<Int, String?>()
//
//    //频道置灰配置
//    private val mGrayMaps = HashMap<Int, Boolean>()
//    private var isOpenDlna = true
//    var dialogShowing = true
//        private set
//    var drawableCrossFadeFactory =
//        DrawableCrossFadeFactory.Builder(300).setCrossFadeEnabled(true).build()
//    val horizontalGridView: HorizontalGridView?
//        get() = mBinding!!.hgTitle
//    val topViewBar: TopViewBar
//        get() = mBinding!!.topBar
//
//    private fun hideBootFragment() {
//        val fragment =
//            supportFragmentManager.findFragmentById(R.id.fragment_container) as BootFragment?
//        if (fragment != null) {
//            supportFragmentManager.beginTransaction()
//                .remove(fragment)
//                .commit()
//            AppLogger.d("hide bootfragment")
//        } else {
//            AppLogger.d("no need hide bootfragment")
//            return
//        }
//    }
//
////    override fun onHideFragment(adCommon: AdCommon) {
////        this.adCommon = adCommon
////        if (!requestPermission() && isTopView || !isTopView) {
////            forceRefresh = true
////            initData()
////        }
////        hideBootFragment()
////        SouthMediaUtil.southNewMediaCheck(this)
////        //        requestPermission();
////        if (adCommon != null && adCommon.isTopView) {
////            AppLogger.d("start topview")
////
////            //不需要授权页面时，启动转场动画
//////            if (mBinding.llPermissionsTips.getVisibility() != View.VISIBLE) {
//////                startTransImgAnim();
//////                AppLogger.d("startTopViewVideo 1");
//////                this.showTransImg = false;
//////                startTopViewVideo();
//////                new Handler().postDelayed(new Runnable() {
//////                    @Override
//////                    public void run() {
//////                        if (mViewPagerAdapter.getFragment(mCurrentPageIndex) != null
//////                                && mViewPagerAdapter.getFragment(mCurrentPageIndex) instanceof HomeContentFragment) {
//////                            HomeContentFragment fragment = (HomeContentFragment) mViewPagerAdapter.getFragment(mCurrentPageIndex);
//////                            fragment.refreshFirstScreenContent();
//////                        }
//////                    }
//////                }, 2000);
//////            }
////        } else {
////            requestUpdateDialog()
////        }
////        adPreLoad()
////    }
//
//    //广告预加载
//    private fun adPreLoad() {
//        try {
//            val loader = SdkFactory.getInstance().createOralAdLoader(this)
//            val component = RequestComponent()
//            component.tuv = UserInfoHelper.getGid()
//            component.site = "1"
//            loader.adTs(component)
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//    }
//
//    fun homeDataPreLoad() {
//        //有权限，不是第一次进入App，在获取广告数据后，提前加载首页
////        if (XXPermissions.isGranted(
////                this,
////                *Permission.Group.STORAGE
////            ) && adOpenControlView != null && adOpenControlView?.isTopView == true
////        ) {
////            initData()
////        }
//    }
//
//    fun restoreFragment() {
//        val animation =
//            TranslateAnimation(0f, 0f, resources.getDimensionPixelSize(R.dimen.y688).toFloat(), 0f)
//        animation.duration = 1000 // 设定动画的时长为1000毫秒(1秒)
//
//        // 设定动画结束后的位置保持为动画的终点，不然动画结束后会回到初始位置
//        animation.fillAfter = true
//        mBinding!!.viewpagerContent.startAnimation(animation)
//        if (dataBeans != null && mViewPagerAdapter!!.getFragment(mCurrentPageIndex) != null && mViewPagerAdapter!!.getFragment(
//                mCurrentPageIndex
//            ) is HomeContentFragment
//        ) {
//            forceRefresh = true
//            (mViewPagerAdapter!!.getFragment(mCurrentPageIndex) as HomeContentFragment).refreshDataContent()
//        }
//        requestUpdateDialog()
//    }
//
//    private fun finishTopViewOnLauncher() {
//        if (mBinding!!.videoRoot.visibility == View.GONE) return
//        mBinding!!.videoRoot.visibility = View.GONE
//        restoreFragment()
//        if (countDownTimer != null) {
//            countDownTimer!!.cancel()
//        }
//        //        releasePlayer();
////        if (showTransImg) {
////            requestUpdateDialog();
////        }
//    }
//
//    override fun onCompletion(mediaPlayer: MediaPlayer) {
//        AppLogger.d("topview onCompletion $adCommon")
//        if (adCommon != null) Advert.getInstance()
//            .reportStartPageTopViewAd(adCommon, ILoader.TopViewState.PLAY_END)
//        finishTopViewOnLauncher()
//        releasePlayer()
//    }
//
//    private fun releasePlayer() {
//        if (videoView != null) {
//            videoView!!.stopPlayback()
//            videoView = null
//        }
//        //        if (mediaPlayer != null) {
////            mediaPlayer.release();
////            mediaPlayer = null;
////        }
//    }
//
//    override fun onError(mediaPlayer: MediaPlayer, i: Int, i1: Int): Boolean {
//        AppLogger.d("topview onError i: $i, i1:$i1")
//        mBinding!!.videoRoot.visibility = View.GONE
//        if (adCommon != null) Advert.getInstance()
//            .reportStartPageTopViewAd(adCommon, ILoader.TopViewState.PLAY_END)
//        finishTopViewOnLauncher()
//        releasePlayer()
//        return true
//    }
//
//    override fun onPrepared(mediaPlayer: MediaPlayer) {
//        AppLogger.d("topview onPrepared $adCommon")
//        if (adCommon != null) Advert.getInstance()
//            .reportStartPageTopViewAd(adCommon, ILoader.TopViewState.PLAY_START)
//        videoDuration = mediaPlayer.duration.toLong()
//        startCountDown(videoDuration)
//    }
//
//    private var countDownTimer: CountDownTimer? = null
//    private var videoDuration: Long = 0 //视频长度
//    private fun startCountDown(duration: Long) {
//        if (videoView != null) {
//            videoView!!.setBackgroundResource(R.drawable.transparent)
//        }
//        countDownTimer = object : CountDownTimer(duration + 1000, 1000) {
//            // 每1000毫秒更新一次
//            override fun onTick(millisUntilFinished: Long) {
//                //倒计时 #FF614E
////                mBinding.countDown.setText("按 返回键 关闭广告 " + String.valueOf(millisUntilFinished / 1000) + "s");
////                AppLogger.d("播放时间： " + (videoDuration - millisUntilFinished) + " ms");
//                if (millisUntilFinished / 1000f < 1) {
//                    mBinding!!.countDown.visibility = View.GONE
//                } else {
//                    mBinding!!.countDown.text = Util.getBootCountDownBackText(millisUntilFinished)
//                }
//                Advert.getInstance().reportPageAdPlayTime(
//                    adCommon,
//                    (videoDuration - millisUntilFinished).toInt() / 1000,
//                    ILoader.TopViewSource.VIDEO_SECOND
//                )
//            }
//
//            override fun onFinish() {}
//        }.start()
//    }
//
//    override fun onInfo(mp: MediaPlayer, what: Int, extra: Int): Boolean {
//        AppLogger.d("onInfo what=$what , extra=$extra")
//        if (what == MediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START && videoView != null) videoView!!.setBackgroundResource(
//            R.drawable.transparent
//        )
//        return true
//    }
//
//    private class LauncherHandler(activity: LauncherActivity) : Handler() {
//        private val weakReference: WeakReference<LauncherActivity>
//
//        init {
//            weakReference = WeakReference(activity)
//        }
//
//        override fun handleMessage(msg: Message) {
//            val launcherActivity = weakReference.get()
//            if (launcherActivity != null) {
//                when (msg.what) {
//                    SELECT -> {
//                        AppLogger.d("handle message : position : " + msg.arg1)
//                        launcherActivity.setCurrentItemPosition(msg.arg1)
//                    }
//                }
//            }
//            super.handleMessage(msg)
//        }
//
//        companion object {
//            const val SELECT = 0x1
//            const val SELECT_DURATION = 500
//        }
//    }
//
//    override fun onStart() {
//        super.onStart()
//        //        IOralAdLoader loader = SdkFactory.getInstance().createOralAdLoader(this);
////        RequestComponent component = new RequestComponent();
////        component.setTuv(DeviceConstant.getInstance().getGID());
////        component.setSite("1");
////        loader.adTs(component);
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
////        Debug.startMethodTracing(SimpleDateFormat("dd_MM_yyyy_hh_mm_ss").format(Date()))
//        mBinding = ActivityLauncherBinding.inflate(layoutInflater)
//        launcherViewModel = ViewModelProvider(this).get(
//            LauncherViewModel::class.java
//        )
//        setContentView(mBinding!!.root)
//        initView()
//        onCreateInit()
//        // 检查是否已经添加了Fragment，例如在配置更改后
////        if (savedInstanceState == null) {
////            supportFragmentManager.beginTransaction()
////                .replace(R.id.fragment_container, BootFragment())
////                .commit()
////        }
//        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
//        initIntent()
//        //        initData();
//        initListener()
//        //        requestPermission();
////        startImageInfoUpload();
//        EventBus.getDefault().register(this)
//
//
////        HomeData.getLocationConfigInfo(this);
//    }
//
//    private fun startImageInfoUpload() {
//        AppLogger.e("musicyy startImageInfoUpload")
//        val imageUploader = UploadHandler(this)
//        imageUploader.startWork()
//    }
//
//    private fun requestLauncherConfig() {
//        NetworkApi.getLauncherConfig(object : DisposableObserver<LauncherConfig?>() {
//            override fun onNext(value: LauncherConfig?) {
//                AppLogger.d(value.toString())
//                if (value == null) {
//                    return
//                }
//                val mCateCodeMaps = HashMap<Int, Boolean>()
//                val mPlayListIdMaps = HashMap<Int, Boolean>()
//                isOpenDlna = value.data.isOpenDlna
//                setIsOpenDlna()
//                Util.setDynamicVideoParams(applicationContext, value.data.isOpenDynamicVideo)
//                for ((channel) in value.data.grayList) {
//                    mGrayMaps[channel] = true
//                    //                    if (mCurrentTabCode == grayInfo.getChannel()) {
////                        changeGray(true);
////                    }
//                }
//                for ((playlistid) in value.data.jumpFull.playlistidList) {
//                    mPlayListIdMaps[playlistid] = true
//                }
//                for ((catecode) in value.data.jumpFull.catecodeList) {
//                    mCateCodeMaps[catecode] = true
//                }
//                LauncherManager.getInstance().setCateCodeMap(mCateCodeMaps)
//                LauncherManager.getInstance().setPlayListIdMap(mPlayListIdMaps)
//            }
//
//            override fun onError(e: Throwable) {
//                val mCateCodeMaps = HashMap<Int, Boolean>()
//                val mPlayListIdMaps = HashMap<Int, Boolean>()
//                LauncherManager.getInstance().setCateCodeMap(mCateCodeMaps)
//                LauncherManager.getInstance().setPlayListIdMap(mPlayListIdMaps)
//                setIsOpenDlna()
//            }
//
//            override fun onComplete() {}
//        })
//    }
//
//    private fun setIsOpenDlna() {
//        SohuDlnaManger.getInstance().isOpen = isOpenDlna
//        SohuDlnaManger.getInstance().initDlna(this@LauncherActivity)
//    }
//
//    private fun requestPermission(): Boolean {
//        if (Build.VERSION.SDK_INT < 23 || XXPermissions.isGranted(
//                this,
//                *Permission.Group.STORAGE
//            )
//        ) {
//            mLlPermissionsTips!!.visibility = View.GONE
//            AppLogger.d("startTopViewVideo 1")
//            startTopViewVideo()
//            return true
//        } else {
//            XXPermissions.with(this).permission(*Permission.Group.STORAGE)
//                .interceptor(object : IPermissionInterceptor {
//                    override fun requestPermissions(
//                        activity: Activity,
//                        allPermissions: List<String>,
//                        callback: OnPermissionCallback
//                    ) {
//                        AppLogger.d("XXPermissions requestPermissions")
//                        if (!PrefUtil.getBoolean(
//
//                                "XXPermissions_never",
//                                false
//                            )
//                        ) {
//                            mLlPermissionsTips!!.visibility = View.VISIBLE
//                        }
//                        super<IPermissionInterceptor>.requestPermissions(
//                            activity,
//                            allPermissions,
//                            callback
//                        )
//                    }
//
//                    override fun grantedPermissions(
//                        activity: Activity,
//                        allPermissions: List<String>,
//                        grantedPermissions: List<String>,
//                        all: Boolean,
//                        callback: OnPermissionCallback
//                    ) {
//                        AppLogger.d("XXPermissions grantedPermissions")
//                        AppLogger.d("startTopViewVideo 2")
//                        startTopViewVideo()
//                        super<IPermissionInterceptor>.grantedPermissions(
//                            activity,
//                            allPermissions,
//                            grantedPermissions,
//                            all,
//                            callback
//                        )
//                    }
//
//                    override fun deniedPermissions(
//                        activity: Activity,
//                        allPermissions: List<String>,
//                        deniedPermissions: List<String>,
//                        never: Boolean,
//                        callback: OnPermissionCallback
//                    ) {
//                        AppLogger.d("XXPermissions deniedPermissions")
//                        AppLogger.d("startTopViewVideo 3")
//                        startTopViewVideo()
//                        super<IPermissionInterceptor>.deniedPermissions(
//                            activity,
//                            allPermissions,
//                            deniedPermissions,
//                            never,
//                            callback
//                        )
//                    }
//                }).request(object : OnPermissionCallback {
//                    override fun onGranted(permissions: List<String>, all: Boolean) {
//                        AppLogger.d("XXPermissions onGranted is all $all")
//                        mLlPermissionsTips!!.visibility = View.GONE
//                    }
//
//                    override fun onDenied(permissions: List<String>, never: Boolean) {
//                        AppLogger.d("XXPermissions onDenied is never:$never")
//                        PrefUtil.putBoolean("XXPermissions_never", never)
//                        mLlPermissionsTips!!.visibility = View.GONE
//                    }
//                })
//        }
//        return false
//    }
//
//    private fun changeGray(flag: Boolean) {
//        val paint = Paint()
//        val cm = ColorMatrix()
//        if (flag) {
//            cm.setSaturation(0f) //灰度效果
//            paint.colorFilter = ColorMatrixColorFilter(cm)
//            window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, paint)
//        } else {
//            window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
//        }
//    }
//
//    private fun requestUpdateDialog() {
//        if (!isShowUpdate) {
//            return
//        }
//        if (Util.getPartnerNo(this) != Constant.PARTNER_NO_XIAOMI_STORE_CHANNEL) {
//            HomeData.sIsHomeCreate = true
//            mUpdatePresenter = UpdatePresenterImpl(this, this)
//            mUpdateHelper = UpdateHelper(this, mUpdatePresenter)
//            mUpdateHelper!!.setExistListener(this)
//            mUpdatePresenter?.getUpdateInfo()
//        }
//    }
//
//    @Suppress("UPPER_BOUND_VIOLATED")
//    private val <T> KClass<T>.javaNull: Class<T?>
//        @JvmName("getJavaClass")
//        get() = (this as ClassBasedDeclarationContainer).jClass as Class<T?>
//
//    private fun requestTeenModePopDialog() {
//        val classOfT: Class<TeenModePopDialogBean?> = TeenModePopDialogBean::class.javaNull
//        NetworkApi.getWithCache(
//            this,
//            object : Observer<com.sohuott.tv.vod.lib.model.launcher.TeenModePopDialogBean?> {
//                override fun onSubscribe(d: Disposable) {}
//                override fun onNext(value: com.sohuott.tv.vod.lib.model.launcher.TeenModePopDialogBean?) {
//                    if (value != null && value.data != null) {
//                        val lastAlertTeenModeDate = PrefUtil.getString(
//                            Constants.KEY_LAST_ALERT_TEEN_MODE_DATE,
//                            ""
//                        )
//                        LogManager.d(TAG, "lastAlertTeenModeDate ? $lastAlertTeenModeDate")
//                        LogManager.d(TAG, "value.data.isAlert ? " + value.data.frequency)
//                        //CMS后台配置控制青少年模式弹框展示次数，3 ： 每天都弹，2 ： 两周弹1次，1 ：每周弹1次 ，0：不弹；默认展示级别为3。
//                        var days = 15 //默认没有弹过，等效超过两周的天数
//                        if (StringUtils.isNotEmptyStr(lastAlertTeenModeDate)) {
//                            val date = Date()
//                            val dft: DateFormat = SimpleDateFormat("yyyy-MM-dd")
//                            val todayStr = dft.format(date)
//                            LogManager.d(TAG, "todayStr ? $todayStr")
//                            try {
//                                val startDay = dft.parse(lastAlertTeenModeDate) //开始时间
//                                val endDay = dft.parse(todayStr) //结束时间
//                                days = DateUtils.getDays(startDay, endDay)
//                                LogManager.d(TAG, "try days ? $days")
//                            } catch (e: ParseException) {
//                                e.printStackTrace()
//                            }
//                        }
//                        LogManager.d(TAG, "days ? $days")
//                        //                    CMS后台配置控制青少年模式弹框展示次数，3 ： 每天都弹，2 ： 两周弹1次，1 ：每周弹1次 ，0：不弹；默认展示级别为3。
//                        if (value.data.frequency_2 == 3) {
//                            if (days >= 1) {
//                                showTeenModePopDialog(value)
//                            } else {
//                                requestMsgDialogData()
//                            }
//                        } else if (value.data.frequency_2 == 2) {
//                            if (days >= 14) {
//                                showTeenModePopDialog(value)
//                            } else {
//                                requestMsgDialogData()
//                            }
//                        } else if (value.data.frequency_2 == 1) {
//                            if (days >= 7) {
//                                showTeenModePopDialog(value)
//                            } else {
//                                requestMsgDialogData()
//                            }
//                        } else if (value.data.frequency_2 == 0) {
//                            //不弹
//                            requestMsgDialogData()
//                        } else if (value.data.frequency_2 == 4) {
//                            //每次都弹
//                            showTeenModePopDialog(value)
//                        } else {
//                            if (days >= 1) {
//                                showTeenModePopDialog(value)
//                            } else {
//                                requestMsgDialogData()
//                            }
//                        }
//                    } else {
//                        requestMsgDialogData()
//                    }
//                }
//
//                override fun onError(e: Throwable) {}
//                override fun onComplete() {}
//            },
//            NetworkApi.networkInterface.teenModePopupData,
//            classOfT
//        )
//    }
//
//    private fun showTeenModePopDialog(teenModePopup: com.sohuott.tv.vod.lib.model.launcher.TeenModePopDialogBean) {
//        mTeenModePopDialog = TeenModePopDialog.Builder(this)
//            .setTitle(teenModePopup.data.title)
//            .setMsg(teenModePopup.data.pop_text)
//            .setCancelListener { dialog ->
//                dialog.dismiss()
//                mTeenModePopDialog = null
//                requestMsgDialogData()
//            }
//            .setPositiveButton(
//                R.string.open_immediately
//            ) {
//                val intent = Intent(this@LauncherActivity, TeenModeDescActivity::class.java)
//                intent.putExtra(RenewActivity.PARAM_AC_TYPE, RenewActivity.ACTYPE_RENEW)
//                <EMAIL>(intent)
//                mTeenModePopDialog!!.dismiss()
//                mTeenModePopDialog = null
//                TeenagersManger.getInstance().exposureShowDecDialogClickOpen()
//                requestMsgDialogData()
//            }
//            .setNegativeButton(
//                R.string.iknow
//            ) {
//                if (mTeenModePopDialog != null) {
//                    mTeenModePopDialog!!.dismiss()
//                    mTeenModePopDialog = null
//                }
//                TeenagersManger.getInstance().exposureShowDecDialogClickKnow()
//                requestMsgDialogData()
//            }.show()
//        val date = Date()
//        val dft: DateFormat = SimpleDateFormat("yyyy-MM-dd")
//        val todayStr = dft.format(date)
//        LogManager.d(TAG, "before put todayStr ? $todayStr")
//        PrefUtil.putString(Constants.KEY_LAST_ALERT_TEEN_MODE_DATE, todayStr)
//        LogManager.d(TAG, "after put todayStr ? $todayStr")
//    }
//
//    private fun requestMsgDialogData() {
//        NetworkApi.getMessageData(PAGE, PAGE_SIZE, object : DisposableObserver<ServerMessage?>() {
//            override fun onNext(value: ServerMessage?) {
//                try {
//                    var msgList: ArrayList<ServerMessage.Data>? = null
//                    if (value != null && value.status == 0 && value.data != null && value.data.size > 0) {
//                        msgList = value.data
//                        AppLogger.d("There are other data!")
//                    }
//                    val popupMsgList = ArrayList<ServerMessage.Data>()
//                    if (msgList != null && msgList.size > 0) {
//                        for (msgData in msgList) {
//                            try {
//                                //CMS后台消息类型type=0（默认），并勾选弹窗展示选项
//                                val type = msgData.type
//                                LogManager.d(TAG, "type ? $type")
//                                if ("0" == type) {
//                                    val parameeterObj = Gson()
//                                    val parameter = parameeterObj.fromJson(
//                                        msgData.parameter, ServerMessage.Parameter::class.java
//                                    )
//                                    if (parameter.isPopup) {
//                                        popupMsgList.add(msgData)
//                                    }
//                                }
//                            } catch (e: Exception) {
//                                LogManager.d(TAG, "e ? $e")
//                            }
//                        }
//                    }
//                    if (popupMsgList.size > 0) {
//                        Collections.sort(popupMsgList) { data1, data2 ->
//                            val date1 = FormatUtils.strToDate(data1.createTime)
//                            val date2 = FormatUtils.strToDate(data2.createTime)
//                            date2.compareTo(date1)
//                        }
//                        val latestPupupMsg = popupMsgList[0]
//                        LogManager.d(TAG, "latestPupupMsg.createTime" + latestPupupMsg.createTime)
//                        LogManager.d(TAG, "latestPupupMsg.name" + latestPupupMsg.name)
//                        LogManager.d(TAG, "latestPupupMsg.content" + latestPupupMsg.content)
//                        val lastMsgDatetime = PrefUtil.getString(
//                            Constants.LAST_MSG_DATETIME,
//                            ""
//                        )
//                        if (StringUtils.isEmptyStr(lastMsgDatetime)) {
//                            showMsgPopDialog(latestPupupMsg)
//                        } else {
//                            LogManager.d(TAG, "lastMsgDatetime$lastMsgDatetime")
//                            LogManager.d(
//                                TAG,
//                                "latestPupupMsg.createTime" + latestPupupMsg.createTime
//                            )
//                            val date1 = FormatUtils.strToDate(lastMsgDatetime)
//                            val date2 = FormatUtils.strToDate(latestPupupMsg.createTime)
//                            if (date2.compareTo(date1) > 0) {
//                                showMsgPopDialog(latestPupupMsg)
//                            } else {
//                                dialogShowing = false
//                                //                                if (!showTransImg) {
////                                    AppLogger.d("startTopViewVideo 3");
////                                    startTopViewVideo();
////                                }
//                                AppLogger.d("There is no new msg! isDialogShowing = " + dialogShowing)
//                            }
//                        }
//                    }
//                } catch (e: Exception) {
//                    LogManager.d(TAG, "e ? $e")
//                }
//            }
//
//            override fun onError(e: Throwable) {
//                AppLogger.d("onErrorResponse, error = $e")
//            }
//
//            override fun onComplete() {
//                AppLogger.d("getMessage() onComplete")
//            }
//        })
//    }
//
//    private fun showMsgPopDialog(latestPupupMsg: ServerMessage.Data) {
//        mMsgPopDialog = MsgPopDialog.Builder(this)
//            .setTitle(latestPupupMsg.name)
//            .setMsg(latestPupupMsg.content)
//            .setCancelListener { dialog: DialogInterface ->
//                dialog.dismiss()
//                mMsgPopDialog = null
//                dialogShowing = false
//            }
//            .setPositiveButton(
//                R.string.see_immediately
//            ) { v: View? ->
//                ActivityLauncher.startMyMessageActivity(this@LauncherActivity)
//                val path = HashMap<String, String>(1)
//                path["pageId"] = "1038"
//                RequestManager.getInstance().onAllEvent(EventInfo(10252, "clk"), path, null, null)
//            }
//            .setNegativeButton(
//                R.string.close
//            ) { v: View? ->
//                mMsgPopDialog!!.dismiss()
//                mMsgPopDialog = null
//                dialogShowing = false
//
////                            if (!showTransImg) {
////                                AppLogger.d("startTopViewVideo 5");
////                                startTopViewVideo();
////                            }
//                val path = HashMap<String, String>(1)
//                path["pageId"] = "1038"
//                RequestManager.getInstance().onAllEvent(EventInfo(10253, "clk"), path, null, null)
//            }.show()
//        PrefUtil.putString(
//            Constants.LAST_MSG_DATETIME,
//            latestPupupMsg.createTime
//        )
//    }
//
//
//    fun startTopViewVideo() {
//        AppLogger.d("startTopViewVideo adcommon $adCommon")
////        if (adOpenControlView!!.isTopView && mTopVideoView == null) {
////            val animation = TranslateAnimation(
////                0f,
////                0f,
////                0f,
////                resources.getDimensionPixelSize(R.dimen.y688).toFloat()
////            )
////            animation.duration = 1000
////            animation.fillAfter = true
////            mBinding!!.viewpagerContent.startAnimation(animation)
////            //todo 替换xml 中引入
////            mBinding!!.videoRoot.visibility = View.VISIBLE
////            mTopVideoView = SoHuVideoView(this).apply {
////            }
//////            mTopVideoView=mBinding!!.videoView
////            mBinding!!.videoRoot.addView(mTopVideoView)
//////            mTopVideoView?.setPlayUrl(adOpenControlView?.topViewPath)
////            mTopVideoView?.setVideoParams(VideoParams().also {
////                it.adSkip = true
////            })
////            mTopVideoView?.start()
////            videoView = SplashVideoView(this)
////            videoView!!.setBackgroundResource(R.drawable.launcher_bg)
////            mBinding!!.videoRoot.addView(videoView)
////            videoView!!.setOnCompletionListener(this)
////            videoView!!.setOnErrorListener(this)
////            videoView!!.setOnPreparedListener(this)
////            videoView!!.setOnInfoListener(this)
////            videoView!!.setVideoPath(adCommon!!.focusVideo)
////            videoView!!.start()
////        }
//    }
//
//    var videoView: SplashVideoView? = null
//
//    @Subscribe
//    fun onEventMainThread(event: LogoutEvent?) {
//        if (null == event) {
//            return
//        }
//        LogManager.d(TAG, "LogoutEvent")
//        topData
//    }
//
//    override fun onNewIntent(intent: Intent) {
//        super.onNewIntent(intent)
//        initIntent()
//        val selectTab = intent.getStringExtra(ParamConstant.PARAM_SELECT_TAB)
//        if (TextUtils.isEmpty(selectTab)) {
//            return
//        }
//        if (ParamConstant.PARAM_GO_TO_MINE == selectTab) {
//            horizontalGridView!!.selectedPosition =
//                Constant.TAG_MY_POSITION
//            horizontalGridView!!.requestFocus()
//            refreshTabLayout()
//            topViewBar.zoomOut()
//        } else if (ParamConstant.PARAM_GO_HOME == selectTab) {
//            handleTitleVisible(true)
//            refreshTabLayout()
//            horizontalGridView!!.setSelectedPositionSmooth(Constant.TAG_FEATURE_POSITION)
//        }
//    }
//
//    private fun initIntent() {
//        isShowUpdate = intent.getBooleanExtra(ParamConstant.PARAM_IS_SHOW_UPDATE, true)
//        val selectTab = intent.getStringExtra(ParamConstant.PARAM_SELECT_TAB)
//        if (ParamConstant.PARAM_GO_TO_MINE == selectTab) {
//            mIsGoToMine = true
//        }
//    }
//
//    override fun onResume() {
//        super.onResume()
//        AppLogger.d("onResume")
//        if (XXPermissions.isGranted(this, *Permission.Group.STORAGE)) {
//            topData
//            if (mHelper!!.isLogin) {
//                //refreshUser
//                UserApi.getUserAdPayMsg(this)
//            }
//            mBinding!!.topBar.refreshTopData()
//        }
//    }
//
//    private fun initView() {
////        mBinding!!.viewpagerContent.offscreenPageLimit = 1
//        arrayObjectAdapter = ArrayObjectAdapter(TitlePresenter())
//        mItemBridgeAdapter = LauncherTabBridgeAdapter(arrayObjectAdapter)
////        mViewPagerAdapter = ContentViewPagerAdapter(supportFragmentManager)
//        mBinding!!.hgTitle.adapter = mItemBridgeAdapter
//        mHelper = LoginUserInformationHelper.getHelper(applicationContext)
//        rootView = (findViewById<View>(android.R.id.content) as ViewGroup).getChildAt(0)
//        mNewAnim = ValueAnimator.ofInt(0, 255)
//        mLauncherHandler = LauncherHandler(this)
//        mLlPermissionsTips = findViewById<View>(R.id.ll_permissions_tips) as LinearLayout
//    }
//
//    private fun initTab(tabList: MutableList<HomeTab.TabItem?>?) {
//        dataBeans = tabList
//        //查找联播type 并移除
//        tabList?.find {
//            it?.type == Constant.TYPE_CAROUSEL
//        }?.let {
//            dataBeans?.remove(it)
//        }
//        //查找我的页面和 展示首页Type 位置
//        dataBeans?.indices?.forEach { index ->
//            val data = dataBeans?.get(index)
//            if (data?.isFirstPage == 1) {
//                Constant.TAG_FEATURE_POSITION = index
//                data.isSelected = true
//                return
//            }
//            if (data?.type == 107) {
//                Constant.TAG_MY_POSITION = index
//            }
//        }
//
//    }
//
//    private fun initData() {
//        startImageInfoUpload()
//        requestLauncherConfig()
//        launcherViewModel!!.getChannelList(this)
//        launcherViewModel!!.homeChannelData.observe(this) { result: ResultData<HomeTab?>? ->
//            if (result is ResultData.Success<HomeTab?>) {
//                if (result.data?.data.isNullOrEmpty()) {
//                    AppLogger.d("错误码:$result?.data?.status ")
//                    setErrorTVVisible()
//                    return@observe
//                }
//                initTab(result.data?.data)
////                mViewPagerAdapter = LauncherViewPagerAdapter(this)
////                mViewPagerAdapter?.setData(dataBeans)
////                mBinding?.viewpagerContent?.adapter = mViewPagerAdapter
////                initViewPager()
////                LauncherTabLayoutMediator(
////                    mBinding!!.tabLayoutLauncher,
////                    mBinding!!.viewpagerContent,
////                    object : LauncherTabLayoutMediator.TabConfigurationStrategy {
////                        override fun onConfigureTab(tab: TabLayout.Tab, position: Int) {
////                            AppLogger.e("LauncherTabLayoutMediator onConfigureTab ${position}")
////                            val mTvMainTitle =
////                                tab.customView?.findViewById<TextView>(R.id.tv_main_title)
////                            val mImgMainTitle =
////                                tab.customView?.findViewById<ImageView>(R.id.img_main_title)
////                            val mImgMainTitle2 =
////                                tab.customView?.findViewById<ImageView>(R.id.img_main_title2)
////                            val mImgLine = tab.customView?.findViewById<View>(R.id.line_main_title)
////                            val data = dataBeans?.get(position)
////                            val isVipTab = data?.type == 103 || data?.name?.contains("会员") == true
////                            if (isVipTab) {
////                                mTvMainTitle?.visibility = View.GONE
////                                mImgMainTitle?.visibility = View.VISIBLE
////                                if (data!!.isSelected) {
////                                    mImgMainTitle?.setBackgroundResource(R.drawable.channel_vip_focused)
////                                } else {
////                                    mImgMainTitle?.setBackgroundResource(R.drawable.channel_vip_default)
////                                }
////                                tab.customView?.background = null
////                            } else {
////                                if (!TextUtils.isEmpty(data!!.picUrl)) {
////                                    mImgMainTitle?.let {
////                                        Glide.with(this@LauncherActivity)
////                                            .load(data.picUrl)
////                                            .into(it)
////                                    }
////                                    mImgMainTitle?.visibility = View.VISIBLE
////                                    if (!TextUtils.isEmpty(data.picUrl2)) {
////                                        mImgMainTitle2?.let {
////                                            Glide.with(this@LauncherActivity)
////                                                .load(data.picUrl2)
////                                                .into(it)
////                                        }
////                                        mImgMainTitle2?.visibility = View.INVISIBLE
////                                    }
////                                } else {
////                                    mTvMainTitle?.text = data.name
////                                    mTvMainTitle?.paint?.isFakeBoldText = true
////                                    mImgLine?.visibility = View.INVISIBLE
////                                    mTvMainTitle?.setTextColor(
////                                        resources.getColor(R.color.bg_channel_list_default)
////                                    )
////                                }
////                            }
////
////                            tab.customView?.onFocusChangeListener =
////                                OnFocusChangeListener { v: View?, hasFocus: Boolean ->
////                                    //focus
////                                    if (hasFocus) {
////                                        if (isVipTab) {
////                                            mImgMainTitle?.setBackgroundResource(R.drawable.channel_vip_focused)
////                                            mImgMainTitle?.visibility = View.VISIBLE
////                                        } else {
////                                            mImgMainTitle?.visibility = View.VISIBLE
////                                            mImgMainTitle2?.visibility = View.INVISIBLE
////                                            mImgLine?.visibility = View.INVISIBLE
////                                            mTvMainTitle?.setTextColor(
////                                                resources.getColor(R.color.bg_channel_list_focus)
////                                            )
////                                        }
////                                    } else {
////                                        //selected
////                                        if (data.isSelected) {
////                                            if (isVipTab) {
////                                                mImgMainTitle?.setBackgroundResource(R.drawable.channel_vip_selected)
////                                                mImgMainTitle?.visibility = View.VISIBLE
////                                            } else {
////                                                if (!TextUtils.isEmpty(data.picUrl2)) {
////                                                    mImgMainTitle?.visibility = View.INVISIBLE
////                                                    mImgMainTitle2?.visibility = View.VISIBLE
////                                                } else {
////                                                    mTvMainTitle?.setTextColor(
////                                                        resources
////                                                            .getColor(R.color.bg_channel_list_select)
////                                                    )
////                                                    mTvMainTitle?.paint?.isFakeBoldText = true
////                                                    mImgLine?.visibility = View.VISIBLE
////                                                }
////                                            }
////                                        } else {
////                                            //default
////                                            if (isVipTab) {
////                                                mImgMainTitle?.setBackgroundResource(R.drawable.channel_vip_default)
////                                            }
////                                            mImgMainTitle2?.visibility = View.INVISIBLE
////                                            mImgMainTitle?.visibility = View.VISIBLE
////                                            mTvMainTitle?.setTextColor(
////                                                resources
////                                                    .getColor(R.color.bg_channel_list_default)
////                                            )
////                                        }
////                                    }
////                                }
////
////                            tab.customView?.setOnClickListener(
////                                View.OnClickListener { v: View? ->
////                                    if (data.ottCategoryId == 0) return@OnClickListener
////                                    val memoInfo =
////                                        java.util.HashMap<String, String>()
////                                    memoInfo["category"] = StringUtil.toString(data.ottCategoryId)
////                                    RequestManager.getInstance()
////                                        .onAllEvent(
////                                            EventInfo(10161, "clk"),
////                                            null, null, memoInfo
////                                        )
////                                    ActivityLauncher.startGridListActivityWithCatecode(
////                                        this@LauncherActivity,
////                                        data.ottCategoryId,
////                                        data.cateCode.toInt(),
////                                        false,
////                                        data.dataType,
////                                        -1,
////                                        data.order + 1
////                                    )
////                                }
////                            )
////                        }
////                    }
////                ).attach()
////                mBinding?.viewpagerContent?.setCurrentItem(Constant.TAG_FEATURE_POSITION, false)
////                val channelList = (result as ResultData.Success<HomeTab?>).data
////                if (channelList == null || channelList.data == null || channelList.data.size < 1) {
//////                ToastUtils.showToast(LauncherActivity.this, "错误码: " + channelList.getStatus());
////                    AppLogger.d("错误码: " + channelList!!.status)
////                    setErrorTVVisible()
////                    return@observe
////                }
////                var tabItem: HomeTab.TabItem? = null
////                dataBeans = channelList.data.toMutableList()
////                for (data in channelList.data) {
////                    if (data.type == Constant.TYPE_CAROUSEL) {
////                        tabItem = data
////                    }
////                }
////                if (tabItem != null) {
////                    dataBeans?.remove(tabItem)
////                }
////                for (data in channelList.data) {
////                    if (data.isFirstPage == 1) {
////                        Constant.TAG_FEATURE_POSITION = channelList.data.indexOf(data)
////                        data.isSelected = true
////                    }
////                    if (data.type == 107) {
////                        Constant.TAG_MY_POSITION = channelList.data.indexOf(data)
////                    }
////                }
//                GlobalScope.launch {
//                    withContext(Dispatchers.Main) {
//                        setUI()
//                    }
//                }
//                topData
//            } else if (result is ResultData.Error) {
//                setErrorTVVisible()
//            }
//        }
//    }
//
//    private fun initListener() {
//        mBinding!!.hgTitle.addOnChildViewHolderSelectedListener(onChildViewHolderSelectedListener)
//    }
//
//    private val topData: Unit
//        private get() {
//            NetworkApi.getTopData(object : DisposableObserver<TopInfo?>() {
//                override fun onNext(value: TopInfo?) {
//                    AppLogger.d("onNext: $value")
//                    mBinding!!.topBar.setData(value)
//                }
//
//                override fun onError(e: Throwable) {
//                    e.printStackTrace()
//                    AppLogger.d("onError: $e")
//                }
//
//                override fun onComplete() {}
//            }, mHelper!!.loginPassport, mHelper!!.loginToken)
//        }
//
//    private fun setUI() {
//        val adapter = arrayObjectAdapter
//        if (adapter != null) {
//            adapter.addAll(0, dataBeans)
//            initViewPager()
//            val horizontalGridView = horizontalGridView
//            val flag: Int
//            flag = if (mIsGoToMine) {
//                Constant.TAG_MY_POSITION
//            } else {
//                Constant.TAG_FEATURE_POSITION
//            }
//            if (dataBeans!!.size > flag) {
//                if (horizontalGridView != null) {
//                    horizontalGridView.selectedPosition = flag
//                    AppLogger.v("setUI setCurrentItemPosition : $flag, lastPos : $flag ")
//                    setCurrentItemPosition(flag)
//                    mCurrentTabType = dataBeans!![flag]!!.type
//                    currentTabCode = dataBeans!![flag]!!.id
//                    if (java.lang.Boolean.TRUE == mGrayMaps.get(currentTabCode.toInt())) {
//                        changeGray(true)
//                    }
//                }
//            } else if (dataBeans!!.size > 0) {
//                if (this.horizontalGridView != null) {
//                    horizontalGridView!!.setSelectedPositionSmooth(0)
//                }
//            }
//            mIsGoToMine = false
//        }
//    }
//
//    private fun initViewPager() {
//        mViewPagerAdapter = LauncherViewPagerAdapter(this)
//        mViewPagerAdapter!!.setData(dataBeans)
//        mBinding!!.viewpagerContent.adapter = mViewPagerAdapter
//        mBinding!!.viewpagerContent.registerOnPageChangeCallback(object :
//            ViewPager2.OnPageChangeCallback() {
//            override fun onPageScrolled(
//                position: Int,
//                positionOffset: Float,
//                positionOffsetPixels: Int
//            ) {
//            }
//
//            override fun onPageSelected(position: Int) {
//                AppLogger.d("onPageSelected position: $position, lastPos : $mLastPostion , currentPos : $mCurrentPosition")
//                isSkipTab = true
//                if (position == mLastPostion) {
//                    //从viewpager中滑动的
//                    refreshTabLayout()
//                }
//                if (position != mCurrentPageIndex) {
//                    mBinding!!.hgTitle.selectedPosition = position
//                }
//                if (mSkinMaps[dataBeans!![position]!!.type] == null) {
//                    if (dataBeans!![position]!!.type == Constant.TYPE_VIP) {
//                        rootView?.setBackgroundResource(R.drawable.launcher_vip_bg)
//                    } else {
//                        rootView?.setBackgroundResource(R.drawable.launcher_bg)
//                    }
//                    hintNew()
//                } else {
//                    showNewWithAnim(dataBeans!![position]!!.type)
//                }
//                if (java.lang.Boolean.TRUE == mGrayMaps[dataBeans!![position]!!.id.toInt()]) {
//                    changeGray(true)
//                } else {
//                    changeGray(false)
//                }
//                if (mOldPosition == -1) {
//                    val pathInfo = HashMap<String, String>()
//                    pathInfo["pageId"] = StringUtil.toString(dataBeans!![position]!!.id)
//                    val objectInfo = HashMap<String, String>()
//                    objectInfo["type"] = "page"
//                    objectInfo["id"] = StringUtil.toString(dataBeans!![position]!!.id)
//                    val memoInfo = HashMap<String, String>()
//                    memoInfo["lastPage"] = "-1"
//                    memoInfo["stayTime"] = "0"
//                    RequestManager.getInstance().onAllEvent(
//                        EventInfo(10135, "imp"),
//                        pathInfo,
//                        objectInfo,
//                        memoInfo
//                    )
//                } else {
//                    val pathInfo = HashMap<String, String>()
//                    pathInfo["pageId"] = StringUtil.toString(dataBeans!![position]!!.id)
//                    val objectInfo = HashMap<String, String>()
//                    objectInfo["type"] = "page"
//                    objectInfo["id"] = StringUtil.toString(dataBeans!![position]!!.id)
//                    val memoInfo = HashMap<String, String>()
//                    memoInfo["lastPage"] = StringUtil.toString(dataBeans!![mOldPosition]!!.id)
//                    memoInfo["stayTime"] =
//                        StringUtil.toString(System.currentTimeMillis() - mOldTime)
//                    RequestManager.getInstance().onAllEvent(
//                        EventInfo(10135, "imp"),
//                        pathInfo,
//                        objectInfo,
//                        memoInfo
//                    )
//                }
//                mOldTime = System.currentTimeMillis()
//                mOldPosition = position
//            }
//
//            override fun onPageScrollStateChanged(state: Int) {}
//        })
//    }
//
//    fun setErrorTVVisible() {
//        if (mBinding!!.tvError.visibility == View.INVISIBLE) {
//            mBinding!!.tvError.visibility = View.VISIBLE
//            mBinding!!.tvError.text = getString(R.string.home_loading_error)
//        }
//    }
//
//    private fun refreshTabLayout() {
//        arrayObjectAdapter!!.setItems(dataBeans, object : DiffCallback<HomeTab.TabItem>() {
//            override fun areItemsTheSame(
//                oldItem: HomeTab.TabItem,
//                newItem: HomeTab.TabItem
//            ): Boolean {
//                return true
//            }
//
//            override fun areContentsTheSame(
//                oldItem: HomeTab.TabItem,
//                newItem: HomeTab.TabItem
//            ): Boolean {
////                        AppLogger.d("return name"+ ((HomeTab.TabItem)oldItem).name + " " + !(((HomeTab.TabItem)oldItem).index == mCurrentPosition || ((HomeTab.TabItem) oldItem).index == mLastPostion));
//                return !(oldItem.index == mCurrentPosition || oldItem.index == mLastPostion)
//            }
//        })
//    }
//
//    private val onChildViewHolderSelectedListener: OnChildViewHolderSelectedListener =
//        object : OnChildViewHolderSelectedListener() {
//            override fun onChildViewHolderSelected(
//                parent: RecyclerView,
//                child: RecyclerView.ViewHolder?,
//                position: Int,
//                subposition: Int
//            ) {
//                super.onChildViewHolderSelected(parent, child, position, subposition)
//                AppLogger.d("onChildViewHolderSelected mCurrentPosition :$mCurrentPosition mLastPostion : $mLastPostion")
//                if (position == mCurrentPosition) return
//                if (position == -1) return
//                if (dataBeans == null) return
//                mLastPostion = mCurrentPosition
//                mCurrentPosition = position
//                for (i in dataBeans!!.indices) {
//                    if (i != mCurrentPosition) {
//                        dataBeans!![i]!!.isSelected = false
//                    } else {
//                        dataBeans!![position]!!.isSelected = true
//                    }
//                }
//                for (i in dataBeans!!.indices) {
//                    dataBeans!![i]!!.index = i
//                }
//                if (horizontalGridView?.getScrollState() == RecyclerView.SCROLL_STATE_IDLE
//                    && horizontalGridView?.isComputingLayout != true
//                ) {
//                }
//                mLauncherHandler!!.removeMessages(LauncherHandler.SELECT)
//                val msg = Message.obtain()
//                msg.what = LauncherHandler.SELECT
//                msg.arg1 = mCurrentPosition
//                mLauncherHandler!!.sendMessageDelayed(msg, LauncherHandler.SELECT_DURATION.toLong())
//                if (dataBeans != null) {
//                    mCurrentTabType = dataBeans!![position]!!.type
//                    currentTabCode = dataBeans!![position]!!.id
//                }
//            }
//        }
//
//    private fun setCurrentItemPosition(position: Int) {
//        AppLogger.v("setCurrentItemPosition : $position, lastPos : $mLastPostion ， currentPos : $mCurrentPosition")
//        if (position != mLastPostion) {
//            mCurrentPageIndex = position
//            mBinding!!.viewpagerContent.setCurrentItem(position, false)
//        }
//    }
//
//    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
////        val fragment =
////            supportFragmentManager.findFragmentById(R.id.fragment_container) as BootFragment?
////        AppLogger.d(
////            "onKeyDown" +
////                    " mBinding.videoRoot.getVisibility() " + mBinding!!.videoRoot.visibility +
////                    " bootFragment " + fragment
////        )
//
//        //bootfragment is visible 启动广告响应按键
////        if (fragment != null) {
////            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_CENTER || event.getKeyCode() == KeyEvent.KEYCODE_ENTER) {
////                fragment.startPayOrVideoDetailActivity();
////                return true;
////            }
////            if (event.keyCode == KeyEvent.KEYCODE_BACK) {
////                AppLogger.d("onKeyDown KEYCODE_BACK fragment.flag " + fragment.flag + " fragment.isCanBack " + fragment.isCanBack)
////                if (fragment.isCanBack) {
////                    AppLogger.d("onHideFragment startLauncher 1")
////                    fragment.releaseVideoView()
////                    onHideFragment(fragment.mAdCommon)
////                    if (fragment.mAdCommon != null && fragment.mAdCommon.isTopView) {
////                        Advert.getInstance()
////                            .reportPageAdFinish(fragment.mAdCommon, ILoader.PageAdState.SKIP)
////                    }
////                    if (launcherViewModel != null && dataBeans != null && mViewPagerAdapter!!.getFragment(
////                            mCurrentPageIndex
////                        ) != null && mViewPagerAdapter!!.getFragment(mCurrentPageIndex) is HomeContentFragment
////                    ) {
////                        (mViewPagerAdapter!!.getFragment(mCurrentPageIndex) as HomeContentFragment).refreshFirstScreenContent()
////                    }
////                }
////                //在广告页面用户不能主动退出app
////                return true
////            } else if (event.keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
////                if (fragment.flag == 0) {
////                    onHideFragment(fragment.mAdCommon)
////                    AppLogger.d("onHideFragment startLauncher 2")
////                }
////                return true
////            }
////        } else {
//        //Launcher响应
//        //当前topview广告正在播放，响应所有按键
//        if (mBinding!!.videoRoot.visibility == View.VISIBLE) {
//            finishTopViewOnLauncher()
//            releasePlayer()
//            Advert.getInstance().reportPageAdFinish(adCommon, ILoader.PageAdState.CLOSE)
//            return true
//        } else {
//            if (event.action == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_BACK) {
//                val mExitDialog = ExitAppDialogNew(this)
//                if (dataBeans != null && dataBeans!!.size > mCurrentPageIndex) {
//                    mExitDialog.setPageId(dataBeans!![mCurrentPageIndex]!!.id)
//                }
//                mExitDialog.setExitAppListener(object : ExitAppListener {
//                    override fun exitApp() {
//                        saveCurrentQueue()
//                        //                    moveTaskToBack(true);
//                        SohuAppUtil.exitApp(this@LauncherActivity)
//                    }
//
//                    override fun onDismiss() {
////                                mExitDialog = null;
//                    }
//                })
//                if (!mExitDialog.isShowing) {
//                    mExitDialog.show()
//                } else {
//                    mExitDialog.dismiss()
//                }
//                return true
//            }
////            }
//        }
//        return super.onKeyDown(keyCode, event)
//    }
//
//    fun upToTopBar() {
//        topViewBar.zoomIn()
//    }
//
//    override fun onFragmentInteraction(uri: Uri) {
//        when (uri.toString()) {
//            Constant.URI_HIDE_TITLE -> handleTitleVisible(false)
//            Constant.URI_SHOW_TITLE -> handleTitleVisible(true)
//        }
//    }
//
//    private fun handleTitleVisible(isShow: Boolean) {
//        if (isShow) {
//            if (mBinding!!.hgTitle.visibility != View.VISIBLE) {
//                mBinding!!.hgTitle.visibility = View.VISIBLE
//            }
//        } else {
//            if (mBinding!!.hgTitle.visibility != View.GONE) {
//                mBinding!!.hgTitle.visibility = View.GONE
//            }
//        }
//    }
//
//    override fun onTopViewBarInteraction(uri: Uri) {
//        if (uri.toString() == Constant.URI_CLICK_MY) {
//            if (Constant.TAG_MY_POSITION != -1) {
//                horizontalGridView!!.setSelectedPositionSmooth(Constant.TAG_MY_POSITION)
//                horizontalGridView!!.requestFocus()
//                refreshTabLayout()
//                setCurrentItemPosition(Constant.TAG_MY_POSITION);
//            }
//        }
//    }
//
//    fun setBackground(id: Int, url: String?) {
//        mSkinMaps[id] = url
//        if (id.toLong() == currentTabCode) {
//            showNewWithAnim(id)
//        }
//    }
//
//    fun showNewWithAnim(id: Int) {
//        if (mSkinMaps[id] == null) {
//            return
//        }
//        Glide.with(this@LauncherActivity).load(mSkinMaps[id])
//            .transition(DrawableTransitionOptions.with(drawableCrossFadeFactory)).into(
//                mBinding!!.launcherBg
//            )
//        mBinding!!.launcherBg.visibility = View.VISIBLE
//        mNewAnim!!.duration = 700
//        mNewAnim!!.addUpdateListener { animation: ValueAnimator ->
//            val currentValue = animation.animatedValue as Int
//            mBinding!!.launcherBg.imageAlpha = currentValue
//            mBinding!!.launcherBg.requestLayout()
//        }
//        mNewAnim!!.start()
//    }
//
//    fun hintNew() {
//        mNewAnim!!.cancel()
//        mBinding!!.launcherBg.visibility = View.GONE
//    }
//
//    fun backToPosition(pos: Int) {
//        if (horizontalGridView!!.visibility != View.VISIBLE) {
//            horizontalGridView!!.visibility = View.VISIBLE
//        }
//        for (i in dataBeans!!.indices) {
//            if (i != pos) {
//                dataBeans!![i]!!.isSelected = false
//            } else {
//                dataBeans!![pos]!!.isSelected = true
//            }
//        }
//        for (i in dataBeans!!.indices) {
//            dataBeans!![i]!!.index = i
//        }
//        horizontalGridView!!.selectedPosition = pos
//        horizontalGridView!!.requestFocus()
//        refreshTabLayout()
//        if (topViewBar.isZoomOut) {
//            topViewBar.zoomOut()
//        }
//    }
//
//    fun backToFirstPage() {
//        AppLogger.d("backFirstPage")
//        setCurrentItemPosition(Constant.TAG_FEATURE_POSITION)
//    }
//
//    fun backToMyPage() {
//        AppLogger.d("backToMyPage")
//        backToPosition(Constant.TAG_MY_POSITION)
//    }
//
//    override fun onDestroy() {
//        super.onDestroy()
//        EventBus.getDefault().unregister(this)
//    }
//
//    @Subscribe
//    fun onEventMainThread(event: LoginSuccessEvent?) {
//        //Receive EventBus about login and reset user image to custom picture
//        AppLogger.d("onEventMainThread(LoginSuccessEvent event)")
//        if (event == null) {
//            return
//        }
//        SyncHistoryAndCollectionUtil.uploadLocalHistoryAndCollection(applicationContext)
//    }
//
//    override fun addUpdateEvent(updateInfo: UpdateInfo) {
//        mUpdateHelper!!.showUpdateDialog(this, updateInfo, false)
//        if (updateInfo == null) {
//            LogManager.d(TAG, "updateInfo == null")
//            return
//        }
//        val hasClickUpdateButton =
//            PrefUtil.getBoolean(Constants.KEY_HAS_CLICK_UPDATE_BUTTON, false)
//        if (hasClickUpdateButton) {
//            if (updateInfo.data == null || updateInfo.data.status == 0) {
//                //已经是最新版本
//                LogManager.d(TAG, "already is newversion")
//                RequestManager.getInstance().onAllEvent(
//                    EventInfo(10212, "slc"), null, null,
//                    null
//                )
//                PrefUtil.putBoolean(
//                    Constants.KEY_HAS_CLICK_UPDATE_BUTTON,
//                    false
//                )
//            } else {
//                //有新版版本
//                LogManager.d(TAG, "updateInfo.data.status ? " + updateInfo.data.status)
//                RequestManager.getInstance().onAllEvent(
//                    EventInfo(10213, "slc"), null, null,
//                    null
//                )
//                PrefUtil.putBoolean(
//                    Constants.KEY_HAS_CLICK_UPDATE_BUTTON,
//                    false
//                )
//            }
//        }
//    }
//
//    override fun updateDownloadProgress(value: Int) {
//        mUpdateHelper!!.updateProgress(value)
//    }
//
//    override fun responseDownloadResult(
//        result: Boolean,
//        updateStatus: Int,
//        networkResponseCode: Int
//    ) {
//        mUpdateHelper!!.dealDownloadResult(this, result, updateStatus, networkResponseCode)
//    }
//
//    override fun initAboutUI(aboutInfo: AboutInfo) {}
//    override fun onExist() {
//        AppLogger.d("onExist requestTeenModePopDialog")
//        requestTeenModePopDialog()
//    }
//
//    var forceRefresh = false
////    val isTopView: Boolean
//    //是否为TOPView
////        get() {
////            if (adOpenControlView != null && adOpenControlView!!.isTopView && !forceRefresh) {
////                return true
////            } else if (adOpenControlView != null && !adOpenControlView!!.isTopView) {
////                return false
////            }
////            return false
////        }
//
//    /**
//     *  重新整理主页代码开始---------------------------------------------------
//     */
//
////    private var adOpenControlView: AdStartImageControlView? = null
//    private var mPartnerNo: String? = null
//
//    private var mPlatformCode: String? = null
//
//    private fun onCreateInit() {
//        mPartnerNo = Util.getPartnerNo(this.applicationContext)
//        mPlatformCode = Util.getPlatformCode(this.applicationContext)
//        initVideoView()
//        requestPrivacyDialog()
//    }
//
//
//    /**
//     * 视频广告
//     */
//    private fun initVideoView() {
////        BasePlayerConfig.enableDebuggable = true
////        BasePlayerConfig.enableRenderReusable = false
////        BasePlayerConfig.renderFactory = RenderFactory.surfaceViewRenderFactory()
////        mSoHuVideoView = findViewById<SoHuVideoView>(R.id.splash_videoView)
////        val videoParams = VideoParams()
////        videoParams.gid = UserHelper.getGid() ?: ""
////        videoParams.adType = AD_REQUEST_TYPE_OPEN
////        mSoHuVideoView?.setVideoParams(videoParams)
////        adOpenControlView = AdStartImageControlView(this)
////        adOpenControlView?.setAdStartCallBack(this)
////        mSoHuVideoView?.addAdControlComponent(adOpenControlView!!, false)
//    }
//
//    /**
//     * 请求隐私协议弹窗数据
//     */
//    private fun requestPrivacyDialog() {
//        AppLogger.e("requestPrivacyDialog fun request ")
//        NetworkApi.getWithCache<PrivacyInfo>(this, object : Observer<PrivacyInfo?> {
//            override fun onSubscribe(d: Disposable) {}
//            override fun onNext(value: PrivacyInfo?) {
//                AppLogger.e("requestPrivacyDialog fun  getWithCache onNext ")
//                if (value?.data != null) {
//                    val privacyVersion =
//                        PrefUtil.getInt(
//                            Constants.KEY_ALERT_PRIVACY_VERSION,
//                            0
//                        )
//                    if (value.data.is_alert == 1 && value.data.version > privacyVersion) {
//                        showPrivacyDialog(value)
//                    } else {
//                        continueCreate()
//                    }
//                } else {
//                    continueCreate()
//                }
//            }
//
//            override fun onError(e: Throwable) {
//                AppLogger.e("onError in getAboutInfo()error: " + e.message, e)
//                continueCreate()
//            }
//
//            override fun onComplete() {}
//        }, NetworkApi.networkInterface.privacyInfoData, PrivacyInfo::class.java)
//
//    }
//
//    private fun showPrivacyDialog(privacyInfo: PrivacyInfo) {
//        val dialog = PrivacyDialog(
//            this
//        ) { isConfirm ->
//            if (isConfirm) {
//                PrefUtil.putInt(
//                    Constants.KEY_ALERT_PRIVACY_VERSION,
//                    privacyInfo.data.version
//                )
//                continueCreate()
//            } else {
//                SohuAppUtil.exitApp(<EMAIL>)
//            }
//        }
//        dialog.setPrivacyInfo(privacyInfo)
//        dialog.show()
//    }
//
//    private fun continueCreate() {
//        AppLogger.d("continueCreate fun on load")
//        GlobalScope.launch {
//            withContext(Dispatchers.IO) {
//                SohuAppUtil.init(<EMAIL>())
//                OttHost.ottApi = UrlWrapper.AD_API_URL
//                SdkFactory.getInstance().setDebugLogStatus(UrlWrapper.LOGDEBUG)
//                SdkFactory.getInstance().prepare(
//                    <EMAIL>,
//                    UrlFactory.host,
//                    Util.getPartnerNo(<EMAIL>),
//                    Util.getProductKey(UrlWrapper.IS_CIBN)
//                )
//                //        showSplash();
//                //青少年模式不要开屏广告
//                if (TeenagersManger.isTeenager()) {
////            onFragmentInteractionListener.onHideFragment(null)
//                    ActivityLauncher.startTeenagersActivity(
//                        <EMAIL>,
//                        -1L
//                    )
//                    return@withContext
//                }
////        initData()
////        enableShowSplashView(false)
////        mBootPresenter = BootPresenterImpl(getActivity(), this)
////        mBootPresenter.createAndStartChildThread()
////        mBootPresenter.getAdTips()
////        com.sohuvideo.base.utils.AdTsManger.getInstants()
////            .initContext(<EMAIL>)
////        Util.sHasShowPrivacyDialog = true
////        Util.uploadDeviceInfo(this.getApplicationContext()) //TODO 上传设备信息 待优化
////        if (intent != null) {
////            val enterId = Util.getEnterId(0, localClassName, mPartnerNo)
////            RequestManager.getInstance().updateEnterId(enterId)
////            AppContext.getInstance().enterId = enterId
////            RequestManager.getInstance().updateParnerId(mPartnerNo, mPlatformCode)
////            DeviceConstants.getInstance().updatePartnerNo(mPartnerNo)
////            DeviceConstants.getInstance().updatePlatformCode(mPlatformCode)
////            RequestManager.onMccEvent("1002", "0")
////        }
////        // startAdPreDownloadService();
////        sendBroadcastToThirdPartner()
//
////        if (Util.isSupportTouchVersion(getActivity())) {
////            mGlideImageView.setOnClickListener(v -> startPayOrVideoDetailActivity());
////        }
////        onFragmentInteractionListener.onHideFragment(null);
//            }
//        }.start()
//        requestAd()
//
//    }
//
//    private fun requestAd() {
//        launcherViewModel?.requestAdSplashTips()
//        launcherViewModel?.adTipsString?.observe(this) {
////            if (it is ResultData.Success) {
////                adOpenControlView?.setBootTips(it.data)
////            }
//        }
//    }
//
//    /**
//     * 发送广播
//     */
//    private fun sendBroadcastToThirdPartner() {
//        AppLogger.d("send broadcast when started app")
//        val intent = Intent()
//        intent.action = "com.sohuott.tv.vod.START_APP"
//        sendBroadcast(intent)
//    }
//
//    private fun enableShowSplashImageView(enable: Boolean = true) {
//        mBinding!!.ivSplashView.visibility = if (enable) View.VISIBLE else View.GONE
//    }
//
//
//    override fun adStart(topViewPath: String?) {
//        //todo 可以加载主页数据
//        homeDataPreLoad()
//    }
//
//    override fun adSuccess() {
//        enableShowSplashImageView(false)
//    }
//
//    override fun adError() {
//        enableShowSplashImageView(false)
//        loadHomeData()
//        //todo 销毁视频
//    }
//
//    override fun adFinish() {
//        enableShowSplashImageView(false)
//        loadHomeData()
//        //todo 销毁视频
//    }
//
//    override fun adStartDetail() {
//        enableShowSplashImageView(false)
//    }
//
//    private fun loadHomeData() {
////        if (!requestPermission() && isTopView || !isTopView) {
////            forceRefresh = true
////            initData()
////        }
//    }
//
//    /**
//     *  重新整理主页代码结束
//     */
//
//
//    companion object {
//        private val TAG = LauncherActivity::class.java.simpleName
//        private const val PAGE = 1
//        private const val PAGE_SIZE = 40
//    }
//
//}