package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.SearchResult;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.SearchUtil;
import com.sohuott.tv.vod.widget.GlideImageView;

/**
 * Created by fenglei on 17-6-22.
 */

public class SearchResultSmallPicItemView extends RelativeLayout
        implements View.OnClickListener {

    protected GlideImageView cornerTagImageView;
    private TextView titleTV;
    private TextView timeTV;
    private int position;
    private boolean mResizeEnable = false;
    private int mAlbumWidth;
    private int mAlbumHeight;

    protected String mPageName = "6_search_result";

    public SearchResultSmallPicItemView(Context context) {
        super(context);
        initUI(context);
    }

    public SearchResultSmallPicItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initUI(context);
    }

    public GlideImageView getImageView(){
        return cornerTagImageView;
    }

    public void setAlbumResize(int width, int height){
        mAlbumWidth = width;
        mAlbumHeight = height;
        mResizeEnable = true;
    }

    public SearchResultSmallPicItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initUI(context);
    }

    public void customLayoutInflater(Context context){
        LayoutInflater.from(context).inflate(
                R.layout.search_result_recyclerview_small_pic_item, this, true);
    }

    private void initUI(Context context) {
        setFocusable(true);
        setOnClickListener(this);
        customLayoutInflater(context);
        cornerTagImageView = (GlideImageView) findViewById(R.id.search_result_small_pic_iv);
        titleTV = (TextView) findViewById(R.id.search_result_small_pic_title_tv);
        timeTV = (TextView) findViewById(R.id.search_result_small_pic_time_tv);
    }

    public void setData(int position, SearchResult.DataBean.ItemsBean itemsBean) {
        if(itemsBean != null) {
            setTag(itemsBean);
            titleTV.setText(itemsBean.getTitle());
            timeTV.setText(second2TimeStr(itemsBean.getPlay_time_second()));
            cornerTagImageView.setImageRes(itemsBean.getBig_pic());
            this.position = position;
        }
    }

    @Override
    public boolean requestFocus(int direction, Rect previouslyFocusedRect) {
        return super.requestFocus(direction, previouslyFocusedRect);
    }

    //child override
    public void jumpActivity(Context context,int aid,int position){
        ActivityLauncher.startVideoDetailActivity(context, aid, Constant.DATA_TYPE_PGC, Constant.PAGE_SEARCH);
        RequestManager.getInstance().onClickSearchResultPgcItemEvent(mPageName,position, aid);
    }

    protected void saveSearchHistory(Context context, SearchResult.DataBean.ItemsBean itemsBean){
        SearchUtil.saveSearchHistory(context, itemsBean);
    }

    public void setPageName(String pageName){
        this.mPageName = pageName;
    }

    @Override
    public void onClick(View v) {
        try {
            SearchResult.DataBean.ItemsBean itemsBean = (SearchResult.DataBean.ItemsBean)v.getTag();
            //ActivityLauncher.startVideoDetailActivity(v.getContext(), itemsBean.getId(), Constant.DATA_TYPE_PGC, Constant.PAGE_SEARCH);
            jumpActivity(v.getContext(), itemsBean.getId(),position);
            //SearchUtil.saveSearchHistory(v.getContext(), itemsBean);
            saveSearchHistory(v.getContext(), itemsBean);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setOnFocus() {
        if(titleTV != null) {
            setTVOnFocus(titleTV);
        }
    }

    public void setUnFocus() {
        if(titleTV != null) {
            setTVUnFocus(titleTV);
        }
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

    private String second2TimeStr(int seconds) {
        StringBuffer result = new StringBuffer();
        int hour = seconds / 3600;
        int minute = (seconds % 3600) / 60;
        int second = (seconds % 3600) % 60;
        if(hour > 0) {
            result.append(formatTime(hour)).append(":");
        }
        result.append(formatTime(minute)).append(":").append(formatTime(second));
        return result.toString();
    }

    private String formatTime(int time) {
        if(time < 10) {
            return 0 + String.valueOf(time);
        }
        return String.valueOf(time);
    }


}
