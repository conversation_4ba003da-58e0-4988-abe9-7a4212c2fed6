package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Rect;

import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

/**
 * Created by rita on 16-2-4.
 * <p/>
 * {@link GridLayoutManager} extension which introduces workaround for focus finding bug when
 * navigating with dpad.
 */

public class CustomGridLayoutManager extends GridLayoutManager {

    private static final String TAG = "CustomGridLayoutManager";
    int mCustomPaddingStart = 0;
    int mCustomPaddingEnd = 0;

    int mType = 0;
    /**
     * intercept focus search
     */
    public interface OnFocusSearchListener {
        /**
         * intercept focus search
         *
         * @param focused
         * @param direction
         * @return
         */
        View onFocusSearch(View focused, int direction);
    }

    private OnFocusSearchListener mOnFocusSearchListener;

    public void setOnFocusSearchListener(OnFocusSearchListener onFocusSearchListener) {
        mOnFocusSearchListener = onFocusSearchListener;
    }

    public CustomGridLayoutManager(Context context, int spanCount) {
        super(context, spanCount);
    }

    @Override
    public View onInterceptFocusSearch(View focused, int direction) {
        if (mOnFocusSearchListener != null) {
            View focusView = mOnFocusSearchListener.onFocusSearch(focused, direction);
            if (focusView != null) {
                return focusView;
            }
        }
        return super.onInterceptFocusSearch(focused, direction);
    }

    public void setCustomPadding(int paddingStart, int paddingEnd) {
        mCustomPaddingStart = paddingStart;
        mCustomPaddingEnd = paddingEnd;
    }

    // VRS = 0 or PGC = 1
    public void setItemType(int type){
        this.mType = type;
    }

    @Override
    public boolean requestChildRectangleOnScreen(RecyclerView parent, View child, Rect rect, boolean immediate, boolean focusedChildVisible) {

        LibDeprecatedLogger.d("parent.getChildAdapterPosition="+parent.getChildAdapterPosition(child) + " , parent.getChildLayoutPosition=" + parent.getChildLayoutPosition(child));
        LibDeprecatedLogger.d("viewType=" + parent.getAdapter().getItemViewType(parent.getChildAdapterPosition(child)));
        //header不处理滑动
        if (parent.getAdapter().getItemViewType(parent.getChildAdapterPosition(child)) < 0) {
            return false;
        }

        if (parent.getChildAdapterPosition(child)  < (mType == 0 ? 6 : 4)) {//第一行
            parent.smoothScrollBy(0, -1000);//焦点在第一行，露出全部header
            return true;
        }

        if (getOrientation() == VERTICAL) {
            int parentTop = 0;
            int parentBottom = getHeight();
            int childTop = child.getTop() + rect.top;
            int childBottom = childTop + rect.bottom;

            // 选中item 的上下边距，如果小于距即滑动
            int offScreenTop = Math.min(0, childTop - parentTop - mCustomPaddingStart);
            int offScreenBottom = Math.max(0, childBottom - parentBottom + mCustomPaddingEnd);


            int dy = offScreenTop != 0 ? offScreenTop : offScreenBottom;

            // 处理焦点放大带来的焦点左右移动时上下跳动问题
            if (dy > 121 || dy < -121) {
                LibDeprecatedLogger.d("scrollby="+dy);
                parent.smoothScrollBy(0, dy);

                return true;
            }
        }

        return false;
    }

    @Override
    public boolean requestChildRectangleOnScreen(RecyclerView parent, View child, Rect rect, boolean immediate) {
        final int parentTop = 0;
        final int parentBottom = getHeight();
        final int childTop = child.getTop() + rect.top;
        final int childBottom = childTop + rect.bottom;

        // 选中item 的右边距，如果小于右边距即滑动
        final int offScreenTop = Math.min(0, childTop - parentTop - getPaddingTop());
        final int offScreenBottom = Math.max(0, childBottom - parentBottom + getPaddingBottom());

        // Favor bringing the top into view over the bottom
        final int dy = offScreenTop != 0 ? offScreenTop : offScreenBottom;

        if (dy != 0) {
            parent.smoothScrollBy(0, dy);
            return true;
        }
        return false;
    }

    @Override
    public View onFocusSearchFailed(View focused, int focusDirection,
                                    RecyclerView.Recycler recycler, RecyclerView.State state) {
        // Need to be called in order to layout new row/column
        View nextFocus = super.onFocusSearchFailed(focused, focusDirection, recycler, state);

        if (nextFocus == null) {
            return null;
        }

        int fromPos = getPosition(focused);
        int nextPos = getNextViewPos(fromPos, focusDirection);
        return findViewByPosition(nextPos);
    }

    /**
     * Manually detect next view to focus.
     *
     * @param fromPos   from what position start to seek.
     * @param direction in what direction start to seek. Your regular {@code View.FOCUS_*}.
     * @return adapter position of next view to focus. May be equal to {@code fromPos}.
     */
    protected int getNextViewPos(int fromPos, int direction) {
        int offset = calcOffsetToNextView(direction);
        if (hitBorder(fromPos, offset)) {
            return fromPos;
        }

        return fromPos + offset;
    }

    /**
     * Calculates position offset.
     *
     * @param direction regular {@code View.FOCUS_*}.
     * @return position offset according to {@code direction}.
     */
    protected int calcOffsetToNextView(int direction) {
        int spanCount = getSpanCount();
        int orientation = getOrientation();

        if (orientation == VERTICAL) {
            switch (direction) {
                case View.FOCUS_DOWN:
                    return spanCount;
                case View.FOCUS_UP:
                    return -spanCount;
                case View.FOCUS_RIGHT:
                    return 1;
                case View.FOCUS_LEFT:
                    return -1;
            }
        } else if (orientation == HORIZONTAL) {
            switch (direction) {
                case View.FOCUS_DOWN:
                    return 1;
                case View.FOCUS_UP:
                    return -1;
                case View.FOCUS_RIGHT:
                    return spanCount;
                case View.FOCUS_LEFT:
                    return -spanCount;
            }
        }

        return 0;
    }

    /**
     * Checks if we hit borders.
     *
     * @param from   from what position.
     * @param offset offset to new position.
     * @return {@code true} if we hit border.
     */
    private boolean hitBorder(int from, int offset) {
        int spanCount = getSpanCount();

        if (Math.abs(offset) == 1) {
            int spanIndex = from % spanCount;
            int newSpanIndex = spanIndex + offset;
            return newSpanIndex < 0 || newSpanIndex >= spanCount;
        } else {
            int newPos = from + offset;
            return newPos < 0 && newPos >= getItemCount();
        }
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        try {
            super.onLayoutChildren(recycler, state);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    @Override
//    public int scrollVerticallyBy(int dy, RecyclerView.Recycler recycler, RecyclerView.State state) {
//        try {
//            return super.scrollVerticallyBy(dy, recycler, state);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return 0;
//    }
}