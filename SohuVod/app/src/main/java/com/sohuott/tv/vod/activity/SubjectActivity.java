package com.sohuott.tv.vod.activity;

import android.os.Bundle;
import android.view.View;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.SubjectPagerAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ComingSoonModel;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.lib.widgets.VerticalViewPager;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by yueyin on 2018/9/20.
 */

public class SubjectActivity extends BaseFragmentActivity {
    private VerticalViewPager mVp;
    private GlideImageView mBgIv;
    private View mErrorView;
    private LoadingView mLoadingView;
    private SubjectPagerAdapter mAdapter;
    private LoginUserInformationHelper mHelper;

    private int mSubjectId;
    private String mPic1;
    private String mPic2;
    private String mSmallPic;

    private ComingSoonModel mComingSoonModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_subject);

        if (getIntent() != null) {
            mSubjectId = getIntent().getIntExtra(ParamConstant.PARAM_SUBJECT_ID, 0);
            mPic1 = getIntent().getStringExtra(ParamConstant.PARAM_SUBJECT_PIC1);
            mPic2 = getIntent().getStringExtra(ParamConstant.PARAM_SUBJECT_PIC2);
            mSmallPic = getIntent().getStringExtra(ParamConstant.PARAM_SUBJECT_SMALL_PIC);
        }
        initView();

        if (mSubjectId == 0) {
            mBgIv.setVisibility(View.VISIBLE);
            mBgIv.setImageRes(mPic2, getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
            mLoadingView.setVisibility(View.GONE);
        } else {
            requestData();
        }
    }

    private void requestData() {

        NetworkApi.getComingSoonData(UrlWrapper.getComingSoonUrl(mSubjectId, mHelper.getLoginPassport(), mHelper.getLoginToken()), new Observer<ComingSoonModel>() {
            @Override
            public void onSubscribe(Disposable d) {
                LibDeprecatedLogger.d("requestSubject() onSubscribe()");
            }

            @Override
            public void onNext(ComingSoonModel value) {
                LibDeprecatedLogger.d("requestSubject() onNext()");
                mAdapter.setTempletData(value);
                mComingSoonModel = value;
                mAdapter.notifyDataSetChanged();
                mLoadingView.setVisibility(View.GONE);
            }

            @Override
            public void onError(Throwable e) {
                mErrorView.setVisibility(View.VISIBLE);
                mVp.setVisibility(View.GONE);
                mLoadingView.setVisibility(View.GONE);
                LibDeprecatedLogger.d("requestSubject() onError()");
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestSubject() onComplete()");
            }
        });
    }

    private void initView() {
        mVp = (VerticalViewPager) findViewById(R.id.subject_vp);
        mErrorView = findViewById(R.id.err_view);
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mBgIv = (GlideImageView) findViewById(R.id.subject_bg);
        mLoadingView.setVisibility(View.VISIBLE);
        
        mVp.setOffscreenPageLimit(255);
        mAdapter = new SubjectPagerAdapter(getSupportFragmentManager(),mPic1, mPic2, mSmallPic);
        mVp.setAdapter(mAdapter);

        mHelper = LoginUserInformationHelper.getHelper(this);
    }

    public List<ContentGroup.DataBean.ContentsBean.SubjectVideoListBean> getComingSoonModel(){
        return mComingSoonModel.getData().getResult().getSubjectInfos();
    }
}
