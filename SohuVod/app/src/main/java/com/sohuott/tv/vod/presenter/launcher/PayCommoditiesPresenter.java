package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.Commodity;
import com.sohuott.tv.vod.lib.model.FilmCommodities;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohu.lib_utils.FontUtils;
import com.sohu.lib_utils.StringUtil;

/**
 * Created by music on 2022/1/4.
 */

public class PayCommoditiesPresenter extends Presenter {
    private Context mContext;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_pay_commodities, parent, false);
        return new ViewHolder(view);
    }

    private void setTypeface(View view) {
        FontUtils.setTypeface(mContext, view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        if (item instanceof Commodity.DataEntity.CommoditiesEntity) {
            Commodity.DataEntity.CommoditiesEntity commoditiesEntity = (Commodity.DataEntity.CommoditiesEntity) item;
            if (!StringUtil.isEmpty(commoditiesEntity.getRemind())){
                if (commoditiesEntity.getRemind().length() == 4) {
                    String originalString = commoditiesEntity.getRemind();
                    String insertString = "\n"; // 要插入的字符串为换行符 "\n"

                    String firstPart = originalString.substring(0, 2); // 获取索引2之前的子字符串
                    String secondPart = originalString.substring(2); // 获取索引2之后的子字符串

                    String finalString = firstPart + insertString + secondPart; // 将三个部分拼接起来

                    vh.mPayTag.setText(finalString);

                } else {
                    vh.mPayTag.setText(commoditiesEntity.getRemind());
                }
                vh.mPayTag.setVisibility(View.VISIBLE);
            }

            vh.mPayTitle.setText(commoditiesEntity.getName());

            if (((Commodity.DataEntity.CommoditiesEntity) item).isAgent()) {
//                int startIndex = commoditiesEntity.getDescription().indexOf(String.valueOf(commoditiesEntity.getOriPrice() / 100));
//                int endIndex = startIndex + String.valueOf(commoditiesEntity.getOriPrice() / 100).length();
//
//                if (startIndex != -1) {
//                    // 获取原始描述
//                    String originalDescription = commoditiesEntity.getDescription();
//
//                    // 在指定位置前后添加空格
//                    String modifiedDescription = originalDescription.substring(0, startIndex) + " "
//                            + originalDescription.substring(startIndex, endIndex) + " "
//                            + originalDescription.substring(endIndex);
//
//                    // 将 getOriPrice 在 getDescription 中的部分标为红色
//                    SpannableStringBuilder spannable = new SpannableStringBuilder(modifiedDescription);
//                    vh.mPayDesc.setText(spannable);
//                } else {
                    vh.mPayDesc.setText(commoditiesEntity.getDescription());
//                }
            } else {
                vh.mPayDesc.setText(commoditiesEntity.getDescription());
            }




            vh.mPayMoney.setText(Util.formatPrice(commoditiesEntity.getPrice() / 100.0));

            if (commoditiesEntity.getOriPrice() > commoditiesEntity.getPrice()) {
                vh.mPayOriMoney.setText("¥" + Util.formatPrice(commoditiesEntity.getOriPrice() / 100.0));
                vh.mPayOriMoney.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG | Paint.ANTI_ALIAS_FLAG);
            }

        } else if (item instanceof FilmCommodities.DataEntity.ComiditiesEntity) {
            FilmCommodities.DataEntity.ComiditiesEntity dataEntity = (FilmCommodities.DataEntity.ComiditiesEntity) item;
            vh.mPayMoney.setText(Util.formatPrice(dataEntity.getPrice() / 100.0));
            if (dataEntity.getOriPrice() > dataEntity.getPrice()) {
                vh.mPayOriMoney.setText("¥" + Util.formatPrice(dataEntity.getOriPrice() / 100.0));
                vh.mPayOriMoney.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
            }
            vh.mPayTitle.setText(dataEntity.getName());
            vh.mPayDesc.setText("版权限制该片需付费观看，有效期" + dataEntity.getPrivilegeAmount() + "天");
        }

        setTypeface(vh.mPayPrefix);
        setTypeface(vh.mPayMoney);
        setTypeface(vh.mPayOriMoney);

        vh.view.setOnFocusChangeListener((v, hasFocus) -> {
            vh.mPayDesc.setSelected(hasFocus);
            if (hasFocus) {
                vh.mArrow.setVisibility(View.VISIBLE);
                vh.mPayMoney.setTextColor(Color.parseColor("#692910"));
                vh.mPayOriMoney.setTextColor(Color.parseColor("#b2692910"));
                vh.mPayPrefix.setTextColor(Color.parseColor("#692910"));
                vh.mPayTitle.setTextColor(Color.parseColor("#692910"));
                vh.mPayDesc.setTextColor(Color.parseColor("#b2692910"));
            } else {
                vh.mArrow.setVisibility(View.GONE);
                vh.mPayMoney.setTextColor(Color.parseColor("#DEBB99"));
                vh.mPayOriMoney.setTextColor(Color.parseColor("#b2E7C5A3"));
                vh.mPayPrefix.setTextColor(Color.parseColor("#DEBB99"));
                vh.mPayTitle.setTextColor(Color.parseColor("#E8E8FF"));
                vh.mPayDesc.setTextColor(Color.parseColor("#b2E8E8FF"));

            }
        });

    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {
        TextView mPayTag, mPayTitle, mPayDesc, mPayMoney, mPayPrefix, mPayOriMoney, mPayTipAutoSign;
        ImageView mArrow;
        public ViewHolder(View view) {
            super(view);
            mPayTag = (TextView) view.findViewById(R.id.pay_commodity_tag);
            mPayTitle = (TextView) view.findViewById(R.id.pay_commodity_title);
            mPayDesc = (TextView) view.findViewById(R.id.pay_commodity_desc);
            mPayMoney = (TextView) view.findViewById(R.id.pay_commodity_money);
            mArrow = (ImageView) view.findViewById(R.id.pay_commodity_arrow);
            mPayPrefix = (TextView) view.findViewById(R.id.pay_commodity_prefix);
            mPayOriMoney = (TextView) view.findViewById(R.id.pay_commodity_money_ori);
            mPayTipAutoSign = view.findViewById(R.id.pay_tip_auto_sign);


        }
    }
}
