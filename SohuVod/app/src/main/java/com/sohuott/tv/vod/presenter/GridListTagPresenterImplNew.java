package com.sohuott.tv.vod.presenter;

import android.content.Context;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.lib.model.GridListTagMenuModel;
import com.sohuott.tv.vod.view.GridListTagViewNew;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by wenjingbian on 2017/6/27.
 */

public class GridListTagPresenterImplNew {

    private static final int PAGE_SIZE = 20;

    private GridListTagViewNew mGridListTagView;
    private Context mContext;

    private int mCurrPage;
    private int mOttCodeId;

    public GridListTagPresenterImplNew(Context context){
        this.mContext = context;
    }

    public void setGridListTagView(GridListTagViewNew gridListTag) {
        this.mGridListTagView = gridListTag;
    }

    public void requestLeftList() {
        NetworkApi.getOttCategoryIdList(new Observer<GridListTagMenuModel>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(GridListTagMenuModel value) {
                LibDeprecatedLogger.d("requestLeftList(): onComplete()");
                if (value != null && value.data != null && value.data.size() > 0) {
                    mGridListTagView.displayLeftListView(value.data);
                } else {
                    mGridListTagView.displayParentErrorView();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestLeftList(): onError()--" + e.getMessage());
                mGridListTagView.displayParentErrorView();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestLeftList(): onComplete()");
            }
        });
    }

    public void requestTagList(final int ottCateId) {
        //display child loading view
        mGridListTagView.displayTagLoadingView();
        //reset current page number
        mCurrPage = 1;
        mOttCodeId = ottCateId;

        NetworkApi.getTagList(ottCateId, 1, PAGE_SIZE, new Observer<AllLabel>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(AllLabel value) {
                LibDeprecatedLogger.d("requestTagList(): onNext()");
                if (value != null && value.data != null && value.data.result != null
                        && value.data.result.size() > 0) {
                    if (mOttCodeId == -1 || value.data.result.get(0).ottCategoryId == mOttCodeId) {
                        mGridListTagView.displayTagView(value.data);
                    }
                } else {
                    mGridListTagView.displayTagErrorView(mContext.getResources().getString(R.string.data_empty));
                }
                RequestManager.getInstance().onGridTagListNewTagViewExposureEvent(mOttCodeId);
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestLeftList(): onError()--" + e.getMessage());
                mGridListTagView.displayTagErrorView(mContext.getResources().getString(R.string.data_err));
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestLeftList(): onComplete()");
            }
        });
    }

    public void requestMoreTagListData(int ottCateId) {
        mOttCodeId = ottCateId;
        NetworkApi.getTagList(ottCateId, mCurrPage + 1, PAGE_SIZE, new Observer<AllLabel>() {

            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(AllLabel value) {
                LibDeprecatedLogger.d("requestMoreTagListData(): onNext()");

                if (value != null && value.data != null && value.data.result != null
                        && value.data.result.size() > 0) {
                    if (mOttCodeId == -1 || value.data.result.get(0).ottCategoryId == mOttCodeId) {
                        mGridListTagView.addTagItems(value.data);
                        mCurrPage++;
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestMoreTagListData(): onError()--" + e.getMessage());
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestMoreTagListData(): onComplete()");
            }
        });
    }

}
