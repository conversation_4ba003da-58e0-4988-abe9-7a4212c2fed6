package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.service.PlayHistoryService;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.widget.HomeHistoryView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;
import com.sohu.lib_utils.StringUtil;

import java.util.HashMap;
import java.util.List;


public class HistoryPresenter extends Presenter {
    private static final String TAG = "HistoryPresenter";
    private Context mContext;
    private PlayHistoryService mPlayHistoryService;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;
    private LoginUserInformationHelper mHelper;
    HashMap<String, String> pathInfo = new HashMap<>();


    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        AppLogger.v( "onCreateViewHolder: ");
        mPlayHistoryService = new PlayHistoryService(mContext);
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_history, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        mHelper = LoginUserInformationHelper.getHelper(mContext);

        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        vh.view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
            }
        });
        if (item instanceof PlayHistory) {
            AppLogger.v( "onBindViewHolder: ");
            pathInfo = ((PlayHistory) item).pathInfo;
            mPlayHistoryService.getAllPlayHistory(new PlayHistoryService.PlayHistoryListener() {
                @Override
                public void onSuccess(List<PlayHistory> playHistoryList) {
                    vh.mHomeHistoryView.setData(playHistoryList, 62);
                }

                @Override
                public void onFail(String reason, List<PlayHistory> playHistoryList) {

                }
            });
//            vh.mTvHistoryTitle.setText(data.name);
            vh.mHomeHistoryView.setPathInfo(pathInfo);
        }
    }

    @Override
    public void onViewAttachedToWindow(Presenter.ViewHolder holder) {
        if (mPlayHistoryService.getPlayHistoryFromDB(-1) == null) {
            return;
        }
        AppLogger.v( "onViewAttachedToWindow: ");
        HashMap<String, String> memoInfo = new HashMap<>();
        memoInfo.put("isLogin", mHelper.getIsLogin() ? "1" : "0");
        memoInfo.put("history", mPlayHistoryService.getPlayHistoryFromDB(-1).size() > 0 ? "1" : "0");
        RequestManager.getInstance().onAllEvent(new EventInfo(10149, "imp"), pathInfo, null, memoInfo);
        if (mPlayHistoryService == null) {
            return;
        }
        List<PlayHistory> playHistoryFromDB = mPlayHistoryService.getPlayHistoryFromDB(-1);
        if (playHistoryFromDB == null) {
            return;
        }
        if (playHistoryFromDB.size() > 0) {
            HashMap<String, String> objectInfo = new HashMap<>();
            objectInfo.put("type", "视频");
            objectInfo.put("vid", StringUtil.toString(mPlayHistoryService.getPlayHistoryFromDB(-1).get(0).getVideoId()));
            objectInfo.put("playlistId", StringUtil.toString(mPlayHistoryService.getPlayHistoryFromDB(-1).get(0).getAlbumId()));
            RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"), pathInfo, objectInfo, memoInfo);
        }

        super.onViewAttachedToWindow(holder);
    }

    @Override
    public void setOnClickListener(Presenter.ViewHolder holder, View.OnClickListener listener) {
        super.setOnClickListener(holder, listener);
        AppLogger.v( "setOnClickListener: ");
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private HomeHistoryView mHomeHistoryView;

        ViewHolder(View view) {
            super(view);
            mHomeHistoryView = (HomeHistoryView) view;
        }
    }
}
