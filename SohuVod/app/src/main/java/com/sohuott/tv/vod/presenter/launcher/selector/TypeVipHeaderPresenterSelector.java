package com.sohuott.tv.vod.presenter.launcher.selector;



import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.base.BasePresenterSelector;
import com.sohuott.tv.vod.model.VipBanner;
import com.sohuott.tv.vod.model.VipUserState;
import com.sohuott.tv.vod.presenter.launcher.VipBannerPresenter;
import com.sohuott.tv.vod.presenter.launcher.VipUserPresenter;

public class TypeVipHeaderPresenterSelector extends BasePresenterSelector {
    private VipUserPresenter userPresenter;
    private VipBannerPresenter bannerPresenter;

    public TypeVipHeaderPresenterSelector() {
        userPresenter = new VipUserPresenter();
        bannerPresenter = new VipBannerPresenter();
    }

    @Override
    public Presenter getPresenter(Object o) {
        if (o instanceof VipUserState){
            return userPresenter;
        } else if (o instanceof VipBanner){
            return bannerPresenter;
        }
        return bannerPresenter;
    }
}
