package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import com.sh.ott.video.player.controller.component.BaseControlComponent

open class BaseVideoProgressComponent constructor(
    context: Context
) : BaseControlComponent(context) {
    open var mCurrentPosition: Long = 0
    open var mDuration: Long = 0
    open var isTryVideo = false
    open var trySeeTime: Long = 0
    open var isCheckStopTry: Boolean = false
    open var videoLength: Long = 0
    open var checkTsServerTime: Long? = 0L

    override fun onProgressChanged(duration: Long, position: Long) {
        mCurrentPosition = position / 1000
        mDuration = duration / 1000
        if (isTryVideo) {
            checkTsServerTime = trySeeTime
            mDuration = trySeeTime
        } else {
            checkTsServerTime = videoLength.toLong()
        }
    }
}