package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import com.sh.ott.video.ad.AdRequestFactory
import com.sh.ott.video.base.component.ShPlayerConstants
import com.sh.ott.video.player.PlayerConstants
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible

/**
 * 播放max视频
 */
class VideoAdPauseFilmComponent constructor(context: Context) :
    BaseVideoAdPauseComponent(context) {
    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        super.onPlayStateChanged(playState, extras)
        if ((currentPlayState == PlayerConstants.VideoState.PLAYING) && adType == ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_PAUSE) {
            if (visibility == VISIBLE) {
                return
            }
            pauseImageView?.gone()
            pauseTextView?.gone()
            tvPauseImageViewTips?.gone()
            visible()
            showPauseVideoPoster()
            AdRequestFactory.getInstants().reportPauseAd(0)
            controller?.startUpdateProgress()
        } else {
            //循环播放之后只调用 buff状态
            if (visibility == VISIBLE && currentPlayState == PlayerConstants.VideoState.BUFFERED) {
                controller?.startUpdateProgress()
                return
            }
            controller?.startUpdateProgress()
        }
    }

    override fun onProgressChanged(duration: Long, position: Long) {
        super.onProgressChanged(duration, position)
        if (adType != ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_PAUSE) return
        if (position > 0) {
            AdRequestFactory.getInstants().reportPauseAdProgress((position / 1000).toInt())
        }
    }

}