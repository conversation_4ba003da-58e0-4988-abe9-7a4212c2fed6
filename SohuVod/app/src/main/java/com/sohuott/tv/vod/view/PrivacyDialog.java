package com.sohuott.tv.vod.view;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.PrivacyInfo;
import com.sohuott.tv.vod.utils.ActivityLauncher;

import java.util.HashMap;

/**
 * Created by yizhang210244 on 2017/5/12.
 */

public class PrivacyDialog extends Dialog implements View.OnClickListener {

    private static final String TAG = PrivacyDialog.class.getSimpleName();

    private Context mContext;


    private Button btnPositive, btnNegative;
    private TextView mMsgtextview, mPrivacyTitle, mAgreementTextview, mPrivacyTextview;
    private TextView mCollectInfo, mThirdInfo,mThirdDir, mTips;
    private ExitListener mExitListener;
    private PrivacyInfo mPrivacyInfo;


    /**
     * Called when a view has been clicked.
     *
     * @param v The view that was clicked.
     */
    @Override
    public void onClick(View v) {
        if (v == btnPositive) {
            mExitListener.onExit(true);
            this.dismiss();
            RequestManager.getInstance().onAllEvent(new EventInfo(10232, "clk"), null, null,
                    null);
        } else if (v == btnNegative) {
            RequestManager.getInstance().onAllEvent(new EventInfo(10233, "clk"), null, null,
                    null);
            mExitListener.onExit(false);
            this.dismiss();
        } else if (v == mAgreementTextview) {
            ActivityLauncher.startAboutActivity(mContext);
        } else if (v == mPrivacyTextview) {
            ActivityLauncher.startAboutActivity(mContext);
        } else if (v == mThirdInfo) {
            ActivityLauncher.startAboutActivity(mContext);
        } else if (v == mCollectInfo) {
            ActivityLauncher.startAboutActivity(mContext);
        } else if (v == mThirdDir) {
            ActivityLauncher.startAboutActivity(mContext);
        }
    }

    public interface ExitListener {
        void onExit(boolean isConfirm);
    }



    public PrivacyDialog(Context context, ExitListener exitListener) {
        super(context, R.style.FullScreenDialog);
        this.mContext = context;
        this.mExitListener = exitListener;
    }

    public void setPrivacyInfo(PrivacyInfo privacyInfo) {
        mPrivacyInfo = privacyInfo;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        this.setContentView(R.layout.dialog_privacy);

        View view = LayoutInflater.from(mContext).inflate(R.layout.dialog_privacy, null);
        Window window = this.getWindow();
        window.setContentView(view);

        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);


        WindowManager.LayoutParams lp=this.getWindow().getAttributes();
        lp.dimAmount=0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        btnPositive = (Button) findViewById(R.id.btn_dialog_positive);
        btnNegative = (Button) findViewById(R.id.btn_dialog_negative);
        mMsgtextview = (TextView) findViewById(R.id.msg_textview);
        mPrivacyTitle = (TextView) findViewById(R.id.privacy_title);
        mAgreementTextview = (TextView) findViewById(R.id.agreement_textview);
        mPrivacyTextview = (TextView) findViewById(R.id.privacy_textview);
        mCollectInfo = findViewById(R.id.collect_info_textview);
        mThirdInfo = findViewById(R.id.third_info_textview);
        mThirdDir = findViewById(R.id.third_dir_textview);
        mTips = findViewById(R.id.tips);
//        btnPositive.setOnFocusChangeListener(this);
//        btnNegative.setOnFocusChangeListener(this);
        btnPositive.setOnClickListener(this);
        btnNegative.setOnClickListener(this);
        mAgreementTextview.setOnClickListener(this);
        mPrivacyTextview.setOnClickListener(this);
        mCollectInfo.setOnClickListener(this);
        mThirdInfo.setOnClickListener(this);
        mThirdDir.setOnClickListener(this);
        initData();
        setCancelable(false);
        HashMap pathInfo = new HashMap<>();
        pathInfo.put("pageId", "1026");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), pathInfo, null,
                null);
    }

    @Override
    public void show() {
        super.show();
        if (btnPositive != null) {
            btnPositive.requestFocus();
        }
    }


    private void initData() {
        if (mPrivacyInfo != null && mPrivacyInfo != null) {
            mPrivacyTitle.setText(mPrivacyInfo.title);
            mMsgtextview.setText(Html.fromHtml(mPrivacyInfo.content));
            mTips.setText(mPrivacyInfo.tips);
            if (!mPrivacyInfo.privacy_btn) {
                mPrivacyTextview.setVisibility(View.GONE);
            }
            if (!mPrivacyInfo.user_agreement_btn) {
                mAgreementTextview.setVisibility(View.GONE);
            }
            if (!mPrivacyInfo.collect_info_btn) {
                mCollectInfo.setVisibility(View.GONE);
            }
            if (!mPrivacyInfo.third_info_btn) {
                mThirdInfo.setVisibility(View.GONE);
            }
            if (!mPrivacyInfo.third_dir_btn) {
                mThirdDir.setVisibility(View.GONE);
            }
        }
    }

}