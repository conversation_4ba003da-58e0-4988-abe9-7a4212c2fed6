package com.sohuott.tv.vod.fragment;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.request.target.DrawableImageViewTarget;
import com.bumptech.glide.request.transition.Transition;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sh.ott.video.ad.AdRequestFactory;
import com.sh.ott.video.base.OnProgressChangedListener;
import com.sh.ott.video.base.component.ShDataSource;
import com.sh.ott.video.base.component.ShPlayerConstants;
import com.sh.ott.video.player.PlayerConstants;
import com.sh.ott.video.player.base.OnStateChangeListener;
import com.sh.ott.video.view.ShVideoView;
import com.sohu.lib_utils.PrefUtil;
import com.sohu.ott.ads.sdk.iterface.ILoader;
import com.sohu.ott.ads.sdk.model.AdCommon;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;
import com.sohuott.tv.vod.app.AppConstants;
import com.sohuott.tv.vod.app.SohuAppUtil;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.BootTipsBean;
import com.sohuott.tv.vod.lib.model.ConfigInfoBatch;
import com.sohuott.tv.vod.lib.model.PrivacyInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.BootPresenter;
import com.sohuott.tv.vod.presenter.BootPresenterImpl;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.videodetail.activity.control.AdStartImageControlView;
import com.sohuott.tv.vod.videodetail.activity.control.OnAdStartControlCallBack;
import com.sohuott.tv.vod.view.BootView;
import com.sohuott.tv.vod.view.PrivacyDialog;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.HashMap;
import java.util.Map;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

public class BootFragment extends BaseFragment implements BootView, OnStateChangeListener, OnProgressChangedListener {

    private static final String TAG = BootFragment.class.getSimpleName();

    public static final String AD_TYPE_VIDEO_MP4 = "video/mp4";
    private static final String AD_TYPE_IMAGE_JPEG = "image/jpeg";
    private static final String AD_TYPE_IMAGE_PNG = "image/png";

    /**
     * TAG values stands for different actions when finished to countdown timer
     */
    private static final int TAG_START_HOME = 1;
    private static final int TAG_SHOW_ADS = 2;
    private static final int TAG_LOAD_IMAGE = 3;

    public int flag = TYPE_AD_DEFAULT; //default ：3，政治开屏：0
    public static final int TYPE_AD_DEFAULT = 3;
    public static final int TYPE_AD_HIDE = 0;


    //Duration of countdown timer
    private static final int COUNTDOWN_DURATION = 4_000; //1.5秒超时

    private static final int TIMEOUT_DURATION = 1_000; //1秒超时


    //The interval along the way receive onTick() callback of MyCountDownTimer
    private static final int COUNTDOWN_INTERVAL = 300;

    private GlideImageView mGlideImageView;

    //进入视频详情页的提示imageview 位置在右下角
    private ImageView enterDetailHintImageView;
    private ViewGroup mAd_enterdetail_hint_layout;

    private TextView mRightEnterTextView;

    private View mBottomBg, mTopBg;

    private TextView mTextView;

    private BootPresenter mBootPresenter;

    private ShVideoView videoView;

    private MyCountDownTimer mCountDownTimer;

    public AdCommon mAdCommon;


    //Integer value to identify action when finished to countdown timer.
    private int tagForCountDownTimer = TAG_START_HOME;

    private boolean isShowAds;
    private boolean isPayAd;
    private boolean isShown;

    private Intent mServiceIntent;

    private ImageView mHomeBg;

    private String mPartnerNo;

    private String mPlatformCode;

    private TextView mBeian;//备案号

    /**
     * 政治开屏提示文案,html格式,需格式化后显示
     */
    private String mBootTips;

    /**
     * 是否可以返回键进入首页
     */
    public Boolean isCanBack = true;

    /**
     * 南传整改广告三秒后可back
     */
    private static final Long CAN_BACK_TIME = 0L;

    /**
     * 视频广告倒计时
     */
    private static final Long VIDEO_AD_TIME = 15_000L;

    private static final int FORCE_ENTER_HOMEACTIVITY = 11;
//    private VideoPlayerCountDownHandler videoCountDownHandler;

    private int aid, dataType;


    OnFragmentInteractionListener onFragmentInteractionListener;

    CountDownTimer videoCountDownTimer;
    long totalVideoDuration;
    long totalVideoPos;

    private Handler mainHandler;
    private Handler backgroundHandler;
    //    private Runnable updateRunnable;
    private CountDownTimer countDownTimer;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.activity_boot, container, false);
        mPartnerNo = Util.getPartnerNo(getActivity());
        mPlatformCode = Util.getPlatformCode(getActivity());
        initView(view);
        showBeiAnNumber();
        requestPrivacyDialog();
        AppLogger.d("SohuAppNew", "BootFragment  onCreateView ");
        return view;
    }

    private void showBeiAnNumber() {
        String beiAn = PrefUtil.getString("bei_an", "");
        if (!beiAn.equals("")) {
            mBeian.setText(beiAn);
        }
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mainHandler = new Handler(Looper.getMainLooper());
        AppLogger.d("SohuAppNew", "BootFragment  onViewCreated ");

    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof OnFragmentInteractionListener) {
            onFragmentInteractionListener = (OnFragmentInteractionListener) context;
        } else {
            throw new RuntimeException(context.toString()
                    + " must implement OnFragmentInteractionListener");
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        HomeData.sBootOrHomeIsStarted = true;
        LibDeprecatedLogger.d("onResume");
        AppLogger.d("SohuAppNew", "BootFragment  onResume ");
    }

    FrameLayout playerRoot;

    AdStartImageControlView adStartImageControlView;

    private void initView(View view) {
        mBeian = view.findViewById(R.id.registration);
        mHomeBg = view.findViewById(R.id.home_bg);
        mTextView = view.findViewById(R.id.tv_countdown);
        enterDetailHintImageView = view.findViewById(R.id.ad_enterdetail_hint);
        mAd_enterdetail_hint_layout = view.findViewById(R.id.ad_enterdetail_hint_layout);
        mGlideImageView = view.findViewById(R.id.giv_ads);
        mRightEnterTextView = view.findViewById(R.id.ad_flag_right_enter);

        mBottomBg = view.findViewById(R.id.bottom_bg);
        mTopBg = view.findViewById(R.id.top_bg);

        playerRoot = view.findViewById(R.id.root_player);
        videoView = view.findViewById(R.id.videoView);
        videoView.addAdOnStateChangeListener(this);
        adStartImageControlView = new AdStartImageControlView(getContext());
        adStartImageControlView.setAdStartCallBack(new OnAdStartControlCallBack() {

            @Override
            public void adStartDetail(boolean isPayAd, boolean isShown, int aid, int dataType) {
                BootFragment.this.isPayAd = isPayAd;
                BootFragment.this.isShown = isShown;
                BootFragment.this.aid = aid;
                BootFragment.this.dataType = dataType;
            }

            @Override
            public void adFinish() {
                if (isTopView){
                    AdRequestFactory.getInstants().reportStartPageAdFinish(ILoader.PageAdState.COMPLETE);
                }else {
                    AdRequestFactory.getInstants().reportStartPageAd(true);
                }
                releaseVideoView();
                LibDeprecatedLogger.d("boot finish onCompletion");
                if (onFragmentInteractionListener != null) {
                    onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
                }
            }

            @Override
            public void adError() {
                mGlideImageView.setVisibility(View.GONE);
                if (onFragmentInteractionListener != null) {
                    onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
                }
            }

            @Override
            public void adSuccess() {
                mGlideImageView.setVisibility(View.GONE);
            }

            @Override
            public void adStart(String topViewPath, boolean isTopView) {
                BootFragment.this.isTopView = isTopView;
                BootFragment.this.topViewUrl = topViewPath;
                if (onFragmentInteractionListener != null) {
                    onFragmentInteractionListener.homeDataPreLoad(isTopView, topViewPath);
                }
                mHomeBg.setVisibility(View.GONE);
                mBeian.setVisibility(View.GONE);
            }
        });
        videoView.addAdVideoControlComponent(adStartImageControlView);
        videoView.addFilmProgressChangedListener(this);
        mTextView.setVisibility(View.GONE);
//        enterDetailHintImageView.setVisibility(View.GONE);
        mAd_enterdetail_hint_layout.setVisibility(View.GONE);
        mBottomBg.setVisibility(View.GONE);
        mTopBg.setVisibility(View.GONE);
    }

    private void requestPrivacyDialog() {
        NetworkApi.getWithCache(getActivity(), new Observer<ConfigInfoBatch>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(ConfigInfoBatch value) {

                if (value != null && value.getData() != null) {
                    PrivacyInfo privacyInfo = value.getData().getPrivacy_agreement();
                    PrefUtil.putString(AppConstants.KEY_PAY_INFO, value.getData().getPay_info().getPayInfo());
                    int privacyVersion = PrefUtil.getInt(AppConstants.KEY_ALERT_PRIVACY_VERSION, 0);
//                    LogManager.d(TAG, "privacyVersion ? " + privacyVersion);
//                    LogManager.d(TAG, "value.data.version ? " + value.data.version);
//                    LogManager.d(TAG, "value.data.isAlert ? " + value.data.is_alert);
//                    LogManager.d(TAG, "value.data.user_agreement_btn ? " + value.data.user_agreement_btn);
//                    LogManager.d(TAG, "value.data.privacy_btn ? " + value.data.privacy_btn);
//                    LogManager.d(TAG, "value.data.collect_info_btn ? " + value.data.collect_info_btn);
//                    LogManager.d(TAG, "value.data.third_info_btn ? " + value.data.third_info_btn);
//                    LogManager.d(TAG, "value.data.third_info_btn ? " + value.data.third_info_btn);
                    if (privacyInfo.is_alert == 1 && privacyInfo.version > privacyVersion) {
                        showPrivacyDialog(privacyInfo);
                    } else {
                        continueCreate();
                    }
                } else {
                    continueCreate();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("onError in getAboutInfo()error: " + e.getMessage(), e);
                continueCreate();
            }

            @Override
            public void onComplete() {

            }
        }, NetworkApi.networkInterface.getConfigInfoBatch("privacy_agreement,pay_info"), ConfigInfoBatch.class);
    }


    private void showPrivacyDialog(final PrivacyInfo privacyInfo) {
        PrivacyDialog dialog = new PrivacyDialog(getActivity(), new PrivacyDialog.ExitListener() {
            @Override
            public void onExit(boolean isConfirm) {
                if (isConfirm) {
                    PrefUtil.putInt(AppConstants.KEY_ALERT_PRIVACY_VERSION, privacyInfo.version);
                    continueCreate();
                } else {
                    SohuAppUtil.exitApp(getActivity());
                }
            }
        });
        dialog.setPrivacyInfo(privacyInfo);
        dialog.show();
    }

    private void continueCreate() {
        SohuAppUtil.init(getActivity().getApplicationContext());

//        showSplash();
        //青少年模式不要开屏广告
        if (TeenagersManger.isTeenager()) {
            onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
            ActivityLauncher.startTeenagersActivity(getContext(), -1L);
            return;
        }
        mBootPresenter = new BootPresenterImpl(getActivity(), this);
        mBootPresenter.createAndStartChildThread();
        mBootPresenter.getAdTips();


        Util.sHasShowPrivacyDialog = true;


        Util.uploadDeviceInfo(getActivity().getApplicationContext()); //TODO 上传设备信息 待优化

        if (getActivity().getIntent() != null) {
            String enterId = Util.getEnterId(0, getActivity().getLocalClassName(), mPartnerNo);
            RequestManager.getInstance().updateEnterId(enterId);
            RequestManager.getInstance().updateParnerId(mPartnerNo, mPlatformCode);
            RequestManager.getInstance().onMccEvent("1002", "0");
        }
        // startAdPreDownloadService();
        sendBroadcastToThirdPartner();

        if (Util.isSupportTouchVersion(getActivity())) {
            mGlideImageView.setOnClickListener(v -> startPayOrVideoDetailActivity());
        }

    }

    public void startPayOrVideoDetailActivity() {
        //当有跳转URL  启动图片加载完成的时候可以跳转
        AppLogger.d(TAG, "startPayOrVideoDetailActivity isPayAd ? " + isPayAd);
        if (isPayAd) {
            startPayActivity();
        } else if (isShown) {
            startVideoDetailActiviy();
            HashMap<String, String> pathInfo = new HashMap<>(1);
            pathInfo.put("pageId", "1048");
            RequestManager.getInstance().onAllEvent(new EventInfo(10306, "clk"), pathInfo, null, null);
        }
    }

    public void startVideoDetailActiviy() {
        AppLogger.d(TAG, "startVideoDetailActiviy aid ? " + aid + " dataType " + dataType);
        if (aid != 0 && dataType != -1) {
            ActivityLauncher.startVideoDetailBackToLauncher(getActivity(), Constant.PAGE_BOOT, aid, dataType, true);
//            getActivity().finish(); //TODO
            onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
            LibDeprecatedLogger.d("onHideFragment startLauncher 3");
        }
    }

    private void startPayActivity() {
        if (mCountDownTimer != null)
            mCountDownTimer.cancel();
        ActivityLauncher.startPayActivity(getActivity(), true, PayActivity.PAY_SOURCE_BOOT_ACTIVITY);
//        getActivity().finish(); //TODO:
        onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
        LibDeprecatedLogger.d("onHideFragment startLauncher 4");
    }

    private void sendBroadcastToThirdPartner() {
        LibDeprecatedLogger.d("send broadcast when started app");
        Intent intent = new Intent();
        intent.setAction("com.sohuott.tv.vod.START_APP");
        getActivity().sendBroadcast(intent);
    }


    public void releaseVideoView() {
//        if (mTextView != null) {
//            mTextView.setVisibility(View.GONE);
//            mTextView.postDelayed(() -> playerRoot.removeAllViews(), 200);
//        }
        if (videoView != null) {
            videoView.release();
        }
    }


    @Override
    public void setBootTips(BootTipsBean bootTips) {
        if (bootTips != null && bootTips.getData() != null && !"".equals(bootTips.getData().getContent())) {
            mBootTips = bootTips.getData().getContent();
            adStartImageControlView.setBootTips(mBootTips);
        }
    }

    @Override
    public void requestAd() {
        ShDataSource shDataSource = new ShDataSource();
        shDataSource.setAdType(ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_OPEN);
        shDataSource.setAdSkip(false);
        shDataSource.setGid(UserInfoHelper.getGid());
        videoView.setDataSource(shDataSource);
        videoView.prepareAsync();
//        RequestComponent requestComponent = new RequestComponent();
//        requestComponent.setTuv(UserInfoHelper.getGid());
//        requestComponent.setSite("1");
//        AdRequestFactory.getInstants().requestStartPageAd(getContext(), requestComponent, new AdStartPageCallBack() {
//            @Override
//            public void onAdStartPageVideo(@Nullable String s, @NonNull Map<String, Object> map) {
//                getAdParams(map);
//                mBeian.setVisibility(View.GONE);
//                tagForCountDownTimer = TAG_SHOW_ADS;
//
//            }
//
//            @Override
//            public void onAdStartPageImage(@Nullable String s, @NonNull Map<String, Object> map) {
//                getAdParams(map);
//                mBeian.setVisibility(View.GONE);
//                tagForCountDownTimer = TAG_SHOW_ADS;
//                isShowAds = true;
//                showAdsRes(s);
//            }
//
//            @Override
//            public void onAdStartPageError(@Nullable String s) {
//                LibDeprecatedLogger.d("setAdCommon adCommon == null Failed to get Ads' URL.");
//                tagForCountDownTimer = TAG_START_HOME;
//                onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
//            }
//        });
    }

    boolean isTopView = false;
    String topViewUrl = null;

    private void getAdParams(Map<String, Object> map) {
        Object topView = map.get(AdRequestFactory.AD_EXT_PARAMS_START_ENABLE_TOP_VIEW);
        if (topView != null) {
            isTopView = (boolean) topView;
        }
        Object focusVideo = map.get(AdRequestFactory.AD_EXT_PARAMS_START_FOCUS_VIDEO);
        if (focusVideo != null) {
            topViewUrl = (String) focusVideo;
        }

    }

    @Override
    public void onPlayerStateChanged(int state, @NonNull HashMap<String, Object> hashMap) {
        switch (state) {
            case PlayerConstants.VideoState.ERROR:
                if (videoView != null) {
                    videoView.setVisibility(View.GONE);
                }
                mHomeBg.setVisibility(View.VISIBLE);
                AdRequestFactory.getInstants().reportStartPageAd(true);
                releaseVideoView();
                onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
                LibDeprecatedLogger.e("boot finish onerror");
                LibDeprecatedLogger.d("onHideFragment startLauncher 5");
                break;
            case PlayerConstants.VideoState.PREPARING:
            case PlayerConstants.VideoState.PREPARED:
                videoView.getFilmVideoController().startUpdateProgress();
                break;
            case PlayerConstants.VideoState.PLAYING:
            case PlayerConstants.VideoState.PLAYING_BACK:
                mGlideImageView.setVisibility(View.GONE);
                if (mBottomBg != null) {
                    mBottomBg.setVisibility(View.GONE);
                }
                videoView.getFilmVideoController().startUpdateProgress();
//                startVideoCountDown(totalVideoDuration + 1000);
                break;
//            case PlayerConstants.VideoState.PLAYBACK_COMPLETED:
//                LibDeprecatedLogger.d("onCompletion");
//                mTextView.setVisibility(View.GONE);
//                mTextView = null;
//                releaseVideoView();
//                mHomeBg.setVisibility(View.VISIBLE);
//                LibDeprecatedLogger.d("boot finish onCompletion");
//                onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
//                if (isTopView) {
//                    AdRequestFactory.getInstants().reportStartPageAdFinish(ILoader.PageAdState.COMPLETE);
//                } else {
//                    AdRequestFactory.getInstants().reportStartPageAd(true);
//                }
//                break;
        }

    }

    @Override
    public void onScreenModeChanged(int mode) {

    }

    @Override
    public void onVideoProgressChanged(long pos, long dur) {
        totalVideoDuration = dur;
        totalVideoPos = pos;
    }

    private class MyCountDownTimer extends CountDownTimer {

        /**
         * Constructor of MyCountDownTimer
         *
         * @param millisInFuture: The number of millis in the future from the call
         *                        to {@link #start()} until the countdown is done and {@link #onFinish()}
         *                        is called.
         */
        public MyCountDownTimer(long millisInFuture) {
            super(millisInFuture, COUNTDOWN_INTERVAL);
        }

        /**
         * Show text of countdown timer while showing Ads.
         *
         * @param millisUntilFinished The amount of time until finished.
         */
        @Override
        public void onTick(long millisUntilFinished) {
            int time = (int) Math.ceil(millisUntilFinished / 1000) + 1;
            LibDeprecatedLogger.d("onTick(), time: " + time);
            if (time <= COUNTDOWN_DURATION) {
                mTextView.setVisibility(View.VISIBLE);
                mTextView.setText(String.valueOf(time));
            }
            if (isShowAds) {
                setIsCanBack(COUNTDOWN_DURATION, time);
            }

        }

        /**
         * Execute different action according to tagForCountDownTimer when countdown timer is done.
         */
        @Override
        public void onFinish() {
            LibDeprecatedLogger.d("onFinish(), TAG = " + tagForCountDownTimer);
            if (mCountDownTimer != null) {
                mCountDownTimer.cancel();
            }
            switch (tagForCountDownTimer) {
                case TAG_START_HOME:
                case TAG_LOAD_IMAGE:
//                    startHomeActivity();
                    //TODO 关闭fragment
                    onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
                    LibDeprecatedLogger.e("boot finish onFinish()");
                    LibDeprecatedLogger.d("onHideFragment startLauncher 6");
                    break;
//                case TAG_SHOW_ADS:
//                    showAdsImage();
//                    break;
                default:
                    break;
            }
        }
    }


    /**
     * Show Ads picture by Glide
     *
     * @param uri: Ads' URL
     */
    private void showAdsRes(String uri) {
        mGlideImageView.setImageRes(uri + "?time=" + System.currentTimeMillis(), getResources().getDrawable(R.drawable.ic_splash), getResources().getDrawable(R.drawable.ic_splash), true,
                new DrawableImageViewTarget(mGlideImageView) {
                    @Override
                    public void onLoadStarted(@Nullable Drawable placeholder) {
                        super.onLoadStarted(placeholder);
                        LibDeprecatedLogger.d("onLoadStarted(): BootActivity");
                        /**
                         * Cancel last countdown timer and start new timer with TAG_LOAD_IMAGE to record time of loading image
                         */
                        if (mCountDownTimer != null) {
                            mCountDownTimer.cancel();
                        }
                        tagForCountDownTimer = TAG_LOAD_IMAGE;
//                        if (mCountDownTimer != null) {
                        mCountDownTimer = new MyCountDownTimer(COUNTDOWN_DURATION);
//                            mCountDownTimer.start();
//                        }
                    }

                    @Override
                    public void onLoadFailed(@Nullable Drawable errorDrawable) {
                        super.onLoadFailed(errorDrawable);
                        LibDeprecatedLogger.w("onLoadFailed(): BootActivity");
                        /**
                         * Cancel last countdown timer and start HomeActivity directly.
                         */
                        if (mCountDownTimer != null) {
                            mCountDownTimer.cancel();
                        }
//                        startHomeActivity();
                        //TODO 关闭fragment
                        onFragmentInteractionListener.onHideFragment(isTopView, topViewUrl);
                    }

                    @Override
                    public void onResourceReady(Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        super.onResourceReady(resource, transition);
                        LibDeprecatedLogger.d("onResourceReady(): BootActivity");
                        if (flag == TYPE_AD_DEFAULT) {
                            getActivity().findViewById(R.id.ad_flag).setVisibility(View.VISIBLE);
                        } else if (flag == TYPE_AD_HIDE) {
                            try {
                                mRightEnterTextView.setText(Html.fromHtml(mBootTips));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        /**
                         * If tagForCountDownTimer equals TAG_LOAD_IMAGE, cancel last countDownTimer and start new timer with TAG_START_HOME.
                         * Else if tagForCountDownTimer equals other values, do nothing.
                         */
                        if (tagForCountDownTimer == TAG_LOAD_IMAGE) {
                            if (mCountDownTimer != null) {
                                mCountDownTimer.cancel();
                            } else {
                                return;
                            }
                            tagForCountDownTimer = TAG_START_HOME;
                            if (mCountDownTimer != null) {
                                mCountDownTimer = new MyCountDownTimer(COUNTDOWN_DURATION);
                                mCountDownTimer.start();
                            }
                            //If isShowAds is true, make enterDetailHintImageView visible and report to server.
                            if (isShowAds) {
                                if (aid != 0 && dataType != -1 && !isPayAd) {
                                    mAd_enterdetail_hint_layout.setVisibility(View.VISIBLE);
                                    enterDetailHintImageView.setImageDrawable(getResources().getDrawable(R.drawable.ok));
                                    mBottomBg.setVisibility(View.VISIBLE);
                                    mTopBg.setVisibility(View.VISIBLE);
                                    HashMap<String, String> pathInfo = new HashMap<>(1);
                                    pathInfo.put("pageId", "1048");
                                    RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), pathInfo, null, null);
                                }
//                                else if (isPayAd) {
//                                    enterDetailHintImageView.setVisibility(View.VISIBLE);
//                                    enterDetailHintImageView.setImageDrawable(getResources().getDrawable(R.drawable.ad_enterdetail_hint_pay));
//                                }
                                AdRequestFactory.getInstants().reportStartPageAd(false);
                            }
                        }
                    }

                    @Override
                    public void onStop() {
                        super.onStop();
                        if (mTextView != null) {
                            mTextView.setVisibility(View.GONE);
                        }
                        if (mAd_enterdetail_hint_layout != null) {
                            mAd_enterdetail_hint_layout.setVisibility(View.GONE);
                        }
                        if (mBottomBg != null) {
                            mBottomBg.setVisibility(View.GONE);
                        }
                        if (mTopBg != null) {
                            mTopBg.setVisibility(View.GONE);
                        }
                    }
                });
    }


    /**
     * 设置是否可以返回
     *
     * @param startTime 倒计时总时间  单位毫秒
     * @param currTime  当前倒计时时间 单位秒
     */
    private void setIsCanBack(long startTime, long currTime) {
        AppLogger.INSTANCE.v("startTime" + startTime);
        AppLogger.INSTANCE.v("currTime" + currTime);
//        if (isCanBack) return;
        //默认倒计时几秒后可返回
//        if (startTime - currTime * 1_000 >= CAN_BACK_TIME) {
        isCanBack = true;
        mTextView.setText("按返回键跳过 " + currTime);
//        }

    }

    @Override
    public void onPause() {
        super.onPause();
//        if (videoCountDownHandler != null) {
//            videoCountDownHandler.removeCallbacksAndMessages(null);
//        }
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }


        isShowAds = false;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LibDeprecatedLogger.d("onDestroy");
        //release resources
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        mGlideImageView = null;
        enterDetailHintImageView = null;
        mAd_enterdetail_hint_layout = null;
        mHomeBg = null;
        videoView = null;
        mTextView = null;
        mAdCommon = null;
//        backgroundHandler.removeCallbacks(updateRunnable);
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        onFragmentInteractionListener = null;
//        backgroundHandler.removeCallbacks(updateRunnable);
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }


    private void startVideoCountDown(long duration) {
        countDownTimer = new CountDownTimer(duration, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                LibDeprecatedLogger.d("开屏广告倒计时 : " + millisUntilFinished);
                if (millisUntilFinished / 1000 < 1) {
                    if (mTextView != null)
                        mTextView.setVisibility(View.GONE);

                } else {
                    if (mTextView != null) {
                        mTextView.setText(Util.getBootCountDownBackText(millisUntilFinished / 1000));
                        mTextView.setVisibility(View.VISIBLE);
                    }
                }

//                AdRequestFactory.getInstants().reportStartPageAdPlayTime((int) (duration - millisUntilFinished) / 1000, ILoader.TopViewSource.VIDEO_FIRST);
                LibDeprecatedLogger.d("开屏广告倒计时上报 : " + ((int) (duration - millisUntilFinished) / 1000));
            }

            @Override
            public void onFinish() {

            }
        };
        countDownTimer.start();
    }

    public interface OnFragmentInteractionListener {
        void onHideFragment(boolean isTopView, String topViewUrl);

        void homeDataPreLoad(boolean isTopView, String topViewUrl);
    }
}
