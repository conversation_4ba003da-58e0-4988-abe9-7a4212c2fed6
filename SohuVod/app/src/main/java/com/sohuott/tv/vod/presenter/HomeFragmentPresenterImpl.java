package com.sohuott.tv.vod.presenter;

import android.content.Context;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.view.IHomeFragmentCoursesView;
import com.sohuott.tv.vod.view.IHomeFragmentView;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by yizhang210244 on 2018/1/17.
 */
public class HomeFragmentPresenterImpl {

    private static final String TAG = HomeFragmentPresenterImpl.class.getSimpleName();

    private IHomeFragmentView mIHomeFragmentView;

    private IHomeFragmentCoursesView mIHomeFragmentCoursesView;

    protected CompositeDisposable compositeDisposable = new CompositeDisposable();

    private Context mContext;

    private long mChannelId;
    private LoginUserInformationHelper mHelper;

    public HomeFragmentPresenterImpl(IHomeFragmentView IHomeFragmentView, long channelId) {
        mIHomeFragmentView = IHomeFragmentView;
        mChannelId = channelId;
    }

    public HomeFragmentPresenterImpl(Context context, IHomeFragmentView IHomeFragmentView, IHomeFragmentCoursesView iHomeFragmentCoursesView,long channelId) {
        mIHomeFragmentView = IHomeFragmentView;
        mIHomeFragmentCoursesView = iHomeFragmentCoursesView;
        mChannelId = channelId;
        mContext = context;
    }

    public void getData() {
        AppLogger.d(TAG, "getData() mChannelId ? " + mChannelId);
        DisposableObserver disposableObserver = new DisposableObserver<HomeRecommendBean>() {
            @Override
            public void onNext(HomeRecommendBean value) {
                AppLogger.d(TAG, "onNext()");
                if (mIHomeFragmentView != null) {
                    mIHomeFragmentView.getDataSuccess(value);
                    refreshCoursesData(mContext);
                }
            }

            @Override
            public void onError(Throwable e) {
                AppLogger.d(TAG, "onError()");
                if(mIHomeFragmentView != null){
                    mIHomeFragmentView.getDataError(e);
                }
                LibDeprecatedLogger.d("onError = " + e.toString());
            }

            @Override
            public void onComplete() {
                AppLogger.d(TAG, "onComplete()");
            }
        };
        AppLogger.d(TAG, "NetworkApi.getHomeRecommend() mChannelId ? " + mChannelId);
        NetworkApi.getHomeRecommend(mChannelId, disposableObserver);
        compositeDisposable.add(disposableObserver);
    }

    //获取教育首页订单和付费标识
    //获取教育首页历史记录和收藏
    public void refreshCoursesData(Context context) {
    }



    public void refreshData() {
        DisposableObserver disposableObserver = new DisposableObserver<HomeRecommendBean>() {
            @Override
            public void onNext(HomeRecommendBean value) {
                if (mIHomeFragmentView != null) {
                    mIHomeFragmentView.refreshDataSuccess(value);
                }
            }

            @Override
            public void onError(Throwable e) {
                if(mIHomeFragmentView != null){
                    mIHomeFragmentView.refreshDataError(e);
                }
                LibDeprecatedLogger.d("onError = " + e.toString());
            }

            @Override
            public void onComplete() {
            }
        };
        NetworkApi.getHomeRecommend(mChannelId, disposableObserver);
        compositeDisposable.add(disposableObserver);
    }


    public void detachView() {
        mIHomeFragmentView = null;
        compositeDisposable.clear();
    }


}
