package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;

import com.sohuott.tv.vod.R;

public class KeyboardDisplayView extends LinearLayout {
    InputShowTextView tv_display_view_first, tv_display_view_second, tv_display_view_third, tv_display_view_fourth;
    int[] mNumberPool = new int[]{-1, -1, -1, -1};
    int mCurrentInputIndex = 0;

    public interface InputCompleteListener {
        void onInputComplete(int[] numberPool);
    }

    public void setInputCompleteListener(InputCompleteListener mInputCompleteListener) {
        this.mInputCompleteListener = mInputCompleteListener;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        this.mInputCompleteListener = null;
        mNumberPool = null;
    }

    private InputCompleteListener mInputCompleteListener;

    public KeyboardDisplayView(Context context) {
        super(context);
        init(context);
    }

    public KeyboardDisplayView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public KeyboardDisplayView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.layout_keyboard_display_view, this, true);
        tv_display_view_first = (InputShowTextView) findViewById(R.id.tv_display_view_first);
        tv_display_view_second = (InputShowTextView) findViewById(R.id.tv_display_view_second);
        tv_display_view_third = (InputShowTextView) findViewById(R.id.tv_display_view_third);
        tv_display_view_fourth = (InputShowTextView) findViewById(R.id.tv_display_view_fourth);
    }

    public void inputOne(int number) {
        if (mCurrentInputIndex > 3) {
            return;
        }
        if (mCurrentInputIndex < 0) {
            mCurrentInputIndex = 0;
        }
        mNumberPool[mCurrentInputIndex] = number;
        updateDisplay(mCurrentInputIndex);
        if (mCurrentInputIndex == 3) {
            if (mInputCompleteListener != null) {
                mInputCompleteListener.onInputComplete(mNumberPool);
            }
            return;
        }
        mCurrentInputIndex++;
    }

    private void updateDisplay(int index) {
        switch (index) {
            case 0:
                if (mNumberPool[0] == -1) {
                    tv_display_view_first.clearText();
                } else {
                    tv_display_view_first.showText(mNumberPool[0] + "");
                }
                break;
            case 1:
                if (mNumberPool[1] == -1) {
                    tv_display_view_second.clearText();
                } else {
                    tv_display_view_second.showText(mNumberPool[1] + "");
                }
                break;
            case 2:
                if (mNumberPool[2] == -1) {
                    tv_display_view_third.clearText();
                } else {
                    tv_display_view_third.showText(mNumberPool[2] + "");
                }
                break;
            case 3:
                if (mNumberPool[3] == -1) {
                    tv_display_view_fourth.clearText();
                } else {
                    tv_display_view_fourth.showText(mNumberPool[3] + "");
                }
                break;
        }


    }

    public void deleteOne() {
        if (mCurrentInputIndex < 0) {
            return;
        }
        mCurrentInputIndex--;
        switch (mCurrentInputIndex) {
            case 3:
                tv_display_view_fourth.clearText();
                mNumberPool[3] = -1;
                break;
            case 2:
                tv_display_view_third.clearText();
                mNumberPool[2] = -1;
                break;
            case 1:
                tv_display_view_second.clearText();
                mNumberPool[1] = -1;
                break;
            case 0:
                tv_display_view_first.clearText();
                mNumberPool[0] = -1;
                break;
        }
        updateDisplay(mCurrentInputIndex);
    }

    public void displayReset() {
        mCurrentInputIndex = 0;
        mNumberPool[0] = -1;
        mNumberPool[1] = -1;
        mNumberPool[2] = -1;
        mNumberPool[3] = -1;
        tv_display_view_first.clearText();
        tv_display_view_second.clearText();
        tv_display_view_third.clearText();
        tv_display_view_fourth.clearText();
    }

}
