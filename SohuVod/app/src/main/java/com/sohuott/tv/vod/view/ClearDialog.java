package com.sohuott.tv.vod.view;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.utils.FocusUtil;

/**
 * Created by wenjingbian on 2017/3/31.
 */

public class ClearDialog extends Dialog implements DialogInterface.OnDismissListener {

    private Button btn_dialog_clear_item, btn_dialog_clear_all, btn_dialog_clear_ok;

    private TextView tv_dialog_clear_title;

    public ClearDialog(@NonNull Context context) {
        super(context, R.style.UpdateDialog);
        init();
    }

    public ClearDialog(Context context, int theme) {
        super(context, theme);
        init();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_clear);

        btn_dialog_clear_item = (Button) findViewById(R.id.btn_dialog_clear_item);
        btn_dialog_clear_all = (Button) findViewById(R.id.btn_dialog_clear_all);
        btn_dialog_clear_ok = (Button) findViewById(R.id.btn_dialog_clear_ok);
        tv_dialog_clear_title = (TextView) findViewById(R.id.tv_dialog_clear_title);

        btn_dialog_clear_item.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    FocusUtil.setFocusAnimator(v, 1.07f, 300);
                } else {
                    FocusUtil.setUnFocusAnimator(v);
                }
            }
        });
        btn_dialog_clear_all.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    FocusUtil.setFocusAnimator(v, 1.07f, 300);
                } else {
                    FocusUtil.setUnFocusAnimator(v);
                }
            }
        });
        setOnDismissListener(this);
    }

    private void init(){
        Window win = this.getWindow();
        win.requestFeature(Window.FEATURE_NO_TITLE);
        win.getDecorView().setPadding(0, 0, 0, 0);
        WindowManager.LayoutParams lp = win.getAttributes();
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        lp.windowAnimations = R.style.BottomInOutStyle;
        lp.gravity = Gravity.BOTTOM;
        win.setAttributes(lp);
        win.setBackgroundDrawableResource(android.R.color.transparent);
    }

    public void setClearItemListener(View.OnClickListener onClickListener) {
        btn_dialog_clear_item.setOnClickListener(onClickListener);
    }

    public void setClearAllListener(View.OnClickListener onClickListener) {
        btn_dialog_clear_all.setOnClickListener(onClickListener);
    }

    public void setClearNetualListener(View.OnClickListener onClickListener) {
        btn_dialog_clear_item.setVisibility(View.GONE);
        btn_dialog_clear_all.setVisibility(View.GONE);
        btn_dialog_clear_ok.setVisibility(View.VISIBLE);
        btn_dialog_clear_ok.setOnClickListener(onClickListener);
    }

    public void setTitle(String title) {
        tv_dialog_clear_title.setVisibility(View.VISIBLE);
        tv_dialog_clear_title.setText(title);
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        FocusUtil.clearAnimation();
    }
}
