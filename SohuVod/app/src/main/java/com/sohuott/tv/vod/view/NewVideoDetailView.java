package com.sohuott.tv.vod.view;


import com.sohuott.tv.vod.lib.model.ActorMovies;
import com.sohuott.tv.vod.lib.model.AlbumInfo;
import com.sohuott.tv.vod.lib.model.SuperHouse;
import com.sohuott.tv.vod.lib.model.VideoDetailFilmCommodities;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;

import java.util.List;

public interface NewVideoDetailView {
    void showLoading();

    void hideLoading();

    void addAlbumData(AlbumInfo model);

    void onAlbumError(int status);

    void onRecommendError();

    void addRecommendData(VideoDetailRecommend response);

    void onCommodityError();

    void addCommodityData(VideoDetailFilmCommodities response);

    void addPgcProducerList(List<AlbumInfo.DataEntity.ActorsEntity> actors);

    void onPgcProducerError();

    void showToast(String text);

    void showToast(int resId);

    void updateUserTicket();

    void updateLikeStatus(boolean isLiked);

    void updateChaseStatus(int chansed);

    void postVideoDetailChaseSuccess();

    void postVideoDetailCancelChaseSuccess();

    void postVideoDetailChaseCompleted();

    void addActorMovies(ActorMovies actorMovies);

    void addSuperHouse(SuperHouse superHouse);
    void addActor(List<AlbumInfo.DataEntity.ActorsEntity> actors);
}
