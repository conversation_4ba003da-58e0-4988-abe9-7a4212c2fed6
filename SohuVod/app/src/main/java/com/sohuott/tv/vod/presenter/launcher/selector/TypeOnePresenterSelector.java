package com.sohuott.tv.vod.presenter.launcher.selector;


import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.base.BasePresenterSelector;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.presenter.launcher.HistoryPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeOneContentPresenter;

public class TypeOnePresenterSelector extends BasePresenterSelector {
    HistoryPresenter historyPresenter = new HistoryPresenter();
    @Override
    public Presenter getPresenter(Object o) {
        if (o instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean){
            return new TypeOneContentPresenter();
        } else if (o instanceof PlayHistory){
            return historyPresenter;
        }
        return new TypeOneContentPresenter();
    }
}
