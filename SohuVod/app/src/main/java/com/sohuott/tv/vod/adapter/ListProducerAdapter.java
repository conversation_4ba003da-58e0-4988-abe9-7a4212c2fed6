package com.sohuott.tv.vod.adapter;

import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ProducerVideoList.DataEntity.ResultEntity.VideoDetails;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.ListProducerView;
import com.sohuott.tv.vod.widget.CornerTagImageView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by XiyingCao on 16-3-14.
 */
public class ListProducerAdapter extends RecyclerView.Adapter<ListProducerAdapter.ViewHolder> {

    public interface FocusController {
        void onNextFocusView(boolean isRequestFocus);
    }

    private ListProducerView mListView;
    private FocusBorderView mFocusBorderView;
    private RecyclerView mRecyclerView;
    private FocusController mFocusController;
    private int mSelctedPos = 0;
    private ArrayList<VideoDetails> mModels;
    private int mActorId;

    public ListProducerAdapter(int id, ListProducerView view, RecyclerView recyclerView) {
        mModels = new ArrayList<>();
        mActorId = id;
        mListView = new WeakReference<ListProducerView>(view).get();
        this.mRecyclerView = recyclerView;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View modelView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_list_producer, viewGroup, false);
        return new ViewHolder(modelView);
    }

    @Override
    public void onBindViewHolder(ViewHolder viewHolder, int position) {
        VideoDetails model = mModels.get(position);

        viewHolder.mTitleView.setText(model.videoTitle);
        if (model.playCount < 5000) {
            viewHolder.mPlayCount.setVisibility(View.GONE);
            viewHolder.mPlayCountIcon.setVisibility(View.GONE);
            viewHolder.mNewView.setVisibility(View.INVISIBLE);
        } else {
            viewHolder.mPlayCountBg.setVisibility(View.GONE);
            viewHolder.mPlayCount.setVisibility(View.INVISIBLE);
            viewHolder.mPlayCountIcon.setVisibility(View.INVISIBLE);
            viewHolder.mNewView.setVisibility(View.GONE);
            viewHolder.mPlayCount.setText("");
        }
        if (model.cateCodeFirst == CornerTagImageView.CORNER_TYPE_VR) {
            viewHolder.mImageView.setCornerTypeWithType(0, 0, 0 ,0, CornerTagImageView.CORNER_TYPE_VR);
        } else if (model.cateCodeFirst == CornerTagImageView.CORNER_TYPE_SOHUCLASS) {
            viewHolder.mImageView.setCornerTypeWithType(0, 0, 0, 0, CornerTagImageView.CORNER_TYPE_NONE);
        } else {
            viewHolder.mImageView.setCornerTypeWithType(0, 0, 0, 0, CornerTagImageView.CORNER_TYPE_PGC);
        }
        viewHolder.mImageView.setCornerHeightRes(R.dimen.y33);
        viewHolder.mImageView.setImageRes(model.cover320);
    }

    @Override
    public int getItemCount() {
        if (mModels == null) {
            return 0;
        }
        return mModels.size();
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.mFocusBorderView = focusBorderView;
    }

    public void setFocusController(FocusController focusController) {
        this.mFocusController = focusController;
    }

    public void clear() {
        mModels.clear();
        notifyDataSetChanged();
        mRecyclerView.smoothScrollToPosition(0);
    }

    public void add(VideoDetails model) {
        mModels.add(model);
        notifyDataSetChanged();
    }

    public void add(List<VideoDetails> models) {
        if (models != null && models.size() > 0) {
            int start = mModels.size();
            this.mModels.addAll(models);
            notifyItemRangeInserted(start, models.size());
        }
    }

    public void setActorId(int actorId) {
        mActorId = actorId;
    }


    public void setSelctedPos(int selctedPos) {
        mSelctedPos = selctedPos;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        public CornerTagImageView mImageView;
        View mPlayCountBg;
        TextView mPlayCount;
        ImageView mPlayCountIcon;
        TextView mNewView;
        TextView mTitleView;

        public ViewHolder(View v) {
            super(v);
            mPlayCountBg = v.findViewById(R.id.count_bg);
            mPlayCount = (TextView) v.findViewById(R.id.play_count);
            mPlayCountIcon = (ImageView) v.findViewById(R.id.play_count_icon);
            mNewView = (TextView) v.findViewById(R.id.new_string);
            mTitleView = (TextView) v.findViewById(R.id.title);
            mImageView = (CornerTagImageView) v.findViewById(R.id.image);

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
                        mSelctedPos = getAdapterPosition();

                        mTitleView.setSelected(true);
                        mTitleView.setMarqueeRepeatLimit(-1);
                        mTitleView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                        mImageView.setSelected(true);
                        mPlayCountBg.setVisibility(View.VISIBLE);
                        if (mPlayCount.getVisibility() == View.INVISIBLE) {
                            mPlayCount.setVisibility(View.VISIBLE);
                            mPlayCountIcon.setVisibility(View.VISIBLE);
                        } else {
                            mNewView.setVisibility(View.VISIBLE);
                        }
                        mListView.updateCountText(mSelctedPos);

                        if (mRecyclerView != null && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                            if (mFocusBorderView != null) {
                                mFocusBorderView.setFocusView(v);
                            }
                            FocusUtil.setFocusAnimator(v, mFocusBorderView);
                        }
                    } else {
                        mTitleView.setSelected(false);
                        mTitleView.setEllipsize(TextUtils.TruncateAt.END);
                        mImageView.setSelected(false);
                        mPlayCountBg.setVisibility(View.GONE);
                        if (mPlayCount.getVisibility() == View.VISIBLE) {
                            mPlayCount.setVisibility(View.INVISIBLE);
                            mPlayCountIcon.setVisibility(View.INVISIBLE);
                        } else {
                            mNewView.setVisibility(View.INVISIBLE);
                        }

                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(v);
                        }
                        FocusUtil.setUnFocusAnimator(v);
                    }
                }
            });
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    VideoDetails model = mModels.get(getAdapterPosition());
                    ActivityLauncher.startVideoDetailActivity(v.getContext(), model.videoId, Constant.DATA_TYPE_PGC, Constant.PAGE_PRODUCER);
                    RequestManager.getInstance().onProducerVideoListClickEvent(mActorId, model.cateCode, model.videoId);
                }
            });
            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_DPAD_UP:
                            if (mSelctedPos < 4) {
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            if (mSelctedPos / 4 == (getItemCount() - 1) / 4) {
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                            if (event.getAction() == KeyEvent.ACTION_DOWN) {
                                CustomGridLayoutManager layoutManager = (CustomGridLayoutManager) mRecyclerView.getLayoutManager();
                                int spanCount = layoutManager.getSpanCount();
                                int itemIndex = getAdapterPosition();

                                if (mSelctedPos == (getItemCount() - 1)) {
                                    return true;
                                }

                                if (itemIndex % spanCount != spanCount - 1) {
                                    return false;
                                } else {
                                    if (mRecyclerView.findViewHolderForAdapterPosition(itemIndex + 1) != null
                                            && mRecyclerView.findViewHolderForAdapterPosition(itemIndex + 1).itemView != null) {
                                        mRecyclerView.findViewHolderForAdapterPosition(itemIndex + 1).itemView.requestFocus();
                                    } else {
                                        mFocusBorderView.setUnFocusView(itemView);
                                        FocusUtil.setUnFocusAnimator(itemView);
                                        mFocusController.onNextFocusView(true);
                                        mRecyclerView.smoothScrollToPosition(itemIndex + 1);
                                    }

                                    return true;
                                }
                            }
                            break;
                        default:
                            break;
                    }
                    return false;
                }
            });
        }
    }
}

