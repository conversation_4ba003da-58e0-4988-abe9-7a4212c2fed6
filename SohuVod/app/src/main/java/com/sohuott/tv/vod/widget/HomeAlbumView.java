package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.google.gson.Gson;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.utils.HomeUtils;
import com.sohuott.tv.vod.model.HomeCarouseParameterModel;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;

/**
 * Created by fenglei on 17-2-17.
 */

public class HomeAlbumView extends RelativeLayout
        implements View.OnClickListener, View.OnFocusChangeListener {

    private CornerTagImageView posterIV;
    private TextView titleTV;
    private TextView subTitleTV;
    private TextView hintTV;
    private TextView scoreTV;
    private ImageView scoreIV;
    private FocusBorderView mFocusBorderView;  //焦点框
    private HomeRecommendBean.Data.Content mContent;
    private long mChannelListId;
    private boolean mResizeEnable = false;
    private int mWidth, mHeight;
    private boolean mIsdts = false;

    public HomeAlbumView(Context context) {
        super(context);
        init(context);
    }

    public HomeAlbumView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public HomeAlbumView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.home_album_view, this, true);
        posterIV = (CornerTagImageView) findViewById(R.id.posterIV);
        titleTV = (TextView) findViewById(R.id.titleTV);
        subTitleTV = (TextView) findViewById(R.id.subTitleTV);
        hintTV = (TextView) findViewById(R.id.hintTV);
        scoreTV = (TextView) findViewById(R.id.scoreTV);
        scoreIV = (ImageView) findViewById(R.id.doubangIV);
        posterIV.setCornerHeightRes(R.dimen.y40);
        setBackgroundResource(R.drawable.recommend_item_selector);
        setFocusable(true);
        setOnClickListener(this);
        setOnFocusChangeListener(this);
    }

    public void setDts(boolean isdts) {
        mIsdts = isdts;
    }

    public void setResize(int width, int height) {
        mResizeEnable = true;
        mWidth = width;
        mHeight = height;
    }

    public void setData(HomeRecommendBean.Data.Content content, long channelListId) {
        if (content == null) {
            return;
        }
        mContent = content;
        mChannelListId = channelListId;
        String imageUrl = HomeUtils.getHomeImageUrl(content, HomeUtils.ALBUM_TYPE);
        if(!TextUtils.isEmpty(imageUrl)) {
            posterIV.setImageRes(imageUrl,false);
        }

        String name = content.getName();
        String comment = content.getTvComment();

        int type = Integer.parseInt(content.getType());
        if (type == HomeRecommendBean.CAROUSE_TYPE) {
            try {
                Gson gson = new Gson();
                HomeCarouseParameterModel parameter = gson.fromJson(content.getParameter(), HomeCarouseParameterModel.class);
                if (parameter != null) {
                    name = parameter.getOrder() + " " + content.getName();
                    comment = parameter.getComment();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if(mIsdts){
            posterIV.setCornerTypeWithType(0,0,0,0,CornerTagImageView.CORNER_TYPE_DTS);
        }else {
            HomeViewJump.setCornerType(content, posterIV);
        }
        titleTV.setText(name);
        subTitleTV.setText(comment);
        setHintTV(content.getAlbumParam());
    }

    public void setAlbumVisibility(boolean value){

        if(mContent == null){
            return;
        }
        String imageUrl = HomeUtils.getHomeImageUrl(mContent, HomeUtils.ALBUM_TYPE);
        if(!TextUtils.isEmpty(imageUrl)){
            if(value){
                posterIV.setImageRes(imageUrl,false);
            }else {
                posterIV.clearImageMemo();
            }
        }
    }


    private void setHintTV(HomeRecommendBean.Data.Content.AlbumParam albumParam) {
        try {
            if (albumParam != null) {
                int cateCode = Integer.parseInt(albumParam.getCateCode());
                //100：电影；101：电视剧；106：综艺；107：纪录片；115：动漫；10001：美剧；
                if (cateCode == 101 || cateCode == 107 || cateCode == 115 || cateCode == 10001 || cateCode == 119) {
                    int tvSets = Integer.parseInt(albumParam.getTvSets());
                    int latestVideoCount = Integer.parseInt(albumParam.getLatestVideoCount());
                    if (latestVideoCount != 0) {
                        if (tvSets == latestVideoCount) {
                            hintTV.setText(latestVideoCount + "集全");
                            setHintTVUI(0, albumParam.getLatestVideoCount().length());
                        } else {
                            hintTV.setText("更新至" + latestVideoCount + "集");
                            setHintTVUI(3, albumParam.getLatestVideoCount().length());
                        }
                        hintTV.setBackgroundResource(R.drawable.home_album_hint_tv_bg);
                        hintTV.setVisibility(View.VISIBLE);
                    } else {
                        hintTV.setVisibility(View.GONE);
                    }
                } else if (cateCode == 106) {
                    if (!TextUtils.isEmpty(albumParam.getShowDate())) {
                        hintTV.setBackgroundResource(R.drawable.home_album_hint_tv_bg);
                        hintTV.setText(albumParam.getShowDate() + "期");
                        hintTV.setVisibility(View.VISIBLE);
                    } else {
                        hintTV.setVisibility(View.GONE);
                    }
                } else {
                    hintTV.setVisibility(View.GONE);
                }
            } else {
                hintTV.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            hintTV.setVisibility(View.GONE);
        }
    }

    private void setHintTVUI(int index, int length) {
        ForegroundColorSpan whiteSpan = new ForegroundColorSpan(Color.parseColor("#d5d5d5"));
        ForegroundColorSpan yellowSpan = new ForegroundColorSpan(Color.parseColor("#ffbd5f"));
        SpannableStringBuilder builder = new SpannableStringBuilder(hintTV.getText().toString());
        if (index > 0) {
            builder.setSpan(whiteSpan, 0, index, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        builder.setSpan(yellowSpan, index, index + length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        builder.setSpan(whiteSpan, index + length, hintTV.getText().toString().length(),
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        hintTV.setText(builder);
    }

    @Override
    public void onClick(View v) {
        if (mContent == null) {
            return;
        }

        HomeViewJump.clickAlbum(getContext(), mContent, mChannelListId,mIsdts,-1);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
            if (mFocusBorderView != null) {
                mFocusBorderView.setFocusView(v);
            }
            FocusUtil.setFocusAnimator(v, mFocusBorderView);
            setTVOnFocus(titleTV);
            setTVOnFocus(subTitleTV);
            showScore();
        } else {
            if (mFocusBorderView != null) {
                mFocusBorderView.setUnFocusView(v);
            }
            FocusUtil.setUnFocusAnimator(v);
            setTVUnFocus(titleTV);
            setTVUnFocus(subTitleTV);
            hideScore();
        }
        setTextColor(hasFocus);
    }

    private void setTextColor(boolean focus) {
        if (focus) {
            titleTV.setTextColor(getResources().getColor(R.color.home_focused_color));
            subTitleTV.setTextColor(getResources().getColor(R.color.home_focused_color));
        } else {
            titleTV.setTextColor(getResources().getColor(R.color.home_title_color));
            subTitleTV.setTextColor(getResources().getColor(R.color.home_subtitle_color));
        }
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

    private void showScore() {
        if (mContent != null) {
            HomeRecommendBean.Data.Content.AlbumParam albumParam = mContent.getAlbumParam();
            if (albumParam != null) {
                try {
                    String scoreSource = albumParam.getScoreSource();
                    if ("1".equals(scoreSource)) {
                        if (!TextUtils.isEmpty(albumParam.getScore())) {
                            scoreTV.setText(albumParam.getScore());
                            scoreTV.setVisibility(View.VISIBLE);
                            scoreIV.setVisibility(View.GONE);
                        }
                    } else if ("2".equals(scoreSource)) {
                        if (!TextUtils.isEmpty(albumParam.getDoubanScore())) {
                            scoreTV.setText(albumParam.getDoubanScore());
                            scoreTV.setVisibility(View.VISIBLE);
                            scoreIV.setVisibility(View.VISIBLE);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void hideScore() {
        scoreIV.setVisibility(View.GONE);
        scoreTV.setVisibility(View.GONE);
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        mFocusBorderView = focusView;
    }

    OnClickCallback mOnClickCallback;

    public void setOnClickCallback(OnClickCallback mOnClickCallback) {
        this.mOnClickCallback = mOnClickCallback;
    }

    public interface  OnClickCallback{
        void onClick(long loopChannelId,int order);
    }

}
