package com.sohuott.tv.vod.view;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.app.SohuAppUtil;
import com.sohuott.tv.vod.presenter.UpdatePresenter;
import com.sohuott.tv.vod.utils.UpdateHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by yizhang210244 on 2017/5/12.
 */

public class UpdateDialogNew extends Dialog implements View.OnFocusChangeListener {

    private static final String TAG = UpdateDialogNew.class.getSimpleName();

    private Context mContext;

    private ProgressBar mProgressBar;

    private Button btnPositive, btnNegative;

    private TextView mTvTitle, desc1textview, desc2textview, desc3textview, desc4textview;


    public UpdateDialogNew(Context context) {
        super(context, R.style.UpdateDialogNew);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.dialog_upgrade);
        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        mProgressBar = (ProgressBar) findViewById(R.id.pb_download);
        btnPositive = (Button) findViewById(R.id.btn_dialog_positive);
        btnNegative = (Button) findViewById(R.id.btn_dialog_negative);
        btnPositive.setOnFocusChangeListener(this);
        btnNegative.setOnFocusChangeListener(this);
        mTvTitle = (TextView) findViewById(R.id.tv_dialog_title);
        desc1textview = (TextView) findViewById(R.id.desc_1_textview);
        desc2textview = (TextView) findViewById(R.id.desc_2_textview);
        desc3textview = (TextView) findViewById(R.id.desc_3_textview);
        desc4textview = (TextView) findViewById(R.id.desc_4_textview);
    }

    private void setTitle(String title) {
        mTvTitle.setText(title);
    }

    private void setMessage(String msg) {
        AppLogger.d(TAG, "setMessage msg ?" + msg);

        List<LittleDesc> list = getNewUpdateDesc(msg);
        if (list != null) {
            int size = Math.min(4, list.size());
            switch (size) {
                case 0:
                    desc1textview.setVisibility(View.GONE);
                    desc2textview.setVisibility(View.GONE);
                    desc3textview.setVisibility(View.GONE);
                    desc4textview.setVisibility(View.GONE);
                    break;
                case 1:
                    desc1textview.setVisibility(View.VISIBLE);
                    desc2textview.setVisibility(View.GONE);
                    desc3textview.setVisibility(View.GONE);
                    desc4textview.setVisibility(View.GONE);
                    break;
                case 2:
                    desc1textview.setVisibility(View.VISIBLE);
                    desc2textview.setVisibility(View.VISIBLE);
                    desc3textview.setVisibility(View.GONE);
                    desc4textview.setVisibility(View.GONE);
                    break;
                case 3:
                    desc1textview.setVisibility(View.VISIBLE);
                    desc2textview.setVisibility(View.VISIBLE);
                    desc3textview.setVisibility(View.VISIBLE);
                    desc4textview.setVisibility(View.GONE);
                    break;
                case 4:
                    desc1textview.setVisibility(View.VISIBLE);
                    desc2textview.setVisibility(View.VISIBLE);
                    desc3textview.setVisibility(View.VISIBLE);
                    desc4textview.setVisibility(View.VISIBLE);
                    break;
            }

            for (int i = 0; i < size; i++) {
                LittleDesc desc = list.get(i);
                if (desc != null) {
                    AppLogger.d(TAG, "desc ?" + desc.toString());
                    switch (desc.getOrder()) {
                        case 1:
                            if (desc != null) {
                                desc1textview.setText("・" + desc.getContent());
                            }
                            break;
                        case 2:
                            if (desc != null) {
                                desc2textview.setText("・" + desc.getContent());
                            }
                            break;
                        case 3:
                            if (desc != null) {
                                desc3textview.setText("・" + desc.getContent());
                            }
                            break;
                        case 4:
                            if (desc != null) {
                                desc4textview.setText("・" + desc.getContent());
                            }
                            break;
                    }
                }
            }

        } else {
            desc1textview.setVisibility(View.GONE);
            desc2textview.setVisibility(View.GONE);
            desc3textview.setVisibility(View.GONE);
            desc4textview.setVisibility(View.GONE);
        }

    }

    private void setPositiveText(String positiveText) {
        if (positiveText == null || positiveText.equals(""))
            btnPositive.setVisibility(View.GONE);
        else
            btnPositive.setText(positiveText);
    }

    private void setNegativeText(String negativeText) {
        if (negativeText == null || negativeText.equals(""))
            btnNegative.setVisibility(View.GONE);
        else
            btnNegative.setText(negativeText);
    }

    private void setPositiveListener(View.OnClickListener btnPositiveListener) {
        btnPositive.setOnClickListener(btnPositiveListener);
    }

    private void setNegativeListener(View.OnClickListener btnNegativeListener) {
        btnNegative.setOnClickListener(btnNegativeListener);
    }

    /**
     * Set dialog's view when downloading APK file
     */
    public void setDownloadingView(final UpdatePresenter updatePresenter, final int updateType) {
        mTvTitle.setText(mContext.getString(R.string.dialog_update_downloading));
//        mTvMsg.setText("");
        mProgressBar.setVisibility(View.VISIBLE);
        btnPositive.setClickable(false);
        btnPositive.setText(R.string.dialog_update_btn_prepare);
        btnNegative.setText(R.string.dialog_update_btn_cancel);
        btnNegative.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                stopDownloadTask(updatePresenter, updateType);
            }
        });
        setOnCancelListener(new OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                stopDownloadTask(updatePresenter, updateType);
            }
        });
    }

    private void stopDownloadTask(UpdatePresenter updatePresenter, int updateType) {
        if (updateType == UpdateHelper.TAG_UPGRADE_USUAL) {
            updatePresenter.stopUpdateTask();
            dismiss();
            if (mExistListener != null) {
                mExistListener.onExist();
            }
        } else if (updateType == UpdateHelper.TAG_UPGRADE_FORCE) {
            updatePresenter.stopUpdateTask();
            SohuAppUtil.exitApp(mContext);
        }
    }

    private UpdateHelper.ExistListener mExistListener;
    public void setExistListener(UpdateHelper.ExistListener existListener) {
        mExistListener = existListener;
    }

    public void updateDownloadProgress(int value) {
        mProgressBar.setProgress(value);
        btnPositive.setText(mContext.getString(R.string.dialog_update_btn_download) + value + "%");
    }


    private List<LittleDesc> getNewUpdateDesc(String newDesc) {
        Gson gson = new Gson();
        JsonParser parser = new JsonParser();
        JsonArray jsonArray = parser.parse(newDesc).getAsJsonArray();
        AppLogger.v("jsonArray=" + jsonArray);
        if (jsonArray != null) {
            ArrayList<LittleDesc> lcs = new ArrayList<LittleDesc>();
            for (JsonElement obj : jsonArray) {
                LittleDesc cse = gson.fromJson(obj, LittleDesc.class);
                AppLogger.v("cse=" + cse);
                lcs.add(cse);
            }
            AppLogger.v("lcs=" + lcs.size());
            return lcs;
        } else {
            AppLogger.v("lcs为空");
            return null;
        }
    }

    /**
     * Called when the focus state of a view has changed.
     *
     * @param v        The view whose state has changed.
     * @param hasFocus The new focus state of v.
     */
    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (v != null && v instanceof Button) {
            Button button = (Button) v;
            if (hasFocus) {
                button.setTextColor(0xFFE8E8FF);
                button.setScaleX(1.1f);
                button.setScaleY(1.1f);
            } else {
                button.setTextColor(0xB3E8E8FF);
                button.setScaleX(1.0f);
                button.setScaleY(1.0f);
            }
        }

    }



    public static class Builder {
        public String mMsg, mTitle, mBtnPositiveText, mBtnNegativeText;
        public View.OnClickListener mBtnPositiveListener, mBtnNegativeListener;
        public OnCancelListener mCancelListener;

        private Context context;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setTitle(int titleRes) {
            this.mTitle = context.getString(titleRes);
            return this;
        }

        public Builder setTitle(String title) {
            this.mTitle = title;
            return this;
        }

        public Builder setMsg(String msg) {
            this.mMsg = msg;
            return this;
        }

        public Builder setMsg(int msgRes) {
            this.mMsg = context.getString(msgRes);
            return this;
        }

        public Builder setPositiveButton(String positiveText, View.OnClickListener btnPositiveListener) {
            this.mBtnPositiveText = positiveText;
            this.mBtnPositiveListener = btnPositiveListener;
            return this;
        }

        public Builder setPositiveButton(int positiveTextRes, View.OnClickListener btnPositiveListener) {
            this.mBtnPositiveText = context.getString(positiveTextRes);
            this.mBtnPositiveListener = btnPositiveListener;
            return this;
        }

        public Builder setNegativeButton(String negativeText, View.OnClickListener btnNegativeListener) {
            this.mBtnNegativeText = negativeText;
            this.mBtnNegativeListener = btnNegativeListener;
            return this;
        }

        public Builder setNegativeButton(int negativeTextRes, View.OnClickListener btnNegativeListener) {
            this.mBtnNegativeText = context.getString(negativeTextRes);
            this.mBtnNegativeListener = btnNegativeListener;
            return this;
        }

        public Builder setCancelListener(OnCancelListener cancelListener) {
            this.mCancelListener = cancelListener;
            return this;
        }

        public UpdateDialogNew create() {
            UpdateDialogNew dialog = new UpdateDialogNew(context);
            dialog.show();
            dialog.setTitle(mTitle);
            dialog.setMessage(mMsg);
            dialog.setPositiveText(mBtnPositiveText);
            dialog.setNegativeText(mBtnNegativeText);
            dialog.setPositiveListener(mBtnPositiveListener);
            dialog.setNegativeListener(mBtnNegativeListener);
            dialog.setOnCancelListener(mCancelListener);
            return dialog;
        }

        public UpdateDialogNew show() {
            return create();
        }
    }
}