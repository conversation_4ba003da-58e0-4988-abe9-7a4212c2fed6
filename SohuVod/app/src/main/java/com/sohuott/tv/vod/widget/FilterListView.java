package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;

/**
 * Created by XiyingCao on 16-3-7.
 */
public class FilterListView extends android.widget.HorizontalScrollView {
    private final LinearLayout mTabLayout;

    public FilterListView(Context context) {
        this(context, null);
    }

    public FilterListView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setHorizontalScrollBarEnabled(false);
        mTabLayout = new LinearLayout(context);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT);
//        layoutParams.leftMargin = (int)getResources().getDimension(R.dimen.x200);
        addView(mTabLayout, layoutParams);
    }


}
