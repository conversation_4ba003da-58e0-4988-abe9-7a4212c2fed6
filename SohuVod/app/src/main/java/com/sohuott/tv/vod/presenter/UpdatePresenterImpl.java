package com.sohuott.tv.vod.presenter;

import android.content.Context;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.app.App;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AboutInfo;
import com.sohuott.tv.vod.lib.model.UpdateInfo;
import com.sohuott.tv.vod.lib.model.WechatPublic;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.task.UpdateTask;
import com.sohuott.tv.vod.view.AboutView;
import java.lang.ref.WeakReference;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by wenjingbian on 2016/3/18.
 */
public class UpdatePresenterImpl implements UpdatePresenter {

    private static final String PARENT_ID = "80151001";

    private UpdateTask mUpdateTask;

    private AboutView mAboutView;

    private Context mContext;

    public UpdatePresenterImpl(Context context, AboutView aboutView) {
        this.mAboutView = new WeakReference<AboutView>(aboutView).get();
        this.mContext = context.getApplicationContext();
    }

    @Override
    public void getAboutInfo() {

        NetworkApi.getAboutInfo(new DisposableObserver<AboutInfo>() {
            @Override
            public void onNext(AboutInfo value) {
                mAboutView.initAboutUI(value);
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("onError in getAboutInfo()error: " + e.getMessage(), e);
            }

            @Override
            public void onComplete() {

            }
        });
    }

    @Override
    public void getQrcodeInfo() {
        DisposableObserver<WechatPublic> disposableObserver = new DisposableObserver<WechatPublic>() {
            @Override
            public void onNext(WechatPublic response) {
                if (null != response) {
                    String data = response.getData();
                    String message = response.getMessage();
                    int status = response.getStatus();

                    if (status == 200 && null != data) {
                        if (!data.trim().equals("")) {
//                            mAboutView.addQrCodeUrl(data);
                        }
                    } else {
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onComplete() {

            }
        };
        NetworkApi.getWechatLogin(UserInfoHelper.getGid(), Constant.TYPE_CAPTCHA_BIND, disposableObserver);
    }

    @Override
    public void getUpdateInfo() {

        NetworkApi.getUpdateInfo(PRODUCT_ID, Util.getPartnerNo(mContext),
                App.getDebug() ? "0" : "1", Util.getVersionName(mContext),
                Util.getVersionCode(mContext),
                new DisposableObserver<UpdateInfo>() {
                    @Override
                    public void onNext(UpdateInfo value) {
                        mAboutView.addUpdateEvent(value);
                    }

                    @Override
                    public void onError(Throwable e) {
                        mAboutView.addUpdateEvent(null);
                        LibDeprecatedLogger.e("onError in getUpdateInfoFromServer, error = " + e.getMessage(), e);
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    @Override
    public void initUpdateTask() {
        mUpdateTask = new UpdateTask(mContext, mAboutView);
    }

    @Override
    public void execUpdateTask(UpdateInfo updateInfo) {
        if (mUpdateTask != null)
            mUpdateTask.execute(updateInfo);
    }

    @Override
    public void stopUpdateTask() {
        if (mUpdateTask != null && !mUpdateTask.isCancelled())
            mUpdateTask.cancel(true);
    }

}
