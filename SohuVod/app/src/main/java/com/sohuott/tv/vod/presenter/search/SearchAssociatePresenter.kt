package com.sohuott.tv.vod.presenter.search

import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.leanback.widget.Presenter
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.databinding.SearchAssociateLayoutItemBinding
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.model.SearchHot
import com.sohuott.tv.vod.search.SearchAssociate

class SearchAssociatePresenter: Presenter() {
    private var context: Context? = null
    interface OnSearchAssociateItemKeyListener{
        fun onSearchItemRightKey(vh: SearchAssociateViewHolder)
        fun onSearchItemLeftKey(vh: SearchAssociateViewHolder)
        fun onSearchItemBackKey()
    }
    private var mOnSearchAssociateItemKeyListener: OnSearchAssociateItemKeyListener? = null

    fun setOnSearchHotItemKeyListener(listener: OnSearchAssociateItemKeyListener){
        mOnSearchAssociateItemKeyListener = listener
    }
    override fun onCreateViewHolder(parent: ViewGroup?): ViewHolder {
        context = parent?.context
        val binding = SearchAssociateLayoutItemBinding.inflate(LayoutInflater.from(context), parent, false)
        return SearchAssociateViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder?, item: Any?) {
        val vh = viewHolder as SearchAssociateViewHolder
        vh.binding.root.setOnKeyListener { v, keyCode, event ->
            if (event.action == KeyEvent.ACTION_DOWN){
                if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    LibDeprecatedLogger.d("SearchHotPresenter onSearchItemRightKey")
                    vh.binding.searchAssociateItemTitle.setTextColor(Color.parseColor("#FF6247"))
                    vh.binding.searchAssociateItemTitle.setTag("selected")
                    mOnSearchAssociateItemKeyListener?.onSearchItemRightKey(viewHolder)
                    return@setOnKeyListener true
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                    LibDeprecatedLogger.d("SearchHotPresenter onSearchItemLeftKey")

                    vh.binding.searchAssociateItemSelected.visible()
                    vh.binding.searchAssociateItemTitle.setTextColor(Color.parseColor("#FF6247"))
                    vh.binding.searchAssociateItemTitle.setTag("selected")

                    mOnSearchAssociateItemKeyListener?.onSearchItemLeftKey(viewHolder)
                } else if (keyCode == KeyEvent.KEYCODE_BACK) {
                    LibDeprecatedLogger.d("SearchHotPresenter onSearchItemBackKey")
                    mOnSearchAssociateItemKeyListener?.onSearchItemBackKey()
                    vh.binding.searchAssociateItemTitle.setTextColor(Color.parseColor("#FF6247"))
                    vh.binding.searchAssociateItemTitle.setTag("selected")
                    return@setOnKeyListener true
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP || keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                    vh.binding.searchAssociateItemTitle.setTag("")
                }
            }
            false
        }

        var titleString: String = ""
        var keyString: String? = null
        vh.binding.searchAssociateItemTitle.setTag("")
        vh.binding.searchAssociateItemSelected.gone()
        vh.binding.root.isSelected = false
        when(item){
            is SearchAssociate.Data.Suggest -> {
                vh.binding.searchAssociateItemTitle.text = item.keyword
                titleString = item.keyword
                keyString = item.highlight
            }
            is SearchHot.Data -> {
                vh.binding.searchAssociateItemTitle.text = item.name
                vh.binding.searchAssociateItemTitle.setTextColor(Color.parseColor("#B3E8E8FF"))
                titleString = item.name
            }
        }


        context?.let {
            setHighlightedText(it, vh.binding.searchAssociateItemTitle, keyString, titleString)
        }

        vh.binding.root.setOnFocusChangeListener { view, b ->
            if (b) {
                vh.binding.searchAssociateItemSelected.gone()
            } else {
                vh.binding.root.isSelected = false
            }
            context?.let {
                setHighlightedText(it, vh.binding.searchAssociateItemTitle, keyString, titleString, b)
            }
        }
    }

    private fun setHighlightedText(context: Context, textView: TextView, keyString: String?, titleString: String, focused: Boolean = false) {
        if (!focused) {
            textView.tag?.let {
                if (it == "selected") {
                    return
                }
            }
        }

        if (keyString.isNullOrEmpty()) {
            val colorStateList = ContextCompat.getColorStateList(context, R.color.search_associate_item_text_selector)
            textView.setTextColor(colorStateList)
            textView.text = titleString
        } else {
            // 查找关键字在标题中的位置
            val startIndex = titleString.indexOf(keyString)
            val endIndex = startIndex + keyString.length
            // 如果关键字存在于标题中
            if (startIndex >= 0) {

                val spannableString = SpannableString(titleString)

                val colorStateList = ContextCompat.getColorStateList(context, R.color.search_associate_item_text_selector)
                textView.setTextColor(colorStateList)

                if (!focused) {
                    spannableString.setSpan(ForegroundColorSpan(Color.parseColor("#FF6247")), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
//            else {
//                spannableString.setSpan(ForegroundColorSpan(Color.parseColor("#e8e8e8")), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
//            }
                // 设置关键字部分的字体颜色为红色，其余部分为白色
//            spannableString.setSpan(ForegroundColorSpan(Color.parseColor("#B3E8E8FF")), 0, startIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
//            spannableString.setSpan(ForegroundColorSpan(Color.parseColor("#B3E8E8FF")), endIndex, titleString.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

                // 将带有颜色的字符串设置为 TextView 的文本
                textView.text = spannableString
            }
        }
    }


    override fun onUnbindViewHolder(viewHolder: ViewHolder?) {

    }

    class SearchAssociateViewHolder(val binding: SearchAssociateLayoutItemBinding): ViewHolder(binding.root)
}