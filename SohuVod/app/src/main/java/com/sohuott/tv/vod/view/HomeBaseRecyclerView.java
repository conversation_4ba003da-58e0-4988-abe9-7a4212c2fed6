package com.sohuott.tv.vod.view;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.util.Log;
import android.view.KeyEvent;

/**
 * Created by yizhang210244 on 2018/1/4.
 */

public class HomeBaseRecyclerView extends RecyclerView{
    private static final String TAG = HomeBaseRecyclerView.class.getSimpleName();
    public HomeBaseRecyclerView(Context context) {
        super(context);
        init(context);
    }

    public HomeBaseRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public HomeBaseRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

    private void init(Context context) {
        setChildDrawingOrderCallback(new ChildDrawingOrderCallback() {
            @Override
            public int onGetChildDrawingOrder(int childCount, int i) {
                int pos = -1;
                for(int index = 0; index < childCount; index++) {
                    if(getChildAt(index).hasFocus()) {
                        pos = index;
                        break;
                    }
                }
                if(pos < 0 || pos >= childCount) {
                    return i;
                }
                if(i == childCount - 1) {
                    return pos;
                }else if(i < pos) {
                    return i;
                }else {
                    return i + 1;
                }
            }
        });
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        Log.d(TAG, "dispatchKeyEvent: " + event.toString());
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            switch (event.getKeyCode()) {
                case KeyEvent.KEYCODE_BACK:
                    if (mCallBackListener != null) {
                        mCallBackListener.onBackKeyDown();
                        return true;
                    }
                default:
                    break;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    public interface CallBackListener {
        void onBackKeyDown();
    }
    private CallBackListener mCallBackListener;
    public void setCallBackListener(CallBackListener callBackListener) {
        mCallBackListener = callBackListener;
    }
}
