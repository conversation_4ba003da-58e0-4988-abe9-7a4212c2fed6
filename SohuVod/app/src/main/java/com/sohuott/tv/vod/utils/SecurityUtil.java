package com.sohuott.tv.vod.utils;

import com.sohu.lib_utils.XXTeaUtil;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> @Sohu.com Inc on 2017/9/5.
 */
public class SecurityUtil {

    /**
     * Security encrypt/decrypt data
     */

    private static final String defaultKey = "%&#@*^!ott.SOHU(com)WG@";

    public static String encrypt(String value){
        try {
            return XXTeaUtil.encryptStr(value,defaultKey);
        }catch (Exception e){}
        return value;
    }

    public static String encrypt(String value, String key){
        try {
            return XXTeaUtil.encryptStr(value,key);
        }catch (Exception e){}
        return value;
    }

    public static String decrypt(String value){
        try {
            return XXTeaUtil.decryptStr(value, defaultKey);
        }catch (Exception e){}
        return value;
    }

    public static String decrypt(String value, String key){
        try {
            return XXTeaUtil.decryptStr(value,key);
        }catch (Exception e){}
        return value;
    }

    public static void main(String[] args){
        String tsValue = String.valueOf((new Date()).getTime());
        String paramValue = "taskId=9&passport=<EMAIL>";
        System.out.println("tsValue : " + tsValue);
        System.out.println("paramValue : " + paramValue);

        //encrypt ts
        String tsEncrypt = SecurityUtil.encrypt(tsValue);
        System.out.println("encrypt tsValue : " + tsEncrypt);
        //encrypt param
        String paramEncrypt = SecurityUtil.encrypt(paramValue,tsValue);
        System.out.println("encrypt paramValue : " + paramEncrypt);

        //decrypt ts
        tsValue = SecurityUtil.decrypt(tsEncrypt);
        System.out.println("decrypt tsValue : " + tsValue);
        //decrypt param
        String paramDecrypt = SecurityUtil.decrypt(paramEncrypt,tsValue);
        System.out.println("decrypt paramValue : " + paramDecrypt);

    }

}
