package com.sohuott.tv.vod.utils;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.view.View;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.Md5Utils;
import com.sohu.lib_utils.PrefUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.app.AppConstants;
import com.sohuott.tv.vod.app.SohuAppUtil;
import com.sohuott.tv.vod.lib.model.UpdateInfo;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.UpdatePresenter;
import com.sohuott.tv.vod.view.UpdateDialogNew;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;

/**
 * Created by wenjingbian on 2016/4/7.
 */
public class UpdateHelper {

    private static final String TAG = UpdateHelper.class.getSimpleName();

    private static final String APK_FILE_NAME = "SohuVod.apk";

    public static final int TAG_UPGRADE_USUAL = 1;

    public static final int TAG_UPGRADE_FORCE = 2;

    private UpdateDialogNew mUpdateDialog;

    private Context mContext;

    private UpdatePresenter mUpdatePresenter;

    private UpdateInfo mUpdateInfo;

    private boolean mDialogIsShow = false;
    private HashMap<String, String> mPathInfo;

    public UpdateHelper(Context context, UpdatePresenter updatePresenter) {
        this.mContext = context;
        this.mUpdatePresenter = updatePresenter;
    }

    public boolean isDialogShowing() {
        return mDialogIsShow;
    }

    public UpdateDialogNew getUpdateDialog() {
        return mUpdateDialog;
    }

    public void showUpdateDialog(final Activity context, final UpdateInfo updateInfo, boolean showToast) {
        if (updateInfo == null) {
            if (mExistListener != null) {
                mExistListener.onExist();
            }
            return;
        }

        this.mUpdateInfo = updateInfo;
        mDialogIsShow = true;

        if (updateInfo.data == null || updateInfo.data.status == 0) {
            if (showToast) {
                ToastUtils.showToast2(mContext, mContext.getString(R.string.toast_update_ctn));
            }
            AppLogger.d(TAG, mContext.getString(R.string.toast_update_ctn));
            mDialogIsShow = false;
            if (mExistListener != null) {
                mExistListener.onExist();
            }
        } else {
            mUpdateDialog = new UpdateDialogNew.Builder(context)
                    .setTitle(context.getString(R.string.dialog_update_ctn_pre)
                            + updateInfo.data.targetVersion)
                    .setMsg(updateInfo.data.newDesc)
                    .setCancelListener(new DialogInterface.OnCancelListener() {
                        @Override
                        public void onCancel(DialogInterface dialog) {
                            mUpdatePresenter.stopUpdateTask();

                            if (updateInfo.data.status == TAG_UPGRADE_USUAL) {
                                mUpdateDialog.dismiss();
                                if (mExistListener != null) {
                                    mExistListener.onExist();
                                }
                            } else if (updateInfo.data.status == TAG_UPGRADE_FORCE) {
                                SohuAppUtil.exitApp(mContext);
                            }
                            mDialogIsShow = false;

                        }
                    })
                    .setPositiveButton(R.string.dialog_update_btn_ok_new,
                            new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    mUpdatePresenter.initUpdateTask();
                                    mUpdatePresenter.execUpdateTask(mUpdateInfo);
                                    mUpdateDialog.setDownloadingView(mUpdatePresenter, updateInfo.data.status);
                                    RequestManager.getInstance().onAllEvent(new EventInfo(10210, "clk"), mPathInfo, null,
                                            null);
                                    PrefUtil.putBoolean( AppConstants.KEY_HAS_CLICK_UPDATE_BUTTON, true);

                                }
                            })
                    .setNegativeButton(R.string.dialog_update_btn_cancel_new,
                            new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    RequestManager.getInstance().onAllEvent(new EventInfo(10211, "clk"), mPathInfo, null,
                                            null);
                                    mDialogIsShow = false;
                                    mUpdatePresenter.stopUpdateTask();

                                    if (updateInfo.data.status == TAG_UPGRADE_USUAL) {
                                        mUpdateDialog.dismiss();
                                        if (mExistListener != null) {
                                            mExistListener.onExist();
                                        }
                                    } else if (updateInfo.data.status == TAG_UPGRADE_FORCE) {
                                        SohuAppUtil.exitApp(mContext);
                                    }
                                }
                            }).show();
            mUpdateDialog.setExistListener(mExistListener);
            mPathInfo = new HashMap<>();
            mPathInfo.put("pageId", "1020");
            RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), mPathInfo, null,
                    null);

        }
    }

    private void download(Activity context, boolean result, final int updateStatus, int networkResponseCode) {
        if (result == true) {
            AppLogger.d(TAG, "Download successfully, prepare to install APK.");
            try {
                if (Md5Utils.compareMd5(mUpdateInfo.data.md5,
                        new File((Util.getApkDownloadPath(mContext))))) {
                    installApk();
                    if (updateStatus == TAG_UPGRADE_FORCE) {
//                        SohuAppUtil.exitApp(mContext);
                    } else {
                        if (mExistListener != null) {
                            mExistListener.onExist();
                        }
                    }
                } else {
                    AppLogger.d(TAG, "Error in check MD5");
                    if (mExistListener != null) {
                        mExistListener.onExist();
                    }
                }
            } catch (Exception e) {
                AppLogger.e(TAG, "Exception: " + e);
                if (mExistListener != null) {
                    mExistListener.onExist();
                }
            }
        } else {
            mUpdateDialog = new UpdateDialogNew.Builder(context)
                    .setTitle(mContext.getString(R.string.dialog_update_retry))
                    .setMsg(getUpdateMsg(mUpdateInfo.data.newDesc))
                    .setPositiveButton(R.string.dialog_update_btn_retry,
                            new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    mUpdateDialog.setDownloadingView(mUpdatePresenter, mUpdateInfo.data.status);
                                    mUpdatePresenter.initUpdateTask();
                                    mUpdatePresenter.execUpdateTask(mUpdateInfo);
                                }
                            })
                    .setCancelListener(new DialogInterface.OnCancelListener() {
                        @Override
                        public void onCancel(DialogInterface dialog) {
                            if (mUpdateInfo != null && mUpdateInfo.data != null) {
                                if (mUpdateInfo.data.status == TAG_UPGRADE_USUAL) {
                                    mUpdateDialog.dismiss();
                                    if (mExistListener != null) {
                                        mExistListener.onExist();
                                    }
                                } else if (mUpdateInfo.data.status == TAG_UPGRADE_FORCE) {
                                    SohuAppUtil.exitApp(mContext);
                                }
                            }
                        }
                    })
                    .setNegativeButton(R.string.dialog_update_btn_cancel,
                            new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    mDialogIsShow = false;
                                    mUpdatePresenter.stopUpdateTask();

                                    if (updateStatus == TAG_UPGRADE_USUAL) {
                                        mUpdateDialog.dismiss();
                                        if (mExistListener != null) {
                                            mExistListener.onExist();
                                        }
                                    } else if (updateStatus == TAG_UPGRADE_FORCE) {
                                        SohuAppUtil.exitApp(mContext);
                                    }
                                }
                            }).show();
            AppLogger.d(TAG, "Download APK failed, network response code is " + networkResponseCode);
        }
    }

    public void dealDownloadResult(Activity context, boolean result, final int updateStatus, int networkResponseCode) {
        if (mUpdateDialog != null && mUpdateDialog.isShowing()) {
            mUpdateDialog.dismiss();
        }
        download(context, result, updateStatus, networkResponseCode);
    }

    public void updateProgress(int value) {
        mUpdateDialog.updateDownloadProgress(value);
    }

    private void installApk() {
        String apkPath = Util.getApkDownloadPath(mContext);
        try {
            Runtime.getRuntime().exec("chmod 777 " + apkPath);
        } catch (IOException e) {
            AppLogger.e(TAG, "Exception during installApk(): " + e);
        }
        ActivityLauncher.startInstallActivity(mContext, apkPath);
    }

    private String getUpdateMsg(String msg) {
        if (msg == null) {
            return null;
        } else {
            String[] msgArray = msg.split("<br>");
            StringBuilder sb = new StringBuilder();
            for (String tmp : msgArray) {
                sb.append(tmp + "\n");
            }
            return sb.toString();
        }
    }

    public interface ExistListener {
        void onExist();
    }

    private ExistListener mExistListener;

    public void setExistListener(ExistListener existListener) {
        mExistListener = existListener;
    }

    private class DismissListener implements View.OnClickListener {

        private int status;

        public DismissListener(int status) {
            this.status = status;
            mDialogIsShow = false;
        }

        @Override
        public void onClick(View v) {
            mUpdatePresenter.stopUpdateTask();

            if (status == TAG_UPGRADE_USUAL) {
                mUpdateDialog.dismiss();
                if (mExistListener != null) {
                    mExistListener.onExist();
                }
            } else if (status == TAG_UPGRADE_FORCE) {
                SohuAppUtil.exitApp(mContext);
            }
        }
    }
}
