package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.model.Header;

public class TypeHeaderPresenter extends Presenter {
    private Context mContext;
    private Header mHeader;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        LibDeprecatedLogger.d("onCreateViewHolder: ");
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_header_layout, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, final Object item) {
        LibDeprecatedLogger.d("onBindViewHolder: ");
        ViewHolder vh = (ViewHolder) viewHolder;

        if (item instanceof Header) {
            mHeader = (Header) item;
            if (mHeader.getChannelType() == Constant.TYPE_VIP) {
                vh.mHeaderTv.setTextColor(Color.parseColor("#E7C5A3"));
            }
            vh.mHeaderTv.setText(((Header) item).getTitle());
        }
    }

    @Override
    public void onViewAttachedToWindow(Presenter.ViewHolder holder) {
        LibDeprecatedLogger.d("onViewAttachedToWindow: ");
        super.onViewAttachedToWindow(holder);
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {
        LibDeprecatedLogger.d("onUnbindViewHolder: ");
    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private TextView mHeaderTv;

        public ViewHolder(View view) {
            super(view);
            mHeaderTv = (TextView) view.findViewById(R.id.item_type_header_title);
        }
    }
}
