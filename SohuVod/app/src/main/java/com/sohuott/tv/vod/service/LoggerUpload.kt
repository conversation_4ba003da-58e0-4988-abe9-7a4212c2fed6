package com.sohuott.tv.vod.service

import XCrashApp
import android.content.Context
import com.drake.net.Get
import com.drake.net.Post
import com.drake.net.utils.scopeNet
import com.sh.ott.logger.ILoggerFileCallBack
import com.sh.ott.logger.Logger
import com.sohu.lib_utils.SafeCompressionUtils
import com.sohu.lib_utils.SafeCompressionUtils.CompressionCallback
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohu.ott.base.lib_user.UserConfigHelper
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.lib.utils.UrlWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Headers.Companion.toHeaders
import java.io.File

class LoggerUpload : ILoggerFileCallBack, CompressionCallback {

    private var mLoggerUploadCallBack: LoggerUploadCallBack? = null
    private var pushSource = FEEDBACK_SOURCE_USER
    private var pushType = FEEDBACK_UPLOAD_USER
    private var reportTime: Long = 0
    private var content: String? = null

    private var context: Context? = null
    private var mScope: CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    fun init(context: Context) {
        this.context = context.applicationContext
    }

    fun push(reportTime: Long, callBack: LoggerUploadCallBack) {
        push(
            reportTime = reportTime,
            uploadType = FEEDBACK_UPLOAD_USER,
            source = FEEDBACK_SOURCE_USER,
            content = "",
            callBack = callBack
        )
    }

    /**
     * @param reportTime 日志时间戳
     * @param uploadType 上传日志类型
     * @param source 上传日志来源
     * @param callBack  上传结果回调
     * @param content 播放器检测内容
     */
    fun push(
        reportTime: Long,
        uploadType: Int,
        source: Int,
        content: String?,
        callBack: LoggerUploadCallBack
    ) {
        this.content = content
        this.reportTime = reportTime
        pushType = uploadType
        pushSource = source
        mLoggerUploadCallBack = callBack
        try {
            scopeNet {
                val body = Get<String>("https://data.vod.ystyt.aisee.tv/ipinfo") {
                }.await()
                AppLogger.d(TAG.plus("IP-INFO"), body)
                getLoggerFile()
            }
        } catch (e: Throwable) {
            AppLogger.e(TAG, e.localizedMessage)
            getLoggerFile()
        }
    }

    private fun getLoggerFile() {
        Logger.getLoggerFile(this@LoggerUpload)
    }

    override fun afterDeleteFile(): Boolean {
        return false
    }

    override fun fileName(): String? {
        return super.fileName()
    }

    private val fileList =  mutableListOf<File>()

    var logFile:File?=null
    override fun onLogFile(file: File?) {
        logFile=file
        fileList.clear()
        XCrashApp.getFileList()?.let {
            fileList.addAll(it.toMutableList())
        }
        logFile?.let {
            fileList.add(it)
        }
        val zipFile = File(
            context?.applicationContext?.filesDir?.absolutePath,
            "backup_${System.currentTimeMillis()}.zip"
        )
        SafeCompressionUtils.compress(
            context = context!!,
            sources = fileList!!,
            destZipFile = zipFile!!,
            scope = mScope,
            callback = this
        )
    }

    override fun onProgress(percent: Int) {
        AppLogger.d("合并日志文件进度:$percent")
    }

    override fun onSuccess(zipFile: File) {
        pushToService(zipFile, zipFile.name)
        AppLogger.d("合并日志文件成功")
        fileList.clear()
    }

    override fun onError(error: Throwable?) {
        val fileName = System.currentTimeMillis()
        pushToService(logFile, fileName.toString())
        AppLogger.d("合并日志文件失败")
        fileList.clear()
    }


    private fun pushToService(file: File?, fileName: String) {
        try {
            scopeNet {
                val body = Post<String>(UrlWrapper.BASE_URL + "/feedback/feedback.json") {
                    setHeaders(HeaderHelper.getHeaders().toHeaders())
                    val gid = UserInfoHelper.getGid()
                    addQuery("uploadType", 1)
                    addQuery("feedbackSource", pushSource)
                    addQuery("problemId", gid + reportTime)
                    addQuery("gid", gid)
                    addQuery("passport", "")
                    addQuery("content", "")
                    addQuery("contact", "")
                    addQuery("playerType", UserConfigHelper.getUserPlayParams() + 1)
                    param("file", fileName.toString() ?: " 文件名为空", file)
                }.await()
                if (body.contains("50015")) {
                    mLoggerUploadCallBack?.onError(FEEDBACK_UPLOAD_ERROR_NETWORK)
                } else {
                    if (pushSource == FEEDBACK_SOURCE_PLAY) {
                        pushToSms()
                    } else {
                        mLoggerUploadCallBack?.onSuccess()
                    }
                }
            }
        } catch (e: Throwable) {
            mLoggerUploadCallBack?.onError(FEEDBACK_UPLOAD_ERROR)
        }
    }

    private fun pushToSms() {
        scopeNet {
            val body = Post<String>(UrlWrapper.BASE_URL + "/feedback/feedback.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                val gid = UserInfoHelper.getGid()
                param("uploadType", "2");
                param("feedbackType", "1");
                param("content", content);
                param("problemId", gid + reportTime);
            }.await()
            mLoggerUploadCallBack?.onSuccess()
        }
    }

    fun release() {
        mLoggerUploadCallBack = null
    }


    companion object {
        //上传日志来源
        const val FEEDBACK_SOURCE_USER = 1
        const val FEEDBACK_SOURCE_PLAY = 2

        /**
         * 上传日志类型
         */
        const val FEEDBACK_UPLOAD_USER = 1
        const val FEEDBACK_UPLOAD_PLAY = 2


        private const val TAG = "LoggerUpload"

        //上传日志结果
        //成功
        const val FEEDBACK_UPLOAD_SUCCESS = 0

        //错误 设备频繁
        const val FEEDBACK_UPLOAD_ERROR_NETWORK = -1
        const val FEEDBACK_UPLOAD_ERROR = -2

        private var mLoggerUpload: LoggerUpload? = null

        @JvmStatic
        fun getInstants(): LoggerUpload {
            mLoggerUpload ?: synchronized(this) {
                mLoggerUpload ?: LoggerUpload().also {
                    mLoggerUpload = it
                }
            }
            return mLoggerUpload!!
        }
    }

    interface LoggerUploadCallBack {
        fun onError(errorType: Int) {
//            日志上传失败
//            设备访问频繁
        }

        fun onSuccess() {
//            日志已上传，正在提交反馈
        }
    }
}

