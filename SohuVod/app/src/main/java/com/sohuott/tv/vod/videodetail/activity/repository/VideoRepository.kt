package com.sohuott.tv.vod.videodetail.activity.repository

import GsonConverter
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.lib_utils.DeviceUtils
import com.sohu.lib_utils.MetaDataHelper
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohu.ott.base.lib_user.UserConfigHelper
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohu.ott.base.lib_user.UserLoginHelper.Companion.getInstants
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger
import com.sohuott.tv.vod.app.App
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.model.AlbumInfo
import com.sohuott.tv.vod.lib.model.PermissionCheck
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo
import com.sohuott.tv.vod.lib.model.PgcEpisodeVideos
import com.sohuott.tv.vod.lib.model.PgcPlayList
import com.sohuott.tv.vod.lib.model.VideoInfo
import com.sohuott.tv.vod.lib.model.VrsEpisodeVideos
import com.sohuott.tv.vod.lib.utils.UrlWrapper
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.coroutineScope
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class VideoRepository {


    /**
     * @param aid AID
     */
    suspend fun requestPgcAlbum(aid: Int?): Deferred<PgcAlbumInfo> {
        return coroutineScope {
            Get<PgcAlbumInfo>("${RetrofitApi.get().retrofitHost.baseHost}video/pgcVideoInfo.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader(HeaderHelper.getCurlOpenKey(),"true")

                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
                addQuery("videoId", aid)
            }
        }
    }


    suspend fun requestVrsAlbum(
        aid: Int?,
        passport: String?,
        gid: String?
    ): Deferred<AlbumInfo> {
        return coroutineScope {
            Get<AlbumInfo>("${RetrofitApi.get().retrofitHost.baseHost}album/info.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader(HeaderHelper.getCurlOpenKey(),"true")

                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
                addQuery("albumId", aid)
                addQuery("partner", MetaDataHelper.getPartnerNo())
                addQuery("passport", passport)
                addQuery("deviceid", gid)
                addQuery("getZYLastVideo", 1)
            }
        }
    }


    suspend fun requestPgcVideo(
        vid: Int?,
        passport: String?
    ): Deferred<PgcPlayList> {
        return coroutineScope {
            Get<PgcPlayList>("${RetrofitApi.get().retrofitHost.playerHost}v2/video/play.do") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader("auth_token", "auth_token")
                addHeader(HeaderHelper.getCurlOpenKey(),"true")
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()


                if (App.debug) {
                    param("id", vid?.toString() ?: "")
                    param("customizeSizes", "120*90,160*90,320*180,240*330,640*360");
                    param("src", "sohuAppSyncData");
                    param("_src", "partner");
                    param("_cn", "mweb");
                    param("_sig", "fdc4be1d654bd73e1ebf03b8f91fdca2");
                    param("_ts", "1553909448076");
                    param("_gid", UserInfoHelper.getGid());
                } else {
                    param("_src", "app");
                    param("_cn", "ott");
                    param("_cv", DeviceUtils.getVersionName());
                    param("_partner", MetaDataHelper.getPartnerNo());
                    param("_mfov", DeviceUtils.getDeviceModel());
                    param("_cp", "AndroidTv");
                    param("_cpv", "1.0");
                    param("_gid", UserInfoHelper.getGid());
                    param("id", "${vid ?: ""}");
                    param("isenc", "0");
                    param("plat", "15");
                }
//
//
//
//                addQuery("aid", aid)
//                addQuery("tvVerId", vid)
//                addQuery("partner", MetaDataHelper.getPartnerNo())
//                if (UserConfigHelper.enableSystemPlayer()) {
//                    addQuery("ptype", 1)
//                    addQuery("ency", 0)
//                } else {
//                    addQuery("ptype", 2)
//                    addQuery("player", "2.0")
//                }
//                addQuery("h265", UserConfigHelper.getH265())
//                addQuery(
//                    "h265Vers",
//                    if (UserConfigHelper.getH265HasChange()) "" else UserConfigHelper.getH265hVers()
//                )
//                addQuery("passport", passport)
//                addQuery("dts", if (isDts == true) "1" else "0")
            }
        }
    }


    suspend fun requestVrsVideo(
        aid: Int?,
        vid: Int?,
        passport: String?,
        isDts: Boolean?
    ): Deferred<VideoInfo> {
        return coroutineScope {
            Get<VideoInfo>("${RetrofitApi.get().retrofitHost.baseHost}video/videoInfo.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader("auth_token", "auth_token")
                addHeader(HeaderHelper.getCurlOpenKey(),"true")

                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
                addQuery("aid", aid)
                addQuery("tvVerId", vid)
                addQuery("partner", MetaDataHelper.getPartnerNo())
                if (UserConfigHelper.enableSystemPlayer()) {
                    addQuery("ptype", 1)
                    addQuery("ency", 0)
                } else {
                    addQuery("ptype", 2)
                    addQuery("player", "2.0")
                }
                addQuery("h265", UserConfigHelper.getH265())
                addQuery(
                    "h265Vers",
                    if (UserConfigHelper.getH265HasChange()) "" else UserConfigHelper.getH265hVers()
                )
                addQuery("passport", passport)
                addQuery("dts", if (isDts == true) "1" else "0")
            }
        }
    }


    suspend fun requestVrsDetailRecommendAll(aid: Int?): Deferred<VideoDetailRecommendModel> {
        return coroutineScope {
            Get<VideoDetailRecommendModel>("${RetrofitApi.get().retrofitHost.baseHost}album/albumRelative.json?") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
                addQuery("albumId", aid)
                addQuery("isTeen", if (TeenagersManger.isTeenager()) 1 else 0)
                addQuery("passport", UserLoginHelper.getInstants().getLoginPassport())
            }
        }
    }

    suspend fun requestPgcDetailRecommendAll(vid: Int?): Deferred<VideoDetailRecommendModel> {
        return coroutineScope {
            Get<VideoDetailRecommendModel>("${RetrofitApi.get().retrofitHost.baseHost}album/pgcVideoRelative.json?") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
                addQuery("videoId", vid)
            }
        }
    }

    suspend fun getVrsEpisodeVideos(
        aid: Int?,
        sortOrder: Int,
        page: Int,
        pageSize: Int
    ): Deferred<VrsEpisodeVideos> {
        return coroutineScope {
            Get<VrsEpisodeVideos>("${RetrofitApi.get().retrofitHost.baseHost}album/videos.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
                addQuery("albumId", aid)
                addQuery("type", 0)
                addQuery("sortOrder", sortOrder)
                addQuery("partner", MetaDataHelper.getPartnerNo())
                addQuery("page", page)
                addQuery("pageSize", pageSize)
            }
        }
    }


    suspend fun getPgcEpisodeVideos(
        playListId: Int,
        sortOrder: Int,
        page: Int,
        pageSize: Int
    ): Deferred<PgcEpisodeVideos> {
        return coroutineScope {
            Get<PgcEpisodeVideos>("${RetrofitApi.get().retrofitHost.baseHost}album/pgcVideoLists.json?videoId=&") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
                addQuery("playListId", playListId)
                addQuery("sortOrder", sortOrder)
                addQuery("page", page)
                addQuery("pageSize", pageSize)
            }
        }
    }

    /**
     * dlna 鉴权手机端使用
     */
    suspend fun loadIphoneMKey(
        passport: String?,
        authToken: String?,
        aid: Int,
        vid: Int,
        apikey: String?,
        uid: String?,
        appVersion: String?,
        gid: String?,
        app_id: String?,
        plat: String?,
        appId: String?,
        ua: String?
    ) : Deferred<PermissionCheck>{
        return coroutineScope {
            Get<PermissionCheck>("${RetrofitApi.get().retrofitHost.baseHost}vip/film/checkpermission.json") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader(HeaderHelper.getCurlOpenKey(),"true")

                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()

                addQuery("passport", passport);
                addQuery("auth_token", authToken);
                addQuery("aid", aid);
                addQuery("vid", vid);
                addQuery("gid", gid);
                addQuery("sver", appVersion);
                addQuery("ua", ua);
                addQuery("uid", uid);
                addQuery("api_key", apikey);
                addQuery("appvs", appVersion);
                addQuery("appid", appId);
                addQuery("plat", plat);
                addQuery("app_id", app_id);
            }
        }
    }


}