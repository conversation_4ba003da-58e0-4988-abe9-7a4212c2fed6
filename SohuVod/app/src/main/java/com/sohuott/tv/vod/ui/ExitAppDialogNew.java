package com.sohuott.tv.vod.ui;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.HorizontalGridView;
import androidx.leanback.widget.ItemBridgeAdapter;

import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sh.ott.video.ad.AdBannerCallBack;
import com.sh.ott.video.ad.AdRequestFactory;
import com.sohu.lib_utils.PrefUtil;
import com.sohu.lib_utils.StringUtil;
import com.sohu.ott.ads.sdk.model.RequestComponent;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.GlideApp;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.utils.PrivacySettingHelper;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.presenter.launcher.TypeRecommendContentPresenter;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by fenglei on 16-3-30.
 */
public class ExitAppDialogNew extends Dialog implements View.OnClickListener, DialogInterface.OnDismissListener, DialogInterface.OnShowListener, AdBannerCallBack {
    private static final String TAG = "ExitAppDialog";

    @Override
    public void onAdBannerError(@Nullable String s) {
        AppLogger.d(TAG, "onAdsLoadedError :"+s);
        if (!isDismissed) {
            exitRecommendLayout.setVisibility(View.VISIBLE);
            mSimpleDraweeView.setVisibility(View.GONE);
        }
    }

    @Override
    public void onAdBannerImage(@Nullable String uri, @Nullable Map<String, Object> map) {
        if (uri != null) {
            LibDeprecatedLogger.d(uri);
        }
        GlideApp.with(getContext()).load(uri).skipMemoryCache(true).listener(new RequestListener<Drawable>() {
            @Override
            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                if (!isDismissed) {
                    exitRecommendLayout.setVisibility(View.VISIBLE);
                }
                return false;
            }

            @Override
            public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                AdRequestFactory.getInstants().reportBannerAd();
//                    Advert.getInstance().reportBannerAd(mAdCommon);
                findViewById(R.id.ad_flag).setVisibility(View.VISIBLE);
                if (countDownHandler != null) {
                    countDownHandler.removeCallbacksAndMessages(null);
                }
                return false;
            }
        }).into(mSimpleDraweeView);
        mSimpleDraweeView.setVisibility(View.VISIBLE);

    }

    public interface ExitAppListener {
        void exitApp();

        void onDismiss();
    }

    private ExitPlayerRelativeLayout rootView;
    private RelativeLayout exitBtn;
    private RelativeLayout continueBtn;
    private RelativeLayout exitRecommendLayout;
    private ExitAppListener mExitAppListener;
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();
    private LoginUserInformationHelper mLoginHelp;
    private ArrayObjectAdapter mArrayObjectAdapter;
    private HorizontalGridView mHorizontalGridView;
    private long pageId;


    public ExitAppDialogNew(Context context) {
        super(context, R.style.ExitAppDialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_player_exit);
        getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

        mLoginHelp = LoginUserInformationHelper.getHelper(getContext());

        rootView = (ExitPlayerRelativeLayout) findViewById(R.id.root);
        exitBtn = (RelativeLayout) rootView.findViewById(R.id.exit_play);
        continueBtn = (RelativeLayout) rootView.findViewById(R.id.continue_play);


        mHorizontalGridView = (HorizontalGridView) findViewById(R.id.exit_recommend_list);
        mHorizontalGridView.setHorizontalSpacing(48);

        mArrayObjectAdapter = new ArrayObjectAdapter(new TypeRecommendContentPresenter());
        ItemBridgeAdapter itemBridgeAdapter = new ItemBridgeAdapter(mArrayObjectAdapter);
        mHorizontalGridView.setAdapter(itemBridgeAdapter);


        exitRecommendLayout = (RelativeLayout) findViewById(R.id.exit_recommend_layout);
        setOnDismissListener(this);
        setOnShowListener(this);
        getPersonalRecommendData();
        exitBtn.setOnClickListener(this);
        continueBtn.setOnClickListener(this);
        mSimpleDraweeView = (ImageView) findViewById(R.id.exit_advert_simpleDraweeView);

        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", StringUtil.toString(pageId));

        RequestManager.getInstance().onAllEvent(new EventInfo(10190, "imp"),
                pathInfo, null, null);
    }

    public void setPageId(long pageId) {
        this.pageId = pageId;
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        exitBtn.requestFocus();
    }

    private void getPersonalRecommendData() {
        DisposableObserver disposableObserver = new DisposableObserver<VideoDetailRecommend>() {
            @Override
            public void onNext(VideoDetailRecommend value) {
                LibDeprecatedLogger.d("value = " + value);
                setPersonalRecommendUI(value);
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("onError = " + e.getMessage(), e);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("onComplete");
            }
        };
        NetworkApi.getPersonalRecommendNew(UserInfoHelper.getGid(), 3,
                mLoginHelp.getLoginPassport(),
                PrivacySettingHelper.INSTANCE.getRecommend(getContext()), disposableObserver);
        mCompositeDisposable.add(disposableObserver);
    }

    private void setPersonalRecommendUI(VideoDetailRecommend personalRecommend) {
        if (personalRecommend != null && personalRecommend.getStatus() == 0
                && personalRecommend.getData() != null && personalRecommend.getData().size() > 0) {
            int size = personalRecommend.getData().size();
            Log.d(TAG, "setPersonalRecommendUI: size" + size);
            mArrayObjectAdapter.addAll(0, personalRecommend.data);
        } else {
            ToastUtils.showToast(getContext(), "错误码 : " + personalRecommend.getStatus() + " , " + "错误信息 ： " + personalRecommend.getMessage());
        }
    }

    @Override
    public void show() {
        super.show();
        if (exitBtn != null) {
            exitBtn.requestFocus();
        }
        RequestManager.getInstance().onExitAppDialogEvent();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        Log.d(TAG, "dispatchKeyEvent: " + event.toString());
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                View focusedView = getCurrentFocus();
                if (focusedView.getId() == R.id.btn_cancel) {
                    return true;
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public void onClick(View v) {
        if (v.equals(exitBtn)) {
            PrefUtil.putInt("DEFAULT_VERSION", 0);
            RequestManager.getInstance().onClickExitDialogExit();
            if (mExitAppListener != null) {
                RequestManager.onMccEndEvent();
                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(pageId));
                RequestManager.getInstance().onAllEvent(new EventInfo(10191, "clk"),
                        pathInfo, null, null);
                mExitAppListener.exitApp();
            }
            dismiss();
        } else if (v.equals(continueBtn)) {
            dismiss();
            HashMap<String, String> pathInfo = new HashMap<>();
            pathInfo.put("pageId", StringUtil.toString(pageId));

            RequestManager.getInstance().onAllEvent(new EventInfo(10192, "clk"),
                    pathInfo, null, null);
            RequestManager.getInstance().onClickExitDialogContinue();
        }
    }

    public void setExitAppListener(ExitAppListener listener) {
        mExitAppListener = listener;
    }

    @Override
    public void setOnDismissListener(OnDismissListener listener) {
        super.setOnDismissListener(listener);
    }





    private ImageView mSimpleDraweeView;
    boolean isDismissed = false;
    boolean isTimeout = false;

    @Override
    public void onDismiss(DialogInterface dialog) {
        isDismissed = true;
        AdRequestFactory.getInstants().destroyLoader();
//        Advert.getInstance().destory();
//        Advert.getInstance().release();
        mSimpleDraweeView.setVisibility(View.GONE);
        if (countDownHandler != null) {
            countDownHandler.removeCallbacksAndMessages(null);
            countDownHandler = null;
        }
        mCompositeDisposable.clear();
        if (mExitAppListener != null) {
            mExitAppListener.onDismiss();
        }
    }

    @Override
    public void onShow(DialogInterface dialog) {
        isTimeout = false;
        isDismissed = false;
        exitRecommendLayout.setVisibility(View.GONE);
        RequestComponent requestComponent = new RequestComponent();
        String gid = UserInfoHelper.getGid();
        requestComponent.setGuid(gid);
        requestComponent.setTuv(gid);
        requestComponent.setSite("1");
        requestComponent.setPosCode("15576");
        AdRequestFactory.getInstants().requestBannerAd(getContext(),requestComponent,this);
//        Advert.getInstance().requestBannerAd(getContext(), this);
        countDownHandler = new CountDownHandler(this);
        countDownHandler.sendEmptyMessageDelayed(MESSAGE_TIME_OUT, TIMEOUT);

    }

    private static final int TIMEOUT = 2000;
    private static final int MESSAGE_TIME_OUT = 1024;
    private CountDownHandler countDownHandler = null;

    public static class CountDownHandler extends Handler {
        private WeakReference<ExitAppDialogNew> mExitAppDialogNewWeakReference;

        public CountDownHandler(ExitAppDialogNew exitAppDialogNew) {
            this.mExitAppDialogNewWeakReference = new WeakReference<ExitAppDialogNew>(exitAppDialogNew);
            ;
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == MESSAGE_TIME_OUT) {
                LibDeprecatedLogger.d("MESSAGE_TIME_OUT");
                if (mExitAppDialogNewWeakReference == null) {
                    return;
                }
                ExitAppDialogNew exitAppDialogNew = mExitAppDialogNewWeakReference.get();
                if (exitAppDialogNew == null) {
                    return;
                }
                exitAppDialogNew.isTimeout = true;
                exitAppDialogNew.exitRecommendLayout.setVisibility(View.VISIBLE);
                exitAppDialogNew.mSimpleDraweeView.setVisibility(View.GONE);
            }
        }
    }


}
