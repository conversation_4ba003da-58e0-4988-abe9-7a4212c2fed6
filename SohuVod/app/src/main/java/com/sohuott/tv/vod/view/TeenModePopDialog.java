package com.sohuott.tv.vod.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;

/**
 * Created by yizhang210244 on 2017/5/12.
 */

public class TeenModePopDialog extends Dialog implements View.OnFocusChangeListener {

    private static final String TAG = TeenModePopDialog.class.getSimpleName();

    private Context mContext;

    private ProgressBar mProgressBar;

    private Button btnPositive, btnNegative;

    private TextView mTvTitle, desc_textview;


    public TeenModePopDialog(Context context) {
        super(context, R.style.UpdateDialogNew);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.dialog_teenmodepop_dialog);

        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        btnPositive = (Button) findViewById(R.id.btn_dialog_positive);
        btnNegative = (Button) findViewById(R.id.btn_dialog_negative);
        btnPositive.setOnFocusChangeListener(this);
        btnNegative.setOnFocusChangeListener(this);
        mTvTitle = (TextView) findViewById(R.id.tv_dialog_title);
        desc_textview = (TextView) findViewById(R.id.desc_textview);
        btnNegative.requestFocus();
        TeenagersManger.getInstance().exposureShowDecDialog();

    }

    private void setTitle(String title) {
        mTvTitle.setText(title);
    }

    private void setMessage(String msg) {
        desc_textview.setText(msg);
    }

    private void setPositiveText(String positiveText) {
        if (positiveText == null || positiveText.equals(""))
            btnPositive.setVisibility(View.GONE);
        else
            btnPositive.setText(positiveText);
    }

    private void setNegativeText(String negativeText) {
        if (negativeText == null || negativeText.equals(""))
            btnNegative.setVisibility(View.GONE);
        else
            btnNegative.setText(negativeText);
    }

    private void setPositiveListener(View.OnClickListener btnPositiveListener) {
        btnPositive.setOnClickListener(btnPositiveListener);
    }

    private void setNegativeListener(View.OnClickListener btnNegativeListener) {
        btnNegative.setOnClickListener(btnNegativeListener);
    }

    /**
     * Set dialog's view when downloading APK file
     */


    /**
     * Called when the focus state of a view has changed.
     *
     * @param v        The view whose state has changed.
     * @param hasFocus The new focus state of v.
     */
    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (v != null && v instanceof Button) {
            Button button = (Button) v;
            if (hasFocus) {
                button.setTextColor(0xFFE8E8FF);
                button.setScaleX(1.1f);
                button.setScaleY(1.1f);
            } else {
                button.setTextColor(0xB3E8E8FF);
                button.setScaleX(1.0f);
                button.setScaleY(1.0f);
            }
        }

    }


    public static class Builder {
        public String mMsg, mTitle, mBtnPositiveText, mBtnNegativeText;
        public View.OnClickListener mBtnPositiveListener, mBtnNegativeListener;
        public OnCancelListener mCancelListener;

        private Context context;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setTitle(int titleRes) {
            this.mTitle = context.getString(titleRes);
            return this;
        }

        public Builder setTitle(String title) {
            this.mTitle = title;
            return this;
        }

        public Builder setMsg(String msg) {
            this.mMsg = msg;
            return this;
        }

        public Builder setMsg(int msgRes) {
            this.mMsg = context.getString(msgRes);
            return this;
        }

        public Builder setPositiveButton(String positiveText, View.OnClickListener btnPositiveListener) {
            this.mBtnPositiveText = positiveText;
            this.mBtnPositiveListener = btnPositiveListener;
            return this;
        }

        public Builder setPositiveButton(int positiveTextRes, View.OnClickListener btnPositiveListener) {
            this.mBtnPositiveText = context.getString(positiveTextRes);
            this.mBtnPositiveListener = btnPositiveListener;
            return this;
        }

        public Builder setNegativeButton(String negativeText, View.OnClickListener btnNegativeListener) {
            this.mBtnNegativeText = negativeText;
            this.mBtnNegativeListener = btnNegativeListener;
            return this;
        }

        public Builder setNegativeButton(int negativeTextRes, View.OnClickListener btnNegativeListener) {
            this.mBtnNegativeText = context.getString(negativeTextRes);
            this.mBtnNegativeListener = btnNegativeListener;
            return this;
        }

        public Builder setCancelListener(OnCancelListener cancelListener) {
            this.mCancelListener = cancelListener;
            return this;
        }

        public TeenModePopDialog create() {
            TeenModePopDialog dialog = new TeenModePopDialog(context);
            dialog.show();
            dialog.setTitle(mTitle);
            dialog.setMessage(mMsg);
            dialog.setPositiveText(mBtnPositiveText);
            dialog.setNegativeText(mBtnNegativeText);
            dialog.setPositiveListener(mBtnPositiveListener);
            dialog.setNegativeListener(mBtnNegativeListener);
            dialog.setOnCancelListener(mCancelListener);
            return dialog;
        }

        public TeenModePopDialog show() {
            return create();
        }
    }
}