package com.sohuott.tv.vod.view;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.PopupWindow;
import com.sohuott.tv.vod.R;

/**
 * 微信客服二维码弹窗
 *
 * <AUTHOR>
 * @Date Created on 2020/4/28.
 */
public class WechatView {

    private Context mContext;

    private PopupWindow mWechatWindow;

    // private ImageView mWechatImage;

    private int mBgWidth,mBgHeight;

    public WechatView(Context context) {
        this.mContext = context;
        this.initView();
    }

    private void initView() {
        View contentView = LayoutInflater.from(mContext).inflate(R.layout.dialog_wechat_qr, null);

        mBgWidth = mContext.getResources().getDimensionPixelSize(R.dimen.x918);
        mBgHeight = mContext.getResources().getDimensionPixelSize(R.dimen.y753);

        if (mWechatWindow == null) {
//            mWechatWindow = new PopupWindow(contentView, ViewGroup.LayoutParams.MATCH_PARENT,
//                    ViewGroup.LayoutParams.MATCH_PARENT, true);
            mWechatWindow = new PopupWindow(contentView, mBgWidth, mBgHeight, true);
            mWechatWindow.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.dialog_more_detail_bg));
            mWechatWindow.setTouchable(true);
            mWechatWindow.setFocusable(true);
            mWechatWindow.setOutsideTouchable(true);
            mWechatWindow.setAnimationStyle(R.style.PopupAnimation);
            mWechatWindow.setContentView(contentView);
        }

    }

    public void show(View parent) {
        if (mWechatWindow != null && parent != null) {
            mWechatWindow.showAtLocation(parent, Gravity.CENTER, 0, 0);
        }
    }
}
