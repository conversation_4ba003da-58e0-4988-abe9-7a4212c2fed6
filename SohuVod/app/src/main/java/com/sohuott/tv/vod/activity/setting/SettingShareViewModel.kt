package com.sohuott.tv.vod.activity.setting

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope

/**
 *
 * @Description
 * @date 2022/3/24 15:19
 * <AUTHOR>
 * @Version 1.0
 */
class SettingShareViewModel : ViewModel() {
     var focusChange = MutableLiveData<Boolean>()

    fun focusChange(change: <PERSON>olean) {
        focusChange.postValue(change)
        viewModelScope
    }

}