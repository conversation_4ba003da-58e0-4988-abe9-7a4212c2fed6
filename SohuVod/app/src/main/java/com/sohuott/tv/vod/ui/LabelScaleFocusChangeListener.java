package com.sohuott.tv.vod.ui;

import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.sohuott.tv.vod.R;

/**
 * Created by feng<PERSON><PERSON> on 16-1-13.
 */
public class LabelScaleFocusChangeListener extends ScaleFocusChangeListener {

    public LabelScaleFocusChangeListener() {
        super();
        setFocusCallback(new FocusCallback() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                TextView tv = (TextView) v.findViewById(R.id.tv_label_title);
                if(tv != null) {
                    if(hasFocus) {
                        tv.setSelected(true);
                        tv.setMarqueeRepeatLimit(-1);
                        tv.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                    } else {
                        tv.setSelected(false);
                        tv.setEllipsize(TextUtils.TruncateAt.END);
                    }
                }
            }
        });
    }

}
