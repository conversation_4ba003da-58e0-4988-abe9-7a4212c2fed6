package com.sohuott.tv.vod.search


import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import androidx.appcompat.widget.AppCompatTextView
import com.sohuott.tv.vod.R
import com.tencent.bugly.proguard.s

class BlinkingCursorTextView(context: Context, attrs: AttributeSet) : AppCompatTextView(context, attrs) {

    private val BLINK_TIME_MS = 500L // cursor blink time in milliseconds
    private var showCursor = false // flag to indicate whether to show cursor or not
    private val cursorHandler = Handler(Looper.getMainLooper()) // handler to schedule cursor blinking
    private val paint = Paint()
    private val cursorRunnable = object : Runnable {
        override fun run() {
            showCursor = !showCursor // toggle flag
            invalidate() // redraw textview
            cursorHandler.postDelayed(this, BLINK_TIME_MS) // schedule next blink
        }
    }

    init {
        cursorHandler.postDelayed(cursorRunnable, BLINK_TIME_MS) // start cursor blinking


        val hintText = "  输入片名/人名首字母或全拼"
        val spannableHint = SpannableStringBuilder(hintText)

        // 将 "输入片名" 设为 #FF6247
        val colorSpan1 = ForegroundColorSpan(Color.parseColor("#FF6247"))
        spannableHint.setSpan(colorSpan1, 9, 12, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)

        // 将 "人名首字母或全拼" 设为 #FF6247
        val colorSpan2 = ForegroundColorSpan(Color.parseColor("#FF6247"))
        spannableHint.setSpan(colorSpan2, 13, hintText.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)


        hint = spannableHint
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (showCursor) {
            paint.color = Color.parseColor("#FF6247") // cursor color same as text color
            paint.isAntiAlias = true
            paint.style = Paint.Style.FILL
            val rectF = RectF(
                layout.getPrimaryHorizontal(text.length).toInt().toFloat(),
                layout.getLineTop(layout.getLineForOffset(text.length)).toFloat(),
                layout.getPrimaryHorizontal(text.length).toInt().toFloat() + context.resources.getDimensionPixelOffset(R.dimen.x4).toFloat(),
                layout.getLineBottom(layout.getLineForOffset(text.length)).toFloat()
            )
            canvas.drawRoundRect(rectF, 10f, 10f, paint) // draw cursor line
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cursorHandler.removeCallbacks(cursorRunnable) // stop cursor blinking when view is detached from window
    }
}
