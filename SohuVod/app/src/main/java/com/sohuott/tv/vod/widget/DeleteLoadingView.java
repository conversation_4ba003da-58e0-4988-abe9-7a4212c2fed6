package com.sohuott.tv.vod.widget;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-4-12.
 */

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;

import com.sohuott.tv.vod.R;

/**
 * @Title:
 * @Description:
 * @Author: yangdongqi
 * @Since: 2014-8-14
 * @Version: 1.1.0
 */
public class DeleteLoadingView extends ImageView {

    private int mDuration;
    private boolean mIsAutoStart;
    private Animator mAnimator;

    public DeleteLoadingView(Context context) {
        super(context);
        mDuration = 300;
        mIsAutoStart = false;

        if (getDrawable() == null) {
            setImageResource(R.drawable.history_delete_process_cover);
        }

        ObjectAnimator animator = ObjectAnimator.ofFloat(this, "rotation", 0.0f, 360f);
        animator.setDuration(mDuration);
        animator.setRepeatMode(ObjectAnimator.RESTART);
        animator.setRepeatCount(ObjectAnimator.INFINITE);
        animator.setInterpolator(new LinearInterpolator());
        mAnimator = animator;
        if (mIsAutoStart) {
            start();
        }
    }

    public DeleteLoadingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mDuration = 300;
        mIsAutoStart = false;

        if (getDrawable() == null) {
            setImageResource(R.drawable.history_delete_process_cover);
        }

        ObjectAnimator animator = ObjectAnimator.ofFloat(this, "rotation", 0.0f, 360f);
        animator.setDuration(mDuration);
        animator.setRepeatMode(ObjectAnimator.RESTART);
        animator.setRepeatCount(ObjectAnimator.INFINITE);
        animator.setInterpolator(new LinearInterpolator());
        mAnimator = animator;
        if (mIsAutoStart) {
            start();
        }
    }

    public DeleteLoadingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mDuration = 300;
        mIsAutoStart = false;

        if (getDrawable() == null) {
            setImageResource(R.drawable.history_delete_process_cover);
        }

        ObjectAnimator animator = ObjectAnimator.ofFloat(this, "rotation", 0.0f, 360f);
        animator.setDuration(mDuration);
        animator.setRepeatMode(ObjectAnimator.RESTART);
        animator.setRepeatCount(ObjectAnimator.INFINITE);
        animator.setInterpolator(new LinearInterpolator());
        mAnimator = animator;
        if (mIsAutoStart) {
            start();
        }
    }


    public void start() {
        mAnimator.start();
    }

    public void end() {
        mAnimator.end();
    }

    public void setDuration(long duration) {
        mAnimator.setDuration(duration);
    }

    public long getDuration() {
        return mAnimator.getDuration();
    }

    @Override
    public void setVisibility(int visibility) {
        super.setVisibility(visibility);
        switch (visibility) {
            case GONE:
            case INVISIBLE:
                end();
                break;
            default:
                start();
                break;
        }
    }
}
