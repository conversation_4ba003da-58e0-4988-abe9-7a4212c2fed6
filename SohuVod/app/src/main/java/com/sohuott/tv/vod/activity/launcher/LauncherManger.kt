package com.sohuott.tv.vod.activity.launcher


class LauncherManager {

    private var cateCodeMap : HashMap<Int, Boolean>? = null
    private var playListIdMap : HashMap<Int, Boolean>? = null

    companion object {
        private var sInstance: LauncherManager? = null
        @JvmStatic
        fun getInstance(): LauncherManager {
            if (sInstance == null) {
                sInstance = LauncherManager()
            }
            return sInstance!!
        }
    }

    fun setCateCodeMap(cateCodeMap: HashMap<Int, Boolean>) {
        this.cateCodeMap = cateCodeMap
    }

    fun setPlayListIdMap(playListIdMap: HashMap<Int, Boolean>) {
        this.playListIdMap = playListIdMap
    }

    fun getCateCodeMap() : HashMap<Int, Boolean> ?{
        return cateCodeMap
    }

    fun getPlayListIdMap() : HashMap<Int, Boolean>? {
        return playListIdMap;
    }

}