package com.sohuott.tv.vod.utils;

import android.content.Context;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;

/**
 * ${DESC}
 *
 * <AUTHOR>
 *         created at 2018/2/6
 */
public class AssetUtils {

    /**
     * read str from file
     * @param context
     * @param filePath
     * @return
     */
    public static String readString(Context context,String filePath){
        InputStream is=null;
        InputStreamReader reader=null;
        try {
            is=context.getAssets().open(filePath);
            reader = new InputStreamReader(is, Charset.forName("UTF-8"));
            BufferedReader bufferedReader = new BufferedReader(reader);
            StringBuffer buffer = new StringBuffer("");
            String str;
            while ((str = bufferedReader.readLine()) != null) {
                buffer.append(str);
                buffer.append("\n");
            }
            return buffer.toString();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            closeIOQuietly(is,reader);
        }
        return  "";
    }

    public static void closeIOQuietly(final Closeable... closeables) {
        if (closeables == null) {
            return;
        }
        for (Closeable closeable : closeables) {
            if (closeable != null) {
                try {
                    closeable.close();
                } catch (IOException ignored) {
                }
            }
        }
    }
}
