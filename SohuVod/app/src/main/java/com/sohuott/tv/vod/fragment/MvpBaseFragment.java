package com.sohuott.tv.vod.fragment;

import android.os.Bundle;

import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.presenter.BasePresenter;
import com.sohuott.tv.vod.presenter.ReflectionPresenterFactory;
import com.sohuott.tv.vod.view.IView;

/**
 * Created by hpb on 2017/7/12.
 */
public abstract  class MvpBaseFragment<P extends BasePresenter> extends BaseFragment implements
        IView{
    protected  P mPresenter;
    private ReflectionPresenterFactory<P> presenterFactory =
            ReflectionPresenterFactory.fromViewClass(getClass());
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getPresenter().attachView(this);

    }
    public P getPresenter(){
        if(mPresenter==null){
            mPresenter=presenterFactory.createPresenter();
        }
        return mPresenter;
    }

    @Override
    public void onDestroy() {
        if(getPresenter()!=null){
            getPresenter().detachView();
        }
        super.onDestroy();
    }

}
