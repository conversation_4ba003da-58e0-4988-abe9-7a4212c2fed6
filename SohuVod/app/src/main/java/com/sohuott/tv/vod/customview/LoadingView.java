package com.sohuott.tv.vod.customview;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import com.sohuott.tv.vod.R;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/3/30.
 */
public class LoadingView extends ImageView {

    private AnimationDrawable animationDrawable;

    public LoadingView(Context context) {
        super(context);
        init();
    }

    public LoadingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public LoadingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (animationDrawable != null) {
            animationDrawable.stop();
            animationDrawable = null;
        }
    }

    public void hide() {
        setVisibility(View.GONE);
        if (animationDrawable != null) {
            animationDrawable.stop();
        }
    }

    public void show() {
        setVisibility(View.VISIBLE);
        if (animationDrawable != null) {
            animationDrawable.start();
        }
    }

    private void init() {
        setBackgroundResource(R.drawable.loading);
        animationDrawable = (AnimationDrawable) getBackground();
        animationDrawable.start();
    }

}
