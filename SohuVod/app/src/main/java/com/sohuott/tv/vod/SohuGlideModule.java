package com.sohuott.tv.vod;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.Registry;
import com.bumptech.glide.annotation.GlideModule;
import com.bumptech.glide.integration.okhttp3.OkHttpUrlLoader;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool;
import com.bumptech.glide.load.engine.cache.DiskLruCacheFactory;
import com.bumptech.glide.load.engine.cache.LruResourceCache;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.module.AppGlideModule;
import com.bumptech.glide.request.RequestOptions;
import com.sh.ott.net.interceptor.CurlLoggingInterceptor;
import com.sh.ott.net.interceptor.SignInterceptor;
import com.sohu.lib_utils.TrustAll;
import com.sohuott.tv.vod.app.App;
import com.sohuott.tv.vod.utils.GlideInterceptor;

import java.io.InputStream;
import java.util.Arrays;
import java.util.Collections;

import okhttp3.Call;
import okhttp3.ConnectionSpec;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;

/**
 * Created by wenjingbian on 2018/2/27.
 */

@GlideModule
public class SohuGlideModule extends AppGlideModule {

    @Override
    public void registerComponents(@NonNull Context context, @NonNull Glide glide, @NonNull Registry registry) {
        OkHttpClient client = new OkHttpClient.Builder()
                .protocols(Collections.singletonList(Protocol.HTTP_1_1))
                .addInterceptor(new GlideInterceptor())
                .addInterceptor(new CurlLoggingInterceptor())
                .addInterceptor(new SignInterceptor(context))
                .hostnameVerifier(new TrustAll.hostnameVerifier())
                .sslSocketFactory(TrustAll.INSTANCE.socketFactory(), TrustAll.INSTANCE.systemDefaultTrustManager())
                .connectionSpecs(Arrays.asList(ConnectionSpec.MODERN_TLS,
                        ConnectionSpec.COMPATIBLE_TLS,
                        ConnectionSpec.CLEARTEXT))
                .build();

        OkHttpUrlLoader.Factory factory = new OkHttpUrlLoader.Factory((Call.Factory) client);
        registry.replace(GlideUrl.class, InputStream.class, factory);
    }

    @Override
    public void applyOptions(Context context, GlideBuilder builder) {
        //set caches for disk and cache
        int memoryCacheSize = 10 * 1024 * 1024;
        int bitmapCacheSize = 0;
        int diskCacheSize = 50 * 1024 * 1024;
        builder.setDiskCache(new DiskLruCacheFactory(context.getCacheDir().getAbsolutePath(), "sohu_image_cache", diskCacheSize));
        builder.setMemoryCache(new LruResourceCache(memoryCacheSize));
        builder.setBitmapPool(new LruBitmapPool(bitmapCacheSize));

        //set default options
        RequestOptions requestOptions = new RequestOptions().format(DecodeFormat.PREFER_RGB_565)
                .disallowHardwareConfig().fitCenter().diskCacheStrategy(DiskCacheStrategy.RESOURCE);
        builder.setDefaultRequestOptions(requestOptions);
        if (App.getLoggerDebug()) {
            builder.setLogLevel(Log.DEBUG);
        } else {
            builder.setLogLevel(Log.ERROR);
        }
    }

    @Override
    public boolean isManifestParsingEnabled() {
        //To upgrade initialized time of glide, it's useful for Glide V3 + Glide V4 when returned true
        return false;
    }
}
