package com.sohuott.tv.vod.view;

import com.sohuott.tv.vod.lib.model.PermissionCheck;
import com.sohuott.tv.vod.lib.model.AlbumInfo;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo;
import com.sohuott.tv.vod.lib.model.SouthMediaCheckResult;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.model.VideoInfo;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;

import java.util.List;

/**
 * Created by music on 2016/3/31.
 */
public interface NewVideoPlayerView {
    void showLoading();

    void hideLoading();

    void addAlbumData(AlbumInfo model);

    void addPgcPlayList(PgcAlbumInfo response);

    void addRecommendData(VideoDetailRecommend model);

    void addAlbumVideosData(List<EpisodeVideos.Video> videoList);

    void likeSuccess();

    void addMKey(PermissionCheck model);

    void onMKeyError();

    void addVideoInfo(VideoInfo model);

    void addTagListData(List<ListAlbumModel> albumList);

    void onError(boolean isNet);

    void onUrlError();
    //南传鉴权接口
    void onSouthMediaCheck(SouthMediaCheckResult result);
    void onSouthMediaCheckError();

    void dlnaPlay();
}
