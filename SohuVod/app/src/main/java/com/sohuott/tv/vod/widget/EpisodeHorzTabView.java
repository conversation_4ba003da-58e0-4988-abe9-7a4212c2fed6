/*
 * Copyright (C) 2011 The Android Open Source Project
 * Copyright (C) 2012 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.sohuott.tv.vod.widget;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.EpisodeHorzTabAdapter;
import com.sohuott.tv.vod.adapter.TabPagerAdapter;
import com.sohuott.tv.vod.fragment.EpisodeBaseFragmentNew;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.Util;

import java.lang.ref.WeakReference;

/**
 * This widget implements the dynamic action bar tab behavior that can change
 * across different configurations or circumstances.
 */
public class EpisodeHorzTabView extends HorizontalScrollView
        implements  View.OnKeyListener,
        View.OnTouchListener,ViewPager.OnPageChangeListener{

    private static final int MSG_TAB_SELECT_UPDATE = 2001;
    private final LinearLayout mTabLayout;

    private ViewPager mViewPager;
    private ViewPager.OnPageChangeListener mListener;
    private int mSelectedTabIndex;
    private boolean mChangeTab = false;

    private boolean mIsChildTabView = false;
    private boolean mIsMenuView = false;

    private final OnFocusChangeListener mTabFocusChangeListener = new OnFocusChangeListener() {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            TabView tabView = (TabView) v;
            final int newSelected = tabView.getIndex();
            if (getContext() != null &&
                    !((Activity) getContext()).isFinishing()) {
                Message msg = new Message();
                msg.what = MSG_TAB_SELECT_UPDATE;
                msg.arg1 = newSelected;
                mHandler.removeMessages(MSG_TAB_SELECT_UPDATE);
                mHandler.sendMessageDelayed(msg, 100);
            }
            setCurrentTab(newSelected);

            if(mIsChildTabView){
                return;
            }
        }
    };
    final EpisodeTabHandler mHandler = new EpisodeTabHandler();
    final class EpisodeTabHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_TAB_SELECT_UPDATE:
                    if (mSelectedTabIndex != msg.arg1) {
                        mChangeTab = true;
                    }
                    mViewPager.setCurrentItem(msg.arg1);
                    break;
                default:
                    break;
            }
            super.handleMessage(msg);
        }
    }



    public EpisodeHorzTabView(Context context) {
        this(context, null);
    }

    private boolean mInTouchMode;

    public EpisodeHorzTabView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setVerticalScrollBarEnabled(false);
        mTabLayout = new LinearLayout(context);
        mTabLayout.setOrientation(LinearLayout.HORIZONTAL);
        LayoutParams layoutParams = new LayoutParams(MATCH_PARENT, MATCH_PARENT);
        addView(mTabLayout, layoutParams);
        mInTouchMode= Util.isSupportTouchVersion(context);
    }

    public void setIsChildTabView(boolean childTabView) {
        mIsChildTabView = childTabView;
    }

    /**
     *
     * @param isMenuView 是否来自全屏播控
     */
    public void setIsMenuView(boolean isMenuView) {
        mIsMenuView = isMenuView;
    }




    @Override
    public void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
//        int widthMode1 = MeasureSpec.getMode(widthMeasureSpec);
//        int widthSize1 = MeasureSpec.getSize(widthMeasureSpec);
//        int heightMode1 = MeasureSpec.getMode(heightMeasureSpec);
//        int heightSize1 = MeasureSpec.getSize(heightMeasureSpec);
        if (mViewPager == null) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
            return;
        }
        final int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        final boolean lockedExpanded = widthMode == MeasureSpec.EXACTLY;
        setFillViewport(lockedExpanded);

        final int oldWidth = getMeasuredWidth();
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        final int newWidth = getMeasuredWidth();

        if (lockedExpanded && oldWidth != newWidth) {
            // Recenter the tab display if we're at a new (scrollable) size.
            setCurrentItem(mSelectedTabIndex);
        }
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    @Override
    public void onPageScrollStateChanged(int arg0) {
        if (mListener != null) {
            mListener.onPageScrollStateChanged(arg0);
        }
    }
    @Override
    public void onPageScrolled(int arg0, float arg1, int arg2) {
        if (mListener != null) {
            mListener.onPageScrolled(arg0, arg1, arg2);
        }
    }
    @Override
    public void onPageSelected(int arg0) {
        int prePos= getCurrentItemIndex();
        setCurrentTab(arg0);
        TabPagerAdapter adapter = ((TabPagerAdapter) (mViewPager.getAdapter()));
        if (mListener != null) {
            mListener.onPageSelected(arg0);
        }
        if (mChangeTab) {
            View child = mTabLayout.getChildAt(mSelectedTabIndex);
            if (child.getLeft() == 0 && child.getRight() == 0
                    && child.getTop() == 0 && child.getBottom() == 0) {
                post(new InnerRunnable(this));
            } else {
                scroll(true);
            }
        }
        mChangeTab = false;
        if (adapter != null) {
            adapter.setTabChange(false);
        }
    }

    private static class InnerRunnable implements Runnable{
        WeakReference<EpisodeHorzTabView> mWrapper;
        InnerRunnable(EpisodeHorzTabView view){
            mWrapper = new WeakReference<>(view);
        }
        @Override
        public void run() {
            EpisodeHorzTabView episodeHorzTabView = mWrapper.get();
            if (episodeHorzTabView != null){
                episodeHorzTabView.scroll(false);
            }
        }
    }

    private void scroll(boolean smooth) {
        View child = mTabLayout.getChildAt(mSelectedTabIndex);
        if(child==null){
            return;
        }
        Rect mTempRect = new Rect();
        child.getDrawingRect(mTempRect);
        offsetDescendantRectToMyCoords(child, mTempRect);
        int scrollDelta = computeScrollDeltaToGetChildRectOnScreen(mTempRect);
        LibDeprecatedLogger.d("scrollDelta = " + scrollDelta);
        if (scrollDelta != 0) {
            if (smooth) {
                smoothScrollBy(scrollDelta, 0);
            } else {
                scrollBy(scrollDelta, 0);
            }
        }
    }

    public void setViewPager(ViewPager view) {
        if (mViewPager != null) {
            mViewPager.setOnPageChangeListener(null);
        }
        final PagerAdapter adapter = view.getAdapter();
        if (adapter == null) {
            throw new IllegalStateException("ViewPager does not have adapter instance.");
        }
        mViewPager = view;
        view.setOnPageChangeListener(this);
        notifyDataSetChanged();
    }

    public void notifyDataSetChanged() {
        mTabLayout.removeAllViews();
        TabPagerAdapter tabPagerAdapter = (TabPagerAdapter) mViewPager.getAdapter();
        int count = tabPagerAdapter.getCount();
        for (int i = 0; i < count; i++) {
            addTab(i, count);
        }
        if (mSelectedTabIndex > count - 1) {
            mSelectedTabIndex = count - 1;
        }
        setCurrentItem(mSelectedTabIndex);
        requestLayout();
    }

    private void addTab(int index, int count) {
        TabPagerAdapter tabPagerAdapter = (TabPagerAdapter) mViewPager.getAdapter();
        TabView view = new TabView(getContext());
        view.mIndex = index;
        view.setId(index + 1);
        view.setFocusable(true);
        view.setIncludeFontPadding(false);
        view.setOnFocusChangeListener(mTabFocusChangeListener);
        int viewId=view.getId();
        if (index == 0) {
            view.setNextFocusLeftId(viewId);
        } else {
            view.setNextFocusLeftId(viewId - 1);
        }
        int tabPagerAdapterCount=tabPagerAdapter.getCount();
        if (index == tabPagerAdapterCount - 1) {
            view.setNextFocusRightId(viewId);
        } else {
            view.setNextFocusRightId(viewId + 1);
        }
        view.setGravity(Gravity.CENTER);
        view.setText(tabPagerAdapter.getText(index));
        view.setSingleLine(true);
        view.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelSize(R.dimen.x28));
        view.setTextColor(getResources()
                .getColorStateList(R.color.episode_tab_item_selector_color));
        if (mIsMenuView){
            view.setBackgroundResource(R.drawable.episode_menu_vrs_item_selector);
        }else {
            view.setBackgroundResource(R.drawable.episode_vrs_item_selector);
        }
        view.setOnKeyListener(this);
        int width = getResources().getDimensionPixelSize(R.dimen.x333);
        int height = getResources().getDimensionPixelSize(R.dimen.y74);


        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                width, height);
        if (index != 0) {
            if(mIsChildTabView){
                layoutParams.leftMargin = (int) getResources().getDimension(R.dimen.x30);
            }else {
                layoutParams.leftMargin = (int) getResources().getDimension(R.dimen.x17);
            }
        }
        view.setLayoutParams(layoutParams);
        if(mInTouchMode){
            view.setOnTouchListener(this);
        }
        mTabLayout.addView(view);
    }

    public void setViewPager(ViewPager view, int initialPosition) {
        setViewPager(view);
        setCurrentItem(initialPosition);
    }

    public int getCurrentItemIndex() {
        return mSelectedTabIndex;
    }

    public void setCurrentItem(int item) {
        if (mViewPager == null) {
            throw new IllegalStateException("ViewPager has not been bound.");
        }
        setCurrentTab(item);
        scroll(true);
        if (mTabLayout != null && mTabLayout.hasFocus() && mTabLayout.getChildAt(mSelectedTabIndex) != null) {
            mTabLayout.getChildAt(mSelectedTabIndex).requestFocus();
        }
        mViewPager.setCurrentItem(item, false);//do not scroll
    }

    public void setCurrentTab(int item) {
        if (mSelectedTabIndex != item) {
            mChangeTab = true;
        }
        mSelectedTabIndex = item;
        int tabCount = mTabLayout.getChildCount();
        for (int i = 0; i < tabCount; i++) {
            View child = mTabLayout.getChildAt(i);
            boolean isSelected = (i == item);
            child.setSelected(isSelected);
        }
    }

    public void setCurrentTabFocus() {
        View child = mTabLayout.getChildAt(mSelectedTabIndex);
        if (child != null) {
            child.requestFocus();
        }
    }

    public View getCurrentTabFocusView() {
        View child = mTabLayout.getChildAt(mSelectedTabIndex);
        if (child != null) {
            return child;
        }
        return null;
    }

    public void setOnPageChangeListener(ViewPager.OnPageChangeListener listener) {
        mListener = listener;
    }

    @Override
    public int getHorizontalFadingEdgeLength() {
        return (int) getResources().getDimension(R.dimen.x367);
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
            if (mViewPager != null && mViewPager.getAdapter() != null) {
                EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
                EpisodeBaseFragmentNew fragment = (EpisodeBaseFragmentNew) adapter.getItem(mViewPager.getCurrentItem());
                fragment.setFragmentFocus(true);
            }
            return true;
        } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
            if (mIsMenuView){
                if (mViewPager != null && mViewPager.getAdapter() != null) {
                    EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
                    EpisodeBaseFragmentNew fragment = (EpisodeBaseFragmentNew) adapter.getItem(mViewPager.getCurrentItem());
                    fragment.setFragmentFocus(true);
                }
                return true;
            }
            if (mViewPager != null && mViewPager.getId() == R.id.episode_init_from_player) {
                return true;
            }
        }
        return false;
    }

    public static class TabView extends androidx.appcompat.widget.AppCompatTextView {
        private int mIndex;

        public TabView(Context context) {
            super(context);
        }

        @Override
        public void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);

            // Re-measure if we went beyond our maximum size.
//            if (mMaxTabWidth > 0 && getMeasuredWidth() > mMaxTabWidth) {
//                super.onMeasure(MeasureSpec.makeMeasureSpec(mMaxTabWidth, MeasureSpec.EXACTLY),
//                        heightMeasureSpec);
//            }
        }

        public int getIndex() {
            return mIndex;
        }
    }

    /**
     * touch event for tab change
     * @param v
     * @param event
     * @return
     */
    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if(event.getAction()==MotionEvent.ACTION_UP){
           int index= mTabLayout.indexOfChild(v);
           setCurrentItem(index);
        }
        return true;
    }
}
