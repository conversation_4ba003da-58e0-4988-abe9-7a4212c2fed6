package com.sohuott.tv.vod.app

import XCrashApp
import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import com.airbnb.mvrx.Mavericks
import com.bytedance.boost_multidex.BoostMultiDex
import com.sh.apm.trace.canary.AppStartTimeManager
import com.sh.ott.video.ConfigBean
import com.sh.ott.video.ShPlayerConfig
import com.sh.ott.video.monitor.api.SohuMonitor
import com.sh.ott.video.vv.VvAppInfo
import com.sh.ott.video.vv.VvConfig
import com.sohu.lib_utils.DeviceUtils
import com.sohu.lib_utils.MetaDataHelper
import com.sohu.lib_utils.PrefUtil
import com.sohu.lib_utils.Utils
import com.sohu.ott.base.lib_user.UserApp
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohu.ott.base.lib_user.UserInfoHelper.getGid
import com.sohuott.tv.base_room.manager.AppVodDatabaseManger
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.BuildConfig
import com.sohuott.tv.vod.account.LibAccountApp
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper
import com.sohuott.tv.vod.app.config.AppConfigInfoManger
import com.sohuott.tv.vod.base_router.RouterManger
import com.sohuott.tv.vod.lib.LibDeprecatedApp
import com.sohuott.tv.vod.lib.api.OkHttpClientInstance
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.service.LoggerUpload
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.bugly.crashreport.CrashReport.UserStrategy


/**
 * app 控制
 */
@SuppressLint("StaticFieldLeak")
object App {

    private lateinit var mApp: Context

    @JvmStatic
    var debug: Boolean = false

    @JvmStatic
    var adDebug: Boolean = false

    @JvmStatic
    var loggerDebug: Boolean = false


    @JvmStatic
    fun onAttach(context: Context) {
        try {
            BoostMultiDex.install(context);
        } catch (e: Throwable) {
            AppLogger.e("onAttach error :${e.localizedMessage}")
        }
    }
    val debugBuildId = 7401

    @JvmStatic
    fun onCreate(app: Context?) {
        if (app == null) return
        mApp = app
        initMust()
//        UMConfigure.init(
//            app,
//            "6840f16979267e02107a1235",
//            "Umeng",
//            UMConfigure.DEVICE_TYPE_PHONE,
//            ""
//        );
        AppLogger.d("debug sub version $debugBuildId")
//        UMCrash.setAppVersion("7.4.0.1", "5", "$debugBuildId")
//        NetConfig.debug = true
    }

    /**
     * 需要同意隐私协议之后才可以初始化的
     */
    @JvmStatic
    fun onCreatePrivacy() {
        initPlayerConfigInfo()
        initVV()
//        AdApp.init(mApp)
    }

    @JvmStatic
    fun printAppInfo(): String {
        val info = "版本信息《 " +
                "app version：${DeviceUtils.getVersionName()}  " +
                "device info：{设备型号:${DeviceUtils.getDeviceModel()}-CPU型号:${DeviceUtils.getDeviceCpuHardware()}-设备品牌:${DeviceUtils.getDeviceBrAND()}-设备制造商:${DeviceUtils.getDeviceManufacturer()}-CPU架构:${DeviceUtils.getDeviceCpuAbi()} } " +
                "android version：${DeviceUtils.getDeviceAndroidVersion()} " +
                "PN：${MetaDataHelper.getPartnerNo()} " +
                "Gid:${UserInfoHelper.getGid()}" +
                "Launcher Time:${
                    AppStartTimeManager.getManger().getAppLauncherTotalCostTime()
                }" +
                "app init Time:${AppStartTimeManager.getManger().getAppInitTotalCostTime()}" +
                "》"
        AppLogger.i(
            info
        )
        return info

    }

    @JvmStatic
    fun initCrash() {
        val strategy = UserStrategy(mApp.applicationContext)
        CrashReport.initCrashReport(mApp.applicationContext, "900029252", false, strategy)
        reportCrashInfo()
    }

    private fun reportCrashInfo() {
        CrashReport.setAppChannel(
            mApp.applicationContext,
            Util.getPartnerNo(mApp.applicationContext)
        ) //设置渠道
        CrashReport.setAppVersion(
            mApp.applicationContext,
            Util.getVersionName(mApp.applicationContext).toString()
        ) //App的版本
        CrashReport.setAppPackage(
            mApp.applicationContext,
            mApp.applicationContext.getPackageName()
        ) //App的包名
        CrashReport.putUserData(
            mApp.applicationContext, "platformid",
            Util.getPlatformId(mApp.applicationContext).toString()
        )
        CrashReport.putUserData(
            mApp.applicationContext, "设备信息",
            "device info：{设备型号:${DeviceUtils.getDeviceModel()}-CPU型号:${DeviceUtils.getDeviceCpuHardware()}-设备品牌:${DeviceUtils.getDeviceBrAND()}-设备制造商:${DeviceUtils.getDeviceManufacturer()}-CPU架构:${DeviceUtils.getDeviceCpuAbi()} } "
        )
        CrashReport.setUserId(getGid())
    }

    @JvmStatic
    fun initXCrash() {
        LoggerUpload.getInstants().init(mApp)
        XCrashApp.init(mApp,"$debugBuildId")
    }

    private fun initMust() {
        PrefUtil.initMMKV(mApp)
        setModelLoggerDebug(mApp)
        Utils.init(mApp.applicationContext)
        initAppData(mApp)
        initUser(mApp)
        setModelApp(mApp)
        initARouter(mApp)
        initMviArchitecture(mApp)
        OkHttpClientInstance.init(mApp);
        initPlaySetting(mApp)
        initPlayer(mApp)
    }

    private fun initARouter(app: Context) {
        RouterManger.getInstance().init(app)
    }


    /**
     * 初始化 爱彼迎 mvi 框架
     */
    private fun initMviArchitecture(app: Context) {
        Mavericks.initialize(app)
    }

    private fun initPlaySetting(app: Context) {
        PlaySettingHelper.initContext(app)
    }


    /**
     * 设置依赖相关debug 控制
     */
    private fun setModelApp(app: Context) {
        LibDeprecatedApp.debug = debug
        LibAccountApp.debug = debug
    }

    /**
     * 这是依赖 日志debug控制
     */
    private fun setModelLoggerDebug(context: Context) {
        AppLogger.debug = loggerDebug
        AppLogger.init(context)
    }


    /**
     * 初始化用户相关
     */
    private fun initUser(app: Context) {
        UserApp.setDebugConfig(debug)
        UserApp.init(app);
    }

    /**
     * 初始化room数据库
     */
    private fun initAppData(app: Context) {
        AppVodDatabaseManger.getInstance().init(app)
    }

    /**
     * 初始化播放器
     */
    private fun initPlayer(app: Context) {
        ShPlayerConfig.sofaLoggerCall = false
        ShPlayerConfig.initShVideo(
            app, ConfigBean(
                gid = UserInfoHelper.getGid(),
                platId = MetaDataHelper.getPlatId().toInt(),
                debug = debug,
                adDebug = adDebug,
                debugLogger = loggerDebug,
                partnerNo = MetaDataHelper.getPartnerNo(),
                versionName = BuildConfig.VERSION_NAME,
                flavor = BuildConfig.FLAVOR,
                monitorKey = "2E53FC84F749D7F07FCC2A52C2547395",
                marlinSwitch = true,
                level = ShPlayerConfig.SOFA_LOG_WARN
            )
        )
        Log.d(
            "initPlayer",
            "播放器初始 sofa" + ShPlayerConfig.sofaSuccess + " drm ${ShPlayerConfig.sofaDrmSuccess}  logger call ${ShPlayerConfig.sofaLoggerCall}"
        )
        //暂时使用这种关闭
        SohuMonitor.setDebug(false)
    }

    /**
     * 初始化播放器配置信息
     */
    private fun initPlayerConfigInfo() {
        AppConfigInfoManger.getInstance().initPlayerConfigInfo()
    }

    private fun initVV() {
        VvConfig.initialize(mApp)
        VvConfig.isApp = true
        VvConfig.enableLogger = loggerDebug
        VvConfig.setAppInfo(VvAppInfo().apply {
            this.platId = MetaDataHelper.getPlatId()
            this.channel = MetaDataHelper.getPartnerNo()
            this.version = DeviceUtils.getVersionName()
        })
    }


    @JvmStatic
    fun getAppContext(): Context {
        return mApp
    }
}