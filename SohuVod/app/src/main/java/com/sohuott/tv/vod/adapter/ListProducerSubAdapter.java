package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.ProducerIntro;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.ListProducerView;

import java.util.List;

/**
 * Created by wenjingbian on 2017/5/1.
 */

public class ListProducerSubAdapter extends RecyclerView.Adapter<ListProducerSubAdapter.SubViewHolder> {

    private Context mContext;
    private RecyclerView mRecyclerView;
    private FocusBorderView mFocusBorderView;

    private ListProducerView mListProducerView;
    private List<ProducerIntro.DataEntity.AlbumsEntity> mAlbumsEntityList;

    private boolean isFirst = true;
    private boolean isItemSelected;
    private int mSelectedPos;

    public ListProducerSubAdapter(Context context, RecyclerView recyclerView) {
        this.mContext = context;
        this.mRecyclerView = recyclerView;
    }

    @Override
    public SubViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.menu_list_producer_item, parent, false);
        SubViewHolder subViewHolder = new SubViewHolder(view);
        return subViewHolder;
    }

    @Override
    public void onBindViewHolder(final SubViewHolder holder, int position) {
        holder.playlist_name.setText(mAlbumsEntityList.get(position).ptitle);
        holder.playlist_count.setText("视频" + mAlbumsEntityList.get(position).videoCount);

//        if (position == 0 && isFirst) {
//            holder.itemView.setSelected(true);
//            holder.itemView.requestFocus();
//            isFirst = false;
//        }

        if (isFirst && holder.getAdapterPosition() == mSelectedPos) {
            holder.itemView.requestFocus();
            holder.itemView.setSelected(true);
            isFirst = false;
        }

//        if (position != 0 && mRecyclerView.getChildAt(0) != null
//                && mRecyclerView.getChildAt(0).isSelected()) {
//            holder.itemView.setSelected(false);
//        }
    }

    @Override
    public int getItemCount() {
        return mAlbumsEntityList != null ? mAlbumsEntityList.size() : 0;
    }

    public void setDataSource(List<ProducerIntro.DataEntity.AlbumsEntity> albumsEntityList) {
        this.mAlbumsEntityList = albumsEntityList;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.mFocusBorderView = focusBorderView;
    }

    public void setView(ListProducerView listProducerView) {
        this.mListProducerView = listProducerView;
    }

    public void setIsFirst(boolean isFirst) {
        this.isFirst = isFirst;
    }

    public void setSelectedPos(int selectedId) {
        if (selectedId <= 0 || mAlbumsEntityList == null || mAlbumsEntityList.size() <= 0) {
            return;
        }
        for (ProducerIntro.DataEntity.AlbumsEntity tmpEntity : mAlbumsEntityList) {
            if (tmpEntity != null && tmpEntity.playlistid == selectedId) {
                mSelectedPos = mAlbumsEntityList.indexOf(tmpEntity);
                mRecyclerView.scrollToPosition(mSelectedPos);
                isItemSelected = true;
                return;
            }
        }
    }

    class SubViewHolder extends RecyclerView.ViewHolder {

        TextView playlist_name;
        TextView playlist_count;

        public SubViewHolder(final View itemView) {
            super(itemView);
            playlist_name = (TextView) itemView.findViewById(R.id.playlist_name);
            playlist_count = (TextView) itemView.findViewById(R.id.playlist_count);

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
                        playlist_name.setSelected(true);
                        playlist_name.setMarqueeRepeatLimit(-1);
                        playlist_name.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                        if (mRecyclerView != null && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                            if (mFocusBorderView != null) {
                                mFocusBorderView.setFocusView(v);
                                FocusUtil.setFocusAnimator(v, mFocusBorderView, 1);
                            }
                        }

                        if (mListProducerView != null) {
                            mListProducerView.refreshRightVideoList(getAdapterPosition(), mRecyclerView.indexOfChild(v));
                        }
                    } else {
                        if (isItemSelected) {
                            itemView.setSelected(false);
                        }
                        playlist_name.setSelected(false);
                        playlist_name.setEllipsize(TextUtils.TruncateAt.END);

                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(v);
                        }
                    }
                }
            });

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    int position = getAdapterPosition();
                    if (position == 0) {
                        if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
                            v.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_y));
                        }
                    } else if (position == getItemCount() - 1) {
                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                            v.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_y));
                        }
                    }
                    if (keyCode == KeyEvent.KEYCODE_DPAD_UP || keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                        isItemSelected = true;
                    } else {
                        isItemSelected = false;
                    }
                    return false;
                }
            });

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mListProducerView != null) {
                        mListProducerView.refreshRightVideoList(getAdapterPosition(), mRecyclerView.indexOfChild(v));
                    }
                }
            });
        }
    }
}
