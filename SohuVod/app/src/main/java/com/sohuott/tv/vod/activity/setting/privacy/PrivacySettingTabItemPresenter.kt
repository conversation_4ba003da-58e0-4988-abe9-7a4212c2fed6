package com.sohuott.tv.vod.activity.setting.privacy

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.alibaba.android.arouter.launcher.ARouter
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.setTimeCountClick
import com.sohuott.tv.vod.base_router.RouterPath
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingItem

/**
 *
 * @Description
 * @date 2022/3/23 10:08
 * <AUTHOR>
 * @Version 1.0
 */
class PrivacySettingTabItemPresenter(private val context: Context) :
    DefaultPresenter(R.layout.item_privacy_setting_tab_layout) {

    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as PrivacySettingItem
        val name = viewHolder.getView<TextView>(R.id.tv_privacy_tab)
        val layout = viewHolder.getView<ConstraintLayout>(R.id.privacy_tab_layout)
        name.text = item.name
        layout.setTimeCountClick(5, 2000) {
//            context.startActivity(Intent(context, BizDevelopMainActivity::class.java))
            ARouter.getInstance().build(RouterPath.BizDevelop.MAIN_ACTIVITY).navigation();
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup?): ViewHolder {
        // set clip children to false for slide transition
        // set clip children to false for slide transition
        val vh = super.onCreateViewHolder(parent)
        if (vh.view is ViewGroup) {
            (vh.view as ViewGroup).clipChildren = false
        }
        return vh


    }

    override fun setOnClickListener(holder: ViewHolder?, listener: View.OnClickListener?) {
        super.setOnClickListener(holder, listener)
    }
}