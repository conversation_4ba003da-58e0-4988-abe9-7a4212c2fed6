package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;

/**
 * Created by yizhang210244 on 2018/4/27.
 */

public class TextAnswerItemView extends RelativeLayout{
    private ProgressBar mProgressBar;
    private TextView mAnswerString;
    private TextView mAnswerNumber;

    public TextAnswerItemView(Context context) {
        super(context);
        init(context);
    }

    public TextAnswerItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public TextAnswerItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context){
        LayoutInflater.from(context).inflate(R.layout.view_text_answer_item, this, true);
        setFocusable(true);
        setBackgroundResource(R.drawable.answer_btn_selector);
        mProgressBar = (ProgressBar) findViewById(R.id.progressbar_answer);
        mAnswerString = (TextView) findViewById(R.id.answer_string);
        mAnswerNumber = (TextView) findViewById(R.id.answer_number);
        mAnswerString.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if(keyCode == KeyEvent.KEYCODE_DPAD_LEFT ||
                        keyCode == KeyEvent.KEYCODE_DPAD_RIGHT ||
                        keyCode == KeyEvent.KEYCODE_DPAD_UP ||
                        keyCode == KeyEvent.KEYCODE_DPAD_DOWN){
                    return true;
                }
                return false;
            }
        });
    }

    /**
     *
     * @param position  第几个位置 1,2,3,4
     * @param answerText 答案内容
     * @param number 多少人选这个答案
     * @param progress 进度
     * @param isQuestion  是问题还是答案 true 问题，false 答案
     * @param type 只有是答案的时候type有用，0 normal ,1答对 2答错。
     */
    public void setData(int position,String answerText,String number,int progress, boolean isQuestion,int type){
        if(!isQuestion){
            setFocusable(false);
            setBackgroundResource(R.color.transparent);
            mProgressBar.setVisibility(View.VISIBLE);
            if(type == 1){
                mProgressBar.setProgressDrawable(getContext().getResources().getDrawable(R.drawable.answer_right_progressbar));
            }else if(type == 2){
                mProgressBar.setProgressDrawable(getContext().getResources().getDrawable(R.drawable.answer_wrong_progressbar));
            }
            mProgressBar.setProgress(progress);
            mAnswerNumber.setText(number);
        }
        mAnswerString.setText("" + intToChar(position) + ": "+answerText);

    }

    public void setAnswerSelected(){
        mAnswerString.setFocusable(true);
        mAnswerString.requestFocus();
    }

    private char intToChar(int index){
        char a = (char) (index + 65);
        return a;
    }

}
