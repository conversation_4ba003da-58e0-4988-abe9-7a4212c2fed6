package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.search.TNinePopLayoutListener;

/**
 * Created by fengle<PERSON> on 17-6-15.
 */

public class TNineKeyboardPopLayout extends RelativeLayout {

    private TextView popContentTV;
    private TNineKeyboardPopView tNineKeyboardPopView;

    private String mPageName = "6_search";

    public TNineKeyboardPopLayout(Context context) {
        super(context);
        initUI(context);
    }

    public TNineKeyboardPopLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initUI(context);
    }

    public TNineKeyboardPopLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initUI(context);
    }

    protected void customLayoutInflater(Context context){
        setBackgroundColor(Color.parseColor("#c0000000"));
        LayoutInflater.from(context).inflate(
                R.layout.tnine_keyboard_pop_layout, this, true);
    }

    public void setPageName(String pageName){
        this.mPageName = pageName;
        if (tNineKeyboardPopView != null)
            tNineKeyboardPopView.setPageName(mPageName);
    }

    private void initUI(Context context) {
//        setBackgroundColor(Color.parseColor("#c0000000"));
//        LayoutInflater.from(context).inflate(R.layout.tnine_keyboard_pop_layout, this, true);
        customLayoutInflater(context);
        popContentTV = (TextView) findViewById(R.id.search_pop_tv);
        tNineKeyboardPopView = (TNineKeyboardPopView) findViewById(R.id.tnine_keyboard_pop_view);
        if(Util.isSupportTouchVersion(context)){
            setOnTouchListener(new OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    return true;
                }
            });
        }
    }

    public void show(String inputTVContent, String content) {
        popContentTV.setText(inputTVContent);
        tNineKeyboardPopView.show(content);
    }

    public void setContentTV(String conntent) {
        popContentTV.setText(popContentTV.getText().toString() + conntent);
    }

    public void settTNinePopLayoutListener(TNinePopLayoutListener listener) {
        tNineKeyboardPopView.settTNinePopLayoutListener(listener);
    }

}
