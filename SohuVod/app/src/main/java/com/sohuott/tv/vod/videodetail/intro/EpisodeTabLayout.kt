package com.sohuott.tv.vod.videodetail.intro

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewTreeObserver
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R

class EpisodeTabLayout : LinearLayout, ViewTreeObserver.OnGlobalFocusChangeListener, View.OnKeyListener {
    private var onItemSelectedListener: OnItemSelectedListener? = null
    private var lastId = 0
    private var hasSeries: Boolean = false
    private var hasTrailer: Boolean = false

    private var mEpisodeTabSelectLayout: RelativeLayout? = null
    private var mEpisodeTabTrailerLayout:RelativeLayout? = null
    private var mEpisodeTabSeriesLayout:RelativeLayout? = null

    private var mEpisodeTabSelectTv: TextView? = null
    private var mEpisodeTabTrailerTv: TextView? = null
    private var mEpisodeTabSeriesTv: TextView? = null

    interface OnItemSelectedListener {
        fun selectedById(id: Int)
    }


    constructor(context: Context) : super(context) {init(context)}
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {init(context)}
    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context)
    }

    private fun init(context: Context) {
        LayoutInflater.from(context).inflate(R.layout.episode_tab_layout, this, true)
        initView()
        initListener()
    }

    private fun initListener() {
        viewTreeObserver.addOnGlobalFocusChangeListener(this)

        mEpisodeTabTrailerLayout?.setOnKeyListener(this)
        mEpisodeTabSeriesLayout?.setOnKeyListener(this)
        mEpisodeTabSelectLayout?.setOnKeyListener(this)
    }

    private fun initView() {
        //episode tabs
        mEpisodeTabSelectLayout = findViewById(R.id.episode_tab_select)
        mEpisodeTabTrailerLayout = findViewById(R.id.episode_tab_trailer)
        mEpisodeTabSeriesLayout = findViewById(R.id.episode_tab_series)

        mEpisodeTabSeriesLayout!!.nextFocusDownId = R.id.episode_series

        mEpisodeTabSelectTv = mEpisodeTabSelectLayout?.findViewById(R.id.tv_main_title)
        mEpisodeTabSelectTv?.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        mEpisodeTabTrailerTv = mEpisodeTabTrailerLayout?.findViewById(R.id.tv_main_title)
        mEpisodeTabTrailerTv?.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        mEpisodeTabSeriesTv = mEpisodeTabSeriesLayout?.findViewById(R.id.tv_main_title)
        mEpisodeTabSeriesTv?.typeface = Typeface.defaultFromStyle(Typeface.BOLD)

    }

    fun setSelectedListener(onItemSelectedListener: OnItemSelectedListener) {
        this.onItemSelectedListener = onItemSelectedListener
    }

    fun initEpisodeTabItems(hasTrailer: Boolean, hasSeries: Boolean) {
        this.hasSeries = hasSeries
        this.hasTrailer = hasTrailer
        lastId = R.id.episode_tab_select
        mEpisodeTabSelectTv?.setTextColor(ContextCompat.getColor(context, R.color.tv_color_ff6247))
        mEpisodeTabTrailerTv?.setTextColor(ContextCompat.getColor(context, R.color.tv_color_e6e8e8ff))
        mEpisodeTabSeriesTv?.setTextColor(ContextCompat.getColor(context, R.color.tv_color_e6e8e8ff))
        mEpisodeTabSelectTv?.text = "选集"
        if (hasTrailer) {
            mEpisodeTabTrailerTv?.text = "花絮"
            mEpisodeTabTrailerLayout?.visibility = VISIBLE
        } else {
            mEpisodeTabTrailerLayout?.visibility = GONE
        }

        if (hasSeries) {
            mEpisodeTabSeriesTv?.text = "同系列"
            mEpisodeTabSeriesLayout?.visibility = VISIBLE
        } else {
            mEpisodeTabSeriesLayout?.visibility = GONE
        }
    }

    private fun setEpisodeTabsItem() {
        mEpisodeTabSelectTv!!.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.tv_color_e6e8e8ff
            )
        )
        mEpisodeTabTrailerTv!!.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.tv_color_e6e8e8ff
            )
        )
        mEpisodeTabSeriesTv!!.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.tv_color_e6e8e8ff
            )
        )
    }

    override fun onGlobalFocusChanged(oldFocus: View?, newFocus: View?) {
        if (oldFocus == null && newFocus == null) {
            AppLogger.v("oldFocus: " + "null" + ", newFocus : " + "null")
        } else if (oldFocus == null && newFocus != null) {
            AppLogger.v("oldFocus: null, newFocus : $newFocus")
        } else if (oldFocus != null && newFocus == null) {
            AppLogger.v("oldFocus: $oldFocus, newFocus : null")
        } else if (oldFocus != null && newFocus != null) {
            // 从tab出去
            if ((oldFocus.id == R.id.episode_tab_select || oldFocus.id == R.id.episode_tab_trailer || oldFocus.id == R.id.episode_tab_series)
                && newFocus.id != R.id.episode_tab_select && newFocus.id != R.id.episode_tab_trailer && newFocus.id != R.id.episode_tab_series
            ) {
                if (oldFocus.id == lastId) {
                    (oldFocus.findViewById<View>(R.id.tv_main_title) as TextView).setTextColor(
                        ContextCompat.getColor(
                            context, R.color.tv_color_ff6247
                        )
                    )
                }
            }

            //回到tab
            if (oldFocus.id != R.id.episode_tab_select && oldFocus.id != R.id.episode_tab_trailer && oldFocus.id != R.id.episode_tab_series
                && (newFocus.id == R.id.episode_tab_select || newFocus.id == R.id.episode_tab_trailer || newFocus.id == R.id.episode_tab_series)
            ) {
                //找到当前select的tab requestfocus
                setEpisodeTabsItem()
                findViewById<View>(lastId).requestFocus()
//                onItemSelectedListener?.selectedById(lastId)
            }

            //tab里面切换
            if ((oldFocus.id == R.id.episode_tab_select || oldFocus.id == R.id.episode_tab_trailer || oldFocus.id == R.id.episode_tab_series)
                && (newFocus.id == R.id.episode_tab_select || newFocus.id == R.id.episode_tab_trailer || newFocus.id == R.id.episode_tab_series)
            ) {
                //找到当前select的tab requestfocus
                setEpisodeTabsItem()
                if (lastId != newFocus.id) {
                    onItemSelectedListener?.selectedById(newFocus.id)
                    lastId = newFocus.id
                }
            }
            AppLogger.v("oldFocus: $oldFocus, newFocus : $newFocus")
        }
    }

    override fun onKey(view: View?, keyCode: Int, keyEvent: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && keyEvent?.action == KeyEvent.ACTION_DOWN) {
            when(view?.id) {
                R.id.episode_tab_series -> {
                        return true
                }
                R.id.episode_tab_trailer -> {
                    if (!hasSeries) {
                        return true
                    }
                }
                R.id.episode_tab_select -> {
                    if (!hasTrailer && !hasSeries) {
                        return true
                    }
                }
                else -> return false
            }
        }
        return false
    }

    fun selectTrailerTab(){
        if (lastId == R.id.episode_tab_trailer) return
        lastId = R.id.episode_tab_trailer
        findViewById<View>(R.id.episode_tab_trailer).requestFocus()
        onItemSelectedListener?.selectedById(R.id.episode_tab_trailer)
    }

}
