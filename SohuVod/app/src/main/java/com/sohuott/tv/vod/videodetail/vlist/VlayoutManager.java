package com.sohuott.tv.vod.videodetail.vlist;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.vlayout.VirtualLayoutManager;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

public class VlayoutManager extends VirtualLayoutManager {
    public VlayoutManager(@NonNull Context context) {
        super(context);
    }

    public VlayoutManager(@NonNull Context context, int orientation) {
        super(context, orientation);
    }

    public VlayoutManager(@NonNull Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    @Override
    public boolean requestChildRectangleOnScreen(@NonNull RecyclerView parent, @NonNull View child, @NonNull Rect rect, boolean immediate, boolean focusedChildVisible) {
        if (getOrientation() == RecyclerView.VERTICAL) {
            int parentTop = 0;
            int parentBottom = getHeight();
            int childTop = child.getTop() + rect.top;
            int childBottom = childTop + rect.bottom;
            LibDeprecatedLogger.d("position : " + getPosition(child) + " , getItemCount(): " + getItemCount());

            int offScreenBottom = Math.max(0, childBottom - parentBottom);
            int offScreenTop = Math.min(0, childTop - parentTop - 100);

            if (offScreenTop == 0) {
                if (getPosition(child) < 2) {
                    return true;
                } else if (getPosition(child) == 2) {
                    offScreenBottom = Math.max(0, childBottom - parentBottom);
                    if (offScreenBottom > 0) {
                        offScreenBottom = Math.max(0, childBottom - parentBottom - 80);
                    }
                } else {
                    offScreenBottom = Math.max(0, childBottom - parentBottom + 250);
                }
            }

            if (offScreenBottom == 0) {
                if (getPosition(child) == 0) {
                    offScreenTop = Math.min(0, childTop - parentTop);
                } else if (getPosition(child) == 2) {
                    offScreenTop = Math.min(0, childTop - parentTop - 160);
                }
            }
            LibDeprecatedLogger.d("getPosition(child):" + getPosition(child) + " , offScreenTop : " + offScreenTop + ", offScreenBottom : " + offScreenBottom);
            // Favor bringing the top into view over the bottom
            int dy = offScreenTop != 0 ? offScreenTop : offScreenBottom;

            if (dy != 0) {
                LibDeprecatedLogger.d("scrollby=" + dy);
                if (dy > -70 && dy < 10) {
                    return true;
                }

                //最后一行 往下滑动
                if (getPosition(child) == getItemCount() - 1 || getPosition(child) == getItemCount() - 2) {
                    if (offScreenTop == 0) {
                        if (dy > -160 && dy < 80) {
                            return true;
                        } else if (dy > 400) {
                            dy = 1000;
                        }
                    }
                }
                parent.smoothScrollBy(0, dy);

                return true;
            }
        }
        return false;
    }
}
