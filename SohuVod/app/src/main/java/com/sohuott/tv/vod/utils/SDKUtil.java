package com.sohuott.tv.vod.utils;

import android.content.Context;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohu.sofa.sofaplayer_java.SofaLibLoader;
import com.sohu.sofa.sofaplayer_java.SofaMediaPlayer;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.lib.utils.Util;

/**
 * 第三方调用非播放接口
 */
public class SDKUtil {
    public static boolean supportSofaPlayer = true;
    //    public static final String DNS_CONFIG_SERVER = "https://doh-ctrl.sohu.com/vmsapp/v0.1.0";
    public static final String DNS_CONFIG_SERVER = "https://doh-ctrl.vod.ystyt.aisee.tv/vmsapp/v0.1.0";

    public static void onApplicationCreate(Context context) {
        //初始化sofa播放器相关
//        int pid = Util.getPlatformId(context);
//        String gid = UserInfoHelper.getGid();
//
//        if (!SofaMediaPlayer.globalInitialize(context) ||
//                !SofaMediaPlayer.drmInitialize(gid, pid)) {
//            AppLogger.e("failed to initialize SofaMediaPlayer!!!");
//            supportSofaPlayer = false;
//        } else {
//            SofaMediaPlayer.setLogLevel(SofaMediaPlayer.SOFA_LOG_VERBOSE);
//            SofaMediaPlayer.toggleLog(AppLogger.getDebug() ? 1 : 0, AppLogger.getDebug()  ? 1 : 0);
//            AppLogger.i("sofa version:" + SofaMediaPlayer.getVersion());
//        }

    }

    public static void onApplicationCreate(Context context, String sofaLibPath) {
        //初始化sofa播放器相关
//        int pid = Util.getPlatformId(context);
//        String gid = UserInfoHelper.getGid();
//        if (!SofaMediaPlayer.globalInitialize(context, new SofaLibLoader() {
//            @Override
//            public void loadLibrary(String s) throws UnsatisfiedLinkError, SecurityException {
//                System.load(sofaLibPath);
//            }
//        }) || !SofaMediaPlayer.cronetInitialize(context, DNS_CONFIG_SERVER, DNS_CONFIG_SERVER) ||
//                !SofaMediaPlayer.drmInitialize(gid, pid)) {
//            AppLogger.e("failed to initialize SofaMediaPlayer!!!");
//            supportSofaPlayer = false;
//        } else {
//            SofaMediaPlayer.setLogLevel(SofaMediaPlayer.SOFA_LOG_VERBOSE);
//            SofaMediaPlayer.toggleLog(AppLogger.getDebug() ? 1 : 0, AppLogger.getDebug() ? 1 : 0);
//            AppLogger.i("sofa version:" + SofaMediaPlayer.getVersion());
//        }
    }

    public static void onApplicationDestroy() {
//        SdkLogger.i("SofaPlayer.isPlayerInitialized: " + SofaPlayer.isPlayerInitialized);
//        if (SofaPlayer.isPlayerInitialized) {
//            SdkLogger.i("destroy sofa...");
//            SofaPlayer.getInstance().destroy();
//        }
    }

}
