package com.sohuott.tv.vod;

import android.app.Activity;
import android.util.Log;

import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.ListRow;
import androidx.leanback.widget.Presenter;
import androidx.leanback.widget.PresenterSelector;
import androidx.leanback.widget.VerticalGridView;

import com.google.gson.Gson;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.model.Footer;
import com.sohuott.tv.vod.model.Header;
import com.sohuott.tv.vod.model.NewFilm;
import com.sohuott.tv.vod.model.VipBanner;
import com.sohuott.tv.vod.model.VipUserState;
import com.sohuott.tv.vod.presenter.launcher.TypeComingContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeFourContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeOneContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeProducerPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeRecommendContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeThreeContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeTwoContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeZeroContentPresenterKt;
import com.sohuott.tv.vod.presenter.launcher.selector.TypeOnePresenterSelector;
import com.sohuott.tv.vod.presenter.launcher.selector.TypeVipHeaderPresenterSelector;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/1/7 11:46 上午
 * @Version 1.0
 */
public class ListHelper {

    private int mCode;

    private VerticalGridView mVerticalGridView;

    private ArrayObjectAdapter mAdapter;

    private HashMap<String, String> mHistoryPathInfo = new HashMap<>();
    private List<ContentGroup.DataBean.ContentsBean.AlbumListBean> mRecommendList;

    private Activity mContext;

    private Boolean isTeenagerMode = false;
    private Boolean isShowFooterButton = true;


    public ListHelper(Activity context, int code, VerticalGridView verticalGridView, ArrayObjectAdapter adapter) {
        mCode = code;
        mVerticalGridView = verticalGridView;
        mAdapter = adapter;
        mContext = context;
    }

    public void setMode(Boolean isTeenagerMode) {
        this.isTeenagerMode = isTeenagerMode;
    }

    public void setIsShowFooterButton(Boolean isShowFooterButton) {
        this.isShowFooterButton = isShowFooterButton;
    }


    /**
     * 组装列表数据
     */
    public void assemblyListData(ContentGroup content) {
        List<ContentGroup.DataBean> dataBeans = content.data;
        if (dataBeans != null) {
            for (int i = 0; i < dataBeans.size(); i++) {
                ContentGroup.DataBean dataBean = dataBeans.get(i);
                if (dataBean.contents == null) {
                    continue;
                }
                for (int j = 0; j < dataBean.contents.size(); j++) {
                    ContentGroup.DataBean.ContentsBean contentsBean = dataBean.contents.get(j);
                    if (contentsBean.albumList == null &&
                            contentsBean.subjectVideoList == null &&
                            contentsBean.pgcVideoList == null &&
                            contentsBean.producersList == null) continue;
                    //非首屏数据
                    initPathLog(contentsBean);
                    addOtherScreenContent(contentsBean);
                    AppLogger.d("OtherScreenContent-> type: " + contentsBean.type);
                }
                //首屏数据
                initPathLog(dataBean);
                addFirstScreenContent(dataBean);
                AppLogger.d("FirstScreenContent-> type: " + dataBean.type);
            }
        }
        if (!isTeenagerMode) {
            addFooter();
        }
    }

    private void addOtherScreenContent(ContentGroup.DataBean.ContentsBean contentsBean) {
        List<ContentGroup.DataBean.ContentsBean.AlbumListBean> list = contentsBean.albumList;
        List<ContentGroup.DataBean.ContentsBean.AlbumListBean> subList;
        switch (contentsBean.type) {
            // 3大+6小
            case Constant.TYPE_36:
            case Constant.TYPE_30:
            case Constant.TYPE_31:
            case Constant.TYPE_34://推新
                // 剪裁 List
                if (list == null || list.size() < 9) {
                    return;
                }
                subList = list;
                if (list.size() > 3) {
                    subList = list.subList(0, 3);
                }
                addWithTryCatch(createListRow(subList, new TypeTwoContentPresenter(), contentsBean.name));

                if (list.size() > 9) {
                    subList = list.subList(3, 9);
                } else {
                    subList = list.subList(3, list.size());
                }
                addWithTryCatch(createListRow(subList, new TypeThreeContentPresenter(), ""));
                break;
            case Constant.TYPE_40:
                if (contentsBean.producersList == null || contentsBean.producersList.size() < 6) {
                    return;
                }

                addWithTryCatch(createProducersListRow(contentsBean.producersList, new TypeProducerPresenter(), contentsBean.name));
                break;
            case Constant.TYPE_41:
                if (contentsBean.pgcVideoList == null) {
                    return;
                }

                if (contentsBean.pgcVideoList.size() < 4) {
                    return;
                } else if (contentsBean.pgcVideoList.size() >= 4 && contentsBean.pgcVideoList.size() < 8) {
                    addWithTryCatch(createPgcListRow(contentsBean.pgcVideoList.subList(0, 4), new TypeOneContentPresenter(), contentsBean.name));
                } else if (contentsBean.pgcVideoList.size() >= 8) {
                    addWithTryCatch(createPgcListRow(contentsBean.pgcVideoList.subList(0, 4), new TypeOneContentPresenter(), contentsBean.name));
                    addWithTryCatch(createPgcListRow(contentsBean.pgcVideoList.subList(4, contentsBean.pgcVideoList.size()), new TypeOneContentPresenter(), ""));
                }


                break;
            case Constant.TYPE_32: // 即将上线
                // 裁剪list 大于6 舍弃
                if (contentsBean.subjectVideoList == null || contentsBean.subjectVideoList.size() < 6) {
                    return;
                }

                if (contentsBean.subjectVideoList.size() > 6) {
                    contentsBean.subjectVideoList = contentsBean.subjectVideoList.subList(0, 6);
                }

                // 创建list row
                addWithTryCatch(createComingSoonListRow(contentsBean.subjectVideoList, new TypeComingContentPresenter(), contentsBean.name));
                break;
            case Constant.TYPE_39: // 6小图
                // 裁剪list 大于6 舍弃
                if (list == null || list.size() < 6) {
                    return;
                }


                subList = list;
                subList.addAll(0, list);
                if (list.size() > 6) {
                    subList = list.subList(0, 6);
                }

                // 创建list row
                addWithTryCatch(createListRow(subList, new TypeThreeContentPresenter(), contentsBean.name));
                break;
            case Constant.TYPE_38://为你推荐

                for (int i = 0; i < contentsBean.albumList.size() / 4; i++) {
                    String name = "";
                    if (i == 0) {
                        name = contentsBean.name;
                    }
                    addWithTryCatch(createListRow(list.subList(i * 4, i * 4 + 4), new TypeRecommendContentPresenter(), name));
                }
                break;
            case Constant.TYPE_33://推新
            case Constant.TYPE_35:
                if (list == null || list.size() < 4) {
                    return;
                }
                subList = list;
                if (list.size() > 4) {
                    subList = list.subList(0, 4);
                }
                addWithTryCatch(createListRow(subList, new TypeOneContentPresenter(), contentsBean.name));

                if (list.size() > 7) {
                    subList = list.subList(4, 8);
                    addWithTryCatch(createListRow(subList, new TypeOneContentPresenter(), ""));
                }

                break;
            case Constant.TYPE_37:
                //1 + 3 -> 历史 + 推荐
                if (list == null) {
                    return;
                }
                subList = list;
                if (list.size() > 3) {
                    subList = list.subList(0, 3);
                }
                addWithTryCatch(1, createHistoryListRow(subList, contentsBean.id, new TypeOnePresenterSelector()));
                break;
        }
    }

    private void initPathLog(ContentGroup.DataBean dataBean) {
        String type = dataBean.type;
        if (dataBean.contents != null && dataBean.contents.size() > 0) {
            for (int i = 0; i < dataBean.contents.size(); i++) {
                if (dataBean.contents.get(i) == null) return;
                ContentGroup.DataBean.ContentsBean contentsBean = dataBean.contents.get(i);
                Log.d("HomeContentFragment", "First Page InitPathLog: mCurrentTabCode ->" + mCode + ", contentsBean.id -> " + dataBean.id + ",i -> " + i);


                Gson gson = new Gson();
                HomeRecommendBean.Data.Content.Parameter parameter = gson.fromJson(contentsBean.parameter,
                        HomeRecommendBean.Data.Content.Parameter.class);
                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCode));
                pathInfo.put("columnId", StringUtil.toString(dataBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                contentsBean.pathInfo = pathInfo;

                if (type.equals(Constant.TYPE_6) ||
                        type.equals(Constant.TYPE_9)) {
                    if (contentsBean.ottCategoryId != null) {
                        HashMap<String, String> memoInfo = new HashMap<>();
                        memoInfo.put("collectionId", contentsBean.ottCategoryId);
                        contentsBean.memoInfo = memoInfo;
                    }
                }

                if (parameter == null) continue;

                HashMap<String, String> objectInfo = new HashMap<String, String>();
                objectInfo.put("type", "视频");
                objectInfo.put("vid", parameter.tvVerId);
                objectInfo.put("playlistid", parameter.albumId);

                contentsBean.objectInfo = objectInfo;

                contentsBean.channelType = mCode;
            }
        }
    }

    private void initPathLog(ContentGroup.DataBean.ContentsBean contentsBean) {
        if (contentsBean.albumList != null) {
            for (int i = 0; i < contentsBean.albumList.size(); i++) {
                if (contentsBean.albumList.get(i) == null) return;
                ContentGroup.DataBean.ContentsBean.AlbumListBean albumListBean = contentsBean.albumList.get(i);
                Log.d("HomeContentFragment", "initPathLog: mCurrentTabCode ->" + mCode + ", contentsBean.id -> " + contentsBean.id + ",i -> " + i);

                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCode));
                pathInfo.put("columnId", StringUtil.toString(contentsBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                albumListBean.pathInfo = pathInfo;

                HashMap<String, String> objectInfo = new HashMap<String, String>();
                objectInfo.put("type", "视频");
                objectInfo.put("vid", StringUtil.toString(albumListBean.tvVerId));
                objectInfo.put("playlistid", StringUtil.toString(albumListBean.id));

                albumListBean.objectInfo = objectInfo;


                if (!StringUtil.isEmpty(albumListBean.pdna)) {
                    HashMap<String, String> memoInfo = new HashMap<String, String>();
                    memoInfo.put("pdna", albumListBean.pdna);
                    albumListBean.memoInfo = memoInfo;
                }

                albumListBean.channelType = mCode;
            }
        } else if (contentsBean.producersList != null) {
            for (int i = 0; i < contentsBean.producersList.size(); i++) {
                if (contentsBean.producersList.get(i) == null) return;
                ContentGroup.DataBean.ContentsBean.ProducersListBean producerBean = contentsBean.producersList.get(i);

                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCode));
                pathInfo.put("columnId", StringUtil.toString(contentsBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                producerBean.pathInfo = pathInfo;

                HashMap<String, String> objectInfo = new HashMap<String, String>();
                objectInfo.put("type", "user");
                objectInfo.put("id", StringUtil.toString(producerBean.uid));

                producerBean.objectInfo = objectInfo;
            }
        } else if (contentsBean.subjectVideoList != null) {
            for (int i = 0; i < contentsBean.subjectVideoList.size(); i++) {
                if (contentsBean.subjectVideoList.get(i) == null) return;
                ContentGroup.DataBean.ContentsBean.SubjectVideoListBean subjectBean = contentsBean.subjectVideoList.get(i);
                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCode));
                pathInfo.put("columnId", StringUtil.toString(contentsBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                subjectBean.pathInfo = pathInfo;

                Gson gson = new Gson();
                ContentGroup.DataBean.ContentsBean.AlbumListBean albumListBean =
                        gson.fromJson(subjectBean.parameter,
                                ContentGroup.DataBean.ContentsBean.AlbumListBean.class);

                HashMap<String, String> memoInfo = new HashMap<String, String>();
                memoInfo.put("playlistId", StringUtil.toString(albumListBean.albumId));
                memoInfo.put("vid", StringUtil.toString(albumListBean.tvVerId));
                memoInfo.put("catecode", StringUtil.toString(albumListBean.cateCode));

                subjectBean.memoInfo = memoInfo;
            }
        } else if (contentsBean.pgcVideoList != null) {
            for (int i = 0; i < contentsBean.pgcVideoList.size(); i++) {
                if (contentsBean.pgcVideoList.get(i) == null) return;
                PgcAlbumInfo.DataEntity dataEntity = contentsBean.pgcVideoList.get(i);

                HashMap<String, String> pathInfo = new HashMap<>();
                pathInfo.put("pageId", StringUtil.toString(mCode));
                pathInfo.put("columnId", StringUtil.toString(contentsBean.id));
                pathInfo.put("index", StringUtil.toString(i + 1));

                dataEntity.pathInfo = pathInfo;

                HashMap<String, String> objectInfo = new HashMap<String, String>();
                objectInfo.put("playlistId", StringUtil.toString(dataEntity.playListId));
                objectInfo.put("vid", StringUtil.toString(dataEntity.videoId));
                objectInfo.put("type", "视频");

                dataEntity.objectInfo = objectInfo;
            }
        }
    }

    private void addFooter() {
        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", StringUtil.toString(mCode));
        Footer footer = new Footer(pathInfo, mCode, isShowFooterButton);
        addWithTryCatch(footer);
    }

    private void addWithTryCatch(Object item) {
        try {
            if (!mVerticalGridView.isComputingLayout()) {
                mAdapter.add(item);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addWithTryCatch(int index, Object item) {
        try {
            if (!mVerticalGridView.isComputingLayout()) {
                mAdapter.add(index, item);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void addFirstScreenContent(ContentGroup.DataBean dataBean) {
        initPathLog(dataBean);
        List<ContentGroup.DataBean.ContentsBean> list = dataBean.contents;
        List<ContentGroup.DataBean.ContentsBean> subList;
        switch (dataBean.type) {
            case Constant.TYPE_4://推新
                if (list == null || list.size() < 1) {
                    return;
                }
                if (list.size() > 1) {
                    subList = list.subList(0, 1);
                } else {
                    subList = list;
                }
                addNewFilm(subList);
                break;
            case Constant.TYPE_7://pgc
            case Constant.TYPE_2:
                // 裁剪 list
                if (list == null || list.size() < 2) {
                    return;
                }
                if (list.size() > 2) {
                    subList = list.subList(0, 2);
                } else {
                    subList = list;
                }
                addWithTryCatch(createFirstPageListRow(subList, new TypeZeroContentPresenterKt()));
                break;
            case Constant.TYPE_5://推新
            case Constant.TYPE_8://pgc
            case Constant.TYPE_3://4个横图
                // 裁剪 list
                if (list == null || list.size() < 4) {
                    return;
                }
                if (list.size() > 4) {
                    subList = list.subList(0, 4);
                } else {
                    subList = list;
                }

                addWithTryCatch(createFirstPageListRow(subList, new TypeOneContentPresenter()));
                break;
            case Constant.TYPE_9://pgc
            case Constant.TYPE_6://6个分类
                // 裁剪 list
                if (list == null || list.size() < 6) {
                    return;
                }
                if (list.size() > 6) {
                    subList = list.subList(0, 6);
                } else {
                    subList = list;
                }

                addWithTryCatch(createFirstPageListRow(subList, new TypeFourContentPresenter()));
                break;
            case Constant.TYPE_10://vip4图轮播
                // 裁剪 list
                if (list == null) {
                    return;
                }
                if (list.size() > 4) {
                    subList = list.subList(0, 4);
                } else {
                    subList = list;
                }
                addVipHeader(subList);
                break;
        }
    }

    private ListRow createListRow(List<ContentGroup.DataBean.ContentsBean.AlbumListBean> list, Presenter presenter, String title) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        ListRow listRow;
        if (!title.equals("")) {
            addHeader(title);
        }
        listRow = new ListRow(arrayObjectAdapter);
        return listRow;
    }

    private ListRow createProducersListRow(List<ContentGroup.DataBean.ContentsBean.ProducersListBean> list, Presenter presenter, String title) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        ListRow listRow;
        if (!title.equals("")) {
            addHeader(title);
        }
        listRow = new ListRow(arrayObjectAdapter);
        return listRow;
    }

    private ListRow createPgcListRow(List<PgcAlbumInfo.DataEntity> list, Presenter presenter, String title) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        ListRow listRow;
        if (!title.equals("")) {
            addHeader(title);
        }
        listRow = new ListRow(arrayObjectAdapter);
        return listRow;
    }

    private ListRow createComingSoonListRow(List<ContentGroup.DataBean.ContentsBean.SubjectVideoListBean> list, Presenter presenter, String title) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        ListRow listRow;
        if (!title.equals("")) {
            addHeader(title);
        }
        listRow = new ListRow(arrayObjectAdapter);
        return listRow;

    }

    private ListRow createFirstPageListRow(List<ContentGroup.DataBean.ContentsBean> list, Presenter presenter) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(presenter);
        arrayObjectAdapter.addAll(0, list);
        return new ListRow(arrayObjectAdapter);
    }

    private ListRow createHistoryListRow(List<ContentGroup.DataBean.ContentsBean.AlbumListBean> list, int columnId, PresenterSelector selector) {

        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(selector);

        mRecommendList = list;

        mHistoryPathInfo.put("pageId", StringUtil.toString(mCode));
        mHistoryPathInfo.put("columnId", StringUtil.toString(columnId));

        arrayObjectAdapter.add(new PlayHistory(mHistoryPathInfo));
        arrayObjectAdapter.addAll(1, list);

        return new ListRow(arrayObjectAdapter);
    }

    private void addNewFilm(List<ContentGroup.DataBean.ContentsBean> list) {
        if (list.size() > 0 && !StringUtil.isEmpty(list.get(0).picUrl3)) {
            if (mContext != null) {
                if (mContext instanceof LauncherActivity) {
                    ((LauncherActivity) mContext).setBackground(0, list.get(0).picUrl3);
                }
            }
        }
        addWithTryCatch(new NewFilm(list));
    }

    private void addVipHeader(List<ContentGroup.DataBean.ContentsBean> list) {
        ArrayObjectAdapter arrayObjectAdapter = new ArrayObjectAdapter(new TypeVipHeaderPresenterSelector());

        arrayObjectAdapter.add(new VipUserState(list.get(0).pathInfo));
        arrayObjectAdapter.add(new VipBanner(list));
        ListRow listRow = new ListRow(arrayObjectAdapter);
        addWithTryCatch(listRow);
    }


    private void addHeader(String title) {
        addWithTryCatch(new Header(title, mCode));
    }


}
