package com.sohuott.tv.vod.view;


import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.lib.model.ProducerIntro.DataEntity.AlbumsEntity;
import com.sohuott.tv.vod.lib.model.ProducerVideoList.DataEntity.ResultEntity.VideoDetails;

import java.util.List;

/**
 * Created by XiyingCao on 16-1-7.
 */
public interface ListProducerView {

    void add(List<VideoDetails> models);

    void showLoading();

    void hideLoading();

    void onError();

    void showListLoading();

    void hideListLoading();

    void onListError();

    void activateLastItemViewListener();

    void disableLastItemViewListener();

    void setProducerIcon(String icon);

    void setProducerName(String name);

    void setProducerFanCount(int fanCount);

    void setProducerPlayCount(long playCount);

    void setProducerIntro(String intro);

    void showAlbumList(List<AlbumsEntity> videoDetailsList);

    void onSideListError();

    void setCount(int count);

    void updateCountText(int mSelctedPos);

    RecyclerView.Adapter getAdapter();

    void refreshRightVideoList(int dataIndex, int viewIndex);
}
