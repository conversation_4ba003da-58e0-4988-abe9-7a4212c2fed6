package com.sohuott.tv.vod.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.adapter.HomeAllCategoryAdapter;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.presenter.HomeFragmentPresenterImpl;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.HomeBaseRecyclerView;
import com.sohuott.tv.vod.view.IHomeFragmentView;

import java.lang.ref.WeakReference;

/**
 * Created by yizhang210244 on 2018/1/3.
 */

public class HomeAllCategoryFragmentNew extends HomeWithRecyclerViewFragment implements IHomeFragmentView {
    private static final String TAG = HomeAllCategoryFragmentNew.class.getSimpleName();
    private FocusBorderView mFocusBorderView;
    private GridLayoutManager mGridLayoutManager;
    private HomeAllCategoryAdapter mHomeAllCategoryAdapter;
    private HomeFragmentPresenterImpl mHomeFragmentPresenter;

    private static final int DISPLAY_IMAGE = 100;
    private static final int RECYCLE_IMAGE = 101;

    private class MyHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            int what = msg.what;
            switch (what) {
                case DISPLAY_IMAGE:
                    displayImage();
                    break;
                default:
                    break;
            }
        }
    }

    private MyHandler mMyHandler = new MyHandler();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppLogger.d(TAG, "onCreate");
        setSubPageName("6_home_" + mChannelId);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        AppLogger.d(TAG, "onCreateView");
        mRootView = inflater.inflate(R.layout.fragment_home_all_category, container, false);
        mFocusBorderView = (FocusBorderView) mRootView.findViewById(R.id.fragment_item_focus);
        mRecyclerView = (HomeBaseRecyclerView) mRootView.findViewById(R.id.recyclerview);
        mRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mRecyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent,
                                       RecyclerView.State state) {
                outRect.bottom = (int) view.getContext().getResources().getDimension(R.dimen.y12);
                outRect.top = (int) view.getContext().getResources().getDimension(R.dimen.y12);
                outRect.left = (int) view.getContext().getResources().getDimension(R.dimen.x12);
                outRect.right = (int) view.getContext().getResources().getDimension(R.dimen.x12);
            }
        });
        mGridLayoutManager = new GridLayoutManager(getContext(), 6, LinearLayoutManager.VERTICAL,
                false);


        mRecyclerView.setHasFixedSize(true);
        mRecyclerView.setLayoutManager(mGridLayoutManager);
        mRecyclerView.setCallBackListener(new HomeBaseRecyclerView.CallBackListener() {
            @Override
            public void onBackKeyDown() {
                backToTop();
            }
        });
        mHomeFragmentPresenter = new HomeFragmentPresenterImpl(this, mChannelId);

        return mRootView;
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        AppLogger.d(TAG, "onViewCreated mHomeFragmentPresenter.getData()");
        mHomeFragmentPresenter.getData();
    }

    @Override
    public void onResume() {
        super.onResume();
        AppLogger.d(TAG, "onResume");
        if (mErrorTV != null && mErrorTV.getVisibility() == View.VISIBLE) {
            mHomeFragmentPresenter.getData();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mHomeFragmentPresenter != null) {
            mHomeFragmentPresenter.detachView();
        }
    }

    private void backToTop() {
        AppLogger.d(TAG, "backToTop");
        if (getActivity() != null && getActivity() instanceof LauncherActivity) {
            View mTabView = ((LauncherActivity) getActivity()).getHorizontalGridView();
            View mTopViewBar = ((LauncherActivity) getActivity()).getTopViewBar();
            if (mTabView != null) {
                if (mTabView != null && mTabView.getVisibility() != View.VISIBLE) {
                    mTabView.setVisibility(View.VISIBLE);
                }
                if (mTopViewBar != null && mTopViewBar.getVisibility() != View.VISIBLE) {
                    mTopViewBar.setVisibility(View.VISIBLE);
                }
                mTabView.requestFocus();
            }
            mRecyclerView.scrollToPosition(0);
        }
    }

    private void setUI(HomeRecommendBean homeRecommend) {
        AppLogger.d(TAG, "setUI homeRecommend ? " + homeRecommend);
        if (getActivity() == null) {
            return;
        }
        hideLoadingView();
        if (homeRecommend == null || homeRecommend.getStatus() != 0
                || homeRecommend.getData() == null || homeRecommend.getData().size() != 1
                || homeRecommend.getData().get(0) == null) {
            mRecyclerView.setVisibility(View.GONE);
            showErrorTV();
            return;
        }
        isUiWithData = true;
        mHomeAllCategoryAdapter = new HomeAllCategoryAdapter(getActivity(), mRecyclerView);
        mHomeAllCategoryAdapter.setFocusBorderView(mFocusBorderView);
        mHomeAllCategoryAdapter.setChannelId(mChannelId);
        mHomeAllCategoryAdapter.setContentList(homeRecommend.getData().get(0).getContents());
        mHomeAllCategoryAdapter.setHasStableIds(true);
        if (getUserVisibleHint()) {
            mHomeAllCategoryAdapter.setAlbumEnable(true);
        } else {
            mHomeAllCategoryAdapter.setAlbumEnable(false);
        }
        final int itemCount = mHomeAllCategoryAdapter.getItemCount();
        mGridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                AppLogger.d(TAG, "position ? " + position);
                AppLogger.d(TAG, "itemCount ? " + itemCount);
                if (position == itemCount - 1) {
                    return 6;
                } else if (position < 6) {
                    return 2;
                } else {
                    return 1;
                }
//                return 6;
//                if (position == mRecyclerView.getChildCount() - 1) {
//                    return 6;
//                }
//                else
//                if (position < 6){
//                    return 2;
//                } else {
//                    return 1;
//                }

            }
        });
        mRecyclerView.setAdapter(mHomeAllCategoryAdapter);
    }

    @Override
    protected void onVisible() {
        super.onVisible();
        AppLogger.d(TAG, "onVisible");
        if (mHomeAllCategoryAdapter != null) {
            mHomeAllCategoryAdapter.onVisible();
        }
        if (mRecyclerView != null) {
            mRecyclerView.scrollToPosition(0);
        }
    }

    @Override
    protected void onInvisible() {
        super.onInvisible();
        AppLogger.d(TAG, "onInVisible");
        if (mHomeAllCategoryAdapter != null) {
            mHomeAllCategoryAdapter.onInVisible();
        }
    }

    @Override
    public void scrollToLeftStart() {
        if (mRecyclerView != null) {
            mRecyclerView.scrollToPosition(0);
        }
    }

    @Override
    public void scrollToRightEnd() {
        if (mRecyclerView != null) {
            RecyclerView.Adapter adapter = mRecyclerView.getAdapter();
            if (adapter != null && adapter.getItemCount() > 0) {
                mRecyclerView.scrollToPosition(adapter.getItemCount() - 1);
            }
        }
    }

    @Override
    public void leftStartViewFocus() {
        if (mRecyclerView != null && mRecyclerView.getAdapter() != null
                && mRecyclerView.getAdapter().getItemCount() > 0) {
            RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(0);
            if (viewHolder != null) {
                viewHolder.itemView.requestFocus();
            }
        }
    }

    @Override
    public void rightEndViewFocus() {
        if (mRecyclerView != null) {
            getRightRequestFocus();
        }
    }

    private void getRightRequestFocus() {
        if (mRecyclerView != null && mRecyclerView.getAdapter() != null
                && mRecyclerView.getAdapter().getItemCount() > 0) {
            int pos;
            if (getIsFocusUpPosition()) {
                pos = mRecyclerView.getAdapter().getItemCount() - 2;
            } else {
                pos = mRecyclerView.getAdapter().getItemCount() - 1;
            }
            RecyclerView.ViewHolder viewHolder =
                    mRecyclerView.findViewHolderForAdapterPosition(pos);
            if (viewHolder != null) {
                viewHolder.itemView.requestFocus();
            }
        }
    }


    @Override
    public void attachImages() {
        super.attachImages();
        mMyHandler.removeMessages(DISPLAY_IMAGE);
        mMyHandler.sendEmptyMessageDelayed(DISPLAY_IMAGE, 500);
//        displayImage();
    }

    private void displayImage() {
        if (mRecyclerView != null && mHomeAllCategoryAdapter != null) {
            mRecyclerView.post(new InnerRunnable(this));
        }
    }
    private static class InnerRunnable implements Runnable {
        private WeakReference<HomeAllCategoryFragmentNew> mWrapper;
        InnerRunnable(HomeAllCategoryFragmentNew homeAllCategoryFragmentNew){
            mWrapper = new WeakReference<>(homeAllCategoryFragmentNew);
        }
        @Override
        public void run() {
            HomeAllCategoryFragmentNew homeAllCategoryFragmentNew = mWrapper.get();
            if (homeAllCategoryFragmentNew != null){
                homeAllCategoryFragmentNew.mHomeAllCategoryAdapter.setAlbumEnable(true);
                homeAllCategoryFragmentNew.mHomeAllCategoryAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void horScroll(KeyEvent event) {
        super.horScroll(event);
    }

    @Override
    public void detachImages() {
        super.detachImages();
        mMyHandler.removeCallbacksAndMessages(null);
        if (mRecyclerView != null && mHomeAllCategoryAdapter != null && mHomeAllCategoryAdapter.isAlbumEnable()) {
            mHomeAllCategoryAdapter.setAlbumEnable(false);
            mHomeAllCategoryAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public View getDefaultUpFocusView() {
        if (mRecyclerView != null && mRecyclerView.getAdapter() != null
                && mRecyclerView.getAdapter().getItemCount() > 0) {
            RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(0);
            if (viewHolder != null) {
                return viewHolder.itemView;
            }
        }
        return null;
    }

    @Override
    public HomeBaseRecyclerView getRecyclerView() {
        return mRecyclerView;
    }

    @Override
    public boolean getIsFocusUpPosition() {
        if (mHomeAllCategoryAdapter != null) {
            return mHomeAllCategoryAdapter.isFocusUpPosition();
        }
        return false;
    }

    @Override
    public void setIsFocusUpPosition(boolean isFocusUpPosition) {
        if (mHomeAllCategoryAdapter != null) {
            mHomeAllCategoryAdapter.setFocusUpPosition(isFocusUpPosition);
        }
    }

    @Override
    public void getDataSuccess(HomeRecommendBean value) {
        AppLogger.d(TAG, "getDataSuccess");
        setUI(value);
    }

    @Override
    public void getDataError(Throwable e) {
        setUI(null);
    }

    @Override
    public void refreshDataSuccess(HomeRecommendBean value) {

    }

    @Override
    public void refreshDataError(Throwable e) {

    }

    @Override
    public void reloadData() {
        super.reloadData();
        if (!isUiWithData) {
            if (mHomeFragmentPresenter != null) {
                mHomeFragmentPresenter.getData();
            }
        } else {
            attachImages();
        }
    }
}
