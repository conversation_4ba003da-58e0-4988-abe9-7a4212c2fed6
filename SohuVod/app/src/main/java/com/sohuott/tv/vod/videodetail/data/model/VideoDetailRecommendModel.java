package com.sohuott.tv.vod.videodetail.data.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by XiyingCao on 2018/3/21.
 */

public class VideoDetailRecommendModel implements Serializable {
    private String message;
    private int status;
    private List<DataBean> data;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean implements Serializable {
        private int cateCode;
        private int secondCateCode;
        private String name;
        private String name_new;
        private int order;
        //1、4个专辑   2、4+4个专辑   3、3个专辑   4、6个专辑    5、6+6个专辑    6、3个标签    7、影人
        private int layerType;
        private int type; // 1相关推荐，2标签影片，3影人，4标签，5二级分类影片
        private List<ContentsBean> contents;

        public int getCateCode() {
            return cateCode;
        }

        public void setCateCode(int cateCode) {
            this.cateCode = cateCode;
        }

        public int getSecondCateCode() {
            return secondCateCode;
        }

        public void setSecondCateCode(int secondCateCode) {
            this.secondCateCode = secondCateCode;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getOrder() {
            return order;
        }

        public void setOrder(int order) {
            this.order = order;
        }

        public int getLayoutType() {
            return layerType;
        }

        public void setLayoutType(int layerType) {
            this.layerType = layerType;
        }

        public String getName_new() {
            return name_new;
        }

        public void setName_new(String name_new) {
            this.name_new = name_new;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public List<ContentsBean> getContents() {
            return contents;
        }

        public void setContents(List<ContentsBean> contents) {
            this.contents = contents;
        }

        public static class ContentsBean implements Serializable {
            private String fanCount;
            private AlbumParamBean albumParam;
            private int cateCode;
            private String horPic;
            private String cornerType;
            private int id;
            private String likeCount;
            private String name;
            private int order;
            private String role;
            private int starType;// 1演员，2导演
            private int type;// 1专辑，2影人，3标签
            private String verPic;
            private String pdna;
            private int index;
            private String tagName;

            public String getFanCount() {
                return fanCount;
            }

            public void setFanCount(String fanCount) {
                this.fanCount = fanCount;
            }


            public AlbumParamBean getAlbumParam() {
                return albumParam;
            }

            public void setAlbumParam(AlbumParamBean albumParam) {
                this.albumParam = albumParam;
            }

            public int getCateCode() {
                return cateCode;
            }

            public void setCateCode(int cateCode) {
                this.cateCode = cateCode;
            }

            public String getHorPic() {
                return horPic;
            }

            public void setHorPic(String horPic) {
                this.horPic = horPic;
            }

            public String getCornerType() {
                return cornerType;
            }

            public void setCornerType(String cornerType) {
                this.cornerType = cornerType;
            }

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }

            public String getLikeCount() {
                return likeCount;
            }

            public void setLikeCount(String likeCount) {
                this.likeCount = likeCount;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public int getOrder() {
                return order;
            }

            public void setOrder(int order) {
                this.order = order;
            }

            public String getRole() {
                return role;
            }

            public void setRole(String role) {
                this.role = role;
            }

            public int getStarType() {
                return starType;
            }

            public void setStarType(int starType) {
                this.starType = starType;
            }

            public int getType() {
                return type;
            }

            public void setType(int type) {
                this.type = type;
            }

            public String getVerPic() {
                return verPic;
            }

            public void setVerPic(String verPic) {
                this.verPic = verPic;
            }

            public String getPdna() {
                return pdna;
            }

            public void setPdna(String pdna) {
                this.pdna = pdna;
            }

            public int getIndex() {
                return index;
            }

            public void setIndex(int index) {
                this.index = index;
            }

            public String getTagName() {
                return tagName;
            }

            public void setTagName(String tagName) {
                this.tagName = tagName;
            }

            public static class AlbumParamBean implements Serializable {
                private int paySeparate;
                private String tvIsFee;
                private String doubanScore;
                private String score;
                private String cornerType;
                private String showDate;
                private String ottFee;
                private String scoreSource;
                private int tvIsEarly;
                private String comment;
                private String tvSets;
                private int useTicket;
                private String latestVideoCount;
                private String tType;
                private String cateCode;

                public int getPaySeparate() {
                    return paySeparate;
                }

                public void setPaySeparate(int paySeparate) {
                    this.paySeparate = paySeparate;
                }

                public String getTvIsFee() {
                    return tvIsFee;
                }

                public void setTvIsFee(String tvIsFee) {
                    this.tvIsFee = tvIsFee;
                }

                public String getDoubanScore() {
                    return doubanScore;
                }

                public void setDoubanScore(String doubanScore) {
                    this.doubanScore = doubanScore;
                }

                public String getScore() {
                    return score;
                }

                public void setScore(String score) {
                    this.score = score;
                }

                public String getCornerType() {
                    return cornerType;
                }

                public void setCornerType(String cornerType) {
                    this.cornerType = cornerType;
                }

                public String getShowDate() {
                    return showDate;
                }

                public void setShowDate(String showDate) {
                    this.showDate = showDate;
                }

                public String getOttFee() {
                    return ottFee;
                }

                public void setOttFee(String ottFee) {
                    this.ottFee = ottFee;
                }

                public String getScoreSource() {
                    return scoreSource;
                }

                public void setScoreSource(String scoreSource) {
                    this.scoreSource = scoreSource;
                }

                public int getTvIsEarly() {
                    return tvIsEarly;
                }

                public void setTvIsEarly(int tvIsEarly) {
                    this.tvIsEarly = tvIsEarly;
                }

                public String getComment() {
                    return comment;
                }

                public void setComment(String comment) {
                    this.comment = comment;
                }

                public String getTvSets() {
                    return tvSets;
                }

                public void setTvSets(String tvSets) {
                    this.tvSets = tvSets;
                }

                public int getUseTicket() {
                    return useTicket;
                }

                public void setUseTicket(int useTicket) {
                    this.useTicket = useTicket;
                }

                public String getLatestVideoCount() {
                    return latestVideoCount;
                }

                public void setLatestVideoCount(String latestVideoCount) {
                    this.latestVideoCount = latestVideoCount;
                }

                public String getTType() {
                    return tType;
                }

                public void setTType(String tType) {
                    this.tType = tType;
                }

                public String getCateCode() {
                    return cateCode;
                }

                public void setCateCode(String cateCode) {
                    this.cateCode = cateCode;
                }
            }
        }
    }
}
