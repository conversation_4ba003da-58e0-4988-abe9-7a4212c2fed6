package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.util.TypedValue
import android.widget.TextView
import com.sh.ott.video.base.component.ShPlayerConstants
import com.sh.ott.video.component.BaseAdControlComponent
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.widget.PlayerLoadingView

/**
 * 准备播放提示
 */
open class BaseVideoStartPreparingComponent constructor(context: Context) :
    BaseControlComponent(context), BaseAdControlComponent {
    private var mIncludeScalePreparingLoadingContent: TextView? = null

    private var mScreenMode = PlayerConstants.ScreenMode.NORMAL

    var mPlayerLoadingView: PlayerLoadingView? = null

    init {
        val view = layoutInflater.inflate(R.layout.layout_sacle_play_preparing_view, this, true)
        mIncludeScalePreparingLoadingContent =
            findViewById(R.id.tv_scale_play_preparing_loading_content)
        mPlayerLoadingView = findViewById(R.id.scale_play_preparing_loading)
        setShowPreparingTextSize()
    }

    var isInitShowState = true
    var adType: Int = -1


    override fun onAdRequestType(type: Int) {
        adType = type
        if (adType == ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_PAUSE) {
           hide()
        }
    }

    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        super.onPlayStateChanged(playState, extras)
        if (adType == ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_PAUSE) {
            hide()
        } else {
            when (playState) {
                PlayerConstants.VideoState.PLAYBACK_COMPLETED,
                PlayerConstants.VideoState.STOPPED,
                PlayerConstants.VideoState.ERROR,
                PlayerConstants.VideoState.PAUSED,
                PlayerConstants.VideoState.PLAYING_BACK,
                PlayerConstants.VideoState.PLAYING -> {
                   hide()
                }

//                PlayerConstants.VideoState.BUFFERING -> {
//                    if (isInitShowState) return
//                    mPlayerLoadingView?.visibility = GONE
//                    gone()
//                }
////
//                PlayerConstants.VideoState.BUFFERED -> {
//                    isInitShowState = false
//                    mPlayerLoadingView?.visibility = GONE
//                    gone()
//                }

                PlayerConstants.VideoState.IDLE,
                -> {
                   show()
                }
            }

        }
    }

    override fun onScreenModeChanged(screenMode: Int) {
        super.onScreenModeChanged(screenMode)
        mScreenMode = screenMode
        setShowPreparingTextSize()
    }

    private fun setShowPreparingTextSize() {
        if (mScreenMode == PlayerConstants.ScreenMode.FULL) {
//            mHintText.setTextSize(TypedValue.COMPLEX_UNIT_PX, resources.getDimensionPixelSize(R.dimen.y40).toFloat())
            mIncludeScalePreparingLoadingContent?.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                resources.getDimensionPixelSize(R.dimen.y40).toFloat()
            )
        } else if (mScreenMode == PlayerConstants.ScreenMode.TINY) {
//            mHintText.setTextSize(TypedValue.COMPLEX_UNIT_PX, resources.getDimensionPixelSize(R.dimen.y26).toFloat())
            mIncludeScalePreparingLoadingContent?.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                resources.getDimensionPixelSize(R.dimen.y26).toFloat()
            )
        } else {
//            mHintText.setTextSize(TypedValue.COMPLEX_UNIT_PX, resources.getDimensionPixelSize(com.sohu.tvsdk.player.R.dimen.y32).toFloat())
            mIncludeScalePreparingLoadingContent?.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                resources.getDimensionPixelSize(R.dimen.y32).toFloat()
            )
        }
    }


    fun show() {
        visible()
        mPlayerLoadingView?.visibility = VISIBLE
    }

    fun hide() {
        gone()
        mPlayerLoadingView?.visibility = GONE
    }


    /**
     * 设置即将播放文案
     */
    fun setTipText(tip: String) {
        mIncludeScalePreparingLoadingContent?.text = "即将播放  ${tip ?: ""}"
        setShowPreparingTextSize()
    }
}