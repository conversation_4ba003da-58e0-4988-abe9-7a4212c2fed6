package com.sohuott.tv.vod.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.widget.GlideImageView;

/**
 * Created by yizhang210244 on 2018/4/26.
 */

public class WinnerUserView extends LinearLayout{
    private GlideImageView mUserIcon;
    private TextView mUserName;
    private TextView mBonus;

    public WinnerUserView(Context context) {
        super(context);
        init(context);
    }
    public WinnerUserView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public WinnerUserView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    private void init(Context context){
        setOrientation(LinearLayout.VERTICAL);
        LayoutInflater.from(context).inflate(R.layout.winner_user_view_layout, this, true);
        setGravity(Gravity.CENTER);
        mUserIcon = (GlideImageView) findViewById(R.id.winner_icon);
        mUserName = (TextView) findViewById(R.id.nick_name);
        mBonus = (TextView) findViewById(R.id.bonus_id);
    }

    public void setData(String userIconUrl,String userName,String bonus){
        if(!TextUtils.isEmpty(userIconUrl)){
            mUserIcon.setCircleImageRes(userIconUrl, getResources().getDrawable(R.drawable.welfare_default_avatar),
                    getResources().getDrawable(R.drawable.welfare_default_avatar) );
        }
        mUserName.setText(userName);
        mBonus.setText(bonus);
    }
}
