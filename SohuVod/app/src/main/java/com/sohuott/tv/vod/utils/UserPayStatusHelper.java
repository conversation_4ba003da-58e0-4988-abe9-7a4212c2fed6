package com.sohuott.tv.vod.utils;

import static com.sohuott.tv.vod.lib.push.event.RefreshUserEvent.PAGE_PAY;

import android.content.Context;
import android.text.TextUtils;

import com.sohuott.tv.vod.account.common.Listener;
import com.sohuott.tv.vod.account.login.CarouselLogin;
import com.sohuott.tv.vod.account.login.Login;
import com.sohuott.tv.vod.account.payment.PayApi;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.DaoSessionInstance;
import com.sohuott.tv.vod.lib.db.greendao.User;
import com.sohuott.tv.vod.lib.db.greendao.UserDao;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.Logout;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.PostHelper;
import com.sohuott.tv.vod.lib.utils.Util;

import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

public class UserPayStatusHelper {
    private static LoginUserInformationHelper mHelper;

    public static void getUserInfo(final Context context, final int type, String passport, String token) {
        mHelper = LoginUserInformationHelper.getHelper(context);
        PayApi.getCarouselLogin(context, "", type, passport, token, 0, 0, 0, new Listener<CarouselLogin>() {
            @Override
            public void onSuccess(CarouselLogin response) {
                LibDeprecatedLogger.d("getCarouselLogin(): response = " + response);
                boolean isVipMem = false;
                if (null != response) {
                    CarouselLogin.DataEntity data = response.getData();
                    int status = response.getStatus();

                    if (status == 200 && null != data) {
                        CarouselLogin.DataEntity.ScanResult scanResult = data.getScanResult();

                        if (null != scanResult) {
                            if (scanResult.getRenew() != null && scanResult.getRenew().equalsIgnoreCase("ok")) {
                                PostHelper.postScanSuccessEvent(0);
                            }
                            if (scanResult.getTpay() != null && scanResult.getTpay().equalsIgnoreCase("ok")) {
                                PostHelper.postScanSuccessEvent(1);
                            }

                        }

                        CarouselLogin.DataEntity.UserInfoEntity userInfo = data.getUserInfo();

                        if (null != userInfo) {
                            if (userInfo.getSignInfo() != null) {
                                if (userInfo.getSignInfo().getAlipay()!= null && userInfo.getSignInfo().getAlipay().getStatus().equals("1")) {
                                    mHelper.putAutoSign("1");
                                } else if (userInfo.getSignInfo().getWechat() != null && userInfo.getSignInfo().getWechat().getStatus().equals("1")){
                                    mHelper.putAutoSign("1");
                                }else {
                                    mHelper.putAutoSign("0");
                                }
                            } else {
                                mHelper.putAutoSign("0");
                            }
                            List<Login.LoginData.Privilege> privileges = userInfo.getPrivileges();
                            String ticketNumber="";
                            if (null != userInfo.ticket
                                    &&!TextUtils.isEmpty(userInfo.ticket.number)
                                    && !userInfo.ticket.number.trim().equals("null")){
                                ticketNumber= Util.getTicketNumber(userInfo.ticket.number.trim());
                            }
                            final User user = userInfo.getSohuUser();

                            if (null != privileges && privileges.size() > 0) {
                                for (int i = 0; i < privileges.size(); i++) {
                                    Login.LoginData.Privilege privilege = privileges.get(i);
                                    long id = privilege.getId();
                                    long expireIn = privilege.getExpireIn();

                                    if (id == Constant.PRIVILEGE_ID_SOHU_MOVIE && expireIn > 0) {
                                        if (!privilege.getTime().equals(mHelper.getVipTime())) {
//                                            if (mHelper.getIsLogin()) {
//                                                isVipMem = true;
//                                                if (mHelper.isVip()) {
//                                                    ToastUtils.showToast2(PayActivity.this,
//                                                            PayActivity.this.getApplicationContext().getResources().getString(R.string.txt_activity_pay_renew_success_tip));
//                                                } else {
//                                                    ToastUtils.showToast2(PayActivity.this,
//                                                            PayActivity.this.getApplicationContext().getResources().getString(R.string.txt_activity_pay_member_success_tip));
//                                                }
//                                            }

                                            saveUserInfo(user, LoginUserInformationHelper.SUPER_VIP_MEMBER, privilege.getTime(), String.valueOf(expireIn));
                                            User mUser = user;
                                            mHelper.putSohuUserInformation(user);

                                            saveUserInfoToDB(context, mUser);

                                            if (!mHelper.getIsLogin()) {
                                                PostHelper.postLoginSuccessEvent();
                                            } else {
                                                PostHelper.postRefreshUserEvent(PAGE_PAY);
                                                PostHelper.postPayEvent(0);
                                            }
                                            if (!TextUtils.isEmpty(ticketNumber)){
                                                mHelper.putUserTicketNumber(ticketNumber);
                                                PostHelper.postTicketEvent();
                                            }
//                                            UserApi.getUserTicket(PayActivity.this);
//                                            UserApi.getUserLikeRank(PayActivity.this);
//                                            loadCommodityData();
                                            break;
                                        }
                                    } else if (i == privileges.size() - 1 && mHelper.getVipTime().equals("0")) {
                                        saveNotVipInfo(context, user,ticketNumber);
                                    }
                                }
                            } else {
                                saveNotVipInfo(context, user,ticketNumber);
                            }
                        }

                        int stateInfo = data.getStateInfo();
                        LibDeprecatedLogger.e("stateInfo=" + stateInfo + "/isVipMem=" + isVipMem );
                        if (stateInfo == 1 && !isVipMem) {
                            if (mHelper.getIsSinglePurchase()) {
                                mHelper.putIsSinglePurchase(false);

//                                loadKey();

//                                ToastUtils.showToast2(PayActivity.this,
//                                        PayActivity.this.getApplicationContext().getResources().getString(R.string.txt_activity_pay_single_purchase_success_tip));
                                PostHelper.postPayEvent(1);
                            }
                        }

                    }
                }
            }

            @Override
            public void onError(Throwable e) {

            }
        });
    }

    public static void requestLogout() {
        NetworkApi.getLogout(new Observer<Logout>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(Logout value) {
                LibDeprecatedLogger.d("requestLogout(): onNext()");

                if (mHelper != null && null != value) {
                    int status = value.getStatus();

                    if (status == 200) {
                        mHelper.clearLoginStatus();
                        PostHelper.postLogoutEvent();
                        //clear cached data in SharedPreference.
                        RequestManager.getInstance().updatePasspost(mHelper.getLoginPassport(), "0");
                    } else {
                        //filed
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestLogout(): onError()--" + e.getMessage());
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestLogout(): onComplete()");
            }
        });
    }
    public static void saveUserInfoToDB(Context context, User user){
        UserDao mUserDao = DaoSessionInstance.getDaoSession(context).getUserDao();
        mUserDao.insertOrReplace(user);
    }

    // 保存用户信息
    public static void saveUserInfo(User sohuUser, int vipStatus, String vipTime, String vipExpireIn) {
        if (null != mHelper) {
            mHelper.putUserGrade(vipStatus);// vip级别 - 0: 初级用户，1: 影视会员
            mHelper.putVipTime(vipTime);// vip会员到期时间
            mHelper.putVipExpireIn(vipExpireIn);// vip会员剩余时间
        }

        // 0: 初级用户，1: 影视会员
        sohuUser.setVipStatus(Integer.valueOf(vipStatus));
        sohuUser.setVipTime(vipTime);
        sohuUser.setVipExpireIn(vipExpireIn);
    }


    public static void saveNotVipInfo(Context context, User user,String ticketNumber) {
        saveUserInfo(user, LoginUserInformationHelper.COMMON_USER, "0", "0");
        if (!mHelper.getIsLogin()) {
            PostHelper.postLoginSuccessEvent();
        }

        User mUser = user;
        mHelper.putSohuUserInformation(user);
        saveUserInfoToDB(context, mUser);
        if (!TextUtils.isEmpty(ticketNumber)){
            mHelper.putUserTicketNumber(ticketNumber);
            PostHelper.postTicketEvent();
        }
//        UserApi.getUserTicket(this);

//        UserApi.getUserLikeRank(this);
        RequestManager.getInstance().updatePasspost(mHelper.getLoginPassport(), mHelper.getUserGrade() + "");
    }

}
