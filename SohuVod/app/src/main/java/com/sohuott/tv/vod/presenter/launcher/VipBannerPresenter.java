package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.service.PlayHistoryService;
import com.sohuott.tv.vod.model.VipBanner;
import com.sohuott.tv.vod.widget.lb.VipBannerView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;


public class VipBannerPresenter extends Presenter {
    private Context mContext;
    private PlayHistoryService mPlayHistoryService;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    private TypeZeroContentPresenterKt.OnTypeZeroPlayListener mOnTypeZeroPlayListener;

    private ViewHolder viewHolder;


    public void setOnTypeZeroPlayListener(TypeZeroContentPresenterKt.OnTypeZeroPlayListener onTypeZeroPlayListener) {
        mOnTypeZeroPlayListener = onTypeZeroPlayListener;
    }

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        LibDeprecatedLogger.d("VipBannerPresenter onCreateViewHolder");
        if (mContext == null) {
            mContext = parent.getContext();
        }
        mPlayHistoryService = new PlayHistoryService(mContext);
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_vip_banner, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        LibDeprecatedLogger.d("VipBannerPresenter onBindViewHolder");
        ViewHolder vh = (ViewHolder) viewHolder;
//        vh.view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//            @Override
//            public void onFocusChange(View v, boolean hasFocus) {
//                mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
//            }
//        });
        this.viewHolder = vh;
        if (item instanceof VipBanner) {
            vh.mVipBannerView.setData(((VipBanner) item).getList());
        }
    }



    @Override
    public void setOnClickListener(Presenter.ViewHolder holder, View.OnClickListener listener) {
        super.setOnClickListener(holder, listener);
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {
        LibDeprecatedLogger.d("VipBannerPresenter onUnbindViewHolder");
    }

    @Override
    public void onViewDetachedFromWindow(Presenter.ViewHolder holder) {
        LibDeprecatedLogger.d("VipBannerPresenter onViewDetachedFromWindow");
        super.onViewDetachedFromWindow(holder);

    }

    @Override
    public void onViewAttachedToWindow(Presenter.ViewHolder holder) {
        super.onViewAttachedToWindow(holder);
        LibDeprecatedLogger.d("VipBannerPresenter onViewAttachedToWindow");

    }
    public ViewHolder getViewHolder(){
        return viewHolder;
    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private VipBannerView mVipBannerView;

        ViewHolder(View view) {
            super(view);
            mVipBannerView = (VipBannerView) view;
        }
    }
}
