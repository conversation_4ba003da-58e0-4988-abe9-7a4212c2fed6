package com.sohuott.tv.vod.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.fragment.app.FragmentActivity;
import androidx.viewpager.widget.ViewPager;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.EpisodeHorzTabAdapter;
import com.sohuott.tv.vod.fragment.EpisodeBaseFragmentNew;
import com.sohuott.tv.vod.fragment.EpisodeTrailerFragment;
import com.sohuott.tv.vod.lib.db.greendao.ChildPlayHistory;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel;
import com.sohuott.tv.vod.lib.service.PlayHistoryService;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.lib.widgets.WrapContentViewPager;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.EpisodeHorzTabView;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by fenglei on 16-6-28.
 * 所有的选集接口均使用videos接口查询数据，其中type=0,为恒定之查询片花
 * 如果该片（非片花专集）有对应片花，aid使用albuminfo中返回的trailerId，而不是原来片子的id进行查询。
 * 排序问题：pgc&vrs中的综艺为降序，vrs其他类型为升序
 * 正片专辑后面append预告片逻辑：只有vrs的非综艺的正片专辑需要append预告片
 * videoCount：  pgc使用latestVideoCount
 * vrs非综艺正片专辑&从videoDetial初始化使用maxVideoOrder+trailerAppendCount
 * vrs其他专辑使用maxVideoOrder
 * 使用videoOrder定位的问题：
 * videoOrder：  pgc使用videoOrder
 * vrs非综艺正片专辑&从videoDetial初始化 正片使用videoOrder；片花使用tvFormalOrder
 * vrs其他专辑使用videoOrder
 */
public class EpisodeLayoutNew extends LinearLayout {
    public static final int CHILD_VRS_PAGE_SIZE = 4;     //VRS
    public static final int PGC_PAGE_SIZE = 3;      //综艺、PGC
    public static final int VRS_PAGE_SIZE = 10;     //VRS
    public static final int VRS_CHAIN_PAGE_SIZE = 5;     //VRS文字链
    public static final int TRAILER_PAGE_SIZE = 6;  //花絮
    public static final int DESC_SORT_ORDER = 0; //降序
    public static final int ASC_SORT_ORDER = 1;  //升序

    public static final int EPISODE_TYPE_VRS = 0;
    public static final int EPISODE_TYPE_PGC = 2;
    public static final int EPISODE_TYPE_TRAILER = 1;

    private EpisodeHorzTabView mTabView;
    private ViewPager mViewPager;
    private int mAid;           //aid
    private int mVid;           //vid
    private int mType = 0;      //0:只查视频，1:只查片花,2:查询全部。目前该值固定为0。片花直接使用trialerId(片花专集id)查询
    private int mLayoutType = -1;    //-1: 没有初始化， 0 : 数字选集， 1： 文字链选集， 2：大图选集， 3：小图选集

    private List<AlbumInfoRecommendModel> mAlbumRecommendLists; // 推荐视频列表
    private int mSortOrder;     //1:升序, 0:降序,默认1
    private int mVideoOrder;    // 当前播放集数
    private int mVideoType;     //VRS or PGC
    private int mTotalCount;
    private FocusBorderView mFocusBorderView;
    private TextView mEpisodePoints;
    private EpisodeTrailerFragment.LoadTrailerDataCallback mLoadTrailerDataCallback;
    private int mCateCode;
    private boolean episodeIsSelected = false; //判断这个选集是否在播放
    private View mUpView;
    private View mDownView;
    private RelativeLayout mTabContentLayout;
    private TextView mEpisodeDesc;
    private boolean mIsTrailerTab;
    /**
     * 是否是全屏播控选集
     */
    private boolean isMenu;

    private boolean mIsChildEpisode = false;

    public EpisodeLayoutNew(Context context) {
        super(context);
        initAttrs(context, null);
        initView();
    }

    public EpisodeLayoutNew(Context context, AttributeSet attrs) {
        super(context, attrs);
        initAttrs(context, attrs);
        initView();
    }

    public EpisodeLayoutNew(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttrs(context, attrs);
        initView();
    }

    public void release() {
        EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
        if (adapter != null) {
            adapter.release();
            adapter = null;
        }
    }

    public void unsubscribe() {
        EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
        if (adapter != null) {
            adapter.unsubscribe();
        }
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        if (context == null || attrs == null) {
            return;
        }
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.EpisodeLayoutNew);
        if (typedArray == null) {
            return;
        }
        isMenu = typedArray.getBoolean(R.styleable.EpisodeLayoutNew_is_menu, false);
        typedArray.recycle();
    }

    private void initView() {
        setChildrenDrawingOrderEnabled(true);
        setOrientation(VERTICAL);
        LayoutInflater.from(getContext()).inflate(R.layout.episode_layout_new, this, true);
        mTabContentLayout = (RelativeLayout) findViewById(R.id.tab_content_layout);
        mEpisodeDesc = (TextView) findViewById(R.id.episode_desc);
        mTabView = (EpisodeHorzTabView) findViewById(R.id.indicator);
        mTabView.setIsMenuView(isMenu);
        mViewPager = new WrapContentViewPager(getContext());
        mViewPager.setClipChildren(false);
        setClipChildren(false);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
//        mViewPager.setLayoutParams(layoutParams);
        //剧集动态添加的位置
        int rangeIndex = 0;
        //全屏菜单播控选集列表初始化布局逻辑
        if (isMenu) {
            rangeIndex = 1;
            //播控修改自身高度 否则剧集区间tabview 因设置bottom 显示不全
            LayoutParams params = (LayoutParams) mTabContentLayout.getLayoutParams();
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            params.bottomMargin = getResources().getDimensionPixelSize(R.dimen.y20);
            mTabContentLayout.setLayoutParams(params);
            //设置距离剧集列表的间距
//            HorizontalScrollView.LayoutParams p2 = (HorizontalScrollView.LayoutParams) mTabView.getLayoutParams();
//            if (p2 != null) {
//                String model = Build.MODEL == null ? "" : Build.MODEL;
//                AppLogger.v("model : " + model);
//                if (model.contains("MiTV2-49")){
////                    p2.bottomMargin = getResources().getDimensionPixelSize(R.dimen.y40);
////                    p2.topMargin = getResources().getDimensionPixelSize(R.dimen.y40);
//                    AppLogger.v("MiTV2-49 : p2.bottomMargin : " + getResources().getDimensionPixelSize(R.dimen.y40) + ", p2.topMargin  : " + getResources().getDimensionPixelSize(R.dimen.y40));
//
//                } else {
////                    p2.bottomMargin = getResources().getDimensionPixelSize(R.dimen.y20);
////                    p2.topMargin = getResources().getDimensionPixelSize(R.dimen.y20);
//                    AppLogger.v("else p2.bottomMargin : " + getResources().getDimensionPixelSize(R.dimen.y20) + ", p2.topMargin  : " + getResources().getDimensionPixelSize(R.dimen.y20));
//                }
//                mTabView.setLayoutParams(p2);
//            }
        }
        addView(mViewPager, rangeIndex, layoutParams);
    }

    public void setIsChildEpisode(boolean childEpisode) {
        mIsChildEpisode = childEpisode;
        mEpisodeDesc.setVisibility(VISIBLE);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        mTabContentLayout.setLayoutParams(params);
        mTabView.setIsChildTabView(true);
        //设置描述“选集”的位置
        RelativeLayout.LayoutParams p1 = (RelativeLayout.LayoutParams) mEpisodeDesc.getLayoutParams();
        if (p1 != null) {
            p1.topMargin = getResources().getDimensionPixelSize(R.dimen.y1);
            p1.leftMargin = getResources().getDimensionPixelSize(R.dimen.x120);
            mEpisodeDesc.setLayoutParams(p1);
        }
        //设置底部tab
        RelativeLayout.LayoutParams p2 = (RelativeLayout.LayoutParams) mTabView.getLayoutParams();
        if (p2 != null) {
            p2.topMargin = getResources().getDimensionPixelSize(R.dimen.y1);
            p2.leftMargin = getResources().getDimensionPixelSize(R.dimen.x30);
            p2.rightMargin = getResources().getDimensionPixelSize(R.dimen.x120);
            mTabView.setLayoutParams(p2);
        }

    }

    @Override
    protected int getChildDrawingOrder(int childCount, int drawingPosition) {
        int pos = -1;
        for (int index = 0; index < childCount; index++) {
            if (getChildAt(index).hasFocus()) {
                pos = index;
                break;
            }
        }
        if (pos < 0 || pos >= childCount) {
            return drawingPosition;
        }
        if (drawingPosition == childCount - 1) {
            return pos;
        } else if (drawingPosition < pos) {
            return drawingPosition;
        } else {
            return drawingPosition + 1;
        }
    }

    public void setEpisodeType(int layoutType) {
        mLayoutType = layoutType;
    }

    public void setRecommendList(List<AlbumInfoRecommendModel> recommendList) {
        mAlbumRecommendLists = recommendList;
    }

    public void setLoadTrailerDataCallback(EpisodeTrailerFragment.LoadTrailerDataCallback loadTrailerDataCallback) {
        mLoadTrailerDataCallback = loadTrailerDataCallback;
    }

    // 从VideoDetail IntroViewHolder init
    // isTrailer： 该album是否是片花专辑
    // carouselVideoOrder: 如不是从轮播进入 设置 -1, 否则设置wei carousel video index
    public void initFromIntroViewHolder(int aid, int vid, int videoType, int catecode, int sortOrder, int layoutType, boolean isTrailer, int totalCount, int carouselVideoOrder) {
        commonInit(aid, vid, videoType, catecode, sortOrder, mLayoutType, isTrailer, totalCount);
        mViewPager.setId(R.id.episode_init_from_intro);
        if (carouselVideoOrder == -1) {
            getVideoOrder();
        } else {
            initLayout(carouselVideoOrder);
        }
    }

    //添加这个接口是因为IntroViewHolder被回收之后，需要记录一下videoOrder //for 详情页
    public void initFromIntroViewHolder(int aid, int vid, int videoType, int catecode, int sortOrder, int layoutType, boolean isTrailer, int totalCount, int carouselVideoOrder, int videoOrder, boolean isTrailerTab) {
        mIsTrailerTab = isTrailerTab;
        commonInit(aid, vid, videoType, catecode, sortOrder, mLayoutType, isTrailer, totalCount);
        mViewPager.setId(R.id.episode_init_from_intro);
        if (carouselVideoOrder == -1) {
//            initLayout(videoOrder);
//            if (isTrailer) {
            initLayout(videoOrder);
//            } else {
//                getVideoOrder();
//            }
        } else {
            initLayout(carouselVideoOrder);
        }

    }


    // 从VideoDetail TrailerViewHolder init
    // 只有片花时，默认从历史记录播。
    //有正片选集也有片花，播放完正片选集,从片花的第一集播放。
    public void initFromTrailerViewHolder(int aid, int vid, int videoType, int catecode, int sortOrder, int layoutType, boolean isTrailer, int totalCount, boolean isOnlyHasTrailer) {
        commonInit(aid, vid, videoType, catecode, sortOrder, mLayoutType, isTrailer, totalCount);
        mViewPager.setId(R.id.episode_init_from_trailer);
        if (isOnlyHasTrailer) {
            getVideoOrder();
        } else {
            initLayout(-1);
        }
    }

    // 从player init
    public void initFromMenuView(int aid, int vid, int videoType, int catecode, int sortOrder, int layoutType, boolean isTrailer, int totalCount, int videoOrder, List<AlbumInfoRecommendModel> albumRecommendList) {
        mIsTrailerTab = isTrailer;
        mLayoutType = layoutType;
        mAlbumRecommendLists = albumRecommendList;
        commonInit(aid, vid, videoType, catecode, sortOrder, mLayoutType, isTrailer, totalCount);
        mViewPager.setId(R.id.episode_init_from_player);
        if (videoOrder == -1) {
            getVideoOrder();
        } else {
            initLayout(videoOrder);
        }
    }

    private void commonInit(int aid, int vid, int videoType, int catecode, int sortOrder, int layoutType, boolean isTrailer, int totalCount) {
        mAid = aid;
        mVid = vid;
        mVideoType = videoType;
        mCateCode = catecode;
        if (mVideoType != Constant.DATA_TYPE_VRS) {
            if (mCateCode == CornerTagImageView.CORNER_TYPE_SOHUCLASS) {
                mSortOrder = ASC_SORT_ORDER;
            } else {
                mSortOrder = DESC_SORT_ORDER;
            }
        } else if (sortOrder == 1) {
            mSortOrder = DESC_SORT_ORDER;
        } else {
            mSortOrder = ASC_SORT_ORDER;
        }
        mTotalCount = totalCount;
    }

    public void setEpisodeFragmentFoucus() {
        if (mViewPager != null && mViewPager != null) {
            EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
            if (adapter == null) {
                return;
            }
            EpisodeBaseFragmentNew fragment = (EpisodeBaseFragmentNew) adapter.getItem(mViewPager.getCurrentItem());
            if (fragment != null) {
                fragment.setFragmentFocus(false);
            }
        }
    }

    public View getEpisodeFragmentFocusView(boolean fromTab) {
        EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
        EpisodeBaseFragmentNew fragment = (EpisodeBaseFragmentNew) adapter.getItem(mViewPager.getCurrentItem());
        if (adapter != null && fragment != null) {
            return fragment.getFragmentFocusView(fromTab);
        }
        return null;
    }

    public void setEpisodeFoucus() {
//        if (mTabView != null) {
//            mTabView.setCurrentTabFocus();
//        }
    }

    public boolean isEpisodeTabFocus() {
        if (mTabView != null && mTabView.hasFocus()) {
            return true;
        }
        return false;
    }

    public View getEpisodeFocusView() {
        if (mTabView != null) {
            return mTabView.getCurrentTabFocusView();
        }
        return null;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        mFocusBorderView = focusBorderView;
    }

    public void setmEpisodePoints(TextView pointsView) {
        mEpisodePoints = pointsView;
    }

    public int isLastEpisode(int videoOrder) {
        if (mSortOrder == ASC_SORT_ORDER) {
            if (videoOrder < 1 || videoOrder > mTotalCount - 1) {
                return -1;
            } else {
                return videoOrder + 1;
            }
        } else {
            if (videoOrder < 2 || videoOrder > mTotalCount) {
                return -1;
            } else {
                return videoOrder - 1;
            }
        }
    }

    private void getVideoOrder() {
        getHistoryVideoOrder().subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Integer>() {
                    @Override
                    public void accept(Integer order) throws Exception {
                        mVideoOrder = order;
                        initLayout(mVideoOrder);
                    }
                });
    }

    private Observable<Integer> getHistoryVideoOrder() {
        return Observable.create(new ObservableOnSubscribe<Integer>() {
            @Override
            public void subscribe(ObservableEmitter<Integer> e) throws Exception {
                if (mIsChildEpisode) {

                } else {
                    PlayHistoryService playHistoryService = new PlayHistoryService(getContext());
                    playHistoryService.getPlayHistoryById(mVideoType
                            , mVideoType == Constant.DATA_TYPE_VRS ? mAid : mVid, new PlayHistoryService.PlayHistoryListener() {
                                @Override
                                public void onSuccess(List<PlayHistory> playHistoryList) {
                                    int order = -1;
                                    if (playHistoryList != null && playHistoryList.size() > 0) {
                                        PlayHistory playHistory = playHistoryList.get(0);
                                        if (playHistory != null) {
                                            order = playHistory.getVideoOrder();
                                        }
                                    }
                                    e.onNext(order);
                                }

                                @Override
                                public void onFail(String reason, List<PlayHistory> playHistoryList) {
                                    e.onError(new Throwable(reason));
                                }
                            });

                }
            }
        });

    }

    private void initLayout(int videoOrder) {
        if (mLayoutType == 0) {
            mTabView.setVisibility(VISIBLE);
            mTabContentLayout.setVisibility(VISIBLE);
            if (videoOrder == -1) {
                if (mSortOrder == DESC_SORT_ORDER) {
                    videoOrder = mTotalCount;
                } else {
                    videoOrder = 1;
                }
            }
            mVideoOrder = correctVideoOrder(videoOrder);
        } else {
            mTabView.setVisibility(GONE);
            mTabContentLayout.setVisibility(GONE);
            mVideoOrder = videoOrder;
        }
        EpisodeHorzTabAdapter adapter = new EpisodeHorzTabAdapter(((FragmentActivity) getContext()).
                getSupportFragmentManager(), mTabView, mAid, mVid, mTotalCount, mSortOrder,
                mLayoutType, getPageSize(), mVideoType, mType, mCateCode, mVideoOrder, calcuStartPage(videoOrder) - 1, Util.getPartnerNo(getContext()),
                mViewPager.getId() == R.id.episode_init_from_player,
                mLoadTrailerDataCallback, isMenu);
        adapter.setEpisodeSelected(episodeIsSelected);
        adapter.setFocusBorderView(mFocusBorderView);
        adapter.setEpisodePoints(mEpisodePoints);
        adapter.setIsChildEpisode(mIsChildEpisode);
        adapter.setIsTrailerTab(mIsTrailerTab);
//        if (mAlbumRecommendLists != null && mAlbumRecommendLists.size() > 0) {
        adapter.setmAlbumRecommendLists(mAlbumRecommendLists);
//        }

        if (mViewPager.getAdapter() != null && mViewPager.getAdapter() instanceof EpisodeHorzTabAdapter) {
            ((EpisodeHorzTabAdapter) mViewPager.getAdapter()).unsubscribe();
        }
        mViewPager.setAdapter(adapter);
        LayoutParams params = (LayoutParams) mViewPager.getLayoutParams();
        if (isMenu) {
            if (mLayoutType == 2) {
                params.bottomMargin = getResources().getDimensionPixelOffset(R.dimen.y90);
                params.topMargin = getResources().getDimensionPixelOffset(R.dimen.y30);
            } else if (mLayoutType == 3) {
                params.bottomMargin = getResources().getDimensionPixelOffset(R.dimen.y10);

            }
            mViewPager.setLayoutParams(params);
        } else {
            if (mLayoutType == 2) {
                params.bottomMargin = getResources().getDimensionPixelOffset(R.dimen.y70);
            } else if (mLayoutType == 3) {
                params.bottomMargin = getResources().getDimensionPixelOffset(R.dimen.y10);
            }
        }
        mTabView.setViewPager(mViewPager);
        mTabView.setCurrentItem(calcuStartPage(videoOrder) - 1);
        mTabView.setOnPageChangeListener(adapter);
        if (mLayoutType != 0) {
            mTabView.setVisibility(GONE);
        } else {
            mTabView.setVisibility(VISIBLE);
        }
    }

    private int correctVideoOrder(int videoOrder) {
        if (mSortOrder == ASC_SORT_ORDER) {
            if (videoOrder < 1 || videoOrder > mTotalCount) {
                videoOrder = 1;
            }
        } else {
            if (videoOrder < 1 || videoOrder > mTotalCount) {
                videoOrder = mTotalCount;
            }
        }
        return videoOrder;
    }

    private int calcuStartPage(int videoOrder) {
        int startPage;
        if (mSortOrder == ASC_SORT_ORDER) {
            startPage = videoOrder / getPageSize() + ((videoOrder % getPageSize()) == 0 ? 0 : 1);
        } else {
            startPage = (mTotalCount - videoOrder) / getPageSize() + 1;
        }
        if (startPage < 1) {
            startPage = 1;
        } else if (startPage > mTotalCount / getPageSize() + 1) {
            startPage = mTotalCount / getPageSize() + 1;
        }
        return startPage;
    }

    public int getPageSize() {
        switch (mVideoType) {
//            case 1:
//                return VRS_CHAIN_PAGE_SIZE;
//            case 2:
//                return CHILD_VRS_PAGE_SIZE;
            case Constant.DATA_TYPE_VRS:
                return VRS_PAGE_SIZE;
            default:
                return PGC_PAGE_SIZE;

        }
    }


    public int getSortOrder() {
        return mSortOrder;
    }

    public int getEpisodeType() {
        return mLayoutType;
    }

    public int getTotalCount() {
        return mTotalCount;
    }

    public int getEpisodeVideoOrder() {
        return mVideoOrder;
    }

    public boolean isInit() {
        return mAid != 0 && mAid != -1;
    }

    public void updateSelectAfterPlay(int videoOrder, boolean isSelect, int vid) {
        mVid = vid;
        if (isSelect) {
            mVideoOrder = videoOrder;
            if (videoOrder < 0 || videoOrder > mTotalCount || mViewPager == null || mViewPager.getAdapter() == null) {
                return;
            }
            EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
            if (adapter != null) {
                adapter.setEpisodeSelected(true);
            }

            if (mViewPager.getCurrentItem() + 1 < mViewPager.getAdapter().getCount()) {
                setFragmentUnSelect(mViewPager.getCurrentItem() + 1);
            }
            if (mViewPager.getCurrentItem() - 1 > -1) {
                setFragmentUnSelect(mViewPager.getCurrentItem() - 1);
            }
            setFragmentUnSelect(mViewPager.getCurrentItem());

            ((EpisodeHorzTabAdapter) mViewPager.getAdapter()).setVideoOrder(mVideoOrder);
            int startPage = calcuStartPage(videoOrder);
            if (startPage - 1 == mTabView.getCurrentItemIndex()) {
                setFragmentSelect(videoOrder, vid);
            } else {
                mTabView.setCurrentItem(startPage - 1);
                setFragmentSelect(videoOrder, vid);
            }
        } else {
            mVideoOrder = -1;
            if (mViewPager == null || mViewPager.getAdapter() == null) {
                return;
            }
            EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
            if (adapter != null) {
                adapter.setEpisodeSelected(false);
            }
            if (mViewPager.getCurrentItem() + 1 <= mViewPager.getAdapter().getCount()) {
                if (mLayoutType != 0) {
                    setFragmentUnSelect(mViewPager.getCurrentItem());
                } else {
                    setFragmentUnSelect(mViewPager.getCurrentItem() + 1);
                }
            }
            if (mViewPager.getCurrentItem() - 1 > -1) {
                setFragmentUnSelect(mViewPager.getCurrentItem() - 1);
            }
            setFragmentUnSelect(mViewPager.getCurrentItem());
        }
    }

    private void setFragmentSelect(int videoOrder, int vid) {
        mVid = vid;
        EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
        EpisodeBaseFragmentNew fragment = (EpisodeBaseFragmentNew) adapter.
                getItem(mViewPager.getCurrentItem());
        fragment.setItemSelect(videoOrder, mViewPager.hasFocus() && getVisibility() == VISIBLE, vid);
    }

    private void setFragmentUnSelect(int index) {
        EpisodeHorzTabAdapter adapter = (EpisodeHorzTabAdapter) mViewPager.getAdapter();
        EpisodeBaseFragmentNew fragment = (EpisodeBaseFragmentNew) adapter.getItemIfExist(index);
        if (fragment != null) {
            fragment.setItemUnSelect();
        }
    }

    public View getPagerView() {
        return mViewPager;
    }

    /**
     * 获取按键处理方式
     *
     * @param keycode
     * @return 0：由系统自动处理   ——需要上层view不屏蔽dispatchKeyEvent
     * 1：内部手动处理    ——本view内部处理，需要上层view屏蔽dispatchKeyEvent
     * 2：外部手动处理    ——上层view内部处理，需要上层view屏蔽dispatchKeyEvent
     */
    public int canProcessKeyBySystem(int keycode) {
        switch (keycode) {
            case KeyEvent.KEYCODE_DPAD_LEFT:
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                return 0;
            case KeyEvent.KEYCODE_DPAD_UP:
                if (findViewById(R.id.indicator) != null && findViewById(R.id.indicator).hasFocus()) {
                    setEpisodeFragmentFoucus();
                    return 1;
                } else if (getPagerView() != null && getPagerView().hasFocus() && getPageSize() == TRAILER_PAGE_SIZE) {
                    View view = mFocusBorderView.getFocusView();
                    if (view != null && view.getParent() != null && ((ViewGroup) view.getParent()).indexOfChild(view) > 2) {
                        return 0;
                    }
                }
                return 2;
            case KeyEvent.KEYCODE_DPAD_DOWN:
                if (getPagerView() != null && getPagerView().hasFocus()) {
                    if (getPageSize() == TRAILER_PAGE_SIZE) {
                        View view = mFocusBorderView.getFocusView();
                        if (view != null && view.getParent() != null && ((ViewGroup) view.getParent()).indexOfChild(view) < 3) {
                            return 0;
                        }
                    }
                    setEpisodeFoucus();
                    return 1;

                }
                return 2;
        }
        return 2;
    }

    public boolean canProcessKeyUpExternal() {
        if (mViewPager.hasFocus()) {
            View view = findFocus();
            if (view != null && view.getParent() != null && ((ViewGroup) view.getParent()).indexOfChild(view) > 2) {
                return false;
            }
            return true;
        }
        return false;
    }

    public int getAid() {
        return mAid;
    }

    public void setEpisodeIsSelected(boolean value) {
        this.episodeIsSelected = value;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mTabView.setOnPageChangeListener(null);
    }
}
