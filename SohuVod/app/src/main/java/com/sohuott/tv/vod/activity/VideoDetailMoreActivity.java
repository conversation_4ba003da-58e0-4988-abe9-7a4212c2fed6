package com.sohuott.tv.vod.activity;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.ActorListAdapter;
import com.sohuott.tv.vod.base_router.RouterPath;
import com.sohuott.tv.vod.lib.utils.StringUtils;
import com.sohuott.tv.vod.videodetail.activity.state.VideoInfoResponse;
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel;
import com.sohuott.tv.vod.view.BaseItemDecoration;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;

import java.util.HashMap;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;

/**
 * Created by XiyingCao on 16-3-14.
 */
@Route(path = RouterPath.Detail.VIDEO_DETAIL_DEC_ACTIVITY)
public class VideoDetailMoreActivity extends BaseActivity {

    private CustomLinearRecyclerView mActorListRecyclerView;
    private ActorListAdapter mActorListAdapter;
    private CustomLinearLayoutManager mLayoutManager;
    private static final String TAG = "VideoDetailMoreActivity";
    @Autowired(name = "VideoInfoResponse")
    public VideoInfoResponse mAlbumInfo;
    @Autowired(name = "VideoDetailRecommendModel")
    public VideoDetailRecommendModel.DataBean mRecommendDataBean;
    private TextView moreDetailTitle, moreDetailText, scoreTextView, typeTextView;
    private ImageView memberIcon;
    private HashMap<String, String> mPathInfo;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        ARouter.getInstance().inject(this);
        setContentView(R.layout.activity_videodetail_more);
        setDisplay();
        initView();
//        Serializable serializable = getIntent().getSerializableExtra(ParamConstant.PARAM_AULBUM_INFO);
//        if (serializable != null) {
//            try {
//                mAlbumInfo = (VideoInfoResponse) serializable;
//            } catch (Exception e) {
//                LogManager.d(TAG, "e ? " + e);
//            }
//        }
//        Serializable serializableRecmmendDataBean = getIntent().getSerializableExtra(ParamConstant.PARAM_RECOMMEND_DATABEAN);
//        if (serializableRecmmendDataBean != null) {
//            try {
//                mRecommendDataBean = (VideoDetailRecommendModel.DataBean) serializableRecmmendDataBean;
//            } catch (Exception e) {
//                LogManager.d(TAG, "e ? " + e);
//            }
//        }
        initAlbumInfoData(mAlbumInfo);
        initActorList(mRecommendDataBean);

        mPathInfo = new HashMap<>(1);
        mPathInfo.put("pageId", "1043");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), mPathInfo, null, null);
    }


    //设置窗口大小
    private void setDisplay() {
        //设置弹出窗口与屏幕对齐
        Window win = this.getWindow();
        int density = (int)(getResources().getDisplayMetrics().density);
        //设置内边距，这里设置为0
        win.getDecorView().setPadding(1 * density, 1 * density, 1 * density, 1 * density);
        WindowManager.LayoutParams lp = win.getAttributes();
        //设置窗口宽度
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        //设置窗口高度
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        //设置Dialog位置
        lp.gravity = Gravity.TOP | Gravity.LEFT;
        win.setAttributes(lp);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    private void initView() {
        moreDetailTitle = findViewById(R.id.more_detail_title);
        moreDetailText = findViewById(R.id.more_detail_text);
        scoreTextView = findViewById(R.id.score_textview);
        typeTextView = findViewById(R.id.type_textview);
        memberIcon = findViewById(R.id.member_icon);

        mActorListRecyclerView = (CustomLinearRecyclerView) findViewById(R.id.actor_list);
        mActorListRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mActorListRecyclerView.addItemDecoration(new BaseItemDecoration(getResources().getDimensionPixelSize(R.dimen.x45)));
    }

    private void initActorList(VideoDetailRecommendModel.DataBean recommendDataBean) {
        if (recommendDataBean != null) {
            if (recommendDataBean.getContents() != null && recommendDataBean.getContents().size() > 0) {
                mActorListRecyclerView.setVisibility(View.VISIBLE);
                mActorListAdapter = new ActorListAdapter(recommendDataBean.getContents(), mActorListRecyclerView);
                mActorListRecyclerView.setAdapter(mActorListAdapter);
                mLayoutManager = new CustomLinearLayoutManager(this);
                mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
                mLayoutManager.setCustomPadding(getResources().getDimensionPixelSize(R.dimen.x370),
                        getResources().getDimensionPixelSize(R.dimen.x65));
                mActorListRecyclerView.setLayoutManager(mLayoutManager);
                mActorListRecyclerView.setItemAnimator(new DefaultItemAnimator());
                mActorListRecyclerView.setItemViewCacheSize(0);
            } else {
                mActorListRecyclerView.setVisibility(View.GONE);
            }
        } else {
            mActorListRecyclerView.setVisibility(View.GONE);
        }
    }

    private void initAlbumInfoData(VideoInfoResponse albumInfo) {
        if (albumInfo != null) {
//            mActorListAdapter = new ActorListAdapter(albumInfo.data.actors, mActorListRecyclerView);
//            mActorListRecyclerView.setAdapter(mActorListAdapter);
//            mLayoutManager = new CustomLinearLayoutManager(this);
//            mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
//            mLayoutManager.setCustomPadding(getResources().getDimensionPixelSize(R.dimen.x370),
//                    getResources().getDimensionPixelSize(R.dimen.x65));
//            mActorListRecyclerView.setLayoutManager(mLayoutManager);
//            mActorListRecyclerView.setItemAnimator(new DefaultItemAnimator());
//            mActorListRecyclerView.setItemViewCacheSize(0);

            moreDetailTitle.setText(albumInfo.getVideoName());
//            moreDetailText.setText(albumInfo.data.tvDesc);
            if (StringUtils.isNotEmptyStr(albumInfo.getScore())) {
                if ("0".equals(albumInfo.getScore().trim()) || "0.0".equals(albumInfo.getScore().trim())) {
                    scoreTextView.setVisibility(View.GONE);
                } else {
                    scoreTextView.setText(albumInfo.getScore());
                }
            } else {
                scoreTextView.setVisibility(View.GONE);
            }

            if (albumInfo.isShowVip()) {
                memberIcon.setVisibility(View.VISIBLE);
            } else {
                memberIcon.setVisibility(View.GONE);
            }
//            StringBuilder typeSb = new StringBuilder();
//            if (StringUtils.isNotEmptyStr(albumInfo.data.tvYear)) {
//                typeSb.append(albumInfo.data.tvYear);
//            }
//            if (StringUtils.isNotEmptyStr(albumInfo.data.areaName)) {
//                typeSb.append(" | ").append(albumInfo.data.areaName);
//            }
//            String genreName = albumInfo.data.genreName;
//
//            if (StringUtils.isNotEmptyStr(genreName)) {
//                String newgereName = genreName.replace(",", " | ");
//                typeSb.append(" | ").append(newgereName);
//            }
            typeTextView.setText(albumInfo.getLabel());
            moreDetailText.setText(albumInfo.getDec());
        }
    }

}
