package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.KeyEvent;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.util.TypedValue;

import androidx.fragment.app.FragmentManager;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.fragment.ConsumeEduRecordFragment;
import com.sohuott.tv.vod.fragment.ConsumeRecordFragment;
import com.sohuott.tv.vod.fragment.HistoryFavorCollectionFragment;
import com.sohuott.tv.vod.fragment.MyUserFragment;
import com.sohuott.tv.vod.fragment.PointFragment;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.TopInfo;
import com.sohuott.tv.vod.lib.push.event.LoginSuccessEvent;
import com.sohuott.tv.vod.lib.push.event.LogoutEvent;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.utils.SouthMediaUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.GlideImageView;
import com.sohuott.tv.vod.widget.UserRelatedHeaderView;

import org.greenrobot.eventbus.Subscribe;

import java.util.HashMap;

import io.reactivex.observers.DisposableObserver;

import static com.sohuott.tv.vod.widget.TopBar.INDEX_SEARCH_BUTTON;

/**
 * Created by wenjingbian on 2017/3/21.
 */

public class ListUserRelatedActivity extends BaseFragmentActivity implements UserRelatedHeaderView.HeaderViewFocusController {
    //Child index of left list view
    public static final int LIST_INDEX_MY = 1;
    public static final int LIST_INDEX_HISTORY = 2;
    public static final int LIST_INDEX_COLLECTION = 3;
    public static final int LIST_INDEX_BOOKED = 4;
    public static final int LIST_INDEX_FAVOR = 5;
    public static final int LIST_INDEX_POINT = 6;
    public static final int LIST_INDEX_CONSUME_RECORD = 7;
    public static final int LIST_INDEX_EDU_CONSUME_RECORD = 8;

    private static final int FRAGMENT_HFC = 1;
    private static final int FRAGMENT_MY = 2;
    private static final int FRAGMENT_CONSUME = 3;
    private static final int FRAGMENT_POINT = 4;
    private static final int FRAGMENT_EDU_CONSUME = 5;

    public static final int SPAN_COUNT = 4;

    private static final int MSG_UPDATE = 1;
    private static final int MSG_VRS_EVENT = 2;

    private int leftTabSize;

    private FocusBorderView mFocusBorderView;

    private LinearLayout parent_view;
    //Sub-list on the left view
    private LinearLayout list_left_hfc;
    private LoadingView mParentLoadingView;
    //items in the left list
    private RelativeLayout item_my, item_history, item_collection, item_booked, item_favor, item_score, item_consume_record, item_edu_consume_record;
    private GlideImageView giv_hfc_user_icon;
    private UserRelatedHeaderView mHeaderView;

    private HistoryFavorCollectionListHandler mHandler;

    private LoginUserInformationHelper mHelper;

    //tag value of the selected item in left list
    private int mLeftSelectedTag = LIST_INDEX_MY;
    private boolean isLoginOnPause;
    private boolean isReportClick;

    private BaseFragment mBaseFragment;
    private HistoryFavorCollectionFragment mHfcFragment;
    private MyUserFragment mMyFragment;
    private ConsumeRecordFragment mConsumeRecordFragment;
    private ConsumeEduRecordFragment mConsumeEduRecordFragment;
    private PointFragment mPointFragment;
    private int mLeftItemKeyCode = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SouthMediaUtil.southNewMediaCheck(this);
        setContentView(R.layout.activity_list_user_related);

        mHandler = new HistoryFavorCollectionListHandler();
        mHfcFragment = new HistoryFavorCollectionFragment();
        mMyFragment = new MyUserFragment();
        mConsumeRecordFragment = new ConsumeRecordFragment();
        mConsumeEduRecordFragment = new ConsumeEduRecordFragment();
        mPointFragment = new PointFragment();

        leftTabSize = Util.getCourseParams(this) == 0 ? 7 : 8;

        initParams();
        initView();
        initData();
        RequestManager.getInstance().onListUserRelatedActivityExposureEvent();
        setPageName("6_user_related");
        focusLeftItem(mLeftSelectedTag);
        isReportClick = true;

        if (Util.getCourseParams(this) == 0) {
            item_edu_consume_record.setVisibility(View.GONE);
        }

        mHandler.removeMessages(MSG_VRS_EVENT);
        mHandler.sendEmptyMessageDelayed(MSG_VRS_EVENT, 1000);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        RequestManager.getInstance().onListUserRelatedActivityExposureEvent();
        isReportClick = true;

        //clear previous status of the left list
        if (list_left_hfc != null) {
            View childView = null;
            TextView textView = null;
            ImageView imageView = null;
            for (int i = 0; i < list_left_hfc.getChildCount(); i++) {
                childView = list_left_hfc.getChildAt(i);
                if (childView != null) {
                    textView = (TextView) childView.findViewById(R.id.tv_item_hfc_left);
                    if (textView != null) {
                        textView.setSelected(false);
                    }
                }
            }
        }

        initParams();

        //update data on the right view according to the current left index
        updateRightContentData(mLeftSelectedTag, false);
        focusLeftItem(mLeftSelectedTag);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!isLoginOnPause && mHelper.getIsLogin()) {
            focusLeftItem(mLeftSelectedTag);
            updateRightContentData(mLeftSelectedTag, false);
        }
        if (mHelper.getIsLogin()) {
            setUserIcon();
        } else {
            giv_hfc_user_icon.setImageResource(R.drawable.ic_hfc_user_default);
        }
        getTopData();
        refreshTopBar();
    }

    @Override
    protected void onPause() {
        super.onPause();
        isLoginOnPause = mHelper.getIsLogin();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //release objects
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
        mHelper = null;
        mBaseFragment = null;
        mHfcFragment = null;
        mMyFragment = null;
        mConsumeRecordFragment = null;
        mConsumeEduRecordFragment = null;
        mPointFragment = null;
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (mParentLoadingView.getVisibility() == View.VISIBLE) {
            return true;
        } else if (mLeftSelectedTag == LIST_INDEX_HISTORY || mLeftSelectedTag == LIST_INDEX_COLLECTION
                || mLeftSelectedTag == LIST_INDEX_BOOKED) {
            if (keyCode == KeyEvent.KEYCODE_MENU) {
                mHfcFragment.showDeleteDialog();
                HashMap<String, String> path = new HashMap<>(1);
                String pageId = "1010";
                if (mLeftSelectedTag == LIST_INDEX_HISTORY) {
                    pageId = "1010";
                } else if (mLeftSelectedTag == LIST_INDEX_COLLECTION) {
                    pageId = "1011";
                } else if (mLeftSelectedTag == LIST_INDEX_BOOKED) {
                    pageId = "1012";
                }
                path.put("pageId", pageId);
                RequestManager.getInstance().onAllEvent(new EventInfo(10208, "clk"), path, null, null);
                return true;
            } else if (keyCode == KeyEvent.KEYCODE_BACK) {
                if (mHfcFragment.updateRecordItemView(false)) {
                    return true;
                }
            }
        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    protected boolean isEventBusAvailable() {
        return true;
    }

    @Subscribe
    public void onEventMainThread(LoginSuccessEvent event) {
        //Receive EventBus about login and reset user image to custom picture
        LibDeprecatedLogger.d("onEventMainThread(LoginSuccessEvent event)");

        if (event == null) {
            return;
        }
        //Refresh user icon when login successfully.
        setUserIcon();
        refreshTopBar();
    }

    private void refreshTopBar() {
        mHeaderView.updateLoginDisplay();
    }

    public void focusOnTopBar() {
        if (mHeaderView != null) {
            mHeaderView.focusChildView(INDEX_SEARCH_BUTTON);
        }
    }

    @Subscribe
    public void onEventMainThread(LogoutEvent event) {
        //Receive EventBus about logout and reset user image to default
        LibDeprecatedLogger.d("onEventMainThread(LogoutEvent event)");
        if (event == null) {
            return;
        }

        //Refresh user icon when logout successfully.
        giv_hfc_user_icon.resetImageRes();
        if (mHfcFragment != null) {
            mHfcFragment.resetCachedListData();
            mHfcFragment.clearLocalHistoryAndCollection();
        }
        refreshTopBar();
    }

    private void getTopData() {
        NetworkApi.getTopData(new DisposableObserver<TopInfo>() {
            @Override
            public void onNext(TopInfo value) {
                LibDeprecatedLogger.d("onNext: " + value.toString());
                mHeaderView.setData(value);
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        }, mHelper.getLoginPassport(), mHelper.getLoginToken());
    }

    public int getLeftSelectedTag() {
        return mLeftSelectedTag;
    }

    public void showParentLoadingView() {
        parent_view.setVisibility(View.GONE);
        mParentLoadingView.setVisibility(View.VISIBLE);
    }

    public void hideParentLoadingView() {
        focusLeftItem(mLeftSelectedTag);
        parent_view.setVisibility(View.VISIBLE);
        mParentLoadingView.setVisibility(View.GONE);
        handleRightCtn(mLeftSelectedTag);
    }

    public void focusLeftItem() {
        focusLeftItem(mLeftSelectedTag);
    }

    public void focusLeftItem(int leftSelectedTag) {
        if (list_left_hfc.getChildAt(leftSelectedTag) != null) {
            list_left_hfc.getChildAt(leftSelectedTag).requestFocus();
            TextView textView = (TextView) list_left_hfc.getChildAt(leftSelectedTag).findViewById(R.id.tv_item_hfc_left);
            if (textView != null) {
                ColorStateList color = textView.getResources().getColorStateList(R.color.item_hfc_left_text_selector);
                textView.setTextColor(color);
            }
        }
        //Just for LeTv to remove shader
        if (leftSelectedTag == LIST_INDEX_POINT && mPointFragment != null && mPointFragment.getView() != null) {
            mPointFragment.getView().invalidate();
        }
    }

    /**
     * Initialize view and the related listener
     */
    private void initView() {
        //init loading view and error view
        mFocusBorderView = (FocusBorderView) findViewById(R.id.focus_border_view);
        mHfcFragment.setFocusBorderView(mFocusBorderView);
        parent_view = (LinearLayout) findViewById(R.id.layout_parent_view);
        mParentLoadingView = (LoadingView) findViewById(R.id.parent_loading_view);

        //init left list view and its items
        giv_hfc_user_icon = (GlideImageView) findViewById(R.id.giv_hfc_user_icon);
        list_left_hfc = (LinearLayout) findViewById(R.id.list_left_ctn);

        item_my = (RelativeLayout) findViewById(R.id.item_my);
        TextView tv_my = (TextView) item_my.findViewById(R.id.tv_item_hfc_left);
        tv_my.setText(getResources().getString(R.string.txt_activity_user_related_my));
        item_my.setTag(LIST_INDEX_MY);

        item_consume_record = (RelativeLayout) findViewById(R.id.item_purchase);
        TextView tv_purchase = (TextView) item_consume_record.findViewById(R.id.tv_item_hfc_left);
        tv_purchase.setText(getResources().getString(R.string.txt_activity_user_related_consume_record));
        item_consume_record.setTag(LIST_INDEX_CONSUME_RECORD);


        item_edu_consume_record = (RelativeLayout) findViewById(R.id.item_edu_purchase);
        TextView tv_edu_purchase = (TextView) item_edu_consume_record.findViewById(R.id.tv_item_hfc_left);
        tv_edu_purchase.setText(getResources().getString(R.string.txt_activity_edu_related_consume_record));
        item_edu_consume_record.setTag(LIST_INDEX_EDU_CONSUME_RECORD);


        item_history = (RelativeLayout) findViewById(R.id.item_history);
        TextView tv_history = (TextView) item_history.findViewById(R.id.tv_item_hfc_left);
        tv_history.setText(getResources().getString(R.string.txt_activity_user_related_history));
        item_history.setTag(LIST_INDEX_HISTORY);

        item_collection = (RelativeLayout) findViewById(R.id.item_collection);
        TextView tv_collection = (TextView) item_collection.findViewById(R.id.tv_item_hfc_left);
        tv_collection.setText(getResources().getString(R.string.txt_activity_user_related_collection));
        item_collection.setTag(LIST_INDEX_COLLECTION);

        item_booked = (RelativeLayout) findViewById(R.id.item_booked);
        TextView tv_booked = (TextView) item_booked.findViewById(R.id.tv_item_hfc_left);
        tv_booked.setText(getResources().getString(R.string.txt_activity_user_related_booked));
        item_booked.setTag(LIST_INDEX_BOOKED);

        item_favor = (RelativeLayout) findViewById(R.id.item_favor);
        TextView tv_favor = (TextView) item_favor.findViewById(R.id.tv_item_hfc_left);
        tv_favor.setText(getResources().getString(R.string.txt_activity_user_related_favor));
        item_favor.setTag(LIST_INDEX_FAVOR);

        item_score = (RelativeLayout) findViewById(R.id.item_score);
        TextView tv_score = (TextView) item_score.findViewById(R.id.tv_item_hfc_left);
        tv_score.setText(getResources().getString(R.string.txt_activity_user_related_score));
        item_score.setTag(LIST_INDEX_POINT);

        mHeaderView = (UserRelatedHeaderView) findViewById(R.id.header_view);
        mHeaderView.setHeaderViewFocusController(this);
        mHeaderView.hideChildView(UserRelatedHeaderView.INDEX_HISTORY_LAYOUT);

        //Set listeners
        int childCount = list_left_hfc.getChildCount();
        if (childCount <= 0) {
            return;
        }
        for (int i = 0; i < childCount; i++) {
            final View tmpView = list_left_hfc.getChildAt(i);
            if (tmpView instanceof RelativeLayout) {
                tmpView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    TextView tmpTv = (TextView) ((RelativeLayout) tmpView).getChildAt(0);

                    @Override
                    public void onFocusChange(View view, boolean hasFocus) {
                        if (hasFocus) {
                            int tag = (int) tmpView.getTag();
                            if (mLeftSelectedTag != tag) {
                                updateRightContentData(tag, true);
                            }
                            if (mLeftSelectedTag > 0 && mLeftSelectedTag <= leftTabSize &&
                                    mLeftSelectedTag != list_left_hfc.indexOfChild(view)) {
                                list_left_hfc.getChildAt(mLeftSelectedTag)
                                        .findViewById(R.id.tv_item_hfc_left).setSelected(false);

                            }
                            mLeftSelectedTag = tag;
                            handleLeftTextColor(tag);
                            tmpTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x40));
                        } else {
                            if (mLeftItemKeyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                                tmpTv.setTextColor(Color.parseColor("#FF6247"));
                            }
                        }

                    }
                });

                tmpView.setOnKeyListener(new View.OnKeyListener() {
                    @Override
                    public boolean onKey(View v, int keyCode, KeyEvent event) {
                        mLeftItemKeyCode = keyCode;
                        TextView tmpTv = (TextView) ((RelativeLayout) tmpView).getChildAt(0);
                        if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && event.getAction() == KeyEvent.ACTION_DOWN) {
                            int index = list_left_hfc.indexOfChild(tmpView);
                            if (index == LIST_INDEX_MY) {
                                return false;
                            } else if (index == LIST_INDEX_CONSUME_RECORD && mConsumeRecordFragment != null) {
                                mConsumeRecordFragment.requestFocusAtPos(0);
                                return true;
                            } else if ((index == LIST_INDEX_HISTORY || index == LIST_INDEX_COLLECTION
                                    || index == LIST_INDEX_FAVOR || index == LIST_INDEX_BOOKED) && mHfcFragment != null) {
                                mHfcFragment.focusChildViewAtPos();
                            } else if (index == LIST_INDEX_POINT && mPointFragment != null) {
                                mPointFragment.requestFocusToDefaultPos();
                                return true;
                            } else if (index == LIST_INDEX_EDU_CONSUME_RECORD && mConsumeEduRecordFragment != null) {
                                mConsumeEduRecordFragment.requestFocusAtPos(0);
                                return true;
                            }
                            return true;
                        } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                            ColorStateList color = v.getResources().getColorStateList(R.color.item_hfc_left_text_selector);
                            tmpTv.setTextColor(color);
                            tmpTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x36));
                            if (list_left_hfc.indexOfChild(tmpView) != list_left_hfc.getChildCount() - 1) {
                                mHfcFragment.updateDeleteView(false);
                            } else {
                                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                                    list_left_hfc.getFocusedChild().startAnimation(
                                            AnimationUtils.loadAnimation(ListUserRelatedActivity.this, R.anim.shake_y));
                                }
                                return true;
                            }
                        } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
                            ColorStateList color = v.getResources().getColorStateList(R.color.item_hfc_left_text_selector);
                            tmpTv.setTextColor(color);
                            tmpTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x36));
                            if (list_left_hfc.indexOfChild(tmpView) != 1) {
                                mHfcFragment.updateDeleteView(false);
                            } else {
                                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                                    list_left_hfc.getFocusedChild().startAnimation(
                                            AnimationUtils.loadAnimation(ListUserRelatedActivity.this, R.anim.shake_y));
                                }
                                return true;
                            }
                        }
                        return false;
                    }
                });
            }
        }
    }

    //首次进入观看历史界面时也要上报默认选中长视频埋点
    private void sendVRSEvent() {
        if (mLeftSelectedTag == LIST_INDEX_HISTORY && mHfcFragment != null) {
            if (mHfcFragment.onlyVRS) {
                RequestManager.getInstance().onAllEvent(new EventInfo(10206, "clk"), null, null, null);
            } else {
                RequestManager.getInstance().onAllEvent(new EventInfo(10207, "clk"), null, null, null);
            }
        }
    }

    /**
     * 左侧item除去当前focus的，其他字体颜色复位
     * 当前focus的设置focus颜色
     *
     * @param tag
     */
    private void handleLeftTextColor(int tag) {
        int childCount = list_left_hfc.getChildCount();
        if (childCount <= 0) {
            return;
        }
        for (int i = 0; i < childCount; i++) {
            View tmpView = list_left_hfc.getChildAt(i);
            if (tmpView instanceof RelativeLayout) {
                int tempViewTag = (int) tmpView.getTag();
                TextView tmpTv = (TextView) ((RelativeLayout) tmpView).getChildAt(0);
                if (tempViewTag != tag) {
                    tmpTv.setTextColor(Color.parseColor("#B5E8E8FF"));
                } else {
                    tmpTv.setTextColor(Color.parseColor("#E8E8FF"));
                }
            }
        }
    }

    /**
     * Init various data
     */
    private void initData() {
        //request and update user icon
        mHelper = LoginUserInformationHelper.getHelper(this);
        updateRightContentData(mLeftSelectedTag, false);
    }

    private void initParams() {
        Uri uri = getIntent().getData();
        if (uri == null) {
            mLeftSelectedTag = Util.convertStringToInt(getIntent().getStringExtra(ParamConstant.PARAM_LEFT_INDEX));
        } else {
            mLeftSelectedTag = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_LEFT_INDEX));
        }
    }

    /**
     * Update date on the right of screen by view's id
     *
     * @param tag item'tag id of the left list
     */
    private void updateRightContentData(int tag, boolean isPostDelay) {
//        mHandler.removeCallbacksAndMessages(null);
        Message message = new Message();
        message.what = MSG_UPDATE;
        message.arg1 = tag;
        mHandler.removeMessages(MSG_UPDATE);
        if (isPostDelay) {
            mHandler.sendMessageDelayed(message, 300);
        } else {
            mHandler.sendMessage(message);
        }
    }

    /**
     * Show content on the right view by tag value of the selected item
     *
     * @param tag tag value of the selected item view
     */
    private void handleRightCtn(int tag) {
        //If user login, request certain data and display its view on the right side.
        switch (tag) {
            case LIST_INDEX_MY:
                transferFragment(FRAGMENT_MY);
                if (isReportClick) {
                    RequestManager.getInstance().onMyUserListTabClickEvent();
                }
                break;
            case LIST_INDEX_HISTORY:
                transferFragment(FRAGMENT_HFC);
                mHfcFragment.isDeleteItem(false);
                if (isReportClick) {
                    RequestManager.getInstance().onHistoryListTabClickEvent();
                }
                break;
            case LIST_INDEX_COLLECTION:
                transferFragment(FRAGMENT_HFC);
                mHfcFragment.isDeleteItem(false);
                if (isReportClick) {
                    RequestManager.getInstance().onCollectionListTabClickEvent();
                }
                break;
            case LIST_INDEX_BOOKED:
                transferFragment(FRAGMENT_HFC);
                mHfcFragment.isDeleteItem(false);
                if (isReportClick) {
                    RequestManager.getInstance().onBookedListTabClickEvent();
                }
                break;
            case LIST_INDEX_FAVOR:
                transferFragment(FRAGMENT_HFC);
                mHfcFragment.isDeleteItem(false);
                if (isReportClick) {
                    RequestManager.getInstance().onFavorListTabClickEvent();
                }
                break;
            case LIST_INDEX_POINT:
                transferFragment(FRAGMENT_POINT);
                if (isReportClick) {
                    RequestManager.getInstance().onPointTabClickEvent();
                }
                break;
            case LIST_INDEX_CONSUME_RECORD:
                transferFragment(FRAGMENT_CONSUME);
                if (isReportClick) {
                    RequestManager.getInstance().onConsumeRecordListTabClickEvent();
                }
                break;
            case LIST_INDEX_EDU_CONSUME_RECORD:
                transferFragment(FRAGMENT_EDU_CONSUME);
                if (isReportClick) {
                    RequestManager.getInstance().onConsumeRecordListTabClickEvent();
                }
                break;
            default:
                break;
        }
    }

    /**
     * Transfer fragment on the right screen
     *
     * @param fragmentIndex index of several fragment
     */
    private void transferFragment(int fragmentIndex) {
        FragmentManager fragmentManager = getSupportFragmentManager();
        BaseFragment fragment = null;
        switch (fragmentIndex) {
            case FRAGMENT_HFC:
                if (mBaseFragment instanceof HistoryFavorCollectionFragment) {
                    mHfcFragment.changeTab(mLeftSelectedTag);
                    return;
                } else {
                    fragment = mHfcFragment;
                }
                break;
            case FRAGMENT_MY:
                fragment = mMyFragment;
                break;
            case FRAGMENT_CONSUME:
                if (mBaseFragment instanceof ConsumeRecordFragment) {
                    mConsumeRecordFragment.initData();
                    return;
                } else {
                    fragment = mConsumeRecordFragment;
                }
                break;
            case FRAGMENT_EDU_CONSUME:
                if (mBaseFragment instanceof ConsumeEduRecordFragment) {
                    mConsumeEduRecordFragment.initData();
                    return;
                } else {
                    fragment = mConsumeEduRecordFragment;
                }
                break;
            case FRAGMENT_POINT:
                fragment = mPointFragment;
                break;
            default:
                break;
        }
        if (fragment == null) {
            return;
        }
        mBaseFragment = fragment;
        if (!isFinishing()) {
            fragmentManager.beginTransaction().replace(R.id.frame_right_ctn, mBaseFragment).commitAllowingStateLoss();
        }
    }

    private void setUserIcon() {
        if (giv_hfc_user_icon != null && mHelper != null && mHelper.getLoginPhoto() != null) {
            giv_hfc_user_icon.setCircleImageRes(mHelper.getLoginPhoto(), getResources().getDrawable(R.drawable.ic_hfc_user_default),
                    getResources().getDrawable(R.drawable.ic_hfc_user_default));
        }
    }

    @Override
    public boolean onFocusDown() {
        return false;
    }

    @Override
    public void onGetFocus(View focusView) {

    }

    /**
     * Custom Handler to handle date on the right view
     */
    private class HistoryFavorCollectionListHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);

            switch (msg.what) {
                case MSG_UPDATE:
                    handleRightCtn(msg.arg1);
                    break;
                case MSG_VRS_EVENT:
                    sendVRSEvent();
                    break;
                default:
                    break;

            }

        }
    }
}
