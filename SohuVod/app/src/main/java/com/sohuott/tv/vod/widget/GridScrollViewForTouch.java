package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ScrollView;

/**
 * Created by wenjingbian on 2017/12/4.
 */

public class GridScrollViewForTouch extends ScrollView {

    public interface OnGridScrollViewListener {
        void onMoveDown();

        void onMoveUp();
    }

    public OnGridScrollViewListener mListener;

    public GridScrollViewForTouch(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public void setListener(OnGridScrollViewListener listener){
        this.mListener = listener;
    }

    @Override
    protected void onScrollChanged(int x, int y, int oldx, int oldy) {
        super.onScrollChanged(x, y, oldx, oldy);
        //top position
        if (oldy == y) {
            return;
        }

        if (y > oldy) { //move down
            mListener.onMoveDown();
        } else if (y < oldy) {//move up
            mListener.onMoveUp();
        }
    }
}
