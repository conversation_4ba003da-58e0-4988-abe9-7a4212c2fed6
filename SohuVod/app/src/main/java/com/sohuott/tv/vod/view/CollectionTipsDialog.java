package com.sohuott.tv.vod.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.annotation.NonNull;
import android.view.KeyEvent;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.model.WechatPublic;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.widget.GlideImageView;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by yizhang210244 on 2017/6/29.
 */

public class CollectionTipsDialog extends Dialog{

    private static final int COUNTDOWN_TOTAL = 10000;
    private static final int COUNTDOWN_INTERVAL = 1000;

    private MyCountDownTimer mMyCountDownTimer;
    private GlideImageView mQrSimpleDraweeView;
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    public CollectionTipsDialog(@NonNull Context context) {
        super(context, R.style.CollectionTipsDialog);
        mMyCountDownTimer = new MyCountDownTimer(COUNTDOWN_TOTAL,COUNTDOWN_INTERVAL);
    }

    private void getQrcodeView() {
        DisposableObserver<WechatPublic> disposableObserver = new DisposableObserver<WechatPublic>() {
            @Override
            public void onNext(WechatPublic response) {
                if (null != response) {
                    String data = response.getData();
                    String message = response.getMessage();
                    int status = response.getStatus();

                    if (status == 200 && null != data) {
                        if (!data.trim().equals("")) {
                            mQrSimpleDraweeView.setClearWhenDetached(false);
                            mQrSimpleDraweeView.setImageRes(data, getContext().getResources().getDrawable(R.drawable.bg_launcher_poster),
                                    getContext().getResources().getDrawable(R.drawable.bg_launcher_poster));
                        }
                    } else {
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onComplete() {

            }
        };
        NetworkApi.getWechatLogin(UserInfoHelper.getGid(), Constant.TYPE_CAPTCHA_BIND, disposableObserver);
        mCompositeDisposable.add(disposableObserver);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_collection_tips);
        mQrSimpleDraweeView = (GlideImageView) findViewById(R.id.qr_image);
        getQrcodeView();
    }

    @Override
    public boolean onKeyDown(int keyCode, @NonNull KeyEvent event) {
        if(isShowing()){
            dismiss();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void show() {
        super.show();
        if(mMyCountDownTimer != null){
            mMyCountDownTimer.start();
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if(mMyCountDownTimer != null){
            mMyCountDownTimer.cancel();
            mMyCountDownTimer = null;
        }
        mCompositeDisposable.clear();
    }

    private class MyCountDownTimer extends CountDownTimer{

        public MyCountDownTimer(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long l) {

        }

        @Override
        public void onFinish() {
            if(isShowing()){
                dismiss();
            }
        }
    }


}
