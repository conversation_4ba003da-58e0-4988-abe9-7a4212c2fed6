package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.PointTaskInfo;
import com.sohuott.tv.vod.view.PointTaskView;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by wenjingbian on 2018/1/4.
 */

public class PointTaskPresenterImpl {

    private PointTaskView mPointTaskView;

    public void setView(PointTaskView pointTaskView) {
        this.mPointTaskView = pointTaskView;
    }

    public void requestTaskData(int taskId, String passport) {
        NetworkApi.getUserPointTask(taskId, passport, new Observer<PointTaskInfo>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(PointTaskInfo value) {
                LibDeprecatedLogger.d("requestRegularData(): onNext().");
                if (mPointTaskView == null) {
                    return;
                }

                if (value == null || value.getData() == null) {
                    mPointTaskView.displayErrorView();
                } else {
                    mPointTaskView.displayView(value.getData());
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestRegularData(): onError().");
                if (mPointTaskView != null) {
                    mPointTaskView.displayErrorView();
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestRegularData(): onComplete().");
            }
        });
    }
}
