package com.sohuott.tv.vod.partner;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.IBinder;
import androidx.annotation.Nullable;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

public class SohuSdkService extends Service {

    private SohuSdkBinder mSohuSdkBinder;
    private ServiceApiHelper mServiceApiHelper;

    public SohuSdkService() {
        mSohuSdkBinder = new SohuSdkBinder();
        mServiceApiHelper = new ServiceApiHelper(this);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        LibDeprecatedLogger.d("SohuSdkService: onBind()");
        return mSohuSdkBinder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        LibDeprecatedLogger.d("SohuSdkService: omUnbind()");
        return super.onUnbind(intent);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        LibDeprecatedLogger.d("SohuSdkService: onStartCommand()");
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LibDeprecatedLogger.d("SohuSdkService: onDestroy()");
        mSohuSdkBinder = null;
        mServiceApiHelper = null;
    }

    public ServiceApiHelper getServiceApiHelper() {
        return mServiceApiHelper;
    }

    public void resumeVideo() {
        LibDeprecatedLogger.d("Continue to play video.");
        if (mServiceApiHelper != null) {
            mServiceApiHelper.playerOnResume();
        }
    }

    public void stopVideo() {
        LibDeprecatedLogger.d("Stop play video.");
        if (mServiceApiHelper != null) {
            mServiceApiHelper.playerOnStop();
        }
    }

    public void pauseVideo() {
        LibDeprecatedLogger.d("Pause playing video.");
        if (mServiceApiHelper != null) {
            mServiceApiHelper.playerOnPause();
        }
    }

    public void fastSeek(int second) {
        LibDeprecatedLogger.d("Start to forward video, time is " + second);
        if (second <= 0) {
            LibDeprecatedLogger.e("Illegal time: " + second);
            return;
        }
        if (mServiceApiHelper != null) {
            mServiceApiHelper.playerOnFastSeek(second);
        }
    }

    public void seekTo(int second) {
        LibDeprecatedLogger.d("Start to seek to the pointed position, time is " + second);
        if (second == 0) {
            LibDeprecatedLogger.e("Illegal time: second is 0.");
            return;
        }
        if (mServiceApiHelper != null) {
            mServiceApiHelper.playerOnSeekTo(second);
        }
    }

    public class SohuSdkBinder extends Binder {

        public SohuSdkService getService() {
            return SohuSdkService.this;
        }
    }

}
