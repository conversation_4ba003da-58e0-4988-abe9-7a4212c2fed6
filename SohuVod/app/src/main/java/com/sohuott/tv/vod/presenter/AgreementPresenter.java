package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AgreementModel;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @Date Created on 2020/3/17.
 */
public class AgreementPresenter extends IBasePresenter {

    private String mName;

    private String mType;

    public static final String TYPE = "protocol";
    public static final String TYPE_JOIN = "join";

    public static final String NAME_USER = "user";
    public static final String NAME_FEE = "fee";
    public static final String NAME_PRIVACY = "privacy";
    public static final String NAME_USER_PRIVACY = "user_privacy";

    public AgreementPresenter(IDataListener listener, String name, String type) {
        super(listener);
        this.mName = name;
        this.mType = type == null ? TYPE : type;
    }

    @Override
    public void loadData() {
        LibDeprecatedLogger.d("Load agreement：" + mName);
        NetworkApi.requestAgreement(mName, mType, new Observer<AgreementModel>() {

            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(AgreementModel value) {
                if (value == null) {
                    LibDeprecatedLogger.w("Load agreement data fail!");
                } else if (value.getData() == null) {
                    LibDeprecatedLogger.w("Load agreement data error! " + value.getMessage());
                } else if (value.getData().size() <= 0) {
                    LibDeprecatedLogger.w("No agreement data! " + value.getMessage());
                } else {
                    LibDeprecatedLogger.d("Agreement data: " + value.getData().get(0).toString());
                    if (mListener != null) {
                        mListener.getData(value);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("Load agreement data error!", e);
            }

            @Override
            public void onComplete() {

            }
        });
    }
}
