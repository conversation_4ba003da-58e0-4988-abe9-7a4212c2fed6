package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.ToastUtils;

/**
 * Created by yizhang210244 on 2017/11/23.
 */

public class NewNetworkDialogActivity extends BaseActivity{
    private Button mConfirm;
    private Button mCancel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.network_dialog);
        initView();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    private void initView() {
        mConfirm = (Button) findViewById(R.id.ok);
        mConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    Intent intent = new Intent(Settings.ACTION_WIFI_SETTINGS);
                    startActivity(intent);
                }catch (Exception e){
                    ToastUtils.showToast(NewNetworkDialogActivity.this,"启动网络设置失败，请到您的设备的系统中连接网络");
                    LibDeprecatedLogger.e("can not start network setting");
                }
                finish();
            }
        });
        mCancel = (Button) findViewById(R.id.cancel);
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }


}
