package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ComingSoonModel;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.view.ComingSoonView;

import java.lang.ref.WeakReference;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by music on 17-4-7.
 */

public class ComingSoonPresenterImpl {

    private ComingSoonView mComingView;


    public ComingSoonPresenterImpl(ComingSoonView comingSoonView) {
        this.mComingView = new WeakReference<ComingSoonView>(comingSoonView).get();
    }

    public void loadComingSoonModel(int subjectId, String passport, String token) {
        NetworkApi.getComingSoonData(UrlWrapper.getComingSoonUrl(subjectId, passport, token),
                new DisposableObserver<ComingSoonModel>() {

                    @Override
                    public void onNext(ComingSoonModel value) {
                        LibDeprecatedLogger.d("loadComingSoonModel(): onNext()");
                        mComingView.addComingSoonData(value);
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.e("loadComingSoonModel(): onError()--" + e.getMessage());
                        mComingView.onError();
                    }

                    @Override
                    public void onComplete() {
                        LibDeprecatedLogger.d("loadComingSoonModel(): onComplete()");
                        mComingView.hideLoading();
                    }
                });
    }

}
