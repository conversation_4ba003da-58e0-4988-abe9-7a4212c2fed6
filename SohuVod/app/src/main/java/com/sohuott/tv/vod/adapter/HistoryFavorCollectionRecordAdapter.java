package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.sohuott.tv.vod.fragment.HistoryFavorCollectionFragment;
import com.sohuott.tv.vod.lib.db.greendao.Collection;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.AuditDenyAids;
import com.sohuott.tv.vod.lib.model.BookedRecord;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.VideoFavorListBean;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohu.lib_utils.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_BOOKED;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_COLLECTION;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_HISTORY;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.SPAN_COUNT;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by wenjingbian on 2017/3/22.
 */

public class HistoryFavorCollectionRecordAdapter
        extends RecyclerView.Adapter<HistoryFavorCollectionRecordAdapter.ListHistoryFavorCollectionRecordViewHolder> {

    public interface FocusController {
        void onFocusSelected(int position);
    }

    private static final int TYPE_PLAY_HISTORY = 1;
    private static final int TYPE_COLLECTION = 2;
    private static final int TYPE_BOOKED = 3;

    private static final int TAG_DEFAULT = -1;
    private static final int TAG_VRS = 0;
    private static final int TAG_VR = 1;
    private static final int TAG_PGC = 2;

    private HistoryFavorCollectionFragment mFragment;
    private Context mContext;
    private RecyclerView mRecyclerView;
    private FocusBorderView mFocusBorderView;  //焦点框

    private FocusController mFocusController;
    private PlayHistory mSelectedHistory;

    private List<?> mDataSource;
    private List<Integer> mDeletingAidList;
    private List<Integer> mCancelAidList;
    private List<AuditDenyAids.DataBean> mDenyAidsList;

    private CustomGridLayoutManager mLayoutManager;

    private LoginUserInformationHelper mHelper;

    private int mLeftTag;
    private boolean isShowDeleteView;
    private List<Integer> exposureIds = new ArrayList<>();

    /**
     * Constructor of HistoryFavorCollectionRecordAdapter
     *
     * @param fragment
     * @param recyclerView RecyclerView attached this adapter
     */
    public HistoryFavorCollectionRecordAdapter(HistoryFavorCollectionFragment fragment,
                                               RecyclerView recyclerView) {
        this.mFragment = fragment;
        this.mRecyclerView = recyclerView;
        this.mLayoutManager = (CustomGridLayoutManager) mRecyclerView.getLayoutManager();
        mDeletingAidList = new ArrayList<>();
        mCancelAidList = new ArrayList<>();
        mContext = fragment.getContext();
        mHelper = LoginUserInformationHelper.getHelper(mContext);
    }

    @Override
    public ListHistoryFavorCollectionRecordViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_list_history_favor_collection_new, parent, false);
        ListHistoryFavorCollectionRecordViewHolder viewHolder = new ListHistoryFavorCollectionRecordViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(final ListHistoryFavorCollectionRecordViewHolder holder, int position) {
        LibDeprecatedLogger.d("onBindViewHolder():" + holder.getLayoutPosition());
        if (mDataSource == null || mDataSource.size() <= 0) {
            return;
        }

        final Object data = mDataSource.get(holder.getAdapterPosition());
        if (isShowDeleteView) {
            holder.iv_delete_item.setVisibility(View.VISIBLE);
        } else {
            holder.iv_delete_item.setVisibility(View.GONE);
        }
        holder.iv_delete_cover.setVisibility(View.GONE);
        holder.iv_delete_progress.setVisibility(View.GONE);
        String uri = "";
        if (data instanceof PlayHistory) {
            PlayHistory playHistory = ((PlayHistory) data);
            String episode = playHistory.getEpisode();
            String tvName = playHistory.getTvName();
            switch (playHistory.getDataType()) {
                case TAG_VRS:
                    if (playHistory.getVideoHorPic() != null && playHistory.getVideoHorPic().length() > 0) {
                        uri = playHistory.getVideoHorPic();
                    }
                    Glide.with(mContext)
                            .load(TextUtils.isEmpty(uri) ? "" : Util.generateHorPicUrl(uri, 640, 360, 80))
                            .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                            .into(holder.ctiv_hfc_video_poster);
                    if (playHistory.getCategoryCode() == 115) { //少儿
                        holder.tv_hfc_title.setText(playHistory.getTvName() + "第" + playHistory.getVideoOrder() + "集");
                        holder.tv_hfc_focus_title.setText(playHistory.getTvName() + "第" + playHistory.getVideoOrder() + "集");
                    } else {
                        holder.tv_hfc_title.setText(TextUtils.isEmpty(episode) ? tvName : episode);
                        holder.tv_hfc_focus_title.setText(TextUtils.isEmpty(episode) ? tvName : episode);
                    }
                    break;
                case TAG_VR:
                    if (playHistory.getVideoHorPic() != null && playHistory.getVideoHorPic().length() > 0) {
                        uri = playHistory.getVideoHorPic();
                    }
                    Glide.with(mContext)
                            .load(TextUtils.isEmpty(uri) ? "" : Util.generateHorPicUrl(uri, 640, 360, 80))
                            .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                            .into(holder.ctiv_hfc_video_poster);
                    holder.tv_hfc_title.setText(tvName);
                    break;
                case TAG_PGC:
                case TAG_DEFAULT:
                    uri = playHistory.getVideoHorPic();
                    Glide.with(mContext)
                            .load(TextUtils.isEmpty(uri) ? "" : Util.generateHorPicUrl(uri, 640, 360, 80))
                            .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                            .into(holder.ctiv_hfc_video_poster);
                    holder.tv_hfc_title.setText(episode);
                    holder.tv_hfc_focus_title.setText(episode);
                    holder.tv_hfc_sets.setText("");
                    break;
                default:
                    break;
            }
            int tvIsFee = playHistory.getFee() == null ? 0 : playHistory.getFee();
            int tvIsEarly = playHistory.getTvIsEarly() == null ? 0 : playHistory.getTvIsEarly();
            int useTicket = playHistory.getUseTicket() == null ? 0 : playHistory.getUseTicket();
            int single = playHistory.getPaySeparate() == null ? 0 : playHistory.getPaySeparate();
            int cornerType = playHistory.getCornerType() == null ? 0 : playHistory.getCornerType();
            holder.ctiv_hfc_video_poster.setCornerTypeWithType(tvIsFee, tvIsEarly, useTicket, single, cornerType);
            handleWatchTime(playHistory, holder.tv_hfc_sub_title);
            handleWatchTime(playHistory, holder.tv_hfc_focus_sub_title);
            holder.tv_hfc_focus_sub_title.setVisibility(View.VISIBLE);
            holder.tv_hfc_sub_title.setVisibility(View.VISIBLE);
            handleTvSets(playHistory.getCategoryCode() == null ? 0 : playHistory.getCategoryCode(), playHistory.getTvSets() == null ? 0 : playHistory.getTvSets(), playHistory.getTvSetNow() == null ? 0 : playHistory.getTvSetNow(), holder.tv_hfc_sets);
            exposureHistory(playHistory, position);
        } else if (data instanceof VideoFavorListBean.DataEntity.ResultEntity) {
            VideoFavorListBean.DataEntity.ResultEntity favorObj = (VideoFavorListBean.DataEntity.ResultEntity) data;
            holder.tv_hfc_title.setText(favorObj.tvName);
            holder.tv_hfc_sub_title.setText(favorObj.likeCount + mContext.getResources().getString(R.string.txt_fragment_favor_count_suf));
            Glide.with(mContext)
                    .load(Util.generateHorPicUrl(favorObj.tvVerPic, 640, 360, 80))
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(holder.ctiv_hfc_video_poster);
        } else if (data instanceof Collection) {
            Collection collection = (Collection) data;
            String tvName = collection.getTvName();
            holder.tv_hfc_title.setText(tvName);
            Glide.with(mContext)
                    .load(Util.generateHorPicUrl(collection.getAlbumExtendsPic_640_360(), 640, 360, 80))
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(holder.ctiv_hfc_video_poster);
            holder.tv_hfc_focus_title.setText(tvName);
            String episode = collection.getTvDesc();
            holder.tv_hfc_focus_sub_title.setVisibility(View.GONE);
            holder.tv_hfc_sub_title.setText("");
            holder.tv_hfc_sub_title.setVisibility(View.GONE);
            int tvIsFee = collection.getTvIsFee() == null ? 0 : collection.getTvIsFee();
            int tvIsEarly = collection.getTvIsEarly() == null ? 0 : collection.getTvIsEarly();
            int useTicket = collection.getUseTicket() == null ? 0 : collection.getUseTicket();
            int single = collection.getPaySeparate() == null ? 0 : collection.getPaySeparate();
            int cornerType = collection.getCornerType() == null ? 0 : collection.getCornerType();
            holder.ctiv_hfc_video_poster.setCornerTypeWithType(tvIsFee, tvIsEarly, useTicket, single, cornerType);
            handleTvSets(collection.getCateCode(), collection.getTvSets() == null ? 0 : Integer.parseInt(collection.getTvSets()), collection.getLatestVideoCount() == null ? 0 : Integer.parseInt(collection.getLatestVideoCount()), holder.tv_hfc_sets);
        } else if (data instanceof BookedRecord.DataBean) {
            BookedRecord.DataBean bookedObj = (BookedRecord.DataBean) data;
            holder.tv_hfc_title.setText(bookedObj.getAlbumInfoResponse().getTvName());
            holder.tv_hfc_sub_title.setText("");
            holder.tv_hfc_sub_title.setVisibility(View.GONE);
            holder.tv_hfc_focus_title.setText(bookedObj.getAlbumInfoResponse().getTvName());
            holder.tv_hfc_focus_sub_title.setText("");
            holder.tv_hfc_focus_sub_title.setVisibility(View.GONE);
            holder.tv_hfc_sets.setText(bookedObj.getTitle());
            int tvIsFee = bookedObj.getAlbumInfoResponse().getTvIsFee();
            int tvIsEarly = bookedObj.getAlbumInfoResponse().getTvIsEarly();
            int useTicket = bookedObj.getAlbumInfoResponse().getUseTicket();
            int single = bookedObj.getAlbumInfoResponse().getPaySeparate();
            int cornerType = bookedObj.getAlbumInfoResponse().getCornerType();
            holder.ctiv_hfc_video_poster.setCornerTypeWithType(tvIsFee, tvIsEarly, useTicket, single, cornerType);
            Glide.with(mContext)
                    .load(Util.generateHorPicUrl(bookedObj.getAlbumInfoResponse().getAlbumExtendsPic_640_360(), 640, 360, 80))
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(holder.ctiv_hfc_video_poster);
        } else {
            holder.tv_hfc_title.setText("");
            holder.tv_hfc_sets.setText("");
            holder.ctiv_hfc_video_poster.resetImageRes();
            holder.tv_hfc_sub_title.setText("");
            holder.tv_hfc_focus_sub_title.setVisibility(View.GONE);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (holder.iv_delete_item.getVisibility() == View.VISIBLE) {
                    LibDeprecatedLogger.d("request to remove item: " + holder.getLayoutPosition());
                    int dataType;
                    int id;
                    if (data instanceof PlayHistory) {
                        dataType = ((PlayHistory) data).getDataType();
                        id = dataType == 0 ? ((PlayHistory) data).getAlbumId() : ((PlayHistory) data).getVideoId();
                        clearHistoryData(dataType, id, ((PlayHistory) data).getAlbumId(), ((PlayHistory) data).getVideoId());
                        mDeletingAidList.add(id);
                        holder.iv_delete_item.setVisibility(View.GONE);
                        holder.iv_delete_cover.setVisibility(View.VISIBLE);
                        holder.iv_delete_progress.setVisibility(View.VISIBLE);
                        RequestManager.getInstance().onHistoryDeleteItemEvent(dataType, id);
                    } else if (data instanceof Collection) {
                        Collection collection = (Collection) data;
                        String latestVideoSet = collection.getLatestVideoCount();
                        String tvSets = collection.getTvSets();
                        if (latestVideoSet != null) {
                            if (TextUtils.equals(latestVideoSet, tvSets)) {
                                clearCollectionData(collection.getAlbumId(), false);
                            } else {
                                clearCollectionData(collection.getAlbumId(), true);
                            }
                            mCancelAidList.add(collection.getAlbumId());
                            holder.iv_delete_item.setVisibility(View.GONE);
                            holder.iv_delete_cover.setVisibility(View.VISIBLE);
                            holder.iv_delete_progress.setVisibility(View.VISIBLE);
                        }
                        RequestManager.getInstance().onCollectionCancelItemEvent(((Collection) data).getAlbumId());
                    } else if (data instanceof BookedRecord.DataBean) {
                        //delete booked video by trailerId when video has gone live.
                        BookedRecord.DataBean dataBean = (BookedRecord.DataBean) data;
                        if (dataBean.getType() == 1) {
                            clearBookedData(String.valueOf(((BookedRecord.DataBean) data).getAlbumInfoResponse().getTrailerId()));
                            mCancelAidList.add(dataBean.getAlbumInfoResponse().getTrailerId());
                        } else {
                            clearBookedData(String.valueOf(((BookedRecord.DataBean) data).getAlbumInfoResponse().getId()));
                            mCancelAidList.add(dataBean.getAlbumInfoResponse().getId());
                        }
                        holder.iv_delete_item.setVisibility(View.GONE);
                        holder.iv_delete_cover.setVisibility(View.VISIBLE);
                        holder.iv_delete_progress.setVisibility(View.VISIBLE);
                        if (dataBean.getType() == 1) {
                            RequestManager.getInstance().onBookedCancelItemEvent(((BookedRecord.DataBean) data).getAlbumInfoResponse().getTrailerId());
                        } else {
                            RequestManager.getInstance().onBookedCancelItemEvent(((BookedRecord.DataBean) data).getAlbumInfoResponse().getId());
                        }
                    }
                } else if (holder.iv_delete_progress.getVisibility() == View.VISIBLE || holder.iv_delete_cover.getVisibility() == View.VISIBLE) {
                    //do nothing
                } else {
                    if (data instanceof PlayHistory) {
                        PlayHistory playhistory = (PlayHistory) data;
                        if (playhistory.getIsAudit() != null && playhistory.getIsAudit() == 0) {
                            ToastUtils.showToast(mContext, "该片已下线，请观看其他影片。");
                            return;
                        }
                        if (mDenyAidsList != null) {
                            for (int i = 0; i < mDenyAidsList.size(); i++) {
                                AuditDenyAids.DataBean data = mDenyAidsList.get(i);
                                if (playhistory.getDataType() == 0) {
                                    if (data.getAid() != null && data.getAid().equals(StringUtil.toString(playhistory.getAlbumId()))) {
                                        ToastUtils.showToast(mContext, "该片已下线，请观看其他影片。");
                                        return;
                                    }
                                } else if (playhistory.getDataType() == 2) {
                                    if (data.getVid() != null && data.getVid().equals(StringUtil.toString(playhistory.getVideoId()))) {
                                        ToastUtils.showToast(mContext, "该片已下线，请观看其他影片。");
                                        return;
                                    }
                                }
                            }

                        }
                        LibDeprecatedLogger.d("to VideoDetailActivity from history page.");
                        int dataType = ((PlayHistory) data).getDataType();
                        int aid = ((PlayHistory) data).getDataType() == TAG_VRS ? ((PlayHistory) data).getAlbumId() : ((PlayHistory) data).getVideoId();
                        int vid = ((PlayHistory) data).getDataType() == TAG_VRS ? ((PlayHistory) data).getVideoId() : ((PlayHistory) data).getAlbumId();
                        int secondCategoryCode = ((PlayHistory) data).getDataType() == TAG_VRS ? 0 : ((PlayHistory) data).getSecondCategoryCode();
                        ActivityLauncher.startVideoDetailDts(mContext, Constant.PAGE_HISTORY_RECORD ,aid, vid, dataType, false, secondCategoryCode);
                        mSelectedHistory = (PlayHistory) data;
                        RequestManager.getInstance().onHistoryListClickEvent(dataType, aid);
                    } else if (data instanceof VideoFavorListBean.DataEntity.ResultEntity) {
                        LibDeprecatedLogger.d("to VideoDetailActivity from favor page.");
                        ActivityLauncher.startVideoDetailActivity(mContext,
                                ((VideoFavorListBean.DataEntity.ResultEntity) data).id, Constant.PAGE_FAVOR_RECORD);
                        RequestManager.getInstance().onFavorListClickEvent(((VideoFavorListBean.DataEntity.ResultEntity) data).id);
                    } else if (data instanceof Collection) {
                        Collection collection = (Collection) data;
                        if (collection.getIsAudit() != null && collection.getIsAudit() == 0) {
                            ToastUtils.showToast(mContext, "该片已下线，请观看其他影片。");
                            return;
                        }
                        if (mDenyAidsList != null) {
                            for (int i = 0; i < mDenyAidsList.size(); i++) {
                                if (mDenyAidsList.get(i).getAid().equals(StringUtil.toString(collection.getAlbumId()))) {
                                    ToastUtils.showToast(mContext, "该片已下线，请观看其他影片。");
                                    return;
                                }
                            }
                        }
                        LibDeprecatedLogger.d("to VideoDetailActivity from collection page.");
                        ActivityLauncher.startVideoDetailActivity(mContext, ((Collection) data).getAlbumId(), Constant.PAGE_COLLECTION_RECORD);
                        RequestManager.getInstance().onCollectionListClickEvent(((Collection) data).getAlbumId());
                    } else if (data instanceof BookedRecord.DataBean) {
                        LibDeprecatedLogger.d("to VideoDetailActivity from booked video page.");
                        ActivityLauncher.startVideoDetailActivity(mContext, ((BookedRecord.DataBean) data).getAlbumInfoResponse().getId(), Constant.PAGE_BOOKED_RECORD);
                        RequestManager.getInstance().onBookedListClickEvent(((BookedRecord.DataBean) data).getAlbumInfoResponse().getId());
                    }
                }
            }
        });
    }

    private void exposureHistory(PlayHistory playHistory, int position) {
        if (exposureIds.contains(playHistory.getVideoId())) {
            return;
        }
        HashMap<String, String> pathInfo = new HashMap<>();
        pathInfo.put("pageId", "10028");
        pathInfo.put("columnId", "1010");
        pathInfo.put("indexId", String.valueOf(position + 1));
        HashMap<String, String> objectInfo = new HashMap<>();
        objectInfo.put("type", "视频");
        objectInfo.put("vid", StringUtil.toString(playHistory.getVideoId()));
        objectInfo.put("playlistId", StringUtil.toString(playHistory.getAlbumId()));
        HashMap<String, String> memoInfo = new HashMap<>();
        memoInfo.put("site", playHistory.getDataType() == 0 ? "1" : "2");
        RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"), pathInfo, objectInfo, memoInfo);
        exposureIds.add(playHistory.getVideoId());
    }

    private void handleWatchTime(PlayHistory playHistory, TextView textView) {
        int watchTime = playHistory.getWatchTime() == null ? 0 : playHistory.getWatchTime();
        int tvLength = playHistory.getTvLength() == null ? 0 : playHistory.getTvLength();
        int order = playHistory.getVideoOrder() == null ? 0 : playHistory.getVideoOrder();
        int sets = playHistory.getTvSets() == null ? 0 : playHistory.getTvSets();
        if (watchTime == 0) {
            if (order > 0) {
                textView.setText(mContext.getResources().getString(R.string.txt_activity_user_related_order_current)
                        + order
                        + (playHistory.getCategoryCode() == 106
                        ? mContext.getResources().getString(R.string.txt_activity_user_related_term_suf)
                        : mContext.getResources().getString(R.string.txt_activity_user_related_set_suf))
                        + mContext.getResources().getString(R.string.txt_fragment_history_record_done));
            } else {
                textView.setText(mContext.getResources().getString(R.string.txt_fragment_history_record_done));
            }
        } else {
            float percent = watchTime * 1f / tvLength;
            if (percent < 0.01f) {
                if (order > 0) {
                    textView.setText(mContext.getResources().getString(R.string.txt_fragment_history_record_more_one)
                            + order
                            + (playHistory.getCategoryCode() == 106
                            ? mContext.getResources().getString(R.string.txt_activity_user_related_term_suf)
                            : mContext.getResources().getString(R.string.txt_activity_user_related_set_suf))
                            +
                            mContext.getResources().getString(R.string.txt_fragment_history_record_less_one));
                } else {
                    textView.setText(mContext.getResources().getString(R.string.txt_fragment_history_record_less_one));
                }

            } else {
                if (order > 0) {
                    textView.setText(mContext.getResources().getString(R.string.txt_fragment_history_record_more_one)
                            + order
                            + (playHistory.getCategoryCode() == 106
                            ? mContext.getResources().getString(R.string.txt_activity_user_related_term_suf)
                            : mContext.getResources().getString(R.string.txt_activity_user_related_set_suf))
                            + Math.round(percent * 100) + "%");
                } else {
                    textView.setText(mContext.getResources().getString(R.string.txt_fragment_history_record_more_one)
                            + Math.round(percent * 100) + "%");
                }
            }
        }
    }


    private void handleTvSets(int cateCode, int tvSets, int tvSetNow, TextView textView) {
        textView.setText("");
        if (cateCode == 100) {
            return;
        }
        if (tvSets != 0) {
            if (tvSetNow == tvSets) {
                textView.setText(
                        tvSets + (cateCode == 106
                                ? mContext.getResources().getString(R.string.txt_activity_user_related_term_suf)
                                : mContext.getResources().getString(R.string.txt_activity_user_related_set_suf))
                                + mContext.getResources().getString(R.string.txt_activity_user_related_set_pre_all));
            } else {
                if (tvSetNow != 0) {
                    textView.setText(mContext.getResources().getString(R.string.txt_activity_user_related_set_pre_update)
                            + tvSetNow + (cateCode == 106
                            ? mContext.getResources().getString(R.string.txt_activity_user_related_term_suf)
                            : mContext.getResources().getString(R.string.txt_activity_user_related_set_suf)));
                }
            }
        } else {
            if (tvSetNow != 0) {
                textView.setText(mContext.getResources().getString(R.string.txt_activity_user_related_set_pre_update)
                        + tvSetNow + (cateCode == 106
                        ? mContext.getResources().getString(R.string.txt_activity_user_related_term_suf)
                        : mContext.getResources().getString(R.string.txt_activity_user_related_set_suf)));
            }
        }
    }

    @Override
    public int getItemCount() {
        return mDataSource != null ? mDataSource.size() : 0;
    }

    /**
     * Update history record view to remove the deleted one
     *
     * @param id the deleted record's id
     */
    public void updateHistoryRecordView(int id) {
        removeItemView(id, TYPE_PLAY_HISTORY);
    }

    /**
     * UUpdate history record view and notify user fail to delete record item
     *
     * @param id the deleted record's id
     */
    public void updateHistoryRecordFailedView(int id) {
        if (mDeletingAidList.contains(id)) {
            mDeletingAidList.remove(mDeletingAidList.indexOf(id));
        }

        int itemPosition = getDataIndex(id);
        if (itemPosition < 0) {
            return;
        }

        ListHistoryFavorCollectionRecordViewHolder viewHolder =
                (ListHistoryFavorCollectionRecordViewHolder) mRecyclerView.findViewHolderForAdapterPosition(itemPosition);
        viewHolder.iv_delete_item.setVisibility(View.VISIBLE);
        viewHolder.iv_delete_progress.setVisibility(View.GONE);
        viewHolder.iv_delete_cover.setVisibility(View.GONE);
        ToastUtils.showToast(mContext, mContext.getResources().getString(R.string.txt_fragment_history_record_delete_fail));
    }


    /**
     * Update collection record view to remove the canceled one
     *
     * @param id the canceled record's id
     */
    public void updateCollectionRecordView(int id) {
        removeItemView(id, TYPE_COLLECTION);
    }

    /**
     * Update collection record view and notify user fail to canceled record item
     *
     * @param id the canceled record's id
     */
    public void updateCollectionRecordFailedView(int id) {
        if (mCancelAidList.contains(id)) {
            mCancelAidList.remove(mCancelAidList.indexOf(id));
        }

        int itemPosition = getDataIndex(id);
        if (itemPosition < 0) {
            return;
        }

        ListHistoryFavorCollectionRecordViewHolder viewHolder =
                (ListHistoryFavorCollectionRecordViewHolder) mRecyclerView.findViewHolderForAdapterPosition(itemPosition);
        viewHolder.iv_delete_item.setVisibility(View.VISIBLE);
        viewHolder.iv_delete_progress.setVisibility(View.GONE);
        viewHolder.iv_delete_cover.setVisibility(View.GONE);
        ToastUtils.showToast(mContext, mContext.getResources().getString(R.string.txt_fragment_collection_delete_fail));
    }

    public void updateBookedRecordView(int id) {
        removeItemView(id, TYPE_BOOKED);
    }

    public void updateBookedRecordFailedView(int id) {
        if (mCancelAidList.contains(id)) {
            mCancelAidList.remove(mCancelAidList.indexOf(id));
        }

        int itemPosition = getDataIndex(id);
        if (itemPosition < 0) {
            return;
        }

        ListHistoryFavorCollectionRecordViewHolder viewHolder =
                (ListHistoryFavorCollectionRecordViewHolder) mRecyclerView.findViewHolderForAdapterPosition(itemPosition);
        viewHolder.iv_delete_item.setVisibility(View.VISIBLE);
        viewHolder.iv_delete_progress.setVisibility(View.GONE);
        viewHolder.iv_delete_cover.setVisibility(View.GONE);
        ToastUtils.showToast(mContext, mContext.getResources().getString(R.string.txt_fragment_booked_delete_fail));
    }

    public void setOnFocusSelected(FocusController focusController) {
        this.mFocusController = focusController;
    }

    public void setLeftTag(int leftTag) {
        this.mLeftTag = leftTag;
    }

    public int getLeftTag() {
        return mLeftTag;
    }

    public void setDataSource(List<?> dataSource) {
        this.mDataSource = dataSource;
    }

    public void addMoreHistories(List<PlayHistory> historyList) {
        if (mDataSource != null) {
            int start = mDataSource.size();
            ((List<PlayHistory>) mDataSource).addAll(historyList);
            notifyItemRangeInserted(start, historyList.size());
        }
    }

    public void addMoreCollections(List<Collection> collectionList) {
        if (mDataSource != null) {
            int start = mDataSource.size();
            ((List<Collection>) mDataSource).addAll(collectionList);
            notifyItemRangeInserted(start, collectionList.size());
        }
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        mFocusBorderView = focusView;
        if (mFocusBorderView == null) {
            return;
        }
        mFocusBorderView.setDrawable(R.drawable.bg_hfc_border_focus);
    }

    public void isShowDeleteView(boolean isShowDeleteView) {
        this.isShowDeleteView = isShowDeleteView;
    }

    public void setDenyAids(List<AuditDenyAids.DataBean> denyAidsList) {
        this.mDenyAidsList = denyAidsList;
    }

    public PlayHistory getSelectedHistory() {
        return mSelectedHistory;
    }

    public void releaseAll() {
        mContext = null;
        mRecyclerView = null;
        mFocusController = null;
        mFragment = null;
        mHelper = null;
        if (mDataSource != null) {
            mDataSource.clear();
            mDataSource = null;
        }
        if (mCancelAidList != null) {
            mCancelAidList.clear();
            mCancelAidList = null;
        }
        if (mDeletingAidList != null) {
            mDeletingAidList.clear();
            mDeletingAidList = null;
        }
        FocusUtil.clearAnimation();
    }

    /**
     * Remove item view from RecordRecyclerView
     *
     * @param id       record's id you want to remove
     * @param dataType record's type you want to remove
     */
    private void removeItemView(int id, int dataType) {
        //get item position
        int itemPosition = getDataIndex(id);
        if (itemPosition < 0) {
            return;
        }

        //request focus
        if (getItemCount() > 1) {
            if (itemPosition == 0) {
                mRecyclerView.getChildAt(1).requestFocus();
            } else {
                mRecyclerView.findViewHolderForAdapterPosition(itemPosition - 1).itemView.requestFocus();
            }
        }

        //update data source
        mDataSource.remove(itemPosition);

        //remove item
        notifyItemRemoved(itemPosition);

        if (dataType == TYPE_PLAY_HISTORY) {
            //remove data from mDeletingAidList
            if (mDeletingAidList.contains(id)) {
                mDeletingAidList.remove(mDeletingAidList.indexOf(id));
            }
            //Display empty view when count is zero.
            if (getItemCount() == 0) {
                mFocusController.onFocusSelected(LIST_INDEX_HISTORY);
                mFragment.fromDeleteButton = true;
                mFragment.displayEmptyView(LIST_INDEX_HISTORY);
            }
        } else if (dataType == TYPE_COLLECTION) {
            //remove data from mCancelAidList
            if (mCancelAidList.contains(id)) {
                mCancelAidList.remove(mCancelAidList.indexOf(id));
            }
            //Display empty view when count is zero.
            if (getItemCount() == 0) {
                mFocusController.onFocusSelected(LIST_INDEX_COLLECTION);
                mFragment.displayEmptyView(LIST_INDEX_COLLECTION);
            }
        } else if (dataType == TYPE_BOOKED) {
            if (mCancelAidList.contains(id)) {
                mCancelAidList.remove(mCancelAidList.indexOf(id));
            }
            //Display empty view when count is zero.
            if (getItemCount() == 0) {
                mFocusController.onFocusSelected(LIST_INDEX_BOOKED);
                mFragment.displayEmptyView(LIST_INDEX_BOOKED);
            }
        }
    }

    /**
     * Get data's index in mDataSource
     *
     * @param id video id or album id you want to get its position
     * @return data's index, default value is -1
     */
    public int getDataIndex(int id) {
        switch (mLeftTag) {
            case LIST_INDEX_HISTORY:
                List<PlayHistory> playHistoryList = (List<PlayHistory>) mDataSource;
                for (PlayHistory playHistory : playHistoryList) {
                    if ((playHistory.getDataType() == 0 ? playHistory.getAlbumId() : playHistory.getVideoId()) == id) {
                        return mDataSource.indexOf(playHistory);
                    }
                }
                break;
            case LIST_INDEX_COLLECTION:
                List<Collection> collectionList = (List<Collection>) mDataSource;
                for (Collection collection : collectionList) {
                    if (collection.getAlbumId() == id) {
                        return mDataSource.indexOf(collection);
                    }
                }
                break;
            case LIST_INDEX_BOOKED:
                List<BookedRecord.DataBean> dataBeanList = (List<BookedRecord.DataBean>) mDataSource;
                int tmpId;
                for (BookedRecord.DataBean dataBean : dataBeanList) {
                    if (dataBean.getType() == 1) {
                        tmpId = dataBean.getAlbumInfoResponse().getTrailerId();
                    } else {
                        tmpId = dataBean.getAlbumInfoResponse().getId();
                    }
                    if (tmpId == id) {
                        return mDataSource.indexOf(dataBean);
                    }
                }
                break;
            default:
                break;
        }
        return -1;
    }

    /**
     * Request to clear history record
     *
     * @param dataType item's data type you want to clear
     * @param id       video id or album id you want to clear
     * @param aid      album id you want to clear
     * @param vid      video id you want to clear
     */
    private void clearHistoryData(int dataType, int id, int aid, int vid) {
        if (mDeletingAidList == null) {
            mDeletingAidList = new ArrayList<>();
        }
        if (!mDeletingAidList.contains(id)) {
            mDeletingAidList.add(id);
            mFragment.getHfcRecordViewPresenterImpl().clearHistoryRecord(dataType, id, aid, vid, false);
        }
    }

    /**
     * Request to clear collection record
     *
     * @param aid
     */
    private void clearCollectionData(int aid, boolean isChased) {
        mFragment.getHfcRecordViewPresenterImpl().clearCollectionRecordItem(aid, isChased, false);
    }

    private void clearBookedData(String aid) {
        mFragment.getHfcRecordViewPresenterImpl().clearBookedRecord(mHelper.getLoginPassport(), aid, mHelper.getLoginToken());
    }

    /**
     * Convert integer value to string value
     *
     * @param integer integer value you want to convert
     * @return string value has been converted.
     */
    private String convertIntegerToString(int integer) {
        if (integer < 0) {
            return null;
        } else if (integer < 10) {
            return "0" + integer;
        } else {
            return String.valueOf(integer);
        }
    }

    /**
     * Custom ViewHolder of RecordRecyclerView
     */
    class ListHistoryFavorCollectionRecordViewHolder extends RecyclerView.ViewHolder {

        CornerTagImageView ctiv_hfc_video_poster; //Poster
        ImageView iv_delete_item; //delete icon
        ImageView iv_delete_cover; //deleting item cover
        ImageView iv_delete_progress; //deleting item icon
        TextView tv_hfc_title; //main title on the poster
        TextView tv_hfc_sub_title; //sub title on the poster
        TextView tv_hfc_sets; //sets
        RelativeLayout rl_hfc_poster_root; //root
        LinearLayout rl_hfc_focus_desc; //focus desc
        RippleDiffuse hfc_focus_play; //play
        TextView tv_hfc_focus_title, tv_hfc_focus_sub_title; //focus title
        ImageView bg_tv_sets;


        public ListHistoryFavorCollectionRecordViewHolder(final View itemView) {
            super(itemView);

            ctiv_hfc_video_poster = (CornerTagImageView) itemView.findViewById(R.id.ctiv_hfc_video_poster);
            iv_delete_item = (ImageView) itemView.findViewById(R.id.iv_delete_item);
            tv_hfc_title = (TextView) itemView.findViewById(R.id.tv_item_hfc_title);
            tv_hfc_sub_title = (TextView) itemView.findViewById(R.id.tv_item_hfc_sub_title);
            ctiv_hfc_video_poster.setCornerHeight(mContext.getResources().getDimensionPixelSize(R.dimen.y40));
            iv_delete_cover = (ImageView) itemView.findViewById(R.id.delete_progress_cover);
            iv_delete_progress = (ImageView) itemView.findViewById(R.id.delete_process);
            tv_hfc_sets = (TextView) itemView.findViewById(R.id.tv_hfc_sets);
            rl_hfc_poster_root = (RelativeLayout) itemView.findViewById(R.id.layout_hfc_item_poster_root);
            rl_hfc_focus_desc = (LinearLayout) itemView.findViewById(R.id.layout_hfc_focus_desc);
            hfc_focus_play = (RippleDiffuse) itemView.findViewById(R.id.hfc_focus_play);
            tv_hfc_focus_title = (TextView) itemView.findViewById(R.id.tv_hfc_focus_title);
            tv_hfc_focus_sub_title = (TextView) itemView.findViewById(R.id.tv_hfc_focus_sub_title);
            bg_tv_sets = (ImageView) itemView.findViewById(R.id.bg_tv_sets);

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    tv_hfc_title.setSelected(hasFocus);
                    if (hasFocus) {
                        handleFocus(v);
                    } else {
                        handleUnfocused(v);
                    }
                }
            });

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    int itemIndex = getAdapterPosition();
                    if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && event.getAction() == KeyEvent.ACTION_DOWN) {
                        LibDeprecatedLogger.d("LEFT " + itemIndex);
                        if (itemIndex % SPAN_COUNT == 0) {
                            mFocusController.onFocusSelected(mLeftTag);
                        }
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && event.getAction() == KeyEvent.ACTION_DOWN) {
                        //Automatically focus next view when the current focus one is the last of row
                        if (itemIndex == (getItemCount() - 1)) {
                            return true;
                        }

                        if (itemIndex == mLayoutManager.findLastCompletelyVisibleItemPosition()
                                || itemIndex == mLayoutManager.findLastCompletelyVisibleItemPosition() - SPAN_COUNT) {
                            if (mRecyclerView.findViewHolderForAdapterPosition(itemIndex + 1) != null) {
                                mRecyclerView.findViewHolderForAdapterPosition(itemIndex + 1).itemView.requestFocus();
                                return true;
                            }
                        }
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                        int sumColumn = getItemCount() % SPAN_COUNT == 0 ? getItemCount() / SPAN_COUNT : getItemCount() / SPAN_COUNT + 1;
                        int currColumn = (itemIndex + 1) % SPAN_COUNT == 0 ? (itemIndex + 1) / SPAN_COUNT : (itemIndex + 1) / SPAN_COUNT + 1;
                        LibDeprecatedLogger.d("sum = " + sumColumn + ", curr = " + currColumn);
                        if (sumColumn == currColumn) {
                            return true;
                        }
                    } else if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
                        mFocusController.onFocusSelected(mLeftTag);
                        return true;
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
                        //如果是第一行，长视频、全部历史恢复focus
                        if (itemIndex / SPAN_COUNT == 0) {
                            mFragment.refocusFilterButton();
                            return true;
                        }
                    }
                    return false;
                }
            });
        }

        private void handleUnfocused(final View v) {
            rl_hfc_poster_root.setBackgroundResource(R.color.transparent);
            rl_hfc_focus_desc.setBackgroundResource(R.color.bg_channel_list_focus);
            bg_tv_sets.setImageResource(R.drawable.bg_item_type_one);
            rl_hfc_focus_desc.setVisibility(View.INVISIBLE);
            hfc_focus_play.cancelWaveAnimation();
            hfc_focus_play.setVisibility(View.GONE);
            tv_hfc_title.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(tv_hfc_sub_title.getText())) {
                tv_hfc_sub_title.setVisibility(View.VISIBLE);
            }
            FocusUtil.setUnFocusAnimator(v);
        }

        private void handleFocus(final View v) {
            rl_hfc_poster_root.setBackgroundResource(R.drawable.bg_hfc_focus);
            rl_hfc_focus_desc.setBackgroundResource(R.color.transparent);
            bg_tv_sets.setImageResource(R.drawable.bg_item_type_one_no_radius);
            rl_hfc_focus_desc.setVisibility(View.VISIBLE);
            hfc_focus_play.setVisibility(View.VISIBLE);
            hfc_focus_play.showWaveAnimation();
            tv_hfc_title.setVisibility(View.GONE);
            tv_hfc_sub_title.setVisibility(View.GONE);
            FocusUtil.setFocusAnimator(v, 1.07f, 200);
        }
    }
}
