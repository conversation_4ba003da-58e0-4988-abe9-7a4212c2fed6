package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.sohuott.tv.vod.activity.TempletActivity;
import com.sohuott.tv.vod.fragment.TempletFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.MenuListBean;
import com.sohuott.tv.vod.lib.widgets.VerticalViewPager;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;

/**
 * Created by music on 17-9-9.
 */

public class TempletPagerAdapter extends FragmentPagerAdapter implements TempletItemAdapter.OnKeyChange {

    private HashMap<Integer, WeakReference<TempletFragment>> mFragmengs = new HashMap<>();

    private List<MenuListBean.MenuDate> menuDataList;

    private FocusBorderView focusBorderView;

    private Context mContext;

    private VerticalViewPager mVerticalViewPager;

    private int mPage = 0, mPlayingPage = 0, mLastPlayingPage = -1;
    private int mPosition = 0;
    private boolean mIsPgc;

    public TempletPagerAdapter(FragmentManager fm, boolean isPgc) {
        super(fm);
        this.mIsPgc = isPgc;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.focusBorderView = focusBorderView;
    }

    public void setmVerticalViewPager(VerticalViewPager mVerticalViewPager) {
        this.mVerticalViewPager = mVerticalViewPager;
    }

    public void setMenuDataList(List<MenuListBean.MenuDate> menuDataList) {
        this.menuDataList = menuDataList;
    }

    public void setmContext(Context mContext) {
        this.mContext = mContext;
    }

    public void releaseAll(){
        if (menuDataList != null) {
            menuDataList.clear();
            menuDataList = null;
        }
        mContext = null;
        mVerticalViewPager = null;
    }

    @Override
    public Fragment getItem(int position) {
        WeakReference<TempletFragment> fragmentWeakReference = mFragmengs.get(new Integer(position));
        TempletFragment fragment = null;
        if (fragmentWeakReference != null) {
            fragment = fragmentWeakReference.get();
        }
        LibDeprecatedLogger.d("fragment = " + fragment + ", fragments size = " + mFragmengs.size()
                + ", pos = " + position);
        if (fragment == null) {
            fragment = TempletFragment.newInstance(getCount() - 1, position, menuDataList.get(position).id, mIsPgc);
            fragment.setOnkeyChange(this);
            mFragmengs.put(new Integer(position), new WeakReference<TempletFragment>(fragment));
        }
        return fragment;
    }

    @Override
    public int getCount() {
        return menuDataList != null ? menuDataList.size() : 0;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        LibDeprecatedLogger.d("destroyItem , fragment size = " + mFragmengs.size() + ", pos = " + position);
        super.destroyItem(container, position, object);
        mFragmengs.remove(position);
    }

    public void setmPage(int mPage) {
        this.mPage = mPage;
    }

    public void setCurrentPlaytingSelected() {
        if (mFragmengs.size() != 0) {
            TempletFragment templetFragment = mFragmengs.get(new Integer(mPage)).get();
            if (templetFragment != null) {
                templetFragment.setSelected();
            }
        }
    }

    public void setSelected(int page) {
        if (mFragmengs.size() != 0) {
            TempletFragment templetFragment = mFragmengs.get(new Integer(page)).get();
            if (templetFragment != null) {
                templetFragment.setSelected();
            }
        }
    }

    public void clearSelected(int page) {
        if (mFragmengs.size() != 0) {
            TempletFragment templetFragment = mFragmengs.get(new Integer(page)).get();
            if (templetFragment != null) {
                if (page != mPage) {
                    templetFragment.clearSelected();
                }
            }
        }
    }

    public void scrollToTop(int page){
        if (mFragmengs.size() != 0) {
            TempletFragment templetFragment = mFragmengs.get(new Integer(page)).get();
            if (templetFragment != null) {
//                if (page != mPage) {
                    templetFragment.scrollToTop();
//                }
            }
        }
    }

    public void scrollToPosition(int page, int pos) {

    }

    @Override
    public void keyChange(int direction, int page, boolean isNeedScroll) {
        LibDeprecatedLogger.d(direction + "," + page + "," + isNeedScroll);
        int mPage = page;
        //向上翻页
        if (direction == 1) {
            if (isNeedScroll) {
                if (page != 0) {
                    mPage = page - 1;
                } else {
                    return;
                }
            }

            TempletFragment templetFragment = mFragmengs.get(new Integer(mPage)).get();
            templetFragment.scrollTo(isNeedScroll, 14);

        } else {
            //向下翻页
            if (isNeedScroll) {
                if (page != getCount() - 1) {
                    mPage = page + 1;
                } else {
                    return;
                }
            }


            TempletFragment templetFragment = mFragmengs.get(new Integer(mPage)).get();
            templetFragment.scrollTo(isNeedScroll, 0);
        }

    }

    @Override
    public void focusChange(View view, boolean b) {
//        if (b) {
//            focusBorderView.setFocusView(view);
//        } else {
//            focusBorderView.setUnFocusView(view);
//        }
    }

    @Override
    public void onKeyRight(int page) {
        ((TempletActivity)mContext).selectRightLabel(page);
    }

    @Override
    public void onVideoSelected(ListAlbumModel model, int page, int position) {

        //设置新的playing page
        TempletFragment templetFragment = mFragmengs.get(new Integer(page)).get();
        templetFragment.setPlayingPage(page);

        this.mLastPlayingPage = mPlayingPage;

        if (mLastPlayingPage != -1 && mLastPlayingPage != page) {
            templetFragment = mFragmengs.get(new Integer(mLastPlayingPage)).get();
            templetFragment.setPlayingPosition(-1);
        }


        ((TempletActivity)mContext).setPlayerUIAndPlay(model);

        this.mPlayingPage = page;
        this.mPosition = position;
    }

    @Override
    public void hideLoading() {
//        ((TempletActivity)mContext).hideLoading();
    }

    public boolean getNextPos() {
        LibDeprecatedLogger.d("mPlayingPage=" + mPlayingPage + ",mPosition=" +  mPosition);
        TempletFragment templetFragment = mFragmengs.get(new Integer(mPlayingPage)).get();
        boolean result = templetFragment.requestNextPosition(mPosition + 1);
        if (!result && mPlayingPage < mFragmengs.size() - 1) {
            templetFragment = mFragmengs.get(new Integer(mPlayingPage + 1)).get();
            mVerticalViewPager.setCurrentItem(mPlayingPage + 1);
            templetFragment.requestNextPosition(0);
            return true;
        }
        return result;
    }

    //移动到正在播放的Page
    //且滚到正在播放的item的位置
    public void moveToPlaytingPage() {
        mVerticalViewPager.setCurrentItem(mPlayingPage);
        TempletFragment templetFragment = mFragmengs.get(new Integer(mPlayingPage)).get();
        templetFragment.scrollToPlayingPosition();
    }

    //如果当前页面是正在播放页面的话
    //滚到正在播放的item的位置
    public void moveToPlaytingPosition(int page) {
        if (page == mPage) {
            TempletFragment templetFragment = mFragmengs.get(new Integer(mPage)).get();
            templetFragment.scrollToPlayingPosition();
        }
    }

}
