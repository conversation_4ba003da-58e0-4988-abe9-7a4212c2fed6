package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.SimpleNumberKeyboard;

/**
 * 青少年模式锁定界面
 */
public class TeenagerLockActivity extends BaseActivity implements SimpleNumberKeyboard.OnCompleteListener {

    /**
     * 初次设置密码
     */
    public static final int TYPE_ONE_SET_PASSWORD = 1;

    /**
     * 再次设置密码
     */
    private static final int TYPE_TWO_SET_PASSWORD = 2;

    /**
     * 关闭青少年
     */
    public static final int TYPE_FINISH = 3;

    /**
     * 超出观看时间40分钟
     */
    public static final int TYPE_SEE_TIME_OUT = 4;

    /**
     * 超出默认观看时间段
     */
    public static final int TYPE_SEE_TIME_OUT_1 = 5;


    public static final String INTENT_TYPE = "TYPE";


    public static final String PRE_KEY_TEENAGER = "teenager_password";

    public static final int RESULT_CODE_FINISH = 500;
    public static final int REQUEST_CODE_FINISH = 501;


    private int mType;

    /**
     * 标题
     */
    private TextView mTitle;

    /**
     * 提示内容
     */
    private TextView mDes;

    /**
     * 密码输入框
     */
    private SimpleNumberKeyboard mBerKeyboard;

    /**
     * 初始状态的上一次密码
     */
    private String mPerPassword;

    /**
     * 是否是重置密码操作
     */
    private Boolean isResetPassword = false;

    private Boolean isTimeOutOne = false;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_teenager_lock);
        initIntent();
        initView();
        initListener();
        setViewChange();
    }

    /**
     * 获取 Intent
     */
    private void initIntent() {
        mType = getIntent().getIntExtra(INTENT_TYPE, -1);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        initIntent();
    }

    /**
     * 初始化布局
     */
    private void initView() {
        mTitle = (TextView) findViewById(R.id.tv_lock_title);
        mDes = (TextView) findViewById(R.id.tv_lock_desc);
        mBerKeyboard = (SimpleNumberKeyboard) findViewById(R.id.keyboard_lock);
    }

    private void initListener() {
        mBerKeyboard.setOnCompleteListener(this);
    }


    /**
     * 根据类型设置view
     */
    private void setViewChange() {
        String titleContent;
        String des;
        switch (mType) {
            case TYPE_ONE_SET_PASSWORD:
                titleContent = getString(R.string.setting_password);
                des = "关闭青少年模式时，需要输入该密码";
                mDes.setText(des);
                if (!isResetPassword) {
                    TeenagersManger.getInstance().exposureSetPassword();
                }
                break;
            case TYPE_TWO_SET_PASSWORD:
                titleContent = getString(R.string.again_input_password);
                des = "请再次输入密码";
                mDes.setText(des);
                break;
            case TYPE_FINISH:
                titleContent = getString(R.string.finish_teenager_model);
                mDes.setText(getFinishTeenagerDesStr());
                break;
            case TYPE_SEE_TIME_OUT:
                titleContent = getString(R.string.input_password);
                mDes.setText(getTimeOutDesStr());
                Log.e("-----------", "");
                TeenagersManger.getInstance().exposureTimeOut();
                break;
            case TYPE_SEE_TIME_OUT_1:
                titleContent = getString(R.string.input_password);
                mDes.setText(getTimeOutStopDesStr());
                isTimeOutOne = true;
                TeenagersManger.getInstance().exposureInterval();
                break;
            default:
                titleContent = getString(R.string.input_password);
                mDes.setText("");
                break;
        }
        mTitle.setText(titleContent);
    }


    /**
     * 获取 关闭青少年提示文案
     *
     * @return Span
     */
    private SpannableStringBuilder getFinishTeenagerDesStr() {
        String input = "请输入";
        String num = " 4位 ";
        String tip = "数字密码";
        return setSpan(input, num, tip);
    }

    /**
     * 超时40分
     *
     * @return span
     */
    private SpannableStringBuilder getTimeOutDesStr() {
        String input = "您今日观看悦厅视频已";
        String num = " 累计40分钟 ";
        String tip = "，根据青少年模式规则，\n今日无法继续观看，或由监护人输入密码继续使用，请合理\n安排使用时间。";
        return setSpan(input, num, tip);
    }

    /**
     * 超出观看时间端
     *
     * @return span
     */
    private SpannableStringBuilder getTimeOutStopDesStr() {
        String input = "为了保障充足的休息时间，";
        String num = " 每日晚10:00 - 次日早6:00 ";
        String tip = "\n期间将无法使用悦厅TV，或由监护人输入密码后使用。";
        return setSpan(input, num, tip);
    }

    /**
     * 设置span
     *
     * @param input 正常文字
     * @param num   变色文字
     * @param tip   正常文字
     * @return
     */
    private SpannableStringBuilder setSpan(String input, String num, String tip) {
        StringBuilder builder = new StringBuilder();
        builder.append(input);
        builder.append(num);
        builder.append(tip);
        int start = builder.indexOf(num);
        LibDeprecatedLogger.e("start:" + start + "end:" + start + num.length());
        SpannableStringBuilder spannableString = new SpannableStringBuilder(builder);
        spannableString.setSpan(
                new ForegroundColorSpan(ContextCompat.getColor(TeenagerLockActivity.this, R.color.text_color_tip)), start, start + num.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE
        );
        return spannableString;
    }

    @Override
    public void complete(String number) {
        switch (mType) {
            case TYPE_ONE_SET_PASSWORD:
                mPerPassword = number;
                mType = TYPE_TWO_SET_PASSWORD;
                setViewChange();
                break;
            case TYPE_TWO_SET_PASSWORD:
                if (TextUtils.equals(mPerPassword, number)) {
                    TeenagersManger.putTeenagerPassword(TeenagerLockActivity.this, mPerPassword);
                    if (isResetPassword) {
                        //如果不是禁播时间段重置密码  观看时间清空
                        if (!isTimeOutOne) {
                            TeenagersManger.getInstance().putTimeout(0L, TeenagerLockActivity.this);
                        }
                        TeenagersManger.getInstance().exposureResetPasswordSuccess();
                        isResetPassword = false;
                    } else {
                        ActivityLauncher.startTeenagersActivity(TeenagerLockActivity.this, -1L);
                        ToastUtils.showToast(TeenagerLockActivity.this, "青少年模式已开启");
                    }
                    finish();
                } else {
                    mType = TYPE_ONE_SET_PASSWORD;
                    mPerPassword = "";
                    ToastUtils.showToast(TeenagerLockActivity.this, "两次密码输入不一致，需重新设置");
                    setViewChange();
                }
                break;
            case TYPE_SEE_TIME_OUT:
            case TYPE_SEE_TIME_OUT_1:
                checkPassword(number, false);
                break;
            case TYPE_FINISH:
                checkPassword(number, true);
                break;
        }
    }

    /**
     * 重置密码成功回调
     */
    @Override
    public void resetSuccess() {
        mType = TYPE_ONE_SET_PASSWORD;
        isResetPassword = true;
        mBerKeyboard.displayReset();
        TeenagersManger.clearTeenagerPassword(TeenagerLockActivity.this);
        setViewChange();
//        TeenagersManger.clearTeenagerPassword(TeenagerLockActivity.this);
    }

    private void checkPassword(String number, Boolean isSetCode) {
        String password = TeenagersManger.getTeenagerPassword();
        if (!password.isEmpty()) {
            Boolean check = TextUtils.equals(number, password);
            if (check) {
                if (mType == TYPE_SEE_TIME_OUT) {
                    TeenagersManger.getInstance().putTimeout(0L, TeenagerLockActivity.this);
                }
                if (mType == TYPE_SEE_TIME_OUT_1) {
                    TeenagersManger.putTeenagerIntervalTime(TeenagerLockActivity.this, TeenagersManger.getInstance().mIntervalStart);
                }
                if (isSetCode) {
                    mBerKeyboard.setIsBanInput(true);
                    TeenagersManger.clearTeenagerPassword(TeenagerLockActivity.this);
                    setResult(RESULT_CODE_FINISH);
                }
                finish();
            } else {
                mBerKeyboard.setIsBanInput(false);
                ToastUtils.showToast(TeenagerLockActivity.this, "密码输入错误");
            }
        }
    }

//    /**
//     * 获取密码
//     *
//     * @return 密码
//     */
//    public static String getTeenagerPassword(Context context) {
//        return PrefUtil.getString(context, PRE_KEY_TEENAGER, "");
//    }
//
//    /**
//     * 设置/清空密码
//     *
//     * @param password 密码
//     */
//    public static void putTeenagerPassword(Context context, String password) {
//        PrefUtil.putString(context, PRE_KEY_TEENAGER, password);
//    }

    @Override
    public void onBackPressed() {

        if (mBerKeyboard.dialog != null && mBerKeyboard.dialog.isShowing()) {
            mBerKeyboard.dialog.dismissQrCode();
        }
        switch (mType) {
            case TYPE_SEE_TIME_OUT_1:
            case TYPE_SEE_TIME_OUT:
                break;
            case TYPE_ONE_SET_PASSWORD:
            case TYPE_TWO_SET_PASSWORD:
//                if (isResetPassword) {
//                    finish();
//                } else {
                ActivityLauncher.startHomeActivity(TeenagerLockActivity.this);
//                }
                break;
            default:
                finish();
                break;
        }
    }

}
