package com.sohuott.tv.vod.videodetail.activity

import GsonConverter
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.lib.model.VideoDetailFilmCommodities
import com.sohuott.tv.vod.lib.utils.UrlWrapper
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.coroutineScope
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class PayInfoRepository {
    suspend fun requestPayInfo(
        aid: Int = 0,
        vid: Int = 0,
        cateCode: Int = 0,
        isLogin: Boolean = false,
        mUserLoginToken: String = "",
        passport: String = "",
    ): Deferred<VideoDetailFilmCommodities?> {
        return coroutineScope {
            val url = StringBuffer()
            url.append(
                String.format(
                    UrlWrapper.getCommoditiesUrl(),
                    aid,
                    vid
                )
            )
            if (cateCode > 0) {
                url.append("&cateCode=$cateCode")
            }
            if (isLogin) {
                url.append("&passport=$passport")
                url.append("&token=$mUserLoginToken")
            }
            Get<VideoDetailFilmCommodities?>("$url") {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.MODERN_TLS,
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
            }
        }
    }
}

data class PayInfoRequestData(
    var aid: Int? = null,
    var vid: Int? = null,
    var isLogin: Boolean = false,
    var mUserLoginToken: String? = null,
    var passport: String? = null,
)

data class PayInfoResponse(
    var mVipTicket: Boolean = false,
    var mVipTicketCount: Int = 0,
    var mVipSingle: Boolean = false,
    var mVipMember: Boolean = false,
    var feeVid: Int = 0,
    var isEduCate: Boolean = false,
    var isShowTime: Boolean = false,
    var time: Long? = null,
    var payText: String? = null
)