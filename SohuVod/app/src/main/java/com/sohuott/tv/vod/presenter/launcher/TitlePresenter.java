package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.leanback.widget.Presenter;

import com.bumptech.glide.Glide;
import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.launcher.HomeTab;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohu.lib_utils.StringUtil;

import java.util.HashMap;

public class TitlePresenter extends Presenter {
    private static final String TAG = "TitlePresenter";
    private Context mContext;

    private SparseArray<Integer> drawableStateMap = new SparseArray<>();

    private static final int STATE_VIP_FOCUSED = 0;
    private static final int STATE_VIP_DEFAULT = 1;
    private static final int STATE_VIP_SELECTED = 2;

    private static final int STATE_CHILD_FOCUSED = 3;
    private static final int STATE_CHILD_DEFAULT = 4;
    private static final int STATE_CHILD_SELECTED = 5;

    public static final int VIP_ICON = 6;
    public static final int FREE_ICON = 7;

    public TitlePresenter() {
        drawableStateMap.put(STATE_VIP_FOCUSED, R.drawable.channel_vip_focused);
        drawableStateMap.put(STATE_VIP_DEFAULT, R.drawable.channel_vip_default);
        drawableStateMap.put(STATE_VIP_SELECTED, R.drawable.channel_vip_selected);


        drawableStateMap.put(VIP_ICON, R.drawable.launcher_title_icon_vip);
        drawableStateMap.put(FREE_ICON, R.drawable.launcher_title_icon_free);
    }
    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_main_title, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        if (item instanceof com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem) {
            final com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem data = (com.sohuott.tv.vod.lib.model.launcher.HomeTab.TabItem) item;
            LibDeprecatedLogger.d("tab name : " + data.getName() + ", selected : " + data.isSelected());
            boolean isVipTab = data.getType() == Constant.TYPE_VIP || data.getName().contains("会员");
            boolean isChildTab = data.getDataType() == Constant.TYPE_CHILD || data.getName().contains("少儿");


            //根据付费状态，设置导航栏背景
            //付费
            if (data.getFeeType() == 2) {
                vh.mContainer.setBackground(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.launcher_title_vip_bg_selector, null));
                ColorStateList colorStateList = ContextCompat.getColorStateList(mContext, R.color.channel_list_text_vip_selector);
                vh.mTvMainTitle.setTextColor(colorStateList);
                if (data.getPosition() == LauncherActivity.START) {
                    //起始
                    vh.mFeeTypeCorner.setBackgroundResource(drawableStateMap.get(VIP_ICON));
                    vh.mRootView.setBackground(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.launcher_title_vip_left, null));
                } else if (data.getPosition() == LauncherActivity.END) {
                    //结束
                    vh.mRootView.setBackground(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.launcher_title_vip_right, null));
                } else if (data.getPosition() == LauncherActivity.SINGLE) {
                    vh.mFeeTypeCorner.setBackgroundResource(drawableStateMap.get(VIP_ICON));
                } else {
                    vh.mRootView.setBackground(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.launcher_title_vip_middle, null));
                }

            } else if (data.getFeeType() == 1) {
                //免费
                vh.mContainer.setBackground(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.launcher_title_bg_selector, null));
                ColorStateList colorStateList = ContextCompat.getColorStateList(mContext, R.color.channel_list_text_selector);
                vh.mTvMainTitle.setTextColor(colorStateList);
                if (data.getPosition() == 1) {
                    vh.mFeeTypeCorner.setBackgroundResource(drawableStateMap.get(FREE_ICON));
                    vh.mRootView.setBackground(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.launcher_title_free_left, null));

                } else if (data.getPosition() == -1) {
                    vh.mRootView.setBackground(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.launcher_title_free_right, null));

                } else if (data.getPosition() == 2) {
                    vh.mFeeTypeCorner.setBackgroundResource(drawableStateMap.get(FREE_ICON));
                } else {
                    vh.mRootView.setBackgroundColor((Color.parseColor("#41363646")));

                }
            } else {
                vh.mContainer.setBackground(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.launcher_title_bg_selector, null));
                ColorStateList colorStateList = ContextCompat.getColorStateList(mContext, R.color.channel_list_text_selector);
                vh.mTvMainTitle.setTextColor(colorStateList);
            }

            // 会员
            if (isVipTab) {
                vh.mTvMainTitle.setVisibility(View.GONE);
                vh.mImgMainTitle.setVisibility(View.VISIBLE);
                if (data.isSelected()) {
                    vh.mImgMainTitle.setBackgroundResource(drawableStateMap.get(STATE_VIP_FOCUSED));
                } else {
                    vh.mImgMainTitle.setBackgroundResource(drawableStateMap.get(STATE_VIP_DEFAULT));
                }


                vh.mContainer.setBackground(null);
            }  else {

                if (!TextUtils.isEmpty(data.getPicUrl())) {

                    Glide.with(mContext)
                            .load(data.getPicUrl())
                            .into(vh.mImgMainTitle);
                    vh.mImgMainTitle.setVisibility(View.VISIBLE);

                    if (!TextUtils.isEmpty(data.getPicUrl2())) {
                        Glide.with(mContext)
                                .load(data.getPicUrl2())
                                .into(vh.mImgMainTitle2);
                        vh.mImgMainTitle2.setVisibility(View.INVISIBLE);
                    }

                } else {
                    vh.mTvMainTitle.setText(((HomeTab.TabItem) item).getName());
                    vh.mTvMainTitle.getPaint().setFakeBoldText(true);
                    vh.mImgLine.setVisibility(View.INVISIBLE);
//                    vh.mTvMainTitle.setTextColor(mContext.getResources().getColor(R.color.bg_channel_list_default));
                }
            }
            vh.mRootView.setSelected(data.isSelected());

            vh.mRootView.setOnFocusChangeListener((v, hasFocus) -> {
                //focus
                if (hasFocus) {
                    if (isVipTab) {
                        vh.mImgMainTitle.setBackgroundResource(drawableStateMap.get(STATE_VIP_FOCUSED));
                        vh.mImgMainTitle.setVisibility(View.VISIBLE);
                    } else {
                        vh.mImgMainTitle.setVisibility(View.VISIBLE);
                        vh.mImgMainTitle2.setVisibility(View.INVISIBLE);
                        vh.mImgLine.setVisibility(View.INVISIBLE);
//                        vh.mTvMainTitle.setTextColor(mContext.getResources().getColor(R.color.bg_channel_list_focus));
                    }

                } else {
                    //selected
                    if (data.isSelected()) {
                        if (isVipTab) {
                            vh.mImgMainTitle.setBackgroundResource(drawableStateMap.get(STATE_VIP_SELECTED));
                            vh.mImgMainTitle.setVisibility(View.VISIBLE);
                        } else {
                            if (!TextUtils.isEmpty(data.getPicUrl2())) {
                                vh.mImgMainTitle.setVisibility(View.INVISIBLE);
                                vh.mImgMainTitle2.setVisibility(View.VISIBLE);
                            } else {
//                                vh.mTvMainTitle.setTextColor(mContext.getResources().getColor(R.color.bg_channel_list_select));
                                vh.mTvMainTitle.getPaint().setFakeBoldText(true);
                                vh.mRootView.setSelected(true);
                                vh.mImgLine.setVisibility(View.VISIBLE);
                            }
                        }
                    } else {
                        //default
                        if (isVipTab) {
                            vh.mImgMainTitle.setBackgroundResource(drawableStateMap.get(STATE_VIP_DEFAULT));
                        }
                        vh.mImgMainTitle2.setVisibility(View.INVISIBLE);
                        vh.mImgMainTitle.setVisibility(View.VISIBLE);
                        vh.mRootView.setSelected(false);
//                        vh.mTvMainTitle.setTextColor(mContext.getResources().getColor(R.color.bg_channel_list_default));
                    }
                }
            });

            vh.mRootView.setOnClickListener(v -> {
                        if (data.getOttCategoryId() == 0) return;

                        HashMap<String, String> memoInfo = new HashMap<>();
                        memoInfo.put("category", StringUtil.toString(data.getOttCategoryId()));

                        RequestManager.getInstance().onAllEvent(new EventInfo(10161, "clk"),
                                null, null, memoInfo);

                        ActivityLauncher.startGridListActivityWithCatecode(mContext, data.getOttCategoryId(),
                                (int) data.getCateCode(), false, data.getDataType(), -1, data.getOrder() + 1);
                    }
            );
        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private TextView mTvMainTitle;
        private ImageView mImgMainTitle, mImgMainTitle2, mFeeTypeCorner;
        private View mImgLine;
        private RelativeLayout mRootView, mContainer;

        ViewHolder(View view) {
            super(view);
            mTvMainTitle = (TextView) view.findViewById(R.id.tv_main_title);
            mImgMainTitle = (ImageView) view.findViewById(R.id.img_main_title);
            mImgMainTitle2 = (ImageView) view.findViewById(R.id.img_main_title2);
            mContainer = view.findViewById(R.id.tv_main_container);
            mRootView = (RelativeLayout) view;
            mImgLine = view.findViewById(R.id.line_main_title);
            mFeeTypeCorner = view.findViewById(R.id.fee_type_corner);
        }
    }
}
