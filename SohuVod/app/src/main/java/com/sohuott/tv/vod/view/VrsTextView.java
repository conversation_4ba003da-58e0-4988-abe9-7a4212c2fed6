package com.sohuott.tv.vod.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;

import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.utils.FocusUtil;

/**
 * show text+badge on top right corner,like vip,trailer
 *
 * <AUTHOR>
 *         created at 2017/10/24
 */
public class VrsTextView extends View {
    private boolean mTextColorEnable;
    private boolean isMenu=false;

    public VrsTextView(Context context) {
        super(context);
        init(context, null);
    }

    public VrsTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public VrsTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private Paint mPaint;
    private String mText;
    private int mBaseLine;
    private int mViewWidth, mViewHeight;
    private static final int TEXT_COLOR_INDEX = 0;
    private static final int TEXT_SIZE_INDEX = 1;
    private static final int BADGE_WIDTH_INDEX = 2;
    private static final int BADGE_HEIGHT_INDEX = 3;
    private int mBadgeWidth, mBadgeHeight;
    private Drawable mBadge;
    private int defaultTextColor;

    private void init(Context context, AttributeSet attrs) {
        mTextColorEnable = true;
        TypedArray typedArray = context.obtainStyledAttributes(attrs, new int[]{android.R.attr.textColor, android.R.attr.textSize, R.attr.badgeWidth, R.attr.badgeHeight});
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setTextAlign(Paint.Align.CENTER);
        mPaint.setStyle(Paint.Style.FILL);
        defaultTextColor=getResources().getColor(R.color.txt_grid_left_normal);
        mPaint.setColor(typedArray.getColor(TEXT_COLOR_INDEX, defaultTextColor));
        mPaint.setTextSize(typedArray.getDimensionPixelOffset(TEXT_SIZE_INDEX, getResources().getDimensionPixelOffset(R.dimen.x36)));
        mBadgeWidth = typedArray.getDimensionPixelOffset(BADGE_WIDTH_INDEX, getResources().getDimensionPixelOffset(R.dimen.x52));
        mBadgeHeight = typedArray.getDimensionPixelOffset(BADGE_HEIGHT_INDEX, getResources().getDimensionPixelOffset(R.dimen.y26));
        typedArray.recycle();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!TextUtils.isEmpty(mText)) {
            canvas.drawText(mText, mViewWidth / 2, mBaseLine, mPaint);
        }
        if (mBadge != null) {
            mBadge.setBounds(mViewWidth - mBadgeWidth, 0, mViewWidth, mBadgeHeight);
            mBadge.draw(canvas);
        }
    }

    @Override
    protected void drawableStateChanged() {
        if (!mTextColorEnable) {
            mPaint.setColor(getResources().getColor(R.color.txt_grid_left_unable));
            setBackground(getResources().getDrawable(R.drawable.episode_vrs_item_unable_selector));
        } else if (isFocused()) {
            mPaint.setColor(Color.WHITE);
            setBackground(getResources().getDrawable(R.drawable.simple_number_keyboard_key_focused_bg));
        } else if (isSelected()) {
            mPaint.setColor(getResources().getColor(R.color.txt_grid_left_focused));
            if (isMenu){
                setBackground(getResources().getDrawable(R.drawable.episode_menu_vrs_item_selector));
            }else {
                setBackground(getResources().getDrawable(R.drawable.episode_vrs_item_selector));

            }
        }  else {
            if (isMenu){
                setBackground(getResources().getDrawable(R.drawable.episode_menu_vrs_item_selector));
            }else {
                setBackground(getResources().getDrawable(R.drawable.episode_vrs_item_selector));
            }
            mPaint.setColor(defaultTextColor);
        }
        super.drawableStateChanged();
    }

    public void setIsMenu(boolean isMenu){
        this.isMenu=isMenu;
    }
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mViewWidth = w;
        mViewHeight = h;
        Paint.FontMetricsInt fontMetricsInt = mPaint.getFontMetricsInt();
        mBaseLine = mViewHeight / 2 + (fontMetricsInt.bottom - fontMetricsInt.top) / 2 - fontMetricsInt.bottom;
    }


    public void setText(String text) {
        if (text.length() > 7) {
            mText = text.substring(0, 7) + "...";
        } else {
            mText = text;
        }
        invalidate();
    }

    public String getText() {
        return mText;
    }

    public void setBadge(@DrawableRes int resId) {
        mBadge = getResources().getDrawable(resId);
        invalidate();
    }

    public void setTextColorEnable(boolean enable) {
        if (enable != mTextColorEnable) {
            mTextColorEnable = enable;
            drawableStateChanged();
        }
    }


}
