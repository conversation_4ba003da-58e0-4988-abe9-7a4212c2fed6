package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-2-4.
 * <p/>
 * {@link LinearLayoutManager} extension which introduces workaround for focus finding bug when
 * navigating with dpad.
 */

public class CustomHistoryLayoutManager extends LinearLayoutManager {

    Context mContext;
    int mCustomPaddingStart;
    int mCustomPaddingEnd;

    public CustomHistoryLayoutManager(Context context) {
        super(context);
        mContext = context;
        mCustomPaddingStart = 0;
        mCustomPaddingEnd = 0;
    }

    public void setCustomPadding(int paddingStart, int paddingEnd) {
        mCustomPaddingStart = paddingStart;
        mCustomPaddingEnd = paddingEnd;
    }

    @Override
    public boolean requestChildRectangleOnScreen(RecyclerView parent, View child, Rect rect, boolean immediate) {

        if (getOrientation() == VERTICAL) {
            int parentTop = 0;
            int parentBottom = getHeight();
            int childTop = child.getTop() + rect.top;
            int childBottom = childTop + rect.bottom;

            // 选中item 的右边距，如果小于右边距即滑动
            int offScreenTop = Math.min(0, childTop - parentTop - mCustomPaddingStart);
            int offScreenBottom = Math.max(0, childBottom - parentBottom + mCustomPaddingEnd);

            // Favor bringing the top into view over the bottom
            int dy = offScreenTop != 0 ? offScreenTop : offScreenBottom;

            if (dy != 0) {
                parent.smoothScrollBy(0, dy);

                return true;
            }
        } else if (getOrientation() == HORIZONTAL) {
            int parentLeft = 0;
            int parentRight = getWidth();
            int childLeft = child.getLeft() + rect.left;
            int childRight = childLeft + rect.right;

            // 选中item 的右边距，如果小于右边距即滑动
            int offScreenLeft = Math.min(0, childLeft - parentLeft - mCustomPaddingStart);
            int offScreenRight = Math.max(0, childRight - parentRight + mCustomPaddingEnd);

            // Favor the "start" layout direction over the end when bringing one
            // side or the other
            // of a large rect into view.
            int dx;
            if (ViewCompat.getLayoutDirection(parent) == ViewCompat.LAYOUT_DIRECTION_RTL) {
                dx = offScreenRight != 0 ? offScreenRight : offScreenLeft;
            } else {
                dx = offScreenLeft != 0 ? offScreenLeft : offScreenRight;
            }
            if (dx != 0) {
                parent.smoothScrollBy(dx, 0);

                return true;
            }
        }

        return false;
    }

    @Override
    public void onItemsRemoved(RecyclerView recyclerView, int positionStart, int itemCount) {
        super.onItemsRemoved(recyclerView, positionStart, itemCount);
    }
}