package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.graphics.Rect;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.PersonalCinemaModel;
import com.sohuott.tv.vod.model.PersonalCinemaItemTypeModel;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.lang.ref.WeakReference;
import java.util.List;

import static android.view.KeyEvent.ACTION_DOWN;
import static android.view.KeyEvent.KEYCODE_DPAD_DOWN;
import static android.view.KeyEvent.KEYCODE_DPAD_UP;

/**
 * Created by yizhang210244 on 2017/12/22.
 */

public class PersonalCinemaAdapter extends RecyclerView.Adapter{
    private static final String TAG = PersonalCinemaAdapter.class.getSimpleName();
    public static final int PERSONAL_TYPE_VRS_RECOMMEND = 0;
    public static final int PERSONAL_TYPE_PGC_RECOMMEND = 1;
    public static final int PERSONAL_TYPE_TAG_RECOMMEND = 2;
    public static final int PERSONAL_TYPE_STAR_RECOMMEND = 3;
    public static final int PERSONAL_TYPE_STAR_MOVIE_RECOMMEND = 4;
    public static final int PERSONAL_TYPE_CATE_CODE = 5;

    private int mCurrentSelectedPosition = 0;
    private RecyclerView mRecyclerView;
    private FocusBorderView mFocusBorderView;
    private List<PersonalCinemaItemTypeModel> mPersonalCinemaItemTypeModelList;
    private PersonalCinemaModel mPersonalCinemaModel;
    private LinearSmoothScroller mLinearSmoothScroller;
    private boolean mIsLongPress = false;

    public PersonalCinemaAdapter(RecyclerView recyclerView) {
        this.mRecyclerView = new WeakReference<RecyclerView>(recyclerView).get();
        if(mRecyclerView != null){
            mRecyclerView.setOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                    super.onScrollStateChanged(recyclerView, newState);
                    if(newState == RecyclerView.SCROLL_STATE_IDLE){
                        setItemFocus(mCurrentSelectedPosition);
                    }
                }
            });
        }


    }

    public int getCurrentSelectedPosition() {
        return mCurrentSelectedPosition;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        mFocusBorderView = focusBorderView;
    }

    public void setPersonalCinemaItemTypeModelList(List<PersonalCinemaItemTypeModel> personalCinemaItemTypeModelList) {
        mPersonalCinemaItemTypeModelList = personalCinemaItemTypeModelList;
    }

    public void setPersonalCinemaModel(PersonalCinemaModel personalCinemaModel) {
        mPersonalCinemaModel = personalCinemaModel;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v;
        RecyclerView.ViewHolder viewHolder = null;
        switch (viewType) {
            case PERSONAL_TYPE_VRS_RECOMMEND:
            case PERSONAL_TYPE_PGC_RECOMMEND:
            case PERSONAL_TYPE_TAG_RECOMMEND:
            case PERSONAL_TYPE_STAR_MOVIE_RECOMMEND:
                v = LayoutInflater.from(parent.getContext()).inflate(R.layout.personal_cinema_movie_item, parent, false);
                viewHolder = new MovieRecommendViewHolder(v);
                break;
            case PERSONAL_TYPE_CATE_CODE:
                v = LayoutInflater.from(parent.getContext()).inflate(R.layout.personal_cinema_catecode, parent, false);
                viewHolder = new CateCodeRecommendViewHolder(v);
                break;
            case PERSONAL_TYPE_STAR_RECOMMEND:
                v = LayoutInflater.from(parent.getContext()).inflate(R.layout.personal_cinema_star, parent, false);
                viewHolder = new StartRecommendViewHolder(v);
                break;
            default:
                break;
        }
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        int view_type = holder.getItemViewType();
        PersonalCinemaItemTypeModel personalCinemaItemTypeModel = mPersonalCinemaItemTypeModelList.get(position);
        switch (view_type){
            case PERSONAL_TYPE_VRS_RECOMMEND:
            {
                MovieRecommendViewHolder movieRecommendViewHolder = (MovieRecommendViewHolder) holder;
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) movieRecommendViewHolder.recommend_title.getLayoutParams();
                layoutParams.setMargins(holder.itemView.getContext().getResources().getDimensionPixelSize(R.dimen.x90),0,0,0);
                movieRecommendViewHolder.recommend_title.setLayoutParams(layoutParams);
                movieRecommendViewHolder.recommend_title.setText("今日推荐");
                PersonalCinemaMovieListAdapter adapter = new PersonalCinemaMovieListAdapter(mRecyclerView,movieRecommendViewHolder.list);
                adapter.setContentsBeanList(mPersonalCinemaModel.getVrsContents());
                adapter.setFocusBorderView(mFocusBorderView);
                adapter.setType(PERSONAL_TYPE_VRS_RECOMMEND);
                adapter.setHasStableIds(true);
                movieRecommendViewHolder.list.setAdapter(adapter);
            }
            break;

            case PERSONAL_TYPE_PGC_RECOMMEND:
            {
                MovieRecommendViewHolder movieRecommendViewHolder = (MovieRecommendViewHolder) holder;
                if(personalCinemaItemTypeModel.isDisplayTitle()){
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) movieRecommendViewHolder.recommend_title.getLayoutParams();
                    layoutParams.setMargins(holder.itemView.getContext().getResources().getDimensionPixelSize(R.dimen.x90),0,0,0);
                    movieRecommendViewHolder.recommend_title.setLayoutParams(layoutParams);
                    movieRecommendViewHolder.recommend_title.setText("今日推荐");
                    movieRecommendViewHolder.recommend_title.setVisibility(View.VISIBLE);
                }else {
                    movieRecommendViewHolder.recommend_title.setVisibility(View.GONE);
                }
                PersonalCinemaMovieListAdapter adapter = new PersonalCinemaMovieListAdapter(mRecyclerView,movieRecommendViewHolder.list);
                adapter.setFocusBorderView(mFocusBorderView);
                adapter.setContentsBeanList(mPersonalCinemaModel.getPgcContents());
                adapter.setType(PERSONAL_TYPE_PGC_RECOMMEND);
                adapter.setHasStableIds(true);
                movieRecommendViewHolder.list.setAdapter(adapter);
            }
            break;

            case PERSONAL_TYPE_CATE_CODE:
            {
                CateCodeRecommendViewHolder cateCodeRecommendViewHolder = (CateCodeRecommendViewHolder) holder;
                if(personalCinemaItemTypeModel.isDisplayTitle()){
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) cateCodeRecommendViewHolder.recommend_title.getLayoutParams();
                    layoutParams.setMargins(holder.itemView.getContext().getResources().getDimensionPixelSize(R.dimen.x90),0,0,0);
                    cateCodeRecommendViewHolder.recommend_title.setLayoutParams(layoutParams);
                    cateCodeRecommendViewHolder.recommend_title.setText("今日推荐");
                    cateCodeRecommendViewHolder.recommend_title.setVisibility(View.VISIBLE);
                }else {
                    cateCodeRecommendViewHolder.recommend_title.setVisibility(View.GONE);
                }
                PersonalCinemaCateCodeAdapter adapter = new PersonalCinemaCateCodeAdapter(mRecyclerView,cateCodeRecommendViewHolder.list);
                adapter.setFocusBorderView(mFocusBorderView);
                adapter.setCateCodeContentsBeanList(mPersonalCinemaModel.getCateCodeContents());
                adapter.setHasStableIds(true);
                cateCodeRecommendViewHolder.list.setAdapter(adapter);
            }
            break;

            case PERSONAL_TYPE_STAR_MOVIE_RECOMMEND:
            {
                MovieRecommendViewHolder movieRecommendViewHolder = (MovieRecommendViewHolder) holder;
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) movieRecommendViewHolder.recommend_title.getLayoutParams();
                layoutParams.setMargins(holder.itemView.getContext().getResources().getDimensionPixelSize(R.dimen.x90),
                        holder.itemView.getContext().getResources().getDimensionPixelSize(R.dimen.y50),0,0);
                movieRecommendViewHolder.recommend_title.setLayoutParams(layoutParams);
                if(mPersonalCinemaModel.getStarRecommend().getStar() != null &&
                        !TextUtils.isEmpty(mPersonalCinemaModel.getStarRecommend().getStar().getName())){
                    movieRecommendViewHolder.recommend_title.setText(mPersonalCinemaModel.getStarRecommend().getStar().getName());
                }else {
                    movieRecommendViewHolder.recommend_title.setText("名人影视推荐");
                }
                PersonalCinemaMovieListAdapter adapter = new PersonalCinemaMovieListAdapter(mRecyclerView,movieRecommendViewHolder.list);
                adapter.setFocusBorderView(mFocusBorderView);
                adapter.setContentsBeanList(mPersonalCinemaModel.getStarRecommend().getContents());
                adapter.setType(PERSONAL_TYPE_STAR_MOVIE_RECOMMEND);
                adapter.setHasStableIds(true);
                if(mPersonalCinemaModel.getStarRecommend().getStar() != null){
                    adapter.setStarId(mPersonalCinemaModel.getStarRecommend().getStar().getId());
                }
                movieRecommendViewHolder.list.setAdapter(adapter);
            }
            break;

            case PERSONAL_TYPE_STAR_RECOMMEND:
            {
                StartRecommendViewHolder startRecommendViewHolder = (StartRecommendViewHolder) holder;
                startRecommendViewHolder.recommend_title.setText("明星推荐");
                PersonalCinemaStarAdapter adapter = new PersonalCinemaStarAdapter(mRecyclerView,startRecommendViewHolder.list);
                adapter.setStarContentsBeanList(mPersonalCinemaModel.getStarContents());
                adapter.setHasStableIds(true);
                startRecommendViewHolder.list.setAdapter(adapter);
            }
            break;

            case PERSONAL_TYPE_TAG_RECOMMEND:
            {
                PersonalCinemaModel.TagContentsBean tagContentsBean = mPersonalCinemaModel.getTagContents().get(personalCinemaItemTypeModel.getPosition());
                MovieRecommendViewHolder movieRecommendViewHolder = (MovieRecommendViewHolder) holder;
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) movieRecommendViewHolder.recommend_title.getLayoutParams();
                layoutParams.setMargins(holder.itemView.getContext().getResources().getDimensionPixelSize(R.dimen.x90),
                        holder.itemView.getContext().getResources().getDimensionPixelSize(R.dimen.y50),0,0);
                movieRecommendViewHolder.recommend_title.setLayoutParams(layoutParams);
                movieRecommendViewHolder.recommend_title.setText(tagContentsBean.getName());
                PersonalCinemaMovieListAdapter adapter = new PersonalCinemaMovieListAdapter(mRecyclerView,movieRecommendViewHolder.list);
                adapter.setFocusBorderView(mFocusBorderView);
                adapter.setContentsBeanList(tagContentsBean.getContents());
                adapter.setType(PERSONAL_TYPE_TAG_RECOMMEND);
                adapter.setTagName(tagContentsBean.getName());
                adapter.setHasStableIds(true);
                movieRecommendViewHolder.list.setAdapter(adapter);
            }
            break;

            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        if(mPersonalCinemaModel == null || mPersonalCinemaItemTypeModelList == null){
            return 0;
        }
        return mPersonalCinemaItemTypeModelList.size();
    }

    @Override
    public int getItemViewType(int position) {
        return mPersonalCinemaItemTypeModelList.get(position).getType();
    }

    public void onVerticalKeyEventForActivity(int keyCode, KeyEvent event) {
        if (event.getAction() == ACTION_DOWN) {
            switch (keyCode) {
                case KEYCODE_DPAD_UP:
                {
                    if(event.getRepeatCount() > 0){
                        mIsLongPress = true;
                    }else {
                        mIsLongPress = false;
                    }
                    int position = mCurrentSelectedPosition - 1;
                    if (mCurrentSelectedPosition == 0) {
                        position = 0;
                    }
                    parentVerticalScroll(position);
                }
                break;
                case KEYCODE_DPAD_DOWN:
                {
                    if(event.getRepeatCount() > 0){
                        mIsLongPress = true;
                    }else {
                        mIsLongPress = false;
                    }
                    int position = mCurrentSelectedPosition + 1;
                    if (mCurrentSelectedPosition >= getItemCount() - 1 ) {
                        position = getItemCount() -1;
                    }
                    parentVerticalScroll(position);
                }
                break;
                default:
                    break;
            }
        }
    }


    private void parentVerticalScroll(int position){
        if (mRecyclerView != null) {
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) mRecyclerView.getLayoutManager();
            if(linearLayoutManager != null){
                int lastItemPosition = linearLayoutManager.findLastCompletelyVisibleItemPosition();
                int firstItemPosition = linearLayoutManager.findFirstCompletelyVisibleItemPosition();
//                if(position >= firstItemPosition && position <= lastItemPosition){
//                    setItemFocus(position);
//                    return;
//                }
                if(position >= firstItemPosition && position <= lastItemPosition){
                    if(position == firstItemPosition || !mRecyclerView.canScrollVertically(1)){
                        setItemFocus(position);
                        return;
                    }
                }
            }
            mCurrentSelectedPosition = position;
            mFocusBorderView.clearFocus();
            mLinearSmoothScroller = new PersonalScroller(mRecyclerView.getContext());
            mLinearSmoothScroller.setTargetPosition(position);
            mRecyclerView.getLayoutManager().startSmoothScroll(mLinearSmoothScroller);
//            mRecyclerView.smoothScrollToPosition(position);
        }
    }

    public void setItemFocus(int position){
        CommonViewHolder viewHolder = (CommonViewHolder) mRecyclerView.findViewHolderForAdapterPosition(position);
        if (viewHolder == null) {
            return;
        }
        mCurrentSelectedPosition = position ;
        PersonalCinemaCommonAdapter adapter = (PersonalCinemaCommonAdapter) viewHolder.list.getAdapter();
        RecyclerView.ViewHolder item_viewholder = viewHolder.list.findViewHolderForAdapterPosition(adapter.getSelectedPosition());
        if(item_viewholder != null){
            if(adapter instanceof PersonalCinemaStarAdapter){
                View view = item_viewholder.itemView.findViewById(R.id.focus);
                if(view != null)
                    view.requestFocus();
            }else {
                if(item_viewholder.itemView.isFocused()){
                    mFocusBorderView.setFocusView(item_viewholder.itemView);
                    FocusUtil.setFocusAnimator(item_viewholder.itemView, mFocusBorderView);
                }else {
                    item_viewholder.itemView.requestFocus();
                }
            }
        }
    }

    class MovieRecommendViewHolder extends CommonViewHolder{
        public TextView recommend_title;
        public MovieRecommendViewHolder(View itemView) {
            super(itemView);
            recommend_title = (TextView) itemView.findViewById(R.id.recommend_title);
            list = (RecyclerView) itemView.findViewById(R.id.movie_list);
            list.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(list.getContext());
            linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
            list.setLayoutManager(linearLayoutManager);
            list.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                    outRect.right = parent.getResources().getDimensionPixelSize(R.dimen.x20);
                }
            });
        }
    }

    class CateCodeRecommendViewHolder extends CommonViewHolder{
        public TextView recommend_title;
        public CateCodeRecommendViewHolder(View itemView) {
            super(itemView);
            recommend_title = (TextView) itemView.findViewById(R.id.recommend_title);
            list = (RecyclerView) itemView.findViewById(R.id.cate_code_list);
            list.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(list.getContext());
            linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
            list.setLayoutManager(linearLayoutManager);
            list.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                    outRect.right = parent.getResources().getDimensionPixelSize(R.dimen.x20);
                }
            });
        }
    }


    class StartRecommendViewHolder extends CommonViewHolder{
        public TextView recommend_title;
        public StartRecommendViewHolder(View itemView) {
            super(itemView);
            recommend_title = (TextView) itemView.findViewById(R.id.recommend_title);
            list = (RecyclerView) itemView.findViewById(R.id.movie_list);
            list.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(list.getContext());
            linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
            list.setLayoutManager(linearLayoutManager);
            list.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                    outRect.right = parent.getResources().getDimensionPixelSize(R.dimen.x20);
                }
            });
        }
    }

    public abstract class CommonViewHolder extends RecyclerView.ViewHolder{
        protected RecyclerView list;
        public CommonViewHolder(View itemView) {
            super(itemView);
        }
    }

    public class PersonalScroller extends LinearSmoothScroller {

        public PersonalScroller(Context context) {
            super(context);
        }

        @Override
        protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
            return mIsLongPress ? 25f / displayMetrics.densityDpi : 75f / displayMetrics.densityDpi;
        }

        @Override
        protected int getVerticalSnapPreference() {
            return LinearSmoothScroller.SNAP_TO_START;
        }
    }

}
