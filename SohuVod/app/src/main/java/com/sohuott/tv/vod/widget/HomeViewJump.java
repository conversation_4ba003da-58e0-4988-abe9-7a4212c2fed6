package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.text.TextUtils;

import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.lib_statistical.manager.RequestManager;
import com.sohu.lib_utils.NetworkUtils;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.ListEduUserRelatedActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.base_router.RouterPath;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;

/**
 * Created by fenglei on 17-2-20.
 */

public class HomeViewJump {
    private static final String TAG = HomeViewJump.class.getSimpleName();
    private static ContentGroup.DataBean.ContentsBean content;


    public static <T> void clickAlbum(Context context, T t, long channelListId, boolean isDts, int position) {
        AppLogger.d(TAG, "clickAlbum t ? " + t);
        if (t instanceof ContentGroup.DataBean.ContentsBean) {
            content = (ContentGroup.DataBean.ContentsBean) t;
        }
        AppLogger.d(TAG, "clickAlbum content ? " + content);
        if (content != null) {
            if (!NetworkUtils.isConnected(context)) {
                ActivityLauncher.startNetworkDialogActivity(context);
                return;
            }
            try {
                int type = Integer.parseInt(content.type);
                AppLogger.d(TAG, "clickAlbum content.type ? " + content.type);
                Gson gson = new Gson();
                HomeRecommendBean.Data.Content.Parameter parameter = null;
                try {
                    parameter = gson.fromJson(content.parameter,
                            HomeRecommendBean.Data.Content.Parameter.class);
                } catch (JsonSyntaxException e) {
                    e.printStackTrace();
                }
                switch (type) {
                    case HomeRecommendBean.LABEL_TYPE: {
                        if (channelListId != -1) {
                            if (isDts) {
                                RequestManager.getInstance().onClickLabelDtsItem(channelListId, Integer.parseInt(parameter.labelId), position == -1 ? content.order : position);
                            } else {
                                RequestManager.getInstance().onClickLabelItem(channelListId, Integer.parseInt(parameter.labelId), position == -1 ? content.order : position);
                            }
                        }
                        ActivityLauncher.startListVideoActivity(context, Integer.parseInt(parameter.labelId), isDts);
                    }
                    break;
                    case HomeRecommendBean.ALBUM_TYPE:
                    case HomeRecommendBean.PERSONAL_RECOMMEND_TYPE: {
                        int aid;
                        String trackPosition = null;
                        int recommendStrategyId = 0;
                        aid = content.albumId;
                        if (parameter != null) {
                            if (type == HomeRecommendBean.PERSONAL_RECOMMEND_TYPE) {
                                trackPosition = parameter.trackPosition;
                                recommendStrategyId = parameter.recommendStrategyId;
                            }
                            if (content.dataType == 2) {
                                aid = Integer.parseInt(parameter.videoId);
                            }
                        }
                        if (TextUtils.isEmpty(trackPosition)) {
                            if (channelListId != -1) {
                                if (isDts) {
                                    RequestManager.getInstance().onClickRecommendDtsItem(channelListId, aid, position == -1 ? content.order : position);
                                } else {
                                    RequestManager.getInstance().onClickRecommendItem(channelListId, aid, position == -1 ? content.order : position);
                                }
                            }
                            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, content.dataType, isDts, 0);
                        } else {
                            if (channelListId != -1) {
                                if (isDts) {
                                    RequestManager.getInstance().onClickRecommendDtsItem(channelListId, aid, position == -1 ? content.order : position, recommendStrategyId, trackPosition);
                                } else {
                                    RequestManager.getInstance().onClickRecommendItem(channelListId, aid, position == -1 ? content.order : position, recommendStrategyId, trackPosition);
                                }
                            }
                            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, content.dataType, isDts, 0);
                        }
                    }
                    break;
                    case HomeRecommendBean.ALL_TYPE: {
                        int ottCategoryId = -1;
                        int cateCode = -1;
                        int showType = 0;
                        try {
                            if (content.ottCategoryId != null) {
                                ottCategoryId = Integer.parseInt((String) content.ottCategoryId);
                            }
                            if (parameter != null) {
                                if (!TextUtils.isEmpty(parameter.cateCode)) {
                                    cateCode = Integer.parseInt(parameter.cateCode);
                                }
                                showType = parameter.showType;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (showType != 1) {
                            if (cateCode != -1) {
                                ActivityLauncher.startGridListActivityWithCatecode(context, ottCategoryId, cateCode,
                                        content.needLabel, content.dataType, channelListId, position == -1 ? content.order : position);
                            } else {
                                ActivityLauncher.startGridListActivity(context, ottCategoryId,
                                        content.needLabel, content.dataType, channelListId, position == -1 ? content.order : position);
                            }
                        } else {
                            ActivityLauncher.startTempletActivity(context, ottCategoryId, content.dataType, content.name, content.picUrl2);
                            RequestManager.getInstance().onClickRecommendItem(channelListId, -1, position == -1 ? content.order : position);
                        }
                    }
                    break;
                    case HomeRecommendBean.WELFARE_TYPE: {
                        ActivityLauncher.startWelfareActivity(context);
                        RequestManager.getInstance().onClickWelfare(channelListId, content.order);
                    }
                    break;
                    case HomeRecommendBean.VIDEO_TYPE: {
                        int aid;
                        int vid;
                        if (content.dataType == 2) { // PGC
                            aid = Integer.parseInt(parameter.videoId);
                            vid = Integer.parseInt(parameter.playListId);
                            RequestManager.getInstance().onClickRecommendItem(channelListId, aid, position == -1 ? content.order : position);
                            if (parameter.cateCodeSecond == null) {
                                ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, vid, content.dataType, isDts, 0);
                            } else {
                                ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, vid, content.dataType, isDts, Integer.parseInt(parameter.cateCodeSecond));
                            }
                        } else {
                            aid = Integer.parseInt(parameter.albumId);
                            vid = Integer.parseInt(parameter.tvVerId);
                            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, vid, content.dataType, isDts, Integer.parseInt(parameter.cateCode));
                            RequestManager.getInstance().onClickRecommendItem(channelListId, aid, position == -1 ? content.order : position);
                        }
                    }
                    break;
                    case HomeRecommendBean.CATEGORY_TYPE: {
                        if (content != null && content.parameter != null) {
                            if (parameter != null) {
                                ActivityLauncher.startGridListActivityWithSub(context, string2Int(parameter.ottCategoryId),
                                        parameter.subClassifyId, content.needLabel, parameter.dataType,
                                        channelListId, position == -1 ? content.order : position);
                            }
                        }
                    }
                    break;
                    case HomeRecommendBean.PRODUCER_TYPE: {
                        if (parameter != null) {
                            ActivityLauncher.startProducerActivity(context, Integer.parseInt(parameter.uid));
                            if (channelListId != -1) {
                                RequestManager.getInstance().onClickProducer(channelListId, position == -1 ? content.order : position);
                            }
                        }
                    }
                    break;
                    case HomeRecommendBean.ALL_LABEL_TYPE: {
                        ActivityLauncher.startGridListTagActivity(context, channelListId);
                    }
                    break;
                    case HomeRecommendBean.TICKET_TYPE: {
                        ActivityLauncher.startListVideoActivity(context);
                        RequestManager.getInstance().onClickTicket(channelListId, position == -1 ? content.order : position);
                    }
                    break;
                    case HomeRecommendBean.SUBJECT_TYPE: {
                        if (parameter == null) return;

                        if (parameter.subjectType == 1) {
                            ActivityLauncher.startCommingSoonActivity(context, parameter.subjectId);
                        }
                        if (parameter.subjectType == 2) {
                            ActivityLauncher.startSubjectActivity(context, parameter.subjectId, parameter.picBigUrl, parameter.picBigUrl2, parameter.picSmallUrl2);
                        }
                        RequestManager.getInstance().onClickLabelItem(channelListId, parameter.subjectId, position == -1 ? content.order : position);
                    }
                    break;
                    case HomeRecommendBean.VIP_BUY: {
                        //开通会员
                        if (parameter.activityType.equals("2")) {
                            ActivityLauncher.startPayActivity(context, PayActivity.PAY_SOURCE_HOME_RECOMMEND, content.picUrl2);
                        } else {
                            ActivityLauncher.startPayActivity(context, PayActivity.PAY_SOURCE_HOME_RECOMMEND);
                        }

                        RequestManager.getInstance().onClickAlbumVipPay(channelListId, position == -1 ? content.order : position);
                    }
                    break;
                    case HomeRecommendBean.CAROUSE_TYPE: {
                        try {
                            if (parameter != null) {
//                                long channelId = Long.parseLong(parameter.loopChannelId);
//                                ActivityLauncher.startCarouselPlayerActivity(context, channelId);
//                                RequestManager.getInstance().onClickCarousel(channelListId, position == -1 ? content.order : position);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                    case HomeRecommendBean.PERSONAL_CINEMA_TYPE: {
                        ActivityLauncher.startPersonalCinemaActivity(context);
                        RequestManager.getInstance().onClickRecommendItem(channelListId, -1, position == -1 ? content.order : position);

                    }
                    break;
                    case HomeRecommendBean.QIAN_FAN_DATI_TYPE: {
                        if (parameter != null
                                && !TextUtils.isEmpty(parameter.anchorId)
                                && !TextUtils.isEmpty(parameter.roomId)) {
//                            ActivityLauncher.startLiveActivity(context, Constant.LIVE_TYPE_INDEX, parameter.roomId, parameter.anchorId);
                            RequestManager.getInstance().onClickQianFanItem(channelListId, -1, position == -1 ? content.order : position);
                        }
                    }
                    break;
                    case HomeRecommendBean.LIVE_TV_TYPE:
                        if (parameter != null) {
                            if (StringUtil.isEmpty(parameter.liveUrl)) {
                                ActivityLauncher.startSubjectActivity(context, content.picUrl2);
                            } else {
                                ActivityLauncher.startLiveTvActivity(context, parameter.liveUrl, parameter.liveUrlb, content.id, parameter.picBigUrl);
                            }

                        }
                        break;
                    case HomeRecommendBean.COURSES_USER_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_MY, "");
                        break;
                    case HomeRecommendBean.COURSES_FAVORITE_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_COLLECTION, "");
                        break;
                    case HomeRecommendBean.COURSES_ORDER_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD, "");
                        break;
                    case HomeRecommendBean.COURSES_HISTORY_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_HISTORY, "");
                        break;
                    case HomeRecommendBean.CHANNEL_NAVIGATION_TYPE:
                        if (parameter == null) {
                            return;
                        }
                        String picUrl2="";
                        String picUrl3="";
                        if (content!=null){
                            picUrl2=content.picUrl2;
                            picUrl3=content.picUrl3;
                        }
                        ARouter.getInstance()
                                .build(RouterPath.ListView.CONFIGURATION_LIST_ACTIVITY)
                                .withString("mBackgroundImage", picUrl3)
                                .withString("mHeaderImageUrl", picUrl2)
                                .withString("mChannelId", String.valueOf(parameter.channelId))
                                .navigation();
                        break;
                    default:
                        break;
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static <T> void clickAllCategoryItem(Context context, HomeRecommendBean.Data.Content categoryContent, long channelListId, boolean isDts, int position) {
        AppLogger.d(TAG, "clickAllCategoryItem categoryContent ? " + categoryContent);
        AppLogger.d(TAG, "clickAllCategoryItem categoryContent ? " + categoryContent);
        if (categoryContent != null) {
            if (!NetworkUtils.isConnected(context)) {
                ActivityLauncher.startNetworkDialogActivity(context);
                return;
            }
            try {
                int type = Integer.parseInt(categoryContent.getType());
                AppLogger.d(TAG, "clickAllCategoryItem categoryContent.getType ? " + categoryContent.getType());
                Gson gson = new Gson();
                HomeRecommendBean.Data.Content.Parameter parameter = null;
                try {
                    parameter = gson.fromJson(categoryContent.getParameter(),
                            HomeRecommendBean.Data.Content.Parameter.class);
                } catch (JsonSyntaxException e) {
                    e.printStackTrace();
                }
                switch (type) {
                    case HomeRecommendBean.LABEL_TYPE: {
                        if (channelListId != -1) {
                            if (isDts) {
                                RequestManager.getInstance().onClickLabelDtsItem(channelListId, Integer.parseInt(parameter.labelId), position == -1 ? categoryContent.getOrder() : position);
                            } else {
                                RequestManager.getInstance().onClickLabelItem(channelListId, Integer.parseInt(parameter.labelId), position == -1 ? categoryContent.getOrder() : position);
                            }
                        }
                        ActivityLauncher.startListVideoActivity(context, Integer.parseInt(parameter.labelId), isDts);
                    }
                    break;
                    case HomeRecommendBean.ALBUM_TYPE:
                    case HomeRecommendBean.PERSONAL_RECOMMEND_TYPE: {
                        int aid;
                        String trackPosition = null;
                        int recommendStrategyId = 0;
                        aid = categoryContent.getAlbumId();
                        if (parameter != null) {
                            if (type == HomeRecommendBean.PERSONAL_RECOMMEND_TYPE) {
                                trackPosition = parameter.trackPosition;
                                recommendStrategyId = parameter.recommendStrategyId;
                            }
                            if (categoryContent.getDataType() == 2) {
                                aid = Integer.parseInt(parameter.videoId);
                            }
                        }
                        if (TextUtils.isEmpty(trackPosition)) {
                            if (channelListId != -1) {
                                if (isDts) {
                                    RequestManager.getInstance().onClickRecommendDtsItem(channelListId, aid, position == -1 ? categoryContent.getOrder() : position);
                                } else {
                                    RequestManager.getInstance().onClickRecommendItem(channelListId, aid, position == -1 ? categoryContent.getOrder() : position);
                                }
                            }
                            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, categoryContent.getDataType(), isDts, 0);
                        } else {
                            if (channelListId != -1) {
                                if (isDts) {
                                    RequestManager.getInstance().onClickRecommendDtsItem(channelListId, aid, position == -1 ? categoryContent.getOrder() : position, recommendStrategyId, trackPosition);
                                } else {
                                    RequestManager.getInstance().onClickRecommendItem(channelListId, aid, position == -1 ? categoryContent.getOrder() : position, recommendStrategyId, trackPosition);
                                }
                            }
                            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, categoryContent.getDataType(), isDts, 0);
                        }
                    }
                    break;
                    case HomeRecommendBean.ALL_TYPE: {
                        int ottCategoryId = -1;
                        int cateCode = -1;
                        int showType = 0;
                        try {
                            if (categoryContent.getOttCategoryId() != null) {
                                ottCategoryId = Integer.parseInt((String) categoryContent.getOttCategoryId());
                            }
                            if (parameter != null) {
                                if (!TextUtils.isEmpty(parameter.cateCode)) {
                                    cateCode = Integer.parseInt(parameter.cateCode);
                                }
                                showType = parameter.showType;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (showType != 1) {
                            if (cateCode != -1) {
                                ActivityLauncher.startGridListActivityWithCatecode(context, ottCategoryId, cateCode,
                                        categoryContent.isNeedLabel(), categoryContent.getDataType(), channelListId, position == -1 ? categoryContent.getOrder() : position);
                            } else {
                                ActivityLauncher.startGridListActivity(context, ottCategoryId,
                                        categoryContent.isNeedLabel(), categoryContent.getDataType(), channelListId, position == -1 ? categoryContent.getOrder() : position);
                            }
                        } else {
                            ActivityLauncher.startTempletActivity(context, ottCategoryId, categoryContent.getDataType(), categoryContent.getName(), content.picUrl2);
                            RequestManager.getInstance().onClickRecommendItem(channelListId, -1, position == -1 ? categoryContent.getOrder() : position);
                        }
                    }
                    break;
                    case HomeRecommendBean.WELFARE_TYPE: {
                        ActivityLauncher.startWelfareActivity(context);
                        RequestManager.getInstance().onClickWelfare(channelListId, categoryContent.getOrder());
                    }
                    break;
                    case HomeRecommendBean.VIDEO_TYPE: {
                        int aid;
                        if (categoryContent.getDataType() == 2) { // PGC
                            aid = Integer.parseInt(parameter.videoId);
                            RequestManager.getInstance().onClickRecommendItem(channelListId, aid, position == -1 ? categoryContent.getOrder() : position);
                            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, categoryContent.getDataType(), isDts, 0);
                        } else {
                            aid = Integer.parseInt(parameter.albumId);
                            int vid = Integer.parseInt(parameter.tvVerId);
                            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, vid, categoryContent.getDataType(), isDts, Integer.parseInt(parameter.cateCode));
                            RequestManager.getInstance().onClickRecommendItem(channelListId, aid, position == -1 ? categoryContent.getOrder() : position);
                        }
                    }
                    break;
                    case HomeRecommendBean.CATEGORY_TYPE: {
                        if (categoryContent != null && categoryContent.getParameter() != null) {
                            if (parameter != null) {
                                ActivityLauncher.startGridListActivityWithSub(context, string2Int(parameter.ottCategoryId),
                                        parameter.subClassifyId, categoryContent.isNeedLabel(), parameter.dataType,
                                        channelListId, position == -1 ? categoryContent.getOrder() : position);
                            }
                        }
                    }
                    break;
                    case HomeRecommendBean.PRODUCER_TYPE: {
                        if (parameter != null) {
                            ActivityLauncher.startProducerActivity(context, Integer.parseInt(parameter.uid));
                            if (channelListId != -1) {
                                RequestManager.getInstance().onClickProducer(channelListId, position == -1 ? categoryContent.getOrder() : position);
                            }
                        }
                    }
                    break;
                    case HomeRecommendBean.ALL_LABEL_TYPE: {
                        ActivityLauncher.startGridListTagActivity(context, channelListId);
                    }
                    break;
                    case HomeRecommendBean.TICKET_TYPE: {
                        ActivityLauncher.startListVideoActivity(context);
                        RequestManager.getInstance().onClickTicket(channelListId, position == -1 ? categoryContent.getOrder() : position);
                    }
                    break;
                    case HomeRecommendBean.SUBJECT_TYPE: {
                        if (parameter == null) return;

                        if (parameter.subjectType == 1) {
                            ActivityLauncher.startCommingSoonActivity(context, parameter.subjectId);
                        }
                        if (parameter.subjectType == 2) {
                            ActivityLauncher.startSubjectActivity(context, parameter.subjectId, parameter.picBigUrl, parameter.picBigUrl2, parameter.picSmallUrl2);
                        }
                        RequestManager.getInstance().onClickLabelItem(channelListId, parameter.subjectId, position == -1 ? categoryContent.getOrder() : position);
                    }
                    break;
                    case HomeRecommendBean.VIP_BUY: {
                        //开通会员
                        if (parameter.activityType.equals("2")) {
                            ActivityLauncher.startPayActivity(context, PayActivity.PAY_SOURCE_HOME_RECOMMEND, categoryContent.getPicUrl2());
                        } else {
                            ActivityLauncher.startPayActivity(context, PayActivity.PAY_SOURCE_HOME_RECOMMEND);
                        }

                        RequestManager.getInstance().onClickAlbumVipPay(channelListId, position == -1 ? categoryContent.getOrder() : position);
                    }
                    break;
                    case HomeRecommendBean.CAROUSE_TYPE: {
                        try {
                            if (parameter != null) {
//                                long channelId = Long.parseLong(parameter.loopChannelId);
//                                ActivityLauncher.startCarouselPlayerActivity(context, channelId);
//                                RequestManager.getInstance().onClickCarousel(channelListId, position == -1 ? categoryContent.getOrder() : position);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                    case HomeRecommendBean.PERSONAL_CINEMA_TYPE: {
                        ActivityLauncher.startPersonalCinemaActivity(context);
                        RequestManager.getInstance().onClickRecommendItem(channelListId, -1, position == -1 ? categoryContent.getOrder() : position);

                    }
                    break;
                    case HomeRecommendBean.QIAN_FAN_DATI_TYPE: {
                        if (parameter != null
                                && !TextUtils.isEmpty(parameter.anchorId)
                                && !TextUtils.isEmpty(parameter.roomId)) {
//                            ActivityLauncher.startLiveActivity(context, Constant.LIVE_TYPE_INDEX, parameter.roomId, parameter.anchorId);
                            RequestManager.getInstance().onClickQianFanItem(channelListId, -1, position == -1 ? categoryContent.getOrder() : position);
                        }
                    }
                    break;
                    case HomeRecommendBean.LIVE_TV_TYPE:
                        if (parameter != null) {
                            if (StringUtil.isEmpty(parameter.liveUrl)) {
                                ActivityLauncher.startSubjectActivity(context, categoryContent.getPicUrl2());
                            } else {
                                ActivityLauncher.startLiveTvActivity(context, parameter.liveUrl, parameter.liveUrlb, categoryContent.getId(), parameter.picBigUrl);
                            }

                        }
                        break;
                    case HomeRecommendBean.COURSES_USER_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_MY, "");
                        break;
                    case HomeRecommendBean.COURSES_FAVORITE_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_COLLECTION, "");
                        break;
                    case HomeRecommendBean.COURSES_ORDER_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD, "");
                        break;
                    case HomeRecommendBean.COURSES_HISTORY_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_HISTORY, "");
                        break;
                    default:
                        break;
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void clickAlbum(Context context, ContentGroup.DataBean.ContentsBean.AlbumListBean content, String pdna) {
        if (content != null) {
            if (!NetworkUtils.isConnected(context)) {
                ActivityLauncher.startNetworkDialogActivity(context);
                return;
            }
            int aid = content.id;
            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, 0, false, pdna);
        }
    }

    public static void clickAlbum(Context context, PgcAlbumInfo.DataEntity content, long channelListId, boolean isDts, int position) {
        if (content != null) {
            if (!NetworkUtils.isConnected(context)) {
                ActivityLauncher.startNetworkDialogActivity(context);
                return;
            }
            int aid = content.videoId;
            ActivityLauncher.startVideoDetailDts(context, Constant.PAGE_HOME, aid, content.playListId, 2, isDts, content.cateCodeSecond);
        }
    }

    private static <T> ContentGroup.DataBean.ContentsBean getContent(T t) {
        if (t instanceof ContentGroup.DataBean.ContentsBean) {
            content = (ContentGroup.DataBean.ContentsBean) t;
        }
        return content;
    }


    public static void clickAlbum(Context context, HomeRecommendBean.Data.Content content, long channelListId, boolean isDts, int position, String isVip) {
        if (content != null) {
            if (!NetworkUtils.isConnected(context)) {
                ActivityLauncher.startNetworkDialogActivity(context);
                return;
            }
            try {
                int type = Integer.parseInt(content.getType());
                Gson gson = new Gson();
                HomeRecommendBean.Data.Content.Parameter parameter = null;
                try {
                    parameter = gson.fromJson(content.getParameter(),
                            HomeRecommendBean.Data.Content.Parameter.class);
                } catch (JsonSyntaxException e) {
                    e.printStackTrace();
                }
                switch (type) {
                    case HomeRecommendBean.COURSES_USER_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_MY, isVip);
                        break;
                    case HomeRecommendBean.COURSES_FAVORITE_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_COLLECTION, isVip);
                        break;
                    case HomeRecommendBean.COURSES_ORDER_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD, isVip);
                        break;
                    case HomeRecommendBean.COURSES_HISTORY_TYPE:
                        ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_HISTORY, isVip);
                        break;
                    default:
                        break;
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void clickAlbum(Context context, int type) {
        String isVip = "";
        switch (type) {
            case HomeRecommendBean.COURSES_USER_TYPE:
                ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_MY, isVip);
                break;
            case HomeRecommendBean.COURSES_FAVORITE_TYPE:
                ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_COLLECTION, isVip);
                break;
            case HomeRecommendBean.COURSES_ORDER_TYPE:
                ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD, isVip);
                break;
            case HomeRecommendBean.COURSES_HISTORY_TYPE:
                ActivityLauncher.startEduUserRelatedActivity(context, ListEduUserRelatedActivity.LIST_INDEX_HISTORY, isVip);
                break;
            default:
                break;
        }
    }


    public static void setCornerType(HomeRecommendBean.Data.Content content, CornerTagImageView posterIV) {
        if (content != null) {
            try {
                int paddingX = posterIV.getResources().getDimensionPixelSize(R.dimen.x7);
                int paddingY = posterIV.getResources().getDimensionPixelSize(R.dimen.y7);
                posterIV.setCornerPaddingX(paddingX);
                posterIV.setCornerPaddingY(paddingY);
                int type = Integer.parseInt(content.getType());
                if (type == HomeRecommendBean.LABEL_TYPE) {
                    posterIV.setCornerType(true);
                } else {
                    String cornerType = "";
                    if (content.getDataType() == 2) {
                        Gson gson = new Gson();
                        HomeRecommendBean.Data.Content.Parameter parameter = gson.fromJson(
                                content.getParameter(), HomeRecommendBean.Data.Content.Parameter.class);
                        if (parameter != null) {
                            cornerType = parameter.cateCodeFirst;
                        }
                    } else {
                        if (content.getAlbumParam() != null) {
                            cornerType = content.getAlbumParam().getCornerType();
                        }
                    }
                    String tvIsFee = "";
                    if (null != content.getAlbumParam()) {
                        tvIsFee = content.getAlbumParam().getTvIsFee();
                    }
                    String tvIsEarly = "";
                    if (content.getAlbumParam() != null) {
                        tvIsEarly = content.getAlbumParam().gettvIsEarly();
                    }
                    int ticketInt = 0;
                    if (content.getAlbumParam() != null) {
                        ticketInt = content.getAlbumParam().getUseTicket();
                    }
                    int single = 0;
                    if (content.getAlbumParam() != null) {
                        single = content.getAlbumParam().getPaySeparate();
                    }
                    int isFeeInt = string2Int(tvIsFee);
                    int cornerTypeInt = string2Int(cornerType);
                    int tvIsEarlyInt = string2Int(tvIsEarly);
                    posterIV.setCornerTypeWithType(isFeeInt, tvIsEarlyInt, ticketInt, single, cornerTypeInt);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static int string2Int(String stringValue) {
        int intValue = -1;
        try {
            if (!TextUtils.isEmpty(stringValue)) {
                intValue = Integer.parseInt(stringValue);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return intValue;
    }

}
