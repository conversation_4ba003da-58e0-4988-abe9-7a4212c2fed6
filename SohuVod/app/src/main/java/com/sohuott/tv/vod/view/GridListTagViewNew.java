package com.sohuott.tv.vod.view;

import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.lib.model.GridListTagMenuModel;

import java.util.List;

/**
 * Created by wenjingbian on 2017/6/27.
 */

public interface GridListTagViewNew {

    void displayTagErrorView(String errorStr);

    void displayTagLoadingView();

    void displayTagView(AllLabel.Data data);

    void addTagItems(AllLabel.Data data);

    void addTagItemsError();

    void displayLeftListView(List<GridListTagMenuModel.DataEntity> data);

    void displayParentErrorView();
}
