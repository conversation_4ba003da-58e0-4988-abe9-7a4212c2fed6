package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;



import androidx.annotation.NonNull;

import com.nineoldandroids.animation.ValueAnimator;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

import java.lang.ref.WeakReference;


/**
 * in carousePlayList,display the flag that being played
 *
 * <AUTHOR>
 * @date 2017/7/5
 */
public class PlayingView extends View {

    private Paint mPaint;
    private float mWidth;
    private float mHeight;
    private ValueAnimator valueAnimator;
    private float rx = 4, ry = 4;
    float baseIntervalSpace;
    float minRectHeight;
    float maxRectHeight;
    RectF rectF1, rectF2, rectF3, rectF4, rectF5;
    float mAnimatedValue;

    private int mViewColor = Color.YELLOW;


    public PlayingView(Context context) {
        super(context);
    }

    public PlayingView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }


    public PlayingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.PlayingView, defStyleAttr, 0);
            if (a != null) {
                mViewColor = a.getColor(R.styleable.PlayingView_play_color, Color.YELLOW);
                a.recycle();
            }
        }
        initPaint();
    }

    private void initPaint() {
        mPaint = new Paint();
        mPaint.setColor(mViewColor);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setAntiAlias(true);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.save();
        canvas.translate(0, mHeight / 2);

        rectF1 = new RectF(0, -(mAnimatedValue + minRectHeight), baseIntervalSpace, mAnimatedValue + minRectHeight);
        rectF2 = new RectF(baseIntervalSpace * 2, -(maxRectHeight - mAnimatedValue), baseIntervalSpace * 3, (maxRectHeight - mAnimatedValue));
        rectF3 = new RectF(baseIntervalSpace * 4, -(mAnimatedValue + minRectHeight), baseIntervalSpace * 5, mAnimatedValue + minRectHeight);
        rectF4 = new RectF(baseIntervalSpace * 6, -(maxRectHeight - mAnimatedValue), baseIntervalSpace * 7, (maxRectHeight - mAnimatedValue));
        rectF5 = new RectF(baseIntervalSpace * 8, -(mAnimatedValue + minRectHeight), mWidth, mAnimatedValue + minRectHeight);
        canvas.drawRoundRect(rectF1, rx, ry, mPaint);
        canvas.drawRoundRect(rectF2, rx, ry, mPaint);
        canvas.drawRoundRect(rectF3, rx, ry, mPaint);
        canvas.drawRoundRect(rectF4, rx, ry, mPaint);
        canvas.drawRoundRect(rectF5, rx, ry, mPaint);

        canvas.restore();

    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mHeight = getMeasuredHeight();
        mWidth = getMeasuredWidth();
        baseIntervalSpace = mWidth / 9;
        minRectHeight = mWidth / 18;
        maxRectHeight = mHeight / 2;
    }

    Handler mAnimHandler = new AnimationHandler(this);

    private void stopAnimation() {
        if (valueAnimator != null) {
            LibDeprecatedLogger.d("stopAnimation");
            clearAnimation();
            valueAnimator.setRepeatCount(0);
            valueAnimator.cancel();
            valueAnimator.removeAllUpdateListeners();
            valueAnimator.end();
            mAnimatedValue = 0f;
            valueAnimator = null;
            postInvalidate();
        }
        mAnimHandler.removeMessages(0);
    }

    ValueAnimator.AnimatorUpdateListener mAnimatorUpdateListener = new ValueAnimator.AnimatorUpdateListener() {
        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
            mAnimatedValue = (float) animation.getAnimatedValue();
            invalidate();
        }
    };

    private void initAnim(float endValue) {
        LibDeprecatedLogger.d("initAnim: " + endValue);
        valueAnimator = ValueAnimator.ofFloat(0f, endValue);
        valueAnimator.setDuration(320);
        valueAnimator.setInterpolator(new LinearInterpolator());
        valueAnimator.setRepeatCount(ValueAnimator.INFINITE);
        valueAnimator.setRepeatMode(ValueAnimator.REVERSE);
        valueAnimator.addUpdateListener(mAnimatorUpdateListener);
    }

    public void show() {
        if (valueAnimator == null || !valueAnimator.isRunning()) {
            setVisibility(View.VISIBLE);
            mAnimHandler.removeMessages(0);
            mAnimHandler.sendEmptyMessageDelayed(0, 200);
        }
    }


    public void hide() {
        if (getVisibility() == VISIBLE) {
            stopAnimation();
            setVisibility(View.GONE);
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (mNeedStartAnim && getVisibility() == VISIBLE) {
            LibDeprecatedLogger.d("restart anim");
            mAnimHandler.removeMessages(0);
            mAnimHandler.sendEmptyMessageDelayed(0, 200);
        }
        mNeedStartAnim = false;
    }

    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        if (!(changedView instanceof PlayingView)) {
            if (visibility != VISIBLE && valueAnimator != null && valueAnimator.isRunning()) {
                mNeedStartAnimAfterVisibilityChanged = true;
                stopAnimation();
            } else if (visibility == VISIBLE && mNeedStartAnimAfterVisibilityChanged) {
                mAnimHandler.removeMessages(0);
                mAnimHandler.sendEmptyMessageDelayed(0, 200);
                mNeedStartAnimAfterVisibilityChanged = false;
            }
        }
    }

    private boolean mNeedStartAnim, mNeedStartAnimAfterVisibilityChanged;

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        LibDeprecatedLogger.d("onDetachedFromWindow");
        stopAnimation();
        mNeedStartAnim = true;
    }

    private static class AnimationHandler extends Handler {
        private WeakReference<PlayingView> mPlayingView;

        public AnimationHandler(PlayingView playingView) {
            mPlayingView = new WeakReference<>(playingView);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (mPlayingView != null && mPlayingView.get() != null) {
                mPlayingView.get().initAnim(mPlayingView.get().maxRectHeight - mPlayingView.get().minRectHeight);
                mPlayingView.get().valueAnimator.start();
            }
        }
    }
}
