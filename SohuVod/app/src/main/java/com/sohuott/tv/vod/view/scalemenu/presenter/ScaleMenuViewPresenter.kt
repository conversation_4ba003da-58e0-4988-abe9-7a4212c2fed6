package com.sohuott.tv.vod.view.scalemenu.presenter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnFocusChangeListener
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.base_leanback.persenter.BasePresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.bumptech.glide.Glide
import com.sohuott.tv.vod.AppLogger

import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.getResDrawable
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.customview.CircleImageView
import com.sohuott.tv.vod.view.scalemenu.PileLayout
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleMenuItem
import kotlin.math.sign

class ScaleMenuViewPresenter constructor(val context: Context) :
    BasePresenter<LeanBackViewHolder>(R.layout.item_scale_menu_view) {
    private var isCan: Boolean = true
    override fun bindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as ScaleMenuItem
        val tvName = viewHolder.getView<TextView>(R.id.tv_item_scale_menu_name)
        val layoutMenu = viewHolder.getView<ConstraintLayout>(R.id.layout_menu)
        val layout = viewHolder.getView<PileLayout>(R.id.layout_only_see_img)
        layout.removeAllViews()
        //是否是 展示图片的tab
        val isUseImage = !item.imagesUrl.isNullOrEmpty()
        if (isUseImage) {
            //循环添加图片View
            item.imagesUrl!!.forEach {
                val imageView: CircleImageView =
                    LayoutInflater.from(context)
                        .inflate(R.layout.item_praise_60, layout, false) as CircleImageView
                if (item.hasSelect) {
                    imageView.borderColor = ContextCompat.getColor(context, R.color.tv_color_ff6247)
                    imageView.borderWidth = 4
                }
                Glide.with(context).load(it).into(imageView)
                layout.addView(imageView);
            }
            //隐藏控件
            tvName.gone()
            layout.visible()
            //重新监听为了 不复用 发生错乱
            layoutMenu.onFocusChangeListener = OnFocusChangeListener { view, hasFocus ->
//                layout.removeAllViews()
                val viewSize = layout.childCount
                for (index in 0 until viewSize) {
                    val viewChild: CircleImageView = layout.getChildAt(index) as CircleImageView
                    if (hasFocus) {
                        viewChild.borderColor =
                            ContextCompat.getColor(context, R.color.tv_color_ff6247)
                        viewChild.borderWidth = 4
                    } else {
                        viewChild.borderWidth = 0
                    }
                }
            }
        } else {
            layout.gone()
            tvName.visible()
            if (!layoutMenu.hasFocus()) {
                if (item.hasSelect) {
                    tvName.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.tv_color_ff6247
                        )
                    )
                } else {
                    tvName.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.tv_color_e6e8e8ff
                        )
                    )
                }
            }
            layoutMenu.setOnFocusChangeListener { _, hasFocus ->
                AppLogger.v("layoutMenu :${hasFocus} ")
                if (hasFocus) {
                    if (isCan) {
                        layoutMenu.background =
                            context.getResDrawable(R.drawable.bg_gradual_change_start_e4705c_end_c53d3d_radius_32)
                        tvName.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.tv_color_e8e8ff
                            )
                        )
                    }
                } else {
                    if (item.hasSelect) {
                        tvName.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.tv_color_ff6247
                            )
                        )
                    } else {
                        tvName.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.tv_color_e6e8e8ff
                            )
                        )
                    }
                    layoutMenu.background = null
//                tvName.setTextColor(ContextCompat.getColor(context, R.color.tv_color_ff6247))
                }
            }
        }

        tvName.text = item.name
        AppLogger.v("ScaleMenuViewPresenter ${item.name} hasSelect:${item.hasSelect}")


    }

    fun setIsCanFocus(isCan: Boolean) {
        this.isCan = isCan
    }

    override fun createViewHolderBefore(view: View) {
        view.setOnFocusChangeListener { _, hasFocus ->
            AppLogger.v("ScaleMenuViewPresenter view hasFocus:$hasFocus")
        }

    }

}