package com.sohuott.tv.vod.activity.setting

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.http.SslError
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Gravity
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.*
import androidx.core.content.ContextCompat
import com.base_web.BaseWebViewActivity
import com.lib_viewbind_ext.viewBinding
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.goneAndVisible
import com.sohuott.tv.vod.activity.base.invisibleAndVisible
import com.sohuott.tv.vod.databinding.ActivityShowPrivacyLayoutBinding
import com.sohuott.tv.vod.lib.api.NetworkApi
import com.sohuott.tv.vod.lib.model.UlrChangeInfo
import io.reactivex.observers.DisposableObserver

/**
 *
 * @Description
 * @date 2022/3/21 12:15
 * <AUTHOR>
 * @Version 1.0
 */
class ShowPrivacyWebViewActivity : BaseWebViewActivity(R.layout.activity_show_privacy_layout) {

    private var mViewBinding: ActivityShowPrivacyLayoutBinding? = null
    private val _binding by viewBinding(onViewDestroyed = {
        mViewBinding = null
    }, ActivityShowPrivacyLayoutBinding::bind)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = _binding
        initWebView(mViewBinding!!.webPrivacy)
        //设置渐变背景
        getWebView()?.background = ContextCompat.getDrawable(this, R.drawable.white_border)
        //去除加载网页的白色背景
        getWebView()?.setBackgroundColor(Color.TRANSPARENT)
        mViewBinding!!.tvError.text = getString(R.string.home_loading_error)
        initData()
        setDisplay()
    }

    private fun setDisplay() {
        val win = this.window
        val density = resources.displayMetrics.density.toInt()
        win.decorView.setPadding(density, density, density, density)
        val lp = win.attributes
        lp.width = WindowManager.LayoutParams.MATCH_PARENT
        lp.height = WindowManager.LayoutParams.MATCH_PARENT
        lp.gravity = Gravity.TOP or Gravity.LEFT
        win.attributes = lp
    }

    private fun initData() {
        val showType = intent.getStringExtra(PUT_KEY_SHOW_TYPE)
        if (showType.isNullOrEmpty()) {
            mViewBinding?.webContainerLayout?.goneAndVisible(mViewBinding?.tvError)
            return
        }
        changeUrl(showType)
    }

    override fun onDestroy() {
        mViewBinding?.webContainerLayout?.removeAllViews()
        mViewBinding?.webPrivacy?.destroy()
        mViewBinding?.pbLoading?.hide()
        super.onDestroy()
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.dialog_scale_in, R.anim.dialog_scale_out)
    }

    private fun changeUrl(showType: String) {
        mViewBinding?.webContainerLayout?.invisibleAndVisible(mViewBinding?.pbLoading)
        NetworkApi.getUserPrivacyWebUrl(object : DisposableObserver<UlrChangeInfo?>() {
            override fun onNext(value: UlrChangeInfo?) {
                val urlMap: Map<String, Any>? = value?.data
                if (urlMap == null || urlMap.isEmpty()) {
                    return
                }
                val url = urlMap[showType] as String?
                AppLogger.v(url ?: "WebView 加载url 为空")
                if (url.isNullOrEmpty()) {
                    onError()
                    return
                }
                var isError = false
                getWebView()?.webViewClient = object : WebViewClient() {

                    override fun onReceivedError(
                        p0: WebView?,
                        p1: WebResourceRequest?,
                        p2: WebResourceError?
                    ) {
                        super.onReceivedError(p0, p1, p2)
                        AppLogger.v("WebView 加载url onReceivedError")
                        isError = true
                    }

                    override fun onReceivedHttpError(
                        p0: WebView?,
                        p1: WebResourceRequest?,
                        p2: WebResourceResponse?
                    ) {
                        super.onReceivedHttpError(p0, p1, p2)
                        AppLogger.v("WebView 加载url onReceivedHttpError")
                        isError = true
                    }

                    //                    override fun onReceivedSslError(
//                        p0: WebView?,
//                        p1: SslErrorHandler?,
//                        p2: SslError?
//                    ) {
//                        p0
//                        super.onReceivedSslError(p0, p1, p2)
//                        AppLogger.v("WebView 加载url onReceivedSslError")
////                        isError = true
////                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
////                            getWebView()?.settings?.mixedContentMode =
////                                MIXED_CONTENT_COMPATIBILITY_MODE
////                        }
//
//                    }
                    override fun onReceivedSslError(
                        p0: WebView?,
                        handler: SslErrorHandler?,
                        p2: SslError?
                    ) {
                        AppLogger.v("WebView 加载url onReceivedSslError:${p2.toString()}")
                        handler?.proceed()
                    }

                    override fun onPageFinished(p0: WebView?, p1: String?) {
                        super.onPageFinished(p0, p1)
                        AppLogger.v("WebView 加载url onPageFinished")
                        if (!isError) {
                            onSuccess()
                        } else {
                            onError()
                        }
                    }
                }
                try {
                    getWebView()?.loadUrl(url)
                } catch (e: Throwable) {
                    AppLogger.e("loadUrl catch 出错 url:{$url} 异常信息:${e.localizedMessage}")
                    onError()
                }
            }

            override fun onError(e: Throwable) {
                e.localizedMessage?.let { "onError" + AppLogger.e(it.toString()) }
                onError()
            }

            override fun onComplete() {}

        })

    }

    private fun onError() {
        mViewBinding?.webContainerLayout?.goneAndVisible(mViewBinding?.tvError)
        mViewBinding?.pbLoading?.gone()
        AppLogger.v("WebView 加载url onError")
    }

    private fun onSuccess() {
        val dm = DisplayMetrics();
        windowManager.defaultDisplay.getMetrics(dm);
        val screenHeight = dm.heightPixels;
        mViewBinding?.tvError?.gone()
        mViewBinding?.pbLoading?.goneAndVisible(mViewBinding?.webContainerLayout)
        getWebView()?.descendantFocusability = ViewGroup.FOCUS_BEFORE_DESCENDANTS
        AppLogger.v("WebView 加载url onSuccess")
    }

    override fun onBackPressed() {
        super.onBackPressed()
        finish()
    }


    companion object {

        /**
         * sdk目录
         */
        const val SDK = "sdk"

        /**
         * 搜狐隐私协议
         */
        const val SOHU_PRIVATE_AGREEMENT = "sohu_private_agreement"

        /**
         * 隐私政策
         */
        const val PRIVATE_AGREEMENT = "private_agreement"

        /**
         * 儿童隐私政策
         */
        const val CHILD_PRIVATE_AGREEMENT = "child_private_agreement"

        /**
         * 用户协议
         */
        const val USER_AGREEMENT = "user_agreement"


        /**
         * 收集信息
         */
        const val COLLECT = "collect"

        /**
         * 共享信息
         */
        const val SHARE = "share"

        /**
         * 权限说明
         */
        const val PERMISSION = "permission"

        /**
         * 付费协议
         */
        const val PAY = "pay"
        private const val PUT_KEY_SHOW_TYPE = "show_type"

        @JvmStatic
        fun actionStart(context: Context, showType: String) {
            val intent = Intent(context, ShowPrivacyWebViewActivity::class.java)
            intent.putExtra(PUT_KEY_SHOW_TYPE, showType)
            context.startActivity(intent)
        }
    }
}