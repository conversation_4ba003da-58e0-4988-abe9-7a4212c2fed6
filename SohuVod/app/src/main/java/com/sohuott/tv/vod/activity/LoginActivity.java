package com.sohuott.tv.vod.activity;

import static android.view.ViewGroup.FOCUS_BLOCK_DESCENDANTS;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.RecyclerView;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.StringUtil;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.common.Listener;
import com.sohuott.tv.vod.account.login.LoginApi;
import com.sohuott.tv.vod.adapter.LoginListAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.fragment.BaseLoginFragment;
import com.sohuott.tv.vod.fragment.RegisterFragment;
import com.sohuott.tv.vod.fragment.SohuLoginFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.push.base.BaseMessageData;
import com.sohuott.tv.vod.lib.rvhelper.BaseQuickAdapter;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.model.DividerItemDecoration;
import com.sohuott.tv.vod.presenter.AgreementPresenter;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.videodetail.activity.service.VideoDetailServiceManger;
import com.sohuott.tv.vod.view.AgreementView;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;

import java.lang.ref.WeakReference;
import java.util.HashMap;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by xianrongchen on 2017-03-28.
 * 登录页面
 */
public class LoginActivity extends BaseFragmentActivity {


    private static final String TAG = LoginActivity.class.getSimpleName();
    Bundle mBundle = new Bundle();
    private boolean mIsQrcodeVisible = true;
    private View mPreviousFocusedView;
    private LinearLayout mSyncPlayHistoryContainer;
    private RecyclerView mListView;
    private LoginListAdapter mListAdapter;
    private CustomLinearLayoutManager mListLayoutMgr;
    private FragmentManager mFragmentMgr;
    private BaseLoginFragment mSohuLoginFragment;
    //    private InputLoginFragment mInputLoginFragment;
    private RegisterFragment mRegisterFragment;

    private RelativeLayout mListContainerLayout;

    private AgreementView mAgreementView;

    private int mCurrentPosition = -1;
    private View mFocusedView;
    private static final int MSG_SEND_EVENT = 1;
    private static final int TIME_DELAY = 5 * 100;

    private int channelId;
    private String pageSource;

    private HashMap<String, String> mPathInfo;

    private static class InnerHandler extends Handler {
        private WeakReference<LoginActivity> mAcWrapper;

        InnerHandler(LoginActivity activity) {
            mAcWrapper = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            LoginActivity ac = mAcWrapper.get();
            if (ac == null) {
                return;
            }
            int what = msg.what;
            switch (what) {
                case MSG_SEND_EVENT:
                    ac.changeFragment();
                    break;
                default:
                    break;
            }
        }
    }

    private Handler mHandler = new InnerHandler(this);

    DisposableObserver mPGidDisposable;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        AppLogger.d(TAG, "onCreate");
        LibDeprecatedLogger.d("onCreate");

        setDisplay();

        mListView = (RecyclerView) findViewById(R.id.list);
        final LoadingView loadingView = (LoadingView) findViewById(R.id.loading);
        loadingView.hide();
        mSyncPlayHistoryContainer = (LinearLayout) findViewById(R.id.sync_play_history_container);
        initData();

        mListContainerLayout = (RelativeLayout) findViewById(R.id.list_container);


        getWindow().getDecorView().setBackgroundResource(R.drawable.launcher_bg);

        setPageName("6_login");
        RequestManager.onEvent("6_login", "100001", null, null, null, null, null);
        if (TextUtils.isEmpty(UserInfoHelper.getPgid())) {
            LibDeprecatedLogger.d("Pgid is null on disk!");
            loadingView.show();
            mPGidDisposable = new DisposableObserver() {
                @Override
                public void onNext(Object value) {
                    loadingView.hide();
                    showData();
                }

                @Override
                public void onError(Throwable e) {
                    loadingView.hide();
                    startActivity(new Intent(LoginActivity.this, PGidExceptionActivity.class));
                    finishLoginActivity(false);

                }

                @Override
                public void onComplete() {

                }
            };
            LoginApi.getPassportGid(this, mPGidDisposable);
        } else {
            showData();
        }

        // 初始化用户协议页面，并且获取协议数据
        mAgreementView = new AgreementView(this, AgreementPresenter.NAME_USER_PRIVACY, AgreementPresenter.TYPE_JOIN);

    }

    //设置窗口大小
    private void setDisplay() {
        //设置弹出窗口与屏幕对齐
        Window win = this.getWindow();
        int density = (int) (getResources().getDisplayMetrics().density);
        //设置内边距，这里设置为0
        win.getDecorView().setPadding(1 * density, 1 * density, 1 * density, 1 * density);
        WindowManager.LayoutParams lp = win.getAttributes();
        //设置窗口宽度
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        //设置窗口高度
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        //设置Dialog位置
        lp.gravity = Gravity.TOP | Gravity.LEFT;
        win.setAttributes(lp);
    }

    private boolean mIsChild = false;

    /**
     * Decide whether child channel
     *
     * @param source
     */
    private void getLoginSource(int source) {
        switch (source) {
            case Constant.HOME_SOURCE:
                mIsChild = false;
                break;
            case Constant.CHILD_SOURCE:
                mIsChild = true;
                break;
            default:
                mIsChild = false;
                break;
        }
    }

    private void showData() {
        mFragmentMgr = getSupportFragmentManager();
        mSohuLoginFragment = SohuLoginFragment.newInstance(mBundle);
        mFragmentMgr.beginTransaction()
                .add(R.id.fragment_container, mSohuLoginFragment)
                .commit();
        mListView.setAdapter(mListAdapter);
    }

    private void initData() {
        Intent intent = getIntent();
        mBundle.putBoolean("normalLogin", true);
        if (null != intent) {
            Bundle bundle = intent.getExtras();
            if (null != bundle) {

                // Judge channels By Jason
                int source = bundle.getInt(ParamConstant.PARAM_SOURCE, 0);
                this.getLoginSource(source);

                int aid = bundle.getInt(ParamConstant.PARAM_AID, 0);
                int vid = bundle.getInt(ParamConstant.PARAM_VID, 0);
                channelId = bundle.getInt(ParamConstant.PARAM_CHANNEL_ID, 0);
                String videoName = bundle.getString(ParamConstant.PARAM_ALBUM_TITLE, "");
                pageSource = bundle.getString(ParamConstant.PARAM_PAGE_SOURCE, "");
                boolean isNormalLogin = bundle.getBoolean(ParamConstant.PARAM_IS_NORMAL_LOGIN, true);

                mPathInfo = new HashMap<>();
                mPathInfo.put("pageId", StringUtil.toString(channelId));

                mBundle.putInt(ParamConstant.PARAM_SOURCE, source);
                mBundle.putBoolean("normalLogin", isNormalLogin);
                mBundle.putString("videoName", videoName);
                mBundle.putInt("aid", aid);
                mBundle.putInt("vid", vid);
                mBundle.putBoolean("fromVideoDetail", pageSource.equals(ParamConstant.PAGE_SOURCE_VIDEO_DETAIL));
                mBundle.putBoolean("isTeenager", pageSource.equals(ParamConstant.PAGE_SOURCE_TEENAGER));


            }
        }
        initListData();
    }

    private void initListData() {
        if (null == mListAdapter) {
            mListAdapter = new LoginListAdapter(this);
            mListAdapter.setOnItemFocusChangedListener(new BaseQuickAdapter.OnItemFocusChangedListener() {
                @Override
                public void onItemFocusChanged(BaseQuickAdapter adapter, View view, int position, boolean hasFocus) {
                    final TextView title = (TextView) view.findViewById(R.id.name);
                    final ImageView icon = (ImageView) view.findViewById(R.id.image);
                    final TextView desc = (TextView) view.findViewById(R.id.desc);
                    final RelativeLayout focusLayout = (RelativeLayout) view.findViewById(R.id.left_view);
                    final ImageView arrow = (ImageView) view.findViewById(R.id.arrow);

                    if (hasFocus) {
                        if (position != mCurrentPosition) {
                            mCurrentPosition = position;
                            mHandler.removeMessages(MSG_SEND_EVENT);
                            mHandler.sendEmptyMessageDelayed(MSG_SEND_EVENT, TIME_DELAY);
                        }
                        mFocusedView = view;
                    }

                    if (hasFocus) {
                        arrow.setVisibility(View.VISIBLE);
                        if (position == LoginListAdapter.POSITION_ZERO) {
                            View itemView = mListView.findViewHolderForLayoutPosition(LoginListAdapter.POSITION_FIRST).itemView;
                            itemView.findViewById(R.id.arrow).setVisibility(View.GONE);
                            ((TextView) itemView.findViewById(R.id.name)).setTextColor(Color.parseColor("#e8e8ff"));
                            ((TextView) itemView.findViewById(R.id.desc)).setTextColor(Color.parseColor("#e8e8ff"));
                            itemView.findViewById(R.id.desc).setAlpha(0.7f);

                        } else {
                            title.setTextColor(Color.parseColor("#e8e8ff"));
                            desc.setTextColor(Color.parseColor("#e8e8ff"));
                            desc.setAlpha(0.7f);
                            View itemView = mListView.findViewHolderForLayoutPosition(LoginListAdapter.POSITION_ZERO).itemView;
                            itemView.findViewById(R.id.arrow).setVisibility(View.GONE);
                        }
                    } else {
                        if (position == LoginListAdapter.POSITION_ZERO) {
                            arrow.setVisibility(View.GONE);
                        } else {
                            title.setTextColor(Color.parseColor("#FF6247"));
                            desc.setTextColor(Color.parseColor("#FF6247"));
                            desc.setAlpha(1f);
                        }
                    }

                    focusLayout.setSelected(hasFocus);
                    icon.setAlpha(hasFocus ? 1f : 0.7f);
                }
            });
        }
        mListLayoutMgr = new CustomLinearLayoutManager(this);
        mListLayoutMgr.setOrientation(CustomLinearLayoutManager.VERTICAL);
        mListView.addItemDecoration(new DividerItemDecoration(0, 0, 0, getResources().getDimensionPixelSize(R.dimen.y8)));
        mListView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mListView.setHasFixedSize(true);
        mListView.setItemAnimator(new DefaultItemAnimator());
        mListView.setLayoutManager(mListLayoutMgr);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            mListView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_LEFT) {
                View focusedView = getCurrentFocus();
                if (focusedView.getId() == R.id.root_view) {
                    return true;
                }
                if (focusedView.getId() == R.id.login_input_1 ||
                        focusedView.getId() == R.id.login_input_4 ||
                        focusedView.getId() == R.id.login_input_7 ||
                        focusedView.getId() == R.id.login_input_clear ||
                        focusedView.getId() == R.id.get_msg_captcha) {
                    if (null != mListAdapter && null != mFocusedView) {
                        mFocusedView.requestFocus();
                        mPreviousFocusedView = focusedView;
                        return true;
                    }
                }

                mPreviousFocusedView = focusedView;
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                if (mCurrentPosition == Constant.POSITION_ZERO) {
                    return true;
                }

                View focusedView = getCurrentFocus();
                if (focusedView.getId() == R.id.root_view) {
                    if (mRegisterFragment != null &&
                            mRegisterFragment.isVisible()) {
                        View itemView = mListView.findViewHolderForLayoutPosition(LoginListAdapter.POSITION_FIRST).itemView;
                        itemView.findViewById(R.id.arrow).setVisibility(View.VISIBLE);
                        mRegisterFragment.getNextFocus().requestFocus();
                        mPreviousFocusedView = focusedView;
                        return true;
                    }
                }
                mPreviousFocusedView = focusedView;
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                View focusedView = getCurrentFocus();
                mPreviousFocusedView = focusedView;
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
                View focusedView = getCurrentFocus();
                if (mCurrentPosition == Constant.POSITION_FIRST && focusedView.getId() == R.id.root_view) {
                    return super.dispatchKeyEvent(event);
                }
                mPreviousFocusedView = focusedView;
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_CENTER
                    || event.getKeyCode() == KeyEvent.KEYCODE_ENTER) {
                if (mRegisterFragment != null && !mRegisterFragment.isHidden()) {
                    mListView.setDescendantFocusability(FOCUS_BLOCK_DESCENDANTS);
                }
            }
        } else if (event.getAction() == KeyEvent.ACTION_UP
                && event.getKeyCode() == KeyEvent.KEYCODE_MENU) {
            LibDeprecatedLogger.d("Show user agreement!");
            mAgreementView.show(new View(this));

        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK
                || keyCode == KeyEvent.KEYCODE_ESCAPE) {
            quitQrcode();
            finishLoginActivity(false);
            return true;
        }
        return super.onKeyUp(keyCode, event);
    }

    public void finishLoginActivity(boolean isRePlay) {
        VideoDetailServiceManger.getInstants().onStartActivityPlay(isRePlay);
        finish();
    }


    public void showSyncLoading() {
        mSyncPlayHistoryContainer.setVisibility(View.VISIBLE);
    }

    public void hideSyncLoading() {
        mSyncPlayHistoryContainer.setVisibility(View.GONE);
    }


    @Override
    protected void onPause() {
        super.onPause();
        mHandler.removeMessages(MSG_SEND_EVENT);
    }

    private void changeFragment() {
        if (isFinishing()) {
            return;
        }
        if (mCurrentPosition != 0) {
            if (mIsQrcodeVisible) {
                quitQrcode();
                mIsQrcodeVisible = false;
            }
        } else {
            if (!mIsQrcodeVisible) {
                mIsQrcodeVisible = true;
            }
        }
        switch (mCurrentPosition) {
            case LoginListAdapter.POSITION_ZERO:
                showFrag(mSohuLoginFragment, mRegisterFragment, "1001");
                break;
            case LoginListAdapter.POSITION_FIRST:
                if (null == mRegisterFragment) {
                    mRegisterFragment = RegisterFragment.newInstance(mBundle);
                }
                showFrag(mRegisterFragment, mSohuLoginFragment, "1002");
                break;
            default:
                break;
        }
    }

    private void showFrag(Fragment fragment, Fragment hideFrag1, String etype) {
        FragmentTransaction mFragmentTransaction = mFragmentMgr.beginTransaction();
        if (null != hideFrag1) {
            mFragmentTransaction.hide(hideFrag1);
        }
        if (!fragment.isAdded()) {
            mFragmentTransaction.add(R.id.fragment_container, fragment);
        }
        if (fragment != null) {
            mFragmentTransaction.show(fragment).commitAllowingStateLoss();

            HashMap pathInfo = new HashMap();
            pathInfo.put("pageId", etype);
            RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), pathInfo, null, null);
//            RequestManager.onEvent(etype, "100001", null, null, null, null, null);
        }
    }


    private void quitQrcode() {
        LoginApi.getQrcodeQuit(new Listener<BaseMessageData>() {
            @Override
            public void onSuccess(BaseMessageData response) {
                LibDeprecatedLogger.d("getQrcodeQuit(): response = " + response);
                if (null != response) {
                    String message = response.getMessage();
                    int status = response.getStatus();

                    if (status == 200) {

                    } else {
                        ToastUtils.showToast2(LoginActivity.this, message);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("getQrcodeQuit(): onError() = " + e.toString());
            }
        });
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mPGidDisposable != null) {
            mPGidDisposable.dispose();
        }
        if (mAgreementView != null) {
            mAgreementView.release();
        }
    }

    @Override
    public void finish() {
        Log.d("LoginActivity", "finish: ");

        if (!StringUtil.isEmpty(pageSource) && pageSource.equals(Constant.LAUNCHER_SOURCE)) {
            if (channelId == 0) {
                RequestManager.getInstance().onAllEvent(new EventInfo(10132, "imp"), null, null, null);
            } else {
                RequestManager.getInstance().onAllEvent(new EventInfo(10132, "imp"), mPathInfo, null, null);
            }
        }
        super.finish();
        //在此时设置转场动画
        overridePendingTransition(R.anim.dialog_scale_in, R.anim.dialog_scale_out);
    }
}
