package com.sohuott.tv.vod.activity;

import static com.sohuott.tv.vod.widget.TopBar.INDEX_SEARCH_BUTTON;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.FilterItemAdapter;
import com.sohuott.tv.vod.adapter.GridListLeftAdapterNew;
import com.sohuott.tv.vod.adapter.GridListTagAdapterNew;
import com.sohuott.tv.vod.adapter.GridListVideoAdapterNew;
import com.sohuott.tv.vod.adapter.NewFilterAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.lib.model.FilterBean;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.MenuListBean;
import com.sohuott.tv.vod.lib.model.TopInfo;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.push.event.LoginSuccessEvent;
import com.sohuott.tv.vod.lib.push.event.LogoutEvent;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.GridListVideoPresenterImplNew;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.utils.SouthMediaUtil;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.CustomRecyclerViewNew;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.GridListViewNew;
import com.sohuott.tv.vod.widget.GridScrollViewForTouch;
import com.sohuott.tv.vod.widget.UserRelatedHeaderView;

import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by wenjingbian on 2017/5/31.
 */

public class GridListActivityNew extends BaseActivity implements GridListViewNew, GridListLeftAdapterNew.IGridListLeftListener,
        FilterItemAdapter.SelectItemChangeCallback, ViewTreeObserver.OnGlobalFocusChangeListener, UserRelatedHeaderView.HeaderViewFocusController {

    //Message.what value of request video list
    private static final int MSG_REFRESH = 1;
    //Span count of VRS video
    public static final int SPAN_VRS = 5;
    //Span count of PGC video
    public static final int SPAN_PGC = 4;

    private UserRelatedHeaderView headerView;
    private RelativeLayout mGridView, layout_filter;
    private LinearLayout mErrorView, mChildErrorView;
    private LoadingView mLoadingView, mChildLoadingView;
    private CustomLinearRecyclerView mLeftList, mHeaderList;
    private CustomRecyclerViewNew mGridList;
    private ImageView iv_filter;
    private TextView tv_grid_curr_line, tv_grid_sum_line, tv_grid_divider, tv_grid_type, tv_filter,
            tv_error_hint;
    private FocusBorderView mFocusView;
    private PopupWindow mFilterWindow;
    private View mRightView;

    private CustomLinearLayoutManager mLeftListLayoutManager;
    private CustomLinearLayoutManager mHeaderLayoutManager;
    private CustomGridLayoutManager mGridListLayoutManager;

    private GridListLeftAdapterNew mGridListLeftAdapter;
    private GridListTagAdapterNew mGridListHeaderAdapter;
    private GridListVideoAdapterNew mGridListAdapter;

    private GridListVideoPresenterImplNew mPresenterImpl;
    private MyHandler mHandler;

    private List<Integer> mPreFilterValue;
    private int mOttCategoryId = Integer.MAX_VALUE;
    private int mVideoType = 0; //0==VRS; 1==PGC
    private int mCateCodeFirst = 0; //identify whether is sohu class video or not(-->CornerTagImageView.CORNER_TYPE_SOHUCLASS)
    private int mSubCategoryId = 0;
    private boolean isShowHeader = false; //identify whether show head view in VRS list
    private boolean isGotHeaderData; // identify whether got data of head view from remote network
    private boolean isEnableScrollListener;
    private boolean isFromLeft = true; //identify where is focus come from(left list or right list)
    private boolean mRefreshWhenFilter;
    private boolean isFilter; //identify whether add any filter items when loaded network data
    private boolean isFirstEnter = true;
    private boolean isLongPressed;
    private boolean isReloadData; //identify whether reloaded data
    private boolean isEnabledMoveToRight;
    private int mLastFocusedViewPos;
    private int mColumnNum;
    private int mCurrLineNum;
    private int mLeftFocusedViewBeforeFilter;
    private FrameLayout mFilterLayout;
    private RecyclerView mFilterListView;
    public boolean backFromRightItem = false;
    private LoginUserInformationHelper mHelper;
    public boolean isFromUp;
    public boolean isFromDown;
    public int currentLeftItemIndex = -100;//当前左侧栏item标识，用于顶部栏上焦点后，左侧文字变红

    private boolean firstFilterListDone = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SouthMediaUtil.southNewMediaCheck(this);
        setContentView(R.layout.activity_list_grid_new);

        initParams();
        initView();
        initData();
        RequestManager.getInstance().onGridListNewExposureEvent(mOttCategoryId);
        setPageName("6_grid_list_video");
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);

        initParams();
        isReloadData = true;
        initView();
        initData();

        mRefreshWhenFilter = false;
        mFilterWindow = null;
        if (mPreFilterValue == null) {
            mPreFilterValue = new ArrayList<>();
        } else {
            mPreFilterValue.clear();
        }
        RequestManager.getInstance().onGridListNewExposureEvent(mOttCategoryId);
    }

    @Override
    protected void onResume() {
        super.onResume();
        getTopData();
        refreshTopBar();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //release objects
        if (mPreFilterValue != null) {
            mPreFilterValue.clear();
            mPreFilterValue = null;
        }
        if (mGridListAdapter != null) {
            mGridListAdapter.releaseAll();
            mGridListAdapter = null;
        }
        if (mGridListHeaderAdapter != null) {
            mGridListHeaderAdapter.releaseAll();
            mGridListHeaderAdapter = null;
        }
        if (mGridListLeftAdapter != null) {
            mGridListLeftAdapter.releaseAll();
            mGridListLeftAdapter = null;
        }
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
        headerView = null;
        mFilterLayout = null;
        mFilterListView = null;
        mHelper = null;
        FocusUtil.clearAnimation();
    }

//    @Override
//    public boolean onKeyDown(int keyCode, KeyEvent event) {
//        if ((keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_UP
//                || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) && event.getRepeatCount() > 1
//                && mGridList.indexOfChild(getCurrentFocus()) >= 0 && !isLongPressed) {
//            isLongPressed = true;
//            mGridList.isLongPressed(true);
//            setViewFocusability(false);
//        }
//        return super.onKeyDown(keyCode, event);
//    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_MENU) {
//            if (mFilterWindow != null && mFilterWindow.isShowing()) {
//                return super.onKeyDown(keyCode, event);
//            }
//            displayFilterWindow(mPresenterImpl.getFilterList());
//            hideFocusViewWhenFilter();
            return true;
        } else if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN ) {
            if (backFromRightItem) {
                backFromRightItem = false;
                return true;
            }
            if (mFilterWindow != null && mFilterWindow.isShowing()) {
                dismissPopUpWindow();
            } else {
                GridListActivityNew.this.finish();
            }
            return true;
        }
//        else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_UP) {
//            if (isLongPressed) {
//                mGridList.isLongPressed(false);
//                mGridList.stopScroll();
//            }
//        } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
//            if (isLongPressed) {
//                mGridList.isLongPressed(false);
//            }
//        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    protected boolean isEventBusAvailable() {
        return true;
    }

    @Subscribe
    public void onEventMainThread(LoginSuccessEvent event) {
        //Receive EventBus about login and reset user image to custom picture
        LibDeprecatedLogger.d("onEventMainThread(LoginSuccessEvent event)");

        if (event == null) {
            return;
        }
        refreshTopBar();
    }

    @Subscribe
    public void onEventMainThread(LogoutEvent event) {
        //Receive EventBus about logout and reset user image to default
        LibDeprecatedLogger.d("onEventMainThread(LogoutEvent event)");
        if (event == null) {
            return;
        }
        refreshTopBar();
    }

    private void refreshTopBar() {
        headerView.updateLoginDisplay();
    }


    private void getTopData() {
        NetworkApi.getTopData(new DisposableObserver<TopInfo>() {
            @Override
            public void onNext(TopInfo value) {
                LibDeprecatedLogger.d("onNext: " + value.toString());
                if (headerView == null) {
                    return;
                }
                headerView.setData(value);
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        }, mHelper.getLoginPassport(), mHelper.getLoginToken());
    }

    @Override
    public void updateLeftListView(List<MenuListBean.MenuDate> menuDataList) {
        if (mGridListLeftAdapter == null || mHandler == null) {
            return;
        }

        //request date of video list
        if (menuDataList != null && menuDataList.size() > 0) {
            tv_grid_type.setText(menuDataList.get(0).categoryName);
            mHandler.removeMessages(MSG_REFRESH);
            mHandler.sendEmptyMessage(MSG_REFRESH);
        }

        //set data source of left list
        if (mLeftList != null && mGridListLeftAdapter != null) {
            mGridListLeftAdapter.setDataList(menuDataList);
            mGridListLeftAdapter.notifyDataSetChanged();
        }

        //set focus to left
        if (mSubCategoryId == 0) {
            layout_filter.requestFocus();
//            setFilterItemSelected(true);
        } else {
            int selectedPos = -1;
            for (MenuListBean.MenuDate tmpData : menuDataList) {
                if (tmpData.id == mSubCategoryId) {
                    if (menuDataList.size() > 1) {
                        selectedPos = menuDataList.indexOf(tmpData) == 0 ? -1 : (menuDataList.indexOf(tmpData) - 1);
                    } else {
                        selectedPos = menuDataList.indexOf(tmpData);
                    }
                    break;
                }
            }

            if (selectedPos == -1) {
                layout_filter.requestFocus();
                setFilterItemSelected(true);
                return;
            }
            mLeftList.scrollToPosition(selectedPos);
            mGridListLeftAdapter.setSelectedPos(selectedPos);
        }
    }

    @Override
    public void displayLeftErrorView() {
        mErrorView.setVisibility(View.VISIBLE);
        mLoadingView.setVisibility(View.GONE);
        mGridView.setVisibility(View.GONE);
    }

    @Override
    public void updateGridListView(VideoGridListBean.DataEntity dataEntity, boolean fromFilter, int subCateCode) {
        if (mGridListAdapter == null) {
            return;
        }

        //hide useless views
        if (mLoadingView != null && mLoadingView.getVisibility() == View.VISIBLE) {
            mLoadingView.setVisibility(View.GONE);
            mErrorView.setVisibility(View.GONE);
            mGridView.setVisibility(View.VISIBLE);
        }
        mChildLoadingView.setVisibility(View.GONE);

        if (isShowHeader && isGotHeaderData && mGridListLeftAdapter.getSelectedPos() == -1) { //display header list view
            mHeaderList.setVisibility(View.VISIBLE);
            if (mGridListHeaderAdapter != null) {
                mGridListHeaderAdapter.setFocusView(mFocusView);
                mHeaderList.setAdapter(mGridListHeaderAdapter);
            }
            if (mHeaderLayoutManager == null) {
                mHeaderLayoutManager = new CustomLinearLayoutManager(this);
            }
            mHeaderLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
            mHeaderList.setLayoutManager(mHeaderLayoutManager);
        } else { //hide header list view
            mHeaderList.setVisibility(View.GONE);
        }

        //display video error view
        if (dataEntity == null || dataEntity.result == null || dataEntity.result.size() <= 0) {
            mChildErrorView.setVisibility(View.VISIBLE);
            tv_error_hint.setText(getResources().getString(R.string.data_empty));
            return;
        }

        mChildErrorView.setVisibility(View.GONE);
        mGridList.setVisibility(View.VISIBLE);

        if (dataEntity.count > 0) {
            if (fromFilter) {
                currentLeftItemIndex = -1;
                if (layout_filter.hasFocus() || headerView.isHasFocus()) {
                    mGridListAdapter.setDataSource(dataEntity.result);
                    mGridList.setAdapter(mGridListAdapter);
                    if (mGridListAdapter.getHeadersSize() == 0) {
                        addFilterView();
                        moveToTop();
                    }
                    isEnabledMoveToRight = true;
                } else {
                    mGridListAdapter.setDataSource(dataEntity.result);
                    mGridListAdapter.notifyDataSetChanged();
                    refocusHeaderItem();
                }
                if (headerView.isHasFocus()) {
                    selectFilterLayout();
                }
            } else {
                currentLeftItemIndex = findLeftItemPosition(subCateCode);
                mGridListAdapter.setDataSource(dataEntity.result);
                mGridList.setAdapter(mGridListAdapter);
                if (!layout_filter.hasFocus()) {
                    isEnabledMoveToRight = true;
                }
                if (headerView.hasFocus()) {
                    selectLeftItem();
                }
            }
            tv_grid_sum_line.setVisibility(View.VISIBLE);
            if (mVideoType == 0) {
                tv_grid_sum_line.setText(dataEntity.count % SPAN_VRS == 0 ? dataEntity.count / SPAN_VRS + "行"
                        : dataEntity.count / SPAN_VRS + 1 + "行");
            } else {
                tv_grid_sum_line.setText(dataEntity.count % SPAN_PGC == 0 ? dataEntity.count / SPAN_PGC + "行"
                        : dataEntity.count / SPAN_PGC + 1 + "行");
            }

            isEnableScrollListener = true;
            mGridListLeftAdapter.setEnabledRightKey(true);
        }
//        headerView.setEnableFocus(true);
    }

    private int findLeftItemPosition(int subCateCode) {
        List<MenuListBean.MenuDate> dateList = (List<MenuListBean.MenuDate>) mGridListLeftAdapter.getDataList();
        int selectedPos = -100;
        if (dateList != null) {
            for (MenuListBean.MenuDate tmpData : dateList) {
                if (tmpData.id == subCateCode) {
                    if (dateList.size() > 1) {
                        selectedPos = dateList.indexOf(tmpData);
                    }
                    break;
                }
            }
        }

        return selectedPos;
    }

    private void refocusHeaderItem() {
        if (mFilterListView == null) {
            return;
        }
        NewFilterAdapter newFilterAdapter = (NewFilterAdapter) mFilterListView.getAdapter();
        NewFilterAdapter.ViewHolder filterVH = (NewFilterAdapter.ViewHolder) mFilterListView.findViewHolderForAdapterPosition(newFilterAdapter.getFocusPos());
        if (filterVH == null) {
            return;
        }
        final FilterItemAdapter.ViewHolder filterItemVH = (FilterItemAdapter.ViewHolder) filterVH.recyclerView.findViewHolderForAdapterPosition(filterVH.adapter.getFocusPos());
        if (filterItemVH == null) {
            return;
        }
        filterItemVH.itemView.requestFocus();
        headerView.setEnableFocus(true);
    }

    private void moveToTop() {
        if (mGridList == null || mGridList.getVisibility() != View.VISIBLE || mGridListLayoutManager == null) {
            return;
        }
        mGridList.scrollToPosition(0);
        mGridListLayoutManager.scrollToPositionWithOffset(0, 0);
    }

    /**
     * 每次刷新数据后滚动到顶部
     */
    private void scrollToTopIfNeed() {
        if (mGridListLayoutManager == null || mGridList == null ||
                mGridList.getVisibility() != View.VISIBLE) {
            return;
        }
        int firstVisibleItemPosition = mGridListLayoutManager.findFirstVisibleItemPosition();
        if (firstVisibleItemPosition == 0) {
            focusRecordViewAtPos();
            return;
        }
        moveToPosition(0);
        mGridList.postDelayed(new InnerRunnable(this), 500);
    }

    private static class InnerRunnable implements Runnable {
        private WeakReference<GridListActivityNew> mWrapper;

        InnerRunnable(GridListActivityNew gridListActivityNew) {
            mWrapper = new WeakReference<>(gridListActivityNew);
        }

        @Override
        public void run() {
            GridListActivityNew gridListActivityNew = mWrapper.get();
            if (gridListActivityNew != null) {
                gridListActivityNew.focusRecordViewAtPos();
            }
        }
    }

    private void moveToPosition(int position) {
        int firstItem = mGridList.getChildLayoutPosition(mGridList.getChildAt(0));
        int lastItem = mGridList.getChildLayoutPosition(mGridList.getChildAt(mGridList.getChildCount() - 1));
        if (position < firstItem || position > lastItem) {
            mGridList.smoothScrollToPosition(position);
        } else {
            int movePosition = position - firstItem;
            int top = mGridList.getChildAt(movePosition).getTop();
            mGridList.smoothScrollBy(0, top);
        }
    }

    /**
     * Request focus of RecordRecyclerView
     */
    private void focusRecordViewAtPos() {
        if (mGridListLayoutManager == null || mGridList == null ||
                mGridList.getVisibility() != View.VISIBLE) {
            return;
        }
        int focusedPosition = mGridListLayoutManager.findFirstVisibleItemPosition();
        LibDeprecatedLogger.d("First visible item position = " + focusedPosition);
        if (mGridList.findViewHolderForAdapterPosition(focusedPosition) != null
                && mGridList.findViewHolderForAdapterPosition(focusedPosition).itemView != null) {
            mGridList.findViewHolderForAdapterPosition(focusedPosition).itemView.requestFocus();
        }
    }

    @Override
    public void displayGridListErrorView(String errorStr) {

        mLoadingView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.GONE);
        mGridView.setVisibility(View.VISIBLE);
        //清空数据
        if (mGridListAdapter != null) {
            mGridListAdapter.setDataSource(new ArrayList<ListAlbumModel>());
            mGridListAdapter.notifyDataSetChanged();
        }
        mChildLoadingView.setVisibility(View.GONE);
        mChildErrorView.setVisibility(View.VISIBLE);
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) mChildErrorView.getLayoutParams();
        params.bottomMargin = getResources().getDimensionPixelSize(R.dimen.y297);
        mChildErrorView.setLayoutParams(params);
        tv_error_hint.setText(errorStr);
//        mGridList.setVisibility(View.GONE);
//        mHeaderList.setVisibility(View.GONE);

    }

    @Override
    public void displayGridListFilterErrorView(String errorStr) {
        currentLeftItemIndex = -1;
        mLoadingView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.GONE);
        mGridView.setVisibility(View.VISIBLE);
        mGridList.setVisibility(View.VISIBLE);
        //清空数据
        mGridListAdapter.setDataSource(new ArrayList<ListAlbumModel>());
        mGridListAdapter.notifyDataSetChanged();
        if (mGridListAdapter.getHeadersSize() == 0 && layout_filter.hasFocus()) {
            addFilterView();
        }
        refocusHeaderItem();
        mChildLoadingView.setVisibility(View.GONE);
        mChildErrorView.setVisibility(View.VISIBLE);
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) mChildErrorView.getLayoutParams();
        params.bottomMargin = getResources().getDimensionPixelSize(R.dimen.y51);
        mChildErrorView.setLayoutParams(params);
        tv_error_hint.setText(errorStr);
        isEnabledMoveToRight = true;
    }

    @Override
    public void displayGridListLoadingView() {
        //loading时清空数据
        removeFilterView();
        mGridListAdapter.setDataSource(new ArrayList<ListAlbumModel>());
        mGridListAdapter.notifyDataSetChanged();
        tv_grid_sum_line.setText("");
        tv_grid_sum_line.setVisibility(View.INVISIBLE);
        tv_grid_divider.setVisibility(View.INVISIBLE);
        tv_grid_curr_line.setText("");
        tv_grid_curr_line.setVisibility(View.INVISIBLE);

        mChildLoadingView.setVisibility(View.VISIBLE);
        mChildErrorView.setVisibility(View.GONE);
        mGridList.setVisibility(View.GONE);
        mHeaderList.setVisibility(View.GONE);
    }

    @Override
    public void displayGridListFilterLoadingView() {
        if (layout_filter.hasFocus()) {//从左侧筛选按钮上下方到左侧按钮
            displayGridListLoadingView();
            return;
        }
        mChildLoadingView.setVisibility(View.VISIBLE);
        mChildErrorView.setVisibility(View.GONE);
        mGridList.setVisibility(View.VISIBLE);
        mHeaderList.setVisibility(View.GONE);
    }

    @Override
    public void addGridListItems(VideoGridListBean.DataEntity dataEntity) {
        if (mGridListAdapter == null || dataEntity == null || dataEntity.count <= 0) {
            return;
        }

        isEnableScrollListener = true;
        if (dataEntity.result != null && dataEntity.result.size() > 0) {
            if (mGridList != null && mGridListAdapter != null) {
                mGridListAdapter.addItems(dataEntity.result);
            }
        }
    }

    @Override
    public void addGridListItemsError() {
        isEnableScrollListener = true;
        ToastUtils.showToast(this, "获取新数据失败，请稍后重试！");
    }

    @Override
    public void onChangedTabListener(int subCateId) {
        if (subCateId >= 0) {
            isEnabledMoveToRight = false;
            mSubCategoryId = subCateId;
            mHandler.removeMessages(MSG_REFRESH);
            mHandler.sendEmptyMessageDelayed(MSG_REFRESH, 500);
        }
    }

    @Override
    public boolean onFocusDown() {
        isFromUp = true;
        View focusedView = getCurrentFocus();
        if (headerView.getChildViewIndex(focusedView) != -1) {
            if (isFromLeft) { //focus comes from left list
                setLeftSelectedPos();
            } else { //focus comes from right content
                if (!focusRightView()) {
                    int selectedPos = mGridListLeftAdapter.getSelectedPos();
                    if (selectedPos < 0 || selectedPos >= mGridListAdapter.getItemCount()) {
                        layout_filter.requestFocus();
                    } else {
                        if (mLeftList.findViewHolderForAdapterPosition(selectedPos) != null
                                && mLeftList.findViewHolderForAdapterPosition(selectedPos).itemView != null) {
                            mLeftList.findViewHolderForAdapterPosition(selectedPos).itemView.requestFocus();
                            setViewFocusability(false);
                            mColumnNum = 0;
                        }
                    }
                }
            }
            isFromLeft = true;
            return true;
        }
        return false;
    }

    @Override
    public void onGetFocus(View focusView) {
//        layout_filter.setSelected(false);
        if (currentLeftItemIndex == -1) {//筛选
            selectFilterLayout();
        } else {
            resetFilterLayout();
        }
        if (currentLeftItemIndex >= 0 && currentLeftItemIndex < mGridListLeftAdapter.getItemCount()) {//左侧列表
            selectLeftItem();
        }
    }

    private void selectLeftItem() {
        if (mLeftList == null) {
            return;
        }
        RecyclerView.ViewHolder viewHolderForAdapterPosition = mLeftList.findViewHolderForAdapterPosition(currentLeftItemIndex);
        if (viewHolderForAdapterPosition == null) {
            return;
        }
        ((GridListLeftAdapterNew.GridListViewViewHolder) viewHolderForAdapterPosition).textView.setTextColor(Color.parseColor("#FF6247"));
        ((GridListLeftAdapterNew.GridListViewViewHolder) viewHolderForAdapterPosition).textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x40));
    }

    private void resetLeftItem() {
        if (mGridListLeftAdapter == null || mLeftList.isComputingLayout()) {
            return;
        }
        if (mLeftList == null) {
            return;
        }
        if (mLeftList.getScrollState() != RecyclerView.SCROLL_STATE_IDLE || mLeftList.isComputingLayout()) {
            return;
        }
        mLeftList.post(new NotifyLeftListRunnable(this));
    }

    private static class NotifyLeftListRunnable implements Runnable {
        private WeakReference<GridListActivityNew> mWrapper;

        NotifyLeftListRunnable(GridListActivityNew activityNew) {
            mWrapper = new WeakReference<>(activityNew);
        }

        @Override
        public void run() {
            GridListActivityNew gridListActivityNew = mWrapper.get();
            if (gridListActivityNew == null) {
                return;
            }
            gridListActivityNew.mGridListLeftAdapter.notifyDataSetChanged();
        }
    }

    private void selectFilterLayout() {
        tv_filter.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x40));
        tv_filter.setTextColor(Color.parseColor("#FF6247"));
        iv_filter.setImageResource(R.drawable.ic_grid_list_filter_focused);
        resetLeftItem();
    }

    private void resetFilterLayout() {
        tv_filter.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x36));
        tv_filter.setTextColor(Color.parseColor("#B5E8E8FF"));
        iv_filter.setImageResource(R.drawable.ic_grid_list_filter_normal);
    }

    @Override
    public void onSelectChange() {
        headerView.setEnableFocus(false);
        if (mFilterListView != null) {
            RecyclerView recyclerView = mFilterListView;
            NewFilterAdapter adapter = (NewFilterAdapter) recyclerView.getAdapter();
            List<Integer> filterValue = new ArrayList<>();
            for (int i = 0; i < adapter.getItemCount(); i++) {
                filterValue.add(((FilterItemAdapter) (((RecyclerView) recyclerView.getChildAt(i).findViewById(R.id.filter_content)).getAdapter())).getSelectPos());
            }
            mPreFilterValue.clear();
            mPreFilterValue.addAll(filterValue);
            updateFilterValueResult();

            //update ui
            if (mLeftFocusedViewBeforeFilter >= 0) {
                if (mLeftList != null && mLeftList.findViewHolderForAdapterPosition(mLeftFocusedViewBeforeFilter) != null
                        && mLeftList.findViewHolderForAdapterPosition(mLeftFocusedViewBeforeFilter).itemView != null) {
                    mLeftList.findViewHolderForAdapterPosition(mLeftFocusedViewBeforeFilter).itemView.setSelected(false);
                }
            }
            layout_filter.setSelected(true);
        }
    }

    @Override
    public void setGridListHeaderData(ArrayList<AllLabel.LabelItem> labelItemArrayList) {
        if (labelItemArrayList != null && labelItemArrayList.size() > 0) {
            isGotHeaderData = true;
            if (mGridListHeaderAdapter == null) {
                mGridListHeaderAdapter = new GridListTagAdapterNew(this, mHeaderList);
            }
            mGridListHeaderAdapter.setLabelList(labelItemArrayList);
        } else {
            isGotHeaderData = false;
        }
    }

    @Override
    public void catchGridListHeaderDataError() {
        isGotHeaderData = false;
    }

    @Override
    public void onGlobalFocusChanged(View oldFocus, View newFocus) {
        int spanCount = mVideoType == 0 ? SPAN_VRS : SPAN_PGC;
        if (newFocus == null) {
            mGridList.smoothScrollToPosition(mLastFocusedViewPos + spanCount);
            RecyclerView.ViewHolder viewHolder = mGridList.findViewHolderForAdapterPosition(mLastFocusedViewPos + spanCount);
            if (viewHolder != null && viewHolder.itemView != null) {
                viewHolder.itemView.requestFocus();
            }
        } else {
            if (newFocus.getParent() instanceof RecyclerView) {
                mLastFocusedViewPos = mGridList.getChildAdapterPosition(newFocus);
            }
        }
    }

    public View getFirstLineOfFilterView() {
        return mFilterListView.getChildAt(0);
    }

    public void updateFilterFocusPos(View view) {
        if (mFilterListView == null) {
            return;
        }
        int position = mFilterListView.getChildAdapterPosition(view);
        if (position != -1) {
            ((NewFilterAdapter) mFilterListView.getAdapter()).setFocusPos(position);
        }
    }

    public void backToFirstItemOfFilterView(){
        if (mFilterListView == null) {
            return;
        }
        mFilterListView.post(new Runnable() {
            @Override
            public void run() {
                RecyclerView.ViewHolder holder = mFilterListView.findViewHolderForAdapterPosition(0);
                if (holder != null){
                    holder.itemView.requestFocus();
                }
            }
        });
    }

    public void addFilterView() {
        setViewFocusability(true);
        if (mFilterLayout == null) {
            mFilterLayout = (FrameLayout) LayoutInflater.from(this).inflate(R.layout.dialog_filter, null);
        }
        if (mFilterListView == null) {
            mFilterListView = (RecyclerView) mFilterLayout.findViewById(R.id.listview);
            mFilterListView.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
            mFilterListView.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                    int position = parent.getChildLayoutPosition(view);
                    int itemCount = parent.getAdapter().getItemCount();
                    if (itemCount < 1) {
                        return;
                    }
                    if (position == itemCount - 1) {//最后一行
                        outRect.bottom = 0;
                    } else {
                        outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y24);
                    }
                }
            });
        }
        mFilterListView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        NewFilterAdapter filterAdapter = new NewFilterAdapter(this);
        List<Integer> filterValue = mPresenterImpl.getFilterValue();
        List<FilterBean.DataEntity> dataEntity = mPresenterImpl.getFilterList();
        filterAdapter.add(dataEntity);
        filterAdapter.addValueList(filterValue);
        filterAdapter.setOnSelectChangeListener(this);
        mFilterListView.setAdapter(filterAdapter);

        mGridListAdapter.updateHeader(mFilterLayout);
//        layout_filter.setSelected(true);
    }

    public void removeFilterView() {
        if (mFilterListView != null) {
            mFilterListView.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
        }
        mGridListAdapter.removeHeader(mFilterLayout);
        layout_filter.setSelected(false);
    }

    /**
     * Display filter window
     *
     * @param dataEntity filter value list
     */
    public void displayFilterWindow(List<FilterBean.DataEntity> dataEntity) {
        if (dataEntity == null || dataEntity.size() <= 0) {
            return;
        }
        setViewFocusability(true);
        //reset value of isFilter
        isFilter = false;

        //init popupWindow
        if (mFilterWindow == null) {
            View contentView = LayoutInflater.from(this).inflate(R.layout.dialog_filter, null);
            RecyclerView filterList;
            filterList = (RecyclerView) contentView.findViewById(R.id.listview);
            filterList.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            filterList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
            filterList.addItemDecoration(new RecyclerView.ItemDecoration() {
                @Override
                public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                    outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y24);
                }
            });
            FocusBorderView focusView = (FocusBorderView) contentView.findViewById(R.id.focus_border_view);

            NewFilterAdapter filterAdapter = new NewFilterAdapter(this);
            filterAdapter.setFocusView(focusView);
            List<Integer> filterValue = mPresenterImpl.getFilterValue();
            filterAdapter.add(dataEntity);
            filterAdapter.addValueList(filterValue);

            filterAdapter.setOnSelectChangeListener(this);
            filterList.setAdapter(filterAdapter);
            int filterHeight;
            if (dataEntity.size() < 5) {
                filterHeight = 41 + getResources().getDimensionPixelSize(R.dimen.y54) * (dataEntity.size() + 1) +
                        getResources().getDimensionPixelSize(R.dimen.y24) * dataEntity.size() +
                        getResources().getDimensionPixelSize(R.dimen.y50);
            } else {
                filterHeight = 41 + getResources().getDimensionPixelSize(R.dimen.y54) * 6 +
                        getResources().getDimensionPixelSize(R.dimen.y24) * 5 +
                        getResources().getDimensionPixelSize(R.dimen.y50);
            }

            mFilterWindow = new PopupWindow(contentView, ViewGroup.LayoutParams.MATCH_PARENT,
                    filterHeight, true) {
            };
            // 如果不设置PopupWindow的背景，无论是点击外部区域还是Back键都无法dismiss弹框
            mFilterWindow.setBackgroundDrawable(getResources().getDrawable(R.drawable.episode_dialog_bg));
            mFilterWindow.setTouchable(true);
            mFilterWindow.setFocusable(true);
            mFilterWindow.setOutsideTouchable(true);
            mFilterWindow.setAnimationStyle(R.style.DialogAnimation);
            mFilterWindow.setContentView(contentView);
            mFilterWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    reworkFocusViewAfterFilter();
                }
            });
        }
        mFilterWindow.showAtLocation(findViewById(R.id.layout_grid_view),
                Gravity.LEFT | Gravity.BOTTOM, 0, 0);
        RequestManager.getInstance().onGridListNewFilterViewExposureEvent(mOttCategoryId);
    }

    /**
     * Dismiss PopUp window
     */
    public void dismissPopUpWindow() {
        if (mFilterWindow != null && mFilterWindow.isShowing()) {
            mFilterWindow.dismiss();
        }
    }

    /**
     * Set focus route
     *
     * @param isFromLeft true if focus view comes from left list
     */
    public void setFocusRoute(boolean isFromLeft) {
        this.isFromLeft = isFromLeft;
    }

    /**
     * Set focus on the right view
     * <p>
     * if header list is visible, focus on the first item of header list
     * otherwise, focus on the first item of video list
     */
    public boolean focusRightView() {
        setViewFocusability(true);

        if (!isEnabledMoveToRight) {
            return false;
        }

        if (mHeaderList != null && mHeaderList.getVisibility() == View.VISIBLE) {
            mHeaderList.getChildAt(0).requestFocus();

            setViewFocusability(false);
            return true;
        } else {
            if (mGridList == null || mGridList.getVisibility() != View.VISIBLE) {
                return false;
            }

            if (mGridList != null && mGridListLayoutManager != null) {
                int index = mGridListLayoutManager.findFirstCompletelyVisibleItemPosition();
                if (mGridList.findViewHolderForAdapterPosition(index) != null
                        && mGridList.findViewHolderForAdapterPosition(index).itemView != null) {
                    mGridList.findViewHolderForAdapterPosition(index).itemView.requestFocus();
                    setViewFocusability(false);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Display header list and focus its first item
     *
     * @param isFocus whether focus the first item or not
     * @return true if header list is visible
     */
    public boolean displayHeaderView(boolean isFocus) {
        if (isShowHeader && isGotHeaderData && mHeaderList != null && mGridListLeftAdapter.getSelectedPos() == -1) {
            mHeaderList.setVisibility(View.VISIBLE);
            if (isFocus && mHeaderList.getChildAt(0) != null) {
                mHeaderList.getChildAt(0).requestFocus();
            }
            setCurrLineTxt(false, -1);
            return true;
        }
        return false;
    }

    /**
     * Set the visibility and text of TextView named tv_grid_curr_line
     *
     * @param isVisible  true if tv_grid_curr_line is visible
     * @param focusedPos the position of focused video item
     */
    public void setCurrLineTxt(boolean isVisible, int focusedPos) {
        if (!isVisible) {
            tv_grid_curr_line.setText("");
            tv_grid_divider.setVisibility(View.GONE);
            tv_grid_curr_line.setVisibility(View.GONE);
            mCurrLineNum = 0;
        } else {
            if (mGridList != null && mGridListLayoutManager != null) {
                if (focusedPos >= 0 && mGridList.findViewHolderForAdapterPosition(focusedPos) != null
                        && mGridList.findViewHolderForAdapterPosition(focusedPos).itemView != null) {
                    int spanCount = mGridListLayoutManager.getSpanCount();
                    tv_grid_curr_line.setText(String.valueOf(focusedPos / spanCount + 1));
                    tv_grid_divider.setVisibility(View.VISIBLE);
                    tv_grid_curr_line.setVisibility(View.VISIBLE);
                    mCurrLineNum = (focusedPos - mGridListAdapter.getHeadersSize()) / spanCount + mGridListAdapter.getHeadersSize();
                    mLastFocusedViewPos = mCurrLineNum * (mVideoType == 0 ? SPAN_VRS : SPAN_PGC) + mColumnNum;
                }
            }
        }
    }

    /**
     * Set focus position of left list and focus on it
     */
    public void setLeftSelectedPos() {
        if (mLeftList == null || mGridListLeftAdapter == null) {
            return;
        }

        setViewFocusability(true);
        mColumnNum = 0;

        if (mGridListLeftAdapter.getSelectedPos() == -1) {
            layout_filter.requestFocus();
        } else if (mLeftList.findViewHolderForAdapterPosition(mGridListLeftAdapter.getSelectedPos()) != null
                && mLeftList.findViewHolderForAdapterPosition(mGridListLeftAdapter.getSelectedPos()).itemView != null) {
            mLeftList.findViewHolderForAdapterPosition(mGridListLeftAdapter.getSelectedPos()).itemView.requestFocus();
        }
    }

    /**
     * take focus on the top bar, the default focused item is search button
     */
    public void focusOnTopBar() {
        if (headerView != null) {
            setViewFocusability(true);
            headerView.focusChildView(INDEX_SEARCH_BUTTON);
        }
    }

    /**
     * Check column number whether is same to the previous one before long pressed key
     *
     * @param position the adapter position of currently focused item
     */
    public boolean checkColumnNum(int position) {
        if (mGridListAdapter.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN && position < mGridListAdapter.getItemCount() - 1) {
            int spanCount = mVideoType == 0 ? SPAN_VRS : SPAN_PGC;
            int columnNum = (position - mGridListAdapter.getHeadersSize()) % spanCount;
            if (columnNum != mColumnNum) { //if column number is different from the previous one, move focus to the previous.
                int targetPos = position / spanCount * spanCount + mColumnNum;
                RecyclerView.ViewHolder viewHolder = mGridList.findViewHolderForAdapterPosition(targetPos);
                if (viewHolder != null && viewHolder.itemView != null) {
                    viewHolder.itemView.requestFocus();
                }
                return false;
            }
        }
        return true;
    }

    /**
     * Set column number to check when key up
     */
    public void setColumnNum(int columnNum) {
        this.mColumnNum = columnNum;
    }

    /**
     * Just available for Touch version(ShenHua Box)
     * Set selected status for filter item
     *
     * @param isSelected true if item is selected, false if item is unselected
     */
    public void setFilterItemSelected(boolean isSelected) {
        if (Util.isSupportTouchVersion(this)) {
            layout_filter.setSelected(isSelected);
        }
    }

    private void initView() {
        headerView = (UserRelatedHeaderView) findViewById(R.id.header_view);
        layout_filter = (RelativeLayout) findViewById(R.id.layout_filter);
        mGridView = (RelativeLayout) findViewById(R.id.layout_grid_view);
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mErrorView = (LinearLayout) findViewById(R.id.err_view);
        mChildLoadingView = (LoadingView) findViewById(R.id.layout_child_loading_view);
        mChildErrorView = (LinearLayout) findViewById(R.id.layout_child_error_view);
        mLeftList = (CustomLinearRecyclerView) findViewById(R.id.rv_grid_left_list);
        if (Util.isSupportTouchVersion(this) && isShowHeader) {
            mRightView = (GridScrollViewForTouch) findViewById(R.id.layout_right_view_touch);
            mHeaderList = (CustomLinearRecyclerView) findViewById(R.id.rv_grid_header_touch);
            mGridList = (CustomRecyclerViewNew) findViewById(R.id.rv_grid_list_touch);
        } else {
            mRightView = (LinearLayout) findViewById(R.id.layout_right_view);
            mHeaderList = (CustomLinearRecyclerView) findViewById(R.id.rv_grid_header);
            mGridList = (CustomRecyclerViewNew) findViewById(R.id.rv_grid_list);
        }
        mRightView.setVisibility(View.VISIBLE);
        mFocusView = (FocusBorderView) findViewById(R.id.focus_border_view);
        tv_grid_curr_line = (TextView) findViewById(R.id.tv_grid_curr_line);
        tv_grid_sum_line = (TextView) findViewById(R.id.tv_grid_sum_line);
        tv_grid_divider = (TextView) findViewById(R.id.tv_grid_divider);
        tv_filter = (TextView) findViewById(R.id.tv_filter);
        iv_filter = (ImageView) findViewById(R.id.iv_filter);
        tv_grid_type = (TextView) findViewById(R.id.tv_grid_type);
        tv_error_hint = (TextView) mChildErrorView.findViewById(R.id.error_hint);
        tv_filter = (TextView) findViewById(R.id.tv_filter);

        mGridView.getViewTreeObserver().addOnGlobalFocusChangeListener(this);
        headerView.setHeaderViewFocusController(this);

        //init filter layout
        layout_filter.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    tv_filter.setTextColor(Color.parseColor("#E8E8FF"));
                    iv_filter.setImageResource(R.drawable.ic_grid_list_filter_normal);
                    if (isFromUp) {
                        isEnabledMoveToRight = false;
                        isFromUp = false;
                    }
                    if (isFromDown) {
                        isEnabledMoveToRight = false;
                        isFromDown = false;
                    }
                    //update rightCtn
                    if (mGridListLeftAdapter == null || !v.isSelected() || isReloadData) {
                        isReloadData = false;
                        mSubCategoryId = 0;
                        if (mHandler == null) {
                            mHandler = new MyHandler();
                        }
                        mHandler.removeMessages(MSG_REFRESH);
                        mHandler.sendEmptyMessageDelayed(MSG_REFRESH, 500);
                    }
                    v.setSelected(false);
                    tv_filter.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x40));
                    resetLeftItem();
                }
            }
        });
        layout_filter.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (event.getAction() != KeyEvent.ACTION_DOWN) {
                    return false;
                }
                if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                    isFromLeft = true;
//                    v.setSelected(true);
                    headerView.focusChildView(UserRelatedHeaderView.INDEX_HOME_LAYOUT);
                    if (!headerView.isHasFocus()) {
                        tv_filter.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x36));
                    }
                    isEnabledMoveToRight = true;
                    mHandler.removeMessages(MSG_REFRESH);
                    return true;
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    boolean focusRight = focusRightView();
                    v.setSelected(focusRight);
                    if (focusRight) {
                        selectFilterLayout();
                    }
                    return true;
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                    if (mLeftList == null || mLeftList.findViewHolderForAdapterPosition(0) == null ||
                            mLeftList.findViewHolderForAdapterPosition(0).itemView == null)
                        return false;
                    mLeftList.findViewHolderForAdapterPosition(0).itemView.requestFocus();
                    tv_filter.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelOffset(R.dimen.x36));
                    isEnabledMoveToRight = true;
                    resetFilterLayout();
                    return true;
                }
                return false;
            }
        });
        layout_filter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                displayFilterWindow(mPresenterImpl.getFilterList());
//                hideFocusViewWhenFilter();
            }
        });

        //init LayoutManager of left list
        mLeftListLayoutManager = new CustomLinearLayoutManager(this);
        mLeftListLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mLeftList.setLayoutManager(mLeftListLayoutManager);
        mLeftListLayoutManager.setCustomPadding(getResources().getDimensionPixelSize(R.dimen.y80),
                getResources().getDimensionPixelSize(R.dimen.y80));
        //init adapter of left list
        mGridListLeftAdapter = new GridListLeftAdapterNew(this, mLeftList);
        mLeftList.setAdapter(mGridListLeftAdapter);
        mGridListLeftAdapter.setGridListLeftListener(this);

        //init LayoutManager of video list
        if (mVideoType == 0) {
            mGridListLayoutManager = new CustomGridLayoutManager(this, SPAN_VRS);
        } else {
            mGridListLayoutManager = new CustomGridLayoutManager(this, SPAN_PGC);
        }
        //set padding of video list
        mGridList.setItemViewCacheSize(0);
        turnOffItemAnimators(mGridList);
        mGridList.setPadding(getResources().getDimensionPixelSize(R.dimen.x48), getResources().getDimensionPixelSize(R.dimen.y20),
                getResources().getDimensionPixelSize(R.dimen.x48), 0);
        mGridList.setLayoutManager(mGridListLayoutManager);
        mGridListLayoutManager.setCustomPadding(2 * getResources().getDimensionPixelSize(R.dimen.y120),
                2 * getResources().getDimensionPixelSize(R.dimen.y120));
        mGridListLayoutManager.setItemType(mVideoType);
        mGridList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                int position = parent.getChildLayoutPosition(view);
                if (mVideoType == 0) {//vrs
                    outRect.right = getResources().getDimensionPixelSize(R.dimen.x48);
                    if (mGridListAdapter.getHeadersSize() == 1 && position == 0) {//第一个item，即筛选item
                        outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y24);
                    } else {
                        outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y24);
                    }
                } else {
                    outRect.right = getResources().getDimensionPixelSize(R.dimen.x48);
                    if (mGridListAdapter.getHeadersSize() == 1 && position == 0) {//第一个item，即筛选item
                        outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y24);
                    } else {
                        outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y6);
                    }
                }
            }
        });
        mGridList.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_MOVE && mGridList != null) {
                    View focusedView = mGridView.getFocusedChild();
                    if (mFocusView != null && focusedView != null) {
                        mFocusView.setUnFocusView(focusedView);
                        FocusUtil.setUnFocusAnimator(focusedView, 100);
                    }
                }
                return false;
            }
        });
        //init adapter of video list
        mGridListAdapter = new GridListVideoAdapterNew(this, mGridList, mVideoType != 0);
        //设置true，并复写getItemId，解决焦点闪烁
        mGridListAdapter.setHasStableIds(true);
        mGridList.setAdapter(mGridListAdapter);
        mGridListAdapter.setFocusView(mFocusView);

        //set padding of header list
        mHeaderList.setPadding(0, getResources().getDimensionPixelSize(R.dimen.y20), 0, 0);
        mHeaderList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                if (parent.indexOfChild(view) < 3) {
                    outRect.right = (mHeaderList.getWidth() - getResources().getDimensionPixelSize(R.dimen.x20)
                            - getResources().getDimensionPixelSize(R.dimen.x322) * 4) / 3;
                }
            }
        });

        //set scroll listener for ScrollView only for touch version
        if (Util.isSupportTouchVersion(this) && isShowHeader) {
            ((GridScrollViewForTouch) mRightView).setListener(new GridScrollViewForTouch.OnGridScrollViewListener() {
                @Override
                public void onMoveDown() {
                    mGridListAdapter.loadImage();
                }

                @Override
                public void onMoveUp() {
                    mGridListAdapter.loadImage();
                }
            });
        }
    }

    private void turnOffItemAnimators(RecyclerView recyclerView) {
        recyclerView.setItemAnimator(null);
//        recyclerView.getItemAnimator().setAddDuration(0);
//        recyclerView.getItemAnimator().setRemoveDuration(0);
//        recyclerView.getItemAnimator().setChangeDuration(0);
//        recyclerView.getItemAnimator().setMoveDuration(0);
//        ((SimpleItemAnimator) recyclerView.getItemAnimator()).setSupportsChangeAnimations(false);
    }

    private void initData() {
        displayLoadingView();

        mHandler = new MyHandler();

        //init filter value list
        mPreFilterValue = new ArrayList<>();
        mRefreshWhenFilter = false;
        mGridList.setOnScrollListener(new FinishScrollListener());
        mPresenterImpl = new GridListVideoPresenterImplNew(this, this, mVideoType, mCateCodeFirst, mOttCategoryId);
        mPresenterImpl.requestLeftList();
        mPresenterImpl.requestFilterList();
        mPresenterImpl.requestHeaderList();
        mHelper = LoginUserInformationHelper.getHelper(this);
    }

    private void initParams() {
        Uri uri = getIntent().getData();
        if (uri == null) {
            mOttCategoryId = Util.convertStringToInt(getIntent().getStringExtra(ParamConstant.PARAM_CATE_ID));
            mCateCodeFirst = Util.convertStringToInt(getIntent().getStringExtra(ParamConstant.PARAM_CATECODE_FIRST));
            mVideoType = Util.convertStringToInt(getIntent().getStringExtra(ParamConstant.PARAM_VIDEO_TYPE));
            isShowHeader = Util.convertStringToBoolean(getIntent().getStringExtra(ParamConstant.PARAM_CATE_SHOW_HEADER));
            mSubCategoryId = Util.convertStringToInt(getIntent().getStringExtra(ParamConstant.PARAM_SUBCATE));
        } else {
            mOttCategoryId = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_CATE_ID));
            mCateCodeFirst = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_CATECODE_FIRST));
            mVideoType = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_VIDEO_TYPE));
            isShowHeader = Util.convertStringToBoolean(uri.getQueryParameter(ParamConstant.PARAM_CATE_SHOW_HEADER));
            mSubCategoryId = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_SUBCATE));
        }

    }


    public boolean isLoadingData() {
        if (mChildLoadingView == null) {
            return false;
        }
        return mChildLoadingView.getVisibility() == View.VISIBLE;
    }

    private void displayLoadingView() {
        mLoadingView.setVisibility(View.VISIBLE);
        mErrorView.setVisibility(View.GONE);
        mGridView.setVisibility(View.GONE);
    }

    private void updateFilterValueResult() {
        if ((mPreFilterValue.size() > 0 && !mPreFilterValue.equals(mPresenterImpl.getFilterValue()))
                | mRefreshWhenFilter) {
            mPresenterImpl.updateFilterValue(mPreFilterValue);
            updateGridListViewWithFilter();
            isFilter = true;
            isShowHeader = false;
        } else {
            isFilter = false;
        }
    }

    @Override
    public void onRequestFilterListDone() {
        //首次进入列表页
        List<Integer> filterValue = mPresenterImpl.getFilterValue();
        if (mPreFilterValue != null) {
            mPreFilterValue.clear();
            mPreFilterValue.addAll(filterValue);
        }
        if (!firstFilterListDone && mHandler != null ) {//首次进入的时候，不刷新，防止与updateLeftListView重复操作
            mHandler.removeMessages(MSG_REFRESH);
            mHandler.sendEmptyMessageDelayed(MSG_REFRESH, 500);
        }
        firstFilterListDone = false;
    }

    /**
     * Update filter conditions and request new data
     */
    private void updateGridListViewWithFilter() {
        List<FilterBean.DataEntity> filterData = mPresenterImpl.getFilterList();
        List<Integer> filterValue = mPresenterImpl.getFilterValue();
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder filterSb = new StringBuilder();
        if (filterData == null || filterData.size() <= 0 || filterValue.size() <= 0) {
            return;
        }
        for (int i = 0; i < filterData.size(); i++) {
            int index = filterValue.get(i);
            stringBuilder.append("&").append(filterData.get(i).alias).append("=")
                    .append(filterData.get(i).cateValues.get(index).searchKey);
            filterSb.append(filterData.get(i).alias).append("=")
                    .append(filterData.get(i).cateValues.get(index).searchKey).append(";");
        }

        mPresenterImpl.requestVideoGridListByFilter(stringBuilder.toString(), isLongPressed);
        RequestManager.getInstance().onGridListNewFilterViewItemClickEvent(filterSb.toString());
    }

    /**
     * Hide focused status when filter window was shown
     */
    private void hideFocusViewWhenFilter() {
        mLeftFocusedViewBeforeFilter = mGridListLeftAdapter.getSelectedPos();
        tv_grid_type.requestFocus();
        if (mGridListLeftAdapter != null) {
            int selectedPos = mGridListLeftAdapter.getSelectedPos();
            if (selectedPos == -1) {
                layout_filter.setSelected(true);
            } else {
                if (mLeftList != null && mLeftList.findViewHolderForAdapterPosition(selectedPos) != null
                        && mLeftList.findViewHolderForAdapterPosition(selectedPos).itemView != null) {
                    mLeftList.findViewHolderForAdapterPosition(selectedPos).itemView.setSelected(true);
                }
            }
        }
    }

    /**
     * recover focus status when filter window was hided.
     */
    public void reworkFocusViewAfterFilter() {
        if (isFilter) {
            if (!focusRightView()) {
                layout_filter.requestFocus();
            }
            mGridListLeftAdapter.setSelectedPos(-1);
        } else {
            if (mLeftFocusedViewBeforeFilter == -1) {
                layout_filter.requestFocus();
            } else {
                if (mLeftList != null && mLeftList.findViewHolderForAdapterPosition(mLeftFocusedViewBeforeFilter) != null
                        && mLeftList.findViewHolderForAdapterPosition(mLeftFocusedViewBeforeFilter).itemView != null) {
                    mLeftList.findViewHolderForAdapterPosition(mLeftFocusedViewBeforeFilter).itemView.requestFocus();
                }
            }
        }
    }

    /**
     * Set focusability for all widgets except video view
     *
     * @param isFocusable true == can be focused, false == cannot be focused
     */
    public void setViewFocusability(boolean isFocusable) {
        if (isFocusable) {
            headerView.setDescendantFocusability(ViewGroup.FOCUS_BEFORE_DESCENDANTS);
            mLeftList.setDescendantFocusability(ViewGroup.FOCUS_BEFORE_DESCENDANTS);
            layout_filter.setFocusable(true);
            tv_grid_type.setFocusable(true);
        } else {
            headerView.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
            mLeftList.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
            layout_filter.setFocusable(false);
            tv_grid_type.setFocusable(false);
        }
    }

    /**
     * Custom Handler to post delay to request video data to avoid frequently connect to network when changed tab on the left view
     */
    private class MyHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            int what = msg.what;
            switch (what) {
                case MSG_REFRESH:
                    mRefreshWhenFilter = true;
                    isEnableScrollListener = false;
                    if (mSubCategoryId == 0 && mPreFilterValue != null && mPreFilterValue.size() > 0) {
                        mPresenterImpl.updateFilterValue(mPreFilterValue);
                        updateGridListViewWithFilter();
                    } else {
                        mPresenterImpl.requestVideoGridList(mSubCategoryId, isLongPressed);
                    }
                    if (isFirstEnter) {
                        isFirstEnter = false;
                    } else {
                        RequestManager.getInstance().onGridListNewSubTabClickEvent(mSubCategoryId);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * Custom OnScrollListener to request network data by page and focus status
     */
    private class FinishScrollListener extends RecyclerView.OnScrollListener {

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            if (!isEnableScrollListener) {
                return;
            }

            int lastVisibleItemPosition = mGridListLayoutManager.findLastVisibleItemPosition() + 1;
            int modelsCount = mGridListAdapter.getItemCount();
            //request more network data when the last completely visible line was the  forth line from the bottom.
            if (lastVisibleItemPosition + 21 >= modelsCount) {
                isEnableScrollListener = false;
                if (isFilter && layout_filter.isSelected()) {
                    mPresenterImpl.requestMoreVideoDataByFilter(isLongPressed);
                } else {
                    mPresenterImpl.requestMoreVideoData(null, mSubCategoryId, isLongPressed);
                }
            }
            if (mGridList.getFocusedChild() == null) {
                return;
            }
        }

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);

            if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                return;
            }
            if (recyclerView == null) {
                return;
            }
            if (mGridList == null || mFocusView == null) {
                return;
            }

            View focusedView = mGridList.getFocusedChild();
            //if currently focused item is null, request focus for previously focused item.
            if (isLongPressed && focusedView == null) {
                int focusPos = mColumnNum + mCurrLineNum * (mVideoType == 0 ? SPAN_VRS : SPAN_PGC);
                RecyclerView.ViewHolder viewHolder = mGridList.findViewHolderForAdapterPosition(focusPos);
                if (viewHolder != null && viewHolder.itemView != null) {
                    viewHolder.itemView.requestFocus();
                }
                isLongPressed = false;
                mGridList.isLongPressed(false);
                return;
            }

            if (mGridListAdapter != null) {
                mGridListAdapter.loadImage();
            }

            int firstPos = mGridListLayoutManager.findFirstCompletelyVisibleItemPosition();
            int lastPos = mGridListLayoutManager.findLastCompletelyVisibleItemPosition();
            int currFocused = focusedView != null ? mGridList.getChildAdapterPosition(focusedView) : -1;
//            if (mGridListAdapter.getHeadersSize() == 1 && currFocused == 0) {//当前焦点在header上，
//                return;
//            }
            //if focused item is invisible completely, request focus on the certain item on the certain column of the first line.
            if (currFocused < firstPos || currFocused > lastPos) {
                RecyclerView.ViewHolder viewHolder = mGridList.findViewHolderForAdapterPosition(firstPos + mColumnNum);
                if (viewHolder != null && viewHolder.itemView != null) {
                    viewHolder.itemView.requestFocus();
                }
            } else {
                if (focusedView != null) {
                    final RecyclerView.ViewHolder viewHolder = mGridList.getChildViewHolder(focusedView);
                    if (checkColumnNum(mGridList.getChildAdapterPosition(mGridList.getFocusedChild()))
                            && viewHolder != null && viewHolder.itemView != null) {
//                        mFocusView.setFocusView(viewHolder.itemView);
                        viewHolder.itemView.requestFocus();
                        if (mVideoType > -1 && mVideoType == 2) {
//                            handlePgcFocus((GridListVideoAdapterNew.PgcViewHolder) viewHolder);
//                            mGridListAdapter.focusOnPgcViewByPos(mGridList.getChildAdapterPosition(viewHolder.itemView));
                        } else if (mVideoType > -1 && mVideoType == 1) {
                            if (viewHolder instanceof GridListVideoAdapterNew.VrsViewHolder) {
//                                handleVrsFocus((GridListVideoAdapterNew.VrsViewHolder) viewHolder);
                            }
                        }
                    }
                    setCurrLineTxt(true, viewHolder.getAdapterPosition());
                }
            }

            //reset isLongPressed
            if (isLongPressed) {
                isLongPressed = false;
                mGridList.isLongPressed(false);
                setViewFocusability(true);
            }
        }

        /**
         * 因为滚动后GridListVideoAdapterNew里设置的item onFocusChange回调没走，所以这里手动处理下
         *
         * @param viewHolder
         */
        private void handleVrsFocus(GridListVideoAdapterNew.VrsViewHolder viewHolder) {
            viewHolder.layout_grid_item_root.setBackgroundResource(R.drawable.bg_hfc_focus);
            viewHolder.layout_grid_item_focus_desc.setVisibility(View.VISIBLE);
            viewHolder.grid_model_focus_play.setVisibility(View.VISIBLE);
            viewHolder.grid_model_focus_play.showWaveAnimation();
            viewHolder.tv_sets.setVisibility(View.VISIBLE);
            viewHolder.grid_sets_bg.setVisibility(View.VISIBLE);
            FocusUtil.setFocusAnimator(viewHolder.itemView, 1.07f, 200);
        }

        private void handlePgcFocus(GridListVideoAdapterNew.PgcViewHolder viewHolder) {
            viewHolder.layout_grid_item_root.setBackgroundResource(R.drawable.bg_hfc_focus);
            viewHolder.layout_grid_item_focus_desc.setVisibility(View.VISIBLE);
            viewHolder.grid_model_focus_play.setVisibility(View.VISIBLE);
            viewHolder.grid_model_focus_play.showWaveAnimation();
            viewHolder.tv_title.setVisibility(View.GONE);
            FocusUtil.setFocusAnimator(viewHolder.itemView, 1.07f, 200);
        }
    }
}
