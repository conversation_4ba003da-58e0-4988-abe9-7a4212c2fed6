package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.text.Html
import android.util.TypedValue
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.view.isVisible
import com.sh.ott.video.base.component.ShPlayerConstants
import com.sh.ott.video.component.AdHeaderControlComponent
import com.sh.ott.video.contor.ShControlComponent
import com.sh.ott.video.contor.ShVideoViewController
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.annotation.ScreenMode
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.data.HomeData
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.videodetail.activity.playerIsPlaying
import com.sohuott.tv.vod.videodetail.activity.playerIsStart


/**
 *
 * 前贴广告
 * @param initScreenMode  初始化时候的默认窗口模式 因为广告文案根据窗口模式不同展示不一样文案，sdk默认初始化时为Normal
 */
class VideoAdHeaderComponent constructor(context: Context, @ScreenMode var initScreenMode: Int) :
    ShControlComponent(context), AdHeaderControlComponent {
    //ad_copy model 广告配置
    private val mAdvertisingCopyModelData = HomeData.getAdvertisingCopy()?.data
    private var tvTimeCount: TextView? = null
    private var time: String = ""
    private var oldTime: Int = 0
    private var fullText: String = mAdvertisingCopyModelData?.pre_paster ?: ""
    private var normalText: String = mAdvertisingCopyModelData?.small_screen ?: ""

    private var playState = PlayerConstants.VideoState.IDLE
    private var adType: Int = -1
    private val showText: String
        get() = if (initScreenMode == PlayerConstants.ScreenMode.FULL) fullText else normalText


    private var controller: ShVideoViewController? = null

    init {
        gone()
        val view = layoutInflater.inflate(R.layout.video_component_ad_header, this, true)
        tvTimeCount = findViewById(R.id.countdown)
        setScreenModelTextSize()
    }

    override fun attachController(controller: ShVideoViewController) {
        this.controller = controller
    }

    override fun onAdRequestType(type: Int) {
        adType = type

    }

    override fun onAdHeaderStarted() {
        show()
    }

    override fun onHeaderCompleted() {
        hide()
    }

    fun show() {
        controller?.showComponent(this)
    }

    fun hide() {
        gone()
    }


    override fun onScreenModeChanged(screenMode: Int) {
        initScreenMode = screenMode
        setScreenModelTextSize()
        updateTime()
    }

    override fun onAdPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        this.playState = playState
        AppLogger.d("VideoAdHeaderComponent", "onAdPlayStateChanged playState:$playState")
//        updateTime()
    }

    override fun onAdVideoTime(time: Int) {
        if (oldTime == time) {
            return
        }
        //如果隐藏时不设置时间
        if (!isVisible) {
            return
        }
        oldTime = time
        if (time in 1..9) {
            this.time = "0$oldTime"
        } else {
            this.time = "$oldTime"
        }
        updateTime()
    }


    private fun updateTime() {
//        if (!playState.playerIsStart() && adType == ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_HEADER) {
//            AppLogger.d("VideoAdHeaderComponent","updateTime gone playState:$playState")
//            gone()
//            return
//        }
        if (adType == ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_PAUSE) {
            AppLogger.d("VideoAdHeaderComponent", "updateTime gone playState:$playState")
            hide()
            return
        }
//        if (!isVisible) {
//            AppLogger.d("VideoAdHeaderComponent", "updateTime visible playState:$playState")
//            show()
//        }
        if (tvTimeCount?.isVisible == false) {
            tvTimeCount?.visible()
            AppLogger.d(
                "VideoAdHeaderComponent",
                "updateTime tvTimeCount visible playState:$playState"
            )
        }

        //悬浮窗只展示时间和秒
        if (initScreenMode == PlayerConstants.ScreenMode.TINY) {
            tvTimeCount?.text = "$time 秒"
            return
        }
        if (Util.getDeviceName()?.equals("10MOONS_ELF6") == true) {
            if (showText.isEmpty()) {
                tvTimeCount?.text = "倒计时:".plus(time).plus("秒")
            } else {
                tvTimeCount?.text =
                    Html.fromHtml(showText).toString().plus(" " + "倒计时:" + time + "秒")
            }
            return
        }
        if (showText.isEmpty()) {
            tvTimeCount?.text = "$time"
            return
        } else {
            tvTimeCount?.text = Html.fromHtml(showText).toString().plus(time)
            return
        }

    }


    /**
     * 刷新窗口切换时候 倒计时布局
     */
    private fun setScreenModelTextSize() {
        val parentLayoutParams = tvTimeCount?.layoutParams as FrameLayout.LayoutParams
        if (initScreenMode == PlayerConstants.ScreenMode.FULL) {
            parentLayoutParams?.height =
                resources.getDimensionPixelSize(R.dimen.y74)
            parentLayoutParams.setMargins(
                0,
                resources.getDimensionPixelSize(R.dimen.y50),
                resources.getDimensionPixelSize(R.dimen.x50),
                0
            )
            tvTimeCount?.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                resources.getDimensionPixelSize(R.dimen.y30).toFloat()
            )
            tvTimeCount?.setPadding(
                resources.getDimensionPixelSize(R.dimen.x20),
                0,
                resources.getDimensionPixelSize(R.dimen.x20),
                0
            )
            tvTimeCount?.setLayoutParams(parentLayoutParams)
        } else {
            parentLayoutParams.height =
                resources.getDimensionPixelSize(R.dimen.y45)
            parentLayoutParams.setMargins(
                0,
                getResources().getDimensionPixelSize(R.dimen.y10),
                getResources().getDimensionPixelSize(R.dimen.x10),
                0
            );

            tvTimeCount?.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                resources.getDimensionPixelSize(R.dimen.y24).toFloat()
            )
            tvTimeCount?.setPadding(
                resources.getDimensionPixelSize(R.dimen.x16),
                0,
                resources.getDimensionPixelSize(R.dimen.x16),
                0
            )
            tvTimeCount?.setLayoutParams(parentLayoutParams)
        }

    }


}