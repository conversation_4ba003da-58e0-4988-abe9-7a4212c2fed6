package com.sohuott.tv.vod.videodetail.activity

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.HorizontalGridView
import androidx.leanback.widget.ItemBridgeAdapter
import com.sohuott.tv.vod.AppLogger

import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.StringUtils
import com.sohuott.tv.vod.presenter.launcher.EpisodeSeriesPresenter
import com.sohuott.tv.vod.ui.EpisodeLayoutNew
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager
import com.sohuott.tv.vod.videodetail.intro.EpisodeTabLayout
import com.sohuott.tv.vod.videodetail.intro.EpisodeTabLayout.OnItemSelectedListener

class EpisodeGroupLayout : ConstraintLayout {

    private var mView: View? = null
    private var mEpisodeTab: EpisodeTabLayout? = null
    private var episodeAbs: TextView? = null
    private var episode: EpisodeLayoutNew? = null
    private var mEpisodeSeriesListView: HorizontalGridView? = null
    private var mArrayObjectAdapter: ArrayObjectAdapter? = null
    private var mItemBridgeAdapter: ItemBridgeAdapter? = null
    private var episodePoints: TextView? = null


    private var mIsTrailerTabPlaying = false
    private var mCurrentIsTrailerTab = false
    private var mEpisodePlayingVideoOrder = 0

    private var mEpisodeGroupInfo: EpisodeGroupInfo? = null

    private var isPlayingEpisode: Boolean = true

    constructor (context: Context) : this(context, null) {
    }

    constructor (context: Context, attrs: AttributeSet?) : this(context, attrs, 0) {
    }

    constructor (context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        mView = inflate(context, R.layout.episode_group_layout, this)
        setClipToPadding(false)
        setClipChildren(false)
        initView()
    }


    private fun initView() {
        // 绑定选集相关控件
        mEpisodeTab = findViewById<EpisodeTabLayout>(R.id.episode_tab)
        episodeAbs = findViewById<TextView>(R.id.episode_abs)
        episode = findViewById<EpisodeLayoutNew>(R.id.episode_layout)
        episodePoints = findViewById<TextView>(R.id.episode_points)
        mEpisodeSeriesListView = findViewById<HorizontalGridView>(R.id.episode_series)
        mArrayObjectAdapter = ArrayObjectAdapter(EpisodeSeriesPresenter())
        mItemBridgeAdapter = ItemBridgeAdapter(mArrayObjectAdapter)
        mEpisodeSeriesListView?.adapter = mItemBridgeAdapter
        mEpisodeSeriesListView?.setHorizontalSpacing(48)
//        episode?.setFocusBorderView(mFocusBorderView)
        episode?.setmEpisodePoints(episodePoints)
        mEpisodeTab?.setSelectedListener(object : OnItemSelectedListener {
            override fun selectedById(id: Int) {
                val aid = mEpisodeGroupInfo?.aid ?: 0
                val videoId = mEpisodeGroupInfo?.vid ?: 0
                val dateType = mEpisodeGroupInfo?.dateType ?: 0
                val cateCode = mEpisodeGroupInfo?.cateCode ?: 0
                val sortOrder = mEpisodeGroupInfo?.sortOrder ?: 0
                val trailerId = mEpisodeGroupInfo?.trailerId ?: 0
                val trailerCount = mEpisodeGroupInfo?.trailerCount ?: 0
                val albumEpisodeType = mEpisodeGroupInfo?.albumEpisodeType ?: 0
                val isTrailer = (mEpisodeGroupInfo?.tvIsIntrest != 0)
                val videoCount = mEpisodeGroupInfo?.videoCount ?: 0
                val isShowTitle = mEpisodeGroupInfo?.isShowTitle ?: 0

                when (id) {
                    R.id.episode_tab_select -> {
                        mCurrentIsTrailerTab = false
                        episode?.visibility = VISIBLE
                        mEpisodeSeriesListView?.visibility = GONE
                        episode?.setEpisodeIsSelected(!mIsTrailerTabPlaying)
                        episode?.episodeType =
                            chooseLayoutType(false, cateCode, isShowTitle, dateType, 0)
                        episode?.initFromIntroViewHolder(
                            aid,
                            videoId,
                            dateType,
                            cateCode,
                            sortOrder,
                            albumEpisodeType,
                            isTrailer,
                            videoCount,
                            -1,
                            if (!mIsTrailerTabPlaying) mEpisodePlayingVideoOrder else 0,
                            false
                        )
                        VideoDetailRequestManager.tabExposure("选集")
                    }

                    R.id.episode_tab_trailer -> {
                        if (!mCurrentIsTrailerTab) {
                            VideoDetailRequestManager.tabExposure("花絮")
                        }
                        mCurrentIsTrailerTab = true
                        episode?.visibility = VISIBLE
                        mEpisodeSeriesListView?.visibility = GONE
                        episode?.setEpisodeIsSelected(mIsTrailerTabPlaying)
                        episode?.episodeType =
                            chooseLayoutType(true, cateCode, isShowTitle, dateType, 0)
                        episode?.initFromIntroViewHolder(
                            trailerId,
                            videoId,
                            dateType,
                            cateCode,
                            1,
                            albumEpisodeType,
                            true,
                            trailerCount,
                            -1,
                            if (mIsTrailerTabPlaying) mEpisodePlayingVideoOrder else 0,
                            true
                        )
                    }

                    R.id.episode_tab_series -> {
                        mCurrentIsTrailerTab = false
                        mEpisodeSeriesListView?.visibility = VISIBLE
                        episode?.visibility = GONE
                        VideoDetailRequestManager.tabExposure("同系列")
                    }
                }

            }
        })
    }

    /**
     * 默认获取该View的焦点处理
     */
    fun requestFocusDefault(forceFullscreen: Boolean) {
        if (episode!!.visibility == GONE || forceFullscreen) {
//            post(new Runnable() {
//                @Override
//                public void run() {
//                    Log.d(TAG, "run: 111");
//                    if (isTeenager()) {
//                        fullscreen_button.requestFocus();
//                    }
//                }
//            });
        } else {
            episode!!.setEpisodeFoucus()
        }
    }

    /**
     * 设置选集所需信息
     */
    fun setEpisodeGroupInfo(episodeGroupInfo: EpisodeGroupInfo?) {
        mEpisodeGroupInfo = episodeGroupInfo
        val aid = mEpisodeGroupInfo?.aid ?: 0
        val videoId = mEpisodeGroupInfo?.vid ?: 0
        val dateType = mEpisodeGroupInfo?.dateType ?: 0
        val cateCode = mEpisodeGroupInfo?.cateCode ?: 0
        val sortOrder = mEpisodeGroupInfo?.sortOrder ?: 0
        val trailerId = mEpisodeGroupInfo?.trailerId ?: 0
        val trailerCount = mEpisodeGroupInfo?.trailerCount ?: 0
        val albumEpisodeType = mEpisodeGroupInfo?.albumEpisodeType ?: 0
        val isTrailer = (mEpisodeGroupInfo?.tvIsIntrest != 0)
        val videoCount = mEpisodeGroupInfo?.videoCount ?: 0
        val isShowTitle = mEpisodeGroupInfo?.isShowTitle ?: 0

        mEpisodeSeriesListView?.visibility = GONE
        episode?.setEpisodeIsSelected(isPlayingEpisode)
        episode?.episodeType = chooseLayoutType(false, cateCode, isShowTitle, dateType, 0)
        episode?.setRecommendList(episodeGroupInfo?.recommendList)
        if (episodeGroupInfo?.isExtendNull == true) {
            if (isPlayingEpisode || dateType != Constant.DATA_TYPE_VRS) {
                episode!!.initFromIntroViewHolder(
                    aid,
                    videoId,
                    dateType,
                    cateCode,
                    2,
                    episodeGroupInfo.albumEpisodeType,
                    isTrailer,
                    videoCount,
                    -1,
                    mEpisodePlayingVideoOrder,
                    mIsTrailerTabPlaying
                )
                AppLogger. v("layout 3")
            } else {
                episode!!.initFromIntroViewHolder(
                    aid,
                    videoId,
                    dateType,
                    cateCode,
                    2,
                    episodeGroupInfo.albumEpisodeType,
                    isTrailer,
                    videoCount,
                    -1
                )
                AppLogger.v("layout 4")
            }
        } else {
            if (isPlayingEpisode || dateType != Constant.DATA_TYPE_VRS) {
                episode!!.initFromIntroViewHolder(
                    aid,
                    videoId,
                    dateType,
                    cateCode,
                    episodeGroupInfo?.sortOrder ?: 0,
                    episodeGroupInfo?.albumEpisodeType ?: 0,
                    episodeGroupInfo?.tvIsIntrest == 1,
                    videoCount,
                    -1,
                    -1,
                    mIsTrailerTabPlaying
                )
                AppLogger. v("layout 1")
            } else {
                episode!!.initFromIntroViewHolder(
                    aid,
                    videoId,
                    dateType,
                    cateCode,
                    episodeGroupInfo?.sortOrder ?: 0,
                    episodeGroupInfo?.albumEpisodeType ?: 0,
                    isTrailer,
                    videoCount,
                    -1
                )
                AppLogger. v("layout 2")
            }
        }
        if (episodeGroupInfo?.dateType != Constant.DATA_TYPE_VRS) {
            episodeAbs?.visibility = VISIBLE
            mEpisodeTab?.initEpisodeTabItems(false, false)
        } else {
            when (episodeGroupInfo.cateCode) {
                //电影
                100 -> {
                    episodeAbs?.visibility = VISIBLE
                }
                //综艺
                106 -> {
                    if (StringUtils.isEmptyStr(episodeGroupInfo.updateNotification)) {
                        episodeAbs?.text = "更新至${episodeGroupInfo.latestVideoCount}期"
                    } else {
                        episodeAbs?.text =
                            "更新至${episodeGroupInfo.latestVideoCount}期，${episodeGroupInfo.updateNotification}"
                    }
                    episodeAbs?.visibility = VISIBLE
                }
                //教育
                119 -> {
                    episodeAbs?.text = "共${episodeGroupInfo.tvSets}课时"
                    episodeAbs?.visibility = VISIBLE
                }
                //电视剧
                101,
                107,
                115,
                10001 -> {
                    if (!TextUtils.isEmpty(episodeGroupInfo.maxVideoOrder)
                        && episodeGroupInfo.maxVideoOrder != episodeGroupInfo.tvSets
                    ) {
                        if (StringUtils.isEmptyStr(episodeGroupInfo.updateNotification)) {
                            episodeAbs!!.text = ("更新至" + episodeGroupInfo.maxVideoOrder
                                    + "集 / 共" + episodeGroupInfo.tvSets + "集")
                        } else {
                            episodeAbs!!.text = ("更新至" + episodeGroupInfo.maxVideoOrder
                                    + "集 / 共" + episodeGroupInfo.tvSets + "集" + "，" + episodeGroupInfo.updateNotification)
                        }
                    } else {
                        if (episodeGroupInfo.updateNotification?.isNotEmpty() == true && episodeGroupInfo.tvIsEarly != 0) {
                            episodeAbs?.text =
                                episodeGroupInfo.tvSets + "集全，" + episodeGroupInfo.updateNotification
                        } else {
                            episodeAbs?.text = episodeGroupInfo.tvSets + "集全"
                        }
                    }
                    episodeAbs?.visibility = VISIBLE
                }

                else -> {
                    episodeAbs?.visibility = VISIBLE
                }

            }
            episode?.visibility = VISIBLE
            if (episodeGroupInfo.albumSeries != null && (episodeGroupInfo.albumSeries?.size
                    ?: 0) > 0
            ) {
                mArrayObjectAdapter?.clear()
                mArrayObjectAdapter?.addAll(0, episodeGroupInfo.albumSeries)
            }
            //初始化选集tab
            mEpisodeTab!!.initEpisodeTabItems(
                (episodeGroupInfo?.hasTrailer == true && episodeGroupInfo.cateCode != Constant.CATECODE_MOVIE),
                (episodeGroupInfo.albumSeries != null && episodeGroupInfo?.albumSeries?.size ?: 0 > 1)
            )
        }

        VideoDetailRequestManager.tabExposure("选集")
        mEpisodeSeriesListView!!.visibility = GONE
    }


    fun updateAfterPlayer(hasEpisode: Boolean, videoOrder: Int, isTrailerTab: Boolean, vid: Int) {
        if (hasEpisode) {
            mEpisodeGroupInfo?.vid = vid
            mIsTrailerTabPlaying = isTrailerTab
            //        boolean episodeIsPlay = episodeType == episode.getEpisodeType() ? true : false;
            val episodeIsPlay =
                mCurrentIsTrailerTab && mIsTrailerTabPlaying || !mCurrentIsTrailerTab && !mIsTrailerTabPlaying
            this.isPlayingEpisode = episodeIsPlay
            mEpisodePlayingVideoOrder = videoOrder
            episode!!.updateSelectAfterPlay(videoOrder, episodeIsPlay, vid)
            if (mIsTrailerTabPlaying) {
                mEpisodeTab!!.selectTrailerTab()
            }
        }

    }


    fun getEpisodeSortOrder(): Int {
        return episode?.getSortOrder() ?: 1
    }

    fun getEpisodeType(): Int {
        return episode?.getEpisodeType() ?: -1
    }

    fun getEpisodeTotalCount(): Int {
        return episode?.getTotalCount() ?: 0
    }

    fun getPageSize(): Int {
        return episode?.getPageSize() ?: 3
    }

    fun isLastEpisode(videoOrder: Int): Int {
        return episode?.isLastEpisode(videoOrder) ?: -1
    }

    fun getEpisodeVideoOrder(): Int {
        return if (episode?.visibility == VISIBLE) {
            episode?.episodeVideoOrder ?: 1
        } else {
            1
        }
    }

}