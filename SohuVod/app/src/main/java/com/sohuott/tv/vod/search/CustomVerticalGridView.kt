package com.sohuott.tv.vod.search

import android.content.Context
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.leanback.widget.VerticalGridView
import androidx.recyclerview.widget.RecyclerView
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger

class CustomVerticalGridView : VerticalGridView {
    private var columns: Int = 0
    private var itemCount: Int = 0
    private var lastItem: View? = null
    private var mOnCustomVerticalGridViewFocusChangeListener: OnCustomVerticalGridViewFocusChangeListener? = null

    fun interface OnCustomVerticalGridViewFocusChangeListener {
        fun onCustomVerticalGridViewFocusUp()
    }

    fun setOnCustomVerticalGridViewFocusChangeListener(listener: OnCustomVerticalGridViewFocusChangeListener) {
        mOnCustomVerticalGridViewFocusChangeListener = listener
    }
    /**
     * 倒数第二行下方没有item的时候，按KEYCODE_DPAD_DOWN时，最后一个item获取焦点
     */
    private val mListener = object : OnChildViewHolderSelectedListener() {



        override fun onChildViewHolderSelected(parent: RecyclerView,
                                               child: ViewHolder?,
                                               position: Int,
                                               subposition: Int) {
            super.onChildViewHolderSelected(parent, child, position, subposition)


            itemCount = adapter?.itemCount ?: 0


            if (position + columns >= itemCount && (position % columns) + 1 > itemCount % columns) {


                child?.itemView?.setOnKeyListener { v, keyCode, event ->

                    if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.action == KeyEvent.ACTION_DOWN) {

                        lastItem = layoutManager?.findViewByPosition(adapter?.itemCount!! - 1)
                        LibDeprecatedLogger.d("position : ${adapter?.itemCount!! - 1} lastItem : $lastItem")
                        //lastItem不等于null，并且当前的这个item不是最后一行
                        if (lastItem != null && ((position + 1) + columns - 1) / columns != (adapter?.itemCount!! + columns - 1) / columns) {

                            lastItem?.requestFocus()
                            return@setOnKeyListener true
                        }
                    }
                    false
                }
            } else if (position < columns) {
                child?.itemView?.setOnKeyListener { v, keyCode, event ->

                    if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.action == KeyEvent.ACTION_DOWN) {
                        mOnCustomVerticalGridViewFocusChangeListener?.onCustomVerticalGridViewFocusUp()
                    }
                    false
                }
            }
        }
    }

    override fun focusSearch(focused: View?, direction: Int): View {
        if (direction == FOCUS_RIGHT) {
            return if (isDescendantView(super.focusSearch(focused, direction))) {
                super.focusSearch(focused, direction)
            } else {
                focused!!
            }
        } else if (direction == FOCUS_LEFT) {
            return if (super.focusSearch(focused, direction) is AppCompatTextView) {
                focused!!
            } else {
                super.focusSearch(focused, direction)
            }
        }
        LibDeprecatedLogger.d("focusSearch focused $focused direction $direction view ${super.focusSearch(focused, direction)}")
        return super.focusSearch(focused, direction)
    }

    private fun isDescendantView(view: View): Boolean {
        var currentView: View? = view
        while (currentView != null) {
            if (currentView.parent === this) {
                return true
            }
            currentView = currentView.parent as? View
        }
        return false
    }

    constructor(context: Context) : super(context) {
        setOnChildViewHolderSelectedListener(mListener)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        setOnChildViewHolderSelectedListener(mListener)
    }

    constructor(context: Context, attrs: AttributeSet, defStyle: Int) : super(context, attrs, defStyle) {
        setOnChildViewHolderSelectedListener(mListener)
    }

    override fun setNumColumns(numColumns: Int) {
        columns = numColumns
        super.setNumColumns(numColumns)
    }
}
