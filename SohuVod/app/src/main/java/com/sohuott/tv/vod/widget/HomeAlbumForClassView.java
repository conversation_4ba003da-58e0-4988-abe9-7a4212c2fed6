package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;

/**
 * Created by yizhang210244 on 2017/9/13.
 */
//针对一级分类和二级分类获取的数据
public class HomeAlbumForClassView extends RelativeLayout
        implements View.OnFocusChangeListener{

    private CornerTagImageView posterIV;
    private TextView titleTV;
    private TextView subTitleTV;
    private TextView hintTV;
    private TextView scoreTV;
    private ImageView scoreIV;
    private FocusBorderView mFocusBorderView;  //焦点框
    private ListAlbumModel mContent;
    private long mChannelListId;
    private boolean mIsFirstClassification;
    private int mPosition;
    private boolean mResizeEnable = false;
    private int mWidth, mHeight;
    private boolean mIsPgc = false;

    public HomeAlbumForClassView(Context context) {
        super(context);
        init(context);
    }

    public HomeAlbumForClassView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public HomeAlbumForClassView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.home_album_for_class_view, this, true);
        posterIV = (CornerTagImageView) findViewById(R.id.posterIV);
        titleTV = (TextView) findViewById(R.id.titleTV);
        subTitleTV = (TextView) findViewById(R.id.subTitleTV);
        hintTV = (TextView) findViewById(R.id.hintTV);
        scoreTV = (TextView) findViewById(R.id.scoreTV);
        scoreIV = (ImageView) findViewById(R.id.doubangIV);
        posterIV.setCornerHeightRes(R.dimen.y40);
        setBackgroundResource(R.drawable.recommend_item_selector);
        setFocusable(true);
//        setOnClickListener(this);
        setOnFocusChangeListener(this);
    }

    public void setResize(int width, int height) {
        mResizeEnable = true;
        mWidth = width;
        mHeight = height;
    }

    private String getImageUrl(ListAlbumModel content){
        String imageUrl = "";
        if(content == null){
             return imageUrl;
        }
        if(mIsPgc){
            if(content.cateCodeFirst != CornerTagImageView.CORNER_TYPE_SOHUCLASS){
                imageUrl = content.bigCover;
            }else {
                imageUrl = content.plCoverImg169;
            }
        }else {
            imageUrl = content.albumExtendsPic_640_360;
        }
        return imageUrl;
    }

    public void setData( ListAlbumModel content,boolean isFirstclassification ,long id, int position,boolean isPgc) {
        if (content == null) {
            return;
        }
        mContent = content;
        mIsPgc = isPgc;
        mIsFirstClassification = isFirstclassification;
        mChannelListId = id;
        mPosition = position;

        String imageUrl = getImageUrl(content);
        if(isPgc){
            if(content.cateCodeFirst != CornerTagImageView.CORNER_TYPE_SOHUCLASS){
                titleTV.setText(content.videoTitle);
            }else {
                titleTV.setText(content.ptitle);
            }
            setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(mContent == null){
                        return;
                    }
                    ActivityLauncher.startVideoDetailActivity(v.getContext(), mContent.videoId,
                            mContent.cateCodeFirst == CornerTagImageView.CORNER_TYPE_VR ? Constant.DATA_TYPE_VR : Constant.DATA_TYPE_PGC,
                            Constant.PAGE_HOME);
                    RequestManager.getInstance().onClickHomeListAlbumItem(mIsFirstClassification,mChannelListId,mContent.videoId,mPosition);
                }
            });

        }else {
            titleTV.setText(content.tvName);
            posterIV.setCornerTypeWithType(content.tvIsFee, content.tvIsEarly, content.useTicket, content.paySeparate,content.cornerType);
            setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(mContent == null){
                        return;
                    }
                    ActivityLauncher.startVideoDetailActivity(v.getContext(), mContent.id,Constant.PAGE_HOME);
                    RequestManager.getInstance().onClickHomeListAlbumItem(mIsFirstClassification,mChannelListId,mContent.id,mPosition);
                }
            });
        }
        if(!TextUtils.isEmpty(imageUrl)) {
            posterIV.setImageRes(imageUrl,false);
        }
        setHintTV(content);
    }

    public void setAlbumVisibility(boolean value){
        if(mContent == null){
            return;
        }
        String imageUrl = getImageUrl(mContent);
        if(!TextUtils.isEmpty(imageUrl)){
            if(value){
                posterIV.setImageRes(imageUrl,false);
            }else {
                posterIV.clearImageMemo();
            }
        }
    }


    private void setHintTV(ListAlbumModel albumParam) {
        try {
            if (albumParam != null) {
                long cateCode = albumParam.cateCode;
                //100：电影；101：电视剧；106：综艺；107：纪录片；115：动漫；10001：美剧；
                if (cateCode == 101 || cateCode == 107 || cateCode == 115 || cateCode == 10001) {
                    int tvSets = albumParam.tvSets;
                    int latestVideoCount = Integer.parseInt(albumParam.latestVideoCount);
                    if (latestVideoCount != 0) {
                        if (tvSets == latestVideoCount) {
                            hintTV.setText(latestVideoCount + "集全");
                            setHintTVUI(0, albumParam.latestVideoCount.length());
                        } else {
                            hintTV.setText("更新至" + latestVideoCount + "集");
                            setHintTVUI(3, albumParam.latestVideoCount.length());
                        }
                        hintTV.setBackgroundResource(R.drawable.home_album_hint_tv_bg);
                        hintTV.setVisibility(View.VISIBLE);
                    } else {
                        hintTV.setVisibility(View.GONE);
                    }
                } else if (cateCode == 106) {
                    if (!TextUtils.isEmpty(albumParam.showDate)) {
                        hintTV.setBackgroundResource(R.drawable.home_album_hint_tv_bg);
                        hintTV.setText(albumParam.showDate + "期");
                        hintTV.setVisibility(View.VISIBLE);
                    } else {
                        hintTV.setVisibility(View.GONE);
                    }
                } else {
                    hintTV.setVisibility(View.GONE);
                }
            } else {
                hintTV.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            hintTV.setVisibility(View.GONE);
        }
    }

    private void setHintTVUI(int index, int length) {
        ForegroundColorSpan whiteSpan = new ForegroundColorSpan(Color.parseColor("#d5d5d5"));
        ForegroundColorSpan yellowSpan = new ForegroundColorSpan(Color.parseColor("#ffbd5f"));
        SpannableStringBuilder builder = new SpannableStringBuilder(hintTV.getText().toString());
        if (index > 0) {
            builder.setSpan(whiteSpan, 0, index, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        builder.setSpan(yellowSpan, index, index + length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        builder.setSpan(whiteSpan, index + length, hintTV.getText().toString().length(),
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        hintTV.setText(builder);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
            if (mFocusBorderView != null) {
                mFocusBorderView.setFocusView(v);
            }
            FocusUtil.setFocusAnimator(v, mFocusBorderView);
            setTVOnFocus(titleTV);
            setTVOnFocus(subTitleTV);
            showScore();
        } else {
            if (mFocusBorderView != null) {
                mFocusBorderView.setUnFocusView(v);
            }
            FocusUtil.setUnFocusAnimator(v);
            setTVUnFocus(titleTV);
            setTVUnFocus(subTitleTV);
            hideScore();
        }
        setTextColor(hasFocus);
    }

    private void setTextColor(boolean focus) {
        if (focus) {
            titleTV.setTextColor(getResources().getColor(R.color.home_focused_color));
            subTitleTV.setTextColor(getResources().getColor(R.color.home_focused_color));
        } else {
            titleTV.setTextColor(getResources().getColor(R.color.home_title_color));
            subTitleTV.setTextColor(getResources().getColor(R.color.home_subtitle_color));
        }
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

    private void showScore() {
        if (mContent != null) {
            ListAlbumModel albumParam = mContent;
            if (albumParam != null) {
                try {
                    String scoreSource = albumParam.scoreSource;
                    if ("1".equals(scoreSource)) {
                        if (!TextUtils.isEmpty(albumParam.score)) {
                            scoreTV.setText(albumParam.score);
                            scoreTV.setVisibility(View.VISIBLE);
                            scoreIV.setVisibility(View.GONE);
                        }
                    } else if ("2".equals(scoreSource)) {
                        if (!TextUtils.isEmpty(albumParam.doubanScore)) {
                            scoreTV.setText(albumParam.doubanScore);
                            scoreTV.setVisibility(View.VISIBLE);
                            scoreIV.setVisibility(View.VISIBLE);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void hideScore() {
        scoreIV.setVisibility(View.GONE);
        scoreTV.setVisibility(View.GONE);
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        mFocusBorderView = focusView;
    }

    HomeAlbumView.OnClickCallback mOnClickCallback;

    public void setOnClickCallback(HomeAlbumView.OnClickCallback mOnClickCallback) {
        this.mOnClickCallback = mOnClickCallback;
    }

    public interface  OnClickCallback{
        void onClick(long loopChannelId,int order);
    }

}
