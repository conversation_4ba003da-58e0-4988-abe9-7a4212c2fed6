package com.sohuott.tv.vod.service;

import android.app.AlarmManager;
import android.app.Notification;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.sohuott.tv.vod.app.App;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ServerMessage;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.receiver.WakeReceiver;
import com.sohuott.tv.vod.ui.SystemNotifyDialog;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.receiver.CancelService;
import com.sohu.lib_utils.ServiceNotificationUtil;

import java.lang.ref.WeakReference;
import java.util.Timer;
import java.util.TimerTask;

import io.reactivex.observers.DisposableObserver;

/**
 * @version V1.0.0
 * @FileName: com.sohuott.tv.vod.service.SystemDialogService.java
 * @author: ZiJiaCui
 * @Description: ${用于展示全局性对话框}
 * @date: 2017-04-10 16:11
 */
public class SystemDialogService extends Service {

    private static final int MSG_SHOW_DIALOG = 1001;

    private Timer mTimer = new Timer();

    private boolean isFromBoot = false;
    private long delay_default = 2 * 1000;
    private static long period = 0;

    // 定时唤醒的时间间隔，这里为了自己测试方边设置了30s
    private final static int ALARM_INTERVAL = 30 * 1000;
    // 发送唤醒广播请求码
    private final static int WAKE_REQUEST_CODE = 5121;
    // 守护进程 Service ID
    private final static int DAEMON_SERVICE_ID = -5121;

    public static long heartBeat = System.currentTimeMillis();

    public static boolean isServiceRunning() {
        return System.currentTimeMillis() - heartBeat < period + 10 * 1000;
    }

    private static class InnerHandler extends Handler {
        private WeakReference<SystemDialogService> mWrapper;
        InnerHandler(SystemDialogService service){
            mWrapper = new WeakReference<>(service);
        }
        @Override
        public void handleMessage(@NonNull Message msg) {
            SystemDialogService service = mWrapper.get();
            if (service == null){
                return;
            }
            LibDeprecatedLogger.d("create dia");
            if (msg.what == MSG_SHOW_DIALOG) {
                ServerMessage.Data data = (ServerMessage.Data) msg.obj;
                try {
                    ServerMessage.Parameter parameter = new Gson().fromJson(data.parameter,
                            ServerMessage.Parameter.class);
                    LibDeprecatedLogger.d("isPop = " + parameter.isPopup);
//                    parameter.isPopup = true;
                    if (parameter.isPopup) {
                        // TODO: 2018/1/9 需要全局提示，并且本地数据早于网络最新数据
                        SystemNotifyDialog dialog = SystemNotifyDialog.getInstance(service.getApplicationContext());
                        if (!dialog.isDialogShowing()) {
                            if (!Util.isOtherPackageOnStackTop(service.getApplicationContext())) {
                                LibDeprecatedLogger.d("2. the app is front localId = " + Util.getLatestHotspotId(service.getApplicationContext()));
                                return;
                            }
                            dialog.showDialog(data);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
//            ActivityLauncher.startTranslucentActivity(SystemDialogService.this);
        }
    }
    private Handler mHandler = new InnerHandler(this);

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
            startForeground(ServiceNotificationUtil.NOTIFICATION_FOREGROUND_ID, ServiceNotificationUtil.getNotification(getApplicationContext()));
            Intent intent =  new Intent(this, CancelService.class);
            startForegroundService(intent);
        }
        LibDeprecatedLogger.d("onCreate");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {

        // 利用 Android 漏洞提高进程优先级，
        startForeground(DAEMON_SERVICE_ID, new Notification());
        // 当 SDk 版本大于18时，需要通过内部 Service 类启动同样 id 的 Service
        if (Build.VERSION.SDK_INT >= 18) {
            Intent innerIntent = new Intent(this, InnerService.class);
            startService(innerIntent);
        }

        // 发送唤醒广播来促使挂掉的UI进程重新启动起来
        // 发送唤醒广播来促使挂掉的UI进程重新启动起来
        AlarmManager alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        Intent alarmIntent = new Intent();
        alarmIntent.setAction(WakeReceiver.ACTION_WAKE);
        PendingIntent operation = PendingIntent.getBroadcast(this, WAKE_REQUEST_CODE, alarmIntent,
                PendingIntent.FLAG_UPDATE_CURRENT);
        alarmManager.setInexactRepeating(AlarmManager.RTC_WAKEUP, System.currentTimeMillis(),
                ALARM_INTERVAL, operation);
        heartBeat = System.currentTimeMillis();

        LibDeprecatedLogger.d("onStartCommand flags = " + flags);
        if (null != intent) {
            isFromBoot = intent.getBooleanExtra(ParamConstant.PARAM_IS_BOOT, false);
            delay_default = intent.getLongExtra(ParamConstant.PARAM_DELAY, delay_default);
        }
        if (null != mTimer) {
            mTimer.cancel();
            mTimer.purge();
        }
        mTimer = new Timer();

        if (App.getDebug()) {
            period = 10 * 1000;
        } else {
            period = 10 * 60 * 1000;
        }
        mTimer.schedule(new BurstInfoTask(), delay_default, period);

        return Service.START_STICKY;
    }

    @Override
    public void onDestroy() {
        LibDeprecatedLogger.d("onDestroy");
        super.onDestroy();
        mTimer.cancel();
        mTimer.purge();
        mTimer = null;
        mHandler.removeCallbacksAndMessages(null);
        mHandler = null;
    }

    /**
     * 开启循环检测
     *
     * @param context
     */
    public static void startSystemDialogService(Context context, boolean isFromBoot) {
        if (null == context) {
            return;
        }

        String flag = Util.getIsGlobalDiaActive(context);
        LibDeprecatedLogger.d("flag = " + flag);
        if (!"1".equalsIgnoreCase(flag)) {
            context.stopService(new Intent(context, SystemDialogService.class));
            return;
        }
        Intent serviceIntent = new Intent(context, SystemDialogService.class);
        serviceIntent.putExtra(ParamConstant.PARAM_IS_BOOT, isFromBoot);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(serviceIntent);
        } else {
            context.startService(serviceIntent);
        }
    }

    public static void startSystemDialogService(Context context, boolean isFromBoot, long delay) {
        if (null == context) {
            return;
        }

        String flag = Util.getIsGlobalDiaActive(context);
        LibDeprecatedLogger.d("flag = " + flag);
        if (!"1".equalsIgnoreCase(flag)) {
            context.stopService(new Intent(context, SystemDialogService.class));
            return;
        }
        Intent serviceIntent = new Intent(context, SystemDialogService.class);
        serviceIntent.putExtra(ParamConstant.PARAM_IS_BOOT, isFromBoot);
        serviceIntent.putExtra(ParamConstant.PARAM_DELAY, delay);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(serviceIntent);
        } else {
            context.startService(serviceIntent);
        }
    }

    private void getLatestHotspot() {
        heartBeat = System.currentTimeMillis();
        if (!Util.isOtherPackageOnStackTop(this)) {
            LibDeprecatedLogger.d("the app is front localId = " + Util.getLatestHotspotId(getApplicationContext()));
            return;
        }
        LibDeprecatedLogger.d("the app is background");
        NetworkApi.getMessageData(1, 1, new DisposableObserver<ServerMessage>() {

            @Override
            public void onNext(ServerMessage value) {
                if (null != value && value.status == 0 && null != value.data
                        && value.data.size() > 0) {
                    ServerMessage.Data data = value.data.get(0);
                    if (null != data) {
                        LibDeprecatedLogger.d("id = " + Util.getLatestHotspotId(getApplicationContext()) + ", " + data.id);
                        if (Util.getLatestHotspotId(getApplicationContext()) < data.id) {
                            // TODO: 2018/1/25 从未展示过
                            mHandler.removeCallbacksAndMessages(null);
                            Message msg = mHandler.obtainMessage(MSG_SHOW_DIALOG);
                            msg.obj = data;
                            mHandler.sendMessage(msg);
                            isFromBoot = false;
                        } else if (Util.getLatestHotspotId(getApplicationContext()) == data.id) {
                            // TODO: 2018/1/25 已展示
                            if (Util.getHotspotAutoClosed(getApplicationContext(), data.id) == -1 && isFromBoot) {
                                // TODO: 2018/1/25 自动消失并且当前为开机启动
                                mHandler.removeCallbacksAndMessages(null);
                                Message msg = mHandler.obtainMessage(MSG_SHOW_DIALOG);
                                msg.obj = data;
                                mHandler.sendMessage(msg);
                                isFromBoot = false;
                            }
                        }
                    }
                } else {

                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("Error: " + e.getMessage(), e);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("onComplete()");
            }
        });
    }

    private class BurstInfoTask extends TimerTask {

        @Override
        public void run() {
            getLatestHotspot();
        }
    }


    /**
     * 实现一个内部的 Service，实现让后台服务的优先级提高到前台服务，这里利用了 android 系统的漏洞，
     * 不保证所有系统可用，测试在7.1.1 之前大部分系统都是可以的，不排除个别厂商优化限制
     */
    public static class InnerService extends Service {

        @Override
        public void onCreate() {
            LibDeprecatedLogger.d("InnerService -> onCreate");
            super.onCreate();
        }

        @Override
        public int onStartCommand(Intent intent, int flags, int startId) {
            LibDeprecatedLogger.d("InnerService -> onStartCommand");
            startForeground(DAEMON_SERVICE_ID, new Notification());
            stopSelf();
            return super.onStartCommand(intent, flags, startId);
        }

        @Override
        public IBinder onBind(Intent intent) {
            // TODO: Return the communication channel to the service.
            throw new UnsupportedOperationException("onBind 未实现");
        }

        @Override
        public void onDestroy() {
            LibDeprecatedLogger.d("InnerService -> onDestroy");
            super.onDestroy();
        }
    }
}


