package com.sohuott.tv.vod.widget;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.AuditDenyAids;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohu.lib_utils.NetworkUtils;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;
import com.sohu.lib_utils.StringUtil;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by fenglei on 17-2-17.
 */

public class HomeHistoryView extends LinearLayout
        implements View.OnClickListener, View.OnFocusChangeListener, View.OnKeyListener {

    private static final String TAG = HomeHistoryView.class.getSimpleName();

    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;
    private LoginUserInformationHelper mHelper;

    private List<AuditDenyAids.DataBean> mAuditDenyAidsList;
    private List<PlayHistory> mPlayHistoryList;
    private long mChannelId;
    private LinearLayout historyLL1;
    private RelativeLayout historyLL3;
    private ConstraintLayout noHistoryRoot;
    //    private ImageView noHistoryIV;
    private TextView titleTV1, noHistoryTV1, noHistoryTV2, historyTV;
    private TextView subTitleTV1;
    HashMap<String, String> pathInfo = new HashMap<>();

//    private FocusBorderView mFocusBorderView;  //焦点框

    public HomeHistoryView(Context context) {
        super(context);
        init(context);
    }

    public HomeHistoryView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public HomeHistoryView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        setOrientation(LinearLayout.VERTICAL);
//        setBackgroundColor(Color.parseColor("#66343434"));
        LayoutInflater.from(context).inflate(R.layout.home_history_view, this, true);
        historyLL1 = (LinearLayout) findViewById(R.id.historyLL1);
        historyLL3 = (RelativeLayout) findViewById(R.id.historyLL3);
//        noHistoryIV = (ImageView) findViewById(R.id.noHistoryIV);
        noHistoryTV1 = (TextView) findViewById(R.id.noHistoryTV1);
        noHistoryTV2 = (TextView) findViewById(R.id.noHistoryTV2);
        noHistoryRoot = (ConstraintLayout) findViewById(R.id.noHistoryRoot);
        historyTV = (TextView) findViewById(R.id.historyTV);
        titleTV1 = (TextView) findViewById(R.id.titleTV1);
        subTitleTV1 = (TextView) findViewById(R.id.subTitleTV1);
        historyLL1.setOnFocusChangeListener(this);
        historyLL3.setOnFocusChangeListener(this);
        historyLL1.setOnKeyListener(this);
        historyLL3.setOnKeyListener(this);
        historyLL1.setOnClickListener(this);
        historyLL3.setOnClickListener(this);
        setOnClickListener(this);
        setOnFocusChangeListener(this);
        setOnKeyListener(this);
        setDescendantFocusability(FOCUS_BEFORE_DESCENDANTS);
//        setBackgroundResource(R.drawable.recommend_item_selector);

        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }

        mHelper = LoginUserInformationHelper.getHelper(context);

    }

    public void setPathInfo(HashMap<String, String> pathInfo) {
        this.pathInfo = pathInfo;
    }

    public void setData(List<PlayHistory> playHistoryList, long channelId) {
        mPlayHistoryList = playHistoryList;
        mChannelId = channelId;
        if (playHistoryList == null || playHistoryList.size() == 0) {
            if (mHelper.getIsLogin()) {
                noHistoryTV1.setText("无观看历史");
                noHistoryTV2.setText("精彩内容看起来");
            } else {
                noHistoryTV1.setText("登录同步账号历史");
                noHistoryTV2.setText("暂无观看历史");
            }
            historyLL1.setVisibility(View.GONE);
            noHistoryRoot.setVisibility(View.VISIBLE);
            setFocusable(true);
            historyLL3.setVisibility(GONE);
//            if(historyLL3.hasFocus()) {
//                historyLL3.setFocusable(false);
//                requestFocus();
//            } else {
//                historyLL3.setFocusable(false);
//            }
            return;
        }
        historyLL3.setVisibility(VISIBLE);
        if (!mHelper.getIsLogin()) {
            historyTV.setText("登录同步账号历史");
        } else {
            historyTV.setText("全部历史");
        }
        noHistoryRoot.setVisibility(View.GONE);
        setFocusable(false);
        historyLL3.setFocusable(true);
        PlayHistory playHistory = playHistoryList.get(0);
        historyLL1.setVisibility(View.VISIBLE);
        showTitle(titleTV1, playHistory);
        subTitleTV1.setText(getSubtitle(playHistory));
        if (playHistory.getDataType() == 0) {
            getAuditDenyAids(playHistory.getAlbumId() + ":" + playHistory.getVideoId(), "");
        } else {
            getAuditDenyAids("", StringUtil.toString(playHistory.getVideoId()));
        }

    }

    private void getAuditDenyAids(String vrsId, String pgcId) {


        NetworkApi.getAuditDenyAids(vrsId, pgcId, new Observer<AuditDenyAids>() {
            @Override
            public void onSubscribe(Disposable disposable) {

            }

            @Override
            public void onNext(AuditDenyAids auditDenyAids) {
                if (auditDenyAids != null && auditDenyAids.getData() != null && auditDenyAids.getData().size() > 0) {
                    mAuditDenyAidsList = auditDenyAids.getData();
                }
            }

            @Override
            public void onError(Throwable throwable) {

            }

            @Override
            public void onComplete() {

            }
        });
    }

    private void showTitle(TextView tv, PlayHistory playHistory) {
        if (playHistory.getDataType() == 2) {
            if (!TextUtils.isEmpty(playHistory.getEpisode())) {
                tv.setText(playHistory.getEpisode());
            } else {
                tv.setText(playHistory.getTvName());
            }
        } else {
            if (playHistory.getCategoryCode() == 106) {
                if (!TextUtils.isEmpty(playHistory.getEpisode())) {
                    tv.setText(playHistory.getEpisode());
                } else {
                    tv.setText(playHistory.getTvName());
                }
            } else {
                if (!TextUtils.isEmpty(playHistory.getTvName())) {
                    tv.setText(playHistory.getTvName());
                } else {
                    tv.setText(playHistory.getEpisode());
                }
            }
        }
    }

    private String getSubtitle(PlayHistory playHistory) {
        String subtitle = "";
        if (playHistory != null) {
            int watchTime = playHistory.getWatchTime() == null ? 0 : playHistory.getWatchTime();
            int tvLength = playHistory.getTvLength() == null ? 0 : playHistory.getTvLength();
            int order = playHistory.getVideoOrder() == null ? 0 : playHistory.getVideoOrder();
            int sets = playHistory.getTvSets() == null ? 0 : playHistory.getTvSets();
            if (watchTime == 0) {
                if (order > 0) {
                    subtitle = getContext().getResources().getString(R.string.txt_activity_user_related_order_current)
                            + order
                            + (playHistory.getCategoryCode() == 106
                            ? getContext().getResources().getString(R.string.txt_activity_user_related_term_suf)
                            : getContext().getResources().getString(R.string.txt_activity_user_related_set_suf))
                            + getContext().getResources().getString(R.string.txt_fragment_history_record_done);
                } else {
                    subtitle = getContext().getResources().getString(R.string.txt_fragment_history_record_done);
                }
            } else {
                float percent = watchTime * 1f / tvLength;
                if (percent < 0.01f) {
                    if (order > 0) {
                        subtitle = getContext().getResources().getString(R.string.txt_fragment_history_record_more_one)
                                + order
                                + (playHistory.getCategoryCode() == 106
                                ? getContext().getResources().getString(R.string.txt_activity_user_related_term_suf)
                                : getContext().getResources().getString(R.string.txt_activity_user_related_set_suf))
                                +
                                getContext().getResources().getString(R.string.txt_fragment_history_record_less_one);
                    } else {
                        subtitle = getContext().getResources().getString(R.string.txt_fragment_history_record_less_one);
                    }

                } else {
                    if (order > 0) {
                        subtitle = getContext().getResources().getString(R.string.txt_fragment_history_record_more_one)
                                + order
                                + (playHistory.getCategoryCode() == 106
                                ? getContext().getResources().getString(R.string.txt_activity_user_related_term_suf)
                                : getContext().getResources().getString(R.string.txt_activity_user_related_set_suf))
                                + Math.round(percent * 100) + "%";
                    } else {
                        subtitle = getContext().getResources().getString(R.string.txt_fragment_history_record_more_one)
                                + Math.round(percent * 100) + "%";
                    }
                }
            }
        }
        return subtitle;
    }

    private String genTimeString(int watchTime) {
        int hour = watchTime / 3600;
        int minute = watchTime / 60 % 60;
        int second = watchTime % 60;
        StringBuilder stringBuilder = new StringBuilder();
        if (hour > 0) {
            stringBuilder.append(hour).append("小时");
        }
        if (minute > 0) {
            stringBuilder.append(minute).append("分钟");
        }
        if (second > 0) {
            stringBuilder.append(second).append("秒");
        }
        return stringBuilder.toString();
    }

    @Override
    public void onClick(View v) {
        if (!NetworkUtils.isConnected(getContext())) {
            ActivityLauncher.startNetworkDialogActivity(getContext());
            return;
        }
        if (v.equals(this) || v.equals(historyLL3)) {
            if (!mHelper.getIsLogin()) {
                RequestManager.getInstance().onAllEvent(new EventInfo(10136, "clk"), pathInfo, null, null);

                ActivityLauncher.startLoginActivity(getContext(), Constant.LAUNCHER_SOURCE, Integer.parseInt(pathInfo.get("pageId")));
            } else {
                RequestManager.getInstance().onAllEvent(new EventInfo(10138, "clk"), pathInfo, null, null);

                ActivityLauncher.startListUserRelatedActivity(getContext(), ListUserRelatedActivity.LIST_INDEX_HISTORY);
                RequestManager.getInstance().onClickHistory(mChannelId);
            }
//            ActivityLauncher.startHistoryFavorListActivity(getContext(), mChannelId);
        } else if (v.equals(historyLL1)) {
            if (mPlayHistoryList != null) {
                HashMap<String, String> objectInfo = new HashMap<>();
                objectInfo.put("type", "视频");
                objectInfo.put("vid", StringUtil.toString(mPlayHistoryList.get(0).getVideoId()));
                objectInfo.put("playlistId", StringUtil.toString(mPlayHistoryList.get(0).getAlbumId()));
                RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), pathInfo, null, null);

                if (mAuditDenyAidsList != null) {
                    for (int i = 0; i < mAuditDenyAidsList.size(); i++) {
                        PlayHistory playHistory = mPlayHistoryList.get(0);
                        AuditDenyAids.DataBean dataBean = mAuditDenyAidsList.get(i);
                        if (playHistory.getDataType() == 0) {
                            if (dataBean.getAid().equals(StringUtil.toString(playHistory.getAlbumId())) &&
                                    dataBean.getVid().equals(StringUtil.toString(playHistory.getVideoId()))) {
                                ToastUtils.showToast(getContext(), "该片已下线，请观看其他影片。");
                            } else {
                                jumpDetailActivity(mPlayHistoryList.get(0), 0);
                            }
                        } else {
                            if (dataBean.getVid().equals(StringUtil.toString(playHistory.getVideoId()))) {
                                ToastUtils.showToast(getContext(), "该片已下线，请观看其他影片。");
                            } else {
                                jumpDetailActivity(mPlayHistoryList.get(0), 0);
                            }
                        }
                    }
                } else {
                    jumpDetailActivity(mPlayHistoryList.get(0), 0);
                }
            }
        }
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT
                    || (v.equals(historyLL1) && keyCode == KeyEvent.KEYCODE_DPAD_UP)
                    || (v.equals(historyLL3) && keyCode == KeyEvent.KEYCODE_DPAD_DOWN)
                    || (v.equals(this) && keyCode == KeyEvent.KEYCODE_DPAD_DOWN)
                    || (v.equals(this) && keyCode == KeyEvent.KEYCODE_DPAD_UP)) {
//                scaleView(1);
            }
        }
        return false;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (!v.equals(this)) {
            mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
        }
        if (hasFocus) {
//            if(getScaleX() == 1.0) {
//            ViewCompat.setElevation(historyLL1, 1);
//            scaleView(FocusUtil.HOME_SCALE);
//                ObjectAnimator focusAnimator = ObjectAnimator.ofFloat(historyLL1, "ScaleUp",
//                        new float[]{1f, FocusUtil.HOME_SCALE}).setDuration(FocusUtil.FOCUS_ANIM_TIME);
//                focusAnimator.start();
//            }
            if (v.equals(historyLL1)) {
                setTVOnFocus(titleTV1);
                setTVOnFocus(subTitleTV1);
            }
        } else {

//            ObjectAnimator focusAnimator = ObjectAnimator.ofFloat(historyLL1, "ScaleDown",
//                    new float[]{FocusUtil.HOME_SCALE, 1f}).setDuration(FocusUtil.FOCUS_ANIM_TIME);
//            focusAnimator.start();
            if (v.equals(historyLL1)) {
                setTVUnFocus(titleTV1);
                setTVUnFocus(subTitleTV1);
            }
        }
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

    private void jumpDetailActivity(PlayHistory playHistory, int pos) {
        if (playHistory != null) {
            int dataType = playHistory.getDataType();
            int aid = dataType == 0 ? playHistory.getAlbumId() : playHistory.getVideoId();
            int vid = dataType == 0 ? playHistory.getVideoId() : playHistory.getAlbumId();
            int secondCategoryCode = dataType == 0 ? 0 : playHistory.getSecondCategoryCode();
            ActivityLauncher.startVideoDetailDts(getContext(), Constant.PAGE_HOME_HISTORY ,aid, vid, dataType, false, secondCategoryCode);

            RequestManager.getInstance().onClickHistoryItem(mChannelId, aid, pos + 1);
        }
    }

    private void scaleView(float scale) {
        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(this, "scaleX", getScaleX(),
                scale).setDuration(FocusUtil.FOCUS_ANIM_TIME);
        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(this, "scaleY", getScaleY(),
                scale).setDuration(FocusUtil.FOCUS_ANIM_TIME);
        AnimatorSet set = new AnimatorSet();
        set.playTogether(scalexAnimator, scaleyAnimator);
        set.start();
    }

    public void setFocusBorderView(FocusBorderView focusView) {
//        mFocusBorderView = focusView;
    }

}
