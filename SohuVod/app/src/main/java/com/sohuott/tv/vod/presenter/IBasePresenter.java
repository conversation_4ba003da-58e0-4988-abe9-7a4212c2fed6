package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.model.IBaseModel;

/**
 * <AUTHOR>
 * @Date Created on 2020/1/10.
 */
public abstract class IBasePresenter {

    public interface IDataListener<T extends IBaseModel> {
        void getData(T data);
    }

    IDataListener mListener;

    IBasePresenter(IDataListener listener) {
        this.mListener = listener;
    }

    abstract void loadData();

    public void release() {
        this.mListener = null;
    }

}
