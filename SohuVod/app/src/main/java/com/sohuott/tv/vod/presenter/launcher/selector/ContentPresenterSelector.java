package com.sohuott.tv.vod.presenter.launcher.selector;



import androidx.leanback.widget.ListRow;

import com.sohuott.tv.vod.activity.configuration.ConfigurationPresenter;
import com.sohuott.tv.vod.base.BasePresenterSelector;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.model.Footer;
import com.sohuott.tv.vod.model.Header;
import com.sohuott.tv.vod.model.VipBanner;
import com.sohuott.tv.vod.model.VipHeader;
import com.sohuott.tv.vod.model.VipUserState;
import com.sohuott.tv.vod.presenter.launcher.HistoryPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeComingContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeFooterPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeFourContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeHeaderPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeNewFilmPresenterKt;
import com.sohuott.tv.vod.presenter.launcher.TypeOneContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeProducerPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeRecommendContentPresenterKt;
import com.sohuott.tv.vod.presenter.launcher.TypeThreeContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeTwoContentPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeVipHeaderPresenter;
import com.sohuott.tv.vod.presenter.launcher.TypeZeroContentPresenterKt;
import com.sohuott.tv.vod.presenter.launcher.VipBannerPresenter;
import com.sohuott.tv.vod.presenter.launcher.VipUserPresenter;
import com.sohuott.tv.vod.presenter.launcher.row.TypeZeroListRowPresenter;

public class ContentPresenterSelector extends BasePresenterSelector {
    public ContentPresenterSelector() {


//        ContentListRowPresenter listRowPresenter = new ContentListRowPresenter();
//        listRowPresenter.setShadowEnabled(false);
//        listRowPresenter.setSelectEffectEnabled(false);
//        listRowPresenter.setKeepChildForeground(false);
//        listRowPresenter.enableChildRoundedCorners(true);

        TypeZeroListRowPresenter listRowPresenterOne = new TypeZeroListRowPresenter();
        listRowPresenterOne.setShadowEnabled(false);
        listRowPresenterOne.setSelectEffectEnabled(false);
        listRowPresenterOne.setKeepChildForeground(false);
        listRowPresenterOne.enableChildRoundedCorners(false);

        addClassPresenter(ListRow.class, listRowPresenterOne, TypeZeroContentPresenterKt.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, TypeNewFilmPresenterKt.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, TypeOneContentPresenter.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, TypeTwoContentPresenter.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, TypeThreeContentPresenter.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, TypeFourContentPresenter.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, TypeComingContentPresenter.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, TypeRecommendContentPresenterKt.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, TypeProducerPresenter.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, VipBannerPresenter.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, VipUserPresenter.class);
        addClassPresenter(ListRow.class, listRowPresenterOne, ConfigurationPresenter.class);


//        TypeFiveListRowPresenter listRowPresenterFive = new TypeFiveListRowPresenter();
//        listRowPresenterFive.setShadowEnabled(false);
//        listRowPresenterFive.setSelectEffectEnabled(false);
//        listRowPresenterFive.setKeepChildForeground(false);
//        listRowPresenterFive.setHeaderPresenter(new ImageRowHeaderPresenter());

//        addClassPresenter(ListRow.class, listRowPresenterFive, TypeFiveContentPresenter.class);
//        addClassPresenter(ListRow.class, listRowPresenterOne, TypeSixContentPresenter.class);

        addClassPresenter(Footer.class, new TypeFooterPresenter());

        addClassPresenter(Header.class, new TypeHeaderPresenter());

        addClassPresenter(VipHeader.class, new TypeVipHeaderPresenter());

//        addClassPresenter(NewFilm.class, new TypeNewFilmPresenter());

        addClassPresenter(PlayHistory.class, new HistoryPresenter());

        addClassPresenter(VipBanner.class, new VipBannerPresenter());

        addClassPresenter(VipUserState.class, new VipUserPresenter());
//
//        addClassPresenter(TypeSeven.class, new TypeSevenPresenter());

    }

}
