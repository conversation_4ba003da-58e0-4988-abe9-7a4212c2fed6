package com.sohuott.tv.vod.videodetail.activity.state

import android.os.Parcelable
import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel
import com.sohuott.tv.vod.lib.model.ContentGroup
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.videodetail.activity.LogoInfo
import kotlinx.parcelize.Parcelize

/**
 * 影片信息
 */
@Parcelize
data class VideoInfoResponse(
    val id: Int? = 0,
    //影片名称
    var videoName: String? = null,
    var videoMaxLine: Int = 1,
    var videoLength: Int? = 0,
    //简介
    var dec: String? = null,
    //简介最大行数
    var decMaxLine: Int = 2,
    //是否显示 vip 标签
    var isShowVip: Boolean = false,
    //评分
    var score: String? = null,
    //由genreName、year、类型组成
    var label: String? = null,

    var isTeenager: Boolean = false,


    var cateCode: Int = 0,
    var videoOrder: Int = 0,

    var actors: String? = null,
    var serviceVid: Int = 0,
    var tvAreaId: Int = 0,
    var trailerId: Int = 0,
    var sortOrder: Int = 0,
    var tvIsEarly: Int = 0,
    var albumEpisodeType: Int = 0,
    var tvIsIntrest: Int = 0,
    var isShowTitle: Int = 0,
    var videoCount: Int = 0,
    var trailerCount: Int = 0,
    var episodePlayingVideoOrder: Int = 0,
    //mAlbumInfo.data.hasTrailer
    var hasTrailer: Boolean = false,
    var updateNotification: String? = null,
    var tvSets: String? = null,
    var maxVideoOrder: String? = null,
    //mAlbumInfo.extend.albumSeries
    var albumSeries: MutableList<ContentGroup.DataBean.ContentsBean.AlbumListBean?>? = null,
    var recommendList: MutableList<AlbumInfoRecommendModel>? = null,
    var isExtendNull: Boolean = false,
    var latestVideoCount: String? = "",
    var hasEpisode: Boolean = false,
    var firstDesc: String? = "",
    var secondDesc: String? = "",
    var recordNumber: String? = "",

    var ottFee: Int = 0,
    var useTicket: Int = 0,
    var paySeparate: Int = 0,
    var isAudit: Int = 0,
    var cateCodeSecond: Int = 0,
    var tvIsFee: Int = 0,
    var cornerType: Int = 0,
    var tvVerPic: String? = null,
    var albumExtendsPic_640_360: String? = null,
    var copyRightTips: String? = null,
    var pgclogoInfo: LogoInfo? = null
) : Parcelable