package com.sohuott.tv.vod.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.KeyEvent;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by rita on 16-2-2.
 */
public class CustomLinearRecyclerView extends RecyclerView {

    private static final long DEFAULT_INTERVAL_TIME = 250;

    private long lastTimeMillis;
    private long mIntervalTime = DEFAULT_INTERVAL_TIME;

    public CustomLinearRecyclerView(Context context) {
        super(context);
    }

    public CustomLinearRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomLinearRecyclerView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return super.dispatchKeyEvent(event) || executeKey();
    }

    private boolean executeKey() {
        return getScrollState() != SCROLL_STATE_IDLE;
    }

    public long getIntervalTime() {
        return mIntervalTime;
    }

    public void setIntervalTime(long mIntervalTime) {
        this.mIntervalTime = mIntervalTime;
    }

}
