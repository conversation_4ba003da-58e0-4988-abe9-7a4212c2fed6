package com.sohuott.tv.vod.activity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.SearchResultAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.model.SearchResult;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.SearchResultContract;
import com.sohuott.tv.vod.presenter.SearchResultPresenter;
import com.sohuott.tv.vod.ui.SearchResultRecyclerView;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.SearchResultGridLayoutManager;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Created by fenglei on 17-6-21.
 */
public class SearchResultActivity extends BaseActivity implements
        SearchResultContract.SearchResultView, View.OnKeyListener,View.OnTouchListener {

    private RelativeLayout rootView;
    private LoadingView loadingView;
    private LinearLayout searchNoResultLayout;
    protected FocusBorderView focusBorderView;
    private LinearLayout btnLL;
    private List<Button> buttonList = new ArrayList<>();
    //    private Button fullBtn;
//    private Button movieBtn;
//    private Button serialBtn;
//    private Button varietyBtn;
//    private Button documentaryBtn;
//    private Button comicBtn;
//    private Button pgcBtn;
    private SearchResultRecyclerView searchResultRecyclerView;

    protected SearchResultContract.Presenter searchResultPresenter;

    private SearchResultAdapter searchResultAdapter;
    private SearchResultGridLayoutManager gridLayoutManager;

    private String query;

    private boolean isFirstRequest = true;

    protected void setCustomUI(){
        setContentView(R.layout.activity_search_result_layout);
        focusBorderView = (FocusBorderView) findViewById(R.id.focus_border_view);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setCustomPageName();
        setCustomUI();
        rootView = (RelativeLayout) findViewById(R.id.root);
        loadingView = (LoadingView) findViewById(R.id.loadingView);
        searchNoResultLayout = (LinearLayout) findViewById(R.id.searchNoResultLayout);

        btnLL = (LinearLayout) findViewById(R.id.btnLL);
        initBtnList();

        searchResultRecyclerView = (SearchResultRecyclerView) findViewById(R.id.searchResultRecyclerView);
        searchResultRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        gridLayoutManager = new SearchResultGridLayoutManager(this, SearchResultAdapter.SPAN_COUNT);
        gridLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        searchResultRecyclerView.setLayoutManager(gridLayoutManager);
        searchResultRecyclerView.addItemDecoration(new SearchResultAdapter.SearchResultItemDecoration());
        searchResultRecyclerView.setHasFixedSize(true);
        searchResultPresenter = new SearchResultPresenter(this);
        rootView.getViewTreeObserver().addOnGlobalFocusChangeListener(new ViewTreeObserver.OnGlobalFocusChangeListener() {
            @Override
            public void onGlobalFocusChanged(View oldFocus, View newFocus) {
                LibDeprecatedLogger.d("onGlobalFocusChanged, oldFocus = " + oldFocus + ", newFocus = " + newFocus);
                if (newFocus instanceof Button) {
                    if (newFocus.isSelected()) {
                        newFocus.setSelected(false);
                    } else {
                        showLoadingView();
                        if (oldFocus instanceof Button) {
                            searchResultPresenter.cancel((int) oldFocus.getTag());
                        }
                        //searchResultPresenter.getSearchResult(query, (int) newFocus.getTag(), 1, SearchResultAdapter.PAGE_SIZE);
                        getSearchResult(query, (int) newFocus.getTag(), 1, SearchResultAdapter.PAGE_SIZE);
                        if (searchResultAdapter != null) {
                            searchResultAdapter.resetLastFocusedView();
                        }
                        RequestManager.getInstance().onSelectSearchResultButtonEvent(mPageName,
                                (int) newFocus.getTag(), ((Button) newFocus).getText().toString());
                    }
                }
            }
        });
//        searchResultRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
//            @Override
//            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
//                Logger.d(TAG, "onScrolled, dy = " + dy);
//            }
//
//            @Override
//            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
//                Logger.d(TAG, "onScrollStateChanged, newState = " + newState);
//                if (newState != RecyclerView.SCROLL_STATE_IDLE) {
////                    focusBorderView.setUnFocusView(getCurrentFocus());
//                } else {
//                    View focusedView = getCurrentFocus();
//                    focusBorderView.setFocusView(focusedView);
//                }
//            }
//        });
        startSearchRequest();
        RequestManager.getInstance().onSearchResultExposureEvent(mPageName);
        RequestManager.getInstance().onSelectSearchResultButtonEvent(mPageName,0, "全部");
        //setPageName("6_search_result");
    }

    protected void setCustomPageName(){
        setPageName("6_search_result");
    }

    public void getSearchResult(String query, int type, int page, int pageSize){
        if (searchResultPresenter!=null)
            searchResultPresenter.getSearchResult(query, type, page, pageSize);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        startSearchRequest();
    }

    public void setLeftBtnTextColor(Button button){
        if(!Util.isSupportTouchVersion(this)){
            button.setTextColor(getResources().getColor(R.color.search_result_tab_item_color));
        }else{
            button.setBackgroundResource(R.drawable.bg_item_grid_left_selector_ontouch);
        }
    }

    private void initBtnList() {
        for (int i = 0; i < btnLL.getChildCount(); i++) {
            if (btnLL.getChildAt(i) instanceof Button) {
                Button button = (Button) btnLL.getChildAt(i);
                buttonList.add(button);
                setLeftBtnTextColor(button);
                button.setOnKeyListener(this);
                button.setOnTouchListener(this);
            }
        }
    }

    private void resetBtnList() {
        for (int i = 0; i < buttonList.size(); i++) {
            if (i != 0) {
                buttonList.get(i).setVisibility(View.GONE);
            } else {
                buttonList.get(i).setTag(0);
                buttonList.get(i).setText("全部");
            }
        }
    }

    private void startSearchRequest() {
        isFirstRequest = true;
        buttonList.get(0).requestFocus();
        mLabelBtn=buttonList.get(0);
        mLabelBtn.setPressed(true);
        resetBtnList();
        showLoadingView();
        if(getIntent().getData() != null){
            Uri uri = getIntent().getData();
            query = uri.getQueryParameter(ParamConstant.PARAM_SEARCH_TXT);
        }else {
            query = getIntent().getStringExtra(ParamConstant.PARAM_SEARCH_TXT);
        }
        //searchResultPresenter.getSearchResult(query, SearchResultContract.TYPE_WHOLE, 1, SearchResultAdapter.PAGE_SIZE,1);
        getSearchResult(query, SearchResultContract.TYPE_WHOLE, 1, SearchResultAdapter.PAGE_SIZE);
    }

    public SearchResultAdapter creatSearchResultAdapter(Context context, SearchResultRecyclerView recyclerView,
                                                        GridLayoutManager gridLayoutManager, SearchResult.DataBean dataBean, int type){
        return new SearchResultAdapter(context, recyclerView, gridLayoutManager, dataBean, type);
    }

    @Override
    public void showSearchResult(SearchResult.DataBean dataBean, int type) {
        setBtnListWithData(dataBean, type);
        View focusedView = getCurrentFocus();
        LibDeprecatedLogger.d("showSearchResult, focusedView = " + focusedView);
        if (focusedView != null && focusedView instanceof Button) {
            LibDeprecatedLogger.d("showSearchResult, focusedView type = " + focusedView.getTag() + ", type = " + type);
            if (focusedView.getTag() == null || type != (int) focusedView.getTag()) {
                return;
            }
        }
        hideLoadingView();
        if (dataBean == null || dataBean.getItems() == null || dataBean.getItems().size() == 0) {
            LibDeprecatedLogger.d("showSearchResult1");
            searchNoResultLayout.setVisibility(View.VISIBLE);
            searchResultRecyclerView.setVisibility(View.GONE);
        } else if (searchResultAdapter == null) {
            LibDeprecatedLogger.d("showSearchResult2");
            searchNoResultLayout.setVisibility(View.GONE);
            searchResultRecyclerView.setVisibility(View.VISIBLE);
            searchResultAdapter = creatSearchResultAdapter(this, searchResultRecyclerView, gridLayoutManager, dataBean, type);
            searchResultAdapter.setPageName(mPageName);
            searchResultAdapter.setFocusBorderView(focusBorderView);
            gridLayoutManager.setSpanSizeLookup(searchResultAdapter.new SearchResultSpinSizeLookUp());
            searchResultAdapter.setQuery(query);
            searchResultRecyclerView.setAdapter(searchResultAdapter);
            searchResultRecyclerView.clearOnScrollListeners();
            searchResultRecyclerView.addOnScrollListener(searchResultAdapter.new SearchScrollListener());
        } else {
            LibDeprecatedLogger.d("showSearchResult3");
            searchNoResultLayout.setVisibility(View.GONE);
            searchResultRecyclerView.setVisibility(View.VISIBLE);
            searchResultAdapter.setData(dataBean, type, 1, query);
            searchResultAdapter.notifyDataSetChanged();
            if(searchResultRecyclerView != null){
                searchResultRecyclerView.smoothScrollToPosition(0);
            }
        }
    }

    private void setBtnListWithData(SearchResult.DataBean dataBean, int type) {
        if (isFirstRequest && type == SearchResultContract.TYPE_WHOLE) {
            if (dataBean != null && dataBean.getItems() != null && dataBean.getItems().size() != 0
                    && dataBean.getCategories() != null && dataBean.getCategories().size() > 0) {
                List<SearchResult.DataBean.CategoriesBean> categoriesBeanList = dataBean.getCategories();
                Collections.sort(categoriesBeanList, new Comparator<SearchResult.DataBean.CategoriesBean>() {
                    @Override
                    public int compare(SearchResult.DataBean.CategoriesBean o1, SearchResult.DataBean.CategoriesBean o2) {
                        if (o1.getType() == SearchResultContract.TYPE_WHOLE) {
                            return 1;
                        }
                        return 0;
                    }
                });
                int size = Math.min(categoriesBeanList.size(), buttonList.size());
                for (int i = 0; i < size; i++) {
                    SearchResult.DataBean.CategoriesBean categoriesBean = categoriesBeanList.get(i);
                    if (categoriesBean != null) {
                        buttonList.get(i).setText(categoriesBean.getLabel());
                        buttonList.get(i).setTag(categoriesBean.getType());
                        buttonList.get(i).setVisibility(View.VISIBLE);
                        if(Util.isSupportTouchVersion(this)
                        &&categoriesBean.getType()==0){
                            buttonList.get(i).setSelected(true);
                        }
                    }
                }
                for (int i = size; i < buttonList.size(); i++) {
                    buttonList.get(i).setVisibility(View.GONE);
                }
            }
        }
        isFirstRequest = false;
    }

    public void updateBtnListWithData(SearchResult.DataBean dataBean) {
        if (dataBean != null && dataBean.getItems() != null && dataBean.getItems().size() != 0
                && dataBean.getCategories() != null && dataBean.getCategories().size() > 0) {
            List<SearchResult.DataBean.CategoriesBean> categoriesBeanList = dataBean.getCategories();
            for (SearchResult.DataBean.CategoriesBean categoriesBean : categoriesBeanList) {
                if (categoriesBean != null) {
                    boolean found = false;
                    int visibleBtnSize = findVisibleBtnSize();
                    for (int i = 0; i < visibleBtnSize; i++) {
                        if ((int) buttonList.get(i).getTag() == categoriesBean.getType()) {
                            found = true;
                            break;
                        }
                    }
                    if (found) {
                        continue;
                    }
                    if (visibleBtnSize < buttonList.size()) {
                        buttonList.get(visibleBtnSize).setText(categoriesBean.getLabel());
                        buttonList.get(visibleBtnSize).setTag(categoriesBean.getType());
                        buttonList.get(visibleBtnSize).setVisibility(View.VISIBLE);
                    }
                }
            }
        }
    }

    private int findVisibleBtnSize() {
        int result = 0;
        if (buttonList != null) {
            for (int i = 0; i < buttonList.size(); i++) {
                if (buttonList.get(i).getVisibility() != View.VISIBLE) {
                    result = i;
                    break;
                }
            }
        }
        return result;
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (searchResultAdapter!=null){
            searchResultAdapter.releaseAll();
            searchResultAdapter = null;
        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (event.getRepeatCount() == 0) {
                lastTimeMillis = System.currentTimeMillis();
            } else {
                if (System.currentTimeMillis() - lastTimeMillis < 300) {
                    return true;
                } else {
                    lastTimeMillis = System.currentTimeMillis();
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    private long lastTimeMillis;

    private View lastFocusedView;

    public boolean leftBtnRequestFocus() {
        if (lastFocusedView != null) {
            btnLL.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
            return lastFocusedView.requestFocus();
        }
        return false;
    }

    private View mLabelBtn;

    /**
     * compat touch event
     * @param v
     * @param event
     * @return
     */
    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if(mLabelBtn.getTag()!=v.getTag()&&event.getAction()==MotionEvent.ACTION_UP){
            v.setSelected(true);
            showLoadingView();
            searchResultPresenter.cancel((int) mLabelBtn.getTag());
            //searchResultPresenter.getSearchResult(query, (int) v.getTag(), 1, SearchResultAdapter.PAGE_SIZE,1);
            getSearchResult(query, (int) v.getTag(), 1, SearchResultAdapter.PAGE_SIZE);
            if (searchResultAdapter != null) {
                searchResultAdapter.resetLastFocusedView();
            }
            RequestManager.getInstance().onSelectSearchResultButtonEvent(
                    mPageName,(int) v.getTag(), ((Button) v).getText().toString());
            mLabelBtn.setSelected(false);
            mLabelBtn=v;
        }
        return true;
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                lastFocusedView = v;
                if (searchResultAdapter != null && searchResultAdapter.getItemCount() > 0) {
                    v.setSelected(true);
                    btnLL.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
                    return searchResultAdapter.rightKeyRequestFocus();
                } else {
                    return true;
                }
            }
        }
        return false;
    }

    private void showLoadingView() {
        LibDeprecatedLogger.d("showLoadingView");
        loadingView.show();
        searchResultRecyclerView.setVisibility(View.GONE);
        searchNoResultLayout.setVisibility(View.GONE);
//        Logger.d(TAG, "showLoadingView");
//        loadingView.removeCallbacks(loadingViewRunnable);
//        loadingView.postDelayed(loadingViewRunnable, 500);
    }

    private void hideLoadingView() {
        LibDeprecatedLogger.d("hideLoadingView");
//        loadingView.removeCallbacks(loadingViewRunnable);
        loadingView.hide();
    }

//    private LoadingViewRunnable loadingViewRunnable = new LoadingViewRunnable();

    private static class LoadingViewRunnable implements Runnable {
        WeakReference<SearchResultActivity> mWrapper;
        LoadingViewRunnable(SearchResultActivity activity){
            mWrapper = new WeakReference<>(activity);
        }
        @Override
        public void run() {
            SearchResultActivity searchResultActivity = mWrapper.get();
            if (searchResultActivity == null){
                return;
            }
            searchResultActivity.loadingView.show();
            searchResultActivity.searchResultRecyclerView.setVisibility(View.GONE);
            searchResultActivity.searchNoResultLayout.setVisibility(View.GONE);
        }
    }

    private String searchTypeToString(int type) {
        String result = "";
        switch (type) {
            case SearchResultContract.TYPE_WHOLE:
                result = "全部";
                break;
            case SearchResultContract.TYPE_MOVIE:
                result = "电影";
                break;
            case SearchResultContract.TYPE_SERIAL:
                result = "电视剧";
                break;
            case SearchResultContract.TYPE_VARIETY:
                result = "综艺";
                break;
            case SearchResultContract.TYPE_DOCUMENTARY:
                result = "纪录片";
                break;
            case SearchResultContract.TYPE_COMIC:
                result = "动漫";
                break;
            case SearchResultContract.TYPE_PGC:
                result = "自媒体";
                break;
            default:
                break;
        }
        return result;
    }

}
