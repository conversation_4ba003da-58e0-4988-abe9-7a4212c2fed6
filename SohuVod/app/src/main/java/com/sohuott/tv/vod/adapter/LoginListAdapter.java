package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.view.KeyEvent;
import android.view.View;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.rvhelper.BaseQuickAdapter;
import com.sohuott.tv.vod.lib.rvhelper.BaseViewHolder;
import com.sohuott.tv.vod.lib.utils.Constant;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017-03-28.
 *
 *
 * @update pengbignhan
 */
public class LoginListAdapter extends BaseQuickAdapter<Object, BaseViewHolder> {

    private static final int COUNT = 2;
    public static final int POSITION_ZERO = 0; // scan
    public static final int POSITION_FIRST = 1;  // key input
//    public static final int POSITION_SECOND = 2; // register
    private Context mContext;

    public LoginListAdapter(Context context) {
        super(R.layout.login_list_item);
        mContext = context;
    }

    @Override
    public int getItemCount() {
        return COUNT;
    }

    @Override
    protected void convert(final BaseViewHolder helper, Object item) {
        switch (helper.getAdapterPosition()) {
            case POSITION_ZERO:
                helper.setText(R.id.name, "扫码登录");
                helper.setText(R.id.desc, "支持手机号 | 账号 | 微信 | QQ | 微博登录");
                helper.setImageResource(R.id.image, R.drawable.ic_login_scan);
                break;
            case POSITION_FIRST:
                helper.setText(R.id.name, "手机验证码登录");
                helper.setText(R.id.desc, "遥控器输入手机号验证码");
                helper.setImageResource(R.id.image, R.drawable.ic_login_phone_new);
                break;
//            case POSITION_SECOND:
//                helper.setText(R.id.name, R.string.txt_activity_login_register_title);
//                helper.setImageResource(R.id.image, R.drawable.ic_login_register_default);
//                break;
            default:
                break;
        }
        helper.itemView.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent event) {
                int position = helper.getAdapterPosition();

                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT) {
                        if (position == Constant.POSITION_FIRST) {
                            helper.itemView.setNextFocusRightId(R.id.login_user_name_btn);
                        }
                    } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                        if (position == Constant.POSITION_ZERO) {
//                            helper.itemView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.out_to_up));
                            helper.itemView.setNextFocusUpId(R.id.root_view);
                        }

                    } else if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
                        if (position == (COUNT - 1)) {
//                            helper.itemView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.out_to_up));
                            helper.itemView.setNextFocusDownId(R.id.root_view);
                        }
                    }
                }
                return false;
            }
        });
    }

}
