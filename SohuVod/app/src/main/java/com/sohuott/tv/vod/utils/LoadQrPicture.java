package com.sohuott.tv.vod.utils;

import android.app.Activity;
import android.content.Context;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.lib_statistical.manager.RequestManager;
import com.sohu.ott.base.lib_user.HeaderHelper;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.common.AccountService;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.LoginQrModel;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.widget.CornerTagImageView;

import java.io.IOException;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.observers.DisposableObserver;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by yizhang210244 on 2018/3/29.
 */

public class LoadQrPicture {
    private static final String TAG = LoadQrPicture.class.getSimpleName();
    private Consumer<LoginQrModel> mConsumer;
    private long mPaySourceComeFrom = PayActivity.PAY_SOURCE_UNKNOWN;
    private LoginUserInformationHelper mHelper;
    private Context mContext;
    DisposableObserver mDisposableObserver;

    public LoadQrPicture(Context context, Consumer<LoginQrModel> consumer) {
        mContext = context;
        mConsumer = consumer;
        mHelper = LoginUserInformationHelper.getHelper(mContext.getApplicationContext());
    }

    public void setPaySourceComeFrom(long paySourceComeFrom) {
        mPaySourceComeFrom = paySourceComeFrom;
    }

    public void getPicture(final String uri, final ImageView imageView) {
        Map<String, String> map = HeaderHelper.getHeaders();
        long channeled = mPaySourceComeFrom + 1000;
        if (!HomeData.sBootOrHomeIsStarted) {
            String enterId = RequestManager.getInstance().getEnterId();
            if (!TextUtils.isEmpty(enterId)) {
                enterId = enterId.substring(0, 1);
                if (TextUtils.equals(enterId, "1")) {
                    channeled = mPaySourceComeFrom + 2000;
                }
            }
        }
        map.put("channeled", String.valueOf(channeled));
        map.put("loginType", String.valueOf(Util.getServerLoginUtype(mHelper.getUtype())));
        new AccountService(mContext).getQrImage(map, uri, new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                // 如果请求成功，将ResponseBody转化为InputStream，然后使用Glide加载图片
                byte[] inputStream = null;
                try {
                    inputStream = response.body().bytes();
                    if (mContext != null && mContext instanceof Activity) {
                        Activity activity = (Activity) mContext;
                        if (activity.isFinishing() || activity.isDestroyed()) {
                            return;
                        }
                    }
                    try {
                        Glide.with(mContext)
                                .load(inputStream)
                                .into(imageView);  // yourImageView是你要展示图片的ImageView
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    if (imageView instanceof CornerTagImageView && imageView.getId() == R.id.renew_qrcode_image) {
                        AppLogger.d(TAG, "R.id.renew_qrcode_image");
                        if (mContext instanceof Activity) {
                            Activity activity = (Activity) mContext;
                            if (activity.isFinishing() || activity.isDestroyed()) {
                                return;
                            }
                        }
                        try {
                            Glide.with(mContext).load(inputStream).transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                                    .into(imageView);
                        }catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        imageView.setImageBitmap(BitmapFactory.decodeByteArray(inputStream, 0, inputStream.length));
                    }

                    LoginQrModel qrModel = new LoginQrModel();
                    qrModel.setToken(response.headers().get("code"));
                    qrModel.setQrcode(response.headers().get("qrcode"));
                    LibDeprecatedLogger.d("qrcode:" + qrModel.getQrcode() + " token:" + qrModel.getToken());
                    Observable.just(qrModel)
                        .subscribeOn(AndroidSchedulers.mainThread())
                        .subscribe(mConsumer);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                LibDeprecatedLogger.d("onFailure:" + t.getMessage());
            }
        });

//        Observable observable = Observable.create(new ObservableOnSubscribe<QrDataModel>() {
//            @Override
//            public void subscribe(ObservableEmitter<QrDataModel> emitter) {
//                try {
//                    Map<String, String> map = Util.getHeaders(mContext.getApplicationContext());
//                    long channeled = mPaySourceComeFrom + 1000;
//                    if (!HomeData.sBootOrHomeIsStarted) {
//                        String enterId = RequestManager.getInstance().getEnterId();
//                        if (!TextUtils.isEmpty(enterId)) {
//                            enterId = enterId.substring(0, 1);
//                            if (TextUtils.equals(enterId, "1")) {
//                                channeled = mPaySourceComeFrom + 2000;
//                            }
//                        }
//                    }
//                    map.put("channeled", String.valueOf(channeled));
//                    map.put("loginType", String.valueOf(Util.getServerLoginUtype(mHelper.getUtype())));
//
//                    OkHttpClient client = new OkHttpClient.Builder().connectionSpecs(Arrays.asList(ConnectionSpec.MODERN_TLS,
//                                    ConnectionSpec.COMPATIBLE_TLS, ConnectionSpec.CLEARTEXT))
//                            .sslSocketFactory(TrustAll.INSTANCE.socketFactory(), TrustAll.INSTANCE.systemDefaultTrustManager())
//                            .hostnameVerifier(new TrustAll.hostnameVerifier()).build();
//                    Request request = new Request.Builder().url(uri).
//                            headers(Headers.of(map)).build();
//
//                    okhttp3.Response response = client.newCall(request).execute();
//                    if (response != null) {
//                        QrDataModel qrDataModel = new QrDataModel();
//                        if (response.body() != null) {
//                            byte[] picByte = response.body().bytes();
//                            if (picByte != null) {
//                                qrDataModel.setBitmap(BitmapFactory.decodeByteArray(picByte, 0, picByte.length));
//                                emitter.onNext(qrDataModel);
//                            }
//                        }
//                        if (response.headers() != null && mConsumer != null) {
//                            LoginQrModel qrModel = new LoginQrModel();
//                            qrModel.setToken(response.headers().get("code"));
//                            qrModel.setQrcode(response.headers().get("qrcode"));
//                            AppLogger.d(qrModel.toString());
//                            //String token = response.headers().get("code");
//                            //qrcode = response.headers().get("qrcode");
//                            //AppLogger.d("polling qrcode=" + qrcode);
//                            //AppLogger.d("polling token=" + token);
//                            //if (!TextUtils.isEmpty(token)) {
//                            Observable.just(qrModel)
//                                    .subscribeOn(AndroidSchedulers.mainThread())
//                                    .subscribe(mConsumer);
//                            //}
//                        }
//                    }
//                } catch (IOException e) {
//                    AppLogger.e("Load QR picture fail!", e);
//                    emitter.onError(e);
//                }
//
//            }
//        });
//
//        mDisposableObserver = new DisposableObserver<QrDataModel>() {
//            @Override
//            public void onNext(QrDataModel value) {
//                if (value != null && mContext != null) {
//                    if (imageView instanceof CornerTagImageView && imageView.getId() == R.id.renew_qrcode_image) {
//                        LogManager.d(TAG, "R.id.renew_qrcode_image");
//                        if (mContext instanceof Activity) {
//                            Activity activity = (Activity) mContext;
//                            if (activity.isFinishing() || activity.isDestroyed()) {
//                                return;
//                            }
//                        }
//                        Glide.with(mContext).load(value.getBitmap()).transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
//                                .into(imageView);
//                    } else {
//                        imageView.setImageBitmap(value.getBitmap());
//                    }
//                }
//            }
//
//            @Override
//            public void onError(Throwable e) {
//
//            }
//
//            @Override
//            public void onComplete() {
//
//            }
//        };
//
//        observable.subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .onTerminateDetach()
//                .subscribe(mDisposableObserver);
    }

    public void cancel() {
//        if (!mDisposableObserver.isDisposed()) {
//            mDisposableObserver.dispose();
            mContext = null;
//        }
    }


}
