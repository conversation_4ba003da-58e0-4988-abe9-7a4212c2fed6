package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import androidx.leanback.widget.Presenter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.model.SearchResult;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager;
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.PlayingView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;


public class TypeTwoContentPresenter extends Presenter {
    public Context mContext;
    public MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_two_layout, parent, false);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final Presenter.ViewHolder viewHolder, final Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        vh.view.setOnFocusChangeListener((v, hasFocus) -> {
            mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
            if (hasFocus) {
                if (vh.mErrorLogo != null && vh.mErrorLogo.getVisibility() == View.VISIBLE) {
                    return;
                }
                vh.mFocusRoot.setVisibility(View.VISIBLE);
                vh.mTVFocusEpisode.setVisibility(View.VISIBLE);
                vh.mEpisodeBg.setVisibility(View.VISIBLE);
                vh.mNameBg.setVisibility(View.INVISIBLE);
                vh.mTvTypeTwoName.setVisibility(View.GONE);
                vh.mFocusPlay.setVisibility(View.VISIBLE);
                vh.mFocusPlay.showWaveAnimation();
            } else {
                vh.mFocusRoot.setVisibility(View.GONE);
                vh.mTVFocusEpisode.setVisibility(View.GONE);
                vh.mEpisodeBg.setVisibility(View.GONE);
                vh.mNameBg.setVisibility(View.VISIBLE);
                vh.mTvTypeTwoName.setVisibility(View.VISIBLE);
                vh.mFocusPlay.setVisibility(View.GONE);
                vh.mFocusPlay.cancelWaveAnimation();
            }
        });
        if (item instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean) {
            ContentGroup.DataBean.ContentsBean.AlbumListBean albumBean = (ContentGroup.DataBean.ContentsBean.AlbumListBean) item;
            Glide.with(mContext)
                    .load(albumBean.albumExtendsPic_640_360)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeTwoPoster);
            vh.mTvTypeTwoName.setText(albumBean.tvName);

            vh.mTVFocusEpisode.setText(Util.getHintTV(item));
            vh.mTvFocusName.setText(albumBean.tvName);
            vh.mTvFocusDesc.setText(albumBean.tvComment);



            RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"),
                    albumBean.pathInfo,
                    albumBean.objectInfo, null);

            if (albumBean.channelType == Constant.TYPE_VIP) {
                vh.mFocusRoot.setBackgroundResource(R.drawable.item_type_one_vip_focus_selector);
                vh.mFocusView.setBackgroundResource(R.drawable.bg_vip_focus_selector);
            } else {
                vh.mIvTypeTwoPoster.setCornerTypeWithType(albumBean.tvIsFee,
                        albumBean.tvIsEarly,
                        albumBean.useTicket,
                        albumBean.paySeparate,
                        albumBean.cornerType);
            }
            vh.view.setOnClickListener(view -> ActivityLauncher.startVideoDetailActivity(mContext, albumBean.id, 0, Constant.PAGE_DETAIL));

        }else if (item instanceof VideoDetailRecommendModel.DataBean.ContentsBean) {
            VideoDetailRecommendModel.DataBean.ContentsBean contentsBean = (VideoDetailRecommendModel.DataBean.ContentsBean) item;
            VideoDetailRequestManager.labelExposure(String.valueOf(contentsBean.getIndex() + 1), String.valueOf(contentsBean.getId()), contentsBean.getTagName());
            Glide.with(mContext)
                    .load(contentsBean.getHorPic())
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeTwoPoster);

            vh.mTvTypeTwoName.setText(contentsBean.getName());

            vh.mTVFocusEpisode.setText(Util.getHintTV(item));
            vh.mTvFocusName.setText(contentsBean.getName());
            if (contentsBean.getAlbumParam() != null) {
                vh.mTvFocusDesc.setVisibility(View.VISIBLE);
                vh.mTvFocusDesc.setText(contentsBean.getAlbumParam().getComment());
                vh.mIvTypeTwoPoster.setCornerTypeWithType(Integer.parseInt(contentsBean.getAlbumParam().getTvIsFee()),
                        contentsBean.getAlbumParam().getTvIsEarly(),
                        contentsBean.getAlbumParam().getUseTicket(),
                        contentsBean.getAlbumParam().getPaySeparate(),
                        Integer.parseInt(contentsBean.getAlbumParam().getCornerType()));
            } else {
                vh.mTvFocusDesc.setVisibility(View.GONE);
            }
            vh.view.setOnClickListener(view -> {
                VideoDetailRequestManager.labelClick(String.valueOf(contentsBean.getIndex() + 1), String.valueOf(contentsBean.getId()), contentsBean.getTagName());
                ActivityLauncher.startVideoDetailActivity(mContext, contentsBean.getId(), contentsBean.getCateCode() == 0 ? 2 : 0, 2003);
            });
            //设置PGC角标
            try {
                if (!((VideoDetailRecommendModel.DataBean.ContentsBean) item).getCornerType().isEmpty()) {
                    vh.mIvTypeTwoPoster.setPgcCornerTypeWithType(Integer.parseInt(((VideoDetailRecommendModel.DataBean.ContentsBean) item).getCornerType()));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        } else if(item instanceof EpisodeVideos.Video){
            EpisodeVideos.Video video = (EpisodeVideos.Video) item;
            if (video.type == 1) {
                //推荐
                Glide.with(mContext)
                        .load(video.videoExtendsPic_640_360)
                        .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                        .into(vh.mIvTypeTwoPoster);
                vh.mTvTypeTwoName.setText(video.tvName);
                vh.mTvFocusName.setText(video.tvName);
                vh.mTVFocusEpisode.setVisibility(View.GONE);
                vh.mIvTypeTwoPoster.setCornerTypeRecommend();
            } else {
                //普通
                if (((EpisodeVideos.Video) item).isSelected) {
                    vh.mTvTypeTwoName.setTextColor(mContext.getResources().getColor(R.color.tv_color_ff6247));
                    vh.mPlayingView.show();
                } else {
                    vh.mTvTypeTwoName.setTextColor(mContext.getResources().getColor(R.color.tv_color_e6e8e8ff));
                    vh.mPlayingView.hide();
                }

                vh.mIvTypeTwoPoster.setCornerType(video.tvSetIsFee, video.tvOttIsFee, 0);

                Glide.with(mContext)
                        .load(video.videoExtendsPic_320_180)
                        .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                        .into(vh.mIvTypeTwoPoster);

//            vh.mTVFocusEpisode.setText(Util.getHintTV(item));
                vh.mTvFocusDesc.setText(video.tvDesc);
                if (!"".equals(video.varietyPeriod)) {
                    vh.mTVFocusEpisode.setText(video.varietyPeriod + "期");
                } else {
                    vh.mTVFocusEpisode.setText("");
                }

                if (StringUtil.isEmpty(video.tvName)) {
                    vh.mTvFocusName.setText("数据缺失");
                    vh.mTvTypeTwoName.setText("数据缺失");
                    vh.mErrorLogo.setVisibility(View.VISIBLE);
                } else {
                    vh.mTvFocusName.setText(video.tvName);
                    vh.mTvTypeTwoName.setText(video.tvName);
                    vh.mErrorLogo.setVisibility(View.GONE);
                }
            }

        } else if (item instanceof SearchResult.Data.RelationItem) {
            SearchResult.Data.RelationItem video = (SearchResult.Data.RelationItem) item;
            Glide.with(mContext)
                    .load(video.getAlbumExtendsPic320180())
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeTwoPoster);
            vh.mIvTypeTwoPoster.setCornerTypeWithType(video.getTvIsFee(),
                    video.getTvIsEarly(),
                    video.getUseTicket(),
                    video.getPaySeparate(),
                    video.getCornerType());
            vh.mTvTypeTwoName.setText(video.getTvName());
            vh.mTvFocusName.setText(video.getTvName());
            vh.mTvFocusDesc.setText(video.getTvDesc());
            vh.mTVFocusEpisode.setText(Util.getHint(video.getCateCode(), video.getTvSets(), Integer.parseInt(video.getLatestVideoCount()), video.getShowDate()));
        }
    }


    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {
        ViewHolder vh = (ViewHolder) viewHolder;
        Glide.with(vh.mIvTypeTwoPoster.getContext()).clear(vh.mIvTypeTwoPoster);
    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final CornerTagImageView mIvTypeTwoPoster;
        private final TextView mTvTypeTwoName, mTvFocusName, mTvFocusDesc, mTVFocusEpisode;
        private LinearLayout mFocusRoot;
        private RippleDiffuse mFocusPlay;
        private View mFocusView, mEpisodeBg, mNameBg;
        private PlayingView mPlayingView;
        private ImageView mErrorLogo;

        public ViewHolder(View view) {
            super(view);
            mIvTypeTwoPoster = (CornerTagImageView) view.findViewById(R.id.iv_type_two_poster);
            mTvTypeTwoName = (TextView) view.findViewById(R.id.tv_type_two_name);
            mFocusRoot = (LinearLayout) view.findViewById(R.id.type_two_focus_root);
            mTvFocusName = (TextView) view.findViewById(R.id.type_two_focus_name);
            mTvFocusDesc = (TextView) view.findViewById(R.id.type_two_focus_desc);
            mTVFocusEpisode = (TextView) view.findViewById(R.id.type_two_focus_episode);
            mFocusPlay = (RippleDiffuse) view.findViewById(R.id.type_two_focus_play);
            mFocusView = view.findViewById(R.id.type_two_focus);
            mEpisodeBg = view.findViewById(R.id.focus_episode_bg);
            mNameBg = view.findViewById(R.id.name_bg);
            mPlayingView = view.findViewById(R.id.on_play_icon);
            mErrorLogo = view.findViewById(R.id.error_logo);
        }
    }
}

