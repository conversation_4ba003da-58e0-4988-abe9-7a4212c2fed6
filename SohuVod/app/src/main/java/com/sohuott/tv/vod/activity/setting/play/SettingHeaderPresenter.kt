package com.sohuott.tv.vod.activity.setting.play

import androidx.appcompat.widget.AppCompatTextView
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R

class SettingHeaderPresenter : DefaultPresenter(R.layout.item_privacy_setting_header_layout) {
    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as SettingItem
        val name = viewHolder.getView<AppCompatTextView>(R.id.tv_privacy_name)
        name.text = item.groupName
    }
}