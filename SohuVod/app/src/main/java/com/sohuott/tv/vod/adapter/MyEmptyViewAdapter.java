package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.utils.StbCompatUtils;

import java.util.List;

/**
 * Created by wenjingbian on 2017/3/22.
 */

public class MyEmptyViewAdapter
        extends RecyclerView.Adapter<MyEmptyViewAdapter.ListHistoryFavorCollectionPromoteViewHolder> {

    public interface IEmptyViewFocus {
        void onEmptyViewFocus();
    }

    //Promoted videos' count
    private static final int PROMOTE_ITEM_COUNT = 5;

    private Context mContext;

    //PromoteRecyclerView
    private RecyclerView mRecyclerView;

    private FocusBorderView mFocusBorderView;  //焦点框

    private HistoryFavorCollectionRecordAdapter.FocusController mFocusController;
    private ListEduRecordAdapter.FocusController mEduFocusController;

    private IEmptyViewFocus mEmptyViewFocus;

    private int mLeftTag;

    private List<?> mDataSource;

    public MyEmptyViewAdapter(Context context, RecyclerView recyclerView) {
        this.mContext = context;
        this.mRecyclerView = recyclerView;
    }

    @Override
    public ListHistoryFavorCollectionPromoteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_list_history_favor_collection, parent, false);
        ListHistoryFavorCollectionPromoteViewHolder viewHolder = new ListHistoryFavorCollectionPromoteViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(final ListHistoryFavorCollectionPromoteViewHolder holder, int position) {
        if (mDataSource == null || mDataSource.size() <= 0) {
//            holder.promote_video_poster.setImageURI("res://" + mContext.getPackageName() + "/" + R.drawable.detail_poster_default);
            holder.promote_video_poster.setImageRes("res://" + mContext.getPackageName() + "/" + R.drawable.detail_poster_default);
            holder.promote_video_name.setText("");
            holder.promote_video_msg.setText("");
            return;
        }

        Object object = mDataSource.get(position);
        if (object instanceof VideoDetailRecommend.DataEntity) {
            final VideoDetailRecommend.DataEntity data = (VideoDetailRecommend.DataEntity) mDataSource.get(position);
            holder.promote_video_name.setText(data.getTvName());
            if (data.getCateCode() == 100) {
                holder.promote_video_msg.setText("");
            } else {
                String latestVideoSet = data.getLatestVideoCount();
                int tvSets = data.getTvSets();
                if (latestVideoSet != null) {
                    if (latestVideoSet.equals(String.valueOf(tvSets))) {
                        holder.promote_video_msg.setText(mContext.getResources().getString(R.string.txt_activity_user_related_set_pre_sum)
                                + tvSets + mContext.getResources().getString(R.string.txt_activity_user_related_set_suf));
                    } else {
                        holder.promote_video_msg.setText(mContext.getResources().getString(R.string.txt_activity_user_related_set_pre_update)
                                + latestVideoSet + mContext.getResources().getString(R.string.txt_activity_user_related_set_suf));
                    }
                }
            }
            String pictureUrl = data.getTvVerPic() == null || data.getTvVerPic().length() <= 0 ?
                    "res://" + mContext.getPackageName() + "/" + R.drawable.detail_poster_default : data.getTvVerPic();
//            holder.promote_video_poster.setImageURI(pictureUrl);
            holder.promote_video_poster.setImageRes(pictureUrl);

            if (data.getId() != 0) {
                holder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ActivityLauncher.startVideoDetailActivity(holder.itemView.getContext(), data.getId(), 0, Constant.PAGE_MY_PERSONAL_RECOMMEND);
                        RequestManager.getInstance().onPersonalRecommendListClickEvent(data.getId());
                    }
                });
            }
        } else if (object instanceof ListAlbumModel) {
            final ListAlbumModel listAlbumModel = (ListAlbumModel) object;
            holder.promote_video_name.setText(listAlbumModel.tvName);
            holder.promote_video_msg.setText("");
            String pictureUrl = listAlbumModel.tvVerPic == null || listAlbumModel.tvVerPic.length() <= 0 ?
                    "res://" + mContext.getPackageName() + "/" + R.drawable.detail_poster_default : listAlbumModel.tvVerPic;
//            holder.promote_video_poster.setImageURI(pictureUrl);
            holder.promote_video_poster.setImageRes(pictureUrl);
            holder.promote_video_poster.setCornerTypeWithType(listAlbumModel.tvIsFee, listAlbumModel.tvIsEarly, listAlbumModel.useTicket,listAlbumModel.paySeparate,listAlbumModel.cornerType);
            if(StbCompatUtils.isXiaomi()&& Build.MODEL.endsWith("mini")){
                holder.promote_video_poster.setCornerHeight(mContext.getResources().getDimensionPixelOffset(R.dimen.y33));
            }

            if (listAlbumModel.id != 0) {
                holder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        ActivityLauncher.startVideoDetailActivity(holder.itemView.getContext(), listAlbumModel.id, 0, Constant.PAGE_MY_VIP_RECOMMEND);
                        RequestManager.getInstance().onVipRecommendListClickEvent(listAlbumModel.id);
                    }
                });
            }
        }
    }

    @Override
    public int getItemCount() {
        return mDataSource != null ? mDataSource.size() : PROMOTE_ITEM_COUNT;
    }

    public void setFocusController(HistoryFavorCollectionRecordAdapter.FocusController focusController) {
        this.mFocusController = focusController;
    }

    public void setFocusController(ListEduRecordAdapter.FocusController focusController) {
        this.mEduFocusController = focusController;
    }

    public void setIEmptyViewFocus(IEmptyViewFocus emptyViewFocus) {
        this.mEmptyViewFocus = emptyViewFocus;
    }

    public void setLeftTag(int leftTag) {
        this.mLeftTag = leftTag;
    }

    public void setDataSource(List<?> dataSource) {
        this.mDataSource = dataSource;
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        mFocusBorderView = focusView;
    }

    public void releaseAll() {
        mContext = null;
        mRecyclerView = null;
        mEmptyViewFocus = null;
        if (mDataSource != null) {
            mDataSource.clear();
            mDataSource = null;
        }
        FocusUtil.clearAnimation();
    }

    class ListHistoryFavorCollectionPromoteViewHolder extends RecyclerView.ViewHolder {

        CornerTagImageView promote_video_poster;
        TextView promote_video_name;
        TextView promote_video_msg;

        public ListHistoryFavorCollectionPromoteViewHolder(final View itemView) {
            super(itemView);
            promote_video_poster = (CornerTagImageView) itemView.findViewById(R.id.ctiv_hfc_video_poster);
            promote_video_name = (TextView) itemView.findViewById(R.id.tv_item_hfc_title);
            promote_video_msg = (TextView) itemView.findViewById(R.id.tv_item_hfc_sub_title);
            promote_video_poster.setImageRes("");

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View view, int keyCode, KeyEvent event) {
                    if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                        if (mEmptyViewFocus != null) {
                            mEmptyViewFocus.onEmptyViewFocus();
                            return true;
                        }
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                            && event.getAction() == KeyEvent.ACTION_DOWN
                            && mRecyclerView.indexOfChild(view) == 0) {
                        if (mFocusController != null) {
                            mFocusController.onFocusSelected(mLeftTag);
                        }
                        if (mEduFocusController != null) {
                            mEduFocusController.onFocusSelected(mLeftTag);
                        }
                    }
                    return false;
                }
            });

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean hasFocus) {
                    promote_video_name.setSelected(hasFocus);
                    if (hasFocus) {
                        promote_video_name.setMarqueeRepeatLimit(-1);
                        promote_video_name.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setFocusView(view);
                        }
                        FocusUtil.setFocusAnimator(view, mFocusBorderView);
                    } else {
                        promote_video_name.setEllipsize(TextUtils.TruncateAt.END);
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(view);
                        }
                        FocusUtil.setUnFocusAnimator(view);
                    }
                }
            });

        }
    }
}
