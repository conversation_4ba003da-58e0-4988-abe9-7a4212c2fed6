package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;

import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.TempletActivity;
import com.sohuott.tv.vod.lib.model.MenuListBean;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.util.List;

/**
 * Created by music on 17-9-7.
 */

public class TempletLeftAdapter extends RecyclerView.Adapter<TempletLeftAdapter.TempletViewHolder> {

    private List<MenuListBean.MenuDate> mMenuDataList;

    private Context mContext;

    private int mSelectedItem = 0;
    private boolean mIsFirst = true;
    private boolean goToLeft = false;

    private FocusBorderView mFocusBorderView;
    private CustomLinearRecyclerView mCustomLinearRecyclerView;

    public TempletLeftAdapter(Context mContext) {
        this.mContext = mContext;
    }

    public void setmFocusBorderView(FocusBorderView mFocusBorderView) {
        this.mFocusBorderView = mFocusBorderView;
    }

    public void setmCustomLinearRecyclerView(CustomLinearRecyclerView mCustomLinearRecyclerView) {
        this.mCustomLinearRecyclerView = mCustomLinearRecyclerView;
    }

    public void releaseAll() {
        mContext = null;
        if (mMenuDataList != null) {
            mMenuDataList.clear();
            mMenuDataList = null;
        }
        mCustomLinearRecyclerView = null;
        mFocusBorderView = null;
    }

    @Override
    public TempletViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_templet_label, parent, false);
        TempletLeftAdapter.TempletViewHolder viewHolder = new TempletLeftAdapter.TempletViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(TempletViewHolder holder, int position) {
        if (mIsFirst && position == mSelectedItem) {
            holder.itemView.setSelected(true);
            holder.textView.setTextColor(mContext.getResources().getColor(R.color.bg_channel_list_focus));
            holder.textView.getPaint().setFakeBoldText(true);
            mIsFirst = false;

        }
        holder.textView.setText(((MenuListBean.MenuDate) mMenuDataList.get(holder.getAdapterPosition())).name);
    }

    @Override
    public int getItemCount() {
        return mMenuDataList != null ? mMenuDataList.size() : 0;
    }

    public void setmMenuDataList(List<MenuListBean.MenuDate> mMenuDataList) {
        this.mMenuDataList = mMenuDataList;
    }

    public class TempletViewHolder extends RecyclerView.ViewHolder {
        public TextView textView;
        public TempletViewHolder(final View itemView) {
            super(itemView);
            textView = (TextView) itemView.findViewById(R.id.tv_grid_left);
            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean b) {
                    if (b) {
                        textView.setTextColor(mContext.getResources().getColor(R.color.bg_channel_list_focus));
                        textView.getPaint().setFakeBoldText(true);
                        if (mFocusBorderView != null) {
                            if (mCustomLinearRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                                if (!Util.isSupportTouchVersion(mContext)) {
                                    mFocusBorderView.setFocusView(itemView);
                                }
                            }
                            itemView.setSelected(true);
                        }
                        //切换viewpager页面
                        ((TempletActivity)mContext).setPage(getAdapterPosition());
                    } else {
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(itemView);
                            if (goToLeft) {
                                itemView.setSelected(true);
                                goToLeft = false;
                            } else {
                                textView.setTextColor(mContext.getResources().getColor(R.color.bg_channel_list_default));
                                itemView.setSelected(false);
                                textView.getPaint().setFakeBoldText(false);
                            }
                        }
                    }
                }
            });


            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (Util.isSupportTouchVersion(mContext)) {
                        textView.setTextColor(mContext.getResources().getColor(R.color.bg_channel_list_focus));
                        textView.getPaint().setFakeBoldText(true);
                        itemView.setSelected(true);
                        //切换viewpager页面
                        ((TempletActivity)mContext).setPage(getAdapterPosition());
                    }
                }
            });


            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View view, int i, KeyEvent keyEvent) {
                    if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                        if (i == KeyEvent.KEYCODE_DPAD_LEFT) {
                            goToLeft = true;
                            ((TempletActivity)mContext).selectMiddleItem(getAdapterPosition());
                            return true;
                        } else if (i == KeyEvent.KEYCODE_DPAD_UP) {
                            if (getAdapterPosition() == 0) {
                                itemView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_y));
                                return true;
                            }
                        } else if (i == KeyEvent.KEYCODE_DPAD_DOWN) {
                            if (getAdapterPosition() == getItemCount() - 1) {
                                itemView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_y));
                                return true;
                            }
                        }
                    }

                    return false;
                }
            });
        }
    }
}
