package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.PointRecordInfo;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Created by wenjingbian on 2018/1/5.
 */

public class PointRecordAdapter extends RecyclerView.Adapter<PointRecordAdapter.PointRecordViewHolder> {

    private Context mContext;

    private List<PointRecordInfo.DataBean.ResultBean.ScoreRecordsBean> mDataSource;

    public PointRecordAdapter(Context context) {
        this.mContext = context;
    }

    @Override
    public PointRecordViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_point_record, parent, false);
        PointRecordViewHolder viewHolder = new PointRecordViewHolder(view);
        return viewHolder;
    }

    private void setHolderTitleColor(PointRecordViewHolder holder) {
        holder.tv_time.setTextColor(0xFFE8E8FF);
        holder.tv_project.setTextColor(0xFFE8E8FF);
        holder.tv_finish.setTextColor(0xFFE8E8FF);
        holder.tv_score.setTextColor(0xFFE8E8FF);
        holder.tv_sum_score.setTextColor(0xFFE8E8FF);
//        holder.tv_time.setTextSize(mContext.getResources().getDimensionPixelSize(R.dimen.x30));
//        holder.tv_project.setTextSize(mContext.getResources().getDimensionPixelSize(R.dimen.x30));
//        holder.tv_finish.setTextSize(mContext.getResources().getDimensionPixelSize(R.dimen.x30));
//        holder.tv_score.setTextSize(mContext.getResources().getDimensionPixelSize(R.dimen.x30));
//        holder.tv_sum_score.setTextSize(mContext.getResources().getDimensionPixelSize(R.dimen.x30));
    }

    private void setHolderContentColor(PointRecordViewHolder holder){
        holder.tv_time.setTextColor(0xB3E8E8FF);
        holder.tv_project.setTextColor(0xB3E8E8FF);
        holder.tv_finish.setTextColor(0xB3E8E8FF);
        holder.tv_score.setTextColor(0xB3E8E8FF);
        holder.tv_sum_score.setTextColor(0xB3E8E8FF);
//        holder.tv_time.setTextSize(R.dimen.x26);
//        holder.tv_project.setTextSize(R.dimen.x26);
//        holder.tv_finish.setTextSize(R.dimen.x26);
//        holder.tv_score.setTextSize(R.dimen.x26);
//        holder.tv_sum_score.setTextSize(R.dimen.x26);
    }
    @Override
    public void onBindViewHolder(PointRecordViewHolder holder, int position) {
        int pos = holder.getAdapterPosition();
        if (mDataSource == null || mDataSource.size() <= 0) {
            return;
        }

        //set background color
        if (pos == 0) {
            holder.itemView.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.bg_record_top));
            setHolderTitleColor(holder);
            holder.tv_time.setText("时间");
            holder.tv_project.setText("项目");
            holder.tv_finish.setText("完成度");
            holder.tv_score.setText("积分");
            holder.tv_sum_score.setText("总积分");
        } else {
            PointRecordInfo.DataBean.ResultBean.ScoreRecordsBean scoreRecord = mDataSource.get(pos - 1);
            if (scoreRecord == null) {
                return;
            }
            holder.itemView.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.bg_record_ctn));
            setHolderContentColor(holder);
            holder.tv_time.setText(getDate(scoreRecord.getCreateTime()));
            holder.tv_project.setText(scoreRecord.getName());
            holder.tv_sum_score.setText(String.valueOf(scoreRecord.getTotalScore()));
            if (scoreRecord.getOpType() == 0) {
                holder.tv_finish.setText("已完成");
                holder.tv_score.setText("+" + scoreRecord.getScore());
            } else if (scoreRecord.getOpType() == 1) {
                if (scoreRecord.getTaskId() == -2) {
                    holder.tv_finish.setText("已完成");
                    holder.tv_score.setText("-" + scoreRecord.getScore());
                } else {
                    holder.tv_finish.setText("兑换成功");
                    holder.tv_score.setText("-" + scoreRecord.getScore());
                }
            }
        }
    }

    @Override
    public int getItemCount() {
        return mDataSource != null ? mDataSource.size() + 1 : 1;
    }

    public void setDataSource(List<PointRecordInfo.DataBean.ResultBean.ScoreRecordsBean> dataSource) {
        this.mDataSource = dataSource;
    }

    public void releaseAll() {
        mContext = null;
        if (mDataSource != null) {
            mDataSource.clear();
            mDataSource = null;
        }
    }

    private String getDate(long dateLong) {
        SimpleDateFormat sdr = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = sdr.format(new Date(dateLong));
        return dateStr;
    }

    static class PointRecordViewHolder extends RecyclerView.ViewHolder {

        TextView tv_time, tv_project, tv_finish, tv_score, tv_sum_score;

        public PointRecordViewHolder(View itemView) {
            super(itemView);
            tv_time = (TextView) itemView.findViewById(R.id.tv_record_time);
            tv_project = (TextView) itemView.findViewById(R.id.tv_record_project);
            tv_finish = (TextView) itemView.findViewById(R.id.tv_record_finish);
            tv_score = (TextView) itemView.findViewById(R.id.tv_record_score);
            tv_sum_score = (TextView) itemView.findViewById(R.id.tv_record_sum_score);
        }
    }
}
