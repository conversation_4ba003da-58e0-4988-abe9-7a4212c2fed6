package com.sohuott.tv.vod.ui;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.model.CommonBean;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.utils.LoadQrPicture;
import com.sohuott.tv.vod.widget.SimpleNumberKeyboard;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/1/21 9:49 上午
 * @Version 1.0
 */
public class ResetPasswordDialog extends Dialog {
    private SimpleNumberKeyboard.OnCompleteListener mListener;
    private LoadQrPicture loadQrPicture;
    private LoginUserInformationHelper mHelper;
    private ImageView imageView;
    private static final int LOOP_QR = 0xe11;

    private LoopHandler mHandler = new LoopHandler();


    public ResetPasswordDialog(@NonNull Context context) {
        super(context, R.style.dialogFullTheme);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_reset_password_layout);
        imageView = (ImageView) findViewById(R.id.iv_qcode);
        mHelper = LoginUserInformationHelper.getHelper(getContext().getApplicationContext());
        loadQrPicture = new LoadQrPicture(getContext(), loginQrModel -> {

        });
        requestQrCode();
    }


    public void setResetPasswordListener(SimpleNumberKeyboard.OnCompleteListener listener) {
        mListener = listener;
    }


    public void showQrCode() {
        TeenagersManger.getInstance().exposureResetPasswordPage();
        show();
        if (imageView != null) {
            requestQrCode();
        }
        SendLoopMessage();
    }

    public void dismissQrCode() {
        dismiss();
    }

    private void removeMessage() {
        mHandler.removeMessages(LOOP_QR);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        removeMessage();
    }

    private void SendLoopMessage() {
        Message message = Message.obtain();
        message.what = LOOP_QR;
        mHandler.sendMessageDelayed(message, 2000);
    }


    private void requestQrCode() {
        loadQrPicture.getPicture(UrlWrapper.getTeenagerQrCodeUrl(UserInfoHelper.getGid(), mHelper.getLoginPassport()), imageView);
    }

    private void loopRequest() {
        NetworkApi.getTeenagerResetPassword(new Observer<CommonBean>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(CommonBean value) {
                if (value.getStatus() == 0) {
                    mListener.resetSuccess();
                    dismissQrCode();
                }
            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onComplete() {

            }
        });
    }

    private final class LoopHandler extends Handler {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case LOOP_QR:
                    SendLoopMessage();
                    loopRequest();
                    break;
            }
        }
    }


}
