package com.sohuott.tv.vod.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;

import androidx.core.content.FileProvider;

import com.alibaba.android.arouter.facade.Postcard;
import com.alibaba.android.arouter.launcher.ARouter;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.activity.AboutActivity;
import com.sohuott.tv.vod.activity.AccountLogOffActivity;
import com.sohuott.tv.vod.activity.ActorListActivity;
import com.sohuott.tv.vod.activity.GridListActivityNew;
import com.sohuott.tv.vod.activity.GridListTagActivityNew;
import com.sohuott.tv.vod.activity.LabelGridListActivity;
import com.sohuott.tv.vod.activity.ListEduUserRelatedActivity;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.activity.ListVideoActivity;
import com.sohuott.tv.vod.activity.LiveTvActivity;
import com.sohuott.tv.vod.activity.LoginActivity;
import com.sohuott.tv.vod.activity.MyMessageActivity;
import com.sohuott.tv.vod.activity.NewNetworkDialogActivity;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.activity.PayInfoActivity;
import com.sohuott.tv.vod.activity.PersonalCinemaActivity;
import com.sohuott.tv.vod.activity.ProducerActivity;
import com.sohuott.tv.vod.activity.RenewActivity;
import com.sohuott.tv.vod.activity.SearchResultActivity;
import com.sohuott.tv.vod.activity.SubjectActivity;
import com.sohuott.tv.vod.activity.TeenagerLockActivity;
import com.sohuott.tv.vod.activity.TempletActivity;
import com.sohuott.tv.vod.activity.TicketUseActivity;
import com.sohuott.tv.vod.activity.TokenExpiredActivity;
import com.sohuott.tv.vod.activity.TvHelperActivity;
import com.sohuott.tv.vod.activity.WelfareActivity;
import com.sohuott.tv.vod.activity.launcher.LauncherActivity;
import com.sohuott.tv.vod.activity.launcher.LauncherManager;
import com.sohuott.tv.vod.activity.teenagers.TeenagersActivity;
import com.sohuott.tv.vod.base_router.RouterConstants;
import com.sohuott.tv.vod.base_router.RouterPath;
import com.sohuott.tv.vod.fragment.lb.HomeContentFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.StringUtils;
import com.sohuott.tv.vod.search.SearchActivity;
import com.sohuott.tv.vod.videodetail.activity.service.EpisodeServiceManger;
import com.sohuott.tv.vod.videodetail.activity.state.VideoInfoResponse;
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by xianrongchen on 2016/7/5.
 */
public class ActivityLauncher {
    private static final String TAG = ActivityLauncher.class.getSimpleName();

    public static void startInstallActivity(Context context, String path) {
        AppLogger.d(TAG, "startInstallActivity path ? " + path);
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Uri apkUri = FileProvider.getUriForFile(context, "com.sohuott.tv.vod.FileProvider", new File(path));
                context.grantUriPermission(context.getApplicationContext().getPackageName(), apkUri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                intent.addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION);
//                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
            } else {
                intent.setDataAndType(Uri.fromFile(new File(path)),
                        "application/vnd.android.package-archive");
            }
            context.startActivity(intent);
        } catch (Exception e) {
            AppLogger.d(TAG, "install updataApk e ? " + e);
            e.printStackTrace();
        }
    }

    public static void startAboutActivity(Context context) {
        Intent intent = new Intent(context, AboutActivity.class);
        context.startActivity(intent);
    }

    public static void startIssuesFeedbackActivity() {
        ARouter.getInstance().build(RouterPath.Setting.ISSUES_FEEDBACK_ACTIVITY).navigation();
    }

    public static void startActorListActivity(Context context, int starId, String starName) {
        startActorListActivity(context, starId, false, starName);
    }

    public static void startActorListActivity(Context context, int starId, boolean isDirector, String name) {
        Intent intent = new Intent(context, ActorListActivity.class);
        if (isDirector) {
            intent.putExtra(ParamConstant.PARAM_DIRECTOR_ID, starId);
        } else {
            intent.putExtra(ParamConstant.PARAM_ACTOR_ID, starId);
        }
        intent.putExtra(ParamConstant.PARAM_ACTOR_NAME, name);
        context.startActivity(intent);
    }

    public static void startVideoDetailMoreActivity(Context context, VideoInfoResponse albumInfo, VideoDetailRecommendModel.DataBean dataBean) {

        ARouter.getInstance().build(RouterPath.Detail.VIDEO_DETAIL_DEC_ACTIVITY)
                .withParcelable("VideoInfoResponse", albumInfo)
                .withSerializable("VideoDetailRecommendModel", dataBean)
                .navigation();
//        Intent intent = new Intent(context, VideoDetailMoreActivity.class);
//        intent.putExtra(ParamConstant.PARAM_AULBUM_INFO, albumInfo);
//        intent.putExtra(ParamConstant.PARAM_RECOMMEND_DATABEAN, dataBean);
//        context.startActivity(intent);
    }




    public static void startGridListActivity(Context context, int categoryId, boolean needLabel, int dataType, long channelId, int pos) {
        if (context != null) {
            startGridListActivityUri(context, categoryId, 0, dataType, needLabel, 0);
            RequestManager.getInstance().onClickCategoryItem(channelId, pos);
        }
    }

    public static void startGridListActivityWithCatecode(Context context, int categoryId, int catecode, boolean needLabel, int dataType, long channelId, int pos) {
        if (context != null) {
            startGridListActivityUri(context, categoryId, catecode, dataType, needLabel, 0);
            if (channelId != -1) {
                RequestManager.getInstance().onClickCategoryItem(channelId, pos);
            }
        }
    }

    public static void startGridListActivityWithSub(Context context, int categoryId, int subCatecode, boolean needLabel, int dataType, long channelId, int pos) {
        if (context != null) {
            startGridListActivityUri(context, categoryId, 0, dataType, needLabel, subCatecode);
            if (channelId != -1) {
                RequestManager.getInstance().onClickCategoryItem(channelId, pos);
            }
        }
    }

    /**
     * Common entrance for starting GridListActivityNew
     *
     * @param context       Context instance
     * @param ottCateId     OttCategoryId for the first level list
     * @param cateCodeFirst identify whether is sohu class video or not(-->CornerTagImageView.CORNER_TYPE_SOHUCLASS)
     * @param videoType     0==VRS; 1==PGC
     * @param isShowHeader  Showing tag label is true, not is false
     * @param subCateId     SubCategoryId for the second level ont the left list
     */
    private static void startGridListActivityUri(Context context, int ottCateId, int cateCodeFirst,
                                                 int videoType, boolean isShowHeader, int subCateId) {
        //进入电影/电视剧/动漫/少儿/综艺/纪录片/生活/游戏频道列表页曝光
        if (ottCateId == 19 || ottCateId == 14 || ottCateId == 20 || ottCateId == 15 || ottCateId == 16 || ottCateId == 32 || ottCateId == 24 || ottCateId == 23) {
            HashMap<String, String> path = new HashMap<>(1);
            path.put("pageId", "1019");
            RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), path, null, null);
        }
        Intent intent = new Intent(context, GridListActivityNew.class);
        Uri.Builder builder = new Uri.Builder().scheme("yt").appendEncodedPath("sohu.tv").path("/gridlist.internal")
                .appendQueryParameter(ParamConstant.PARAM_CATE_ID, String.valueOf(ottCateId))
                .appendQueryParameter(ParamConstant.PARAM_CATECODE_FIRST, String.valueOf(cateCodeFirst))
                .appendQueryParameter(ParamConstant.PARAM_VIDEO_TYPE, String.valueOf(videoType))
                .appendQueryParameter(ParamConstant.PARAM_CATE_SHOW_HEADER, String.valueOf(isShowHeader))
                .appendQueryParameter(ParamConstant.PARAM_SUBCATE, String.valueOf(subCateId));
        intent.setData(builder.build());
        context.startActivity(intent);
    }

    public static void startGridListTagActivity(Context context, long channelId) {
        if (context != null) {
            Intent intent = new Intent(context, GridListTagActivityNew.class);
            context.startActivity(intent);
            RequestManager.getInstance().onClickAllLabel(channelId);
        }
    }

    public static void startCommingSoonActivity(Context context, int subjectId) {
        if (context != null) {
            LibDeprecatedLogger.e("startCommingSoonActivity 跳转 subjectId is :" + subjectId);
        }
    }

    public static void startListUserRelatedActivity(Context context, int selectedId) {
        if (context != null) {
            try {
                startListUserRelatedActivityUri(context, selectedId);
            } catch (Exception e) {
                LibDeprecatedLogger.e("Start user activity fail!", e);
            }
        }
    }

    /**
     * 进入用户相关信息页面(教育专用)
     *
     * @param context
     * @param selectedId
     */
    public static void startEduUserRelatedActivity(Context context, int selectedId, String isVip) {
        if (context != null) {
            try {
                startEduUserRelatedActivityUri(context, selectedId, isVip);
            } catch (Exception e) {
                LibDeprecatedLogger.e("Start EDU user activity fail!", e);
            }
        }
    }

    /**
     * Common entrance for starting ListUserRelatedActivity
     *
     * @param context    Context instance
     * @param selectedId int value for selected index on the left list
     */
    private static void startListUserRelatedActivityUri(Context context, int selectedId) {
        Intent intent = new Intent(context, ListUserRelatedActivity.class);
        Uri.Builder builder = new Uri.Builder().scheme("yt").appendPath("sohu.tv").path("/listuserrelated.internal");
        if (selectedId < ListUserRelatedActivity.LIST_INDEX_MY || selectedId > ListUserRelatedActivity.LIST_INDEX_CONSUME_RECORD) {
            builder.appendQueryParameter(ParamConstant.PARAM_LEFT_INDEX, String.valueOf(ListUserRelatedActivity.LIST_INDEX_MY));
        } else {
            builder.appendQueryParameter(ParamConstant.PARAM_LEFT_INDEX, String.valueOf(selectedId));
        }
        intent.setData(builder.build());
        context.startActivity(intent);
    }

    /**
     * 通过URI开启用户相关信息页面(教育专用)
     *
     * @param context
     * @param selectedId
     */
    private static void startEduUserRelatedActivityUri(Context context, int selectedId, String isVip) {
        Intent intent = new Intent(context, ListEduUserRelatedActivity.class);
        Uri.Builder builder = new Uri.Builder().scheme("yt").appendPath("sohu.tv").path("/eduuserrelated.internal");
        if (selectedId < ListEduUserRelatedActivity.LIST_INDEX_MY || selectedId > ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD) {
            builder.appendQueryParameter(ParamConstant.PARAM_LEFT_INDEX, String.valueOf(ListEduUserRelatedActivity.LIST_INDEX_MY));
        } else {
            builder.appendQueryParameter(ParamConstant.PARAM_LEFT_INDEX, String.valueOf(selectedId));
        }
        builder.appendQueryParameter(ParamConstant.PARAM_VIP, isVip);

        intent.setData(builder.build());
        context.startActivity(intent);
    }

    public static void startHomeActivity(Context context) {
        Intent intent = new Intent(context, LauncherActivity.class);
        context.startActivity(intent);
    }

    public static void startHomeActivity(Context context, Boolean isShowUpdate) {
        Intent intent = new Intent(context, LauncherActivity.class);
        intent.putExtra(ParamConstant.PARAM_IS_SHOW_UPDATE, isShowUpdate);
        context.startActivity(intent);
    }

    public static void startHomeActivityPosition(Context context, String param) {
        Intent intent = new Intent(context, LauncherActivity.class);
        intent.putExtra(ParamConstant.PARAM_SELECT_TAB, param);
        context.startActivity(intent);
    }

    /**
     * Just for "观影券专区"
     */
    public static void startListVideoActivity(Context context) {
        startListVideoActivityUri(context, 0L, false, false, false, 0, false);
    }

    public static void startListVideoActivity(Context context, long labelId) {
        startListVideoActivityUri(context, labelId, true, false, false, 0, false);
    }

    public static void startListVideoActivity(Context context, long labelId, boolean isFromPopUpWindow, int source_id) {
        startListVideoActivityUri(context, labelId, true, false, isFromPopUpWindow, source_id, true);
    }

    public static void startListVideoActivity(Context context, long labelId, boolean isDts) {
        startListVideoActivityUri(context, labelId, true, isDts, false, 0, false);
    }

    /**
     * Common entrance for starting ListVideoActivity
     *
     * @param context           Context instance
     * @param labelId
     * @param isLabelNormal     false only for "观影券", default value is true
     * @param isDts             true is DTS video, false is not DTS video
     * @param isFromPopUpWindow true is calling from outside PopUpWindow, false is calling from internal pages
     * @param sourceId          default value is 1
     * @param isNeedNewTask     whether need create new task to launch ListVideoActivity
     */
    private static void startListVideoActivityUri(Context context, long labelId, boolean isLabelNormal,
                                                  boolean isDts, boolean isFromPopUpWindow, int sourceId,
                                                  boolean isNeedNewTask) {
        Intent intent = new Intent(context, ListVideoActivity.class);
        Uri.Builder builder = new Uri.Builder().scheme("yt").appendPath("sohu.tv").path("listvideo.internal")
                .appendQueryParameter(ParamConstant.PARAM_LABEL_ID, String.valueOf(labelId))
                .appendQueryParameter(ParamConstant.PARAM_LABEL_NORMAL, String.valueOf(isLabelNormal))
                .appendQueryParameter(ParamConstant.PARAM_IS_DTS, String.valueOf(isDts))
                .appendQueryParameter(ParamConstant.PARAM_IS_POPUP_WINDOW, String.valueOf(isFromPopUpWindow))
                .appendQueryParameter("source_id", String.valueOf(sourceId));
        intent.setData(builder.build());
        if (isNeedNewTask) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(intent);
    }

    public static void startLoginActivity(Context context) {
        Intent intent = new Intent(context, LoginActivity.class);
        context.startActivity(intent);
    }

    public static void startLoginActivityWithNewTask(Context context) {
        Intent intent = new Intent(context, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void startLoginActivity(Context context, int source) {
        Intent intent = new Intent(context, LoginActivity.class);
        intent.putExtra(ParamConstant.PARAM_SOURCE, source);
        context.startActivity(intent);
    }

    public static void startLoginActivity(Context context, String page) {
        Intent intent = new Intent(context, LoginActivity.class);
        intent.putExtra(ParamConstant.PARAM_PAGE_SOURCE, page);
        context.startActivity(intent);
    }

    public static void startLoginActivity(Context context, boolean isNormalLogin) {
        Intent intent = new Intent(context, LoginActivity.class);
        intent.putExtra(ParamConstant.PARAM_IS_NORMAL_LOGIN, isNormalLogin);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void startLoginActivity(Context context, String page, int channelId) {
        Intent intent = new Intent(context, LoginActivity.class);
        intent.putExtra(ParamConstant.PARAM_PAGE_SOURCE, page);
        intent.putExtra(ParamConstant.PARAM_CHANNEL_ID, channelId);
        context.startActivity(intent);
    }

    public static void startLoginActivity(Context context, int aid, int vid, String videoName, String page) {
        Intent intent = new Intent(context, LoginActivity.class);
        intent.putExtra(ParamConstant.PARAM_AID, aid);
        intent.putExtra(ParamConstant.PARAM_VID, vid);
        intent.putExtra(ParamConstant.PARAM_ALBUM_TITLE, videoName);
        intent.putExtra(ParamConstant.PARAM_PAGE_SOURCE, page);
        context.startActivity(intent);
    }

    public static void startMyMessageActivity(Context context) {
        Intent intent = new Intent(context, MyMessageActivity.class);
        context.startActivity(intent);
    }

    public static void startPayActivity(Context context) {
        Intent intent = new Intent(context, PayActivity.class);
        intent.putExtra(ParamConstant.PARAM_AID, 0);
        intent.putExtra(ParamConstant.PARAM_VID, 0);
        intent.putExtra(ParamConstant.PARAM_ALBUM_TITLE, "");
        intent.putExtra(ParamConstant.PARAM_ALBUM_POSTER, "");
        intent.putExtra(ParamConstant.PARAM_PAY_TYPE, Constant.PAY_ENTER_TYPE_VIP);
        intent.putExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, PayActivity.PAY_SOURCE_DETAIL);

        intent.putExtra(ParamConstant.PARAM_IS_FROM_BOOTACTIVITY, false);

        intent.putExtra("source_id", 0);
        intent.putExtra(ParamConstant.PARAM_CHILD, false);
        context.startActivity(intent);
    }

    public static void startPayActivity(Context context, long paySourceComeFrom) {
//        Intent intent = new Intent(context, PayActivity.class);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.putExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, paySourceComeFrom);
//        context.startActivity(intent);
        startPayActivity(context, 0, 0, "", "", Constant.PAY_ENTER_TYPE_VIP,
                paySourceComeFrom, false, true, 0, false, 0);
    }

    public static void startPayActivity(Context context, long paySourceComeFrom, String poster) {
//        Intent intent = new Intent(context, PayActivity.class);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.putExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, paySourceComeFrom);
//        context.startActivity(intent);
        startPayActivity(context, 0, 0, "", poster, Constant.PAY_ENTER_TYPE_VIP,
                paySourceComeFrom, false, true, 0, false, 0);
    }

    public static void startPayActivity(Context context, long paySourceComeFrom, int source_id) {
//        Intent intent = new Intent(context, PayActivity.class);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.putExtra("source_id", source_id);
//        intent.putExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, paySourceComeFrom);
//        context.startActivity(intent);

        startPayActivity(context, 0, 0, "", "", Constant.PAY_ENTER_TYPE_VIP,
                paySourceComeFrom, false, true, source_id, false, 0);
    }


    public static void startPayActivity(Context context, boolean fromBoot, long paySourceComeFrom) {
//        Intent intent = new Intent(context, PayActivity.class);
//        intent.putExtra(ParamConstant.PARAM_IS_FROM_BOOTACTIVITY, fromBoot);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.putExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, paySourceComeFrom);
//        context.startActivity(intent);

        startPayActivity(context, 0, 0, "", "", Constant.PAY_ENTER_TYPE_VIP,
                paySourceComeFrom, fromBoot, true, 0, false, 0);
    }

    public static void startPayActivity(Context context, boolean fromBoot, long paySourceComeFrom, int source_id) {
//        Intent intent = new Intent(context, PayActivity.class);
//        intent.putExtra(ParamConstant.PARAM_IS_FROM_BOOTACTIVITY, fromBoot);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.putExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, paySourceComeFrom);
//        intent.putExtra("source_id", source_id);
//        context.startActivity(intent);

        startPayActivity(context, 0, 0, "", "", Constant.PAY_ENTER_TYPE_VIP,
                paySourceComeFrom, fromBoot, true, source_id, false, 0);
    }

    public static void startPayActivity(Context context, String page, long paySourceComeFrom, boolean isChild) {
        startPayActivity(context, 0, 0, "", "", Constant.PAY_ENTER_TYPE_VIP,
                paySourceComeFrom, false, false, 0, isChild, 0);
    }

    public static void startPayActivityWithAidVid(Context context, int aid, int vid, String page, long paySourceComeFrom, boolean isChild) {
        startPayActivity(context, aid, vid, "", "", Constant.PAY_ENTER_TYPE_VIP,
                paySourceComeFrom, false, false, 0, isChild, 0);
    }

    public static void startPayActivity(Context context, int aid, int vid, String videoName, long paySourceComeFrom) {
        startPayActivity(context, aid, vid, videoName, "", Constant.PAY_ENTER_TYPE_SINGLE,
                paySourceComeFrom, false, false, 0, false, 0);
    }

    public static void startPayActivity(Context context, String poster, int aid, int vid, long paySourceComeFrom) {
        startPayActivity(context, aid, vid, "", poster, Constant.PAY_ENTER_TYPE_TICKET,
                paySourceComeFrom, false, false, 0, false, 0);
    }

    public static void startPayActivity(Context context,
                                        int aid,
                                        int vid,
                                        String videoName,
                                        String poster,
                                        int payType,
                                        long paySourceComeFrom,
                                        int categoryId) {
        startPayActivity(context, aid, vid, videoName, poster, payType,
                paySourceComeFrom, false, false, 0, false, categoryId);
    }

    public static void startPayActivity(Context context,
                                        int aid,
                                        int vid,
                                        String videoName,
                                        String poster,
                                        int payType,
                                        long paySourceComeFrom,
                                        boolean isFromBoot,
                                        boolean isNewTask,
                                        int sourceId,
                                        boolean isChild,
                                        int categoryId) {
        Intent intent = new Intent(context, PayActivity.class);
        intent.putExtra(ParamConstant.PARAM_AID, aid);
        intent.putExtra(ParamConstant.PARAM_VID, vid);
        intent.putExtra(ParamConstant.PARAM_ALBUM_TITLE, videoName);
        intent.putExtra(ParamConstant.PARAM_ALBUM_POSTER, poster);
        intent.putExtra(ParamConstant.PARAM_PAY_TYPE, payType);
        intent.putExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, paySourceComeFrom);

        intent.putExtra(ParamConstant.PARAM_CATEGORY_ID, categoryId);
        intent.putExtra(ParamConstant.PARAM_IS_FROM_BOOTACTIVITY, isFromBoot);
        if (isNewTask) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        intent.putExtra("source_id", sourceId);
        intent.putExtra(ParamConstant.PARAM_CHILD, isChild);
        context.startActivity(intent);
    }

    public static void startRenewActivity(Context context, int payType) {
        Intent intent = new Intent(context, RenewActivity.class);
        intent.putExtra(RenewActivity.PARAM_AC_TYPE, payType);
        context.startActivity(intent);
    }

    public static void startProducerActivity(Context context, int producerId) {
        Intent intent = new Intent(context, ProducerActivity.class);
        intent.putExtra(ParamConstant.PARAM_PRODUCER_ID, producerId);
        context.startActivity(intent);
    }

    public static void startProducerActivity(Context context, int producerId, int subSelectedId) {
        Intent intent = new Intent(context, ProducerActivity.class);
        intent.putExtra(ParamConstant.PARAM_PRODUCER_ID, producerId);
        intent.putExtra(ParamConstant.PARAM_PRODUCER_SUB_ID, subSelectedId);
        context.startActivity(intent);
    }

    public static void startSearchActivity(Context context) {
        Intent intent = new Intent(context, SearchActivity.class);
        context.startActivity(intent);
    }

    public static void startSearchActivity(Context context, String text, boolean isVoiceSearch) {
        Intent intent = new Intent(context, SearchResultActivity.class);
        intent.putExtra(ParamConstant.PARAM_SEARCH_TXT, text);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra(ParamConstant.PARAM_IS_VOICE_SEARCH, isVoiceSearch);
        context.startActivity(intent);
    }

    public static void startSearchResultActivity(Context context, String keyword) {
        Intent intent = new Intent(context, SearchResultActivity.class);
        intent.putExtra(ParamConstant.PARAM_SEARCH_TXT, keyword);
        context.startActivity(intent);
    }

    public static void startTvHelperActivity(Context context) {
        Intent intent = new Intent(context, TvHelperActivity.class);
        context.startActivity(intent);
    }

    public static void startVideoDetailActivity(Context context, int aid, int fromPage) {
        startVideoPlayerActvityUri(context, fromPage, aid, 0, 0, false,
                0, false, false, "", 0);
    }

    public static void startVideoDetailActivity(Context context, int aid, int dataType, int fromPage) {
        startVideoPlayerActvityUri(context, fromPage, aid, dataType, 0, false,
                0, false, false, "", 0);
    }

    public static void startVideoDetailActivity(Context context, int aid, int dataType, int fromPage, String pdna) {
        startVideoPlayerActvityUri(context, fromPage, aid, dataType, 0, false,
                0, false, false, pdna, 0);
    }

    public static void startVideoDetailWithNewTask(Context context, int aid, int fromPage, int type) {
        startVideoPlayerActvityUri(context, fromPage, aid, type, 0, false,
                0, false, true, "", 0);
    }

    public static void startVideoDetailDts(Context context, int fromPage, int aid, int dataType, boolean isDts, int catecode) {
        startVideoPlayerActvityUri(context, fromPage, aid, dataType, 0, isDts,
                0, false, false, "", catecode);
    }

    public static void startVideoDetailDts(Context context, int fromPage, int aid, int dataType, boolean isDts, String pdna) {
        startVideoPlayerActvityUri(context, fromPage, aid, dataType, 0, isDts,
                0, false, false, pdna, 0);
    }

    public static void startVideoDetailDts(Context context, int fromPage, int aid, int vid, int dataType, boolean isDts, int catecode) {
        startVideoPlayerActvityUri(context, fromPage, aid, dataType, vid, isDts,
                0, false, false, "", catecode);
    }

    public static void startVideoDetailBackToLauncher(Context context, int fromPage, int aid, int dataType, boolean fromBootActivity) {
        startVideoPlayerActvityUri(context, fromPage, aid, dataType, 0, false,
                0, fromBootActivity, true, "", 0);
    }

    public static void startVideoDetailWithAppSource(Context context, int fromPage, int aid, int dataType, int source_id) {
        startVideoPlayerActvityUri(context, fromPage, aid, dataType, 0, false,
                source_id, true, true, "", 0);
    }

    public static void startVideoDetailFromCarousel(Context context, int fromPage, int aid, int dataType, int vid) {
        startVideoPlayerActvityUri(context, fromPage, aid, dataType, vid, false,
                0, false, false, "", 0);
    }

    /**
     * 公共函数，调起详情页
     *
     * @param context
     * @param fromPage    上个页面代码：必填参数
     * @param aid         专辑id：必填参数
     * @param dataType    专辑类型：非必填参数，默认为VRS
     * @param vid         指定播放视频的vid：非必填参数，只有需要指定播放第几集时传入
     * @param isDts       专辑是不是DTS：非必填参数，默认为false
     * @param source_id   应用启动来源：非必填参数，只有由详情页启动页面需要传入
     * @param backToHome  直接返回首页：非必填参数，默认为false
     * @param needNewTask 是否需要创建一个新的task：非必填参数，默认为false
     */
    public static void startVideoPlayerActvityUri(Context context, int fromPage, int aid, int dataType, int vid,
                                                  boolean isDts, int source_id, boolean backToHome, boolean needNewTask, String pdna, int catecode) {
        //判断配置新闻的话直接全屏
        EpisodeServiceManger.getInstants().onEpisodeClickRelease();
        Map<Integer, Boolean> CateCodeMap = LauncherManager.getInstance().getCateCodeMap();
        Map<Integer, Boolean> PlayListIdMap = LauncherManager.getInstance().getPlayListIdMap();
        boolean mCatecode = false;
        boolean mPlayListId = false;
        if (CateCodeMap != null) {
            mCatecode = Boolean.TRUE.equals(CateCodeMap.get(catecode));
        }
        if (PlayListIdMap != null) {
            mPlayListId = Boolean.TRUE.equals(PlayListIdMap.get(vid));
        }
        LibDeprecatedLogger.d("catecode : " +
                Boolean.TRUE.equals(mCatecode) +
                " ,playlist : " +
                Boolean.TRUE.equals(mPlayListId));

        if (Boolean.TRUE.equals(mCatecode) ||
                Boolean.TRUE.equals(mPlayListId)) {
            if (dataType != 0) {
                startPlayerActivity(context, aid, vid, dataType);
                return;
            }
        }
        Postcard postcard = ARouter.getInstance().build(RouterPath.Detail.VIDEO_DETAIL_ACTIVITY);
        postcard.withString(RouterConstants.VideoDetailParamsKey.PAGE_SOURCE, String.valueOf(fromPage))
                .withString(RouterConstants.VideoDetailParamsKey.AID, String.valueOf(aid))
                .withString(RouterConstants.VideoDetailParamsKey.SOURCE_ID, String.valueOf(source_id));
        if (vid != 0 && dataType == 0) {
            postcard.withString(RouterConstants.VideoDetailParamsKey.VID, String.valueOf(vid));
        }
        if (dataType != 0) {
            postcard.withString(RouterConstants.VideoDetailParamsKey.TYPE, String.valueOf(dataType));
        }
        if (isDts) {
            postcard.withString(RouterConstants.VideoDetailParamsKey.DTS, String.valueOf(1));
        }
        if (backToHome) {
            postcard.withString(RouterConstants.VideoDetailParamsKey.BACK_HOME, String.valueOf(1));
        }
        if (!StringUtils.isEmptyStr(pdna)) {
            postcard.withString(RouterConstants.VideoDetailParamsKey.P_DNA, pdna);
        }
//        if (needNewTask) {
//        }
        postcard.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        postcard.navigation();

//        Intent intent = new Intent(context, VideoDetailActivityNew.class);
//        Uri.Builder builder = new Uri.Builder().scheme("yt").appendPath("sohu.tv").path("/videodetail.internal")
//                .appendQueryParameter("pagesource", String.valueOf(fromPage))
//                .appendQueryParameter("aid", String.valueOf(aid));
//        if (dataType != 0) {
//            builder.appendQueryParameter("type", String.valueOf(dataType));
//        }
//        if (vid != 0 && dataType == 0) {
//            builder.appendQueryParameter("vid", String.valueOf(vid));
//        }
//        if (isDts) {
//            builder.appendQueryParameter("dts", String.valueOf(1));
//        }
//        builder.appendQueryParameter("source_id", String.valueOf(source_id));
//        if (backToHome) {
//            builder.appendQueryParameter("backhome", String.valueOf(1));
//        }
//        if (!StringUtils.isEmptyStr(pdna)) {
//            builder.appendQueryParameter("pdna", pdna);
//        }
//
//
//        intent.setData(builder.build());
//        if (needNewTask) {
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        }
//        context.startActivity(intent);
    }

    public static void startPlayerActivity(Context context, int aid, int vid, int dataType) {
        Postcard postcard = ARouter.getInstance().build(RouterPath.Detail.VIDEO_DETAIL_ACTIVITY);
        postcard.withString(RouterConstants.VideoDetailParamsKey.AID, String.valueOf(aid));
        postcard.withBoolean(RouterConstants.VideoDetailParamsKey.IS_ALL_FULL, true);
        if (vid != 0 && dataType == 0) {
            postcard.withString(RouterConstants.VideoDetailParamsKey.VID, String.valueOf(vid));
        }
        if (dataType != 0) {
            postcard.withString(RouterConstants.VideoDetailParamsKey.TYPE, String.valueOf(dataType));
        }
        postcard.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        postcard.navigation();
    }

    public static void startWelfareActivity(Context context) {
        navigatorTo(context, WelfareActivity.class.getName(), new Intent());
    }


    public static void startNetworkDialogActivity(Context context) {
        Intent intent = new Intent(context, NewNetworkDialogActivity.class);
        context.startActivity(intent);
    }


    public static void startTicketUseActivity(Context context, String title, String album_url, long aid, long vid) {

        if (context != null) {
            Intent intent = new Intent(context, TicketUseActivity.class);
            intent.putExtra(ParamConstant.PARAM_AID, aid);
            intent.putExtra(ParamConstant.PARAM_VID, vid);
            intent.putExtra(ParamConstant.PARAM_ALBUM_POSTER, album_url);
            intent.putExtra(ParamConstant.PARAM_ALBUM_TITLE, title);
            context.startActivity(intent);
        }
    }


    public static void startTempletActivity(Context context, int ottCateCode, int dataType, String title, String picUrl) {
        if (context != null) {
            try {
                startTempletActivityUri(context, ottCateCode, dataType, title, picUrl);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void startTempletActivityUri(Context context, int ottCateCode, int dataType, String title, String picUrl) {
        Intent intent = new Intent(context, TempletActivity.class);
        Uri.Builder builder = new Uri.Builder().scheme("yt").appendPath("sohu.tv").path("/templet.internal")
                .appendQueryParameter(ParamConstant.PARAM_CATE_ID, String.valueOf(ottCateCode))
                .appendQueryParameter(ParamConstant.PARAM_VIDEO_TYPE, String.valueOf(dataType))
                .appendQueryParameter(ParamConstant.PARAM_ALBUM_TITLE, title)
                .appendQueryParameter(ParamConstant.PARAM_ALBUM_POSTER, picUrl);
        intent.setData(builder.build());
        context.startActivity(intent);
    }

    public static void startLabelGridListActivity(Context context, int cateCode, String filterStr, String filterValue) {
        if (context != null) {
            Intent intent = new Intent(context, LabelGridListActivity.class);
            intent.putExtra(ParamConstant.PARAM_VIDEO_CATE_CODE, cateCode);
            intent.putExtra(ParamConstant.PARAM_VIDEO_FILTER, filterStr);
            intent.putExtra(ParamConstant.PARAM_VIDEO_FILTER_VALUE, filterValue);
            context.startActivity(intent);
        }
    }

    public static void startTokenExpiredActivity(Context context) {
        Intent intent = new Intent(context, TokenExpiredActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void startPersonalCinemaActivity(Context context) {
        Intent intent = new Intent(context, PersonalCinemaActivity.class);
        context.startActivity(intent);
    }

    public static void navigatorTo(Context from, final String toActivityName, final Intent intent) {
        if (from == null || toActivityName == null || intent == null) {
            return;
        }
        Class<?> clazz = null;
        try {
            clazz = Class.forName(toActivityName);
            if (clazz != null) {
                intent.setClass(from, clazz);
                from.startActivity(intent);
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }


    public static void startLivePlayerActivity(Context context, String roomId, String anchorId) {
//        Intent intent = new Intent(context, LivingPlayerActivity.class);
//        intent.putExtra(ParamConstant.PARAM_LIVE_ROOM_ID, roomId);
//        intent.putExtra(ParamConstant.PARAM_LIVE_ANCHOR_ID, anchorId);
//        context.startActivity(intent);
    }

//    public static void startLiveActivity(Context context, int type) {
//        Intent intent = new Intent(context, LivingActivity.class);
//        intent.putExtra(ParamConstant.PARAM_LIVE_TYPE, type);
//        context.startActivity(intent);
//    }
//
//    public static void startLiveActivity(Context context, int type, String roomId, String anchorId) {
//        Intent intent = new Intent(context, LivingActivity.class);
//        intent.putExtra(ParamConstant.PARAM_LIVE_TYPE, type);
//        intent.putExtra(ParamConstant.PARAM_LIVE_ROOM_ID, roomId);
//        intent.putExtra(ParamConstant.PARAM_LIVE_ANCHOR_ID, anchorId);
//        context.startActivity(intent);
//    }

    /**
     * Goto Live TV Activity
     *
     * @param context
     */
    public static void startLiveTvActivity(Context context, String liveUrl, String liveUrlBakup, int roomId, String picUrl) {
        Intent intent = new Intent(context, LiveTvActivity.class);
        intent.putExtra(ParamConstant.PARAM_LIVE_TV_URL, liveUrl);
        intent.putExtra(ParamConstant.PARAM_LIVE_TV_URL_BAKUP, liveUrlBakup);
        intent.putExtra(ParamConstant.PARAM_LIVE_ROOM_ID, roomId);
        intent.putExtra(ParamConstant.PARAM_LIVE_TV_PIC, picUrl);
        context.startActivity(intent);
    }

    public static void startSubjectActivity(Context context, int subjectId, String pic1, String pic2, String smallPic) {
        Intent intent = new Intent(context, SubjectActivity.class);
        intent.putExtra(ParamConstant.PARAM_SUBJECT_ID, subjectId);
        intent.putExtra(ParamConstant.PARAM_SUBJECT_PIC1, pic1);
        intent.putExtra(ParamConstant.PARAM_SUBJECT_PIC2, pic2);
        intent.putExtra(ParamConstant.PARAM_SUBJECT_SMALL_PIC, smallPic);
        context.startActivity(intent);
    }

    public static void startSubjectActivity(Context context, String pic2) {
        Intent intent = new Intent(context, SubjectActivity.class);
        intent.putExtra(ParamConstant.PARAM_SUBJECT_PIC2, pic2);
        context.startActivity(intent);
    }

    public static void startChildLockActivity(Context context, int type) {
        startChildLockActivity(context, type, -1, -1L);
    }

    public static void startChildLockActivity(Context context, int type, Long channelId) {
        startChildLockActivity(context, type, -1, channelId);
    }

    public static void startChildLockActivity(Context context, int type, int requestCode, Long channelId) {
        Intent intent = new Intent(context, TeenagerLockActivity.class);
        intent.putExtra(TeenagerLockActivity.INTENT_TYPE, type);
        intent.putExtra(HomeContentFragment.BUNDLE_KEY_TAB_CODE, channelId);
        if (context instanceof Activity && requestCode != -1) {
            Activity activity = (Activity) context;
            activity.startActivityForResult(intent, requestCode);
        } else {
            context.startActivity(intent);
        }
    }

    /**
     * 跳转青少年页面
     *
     * @param context
     * @param tabId   青少年页面id
     */
    public static void startTeenagersActivity(Context context, Long tabId) {
        TeenagersActivity.actionStart(context, tabId);
    }

    public static void startAccountLogOffActivity(Context context) {
        Intent intent = new Intent(context, AccountLogOffActivity.class);
        context.startActivity(intent);
    }

    public static void startPayInfoActivity(Context context) {
        Intent intent = new Intent(context, PayInfoActivity.class);
        context.startActivity(intent);
    }
}
