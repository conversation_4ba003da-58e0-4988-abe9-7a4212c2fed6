package com.sohuott.tv.vod.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.LogEventModel;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListWelfareModel;
import com.sohuott.tv.vod.lib.model.ListWelfareModel.DataEntity.ActivityListEntity;
import com.sohuott.tv.vod.lib.rvhelper.BaseQuickAdapter;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.utils.SimpleDisposableObsever;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.ScrollingTextView;
import com.sohuott.tv.vod.widget.WelfareRecyclerView;
import com.sohuott.tv.vod.widget.WelfareViewPager;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.disposables.CompositeDisposable;


/**
 * v6.5积分商城首页
 *
 * <AUTHOR>
 *         created at 2017/12/22
 */
public class WelfareActivityListFragment extends BaseFragment implements BaseQuickAdapter.OnItemClickListener,
        View.OnClickListener, View.OnFocusChangeListener, BaseQuickAdapter.OnItemFocusChangedListener {
    private WelfareRecyclerView mRecyclerView;
    private View mFirstOrLastFocusView;
    private CustomGridLayoutManager mLayoutManager;
    private WelfareViewPager mBanner;
    private FocusBorderView mFocusBorderView;
    private WelfareActivityDetailFragment.OnProductClickListener mOnProductClickListener;
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();


    public void setOnProductClickListener(WelfareActivityDetailFragment.OnProductClickListener onProductClickListener) {
        mOnProductClickListener = onProductClickListener;
    }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_welfare_activity_list, null);
        mFocusBorderView = (FocusBorderView) rootView.findViewById(R.id.v_focus);
        mRecyclerView = (WelfareRecyclerView) rootView.findViewById(R.id.rv_activity_list);
        mRecyclerView.setFocusBorderView(mFocusBorderView);
        mBanner= mRecyclerView.getHeaderPager();
        mLayoutManager=mRecyclerView.getLayoutManager();
        mRecyclerView.addBannerClickListener(this);
        mRecyclerView.setItemViewCacheSize(0);
        mRecyclerView.setBannerFocusChangeListener(this);
        mRecyclerView.addOnItemClickListener(this);
        mRecyclerView.setOnItemFocusChangedListener(this);
        RequestManager.onEvent(new LogEventModel("6_welfare", "100001"));
        setSubPageName("6_welfare");
        return rootView;
    }


    /**
     * 是否是RecyclerView最左边的view
     *
     * @param view
     * @return
     */
    public boolean isOnLeft(View view) {
        final RecyclerView.ViewHolder viewHolder = mRecyclerView.findContainingViewHolder(view);
        if (viewHolder != null) {
            int pos = viewHolder.getAdapterPosition();
            boolean onLeft =( (pos - 1) % mLayoutManager.getSpanCount() == 0)&&pos==1;
            if (onLeft) {
                mFirstOrLastFocusView = view;
            }
            return onLeft;
        }
        return false;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        refreshList(false);
    }

    public void refreshList(final boolean justRefreshUserInfo) {
        String passport = LoginUserInformationHelper.getHelper(getContext()).getLoginPassport();
        SimpleDisposableObsever listObserver = new SimpleDisposableObsever<ListWelfareModel>() {
            @Override
            public void onNext(ListWelfareModel value) {
                if (value.status == 0) {
                    if (value.data != null && value.data.activityList != null) {
                        if (value.extend != null && LoginUserInformationHelper.getHelper(getContext()).getIsLogin()) {
                            LoginUserInformationHelper.getHelper(getContext()).putUserLikeRank(value.extend.rank);
                            LoginUserInformationHelper.getHelper(getContext()).putTotalScore(value.extend.totalScore);
                        }
                        if (justRefreshUserInfo) {
                            return;
                        }
                        List<ActivityListEntity> bannerList = new ArrayList<>();
                        for (ActivityListEntity entity : value.data.activityList) {
                            if (entity.location == ListWelfareModel.LOCATION_BANNER) {
                                bannerList.add(entity);
                            }
                        }
                        if (bannerList.size() > 0) {
                            value.data.activityList.removeAll(bannerList);
                        }
                        mRecyclerView.setHeaderData(bannerList);
                        mRecyclerView.setAdapterData(value.data.activityList);
                    }
                } else {
                    LibDeprecatedLogger.w("response status=" + value.status);
                }
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
            }
        };
        NetworkApi.getWelfareEventList(passport, listObserver);
        mCompositeDisposable.add(listObserver);
    }

    public void setFocusViewFromKeyBack() {
        if (mFirstOrLastFocusView != null) {
            mFirstOrLastFocusView.requestFocus();
        }
    }

    public void scrollUp(View focusView){
        if(mRecyclerView.getAdapter().getItemCount()>0){
            RecyclerView.ViewHolder viewHolder= mRecyclerView.findContainingViewHolder(focusView);
            if(viewHolder!=null){
                if(mBanner.getChildCount()>0
                        &&viewHolder.getAdapterPosition()>=1
                        &&viewHolder.getAdapterPosition()<=3){
                    mLayoutManager.scrollToPositionWithOffset(0,0);
                }else if(viewHolder.getAdapterPosition()>3){
                    int newPos=viewHolder.getAdapterPosition()-3;
                    View nextView = mLayoutManager.findViewByPosition(newPos);
                    Rect rect = new Rect();
                    nextView.getLocalVisibleRect(rect);
                    if(rect.top!=0){
                        mLayoutManager.scrollToPositionWithOffset(newPos,0);
                    }
                }
            }
        }
    }
    public View getFocusViewFromLeftLayout() {
        //sometimes mFirstOrLastFocusView has been recycled
        if (mFirstOrLastFocusView != null) {
            if(mFirstOrLastFocusView.isShown()){
                return mFirstOrLastFocusView;
            }
            LibDeprecatedLogger.w("mFirstOrLastFocusView is not shown");
            int pos=mLayoutManager.findFirstCompletelyVisibleItemPosition();
            mFirstOrLastFocusView=mLayoutManager.findViewByPosition(pos);
            return mFirstOrLastFocusView;
        } else if (mBanner.getCurrentView() != null) {
            if(mBanner.getCurrentView().isShown()){
                mBanner.startPauseLoop(false);
                return mBanner.getCurrentView();
            }
            LibDeprecatedLogger.w("banner view is not shown");
            int pos=mLayoutManager.findFirstCompletelyVisibleItemPosition();
            mFirstOrLastFocusView=mLayoutManager.findViewByPosition(pos);
            return mFirstOrLastFocusView;
        } else if (mLayoutManager.getChildCount() > 1) {
            View view = mLayoutManager.getChildAt(1);
            ScrollingTextView nameText = (ScrollingTextView) view.findViewById(R.id.tv_product_name);
            nameText.setEllipsize(TextUtils.TruncateAt.MARQUEE);
            nameText.setSelfFocus(true);
            return view;
        }
        return null;
    }

    @Override
    public void onClick(View v) {
        //click banner image
        if (mOnProductClickListener != null) {
            mFirstOrLastFocusView = v;
            LogEventModel eventModel = new LogEventModel("6_welfare", "6_welfare_banner_click");
            eventModel.expand1 = v.getTag(v.getId()).toString();
            RequestManager.onEvent(eventModel);
            mOnProductClickListener.onProductClick((int) v.getTag(v.getId()), false);
        }
    }

    @Override
    public void onFocusChange(final View v, final boolean hasFocus) {
        if (mFocusBorderView != null) {
            if (hasFocus) {
                if(mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                    mFocusBorderView.setVisibility(View.VISIBLE);
                    mFocusBorderView.setFocusView(v);
                    FocusUtil.setFocusAnimator(v, mFocusBorderView, 1f, 100);
                }
            } else if (!(v.getId()==R.id.welfare_banner_view)) {
                mFocusBorderView.setUnFocusView(v);
                FocusUtil.setUnFocusAnimator(v);
            } else {
                mFocusBorderView.setVisibility(View.INVISIBLE);
            }
        } else {
            LibDeprecatedLogger.w("mFocusBorderView==null");
        }
    }

    @Override
    public void onItemFocusChanged(BaseQuickAdapter adapter, View view, int position, boolean hasFocus) {
        onFocusChange(view, hasFocus);
        ScrollingTextView nameText = (ScrollingTextView) view.findViewById(R.id.tv_product_name);
        nameText.setEllipsize(hasFocus ? TextUtils.TruncateAt.MARQUEE : TextUtils.TruncateAt.END);
        nameText.setSelfFocus(hasFocus);
    }

    @Override
    public void onResume() {
        super.onResume();
        LibDeprecatedLogger.d("onResume");
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        LibDeprecatedLogger.d("onHiddenChanged:hidden="+hidden);
        if (!hidden) {
            RequestManager.onEvent(new LogEventModel("6_welfare", "100001"));
        }
        mBanner.startPauseLoop(!mBanner.hasFocus()&&!hidden);
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        //click product
        if (mOnProductClickListener != null) {
            mFirstOrLastFocusView = view;
            ActivityListEntity entity = (ActivityListEntity) adapter.getItem(position);
            mOnProductClickListener.onProductClick(entity.id, false);

            LogEventModel eventModel = new LogEventModel("6_welfare", "6_welfare_item_click");
            eventModel.expand1 = String.valueOf(entity.id);
            RequestManager.onEvent(eventModel);
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mCompositeDisposable.clear();
    }

}
