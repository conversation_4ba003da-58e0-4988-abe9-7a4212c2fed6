package com.sohuott.tv.vod.activity.setting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.FocusFinder
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.navigation.Navigation
import androidx.recyclerview.widget.RecyclerView
import com.com.sohuott.tv.vod.base_component.NewBaseActivity
import com.lib_statistical.IMP
import com.lib_statistical.addPathAndObjectInfoEvent
import com.lib_viewbind_ext.viewBinding

import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.setting.privacy.PrivacySettingTabListSelector
import com.sohuott.tv.vod.databinding.ActivitySettingBinding
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingHeaderItem
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingItem

/**
 * 设置页面
 *
 */
class SettingActivity : NewBaseActivity(R.layout.activity_setting) {
    private var mViewBinding: ActivitySettingBinding? = null
    private val _binding by viewBinding(onViewDestroyed = {
        mViewBinding = null
        mAdapter = null
        mArrayAdapter = null
    }, ActivitySettingBinding::bind)
    private val viewModel by viewModels<SettingShareViewModel>()
    private var mArrayAdapter: ArrayObjectAdapter? = null
    private var mAdapter: ItemBridgeAdapter? = null
    private var mCurrentPosition: Int = 1
    private var isKeyEvent = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding = _binding
        initAdapter()
        initData()
        initViewModel()
    }

    private fun initViewModel() {
        viewModel.focusChange.observe(this) {
            AppLogger.v("$it")
            isKeyEvent = false
            mViewBinding!!.leanbackSettingList.selectedPosition = mCurrentPosition
            mViewBinding!!.leanbackSettingList.requestFocus()
        }
    }

    private fun initAdapter() {
        mArrayAdapter = ArrayObjectAdapter(PrivacySettingTabListSelector(this))
        mAdapter = ItemBridgeAdapter(mArrayAdapter)
        mViewBinding!!.leanbackSettingList.adapter = mAdapter
    }

    private fun initData() {
        mArrayAdapter?.add(PrivacySettingHeaderItem(name = "设置"))
        mArrayAdapter?.add(PrivacySettingItem(name = "播放设置"))
        mArrayAdapter?.add(PrivacySettingItem(name = "隐私设置"))

        mViewBinding!!.leanbackSettingList.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {

                mCurrentPosition = position
                changeFragment()
                val itemView = child?.itemView
                val layout = itemView?.findViewById<ConstraintLayout>(R.id.privacy_tab_layout)
                val textView = itemView?.findViewById<TextView>(R.id.tv_privacy_tab)
                itemView?.setOnFocusChangeListener { _, hasFocus ->
                    AppLogger.v("$hasFocus")
                    if (isKeyEvent) {
                        itemView?.background = null
                        layout?.animate()?.scaleX(1.0f)?.scaleY(1.0f)?.setDuration(300)
                            ?.start()
                        return@setOnFocusChangeListener
                    }
                    if (hasFocus) {
                        textView?.setTextColor(
                            ContextCompat.getColor(
                                this@SettingActivity,
                                R.color.color_consume_record_02
                            )
                        )
                        itemView?.background =
                            ContextCompat.getDrawable(
                                this@SettingActivity,
                                R.drawable.login_item_bg_focused
                            )
                        layout?.animate()?.scaleX(1.04f)?.scaleY(1.04f)?.setDuration(300)
                            ?.start()
                    } else {
                        textView?.setTextColor(
                            ContextCompat.getColor(
                                this@SettingActivity,
                                R.color.color_consume_record_04
                            )
                        )
                        textView?.isSelected = hasFocus
                        itemView?.background = null
                        layout?.animate()?.scaleX(1.0f)?.scaleY(1.0f)?.setDuration(300)
                            ?.start()
                    }
                }
                layout?.setOnKeyListener { view, keyCode, event ->
                    val finder = FocusFinder.getInstance()

                    if (event.action == KeyEvent.ACTION_DOWN) {
                        when (keyCode) {
                            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                                val right = finder.findNextFocus(
                                    view.parent as ViewGroup?,
                                    view,
                                    View.FOCUS_RIGHT
                                )
                                if (right == null) {
                                    textView?.setTextColor(
                                        ContextCompat.getColor(
                                            this@SettingActivity,
                                            R.color.home_color_carousel_channel_item_select_left
                                        )
                                    )
                                    isKeyEvent = true
                                } else {
                                    isKeyEvent = false
                                }

                            }

                            KeyEvent.KEYCODE_DPAD_DOWN -> {
                                val down = finder.findNextFocus(
                                    view.parent as ViewGroup?,
                                    view,
                                    View.FOCUS_DOWN
                                )
                                if (mCurrentPosition == mArrayAdapter?.size()?.minus(1) ?: 0) {
                                    return@setOnKeyListener true
                                }
                                if (down == null) {
                                    textView?.setTextColor(
                                        ContextCompat.getColor(
                                            this@SettingActivity,
                                            R.color.home_color_carousel_channel_item_select_left
                                        )
                                    )
                                    isKeyEvent = true
                                } else {
                                    isKeyEvent = false
                                }
                            }
                            KeyEvent.KEYCODE_DPAD_UP -> {
                                val down = finder.findNextFocus(
                                    view.parent as ViewGroup?,
                                    view,
                                    View.FOCUS_UP
                                )
                                if (mCurrentPosition == 1) {
                                    return@setOnKeyListener true
                                }
                                if (down == null) {
                                    textView?.setTextColor(
                                        ContextCompat.getColor(
                                            this@SettingActivity,
                                            R.color.home_color_carousel_channel_item_select_left
                                        )
                                    )
                                    isKeyEvent = true
                                } else {
                                    isKeyEvent = false
                                }
                            }
                        }
                    }

                    false
                }
                super.onChildViewHolderSelected(parent, child, position, subposition)
            }
        })
        mViewBinding!!.leanbackSettingList.selectedPosition = mCurrentPosition
//        mViewBinding.leanbackSettingList.requestFocus()
    }

    override fun onBackPressed() {
//        super.onBackPressed()
        finish()
    }

    private fun changeFragment() {
        when (mCurrentPosition) {
            1 -> {
                addPathAndObjectInfoEvent(10135, IMP, {
                    it["pageId"] = "1046"
                }, {
                    it["type"] = "page"
                    it["id"] = "1046"
                })
                AppLogger.v("changeFragment action_privacySettingFragment_to_playSettingFragment")
                Navigation.findNavController(this, R.id.fragment_container_view)
                    .navigate(R.id.playSettingFragment)
//                Navigation.createNavigateOnClickListener(,null)
            }
            2 -> {
                addPathAndObjectInfoEvent(10135, IMP, {
                    it["pageId"] = "1032"
                }, {
                    it["type"] = "page"
                    it["id"] = "1032"
                })
                Navigation.findNavController(this, R.id.fragment_container_view)
                    .navigate(R.id.privacySettingFragment)

                AppLogger.v("changeFragment action_playSettingFragment_to_privacySettingFragment")
//                Navigation.createNavigateOnClickListener(R.id.action_playSettingFragment_to_privacySettingFragment,null)
            }
        }

    }

    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.action == KeyEvent.ACTION_DOWN) {
            when (event?.keyCode) {
                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    //处理fragment 焦点返回到activity 中
                    val view = mViewBinding!!.settingContainer.findFocus()
                    AppLogger.v("dispatchKeyEvent findFocus view:${view}")
                    val finder = FocusFinder.getInstance()
                    val left =
                        finder.findNextFocus(view.parent as ViewGroup?, view, View.FOCUS_LEFT)
                    if (left == null) {
                        AppLogger.v("dispatchKeyEvent findFocus left view:${left}")
                        viewModel.focusChange(true)
                        return true
                    }
                }
            }
        }
        return super.dispatchKeyEvent(event)

    }

    override fun onDestroy() {
        super.onDestroy()
        mAdapter = null
        mArrayAdapter = null
    }

    companion object {
        fun actionStart(context: Context) {
            val intent = Intent(context, SettingActivity::class.java)
            context.startActivity(intent)
        }
    }
}