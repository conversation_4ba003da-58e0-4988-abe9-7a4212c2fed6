package com.sohuott.tv.vod.view;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.View;
import android.widget.Scroller;

import com.sohuott.tv.vod.widget.CenterLayoutManager;


/**
 * Created by rita on 17-4-14.
 * <p/>
 */
public class CustomVerticalCenterFocusRecyclerView extends RecyclerView {
    private Scroller mScroller;
    private int mLastY;

    public CustomVerticalCenterFocusRecyclerView(Context context) {
        super(context);
        init(context);
    }

    public CustomVerticalCenterFocusRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public CustomVerticalCenterFocusRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

    private void init(Context context) {
        mScroller = new Scroller(context);
    }

    PositionChangeListener mPositionChangeListener;

    public void PositionChangeListener(PositionChangeListener onScrollListener) {
        this.mPositionChangeListener = onScrollListener;
    }

    private int currentPos;
    boolean isUp, hasTriggeredListener;

    @Override
    public void scrollToPosition(final int position) {
        super.scrollToPosition(position);
        isUp = position < currentPos;
        hasTriggeredListener = false;
        currentPos = position;
//        if (getLayoutManager() instanceof CenterLayoutManager) {
//            if (position != 0 && position != getAdapter().getItemCount() - 1) {
//                post(new Runnable() {
//                    @Override
//                    public void run() {
//                        smoothScrollToPosition(position);
//                    }
//                });
//            } else {
//                if (!hasTriggeredListener && mPositionChangeListener != null) {
//                    hasTriggeredListener = true;
//                    mPositionChangeListener.onPositionChanged(currentPos, isUp);
//                }
//            }
//        }
    }

    @Override
    public void onScrollStateChanged(int state) {
        super.onScrollStateChanged(state);
//        if (state == RecyclerView.SCROLL_STATE_IDLE) {
//            if (!hasTriggeredListener && mPositionChangeListener != null) {
//                hasTriggeredListener = true;
//                mPositionChangeListener.onPositionChanged(currentPos, isUp);
//            }
//        }
    }

    public interface PositionChangeListener {
        void onPositionChanged(int pos, boolean isUp);
    }

    @Override
    public void computeScroll() {
        super.computeScroll();
        //computeScrollOffset返回true表示滚动还在继续，持续时间应该就是startScroll设置的时间
        if (mScroller != null && mScroller.computeScrollOffset()) {
            scrollBy(0, mLastY - mScroller.getCurrY());
            mLastY = mScroller.getCurrY();
            postInvalidate();//让系统继续重绘，则会继续重复执行computeScroll
        }
    }

    /**
     * 将指定item平滑移动到整个view的中间位置
     *
     * @param position
     */
    public void smoothToCenter(int position) {
        int parentHeight = getHeight();//获取父视图的宽度
        ViewHolder viewHolder = findViewHolderForAdapterPosition(position);
        if (viewHolder == null || viewHolder.itemView == null) {
            return;
        }
        View targetChild = viewHolder.itemView;
        int childTopPx = targetChild.getTop();//子view相对于父view的上边距
        int childBottomPx = targetChild.getBottom();//子view相对于父view的下边距

        int childHeight = targetChild.getHeight();
        int centerTop = parentHeight / 2 - childHeight / 2;//计算子view居中后相对于父view的左边距
        int centerBottom = parentHeight / 2 + childHeight / 2;//计算子view居中后相对于父view的右边距
        if (childTopPx > centerTop) {//子view左边距比居中view大（说明子view靠父view的右边，此时需要把子view向左平移
            //平移的起始位置就是子view的左边距，平移的距离就是两者之差
            mLastY = childTopPx;
            mScroller.startScroll(0, childTopPx, 0, centerTop - childTopPx, 50);//300为移动时长，可自行设定
            postInvalidate();
        } else if (childBottomPx < centerBottom) {
            mLastY = childBottomPx;
            mScroller.startScroll(0, childBottomPx, 0, centerBottom - childBottomPx, 50);
            postInvalidate();
        }
    }
}
