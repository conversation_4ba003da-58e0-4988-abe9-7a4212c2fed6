package com.sohuott.tv.vod.utils;

import android.content.Context;
import android.text.TextUtils;

import com.sohu.lib_utils.PrefUtil;

import java.lang.ref.WeakReference;

/**
 * Created by wenjingbian on 2017/8/25.
 */

public class TableManagerInfoHelper {

    private static final String TABLE_MANAGER_INFO = "TABLE_MANAGER_INFO";

    private WeakReference<Context> mContext;

    public TableManagerInfoHelper(Context context) {
        mContext = new WeakReference<Context>(context);
    }

    public void putDefaultTabs(String defaultTabs) {
        PrefUtil.putString( TABLE_MANAGER_INFO, "defaultTabs", TextUtils.isEmpty(defaultTabs) ? "" : defaultTabs);
    }

    public void putForcedTabs(String forcedTabs) {
        PrefUtil.putString( TABLE_MANAGER_INFO, "forcedTabs", TextUtils.isEmpty(forcedTabs) ? "" : forcedTabs);
    }

    public void putOptionalTabs(String optionalTabs) {
        PrefUtil.putString( TABLE_MANAGER_INFO, "optionalTabs", TextUtils.isEmpty(optionalTabs) ? "" : optionalTabs);
    }

    public String getDefaultTabs() {
        return PrefUtil.getString(TABLE_MANAGER_INFO, "defaultTabs", "");
    }

    public String getForcedTabs() {
        return PrefUtil.getString( TABLE_MANAGER_INFO, "forcedTabs", "");
    }

    public String getOptionalTabs() {
        return PrefUtil.getString(TABLE_MANAGER_INFO, "optionalTabs", "");
    }

    public void clearDefaultTabs() {
        PrefUtil.putString( TABLE_MANAGER_INFO, "defaultTabs", "");
    }

    public void clearForcedTabs() {
        PrefUtil.putString( TABLE_MANAGER_INFO, "forcedTabs", "");
    }

    public void clearOptionalTabs() {
        PrefUtil.putString( TABLE_MANAGER_INFO, "optionalTabs", "");
    }
}
