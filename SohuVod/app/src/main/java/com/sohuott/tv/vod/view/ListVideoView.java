package com.sohuott.tv.vod.view;

import com.sohuott.tv.vod.adapter.ListVideoAdapter;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;

import java.util.List;

/**
 * Created by XiyingCao on 16-1-7.
 */
public interface ListVideoView {
    void add(ListAlbumModel model);

    void add(List<ListAlbumModel> models);

    void showLoading();

    void hideLoading();

    void onError();

    void setListTitle(String title);

    void setListSubTitle(String subTitle);

    void setListCategory(String category);

    void setBackground(String url);

    void setVideoDetails(String comments, String title, String subTitle, String details, String scoreSource, String score, String doubanScore);

    ListVideoAdapter getAdapter();

    int getSelectedPos();

    void setCount(int count);

    void showContinue(int continuesly);
}
