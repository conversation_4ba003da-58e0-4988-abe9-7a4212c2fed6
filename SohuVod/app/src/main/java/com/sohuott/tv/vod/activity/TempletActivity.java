package com.sohuott.tv.vod.activity;

import static com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.HINT_TEXT_DEFAULT;
import static com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.HINT_TEXT_ERROR;
import static com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.HINT_TEXT_FINISH;

import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory;
import com.bumptech.glide.request.transition.Transition;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sh.ott.video.contor.ShVideoViewController;
import com.sh.ott.video.player.PlayerConstants;
import com.sh.ott.video.player.base.OnStateChangeListener;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.TempletLeftAdapter;
import com.sohuott.tv.vod.adapter.TempletPagerAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.MenuListBean;
import com.sohuott.tv.vod.lib.model.VideoGridListBean;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.lib.widgets.VerticalViewPager;
import com.sohuott.tv.vod.lib.widgets.ViewPager;
import com.sohuott.tv.vod.presenter.TempletPresenter;
import com.sohuott.tv.vod.presenter.TempletPresenterImpl;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.videodetail.activity.control.VideoPlayControlComponent;
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent;
import com.sohuott.tv.vod.videodetail.activity.control.VideoStartPreparingComponent;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.TempletView;

import java.util.HashMap;
import java.util.List;

/**
 * Created by music on 17-9-6.
 */

public class TempletActivity extends BaseFragmentActivity implements TempletView {

    private TempletPresenter mTempletPresenter;
    private CustomLinearRecyclerView mLeftListView;
    private CustomLinearLayoutManager mLinearLayoutManager;
    private TempletPagerAdapter mTempletPagerAdapter;
    private TempletLeftAdapter mTempletLeftAdapter;

    private LoadingView mLoadingView;
    private RelativeLayout mRoot;
    private VerticalViewPager mVerticalViewPager;
    private FocusBorderView mFocusBorderView;
    private View mScreenViewFocus;

    private TextView mTitle, mScore, mDirector, mType, mYear, mTypeDesc, mActor, mTip;

    private RelativeLayout mYearLine;
    private FrameLayout mLoadingBackground;
    private LinearLayout mDescRoot;
    private RelativeLayout mLineRL;
    private TextView mPageTitleTV;

    private boolean mIsRight;//是否通过右键触发选择右边列表
    private boolean mIsSleep = false;
    private boolean mIsPgc;
    private boolean isFirst = true;
    private int mOttCategoryId;
    private int mOldPage = 0;
    private String mTitleString;
    private String mBackgroundUrl;

    private int screenModel = PlayerConstants.ScreenMode.NORMAL;

    private com.sohuott.tv.vod.CommonVideoView commonVideoView;

    private VideoPromptCopyComponent mVideoPromptCopyComponent;
    private VideoStartPreparingComponent mVideoStartPreparingComponent;
    private VideoPlayControlComponent mVideoPlayControlComponent;

    private DrawableCrossFadeFactory drawableCrossFadeFactory =
            new DrawableCrossFadeFactory.Builder(300)
                    .setCrossFadeEnabled(true)
                    .build();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_templet);

        initParams();
        initView();
        initData();
        HashMap mPath = new HashMap<>(1);
        mPath.put("pageId", "1049");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), mPath, null, null);
//        Toast.makeText(this, Util.getSystemModel(), Toast.LENGTH_LONG).show();
    }

    private void initParams() {
        if (getIntent().getData() != null) {
            Uri uri = getIntent().getData();
            mTitleString = uri.getQueryParameter(ParamConstant.PARAM_ALBUM_TITLE);
            mOttCategoryId = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_CATE_ID));
            mIsPgc = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_VIDEO_TYPE)) == 0 ? false : true;
            mBackgroundUrl = uri.getQueryParameter(ParamConstant.PARAM_ALBUM_POSTER);
        } else {
            mTitleString = getIntent().getStringExtra(ParamConstant.PARAM_ALBUM_TITLE);
            mOttCategoryId = getIntent().getIntExtra(ParamConstant.PARAM_CATE_ID, Integer.MAX_VALUE);
            mIsPgc = getIntent().getIntExtra(ParamConstant.PARAM_VIDEO_TYPE, 0) == 0 ? false : true;
            mBackgroundUrl = getIntent().getStringExtra(ParamConstant.PARAM_ALBUM_POSTER);
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mIsSleep) {
            mIsSleep = false;
        }
    }

    private int filmPlayState = PlayerConstants.VideoState.IDLE;

    private void initView() {
        commonVideoView = findViewById(R.id.common_video);
        mVideoPromptCopyComponent = new VideoPromptCopyComponent(this);
        mVideoStartPreparingComponent = new VideoStartPreparingComponent(this);
        mVideoPlayControlComponent = new VideoPlayControlComponent(this, null);
        mVideoPlayControlComponent.hideMenuItem();
        commonVideoView.setShVideoViewControl(new ShVideoViewController(this));
        commonVideoView.addVideoControlComponent(mVideoPromptCopyComponent);
        commonVideoView.addVideoControlComponent(mVideoStartPreparingComponent);
        commonVideoView.addFilmVideoControlComponent(mVideoPlayControlComponent);
        commonVideoView.addFilmOnStateChangeListener(new OnStateChangeListener() {
            @Override
            public void onScreenModeChanged(int screen) {
                if (screen == PlayerConstants.ScreenMode.NORMAL) {
                    mScreenViewFocus.bringToFront();
                    mTip.setVisibility(View.VISIBLE);
                    mTip.bringToFront();
                    mTempletPagerAdapter.moveToPlaytingPage();
                }
                if (screen == PlayerConstants.ScreenMode.FULL) {
                    mTip.setVisibility(View.GONE);
                }
                screenModel = screen;
            }

            @Override
            public void onPlayerStateChanged(int state, @NonNull HashMap<String, Object> hashMap) {
                filmPlayState = state;
                switch (state) {
                    case PlayerConstants.VideoState.PLAYING:
                    case PlayerConstants.VideoState.PLAYING_BACK:
                    case PlayerConstants.VideoState.BUFFERED:
                        commonVideoView.getFilmVideoController().startUpdateProgress();
                        mVideoPromptCopyComponent.setPayHintText(HINT_TEXT_DEFAULT);
                        break;
                    case PlayerConstants.VideoState.ERROR:
                        commonVideoView.getFilmVideoController().stopUpdateProgress();
                        mVideoPromptCopyComponent.setPayHintText(HINT_TEXT_ERROR);
                        break;
                    case PlayerConstants.VideoState.PLAYBACK_COMPLETED:
                        commonVideoView.getFilmVideoController().stopUpdateProgress();
                        if (mTempletPagerAdapter.getNextPos() == false) {
                            mVideoPromptCopyComponent.setPayHintText(HINT_TEXT_FINISH);
                        }
                        break;
                }
            }
        });
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mRoot = (RelativeLayout) findViewById(R.id.cs_root);
        mLeftListView = (CustomLinearRecyclerView) findViewById(R.id.cs_videolist);
        mVerticalViewPager = (VerticalViewPager) findViewById(R.id.pager);
        mFocusBorderView = (FocusBorderView) findViewById(R.id.fragment_item_focus);
        mScreenViewFocus = findViewById(R.id.cs_focus_view);
//        mScreenView = (SimplifyScaleScreenView) findViewById(R.id.cs_videoview);
//        mScreenView = new SimplifyScaleScreenViewNew(this);
        mLoadingBackground = (FrameLayout) findViewById(R.id.loading_background);
        mDescRoot = (LinearLayout) findViewById(R.id.desc_root);
        mYearLine = (RelativeLayout) findViewById(R.id.cs_year_line);
        mLineRL = (RelativeLayout) findViewById(R.id.cs_line);
        mPageTitleTV = (TextView) findViewById(R.id.cs);

        mTitle = (TextView) findViewById(R.id.cs_title);
        mType = (TextView) findViewById(R.id.cs_type);
        mYear = (TextView) findViewById(R.id.cs_year);
        mTypeDesc = (TextView) findViewById(R.id.cs_type_desc);
        mTip = (TextView) findViewById(R.id.cs_tip);
        mActor = (TextView) findViewById(R.id.cs_actor);

        Glide.with(this).asDrawable().load(mBackgroundUrl).error(R.drawable.activity_background)
                .placeholder(R.drawable.activity_background)
                .transition(DrawableTransitionOptions.with(drawableCrossFadeFactory))
                .into(new CustomTarget<Drawable>() {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        mRoot.setBackground(resource);
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {
                        mRoot.setBackground(placeholder);
                    }

                    @Override
                    public void onLoadFailed(@Nullable Drawable errorDrawable) {
                        mRoot.setBackground(errorDrawable);
                    }
                });
    }

    private void showPlayControl() {
        if (screenModel != PlayerConstants.ScreenMode.FULL) {
            return;
        }
        if (filmPlayState == PlayerConstants.VideoState.PLAYING || filmPlayState == PlayerConstants.VideoState.PLAYING_BACK || filmPlayState == PlayerConstants.VideoState.BUFFERED || filmPlayState == PlayerConstants.VideoState.BUFFERING || filmPlayState == PlayerConstants.VideoState.PAUSED) {
            mVideoPlayControlComponent.show();
        }

    }

    private void initData() {

        mPageTitleTV.setText(mTitleString);

        mTempletPresenter = new TempletPresenterImpl(this, mOttCategoryId);

        mLinearLayoutManager = new CustomLinearLayoutManager(this);
        mLinearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mTempletLeftAdapter = new TempletLeftAdapter(this);
        mTempletLeftAdapter.setmFocusBorderView(mFocusBorderView);
        mTempletLeftAdapter.setmCustomLinearRecyclerView(mLeftListView);
        mLeftListView.setLayoutManager(mLinearLayoutManager);
        mLeftListView.setAdapter(mTempletLeftAdapter);
        mLeftListView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.bottom = getResources().getDimensionPixelOffset(R.dimen.y5);
            }
        });
        mLinearLayoutManager.setCustomPadding(1 * getResources().getDimensionPixelSize(R.dimen.y220),
                1 * getResources().getDimensionPixelSize(R.dimen.y220));

        mVerticalViewPager.setOffscreenPageLimit(255);

        mTempletPagerAdapter = new TempletPagerAdapter(getSupportFragmentManager(), mIsPgc);
        mTempletPagerAdapter.setFocusBorderView(mFocusBorderView);
        mTempletPagerAdapter.setmContext(this);
        mTempletPagerAdapter.setmVerticalViewPager(mVerticalViewPager);
        mVerticalViewPager.setAdapter(mTempletPagerAdapter);


//        mScreenView.setCirculate(false);

        mLeftListView.setOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    if (mLeftListView.getFocusedChild() != null) {
                        if (!Util.isSupportTouchVersion(TempletActivity.this)) {
                            mFocusBorderView.clearFocus();
                            mFocusBorderView.setFocusView(mLeftListView.getChildViewHolder(mLeftListView.getFocusedChild()).itemView);
                        }
                    }
                    if (mLeftListView.findViewHolderForAdapterPosition(mOldPage) != null) {
                        mLeftListView.findViewHolderForAdapterPosition(mOldPage).itemView.setSelected(true);
                        ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(mOldPage)).textView.setTextColor(getResources().getColor(R.color.bg_channel_list_focus));
                        ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(mOldPage)).textView.getPaint().setFakeBoldText(true);
                    }

                }
            }
        });

        mScreenViewFocus.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    mTip.setVisibility(View.VISIBLE);
                    mTip.bringToFront();
                    if (!Util.isSupportTouchVersion(TempletActivity.this)) {
                        mFocusBorderView.setFocusView(v);
                    }
                } else {
                    mTip.setVisibility(View.GONE);
                    mFocusBorderView.setUnFocusView(commonVideoView);
                    mFocusBorderView.clearFocus();
                }
            }
        });

        commonVideoView.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                        mFocusBorderView.setUnFocusView(commonVideoView);
                        mFocusBorderView.clearFocus();
                        mTempletPagerAdapter.setCurrentPlaytingSelected();
                        return true;
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                        return true;
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                        return true;
                    }
                }

                return false;
            }
        });

        mScreenViewFocus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                mFocusBorderView.setUnFocusView(commonVideoView);
                mFocusBorderView.clearFocus();
                commonVideoView.startLayoutParamsFull((ViewGroup) getWindow().getDecorView(), commonVideoView);
                showPlayControl();
//                mScreenView.setFullScreen(true);
//                commonVideoView.bringToFront();
                RequestManager.getInstance().onEvent("6_templet", "6_templet_fullscreen", null, null, null, null, null);

            }
        });

//        mScreenView.setSimplifyPlayerCallback(new SimplifyScaleScreenViewNew.SimplifyPlayerCallback() {
//            @Override
//            public void onPlayed() {
//
//            }
//
//            @Override
//            public void onPlayCompleted() {
//                mScreenView.stopPlay();
//                if (mTempletPagerAdapter.getNextPos() == false) {
//                    mScreenView.showComplete();
//                }
//            }
//
//            @Override
//            public void onChangeToSmallScreen() {
//                mScreenViewFocus.bringToFront();
//                mTip.bringToFront();
//                mTempletPagerAdapter.moveToPlaytingPage();
//            }
//
//            @Override
//            public void onError() {
//
//            }
//
//            @Override
//            public void onDuration(int pos) {
//
//            }
//        });


        mVerticalViewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                LibDeprecatedLogger.d(position + "," + positionOffset + "," + positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                LibDeprecatedLogger.d(position + "");
                mLeftListView.findViewHolderForAdapterPosition(mOldPage).itemView.setSelected(false);
                ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(mOldPage)).textView.setTextColor(getResources().getColor(R.color.bg_channel_list_default));
                ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(mOldPage)).textView.getPaint().setFakeBoldText(false);
                if (mOldPage > position) {
                    //向上滑动翻页
                    if (mLeftListView.findViewHolderForAdapterPosition(position - 2) == null) { //提前2个位置滚动
                        if (position > 0 && !isItemFullVisible(mLinearLayoutManager.findFirstVisibleItemPosition()) || mLeftListView.findViewHolderForAdapterPosition(position) == null) { //当前位置大于0
                            mLeftListView.smoothScrollToPosition(position - 1 < 0 ? 0 : position - 1); //平滑滚动
                        } else {//不需要滚动的情况 只需要让item上selected状态即可
                            if (mLeftListView.findViewHolderForAdapterPosition(position).itemView == null)
                                return;
                            mLeftListView.findViewHolderForAdapterPosition(position).itemView.setSelected(true);
                            ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(position)).textView.setTextColor(getResources().getColor(R.color.bg_channel_list_focus));
                            ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(position)).textView.getPaint().setFakeBoldText(true);
                        }
                    } else {
                        mLeftListView.findViewHolderForAdapterPosition(position).itemView.setSelected(true);
                        ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(position)).textView.setTextColor(getResources().getColor(R.color.bg_channel_list_focus));
                        ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(position)).textView.getPaint().setFakeBoldText(true);
                    }
                } else {
                    //向下滑动翻页
                    if (mLeftListView.findViewHolderForAdapterPosition(position + 2) == null) {
                        if (position < mTempletLeftAdapter.getItemCount() - 1 && mTempletLeftAdapter.getItemCount() > 4 && !isItemFullVisible(mLinearLayoutManager.findLastVisibleItemPosition())) {
                            mLeftListView.smoothScrollToPosition(position + 1);
                        } else {
                            mLeftListView.findViewHolderForAdapterPosition(position).itemView.setSelected(true);
                            ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(position)).textView.setTextColor(getResources().getColor(R.color.bg_channel_list_focus));
                            ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(position)).textView.getPaint().setFakeBoldText(true);
                        }
                    } else {
                        mLeftListView.findViewHolderForAdapterPosition(position).itemView.setSelected(true);
                        ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(position)).textView.setTextColor(getResources().getColor(R.color.bg_channel_list_focus));
                        ((TempletLeftAdapter.TempletViewHolder) mLeftListView.findViewHolderForAdapterPosition(position)).textView.getPaint().setFakeBoldText(true);
                    }
                }

                mOldPage = position;
                mIsRight = false;
                mTempletPagerAdapter.clearSelected(position);
                mTempletPagerAdapter.setmPage(mOldPage);
//                mLeftListView.getChildAt(position).setSelected(true);
                RequestManager.getInstance().onEvent("6_templet_tag", "6_templet_tag_click", position + "",
                        null, null, null, null);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                LibDeprecatedLogger.d(state + "");
            }
        });
    }

    private boolean isItemFullVisible(int pos) {
        View view = mLinearLayoutManager.findViewByPosition(pos);
        Rect rect = new Rect();
        view.getLocalVisibleRect(rect);
        if (pos == mLinearLayoutManager.findLastVisibleItemPosition()) {
            WindowManager manager = this.getWindowManager();
            DisplayMetrics dm = new DisplayMetrics();
            manager.getDefaultDisplay().getMetrics(dm);
            return rect.bottom == dm.heightPixels;
        }
        return rect.top == 0;
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (commonVideoView != null && screenModel == PlayerConstants.ScreenMode.FULL) {
            int keyCode = event.getKeyCode();
            boolean handled = false;
            switch (keyCode) {
                case KeyEvent.KEYCODE_DPAD_LEFT:
                case KeyEvent.KEYCODE_DPAD_RIGHT:
                    mVideoPlayControlComponent.dispatchKeyEvent(event);
                    handled = true;
                    break;
                case KeyEvent.KEYCODE_ENTER:
                case KeyEvent.KEYCODE_DPAD_CENTER:
                    if (event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (filmPlayState == PlayerConstants.VideoState.BUFFERED || filmPlayState == PlayerConstants.VideoState.PLAYING || filmPlayState == PlayerConstants.VideoState.PLAYING_BACK) {
                            commonVideoView.pause();
                        } else if (filmPlayState == PlayerConstants.VideoState.PAUSED) {
                            commonVideoView.start();
                        }
                        showPlayControl();
                    }
                    handled = true;
                    break;
                case KeyEvent.KEYCODE_DPAD_DOWN:
                    handled = true;
                    break;
                case KeyEvent.KEYCODE_BACK:
                case KeyEvent.KEYCODE_ESCAPE:
                    if (event.getAction() == KeyEvent.ACTION_UP) {
                        commonVideoView.setNormalScreenSize(
                                (int) getResources().getDimension(R.dimen.x1300),
                                (int) getResources().getDimension(R.dimen.y732)
                        );
                        commonVideoView.startLayoutParamsNormal(commonVideoView);
                        mFocusBorderView.setFocusView(commonVideoView);
                        if (filmPlayState == PlayerConstants.VideoState.PAUSED) {
                            commonVideoView.start();
                        }
                    }
                    handled = true;
                default:
                    break;
            }
            return handled;
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public void hideLoading() {
        mLoadingView.setVisibility(View.GONE);
        mRoot.setVisibility(View.VISIBLE);
        mLoadingBackground.setVisibility(View.GONE);
    }

    @Override
    public void addLeftData(List<MenuListBean.MenuDate> menuDataList) {
        if (mTempletLeftAdapter == null) return;
        mTempletLeftAdapter.setmMenuDataList(menuDataList);
        if (menuDataList.size() < 5) {
            ViewGroup.LayoutParams lp = mLeftListView.getLayoutParams();
            ViewGroup.MarginLayoutParams marginParams = null;

            if (lp instanceof ViewGroup.MarginLayoutParams) {
                marginParams = (ViewGroup.MarginLayoutParams) lp;
            } else {
                //不存在时创建一个新的参数
                //基于View本身原有的布局参数对象
                marginParams = new ViewGroup.MarginLayoutParams(lp);
            }

            WindowManager manager = this.getWindowManager();
            DisplayMetrics dm = new DisplayMetrics();
            manager.getDefaultDisplay().getMetrics(dm);
            marginParams.topMargin = (dm.heightPixels - menuDataList.size() * getResources().getDimensionPixelSize(R.dimen.y220)) / 2;
            mLeftListView.setLayoutParams(marginParams);
        }
        mTempletPagerAdapter.setMenuDataList(menuDataList);
        mTempletPagerAdapter.notifyDataSetChanged();
        mTempletLeftAdapter.notifyDataSetChanged();
        if (menuDataList.size() == 1) {
            mLeftListView.setVisibility(View.GONE);
        } else {
            RequestManager.getInstance().onEvent("6_templet_tag", "100001", null,
                    null, null, null, null);
        }
//        hideLoading();
    }

    @Override
    public void addRightData(VideoGridListBean.DataEntity dataEntity) {

    }


    @Override
    public void onLeftError() {

    }

    @Override
    public void onRightError() {

    }

    //右侧label焦点切换viewpager
    public void setPage(int page) {
        mVerticalViewPager.setCurrentItem(page);
        if (!mIsRight) {
//            mTempletPagerAdapter.moveToPlaytingPosition(page);
            mTempletPagerAdapter.scrollToTop(page);
        }
    }

    //当焦点在左边列表，此时按右键，跳转至右边标签页
    public void selectRightLabel(int position) {
        if (mLeftListView.getVisibility() != View.GONE) {
            mIsRight = true;
            mLeftListView.findViewHolderForAdapterPosition(position).itemView.requestFocus();
        }
    }

    public void selectMiddleItem(int page) {
        mTempletPagerAdapter.setSelected(page);
    }

    @Override
    protected void onPause() {
        super.onPause();
//        if (mScreenView != null) {
//            mIsSleep = true;
//            mScreenView.stopPlay();
//        }
        commonVideoView.pause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mTempletPagerAdapter != null) {

        }
        commonVideoView.destroy();

        if (mTempletLeftAdapter != null) {
            mTempletLeftAdapter.releaseAll();
            mTempletLeftAdapter = null;
        }
    }

    //设置UI并播放
    public void setPlayerUIAndPlay(ListAlbumModel model) {
        mVideoStartPreparingComponent.show();
        hideLoading();
        if (mIsPgc) {
            if (!isFirst) {
                RequestManager.getInstance().onEvent("6_templet", "6_templet_videolist_click", model.videoId + "",
                        model.videoId + "", Constant.DATA_TYPE_PGC + "", null, null);
            } else {
                isFirst = false;
            }

            mDescRoot.setVisibility(View.VISIBLE);
            mTitle.setText(model.videoTitle);
//            mType.setText("出品人：" + model.userName);

            mYear.setVisibility(View.GONE);
            mYearLine.setVisibility(View.GONE);
            mLineRL.setVisibility(View.GONE);
//            mScreenView.setPlayParamsAndPlay(model.videoId,
//                    model.videoId, Constant.DATA_TYPE_PGC);
            commonVideoView.startPlayVideo(model.videoId, model.videoId, Constant.DATA_TYPE_PGC);
            LibDeprecatedLogger.d(model.videoId + "," +
                    model.videoId);


        } else {

            if (!isFirst) {
                RequestManager.getInstance().onEvent("6_templet", "6_templet_videolist_click", model.id + "",
                        model.tvVerId + "", Constant.DATA_TYPE_VRS + "", null, null);
            } else {
                isFirst = false;
            }


            mDescRoot.setVisibility(View.VISIBLE);
            mTitle.setText(model.tvName);
//            mScreenView.setPlayParamsAndPlay(model.id,
//                    model.tvVerId, Constant.DATA_TYPE_VRS);
            commonVideoView.startPlayVideo(model.id, model.tvVerId, Constant.DATA_TYPE_VRS);
            LibDeprecatedLogger.d(model.id + "," + model.tvVerId);

            mType.setText(model.areaName);
            if (model.tvYear == 0) {
                mYear.setVisibility(View.GONE);
                mYearLine.setVisibility(View.GONE);
                ViewGroup.LayoutParams params = mTypeDesc.getLayoutParams();
                ViewGroup.MarginLayoutParams marginParams = null;
                //获取view的margin设置参数
                if (params instanceof ViewGroup.MarginLayoutParams) {
                    marginParams = (ViewGroup.MarginLayoutParams) params;
                } else {
                    //不存在时创建一个新的参数
                    marginParams = new ViewGroup.MarginLayoutParams(params);
                }
                //设置margin
                marginParams.setMargins((int) getResources().getDimension(R.dimen.x22), marginParams.topMargin, marginParams.rightMargin, marginParams.bottomMargin);
                mTypeDesc.setLayoutParams(marginParams);
            } else {
                mYear.setText(model.tvYear + "");
            }


            StringBuffer typeSb = new StringBuffer();
            for (String type : model.genreName.split(",")) {
                typeSb.append(type + "   ");
            }
            mTypeDesc.setText(typeSb);

            if (model.act == null || model.act.equals("null") || model.act.equals("无")
                    || model.act.equals("未知")) {
                mActor.setVisibility(View.GONE);
            } else {
                mActor.setVisibility(View.VISIBLE);
                StringBuffer actSb = new StringBuffer("主演：");
                for (String act : model.act.split(",")) {
                    actSb.append(act + "   ");
                }
                mActor.setText(actSb.toString());
            }
        }

    }
}
