package com.sohuott.tv.vod.activity;

import android.os.Bundle;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.WechatPublic;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.widget.GlideImageView;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by yizhang210244 on 2017/7/4.
 */

public class TvHelperActivity extends BaseActivity{

    private GlideImageView mQrSimpleDraweeView;
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_tv_help);
        mQrSimpleDraweeView = (GlideImageView) findViewById(R.id.qr_image);
        getQrcodeView();
        RequestManager.getInstance().onEvent("6_tv_help", "100001", null, null, null, null, null);
        setPageName("6_tv_help");
    }

    private void getQrcodeView() {
        DisposableObserver<WechatPublic> disposableObserver = new DisposableObserver<WechatPublic>() {
            @Override
            public void onNext(WechatPublic response) {
                if (null != response) {
                    String data = response.getData();
                    int status = response.getStatus();

                    if (status == 200 && null != data) {
                        if (!data.trim().equals("")) {
                            mQrSimpleDraweeView.setImageRes(data,getResources().getDrawable(R.drawable.bg_launcher_poster),getResources().getDrawable(R.drawable.bg_launcher_poster));
                        }
                    } else {
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onComplete() {

            }
        };
        NetworkApi.getWechatLogin(UserInfoHelper.getGid(), Constant.TYPE_CAPTCHA_BIND, disposableObserver);
        mCompositeDisposable.add(disposableObserver);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        mCompositeDisposable.clear();
    }
}
