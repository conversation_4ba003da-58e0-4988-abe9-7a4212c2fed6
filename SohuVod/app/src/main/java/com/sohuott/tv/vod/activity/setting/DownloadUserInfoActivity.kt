package com.sohuott.tv.vod.activity.setting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.com.sohuott.tv.vod.base_component.NewBaseActivity
import com.lib_statistical.IMP
import com.lib_statistical.addClickPathInfoEvent
import com.lib_statistical.addPathAndObjectInfoEvent
import com.lib_viewbind_ext.viewBinding
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.databinding.ActivityDownloadUserInfoBinding
import com.sohuott.tv.vod.utils.PrivacySettingHelper

/**
 * 个人信息下载页面
 *
 */
class DownloadUserInfoActivity : NewBaseActivity(R.layout.activity_download_user_info) {

    private val mViewBinding by viewBinding(ActivityDownloadUserInfoBinding::bind)


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewBinding.userInfoContent.text = PrivacySettingHelper.userInfoContent ?: ""
        addPathAndObjectInfoEvent(10135, IMP, {
            it["pageId"] = "1033"
        }, {
            it["type"] = "page"
            it["id"] = "1033"
        })
        mViewBinding.userInfoExit.setOnClickListener {
            addClickPathInfoEvent(10253) {
                it["pageId"] = "1033"
            }
            finish()
        }
        mViewBinding.userInfoExit.requestFocus()
    }

    override fun onBackPressed() {
        finish()
//        super.onBackPressed()
    }

    companion object {
        fun actionStart(context: Context) {
            val intent = Intent(context, DownloadUserInfoActivity::class.java)
            context.startActivity(intent)
        }
    }
}