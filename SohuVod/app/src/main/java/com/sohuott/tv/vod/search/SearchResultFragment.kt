package com.sohuott.tv.vod.search

import android.graphics.Color
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.Presenter
import com.drake.net.Get
import com.drake.net.utils.scopeNetLife
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.databinding.FragmentSearchResultBinding
import com.sohuott.tv.vod.fragment.CustomItemBridgeAdapter
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.utils.ToastUtils
import com.sohuott.tv.vod.model.SearchResult
import com.sohuott.tv.vod.presenter.launcher.VideoDetailTypeTwoContentPresenter
import okhttp3.Headers.Companion.toHeaders
import java.math.BigInteger
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

class SearchResultFragment: Fragment(), KeyEventHandler,
    CustomItemBridgeAdapter.OnItemViewClickedListener {
    private lateinit var resultTitle: String
    private var _binding: FragmentSearchResultBinding? = null
    private val binding get() = _binding!!

    //结果
    private var searchResultArrayAdapter: ArrayObjectAdapter? = null
    private var searchResultItemBridgeAdapter: CustomItemBridgeAdapter? = null
    private var searchResultPresenter: SearchResultPresenterKt? = null

    //相关
    private var searchRelatedArrayAdapter: ArrayObjectAdapter? = null
    private var searchRelatedItemBridgeAdapter: CustomItemBridgeAdapter? = null
    private var searchRelatedPresenter: VideoDetailTypeTwoContentPresenter? = null

    private val SORT_DEFAULt = 0
    private val SORT_TIME = 1
    private val SORT_HOT = 2


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        resultTitle = arguments?.getString(ARG_TEXT) ?: ""
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSearchResultBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        init()
        getData(SORT_DEFAULt)
        binding.searchResultDefault.isSelected = true
//        binding.searchResultList.requestFocus()
        binding.searchResultTitle.setText(getColoredTitle(""), TextView.BufferType.SPANNABLE)
    }

    private fun init(){
        //结果
        searchResultPresenter = SearchResultPresenterKt()
        searchResultArrayAdapter = ArrayObjectAdapter(searchResultPresenter)
        searchResultItemBridgeAdapter = object: CustomItemBridgeAdapter(searchResultArrayAdapter){
            override fun getOnItemViewClickedListener(): OnItemViewClickedListener {
                return this@SearchResultFragment
            }
        }
        binding.searchResultList.adapter = searchResultItemBridgeAdapter
        binding.searchResultList.setNumColumns(3)
        binding.searchResultList.verticalSpacing = 48
        binding.searchResultList.horizontalSpacing = 24

        //相关
        searchRelatedPresenter = VideoDetailTypeTwoContentPresenter()
        searchRelatedArrayAdapter = ArrayObjectAdapter(searchRelatedPresenter)
        searchRelatedItemBridgeAdapter = object: CustomItemBridgeAdapter(searchRelatedArrayAdapter){
            override fun getOnItemViewClickedListener(): OnItemViewClickedListener {
                return this@SearchResultFragment
            }
        }
        binding.searchRelativeList.adapter = searchRelatedItemBridgeAdapter
        binding.searchRelativeList.setNumColumns(4)
        binding.searchRelativeList.verticalSpacing = 48
        binding.searchRelativeList.horizontalSpacing = 24
    }

    private fun getData(sort: Int){
        val time = System.currentTimeMillis()
        scopeNetLife {
            val result = Get<SearchResult>("${RetrofitApi.get().retrofitHost.baseHost}search/search.json"){
                param("query", resultTitle)
                param("pageSize", 30)
                param("page", 1)
                param("sort", sort)
                param("timeStamp", time)
                param("code", md5(resultTitle + time + "09606ac70454ce82"))
                setHeaders(HeaderHelper.getHeaders().toHeaders())
            }.await()
            if (result.data.searchItems.isEmpty()) {
                ToastUtils.showToast(context, "空数据")
            } else {
                searchResultArrayAdapter?.addAll(0, result.data.searchItems)
                binding.searchResultList.layoutParams.height = Math.ceil(result.data.searchItems.size / 3.0).toInt() * 346 + (Math.ceil(result.data.searchItems.size / 3.0).toInt() - 2) * 48
                binding.searchResultList.requestLayout()
                binding.searchRelativeTitle.text = "\u201c${result.data.searchItems[0].tvName}\u201d的相关影视推荐"
                if (result.data.relationItems.isNotEmpty()) {
                    searchRelatedArrayAdapter?.addAll(0, result.data.relationItems)
                    binding.searchRelativeList.layoutParams.height = Math.ceil(result.data.relationItems.size / 4.0).toInt() * 226 + (Math.ceil(result.data.relationItems.size / 4.0).toInt()) * 48
                    binding.searchRelativeList.requestLayout()
                }
            }
            LibDeprecatedLogger.d("searchList size: ${result.data.searchItems.size}")
            LibDeprecatedLogger.d("relationList size: ${result.data.relationItems.size}")
        }
    }

    private fun getColoredTitle(text: String) :SpannableStringBuilder {
        val coloredText = "全部\u201c$resultTitle\u201d的结果"
        // 设为 #FF6247
        val colorSpan = ForegroundColorSpan(Color.parseColor("#FF6247"))
        return SpannableStringBuilder(coloredText).apply {
            setSpan(colorSpan, 2, coloredText.length - 3, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        }
    }

    fun md5(input: String): String {
        return try {
            val messageDigest = MessageDigest.getInstance("MD5")
            messageDigest.update(input.toByteArray())
            val digest = messageDigest.digest()
            val bigInteger = BigInteger(1, digest)
            String.format("%032x", bigInteger)
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            ""
        }
    }

    override fun onDetach() {
        LibDeprecatedLogger.d("SearchResultFragment onDetach")
        super.onDetach()
    }

    override fun onDestroyView() {
        LibDeprecatedLogger.d("SearchResultFragment onDestroyView")
        super.onDestroyView()
        _binding = null
//        (context as SearchActivity).resumeFocus()
    }

    companion object {
        private const val ARG_TEXT = "search_result"

        fun newInstance(text: String): SearchResultFragment{
            val args = Bundle()
            args.putString(ARG_TEXT, text)
            val fragment = SearchResultFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun onKeyEvent(event: KeyEvent): Boolean {
        return false
    }

    override fun onItemClicked(
        focusView: View?,
        itemViewHolder: Presenter.ViewHolder?,
        item: Any?
    ) {
        when(item) {
            is SearchResult.Data.SearchItem -> {ToastUtils.showToast(context, item.tvName)}
            is SearchResult.Data.RelationItem -> {ToastUtils.showToast(context, item.tvName)}
        }

    }
}

interface KeyEventHandler {
    fun onKeyEvent(event: KeyEvent): Boolean
}
