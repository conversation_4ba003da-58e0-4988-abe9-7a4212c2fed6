package com.sohuott.tv.vod.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Message;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 下载图片
 *
 * <AUTHOR>
 * @Date Created on 2020/1/9.
 */
public class LoadPicture {

    private Context mContext;
    private Handler mHandler;

    public static final int HANDLER_WHAT = 999;

    public LoadPicture(Context context, Handler handler) {
        this.mContext = context;
        this.mHandler = handler;
    }

    /**
     * 异步下载图片，并通过Handler发送下载结果，交给使用方处理
     *
     * @param url
     */
    public void load(String url) {
//        Observable observable = Observable.create(new ObservableOnSubscribe<byte[]>() {
//            public void subscribe(ObservableEmitter<byte[]> e) throws Exception {
//            }
//        });
        if (url == null || url.isEmpty()) {
            return;
        }
        OkHttpClient okHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .url(url).build();
        okHttpClient.newCall(request).enqueue(new Callback() {

            @Override
            public void onFailure(Call call, IOException e) {
                LibDeprecatedLogger.w("Load picture fail!", e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response != null) {
                    LibDeprecatedLogger.d("Load picture response: " + response.code());
                }
                byte[] imageByte = null;
                if (response != null && response.body() != null) {
                    imageByte = response.body().bytes();
                    LibDeprecatedLogger.d("Load picture success!");
                }
                handlerResult(imageByte);
            }
        });
    }

    private void handlerResult(byte[] image) {
        Message msg = Message.obtain();
        msg.obj = image;
        msg.what = HANDLER_WHAT;
        mHandler.sendMessage(msg);
    }

//    @Override
//    public void run() {
//        load(mLoadUrl);
//    }
//
//    private String mLoadUrl;
//    public void setLoadUrl(String url) {
//        this.mLoadUrl = url;
//    }
}
