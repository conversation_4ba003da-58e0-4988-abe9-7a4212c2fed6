package com.sohuott.tv.vod.utils

import com.sohuott.tv.base_room.entity.ImageInfo
import com.sohuott.tv.base_room.manager.ImageInfoManager
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import okhttp3.Interceptor
import okhttp3.Response
import java.util.concurrent.TimeUnit

class GlideInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val startTime = System.nanoTime()
        val response = chain.proceed(request)
        val endTime = System.nanoTime()

        if (response.networkResponse != null) {
            val url = request.url.toString()
            val duration = TimeUnit.NANOSECONDS.toMillis(endTime - startTime)
            val size = response.body?.contentLength() ?: 0
            val tag = request.header("ImageTag").toString()
            if (tag != "null") {
                LibDeprecatedLogger.d("Glide url=$url, duration=$duration, size=$size, tag=$tag, start=$startTime, end=$endTime")
                val info = ImageInfo(id = 0, url = url, duration = duration, size = size, tag = tag)
                ImageInfoManager.add(info)
            }
        }

        return response
    }
}