package com.sohuott.tv.vod.search

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.View
import android.view.View.OnClickListener
import android.view.View.OnFocusChangeListener
import android.view.View.OnKeyListener
import android.widget.TextView
import androidx.activity.viewModels
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.BaseGridView
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.com.sohuott.tv.vod.base_component.NewBaseActivity
import com.drake.net.Get
import com.drake.net.utils.debounce
import com.drake.net.utils.launchIn
import com.drake.net.utils.scopeNetLife
import com.lib_viewbind_ext.viewBinding
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.databinding.ActivityNewSearchBinding
import com.sohuott.tv.vod.fragment.CustomItemBridgeAdapter
import com.sohuott.tv.vod.fragment.CustomItemBridgeAdapter.OnItemViewClickedListener
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.base.ActivityManagerUtil
import com.sohuott.tv.vod.lib.db.greendao.DaoSession
import com.sohuott.tv.vod.lib.db.greendao.DaoSessionInstance
import com.sohuott.tv.vod.lib.db.greendao.SearchHistory
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.model.SearchHot
import com.sohuott.tv.vod.presenter.search.SearchAssociatePresenter
import com.sohuott.tv.vod.presenter.search.SearchHotPresenter
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import okhttp3.Headers.Companion.toHeaders
import java.math.BigInteger
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

class SearchActivity : NewBaseActivity(R.layout.activity_new_search),
    SearchHistoryDatabaseProvider,
    KeyEventHandler,
    OnClickListener,
    OnFocusChangeListener,
    OnKeyListener {

    private var isFirstTimeShowTips: Boolean = true
    private val SEARCH_TEXT_MAX_LENGTH = 26

    //SearchAssociateFlow
    private val searchAssociateFlow = MutableSharedFlow<String>()

    //热搜榜
    private var hotSearchArrayAdapter: ArrayObjectAdapter? = null
    private var hotSearchPresenter: SearchHotPresenter? = null
    private var hotSearchItemBridgeAdapter: CustomItemBridgeAdapter? = null

    //搜索历史
    private var searchHistoryArrayAdapter: ArrayObjectAdapter? = null
    private var searchHistoryPresenter: SearchHotPresenter? = null
    private var searchHistoryItemBridgeAdapter: CustomItemBridgeAdapter? = null

    //搜索联想
    private var searchAssociateArrayAdapter: ArrayObjectAdapter? = null
    private var searchAssociatePresenter: SearchAssociatePresenter? = null
    private var searchAssociateItemBridgeAdapter: ItemBridgeAdapter? = null

    private var suggestsList: List<SearchAssociate.Data.Suggest>? = null

    private val searchViewModel: SearchViewModel by viewModels()

    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private val PAGE_FROM_INPUT = 0
    private val PAGE_FROM_HISTORY = 1
    private val PAGE_FROM_HOT = 2

    var rightKeyPressed:Boolean = false

    var scope: CoroutineScope? = null
    private var mViewBinding: ActivityNewSearchBinding? = null
    private val _binding by viewBinding(onViewDestroyed = {
        mViewBinding = null
    }, ActivityNewSearchBinding::bind)


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ActivityManagerUtil.addActivityToList(this)
        mViewBinding = _binding
        init()
        initListener()
        initData()

        //埋点
        SearchRequestManager.pageExposure()
        SearchRequestManager.keyBoardExposure("0")
    }

    override fun onResume() {
        super.onResume()
        LibDeprecatedLogger.d("SearchActivity onResume")
        //刷新搜索历史
        searchViewModel.getSearchHistory(this@SearchActivity)
//        currentFocusView?.requestFocus()
    }

    @SuppressLint("RestrictedApi")
    private fun init() {

        mBrowseItemFocusHighlight =
            MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false)

        mViewBinding?.fullKeyboardLayout?.requestFirstFocus()
        // 设置 debounce 操作符，防止频繁调用
        val debouncedFlow = searchAssociateFlow.debounce(500)

        // 联想词
        lifecycleScope.launch {
            debouncedFlow.collect { event ->
                println("处理事件: $event")
                showSearchResult(
                    event,
                    true,
                    PAGE_FROM_INPUT,
                    mViewBinding?.searchEditText?.text.toString()
                )
                if (mViewBinding?.searchAssociateNoResult?.visibility == View.VISIBLE) {
                    SearchRequestManager.hotSearchFocus(event)
                } else {
                    SearchRequestManager.guessSearchFocus(event)
                }
            }
        }
        hotSearchPresenter = SearchHotPresenter()
        hotSearchArrayAdapter = ArrayObjectAdapter(hotSearchPresenter)

        searchHistoryPresenter = SearchHotPresenter()
        searchHistoryArrayAdapter = ArrayObjectAdapter(searchHistoryPresenter)



        searchHistoryArrayAdapter?.let {
            searchHistoryItemBridgeAdapter = createItemBridgeAdapter(it)
        }


        hotSearchArrayAdapter?.let {
            hotSearchItemBridgeAdapter = createItemBridgeAdapter(it)
        }

        mViewBinding?.searchHotList.let {
            it?.setNumColumns(2)
            it?.focusScrollStrategy = BaseGridView.FOCUS_SCROLL_ITEM
//            it?.verticalSpacing = 40
            it?.adapter = hotSearchItemBridgeAdapter
        }

        mViewBinding?.historyList.let {
            it?.setNumColumns(2)
            it?.focusScrollStrategy = BaseGridView.FOCUS_SCROLL_ITEM

            it?.adapter = searchHistoryItemBridgeAdapter
        }

        //搜索联想
        searchAssociatePresenter = SearchAssociatePresenter()
        searchAssociateArrayAdapter = ArrayObjectAdapter(searchAssociatePresenter)

        searchAssociateArrayAdapter?.let {
            searchAssociateItemBridgeAdapter = ItemBridgeAdapter(it)
        }

        mViewBinding?.searchAssociateList?.let {
            it.setNumColumns(1)
//            it.verticalSpacing = 40
            it.adapter = searchAssociateItemBridgeAdapter
        }

        mViewBinding?.keyboardFull?.isSelected = true
    }

    private fun createItemBridgeAdapter(adapter: ArrayObjectAdapter): CustomItemBridgeAdapter {
        return object : CustomItemBridgeAdapter(adapter) {
            override fun getOnItemViewClickedListener(): OnItemViewClickedListener {
                return OnItemViewClickedListener { view, holder, item ->
                    // Handle click event
                    when (item) {
                        is SearchHot.Data -> {
                            LibDeprecatedLogger.d(item.name)
                            showSearchResult(item.name, false, PAGE_FROM_HOT, "")
                            SearchRequestManager.hotSearchContentClick(item.name)
                        }

                        is SearchHistory -> {
                            LibDeprecatedLogger.d(item.albumTitle)
                            showSearchResult(item.albumTitle, false, PAGE_FROM_HISTORY, "")
                            SearchRequestManager.searchHistoryContentClick(item.albumTitle)
                        }
                    }
                }
            }
        }
    }

    //搜索结果页面左滑曝光
    private fun searchResultLeftExposure() {
        //搜索联想词
        if (mViewBinding?.searchAssociateNoResult?.visibility == View.VISIBLE) {
            SearchRequestManager.hotSearchExposure(
                false,
                searchViewModel.hotSearchList.value.joinToString(";") { it.name })
        } else {
            SearchRequestManager.guessSearchExposure(
                "1",
                mViewBinding?.searchEditText?.text.toString(),
                suggestsList?.joinToString(";") { it.keyword })
        }

        //键盘
        if (mViewBinding?.fullKeyboardLayout?.visibility == View.VISIBLE) {
            SearchRequestManager.keyBoardExposure("0")
        } else {
            SearchRequestManager.keyBoardExposure("1")
        }
    }

    private var currentFocusView: View? = null
    private fun showSearchResult(
        text: String,
        isFromActivity: Boolean,
        fromPage: Int,
        originalText: String
    ) {
        if (isFromActivity) {
            mViewBinding?.searchResultRight?.removeAllViews()
            //滑动

            val searchResultView = SearchResultView(this)
            searchResultView.setOnSearchResultFocusChangeListener(object :
                SearchResultView.OnSearchResultFocusChangeListener {
                override fun onSearchResultFocusLeft() {
                    val translation: ObjectAnimator =
                        ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", 0f)
                    translation.duration = 500
                    translation.start()
                    mViewBinding?.searchAssociateList?.requestFocus()
                    searchResultLeftExposure()

                }

                override fun onSearchResultFocusBack() {
                    val translation: ObjectAnimator =
                        ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", 0f)
                    translation.duration = 500
                    translation.start()
                    mViewBinding?.keyboardDeleteRoot?.requestFocus()
                    searchResultLeftExposure()
                }
            })

            searchResultView.setResultTitle(text, fromPage, originalText)
            mViewBinding?.searchResultRight?.addView(searchResultView)
        } else {
            //全屏
            val searchResultView = SearchResultView(this)


            searchResultView.setOnSearchResultFocusChangeListener(object :
                SearchResultView.OnSearchResultFocusChangeListener {
                override fun onSearchResultFocusLeft() {

                }

                override fun onSearchResultFocusBack() {
                    mViewBinding?.searchResultFull?.removeAllViews()
                    mViewBinding?.keyboardRoot?.visibility = View.VISIBLE
                    mViewBinding?.searchHotHistoryRoot?.visibility = View.VISIBLE
                    currentFocusView?.requestFocus()

                    //埋点
                    SearchRequestManager.pageExposure()
                    //键盘
                    if (mViewBinding?.fullKeyboardLayout?.visibility == View.VISIBLE) {
                        SearchRequestManager.keyBoardExposure("0")
                    } else {
                        SearchRequestManager.keyBoardExposure("1")
                    }
                    SearchRequestManager.hotSearchExposure(
                        true,
                        searchViewModel.hotSearchList.value.joinToString(";") { it.name })
                    SearchRequestManager.searchHistoryExposure(
                        searchViewModel.searchHistoryList.value.joinToString(
                            ";"
                        ) { it.albumTitle })
                }
            })

            searchResultView.setResultTitle(text, fromPage, originalText)
            mViewBinding?.searchResultFull?.addView(searchResultView)

            currentFocusView = currentFocus
//            setLeftToLeftConstraint(mViewBinding?.searchResultActivity!!, mViewBinding?.root!!)
//            mViewBinding?.searchResultActivity?.setResultTitle(text)
//            mViewBinding?.searchResultActivity?.visibility = View.VISIBLE
            mViewBinding?.keyboardRoot?.gone()
            mViewBinding?.searchHotHistoryRoot?.gone()
            searchResultView.requestFocus()
//            mViewBinding?.searchResultActivity?.requestFocus()

        }
//        val fragment = SearchResultFragment.newInstance(text)
//        if (isFromActivity) {
//            supportFragmentManager.beginTransaction()
//                .replace(R.id.search_result_activity, fragment, "SearchResultActivity")
//                .addToBackStack(null)
//                .commit()
//        } else {
//            supportFragmentManager.beginTransaction()
//                .replace(R.id.search_result_fragment, fragment, "SearchResultFragment")
//                .addToBackStack(null)
//                .commit()
//        }
    }

    private fun initListener() {
        mViewBinding?.keyboardDeleteRoot?.setOnClickListener(this)
        mViewBinding?.keyboardDeleteRoot?.onFocusChangeListener = this
        mViewBinding?.keyboardClearRoot?.setOnClickListener(this)
        mViewBinding?.keyboardClearRoot?.onFocusChangeListener = this
        mViewBinding?.historyClear?.onFocusChangeListener = this

        mViewBinding?.keyboardFull?.onFocusChangeListener = this
        mViewBinding?.keyboardT9?.onFocusChangeListener = this
        mViewBinding?.keyboardT9?.setOnKeyListener(this)
        mViewBinding?.historyClear?.setOnClickListener(this)

//        mViewBinding?.searchResultActivity?.setOnSearchResultFocusChangeListener(object : SearchResultView.OnSearchResultFocusChangeListener {
//            override fun onSearchResultFocusLeft() {
//                val translation: ObjectAnimator =
//                    ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", 0f)
//                translation.duration = 500
//                translation.start()
//                mViewBinding?.searchAssociateList?.requestFocus()
//            }
//
//            override fun onSearchResultFocusBack() {
//                if (mViewBinding?.keyboardRoot?.visibility == View.VISIBLE) {
//                    val translation: ObjectAnimator =
//                        ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", 0f)
//                    translation.duration = 500
//                    translation.start()
//                    mViewBinding?.searchAssociateList?.requestFocus()
//                }
//            }
//        })
        mViewBinding?.keyboardRoot?.setAutoFocus(true)
        mViewBinding?.keyboardRoot?.setOnSearchKeyBoardFocusChangeListener(object :
            SearchKeyBoardLayout.OnSearchKeyBoardFocusChangeListener {
            override fun onSearchKeyBoardFocusRight() {
                if (mViewBinding?.searchAssociateRoot?.visibility == View.VISIBLE) {
                    val dis = mViewBinding?.keyboardRoot?.measuredWidth
                    val translation: ObjectAnimator =
                        ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", -dis!!.toFloat())
                    translation.duration = 500
                    translation.start()
                    mViewBinding?.searchAssociateList?.requestFocus()
                }
            }
        })

        mViewBinding?.searchAssociateList?.addOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                //利用flow的debounce操作符，显示searchresult页面,防止频繁调用
                lifecycleScope.launch {
                    searchAssociateFlow.emit(child?.itemView?.findViewById<TextView>(R.id.search_associate_item_title)?.text.toString())
                    if (position == 0) {
                        child?.itemView?.isSelected = true
                        child?.itemView?.findViewById<View>(R.id.search_associate_item_selected)
                            ?.visible()
                    }
                }
                super.onChildViewHolderSelected(parent, child, position, subposition)
            }
        })

        searchAssociatePresenter?.setOnSearchHotItemKeyListener(object :
            SearchAssociatePresenter.OnSearchAssociateItemKeyListener {
            override fun onSearchItemRightKey(vh: SearchAssociatePresenter.SearchAssociateViewHolder) {
                LibDeprecatedLogger.d("SearchActivity onSearchItemRightKey")
                val dis =
                    mViewBinding?.searchAssociateRoot?.measuredWidth?.plus(mViewBinding?.keyboardRoot?.measuredWidth!!)
                val translation: ObjectAnimator =
                    ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", -dis!!.toFloat())
                translation.duration = 500
                translation.start()

                try {
                    rightKeyPressed = true
                    (mViewBinding?.searchResultRight?.getChildAt(0) as SearchResultView).requestFirstFocus()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onSearchItemLeftKey(vh: SearchAssociatePresenter.SearchAssociateViewHolder) {
                AppLogger.d("SearchActivity onSearchItemLeftKey")
                rightKeyPressed = false
                val translation: ObjectAnimator =
                    ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", 0f)
                translation.duration = 500
                translation.start()
                //键盘
                if (mViewBinding?.fullKeyboardLayout?.visibility == View.VISIBLE) {
                    SearchRequestManager.keyBoardExposure("0")
                } else {
                    SearchRequestManager.keyBoardExposure("1")
                }
            }

            override fun onSearchItemBackKey() {
                AppLogger.d("SearchActivity onSearchItemBackKey")
                rightKeyPressed = false
                val translation: ObjectAnimator =
                    ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", 0f)
                translation.duration = 500
                translation.start()
                mViewBinding?.keyboardDeleteRoot?.requestFocus()
            }

        })

        mViewBinding?.fullT9Layout?.setOnFocusDownListener {
            mViewBinding?.keyboardT9?.requestFocus()
        }

        mViewBinding?.fullT9Layout?.setOnClickTNineKeyboardListener { content ->
            LibDeprecatedLogger.d(content)
            when (content) {
                "back" -> {
                    mViewBinding?.searchEditText?.let {
                        if (it.text.toString().isNotEmpty()) {
                            if (it.text.toString().length == 1) {
                                mViewBinding?.searchKeyboardDeletePop?.gone()
                                isFirstTimeShowTips = false
                            }
                            it.text =
                                it.text.toString().subSequence(0, it.text.toString().length - 1)
                        } else {
                            finish()
                        }
                    }
                }

                else -> {
                    mViewBinding?.fullT9PopLayout?.visible()
                    mViewBinding?.fullT9PopLayout?.show(
                        mViewBinding?.searchEditText?.text.toString(),
                        content
                    )
                }
            }
        }

        mViewBinding?.fullT9PopLayout?.settTNinePopLayoutListener(object :
           TNinePopLayoutListener {
            override fun onPressBack(content: String?) {
                LibDeprecatedLogger.d("fullT9PopLayout onPressBack $content")
                if (mViewBinding?.fullT9PopLayout?.visibility == View.VISIBLE) {
                    mViewBinding?.fullT9PopLayout?.gone()
                    mViewBinding?.fullT9Layout?.setFocusView(content)

                }
            }

            override fun onPressButton(content: String?) {
                LibDeprecatedLogger.d("fullT9PopLayout onPressButton $content")
                mViewBinding?.searchEditText?.let {
                    if (it.text.length < SEARCH_TEXT_MAX_LENGTH) {
                        it.append(content)
                    }
                    if (it.text.length > 3) {
                        mViewBinding?.searchKeyboardDeletePop?.gone()
                        isFirstTimeShowTips = false
                    } else if (isFirstTimeShowTips && it.text.length <= 3) {
                        mViewBinding?.searchKeyboardDeletePop?.visible()
                    }
                }
                if (mViewBinding?.fullT9PopLayout?.visibility == View.VISIBLE) {
                    mViewBinding?.fullT9PopLayout?.gone()
                    mViewBinding?.fullT9PopLayout?.setContentTV(content)
                    mViewBinding?.fullT9Layout?.setFocusView(content)
                }
            }
        })

        mViewBinding?.fullKeyboardLayout?.setOnFocusDownListener {
            mViewBinding?.keyboardFull?.requestFocus()
        }

        mViewBinding?.fullKeyboardLayout?.setOnClickFullKeyboardListener { content ->
            LibDeprecatedLogger.d(content)
            when (content) {
                "back" -> {
                    mViewBinding?.searchEditText?.let {
                        if (it.text.toString().isNotEmpty()) {
                            if (it.text.toString().length == 1) {
                                mViewBinding?.searchKeyboardDeletePop?.gone()
                                isFirstTimeShowTips = false
                            }
                            it.text =
                                it.text.toString().subSequence(0, it.text.toString().length - 1)
                        } else {
                            finish()
                        }
                    }
                }

                else -> {
                    mViewBinding?.searchEditText?.let {
                        if (it.text.length < SEARCH_TEXT_MAX_LENGTH) {
                            it.append(content)
                        }
                        if (it.text.length > 3) {
                            mViewBinding?.searchKeyboardDeletePop?.gone()
                            isFirstTimeShowTips = false
                        } else if (isFirstTimeShowTips && it.text.length <= 3) {
                            mViewBinding?.searchKeyboardDeletePop?.visible()
                        }
                    }
                }
            }
        }


    }

    fun initData() {

        //初始化历史和热搜数据
        lifecycleScope.launch {
            searchViewModel.searchHotList(this@SearchActivity)
            searchViewModel.getSearchHistory(this@SearchActivity)

            launch {
                searchViewModel.hotSearchList.collect { list ->
                    hotSearchArrayAdapter?.clear()
                    hotSearchArrayAdapter?.addAll(0, list)
                    mViewBinding?.searchHotList?.layoutParams?.height =
                        Math.ceil(list.size / 2.0).toInt() * 90 + 48
                    mViewBinding?.searchHotList?.requestLayout()
                    if (list.isNotEmpty()) {
                        SearchRequestManager.hotSearchExposure(
                            true,
                            list.joinToString(";") { it.name })
                    }
                }
            }

            launch {
                searchViewModel.searchHistoryList.collect { list ->
                    if (list.isNotEmpty()) {
                        searchHistoryArrayAdapter?.clear()
                        searchHistoryArrayAdapter?.addAll(0, list)
                        mViewBinding?.historyList?.visibility = View.VISIBLE
                        mViewBinding?.historyTitle?.visibility = View.VISIBLE
                        mViewBinding?.historyClear?.visibility = View.VISIBLE
                        mViewBinding?.historyList?.layoutParams?.height =
                            Math.ceil(list.size / 2.0).toInt() * 90 + 48
                        mViewBinding?.historyList?.requestLayout()
                        SearchRequestManager.searchHistoryExposure(list.joinToString(";") { it.albumTitle })
                    } else {
                        mViewBinding?.historyList?.visibility = View.GONE
                        mViewBinding?.historyTitle?.visibility = View.GONE
                        mViewBinding?.historyClear?.visibility = View.GONE
                    }
                }
            }
        }


        //搜索联想词
        mViewBinding?.searchEditText?.debounce()?.distinctUntilChanged()?.launchIn {
            if (it.isEmpty()) {
                //搜索框为空时显示历史和热搜
//                mViewBinding?.searchResultActivity?.clearData()
                mViewBinding?.keyboardRoot?.setAutoFocus(true)
                mViewBinding?.searchHotHistoryRoot?.visibility = View.VISIBLE
                mViewBinding?.searchAssociateRoot?.visibility = View.GONE
//                mViewBinding?.searchResultActivity?.visibility = View.GONE
                mViewBinding?.searchResultRight?.removeAllViews()
                mViewBinding?.searchKeyboardDeletePop?.visibility = View.GONE
            } else {
                mViewBinding?.keyboardRoot?.setAutoFocus(false)
                //搜索框不为空时显示联想词
                if (it.length <= 3 && isFirstTimeShowTips) {
                    //第一次搜索时显示删除键提示
                    mViewBinding?.searchKeyboardDeletePop?.visibility = View.VISIBLE
                } else if (mViewBinding?.searchKeyboardDeletePop?.visibility == View.VISIBLE) {
                    mViewBinding?.searchKeyboardDeletePop?.visibility = View.GONE
                    isFirstTimeShowTips = false
                }

                LibDeprecatedLogger.d("当前搜索:$it")


                val time = System.currentTimeMillis()
                scope?.cancel() // 发起新的请求前取消旧的请求, 避免旧数据覆盖新数据
                //请求联想词接口
                scope = scopeNetLife {
                    val result =
                        Get<SearchAssociate>("${RetrofitApi.get().retrofitHost.baseHost}search/searchAssociate.json") {
                            param("key", it)
                            param("count", 30)
                            param("timeStamp", time)
                            param("code", md5(it + time + "09606ac70454ce82"))
                            setHeaders(HeaderHelper.getHeaders().toHeaders())
                        }.await()

                    LibDeprecatedLogger.d(result.data.suggests.toString())

                    searchAssociateArrayAdapter?.clear()

                    mViewBinding?.searchHotHistoryRoot?.visibility = View.GONE
                    mViewBinding?.searchAssociateRoot?.visibility = View.VISIBLE
//                    mViewBinding?.searchResultActivity?.visibility = View.VISIBLE
                    setHighlightedText(
                        mViewBinding?.searchAssociateTitle,
                        "\u201c${formatString(it)}\u201d",
                        "猜你想搜\u201c${formatString(it)}\u201d"
                    )
                    if (result.data.suggests.isNotEmpty()) {
                        //有联想词时显示联想词布局
                        searchAssociateArrayAdapter?.addAll(
                            0,
                            if (result.data.suggests.size > 20) result.data.suggests.subList(
                                0,
                                20
                            ) else result.data.suggests
                        )
                        mViewBinding?.searchAssociateList?.layoutParams?.height = resources.getDimensionPixelOffset(R.dimen.y890)
//                        showSearchResult(result.data.suggests[0].keyword, true)
                        mViewBinding?.searchAssociateNoResult?.gone()
                        mViewBinding?.searchAssociateHotTitle?.gone()

                        suggestsList = result.data.suggests
                        SearchRequestManager.guessSearchExposure(
                            "1",
                            it,
                            result.data.suggests.joinToString(";") { it.keyword })
                    } else {
                        //无联想词时显示无结果布局，数据替换为热搜
                        mViewBinding?.searchAssociateNoResult?.visible()
                        mViewBinding?.searchAssociateHotTitle?.visible()
                        mViewBinding?.searchAssociateList?.layoutParams?.height = resources.getDimensionPixelOffset(R.dimen.y580)

                        searchAssociateArrayAdapter?.clear()
                        searchAssociateArrayAdapter?.addAll(0, searchViewModel.hotSearchList.value)

                        SearchRequestManager.hotSearchExposure(
                            false,
                            searchViewModel.hotSearchList.value.joinToString(";") { it.name })
                        SearchRequestManager.guessSearchExposure("0", it, "")
                    }
                }
            }
        }

    }

    //格式化搜索结果标题
    private fun formatString(input: String): String {
        return if (input.length > 4) {
            input.substring(0, 3) + "..." + input.last()
        } else {
            input
        }
    }

    //设置搜索结果标题高亮
    private fun setHighlightedText(textView: TextView?, keyString: String, titleString: String) {
        // 查找关键字在标题中的位置
        val startIndex = titleString.indexOf(keyString)
        val endIndex = startIndex + keyString.length

        // 如果关键字存在于标题中
        if (startIndex >= 0) {
            val spannableString = SpannableString(titleString)

            // 设置关键字部分的字体颜色为红色，其余部分为白色
            spannableString.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF6247")),
                startIndex,
                endIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannableString.setSpan(
                ForegroundColorSpan(Color.parseColor("#E8E8FF")),
                0,
                startIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannableString.setSpan(
                ForegroundColorSpan(Color.parseColor("#E8E8FF")),
                endIndex,
                titleString.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // 将带有颜色的字符串设置为 TextView 的文本
            textView?.text = spannableString
        } else {
            // 如果关键字不存在于标题中，将整个标题设置为白色
            textView?.setTextColor(Color.WHITE)
            textView?.text = titleString
        }
    }


    fun md5(input: String): String {
        return try {
            val messageDigest = MessageDigest.getInstance("MD5")
            messageDigest.update(input.toByteArray())
            val digest = messageDigest.digest()
            val bigInteger = BigInteger(1, digest)
            String.format("%032x", bigInteger)
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            ""
        }
    }

    override fun provideSearchHistoryDatabase(): DaoSession {
        return DaoSessionInstance.getDaoSession(applicationContext)
    }

    override fun onKeyEvent(event: KeyEvent): Boolean {
        val fragment = supportFragmentManager.findFragmentByTag("SearchResultFragment")
        return if (fragment is KeyEventHandler) {
            fragment.onKeyEvent(event)
        } else {
            false
        }
    }


    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.keyboard_delete_root -> {
                mViewBinding?.searchEditText?.text.let {
                    if (it.toString().isNotEmpty()) {
                        if (it.toString().length == 1) {
                            mViewBinding?.searchKeyboardDeletePop?.gone()
                            isFirstTimeShowTips = false
                        }
                        mViewBinding?.searchEditText?.text =
                            it.toString().subSequence(0, it.toString().length - 1)
                    }
                }
                SearchRequestManager.searchHomeDeleteClick()

            }

            R.id.keyboard_clear_root -> {
                mViewBinding?.searchEditText?.text = ""
                mViewBinding?.searchKeyboardDeletePop?.gone()
                isFirstTimeShowTips = false
                SearchRequestManager.searchHomeClearClick()
            }

            R.id.history_clear -> {
                searchViewModel.clearHistory(this)
                SearchRequestManager.searchHistoryClearClick()
            }
        }
    }

    override fun onFocusChange(v: View?, hasFocus: Boolean) {
        mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
        when (v?.id) {
            R.id.keyboard_full -> {

                if (mViewBinding?.fullKeyboardLayout?.visibility == View.VISIBLE) {

                } else {
                    mViewBinding?.fullKeyboardLayout?.visible()
                    mViewBinding?.fullT9Layout?.gone()
                    SearchRequestManager.keyBoardExposure("0")
                }
                mViewBinding?.keyboardFull?.isSelected = true
                mViewBinding?.keyboardT9?.isSelected = false
                mViewBinding?.keyboardFull?.bringToFront()
            }

            R.id.keyboard_T9 -> {
                if (mViewBinding?.fullT9Layout?.visibility == View.VISIBLE) {

                } else {
                    mViewBinding?.fullKeyboardLayout?.gone()
                    mViewBinding?.fullT9Layout?.visible()

                    SearchRequestManager.keyBoardExposure("1")
                }

                mViewBinding?.keyboardT9?.isSelected = true
                mViewBinding?.keyboardT9?.bringToFront()
                mViewBinding?.keyboardFull?.isSelected = false

            }
        }
    }

    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.action == KeyEvent.ACTION_DOWN) {
            if (event.keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                LibDeprecatedLogger.d("SearchActivity dispatchKeyEvent KEYCODE_DPAD_LEFT : ${mViewBinding?.root?.translationX}")
                LibDeprecatedLogger.d("SearchActivity dispatchKeyEvent KEYCODE_DPAD_LEFT : $currentFocus")
                if (isDescendantView(
                        currentFocus,
                        mViewBinding?.keyboardRoot?.id
                    ) && mViewBinding?.root?.translationX == -1190.0f
                ) {

                    if (mViewBinding?.searchResultRight?.childCount!! > 0) {
                        val translation: ObjectAnimator =
                            ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", 0f)
                        translation.duration = 500
                        translation.start()
                        mViewBinding?.searchAssociateList?.requestFocus()
                        return true
                    }
                }
            } else if (event.keyCode == KeyEvent.KEYCODE_BACK) {
                if (isDescendantView(
                        currentFocus,
                        mViewBinding?.keyboardRoot?.id
                    ) && mViewBinding?.root?.translationX == -1190.0f
                ) {
                    if (mViewBinding?.searchResultRight?.childCount!! > 0) {
                        val translation: ObjectAnimator =
                            ObjectAnimator.ofFloat(mViewBinding?.root, "translationX", 0f)
                        translation.duration = 500
                        translation.start()
                        mViewBinding?.searchAssociateList?.requestFocus()
                        return true
                    }
                }
            }

        }
        return super.dispatchKeyEvent(event)
    }

    private fun isDescendantView(view: View?, parentId: Int?): Boolean {
        var currentView: View? = view
        while (currentView != null) {
            if (currentView.id == parentId) {
                return true
            }
            currentView = currentView.parent as? View
        }
        return false
    }

    override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
        if (event?.action == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DPAD_UP) {
            mViewBinding?.fullT9Layout?.requestBottomLayout()
            return true
        }
        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        ActivityManagerUtil.removeActivityFromList(this)
    }

}