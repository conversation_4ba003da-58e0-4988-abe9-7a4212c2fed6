package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.leanback.widget.Presenter;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.google.gson.Gson;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.model.HomeRecommend;
import com.sohuott.tv.vod.widget.CornerTagImageView;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;


public class TypeOneContentPresenter extends Presenter {
    private Context mContext;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_one_layout, parent, false);
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppLogger.d("TypeOneContentPresenter:onBindViewHolder -> onclick");
            }
        });

        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }
        return new ViewHolder(view);
    }


    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, final Object item) {
        final ViewHolder vh = (ViewHolder) viewHolder;
        vh.view.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
                if (hasFocus) {

                    vh.mFocusRoot.setVisibility(View.VISIBLE);
                    vh.mTVFocusEpisode.setVisibility(View.VISIBLE);
                    vh.mEpisodeBg.setVisibility(View.VISIBLE);
                    vh.mNameBg.setVisibility(View.INVISIBLE);
                    vh.mTvTypeTwoName.setVisibility(View.GONE);
                    vh.mFocusPlay.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.showWaveAnimation();
                    if (item instanceof PgcAlbumInfo.DataEntity) {
                        vh.mTvFocusDesc.setVisibility(View.GONE);
                        vh.mTvFocusName.setSingleLine(false);
                    } else if (item instanceof ContentGroup.DataBean.ContentsBean) {

                        if (!((ContentGroup.DataBean.ContentsBean) item).type.equals(StringUtil.toString(HomeRecommendBean.VIDEO_TYPE)) &&
                                !((ContentGroup.DataBean.ContentsBean) item).type.equals(StringUtil.toString(HomeRecommendBean.ALBUM_TYPE))){
                            vh.mFocusPlay.setVisibility(View.GONE);
                            vh.mFocusPlay.cancelWaveAnimation();
                        }
                        if (((ContentGroup.DataBean.ContentsBean) item).dataType == 2) {
                            vh.mTvFocusName.setSingleLine(false);
                        }
                    }
                } else {
                    vh.mFocusRoot.setVisibility(View.GONE);
                    vh.mTVFocusEpisode.setVisibility(View.GONE);
                    vh.mEpisodeBg.setVisibility(View.GONE);
                    vh.mNameBg.setVisibility(View.VISIBLE);
                    vh.mTvTypeTwoName.setVisibility(View.VISIBLE);
                    vh.mFocusPlay.setVisibility(View.GONE);
                }
            }
        });
        if (item instanceof ContentGroup.DataBean.ContentsBean) {

            ContentGroup.DataBean.ContentsBean contentsBean = (ContentGroup.DataBean.ContentsBean) item;

            Glide.with(mContext)
                    .load(contentsBean.picUrl)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeTwoPoster);
            vh.mTvTypeTwoName.setText(contentsBean.name);

            vh.mTVFocusEpisode.setText(Util.getHintTV(contentsBean));
            vh.mTvFocusName.setText(contentsBean.name);

            if (StringUtil.isEmpty(contentsBean.tvComment)) {
                vh.mTvFocusDesc.setVisibility(View.GONE);
            } else {
                vh.mTvFocusDesc.setText(contentsBean.tvComment);
            }


            if (contentsBean.type.equals(StringUtil.toString(HomeRecommend.LABEL_TYPE))) {
                vh.mIvTypeTwoPoster.setCornerType(true);
            } else {
                if (contentsBean.parameter != null && !contentsBean.parameter.isEmpty()) {
                    try {
                        Gson gson = new Gson();
                        HomeRecommendBean.Data.Content.Parameter parameter = gson.fromJson(contentsBean.parameter,
                                HomeRecommendBean.Data.Content.Parameter.class);
                        if (parameter.cornerType != null)
                            vh.mIvTypeTwoPoster.setPgcCornerTypeWithType(Integer.parseInt(parameter.cornerType));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
                if (contentsBean.albumParam == null) return;
                vh.mIvTypeTwoPoster.setCornerTypeWithType(Integer.parseInt(contentsBean.albumParam.tvIsFee),
                        contentsBean.albumParam.tvIsEarly,
                        contentsBean.albumParam.useTicket,
                        contentsBean.albumParam.paySeparate,
                        Integer.parseInt(contentsBean.albumParam.cornerType));
            }

            RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"),
                    contentsBean.pathInfo,
                    contentsBean.objectInfo, null);

        } else if (item instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean){
            ContentGroup.DataBean.ContentsBean.AlbumListBean albumListBean = (ContentGroup.DataBean.ContentsBean.AlbumListBean) item;

            Glide.with(mContext)
                    .load(albumListBean.albumExtendsPic_640_360)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeTwoPoster);
            vh.mTvTypeTwoName.setText(albumListBean.tvName);
            vh.mTVFocusEpisode.setText(Util.getHintTV(albumListBean));
            vh.mTvFocusName.setText(albumListBean.tvName);
            vh.mTvFocusDesc.setText(albumListBean.tvComment);



            if (albumListBean.channelType == Constant.TYPE_VIP) {
                vh.mFocusRoot.setBackgroundResource(R.drawable.item_type_one_vip_focus_selector);
                vh.mFocusView.setBackgroundResource(R.drawable.bg_vip_focus_selector);
            } else {
                vh.mIvTypeTwoPoster.setCornerTypeWithType(albumListBean.tvIsFee,
                        albumListBean.tvIsEarly,
                        albumListBean.useTicket,
                        albumListBean.paySeparate,
                        albumListBean.cornerType);
            }

            if (albumListBean.memoInfo != null) {
                RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"),
                        albumListBean.pathInfo,
                        albumListBean.objectInfo,
                        albumListBean.memoInfo);
            } else {
                RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"),
                        albumListBean.pathInfo,
                        albumListBean.objectInfo, null);
            }

        } else if (item instanceof PgcAlbumInfo.DataEntity) {
            PgcAlbumInfo.DataEntity dataEntity = (PgcAlbumInfo.DataEntity) item;


            Glide.with(mContext)
                    .load(dataEntity.cover320)
                    .transform(new RoundedCorners(mContext.getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(vh.mIvTypeTwoPoster);
            vh.mTvTypeTwoName.setText(dataEntity.videoTitle);

            vh.mTvFocusName.setText(dataEntity.videoTitle);
            vh.mTvFocusDesc.setText(dataEntity.ptitle);
            try {
                vh.mIvTypeTwoPoster.setPgcCornerTypeWithType(Integer.parseInt(dataEntity.cornerType));
            }catch (Exception e) {
                e.printStackTrace();
            }

            RequestManager.getInstance().onAllEvent(new EventInfo(10146, "imp"),
                    dataEntity.pathInfo,
                    dataEntity.objectInfo, null);
        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {
        ViewHolder vh = (ViewHolder)viewHolder;
        Glide.with(vh.mIvTypeTwoPoster.getContext()).clear(vh.mIvTypeTwoPoster);
    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final CornerTagImageView mIvTypeTwoPoster;
        private final TextView mTvTypeTwoName, mTvFocusName, mTvFocusDesc, mTVFocusEpisode;
        private final LinearLayout mFocusRoot;
        private final RippleDiffuse mFocusPlay;
        private final View mFocusView, mEpisodeBg, mNameBg;


        public ViewHolder(View view) {
            super(view);
            mIvTypeTwoPoster = (CornerTagImageView) view.findViewById(R.id.iv_type_two_poster);
            mTvTypeTwoName = (TextView) view.findViewById(R.id.tv_type_two_name);
            mFocusRoot = (LinearLayout) view.findViewById(R.id.type_one_focus_root);
            mTvFocusName = (TextView) view.findViewById(R.id.type_one_focus_name);
            mTvFocusDesc = (TextView) view.findViewById(R.id.type_one_focus_desc);
            mTVFocusEpisode = (TextView) view.findViewById(R.id.type_one_focus_episode);
            mFocusPlay = (RippleDiffuse) view.findViewById(R.id.type_one_focus_play);
            mFocusView = view.findViewById(R.id.type_one_focus);
            mEpisodeBg = view.findViewById(R.id.focus_episode_bg);
            mNameBg = view.findViewById(R.id.name_bg);
        }
    }
}
