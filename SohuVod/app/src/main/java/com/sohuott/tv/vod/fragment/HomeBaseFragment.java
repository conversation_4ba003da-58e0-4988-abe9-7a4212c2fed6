package com.sohuott.tv.vod.fragment;

import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.model.HomeLabel;
import com.sohuott.tv.vod.model.HomeRecommend;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Created by fenglei on 16-4-1.
 */
public class HomeBaseFragment extends BaseFragment {

    private static final String TAG = HomeBaseFragment.class.getSimpleName();

    protected View mRootView;

    protected LoadingView mLoadingView;

    protected TextView mErrorTV;

    protected boolean mIsFirst;

    protected long mChannelId;
    protected int mPosition;
    protected int mFirstContentPos;
    protected int mTabType;
    protected int mOttCategoryId;
    protected long mCateCode;
    protected int mSubClassifyId;

    private long updateDelay;

    // 标志位 标志已经初始化完成。
    protected boolean isPrepared;

    //标志位 fragment是否可见
    protected boolean isVisible;
    protected boolean isInited;
    protected boolean isUpdate;
    protected boolean isUiWithData = false;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppLogger.d(TAG, "onCreate  this ? " + this);
        mIsFirst = true;
        mChannelId = getArguments().getLong("id");
        AppLogger.d(TAG, "onCreate  mChannelId ? " + mChannelId);
        mPosition = getArguments().getInt("position");
        mFirstContentPos = getArguments().getInt("firstContentPos");
        mTabType = getArguments().getInt("type");
        mOttCategoryId = getArguments().getInt("ottCategoryId");
        mCateCode = getArguments().getLong("cateCode");
        mSubClassifyId = getArguments().getInt("subClassifyId");
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        AppLogger.d(TAG, "onViewCreated  this ? " + this);
        initLoadingView();
        initErrorTV();
        isPrepared = true;
        isInited = true;
        lazyLoad();
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        AppLogger.d(TAG, "onAttach  this ? " + this);
    }

    @Override
    public void onDetach() {
        super.onDetach();
        AppLogger.d(TAG, "onDetach  this ? " + this);
    }

    @Override
    public void onResume() {
        super.onResume();
        AppLogger.d(TAG, "onResume  this ? " + this);
    }


    @Override
    public void onPause() {
        super.onPause();
        AppLogger.d(TAG, "onPause  this ? " + this);
        mIsFirst = false;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        AppLogger.d(TAG, "setUserVisibleHint  this ? " + this);
        if (getUserVisibleHint()) {
            isVisible = true;
            onVisible();
        } else {
            isVisible = false;
            onInvisible();
        }
    }

    protected void onVisible() {
        AppLogger.d(TAG, "onVisible  this ? " + this);
        lazyLoad();
    }

    protected void lazyLoad() {
        AppLogger.d(TAG, "lazyLoad  this ? " + this);

    }

    protected void onInvisible() {
        AppLogger.d(TAG, "onInvisible  this ? " + this);

    }

    protected void setUpdateDelay() {
        Random random = new Random();
        int delayRandom = Math.abs(random.nextInt()) % 10000;
        updateDelay = 1 * 60 * 1000 + delayRandom;
    }

    protected long getUpdateDelay() {
        return updateDelay;
    }

    //第一次进入时fragment内容加载出来后再设置焦点并显示内容
//    protected void showActivityContent(View focusView) {
//        HomeActivity homeActivity = (HomeActivity)getActivity();
//        if(homeActivity != null && homeActivity.isFirst()) {
//            if(focusView != null && focusView.isFocusable()) {
//                focusView.requestFocus();
//            }
//            homeActivity.setContentVisible();
//            homeActivity.setFirst(false);
//        }
//    }

    private void initLoadingView() {
        if(mRootView != null) {
            mLoadingView = (LoadingView)mRootView.findViewById(R.id.detail_loading_view);
        }
    }

    private void initErrorTV() {
        if(mRootView != null) {
            mErrorTV = (TextView)mRootView.findViewById(R.id.error_tv);
        }
    }

    protected void showLoadingView() {
        if(mLoadingView != null) {
            mLoadingView.show();
        }
    }

    protected void hideLoadingView() {
        if(mLoadingView != null) {
            mLoadingView.hide();
        }
    }

    protected void showErrorTV() {
        showErrorTV(getString(R.string.home_loading_error));
    }

    protected void showErrorTV(String errorString) {
        if(mErrorTV != null) {
            mErrorTV.setText(errorString);
            mErrorTV.setVisibility(View.VISIBLE);
        }
    }

    protected void hideErrorTV() {
        if(mErrorTV != null) {
            mErrorTV.setVisibility(View.GONE);
        }
    }

    protected List<HomeRecommend.HomeRecommendItem> getRecommendItemList(
            HomeRecommend homeRecommend) {
        List<HomeRecommend.HomeRecommendItem> homeRecommendItemList = null;
        if (homeRecommend != null && homeRecommend.data != null) {
            for (int i = 0; i < homeRecommend.data.size(); i++) {
                HomeRecommend.HomeRecommendData homeRecommendData = homeRecommend.data.get(i);
                if (homeRecommendData != null) {
                    if ("1".equals(homeRecommendData.type)) {
                        homeRecommendItemList = homeRecommendData.contents;
                        break;
                    }
                }
            }
        }
        return homeRecommendItemList;
    }

    protected List<HomeLabel.HomeLabelItem> getLabelItemList(HomeLabel homeLabel) {
        ArrayList<HomeLabel.HomeLabelItem> labelItemList = null;
        if (homeLabel != null && homeLabel.data != null) {
            labelItemList = homeLabel.data.result;
        }
        return labelItemList;
    }

    protected int getTotalLabelPage(HomeLabel homeLabel, int labelPageSize) {
        int page = 0;
        if (homeLabel != null && homeLabel.data != null) {
            int totalCount = homeLabel.data.count;
            page = totalCount / labelPageSize;
            if(totalCount % labelPageSize != 0) {
                page = page + 1;
            }
            return page;
        }
        return page;
    }

    protected HomeRecommend.HomeRecommendItem findRecommendItemByType(
            List<HomeRecommend.HomeRecommendItem> homeRecommendItemList, int type) {
        if (homeRecommendItemList != null) {
            for (int i = 0; i < homeRecommendItemList.size(); i++) {
                try {
                    int recommendType = Integer.parseInt(homeRecommendItemList.get(i).type);
                    if (recommendType == type) {
                        return homeRecommendItemList.get(i);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public View getDefaultUpFocusView(){
        return null;
    }

    public void onPageScrollStateStopped(){

    }

    public void onPageScrollDragging(){

    }

    public void reloadData(){

    }

//    protected void sortRecommendItemByType(List<HomeRecommend.HomeRecommendItem> homeRecommendItemList) {
//        if (homeRecommendItemList != null && homeRecommendItemList.size() > 0) {
//            Collections.sort(homeRecommendItemList, new SortByType());
//        }
//    }
//
//    class SortByType implements Comparator {
//        public int compare(Object o1, Object o2) {
//            HomeRecommend.HomeRecommendItem item1 = (HomeRecommend.HomeRecommendItem) o1;
//            HomeRecommend.HomeRecommendItem item2 = (HomeRecommend.HomeRecommendItem) o2;
//            try {
//                int type1 = Integer.parseInt(item1.type);
//                int type2 = Integer.parseInt(item2.type);
//                if (type1 < type2) {
//                    return 1;
//                } else if (type1 > type2) {
//                    return -1;
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            return 0;
//        }
//    }

}
