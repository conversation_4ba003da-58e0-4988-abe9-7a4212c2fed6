package com.sohuott.tv.vod.videodetail.vlist;

import android.content.Context;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.Pair;
import android.view.KeyEvent;
import android.view.View;
import android.view.animation.AnimationUtils;

import com.alibaba.android.vlayout.DelegateAdapter;
import com.alibaba.android.vlayout.LayoutHelper;
import com.alibaba.android.vlayout.Range;
import com.alibaba.android.vlayout.layout.GridLayoutHelper;
import com.alibaba.android.vlayout.layout.LinearLayoutHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.util.List;

import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_TYPE_INTRO;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_TYPE_SPECIAL;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_TYPE_TITLE;
import static com.sohuott.tv.vod.lib.utils.Constant.DETAIL_VLIST_TYPE_TRAILER;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by XiyingCao on 2018/3/19.
 */

public class VLayoutRecyclerView extends RecyclerView {
    protected FocusBorderView mFocusBorderView;
    protected boolean mIsLongPress;
    protected VlayoutManager mLayoutManager;
    protected VLayoutScroller mScroller;
    protected float mLastFocusX;
    protected int mSelectPos;
    protected int mTargetPos;
    protected DelegateAdapter mAdapter;

    public VLayoutRecyclerView(Context context) {
        super(context);
        init();
    }

    public VLayoutRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public VLayoutRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        setChildrenDrawingOrderEnabled(true);
        setItemViewCacheSize(0);
        mLayoutManager = new VlayoutManager(getContext());
        mLayoutManager.setRecycleChildrenOnDetach(true);
        setLayoutManager(mLayoutManager);
        //设置适配器
        mAdapter = new DelegateAdapter(mLayoutManager, true);
        setAdapter(mAdapter);
    }

    public void handleFocus() {
        ViewHolder viewHolder = findViewHolderForAdapterPosition(mTargetPos);
        if (viewHolder != null && viewHolder.itemView.findViewById(R.id.focus) != null) {
            viewHolder.itemView.findViewById(R.id.focus).requestFocus();
        }
        mSelectPos = mTargetPos;
    }

    public boolean hitBorder(int keycode) {
        switch (keycode) {
            case KeyEvent.KEYCODE_DPAD_DOWN:
                if (hitBotton()) {
                    if (findFocus() != null && (findFocus().getAnimation() == null || findFocus().getAnimation().hasEnded())) {
                        findFocus().startAnimation(AnimationUtils.loadAnimation(getContext(),
                                R.anim.shake_y));
                        mFocusBorderView.startAnimation(AnimationUtils.loadAnimation(getContext(),
                                R.anim.shake_y));
                    }
                    return true;
                }
                break;
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                if (hitLast()) {
                    if (findFocus() != null && (findFocus().getAnimation() == null || findFocus().getAnimation().hasEnded())) {
                        findFocus().startAnimation(AnimationUtils.loadAnimation(getContext(),
                                R.anim.shake_y));
                        mFocusBorderView.startAnimation(AnimationUtils.loadAnimation(getContext(),
                                R.anim.shake_y));
                    }
                    return true;
                }
                break;
            default:
                break;
        }
        return false;
    }

    private boolean hitLast() {
        if (mSelectPos == mAdapter.getItemCount() - 1 && getScrollState() == SCROLL_STATE_IDLE) {
            return true;
        }
        return false;
    }

    private boolean hitBotton() {
        LinearLayoutHelper nextHelper = (LinearLayoutHelper) mLayoutManager.getLayoutHelpers()
                .get(mLayoutManager.getLayoutHelpers().size() - 1);
        Range<Integer> range = nextHelper.getRange();
//        int first = range.getLower() + (nextHelper.getItemCount() - 1) / nextHelper.getSpanCount() * nextHelper.getSpanCount();
        if (range.getUpper() >= mSelectPos && getScrollState() == SCROLL_STATE_IDLE) {
            return true;
        }
        return false;
    }

    /**
     * 以向上对齐的方法滑动列表，不需滑动矫正
     *
     * @param target 滑动目标
     */
    public void scrollToPositionAlignTop(int target) {
        scrollToPositionAlignTop(target, target);
    }

    /**
     * 以向上对齐的方法滑动列表，需滑动矫正
     *
     * @param focusPos  焦点目标位置
     * @param scrollPos 滑动目标位置
     */
    public void scrollToPositionAlignTop(int focusPos, int scrollPos) {
        LibDeprecatedLogger.d("customScrollToPosAlignTop pos is " + focusPos + ", scrollPos is " + scrollPos);
        if (mScroller.isRunning() || focusPos == -1) {
            LibDeprecatedLogger.d("customScrollToPos running return");
            return;
        }
        //滑动开始取消焦点显示
        mTargetPos = focusPos;
        actualScrollToPosition(scrollPos);
    }

    public void scrollToPositionAfterLongPress(int focusPos, int scrollPos) {
        LibDeprecatedLogger.d("customScrollToPosAfterLongPres pos is " + focusPos + ", scrollPos is " + scrollPos);
        if (focusPos == -1) {
            LibDeprecatedLogger.d("customScrollToPos running return");
            return;
        }
        //滑动开始取消焦点显示
        mTargetPos = focusPos;
        actualScrollToPosition(scrollPos);
    }

    private void actualScrollToPosition(int scrollPos) {
        LibDeprecatedLogger.d("actualScrollToPosition  pos is " + scrollPos);
        if (!mScroller.needScroll(scrollPos)) {
            //不需要滑动直接处理焦点变化
            LibDeprecatedLogger.d("actualScrollToPosition  not need to scroll");
            handleFocus();
        } else {
            //清楚焦点状态后开始滑动
            LibDeprecatedLogger.d("actualScrollToPosition scroll");
            mFocusBorderView.clearFocus();
            mScroller.setTargetPosition(scrollPos);
            mLayoutManager.startSmoothScroll(mScroller);
        }
    }

    public void forceScroll(int scrollPos) {
        if (mFocusBorderView != null) {
            mFocusBorderView.clearFocus();
        }
        if (mScroller != null) {
            mScroller.setTargetPosition(scrollPos);
        }
        if (mLayoutManager != null) {
            mLayoutManager.startSmoothScroll(mScroller);
        }
    }

    /**
     * 查找短按键时下一个焦点位置
     *
     * @param keycode
     * @param focusPos 当前焦点位置
     * @return
     */
    public Pair findNextFocusPos(int keycode, int focusPos, View focus) {
        int nextPos = 0;
        Object nextPosObj;
        List<LayoutHelper> layoutHelpers = mLayoutManager.getLayoutHelpers();
        int helperIndex = (int) focus.getTag(R.id.video_detail_recommend_adapter_index);
        int nextHelperIndex = helperIndex;

        switch (keycode) {
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                //如果已经是最后一个item不再移动焦点
                if (focusPos >= getAdapter().getItemCount() - 1) {
                    return getScrollPos(focusPos, helperIndex);
                }
                nextPosObj = focus.getTag(R.id.video_detail_recommend_focus_right_abs_pos);
                if (nextPosObj == null) {
                    return new Pair(-1, -1);
                }
                nextPos = (int) nextPosObj;
                //如果新焦点已经不再当前helper的范围内，需要更新helperIndex
                if (nextPos == -1) {
                    //忽略标题item
                    if (layoutHelpers.get(helperIndex + 1).getZIndex() == DETAIL_VLIST_TYPE_TITLE) {
                        return getScrollPos(focusPos + 2, helperIndex + 2);
                    } else {
                        return getScrollPos(focusPos + 1, helperIndex + 1);
                    }
                }
                return getScrollPos(nextPos, helperIndex);
            case KeyEvent.KEYCODE_DPAD_LEFT:
                nextPosObj = focus.getTag(R.id.video_detail_recommend_focus_left_abs_pos);
                if (nextPosObj == null) {
                    return new Pair(-1, -1);
                }
                nextPos = (int) nextPosObj;
                //如果新焦点已经不再当前helper的范围内
                if (nextPos == -1) {
                    //忽略标题item
                    if (layoutHelpers.get(helperIndex - 1).getZIndex() == DETAIL_VLIST_TYPE_TITLE) {
                        return getScrollPos(focusPos - 2, helperIndex - 2);
                    } else {
                        return getScrollPos(focusPos - 1, helperIndex - 1);
                    }
                }
                return getScrollPos(nextPos, helperIndex);
            case KeyEvent.KEYCODE_DPAD_DOWN:
                nextPosObj = focus.getTag(R.id.video_detail_recommend_focus_down_abs_pos);
                if (nextPosObj == null) {
                    return new Pair(-1, -1);
                }
                nextPos = (int) nextPosObj;
                //如果新焦点已经不再当前helper的范围内
                if (nextPos == -1) {
                    //已经是最后整个list中最后一个helper了
                    if (helperIndex == mLayoutManager.getLayoutHelpers().size() - 1) {
                        return getScrollPos(focusPos, helperIndex);
                    } else {
                        //找到下一个helper中对应位置
                        if (layoutHelpers.get(helperIndex + 1).getZIndex() == DETAIL_VLIST_TYPE_TITLE) {
                            nextHelperIndex += 2;
                            LibDeprecatedLogger.d("findNextFocusPos down next helper is title " + nextHelperIndex);
                        } else {
                            nextHelperIndex++;
                            LibDeprecatedLogger.d("findNextFocusPos down next helper is not title" + nextHelperIndex);
                        }
                        if (nextHelperIndex > layoutHelpers.size() - 1) {
                            return getScrollPos(focusPos, helperIndex);
                        }
                        GridLayoutHelper nextHelper = (GridLayoutHelper) layoutHelpers.get(nextHelperIndex);
                        Range<Integer> range = nextHelper.getRange();
                        focusPos = range.getLower() + (int) (nextHelper.getSpanCount() * mLastFocusX);
                        if (range.getUpper() < focusPos) {
                            return getScrollPos(range.getUpper(), nextHelperIndex);
                        }
                        return getScrollPos(focusPos, nextHelperIndex);
                    }
                }
                return getScrollPos(nextPos, helperIndex);
            case KeyEvent.KEYCODE_DPAD_UP:
                nextPosObj = focus.getTag(R.id.video_detail_recommend_focus_up_abs_pos);
                if (nextPosObj == null) {
                    return new Pair(-1, -1);
                }
                nextPos = (int) nextPosObj;
                //如果新焦点已经不再当前helper的范围内
                if (nextPos == -1) {
                    if (helperIndex < 1) {
                        return getScrollPos(focusPos, helperIndex);
                    }
                    //找到上一个helper中对应位置
                    if (layoutHelpers.get(helperIndex - 1).getZIndex() == DETAIL_VLIST_TYPE_TITLE) {
                        nextHelperIndex -= 2;
                    } else {
                        nextHelperIndex--;
                    }
                    if (nextHelperIndex < 0) {
                        return getScrollPos(focusPos, helperIndex);
                    }

                    GridLayoutHelper nextHelper = (GridLayoutHelper) layoutHelpers.get(nextHelperIndex);
                    Range<Integer> range = nextHelper.getRange();
                    focusPos = range.getLower() + (nextHelper.getItemCount() - 1) / nextHelper.getSpanCount() * nextHelper.getSpanCount()
                            + (int) (nextHelper.getSpanCount() * mLastFocusX);
                    if (range.getUpper() < focusPos) {
                        return getScrollPos(range.getUpper(), nextHelperIndex);
                    }
                    return getScrollPos(focusPos, nextHelperIndex);
                }
                return getScrollPos(nextPos, helperIndex);
            default:
                return getScrollPos(focusPos, helperIndex);
        }
    }

    /**
     * 滑动结束后，当前获取焦点的位置
     *
     * @param isDown 是否是向下滑动
     * @return 返回 -1 数据异常
     */
    public Pair<Integer, Integer> getAfterLongKeyFocusPos(boolean isDown) {
        int pos, helperIndex;
        if (isDown) {
            pos = mLayoutManager.findLastCompletelyVisibleItemPosition();
            ViewHolder viewHolder = findViewHolderForAdapterPosition(pos);
            if (viewHolder == null) {
                return new Pair(-1, -1);
            }
            helperIndex = (Integer) viewHolder.itemView.getTag(R.id.video_detail_recommend_adapter_index);
            switch (viewHolder.getItemViewType()) {
                case DETAIL_VLIST_TYPE_TITLE:
                    if (helperIndex < mLayoutManager.getLayoutHelpers().size() - 1) {
                        pos++;
                        helperIndex++;
                    }
                    break;
                case DETAIL_VLIST_TYPE_INTRO:
                case DETAIL_VLIST_TYPE_TRAILER:
                    return new Pair<>(pos, pos);
                default:
                    break;
            }
        } else {
            pos = mLayoutManager.findFirstVisibleItemPosition();
            ViewHolder viewHolder = findViewHolderForAdapterPosition(pos);
            if (viewHolder == null) {
                return new Pair(-1, -1);
            }
            helperIndex = (Integer) viewHolder.itemView.getTag(R.id.video_detail_recommend_adapter_index);
            switch (viewHolder.getItemViewType()) {
                case DETAIL_VLIST_TYPE_TITLE:
                    if (pos > 0) {
                        pos--;
                        helperIndex--;
                    }
                    break;
                case DETAIL_VLIST_TYPE_INTRO:
                case DETAIL_VLIST_TYPE_TRAILER:
                    return new Pair<>(pos, pos);
                default:
                    break;
            }
        }
        return getScrollPos(getFocusPosAfterScroll(pos, helperIndex, isDown), helperIndex);
    }

    private int getFocusPosAfterScroll(int pos, int helperIndex, boolean isDown) {
        GridLayoutHelper helper = (GridLayoutHelper) mLayoutManager.getLayoutHelpers().get(helperIndex);
        Range<Integer> range = helper.getRange();
        int colNum = (pos - range.getLower()) / helper.getSpanCount();
        if (isDown) {
            if (colNum == (helper.getItemCount() - 1) / helper.getSpanCount() && helperIndex == mLayoutManager.getLayoutHelpers().size() - 1) {
                return range.getUpper();
            }
        } else {
            if (helperIndex == 0 && colNum == 0) {
                return 0;
            }
        }
        int target = (int) (range.getLower() + helper.getSpanCount() * (mLastFocusX + colNum));
        if (range.getUpper() < target) {
            return range.getUpper();
        }
        return target;
    }

    /**
     * 根据焦点位置计算滑动位置
     *
     * @param focusPos    焦点绝对位置
     * @param helperindex
     * @return
     */
    private Pair<Integer, Integer> getScrollPos(int focusPos, int helperindex) {
        GridLayoutHelper layoutHelper = (GridLayoutHelper) mLayoutManager.getLayoutHelpers().get(helperindex);
        int relativeColNo = (focusPos - layoutHelper.getRange().getLower()) / layoutHelper.getSpanCount();
        if (relativeColNo == 0 && helperindex > 0 && layoutHelper.getZIndex() < DETAIL_VLIST_TYPE_SPECIAL) {
            return new Pair<>(focusPos, layoutHelper.getRange().getLower() - 1);
        } else {
            return new Pair<>(focusPos, layoutHelper.getRange().getLower() + relativeColNo * layoutHelper.getSpanCount());
        }
    }

    public int getTargetPos() {
        return mTargetPos;
    }

    public class VLayoutScroller extends LinearSmoothScroller {
        boolean mIsScrollDown;

        public VLayoutScroller(Context context) {
            super(context);
        }

        @Override
        protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
            return mIsLongPress ? 30f / displayMetrics.densityDpi : 50f / displayMetrics.densityDpi;
        }

        @Override
        protected int getVerticalSnapPreference() {
            return LinearSmoothScroller.SNAP_TO_START;
        }

        public boolean needScroll(int pos) {
            LibDeprecatedLogger.d("needScroll pos is " + pos);
            if (mSelectPos == pos) {
                LibDeprecatedLogger.d("needScroll already there");
                return false;
            }
            // 如果已经滑到边界 -> 不再滑动
            if (mSelectPos > pos && mLayoutManager.findFirstCompletelyVisibleItemPosition() == 0) {
                // scroll up
                LibDeprecatedLogger.d("needScroll already 0");
                return false;
            } else if (mSelectPos < pos && mLayoutManager.findLastCompletelyVisibleItemPosition() == mAdapter.getItemCount()) {
                // scroll down
                LibDeprecatedLogger.d("needScroll already last");
                return false;
            }

            ViewHolder viewHolder = findViewHolderForAdapterPosition(pos);
            if (viewHolder != null) {
                // 如果对应item已经出现在并且已经按要求滑动到第一行 -> 不再滑动
                final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)
                        viewHolder.itemView.getLayoutParams();
                int top = mLayoutManager.getDecoratedTop(viewHolder.itemView) - params.topMargin;
                int start = mLayoutManager.getPaddingTop();
                int dy = start - top;
                if (dy == 0) {
                    LibDeprecatedLogger.d("needScroll dy == 0");
                    return false;
                }
            }
            return true;
        }

        @Override
        public void setTargetPosition(int targetPosition) {
            super.setTargetPosition(targetPosition);
            if (targetPosition > mSelectPos) {
                mIsScrollDown = true;
            } else {
                mIsScrollDown = false;
            }
        }
    }

    @Override
    protected int getChildDrawingOrder(int childCount, int i) {
        View focused = getFocusedChild();
        int focusedPos = indexOfChild(focused);
        if (focusedPos == -1) {
            return i;
        }

        if (focusedPos == i) {
            return childCount - 1;
        } else if (i == childCount - 1) {
            return focusedPos;
        } else {
            return i;
        }
    }
}
