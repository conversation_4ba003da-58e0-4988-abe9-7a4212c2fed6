package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;

/**
 * Created by <PERSON><PERSON><PERSON>ao on 16-1-18.
 */
public class CornerTagImageView extends GlideImageView implements View.OnFocusChangeListener {
    public static final int CORNER_TYPE_NONE = 0;

    //  mCornerType第一种格式：清晰度
    public static final int CORNER_TYPE_4K = 1;
    public static final int CORNER_TYPE_BLUE = 2;
    public static final int CORNER_TYPE_SOHU_ONLY = 3;
    public static final int CORNER_TYPE_SELF_MADE = 4;
    public static final int CORNER_TYPE_MEMBER_FIRST = 5;

    //  mCornerType第二种格式：影片catecode（firstcatecode）
    //  cateCode - 100：电影；101：电视剧；106：综艺；107：纪录片；115：动漫；10001：美剧；
    public static final int CORNER_TYPE_VARIETY = 106;
    public static final int CORNER_TYPE_TV = 101;
    public static final int CORNER_TYPE_COMIC = 115;
    public static final int CORNER_TYPE_MOVIE = 100;
    public static final int CORNER_TYPE_DOCUMENT = 107;
    public static final int CORNER_TYPE_AMERICAN = 10001;
    public static final int CORNER_TYPE_SOHUCLASS = 9999998;

    public static final int CORNER_TYPE_PGC = 212;
    public static final int CORNER_TYPE_VR = 213;

    //  mCornerType第三种格式：影片是否付费（会员）
    public static final int CORNER_TYPE_MEMBER = 20;
    //  mCornerType第四种格式：影片是否用卷（会员T片）
    public static final int CORNER_TYPE_COUPON = 21;

    //  mCornerType第五种格式：标签（专区）
    public static final int CORNER_TYPE_TAG = 22;

    //  mCornerType第六种格式：dts
    public static final int CORNER_TYPE_DTS = 23;
    //  mCornerType第六种格式：单片购买
    public static final int CORNER_TYPE_SINGLE = 24;
    // mCornerType第七种格式：在线课堂付费
    public static final int CORNER_TYPE_EDU = 25;

    //免费标识
    public static final int CORNER_TYPE_FREE = 26;

    //  mTvStype:正片；片花；预告片；MV。。。。。。
    public static final int CORNER_STYPE_ZHENGPIAN = 1;
    public static final int CORNER_STYPE_PREVUE = 2;
    public static final int CORNER_STYPE_BLOOPER = 3;
    public static final int CORNER_STYPE_MAKING = 4;
    public static final int CORNER_STYPE_TRAILIER = 5;
    public static final int CORNER_STYPE_RELATED = 6;
    public static final int CORNER_STYPE_MICROFILM = 34;
    public static final int CORNER_STYPE_MV = 37;

    //  location:默认右上角显示
    public static final int CORNER_LOCATION_RIGHT_UP = 0;
    public static final int CORNER_LOCATION_RIGHT_DOWN = 1;
    public static final int CORNER_LOCATION_LEFT_UP = 2;
    public static final int CORNER_LOCATION_LEFT_DOWN = 3;

    //  mCornerType值取觉与三种值：是否付费，catecode，清晰度
    //  （三者的优先级是付费高于其他两个，catecode&清晰度显示哪个根据产品设计）
    private int mCornerType = CORNER_TYPE_NONE;
    //  mCornerType v.s. mTyStype优先级是 mTvStype高于mCornerType。
    //  如果mTvStype规定该类需要显示对应角标则不显示mCornerType
    private int mTvStype = CORNER_TYPE_NONE;
    private int mCornerLocation = CORNER_LOCATION_RIGHT_UP;

    private Drawable mCornerDrawable = null;
    private float mCornerScale = 0.3f;
    private int mCornerPaddingX = 0;
    private int mCornerPaddingY = 0;
    private int mCornerHeight = 0;

    private LoginUserInformationHelper mHelper;

    public CornerTagImageView(Context context) {
        this(context, null);
    }

    public CornerTagImageView(Context context, AttributeSet attrs) {
        this(context, attrs, R.attr.customCornerStyle);
        if (isFocusable()) {
            setOnFocusChangeListener(this);
        }
    }

    public CornerTagImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mHelper = LoginUserInformationHelper.getHelper(context);
        TypedArray attributesArray = context.obtainStyledAttributes(
                attrs, R.styleable.CornerTagImageView, defStyleAttr, 0);

        mCornerPaddingX = attributesArray.getDimensionPixelSize(R.styleable.CornerTagImageView_cornerPaddingX, 0);
        mCornerPaddingY = attributesArray.getDimensionPixelSize(R.styleable.CornerTagImageView_cornerPaddingY, 0);

        mCornerLocation = attributesArray.getInt(R.styleable.CornerTagImageView_cornerLocation, 0);

    }

    // 只是设定专区（标签）角标
    public void setCornerType(boolean isTag) {
        if (isTag) {
            mCornerType = CORNER_TYPE_TAG;
            mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_22);
        } else {
            mCornerDrawable = null;
        }
    }

    public void setCornerTypeRecommend() {
        mCornerDrawable = this.getResources().getDrawable(R.drawable.episode_item_recommend);
    }

    public void setPgcCornerTypeWithType(int cornerType){
        //0为不显示免费 1为显示免费
        if (cornerType == 1) {
            mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_24);
        }
    }

    /* 最新接口 付费标识 》 免费标识
     * 付费标识：优先级别：用券tType > 抢先看 cornerType == 5 > 会员 isFee
      * 免费标识： 若干*/
    public void setCornerTypeWithType(int isFee, int tvIsEarly, int useTicket, int single, int cornerType) {
        if (cornerType == -1) return; // 强制不显示角标
        if (useTicket == 1) {
            mCornerType = CORNER_TYPE_COUPON;
        }else if(single == 1){
            mCornerType = CORNER_TYPE_SINGLE;
        } else if ((isFee > 0 || tvIsEarly > 0)) {
            if(cornerType == CORNER_TYPE_EDU){
                mCornerType = CORNER_TYPE_EDU;
            }else if(cornerType != CORNER_TYPE_MEMBER_FIRST) {
                mCornerType = CORNER_TYPE_MEMBER;
            }else {
                mCornerType = cornerType;
            }
        } else {
            if (cornerType == CORNER_TYPE_EDU){
                mCornerType = CORNER_TYPE_NONE;
            }else {
                mCornerType = CORNER_TYPE_FREE;
            }
        }



        switch (mCornerType) {
//            case CORNER_TYPE_4K:
//                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_1);
//                break;
            case CORNER_TYPE_BLUE:
                mCornerDrawable = null;
                break;
            case CORNER_TYPE_SOHU_ONLY:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_3);
                break;
            case CORNER_TYPE_SELF_MADE:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_4);
                break;
            case CORNER_TYPE_MEMBER_FIRST:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_5);
                break;
            case CORNER_TYPE_VR:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_6);
                break;
            case CORNER_TYPE_MEMBER:
                if (mHelper.getIsLogin() && mHelper.isVip() && (System.currentTimeMillis()) <= Long.valueOf(mHelper.getVipTime())) {
                    //会员
                    mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_26);

                } else {
                    mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_20);
                }
                break;
            case CORNER_TYPE_COUPON:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_21);
                break;
            case CORNER_TYPE_VARIETY:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_7);
                break;
            case CORNER_TYPE_TV:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_8);
                break;
            case CORNER_TYPE_COMIC:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_9);
                break;
            case CORNER_TYPE_MOVIE:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_10);
                break;
            case CORNER_TYPE_DOCUMENT:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_11);
                break;
            case CORNER_TYPE_AMERICAN:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_12);
                break;
            case CORNER_TYPE_PGC:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_13);
                break;
            case CORNER_TYPE_DTS:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_dts);
                break;
            case CORNER_TYPE_SINGLE:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_single);
                break;
            case CORNER_TYPE_EDU:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_23);
                break;
            case CORNER_TYPE_FREE:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_24);
                break;
            default:
                mCornerDrawable = null;
                break;
        }
    }

    public void setCornerType(int isFee, int ottFee, int type) {
        if ((isFee > 0 || ottFee > 0) && type != CORNER_TYPE_MEMBER_FIRST) {
            mCornerType = CORNER_TYPE_MEMBER;
        } else {
            mCornerType = type;
        }
        switch (mCornerType) {
//            case CORNER_TYPE_4K:
//                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_1);
//                break;
            case CORNER_TYPE_BLUE:
                mCornerDrawable = null;
                break;
            case CORNER_TYPE_SOHU_ONLY:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_3);
                break;
            case CORNER_TYPE_SELF_MADE:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_4);
                break;
            case CORNER_TYPE_MEMBER_FIRST:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_5);
                break;
            case CORNER_TYPE_VR:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_6);
                break;
            case CORNER_TYPE_MEMBER:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_20);
                break;
            case CORNER_TYPE_VARIETY:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_7);
                break;
            case CORNER_TYPE_TV:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_8);
                break;
            case CORNER_TYPE_COMIC:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_9);
                break;
            case CORNER_TYPE_MOVIE:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_10);
                break;
            case CORNER_TYPE_DOCUMENT:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_11);
                break;
            case CORNER_TYPE_AMERICAN:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_12);
                break;
            case CORNER_TYPE_PGC:
                mCornerDrawable = this.getResources().getDrawable(R.drawable.item_corner_13);
                break;
            default:
                mCornerDrawable = null;
                break;
        }
    }


    public void setCornerLocation(int location) {
        mCornerLocation = location;
    }

    public void setCornerHeightRes(int res) {
        try {
            mCornerHeight = getResources().getDimensionPixelSize(res);
        } catch (Resources.NotFoundException ex) {
            mCornerHeight = 0;
        }
    }

    public void setCornerHeight(int cornerHeight) {
        mCornerHeight = cornerHeight;
    }

    public void setCornerScale(float cornerScale) {
        mCornerScale = cornerScale;
    }

    public void setCornerPaddingX(int x) {
        mCornerPaddingX = x;
    }

    public void setCornerPaddingY(int y) {
        mCornerPaddingY = y;
    }

    public void resetCornerType(){
        mCornerDrawable = null;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mCornerDrawable == null) {
            return; // couldn't resolve the URI
        }

        if (mCornerScale < 0 || mCornerScale > 1) {
            return;     // nothing to draw (empty bounds)
        }

        float h2W = (float) mCornerDrawable.getIntrinsicHeight() / (float) mCornerDrawable.getIntrinsicWidth();
//        int cornerWidth = (int) (mCornerScale * (getWidth() - getPaddingLeft() - getPaddingRight()));
//        int cornerHeight = (int) (h2W * cornerWidth);
        int cornerWidth;
        int cornerHeight;
        if (mCornerHeight > 0) {
            cornerWidth = (int) (mCornerHeight / h2W);
            cornerHeight = mCornerHeight;
        } else {
            cornerWidth = mCornerDrawable.getIntrinsicWidth();
            cornerHeight = mCornerDrawable.getIntrinsicHeight();
        }

        switch (mCornerLocation) {
            case CORNER_LOCATION_LEFT_UP:
                mCornerDrawable.setBounds(
                        getScrollX() + getPaddingLeft() + mCornerPaddingX,
                        getScrollY() + getPaddingTop() + mCornerPaddingY,
                        getScrollX() + getPaddingLeft() + cornerWidth + mCornerPaddingX,
                        getScrollY() + getPaddingTop() + cornerHeight + mCornerPaddingY);
                break;
            case CORNER_LOCATION_RIGHT_UP:
                mCornerDrawable.setBounds(
                        getScrollX() + getRight() - getLeft() - getPaddingRight() - cornerWidth - mCornerPaddingX,
                        getScrollY() + getPaddingTop() + mCornerPaddingY,
                        getScrollX() + getRight() - getLeft() - getPaddingRight() - mCornerPaddingX,
                        getScrollY() + getPaddingTop() + cornerHeight + mCornerPaddingY);
                break;
            case CORNER_LOCATION_LEFT_DOWN:
                mCornerDrawable.setBounds(
                        getScrollX() + getPaddingLeft() + mCornerPaddingX,
                        getScrollY() + getBottom() - getTop() - getPaddingBottom() - cornerHeight - mCornerPaddingY,
                        getScrollX() + getPaddingLeft() + cornerWidth + mCornerPaddingX,
                        getScrollY() + getBottom() - getTop() - getPaddingBottom() - mCornerPaddingY);
                break;
            case CORNER_LOCATION_RIGHT_DOWN:
                mCornerDrawable.setBounds(
                        getScrollX() + getRight() - getLeft() - getPaddingRight() - cornerWidth - mCornerPaddingX,
                        getScrollY() + getBottom() - getTop() - getPaddingBottom() - cornerHeight - mCornerPaddingY,
                        getScrollX() + getRight() - getLeft() - getPaddingRight() - mCornerPaddingX,
                        getScrollY() + getBottom() - getTop() - getPaddingBottom() - mCornerPaddingY);
                break;
        }
        mCornerDrawable.draw(canvas);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        startOrStopMarquee(hasFocus);
    }


    public void startOrStopMarquee(boolean hasFocus) {
        if (getTag() instanceof TextView) {
            ((TextView) getTag()).setSelected(hasFocus);
            if (hasFocus) {
                ((TextView) getTag()).setSelected(true);
                ((TextView) getTag()).setMarqueeRepeatLimit(-1);
                ((TextView) getTag()).setEllipsize(TextUtils.TruncateAt.MARQUEE);
            } else {
                ((TextView) getTag()).setSelected(false);
                ((TextView) getTag()).setEllipsize(TextUtils.TruncateAt.END);
            }
        }
    }
}
