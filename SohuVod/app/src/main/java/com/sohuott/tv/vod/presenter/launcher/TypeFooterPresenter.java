package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.model.Footer;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.lb.ScaleConstraintLayout;
import com.sohuott.tv.vod.widget.lb.TabVerticalGridView;

public class TypeFooterPresenter extends Presenter {
    private Context mContext;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        LibDeprecatedLogger.d("onCreateViewHolder: ");
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_footer_layout, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, final Object item) {
        LibDeprecatedLogger.d("onBindViewHolder: ");
        ViewHolder vh = (ViewHolder) viewHolder;

        if (item instanceof Footer) {

            if (((Footer) item).channelType == Constant.TYPE_VIP) {
                vh.mBackFocus.setBackgroundResource(R.drawable.bg_vip_focus_selector);
                vh.mLookFocus.setBackgroundResource(R.drawable.bg_vip_focus_selector);
                vh.mBackToTop.setBackgroundResource(R.drawable.bg_item_type_footer_vip);
                vh.mLookAround.setBackgroundResource(R.drawable.bg_item_type_footer_vip);
                vh.mBackIv.setBackgroundResource(R.drawable.iv_footer_back_vip);
                vh.mLookIv.setBackgroundResource(R.drawable.iv_footer_search_vip);
                vh.mLookTv.setTextColor(Color.parseColor("#DEBB99"));
                vh.mBackTv.setTextColor(Color.parseColor("#DEBB99"));
            }
            vh.mTips.setTextColor(Color.parseColor("#B2E8E8FF"));
            vh.mBackToTop.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (v.getParent().getParent() instanceof TabVerticalGridView) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10148, "clk"), ((Footer) item).pathInfo, null, null);

                        ((TabVerticalGridView) v.getParent().getParent()).backToTop();
                    }
                }
            });

            vh.mLookAround.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10137, "clk"), ((Footer) item).pathInfo, null, null);

                    ActivityLauncher.startSearchActivity(mContext);
                }
            });

            if (((Footer) item).isShowButton) {
                vh.mLookAround.setVisibility(View.VISIBLE);
                vh.mBackToTop.setVisibility(View.VISIBLE);
            } else {
                vh.mLookAround.setVisibility(View.GONE);
                vh.mBackToTop.setVisibility(View.GONE);
            }
            RequestManager.getInstance().onAllEvent(new EventInfo(10141, "imp"), ((Footer) item).pathInfo, null, null);

        }
    }

    @Override
    public void onViewAttachedToWindow(Presenter.ViewHolder holder) {
        LibDeprecatedLogger.d("onViewAttachedToWindow: ");
        super.onViewAttachedToWindow(holder);
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {
        LibDeprecatedLogger.d("onUnbindViewHolder: ");
    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private ScaleConstraintLayout mBackToTop;
        private ScaleConstraintLayout mLookAround;
        private View mBackFocus, mLookFocus;
        private ImageView mLookIv, mBackIv;
        private TextView mLookTv, mBackTv, mTips;

        public ViewHolder(View view) {
            super(view);
            mBackToTop = (ScaleConstraintLayout) view.findViewById(R.id.cl_back_to_top);
            mLookAround = (ScaleConstraintLayout) view.findViewById(R.id.cl_look_around);
            mBackFocus = view.findViewById(R.id.back_top_focus);
            mLookFocus = view.findViewById(R.id.look_around_focus);
            mLookIv = (ImageView) view.findViewById(R.id.iv_footer_search);
            mBackIv = (ImageView) view.findViewById(R.id.iv_footer_back);
            mLookTv = (TextView) view.findViewById(R.id.tv_look_around);
            mBackTv = (TextView) view.findViewById(R.id.tv_back_to_top);
            mTips = (TextView) view.findViewById(R.id.type_footer_tips);
        }
    }
}
