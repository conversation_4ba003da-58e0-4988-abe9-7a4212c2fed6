package com.sohuott.tv.vod.activity;

import android.graphics.Rect;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.PersonalCinemaAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.PersonalCinemaModel;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.model.PersonalCinemaItemTypeModel;
import com.sohuott.tv.vod.presenter.PersonalCinemaPresenter;
import com.sohuott.tv.vod.presenter.PersonalCinemaPresenterImpl;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.PersonalBaseRecyclerview;
import com.sohuott.tv.vod.view.PersonalCinemaView;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by yizhang210244 on 2017/12/22.
 */

public class PersonalCinemaActivity extends BaseActivity implements PersonalCinemaView ,View.OnClickListener{
    private PersonalBaseRecyclerview mPersonalRecyclerView;
    private GlideImageView mUserIcon;
    private TextView mTitleTips;
    private TextView mLoginButton;
    private ImageView mLoginFocus;
    private PersonalCinemaPresenter mPersonalCinemaPresenter;
    private PersonalCinemaAdapter mPersonalCinemaAdapter;
    private FocusBorderView mFocusBorderView;
    private LoadingView mLoadingView;
    private View mErrorView;
    List<PersonalCinemaItemTypeModel> mPersonalCinemaItemTypeModelList = new ArrayList<>();
    private LoginUserInformationHelper mHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_personal_cinema);
        initView();
        initData();
        RequestManager.getInstance().onEvent("6_personal_cinema", "100001", null, null, null, null, null);
        setPageName("6_personal_cinema");
    }

    private void initView(){
        mPersonalRecyclerView = (PersonalBaseRecyclerview) findViewById(R.id.personal_cinema_recyclerview);
        mPersonalRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mPersonalRecyclerView.setItemAnimator(null);
        mUserIcon = (GlideImageView) findViewById(R.id.user_icon);
        mTitleTips = (TextView) findViewById(R.id.title_tips);
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mErrorView = findViewById(R.id.err_view);
        mFocusBorderView = (FocusBorderView) findViewById(R.id.focus_border_view);
        LinearLayoutManager customLinearLayoutManager = new LinearLayoutManager(this);
        customLinearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mPersonalRecyclerView.setLayoutManager(customLinearLayoutManager);
        mPersonalRecyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                int position = ((RecyclerView.LayoutParams) view.getLayoutParams()).getViewLayoutPosition();
                if(position == parent.getAdapter().getItemCount() -1 ){
                    outRect.bottom = (int) view.getContext().getResources().getDimension(R.dimen.y50);
                }
            }
        });
        mLoginButton = (TextView) findViewById(R.id.login_button);
        mLoginFocus = (ImageView) findViewById(R.id.login_button_focus);
        mLoginButton.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if(hasFocus){
                    mLoginFocus.setVisibility(View.VISIBLE);
                }else {
                    mLoginFocus.setVisibility(View.GONE);
                }
                mLoginButton.setSelected(hasFocus);
            }
        });
        mLoginButton.setOnClickListener(this);
        mLoginButton.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if(event.getAction() == KeyEvent.ACTION_DOWN){
                    if(keyCode == KeyEvent.KEYCODE_DPAD_LEFT){
                        loginButtonShake(false);
                        return true;
                    }else if(keyCode == KeyEvent.KEYCODE_DPAD_RIGHT){
                        if(mPersonalRecyclerView != null
                                && mPersonalCinemaAdapter != null
                                && mPersonalCinemaAdapter.getItemCount() > 0){
                            mPersonalCinemaAdapter.setItemFocus(0);
                        }
                        return true;
                    }
                }
                return false;
            }
        });
        mUserIcon.requestFocus();
    }

    private void initData(){
        showLoading();
        mPersonalCinemaPresenter = new PersonalCinemaPresenterImpl(this);
        mPersonalCinemaPresenter.getData();
        mHelper = LoginUserInformationHelper.getHelper(getApplicationContext());
    }

    public void showLoading() {
        mLoadingView.show();
        mErrorView.setVisibility(View.GONE);
        mPersonalRecyclerView.setVisibility(View.GONE);
        mTitleTips.setVisibility(View.GONE);
    }

    public void showError() {
        mLoadingView.hide();
        TextView errHintView = (TextView) mErrorView.findViewById(R.id.error_hint);
        errHintView.setText(R.string.data_err);
        mErrorView.setVisibility(View.VISIBLE);
    }

    private void showSuccess(){
        if(!mHelper.getIsLogin()){
            mTitleTips.setVisibility(View.VISIBLE);
        }
        mPersonalRecyclerView.setVisibility(View.VISIBLE);
        mErrorView.setVisibility(View.GONE);
        mLoadingView.hide();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(mHelper.getIsLogin()){
            if(mLoginButton.getVisibility() == View.VISIBLE){
                if(mLoginButton.isFocused() && mPersonalRecyclerView != null && mPersonalCinemaAdapter != null){
                    mPersonalCinemaAdapter.setItemFocus(0);
                }
                mLoginButton.setVisibility(View.GONE);
            }
            if(mTitleTips.getVisibility() == View.VISIBLE){
                mTitleTips.setVisibility(View.GONE);
            }
            mUserIcon.setCircleImageRes(mHelper.getLoginPhoto());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPersonalCinemaPresenter.detachView();
        if(mPersonalCinemaAdapter != null){
            mPersonalCinemaAdapter.setPersonalCinemaModel(null);
            mPersonalCinemaAdapter.setPersonalCinemaItemTypeModelList(null);
        }
        mPersonalCinemaItemTypeModelList.clear();
        mPersonalCinemaItemTypeModelList = null;
    }

    @Override
    public void showDataSuccess(PersonalCinemaModel value) {
        if(value == null){
            showError();
            return;
        }
        showSuccess();
        if(value.getVrsContents() != null && value.getVrsContents().size() > 0){
            PersonalCinemaItemTypeModel personalCinemaItemTypeModel = new PersonalCinemaItemTypeModel();
            personalCinemaItemTypeModel.setType(PersonalCinemaAdapter.PERSONAL_TYPE_VRS_RECOMMEND);
            personalCinemaItemTypeModel.setDisplayTitle(true);
            mPersonalCinemaItemTypeModelList.add(personalCinemaItemTypeModel);
        }

        if(value.getPgcContents() != null && value.getPgcContents().size() > 0){
            PersonalCinemaItemTypeModel personalCinemaItemTypeModel = new PersonalCinemaItemTypeModel();
            personalCinemaItemTypeModel.setType(PersonalCinemaAdapter.PERSONAL_TYPE_PGC_RECOMMEND);
            if(mPersonalCinemaItemTypeModelList.size() == 0){
                personalCinemaItemTypeModel.setDisplayTitle(true);
            }else {
                personalCinemaItemTypeModel.setDisplayTitle(false);
            }
            mPersonalCinemaItemTypeModelList.add(personalCinemaItemTypeModel);
        }

        if(value.getCateCodeContents() != null && value.getCateCodeContents().size() > 0){
            PersonalCinemaItemTypeModel personalCinemaItemTypeModel = new PersonalCinemaItemTypeModel();
            personalCinemaItemTypeModel.setType(PersonalCinemaAdapter.PERSONAL_TYPE_CATE_CODE);
            if(mPersonalCinemaItemTypeModelList.size() == 0){
                personalCinemaItemTypeModel.setDisplayTitle(true);
            }else {
                personalCinemaItemTypeModel.setDisplayTitle(false);
            }
            mPersonalCinemaItemTypeModelList.add(personalCinemaItemTypeModel);
        }

        if(value.getStarRecommend() != null && value.getStarRecommend().getContents().size() > 0){
            PersonalCinemaItemTypeModel personalCinemaItemTypeModel = new PersonalCinemaItemTypeModel();
            personalCinemaItemTypeModel.setType(PersonalCinemaAdapter.PERSONAL_TYPE_STAR_MOVIE_RECOMMEND);
            mPersonalCinemaItemTypeModelList.add(personalCinemaItemTypeModel);
        }

        if(value.getStarContents() != null && value.getStarContents().size() > 0){
            PersonalCinemaItemTypeModel personalCinemaItemTypeModel = new PersonalCinemaItemTypeModel();
            personalCinemaItemTypeModel.setType(PersonalCinemaAdapter.PERSONAL_TYPE_STAR_RECOMMEND);
            mPersonalCinemaItemTypeModelList.add(personalCinemaItemTypeModel);
        }

        if(value.getTagContents() != null && value.getTagContents().size() > 0){
            for (int i = 0; i < value.getTagContents().size(); i++) {
                PersonalCinemaModel.TagContentsBean tagContentsBean = value.getTagContents().get(0);
                if(tagContentsBean.getContents() != null && tagContentsBean.getContents().size() > 0){
                    PersonalCinemaItemTypeModel personalCinemaItemTypeModel = new PersonalCinemaItemTypeModel();
                    personalCinemaItemTypeModel.setType(PersonalCinemaAdapter.PERSONAL_TYPE_TAG_RECOMMEND);
                    personalCinemaItemTypeModel.setPosition(i);
                    mPersonalCinemaItemTypeModelList.add(personalCinemaItemTypeModel);
                }
            }
        }
        if(mPersonalCinemaItemTypeModelList.size() == 0){
            showError();
            return;
        }
        LibDeprecatedLogger.d("personalCinema item size = " + mPersonalCinemaItemTypeModelList.size());
        mPersonalCinemaAdapter = new PersonalCinemaAdapter(mPersonalRecyclerView);
        mPersonalCinemaAdapter.setFocusBorderView(mFocusBorderView);
        mPersonalCinemaAdapter.setPersonalCinemaItemTypeModelList(mPersonalCinemaItemTypeModelList);
        mPersonalCinemaAdapter.setPersonalCinemaModel(value);
        mPersonalRecyclerView.setAdapter(mPersonalCinemaAdapter);
        mPersonalRecyclerView.post(new InnerRunnable(this));
        int cacheSize = 7;
        if(mPersonalCinemaItemTypeModelList.size()<7){
            cacheSize = mPersonalCinemaItemTypeModelList.size();
        }
        mPersonalRecyclerView.setItemViewCacheSize(cacheSize);
    }

    private static class InnerRunnable implements Runnable {
        private WeakReference<PersonalCinemaActivity> mWrapper;
        InnerRunnable(PersonalCinemaActivity personalCinemaActivity){
            mWrapper = new WeakReference<>(personalCinemaActivity);
        }
        @Override
        public void run() {
            PersonalCinemaActivity personalCinemaActivity = mWrapper.get();
            if (personalCinemaActivity != null){
                personalCinemaActivity.mPersonalCinemaAdapter.setItemFocus(0);
            }
        }
    }

    @Override
    public void showDataError(String msg) {
        showError();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {

        if(event.getAction() == KeyEvent.ACTION_DOWN){
            switch (event.getKeyCode()){
                case KeyEvent.KEYCODE_DPAD_UP: {
                    mUserIcon.setFocusable(true);
                    mLoginButton.setFocusable(true);
                    if (mPersonalCinemaAdapter != null && mPersonalRecyclerView.getVisibility() == View.VISIBLE) {
                        if (mPersonalRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE
                                && !mPersonalRecyclerView.canScrollVertically(-1)
                                && mPersonalCinemaAdapter.getCurrentSelectedPosition() == 0) {
                            View focusView = getCurrentFocus();
                            if(focusView != null){
                                if(focusView.equals(mLoginButton)){
                                    loginButtonShake(true);
                                    return true;
                                }else if(!focusView.equals(mUserIcon)){
                                    if(mLoginButton.getVisibility() == View.VISIBLE){
                                        mLoginButton.requestFocus();
                                    }else {
                                        if(focusView.getAnimation() == null || focusView.getAnimation().hasEnded()){
                                            focusView.startAnimation(AnimationUtils.loadAnimation(this,
                                                    R.anim.shake_y));
                                            if(mFocusBorderView != null){
                                                mFocusBorderView.startAnimation(AnimationUtils.loadAnimation(this,
                                                        R.anim.shake_y));
                                            }
                                        }
                                    }
                                    return true;
                                }
                            }
                        }
                        mUserIcon.requestFocus();
                        mPersonalCinemaAdapter.onVerticalKeyEventForActivity(event.getKeyCode(), event);
                    }
                    return true;
                }
                case KeyEvent.KEYCODE_DPAD_DOWN:
                    mUserIcon.setFocusable(true);
                    mLoginButton.setFocusable(true);
                    if(mPersonalCinemaAdapter != null && mPersonalRecyclerView.getVisibility() == View.VISIBLE){
                        View focusView = getCurrentFocus();
                        if(focusView != null && focusView.equals(mLoginButton)){
                            mPersonalCinemaAdapter.setItemFocus(0);
                            return true;
                        }
                        if(mPersonalCinemaAdapter.getCurrentSelectedPosition() == mPersonalCinemaAdapter.getItemCount()-1){
                            if(focusView != null && !focusView.equals(mUserIcon)
                                && (focusView.getAnimation() == null || focusView.getAnimation().hasEnded()) ){
                                focusView.startAnimation(AnimationUtils.loadAnimation(this,
                                        R.anim.shake_y));
                                mFocusBorderView.startAnimation(AnimationUtils.loadAnimation(this,
                                        R.anim.shake_y));
                            }
                            return true;
                        }
                        mUserIcon.requestFocus();
                        mPersonalCinemaAdapter.onVerticalKeyEventForActivity(event.getKeyCode(),event);
                    }
                    return true;
                case KeyEvent.KEYCODE_DPAD_LEFT:
                case KeyEvent.KEYCODE_DPAD_RIGHT: {
                    if(mLoginButton.isFocused()){
                        return super.dispatchKeyEvent(event);
                    }
                    if(mUserIcon.isFocused()){
                        return true;
                    }
                    mUserIcon.setFocusable(false);
                    mLoginButton.setFocusable(false);
                    if (mPersonalRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                        return true;
                    }
                    break;
                }
                default:
                    break;
            }
        }

        return super.dispatchKeyEvent(event);
    }

    @Override
    public void onClick(View v) {
        if(v == null){
            return;
        }
        if(v.equals(mLoginButton)){
            ActivityLauncher.startLoginActivity(this);
            RequestManager.getInstance().onEvent("6_personal_cinema", "6_personal_cinema_login_click",
                    null, null, null, null, null);
        }
    }

    /**
     *
     * @param isUpDown 是上下还是左右，true 上下，false 左右
     */
    private void loginButtonShake(boolean isUpDown ){
        if(mLoginButton == null || mLoginFocus == null){
            return;
        }
        if(mLoginButton.getAnimation() == null || mLoginButton.getAnimation().hasEnded()){
            if(isUpDown){
                mLoginButton.startAnimation(AnimationUtils.loadAnimation(this,
                        R.anim.shake_y));
                mLoginFocus.startAnimation(AnimationUtils.loadAnimation(this,
                        R.anim.shake_y));
            }else {
                mLoginButton.startAnimation(AnimationUtils.loadAnimation(this,
                        R.anim.shake_x));
                mLoginFocus.startAnimation(AnimationUtils.loadAnimation(this,
                        R.anim.shake_x));
            }

        }
    }
}
