package com.sohuott.tv.vod.fragment;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.LoginActivity;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.lib_statistical.manager.LogEventModel;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListWelfareModel;
import com.sohuott.tv.vod.lib.model.WelfareDetailModel;
import com.sohuott.tv.vod.lib.model.WelfareRequireModel;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Spanny;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.utils.SimpleDisposableObsever;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.functions.Consumer;

/**
 * v6.5积分商城兑换详情页
 *
 * <AUTHOR>
 *         created at 2017/12/22
 */
public class WelfareActivityDetailFragment extends BaseFragment implements View.OnClickListener {


    public interface OnProductClickListener {
        /**
         * click recyclerView item
         *
         * @param activityId
         * @param isHistory
         */
        void onProductClick(int activityId, boolean isHistory);

        /**
         * show score tip while totalScore<score
         */
        void onShowScoreTip();
    }

    private static final String ACTIVITY_ID = "activityId";
    private static final int MAX_LINES = 10;
    public static final int IP_TOO_FREQUENT = 50011; //IP太频繁
    public static final int PASSPORT_TOO_FREQUENT = 50012; //passport太频繁
    public static final int USER_NOT_LOGIN = 50013;//用户未登录

    public static final int ACTIVITY_NOT_BEGIN = 50021;//活动未开始
    public static final int ACTIVITY_ALREADY_END = 50022;//活动已结束
    public static final int ACTIVITY_NO_LEFT = 50023;//奖已领完
    public static final int ACTIVITY_ALREADY_GET = 50024;//奖已领过
    public static final int ACTIVITY_NOT_EXIST = 50025;//活动不存在

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mHelper = LoginUserInformationHelper.getHelper(context);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mActivityId = getArguments().getInt(ACTIVITY_ID);
        RequestManager.onEvent(new LogEventModel("6_welfare_detail", "100001"));
    }

    private String mStatusTip = "";
    private int mExchangeTimes;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mLayoutInflater = inflater;
        View rootView = inflater.inflate(R.layout.fragment_welfare_activity_detail, null);
        initView(rootView);
        mTvExchangeBtn.requestFocus();
        setSubPageName("6_welfare_detail");
        return rootView;
    }

    private void getWelfareDetail() {
        SimpleDisposableObsever<WelfareDetailModel> detailObserver = new SimpleDisposableObsever<WelfareDetailModel>() {

            @Override
            public void onNext(WelfareDetailModel value) {
                if (value != null && value.status == 0
                        && value.data != null) {
                    mTvExchangeBtn.setSelected(false);
                    WelfareDetailModel.DataEntity activityInfo = value.data;
                    mTvProductName.setText(activityInfo.name);
                    mScore = activityInfo.score;
                    SimpleDateFormat dateFormat=new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
                    if(activityInfo.startTime>0){
                        mStartTime=dateFormat.format(new Date(activityInfo.startTime));
                    }
                    if(activityInfo.endTime>0){
                        mEndTime=dateFormat.format(new Date(activityInfo.endTime));
                    }
                    mFivProduct.setImageRes(activityInfo.poster);
                    mExchangeTimes = activityInfo.exchangeTimes;
                    if (activityInfo.status == ListWelfareModel.STATUS_OVER
                            || activityInfo.status == ListWelfareModel.STATUS_SELL_OVER
                            || activityInfo.exchangeTimes == 0) {
                        mTvExchangeBtn.setSelected(true);
                        mStatusTip = "活动已结束";
                        if (activityInfo.status == ListWelfareModel.STATUS_SELL_OVER) {
                            mStatusTip = "活动商品已售完";
                        }
                    } else if (activityInfo.status == ListWelfareModel.STATUS_RECEIVED
                            && activityInfo.exchangeTimes == 1) {
                        mTvExchangeBtn.setSelected(true);
                        mStatusTip = "您已兑换，不可重复兑换";
                    }
                    mTvProductScore.setText(activityInfo.score + getString(R.string.score));
                    if (activityInfo.totalCount > 0) {
                        int percent = (int) (((activityInfo.totalCount - activityInfo.leftCount) * 1.0f / activityInfo.totalCount) * 100);
                        mTvProductSale.setText(getString(R.string.sell) + percent + getString(R.string.percent));
                    }
                    mProductName = activityInfo.name;
                    if (activityInfo.details != null && activityInfo.details.size() > 0) {
                        handleDescription(activityInfo);
                    }
                } else {
                    mTvProductName.setText(getString(R.string.app_name));
                    mFivProduct.setImageRes(R.drawable.vertical_default_big_poster);
                    ToastUtils.showToast(getContext(), value.message);
                    mTvExchangeBtn.setSelected(true);
                }
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
            }
        };
        NetworkApi.getWelfareDetails(mActivityId, mHelper.getLoginPassport(), detailObserver);
        mCompositeDisposable.add(detailObserver);
    }

    @Override
    public void onResume() {
        super.onResume();
        getWelfareDetail();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mCompositeDisposable.clear();
        if (mAnimationDrawable != null) {
            mAnimationDrawable.stop();
        }
    }

    public static WelfareActivityDetailFragment newInstance(int activityId) {
        WelfareActivityDetailFragment detailFragment = new WelfareActivityDetailFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(ACTIVITY_ID, activityId);
        detailFragment.setArguments(bundle);
        return detailFragment;
    }

    private GlideImageView mFivProduct;
    private TextView mTvProductScore;
    private TextView mTvProductName;
    private TextView mProductIntro;
    private TextView mTvProductSale;
    private LinearLayout mScoreTipLayout;
    private TextView mTvExchangeBtn, mMoreBtn;
    private ImageView mAnimView;
    private LoginUserInformationHelper mHelper;
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();

    private LayoutInflater mLayoutInflater;


    private int mActivityId;
    private String mStartTime;
    private String mEndTime;
    private long mScore;
    private String mProductName, mProductDetailIntro;

    private static class InnerHandler extends Handler {
        private WeakReference<WelfareActivityDetailFragment> mWrapper;
        InnerHandler(WelfareActivityDetailFragment fragment){
            mWrapper = new WeakReference<>(fragment);
        }
        @Override
        public void handleMessage(@NonNull Message msg) {
            WelfareActivityDetailFragment fragment = mWrapper.get();
            if (fragment == null){
                return;
            }
            if (fragment.mAnimationDrawable.getCurrent() != fragment.mAnimationDrawable
                    .getFrame(fragment.mAnimationDrawable.getNumberOfFrames() - 1)) {
                sendEmptyMessageDelayed(0, fragment.mAnimDuration);
            } else {
                fragment.mAnimationDrawable.stop();
                AlphaAnimation alphaAnimation = new AlphaAnimation(1, 0);
                alphaAnimation.setDuration(1000);
                fragment.mAnimView.startAnimation(alphaAnimation);
                fragment.mAnimView.setVisibility(View.GONE);
            }
        }
    }
    private Handler mHandler = new InnerHandler(this);


    private void handleDescription(WelfareDetailModel.DataEntity activityInfo) {
        final StringBuilder stringBuilder = new StringBuilder();
        for (WelfareDetailModel.DataEntity.DetailsEntity detailsEntity : activityInfo.details) {
            stringBuilder.append(detailsEntity.name).append("：").append("\n").append(detailsEntity.value).append("\n");
        }
        mProductDetailIntro = stringBuilder.toString();
        final int len=setDescText(stringBuilder);
        mProductIntro.post(new InnerRunnable(this, stringBuilder, len));
    }
    private static class InnerRunnable implements Runnable {
        private WeakReference<WelfareActivityDetailFragment> mWrapper;
        private StringBuilder sb;
        private int len;
        InnerRunnable(WelfareActivityDetailFragment welfareActivityDetailFragment, StringBuilder sb, int len){
            mWrapper = new WeakReference<>(welfareActivityDetailFragment);
            this.sb = sb;
            this.len = len;
        }
        @Override
        public void run() {
            WelfareActivityDetailFragment welfareActivityDetailFragment = mWrapper.get();
            if (welfareActivityDetailFragment != null){
                int lineCount = welfareActivityDetailFragment.mProductIntro.getLineCount();
                if (lineCount > MAX_LINES) {
                    int index = welfareActivityDetailFragment.mProductIntro.getLayout().getLineEnd(MAX_LINES - 1);
                    String newText = sb.substring(0, index - len);
                    if (newText.endsWith("\n")) {
                        newText = newText.substring(0, newText.length() - 1) + "..." + "\n";
                    }
                    welfareActivityDetailFragment.setDescText(newText);
                    welfareActivityDetailFragment.mProductIntro.append("...");
                }
                welfareActivityDetailFragment.mMoreBtn.setVisibility(lineCount < MAX_LINES ? View.INVISIBLE : View.VISIBLE);
            }
        }
    }

    private int setDescText(CharSequence desc) {
        Spanny spanny = new Spanny(mProductName + "\n",
                new AbsoluteSizeSpan((int) getResources().getDimension(R.dimen.x36)),
                new ForegroundColorSpan(getResources().getColor(R.color.common_white)));
        if (!TextUtils.isEmpty(mStartTime)) {
            spanny.append("开始时间：" + mStartTime + "\n",
                    new AbsoluteSizeSpan((int) getResources().getDimension(R.dimen.x30)),
                    new ForegroundColorSpan(getResources().getColor(R.color.light_gray_no_alpha)));
        }
        if (!TextUtils.isEmpty(mEndTime)) {
            spanny.append("结束时间：" + mEndTime + "\n",
                    new AbsoluteSizeSpan((int) getResources().getDimension(R.dimen.x30)),
                    new ForegroundColorSpan(getResources().getColor(R.color.light_gray_no_alpha)));
        }
        int len=spanny.length();
        spanny.append(desc,
                new AbsoluteSizeSpan((int) getResources().getDimension(R.dimen.x30)),
                new ForegroundColorSpan(getResources().getColor(R.color.light_gray_no_alpha)));
        mProductIntro.setText(spanny);
        return len;
    }

    private void initView(View rootView) {
        mFivProduct = (GlideImageView) rootView.findViewById(R.id.fiv_product);
        mTvProductName = (TextView) rootView.findViewById(R.id.tv_product_name);
        mTvProductScore = (TextView) rootView.findViewById(R.id.tv_product_score);
        mTvProductSale = (TextView) rootView.findViewById(R.id.tv_product_sale);
        mProductIntro = (TextView) rootView.findViewById(R.id.tv_detail_intro);
        mTvExchangeBtn = (TextView) rootView.findViewById(R.id.tv_exchange);
        mAnimView = (ImageView) rootView.findViewById(R.id.iv_anim);
        mMoreBtn = (TextView) rootView.findViewById(R.id.tv_more);
        mScoreTipLayout = (LinearLayout) rootView.findViewById(R.id.ll_score_tip);
        mTvExchangeBtn.setOnClickListener(this);
        mMoreBtn.setOnClickListener(this);
    }


    AnimationDrawable mAnimationDrawable;
    int mAnimDuration;

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.tv_exchange) {
            if (v.isSelected()) {
                if (!TextUtils.isEmpty(mStatusTip)) {
                    ToastUtils.showToast(getContext(), mStatusTip);
                }
                return;
            }
            if (mHelper.getIsLogin()) {
                if (mHelper.getTotalScore() == 0 || mHelper.getTotalScore() < mScore) {
                    showScoreTip();
                    return;
                } else {
                    HashMap<String, String> params = new HashMap<>();
                    params.put("passport", mHelper.getLoginPassport());
                    params.put("gid", UserInfoHelper.getGid());
                    params.put("activityId", String.valueOf(mActivityId));
                    mCompositeDisposable.add(NetworkApi.getWelfareRequireUrl(params
                            , new SimpleDisposableObsever<WelfareRequireModel>() {
                                @Override
                                public void onNext(WelfareRequireModel value) {
                                    if (value.status != 0) {
                                        showExchangeFailedTip(value.status, value.message);
                                    } else {
                                        startSucceedAnim();
                                        if (value.extend != null) {
                                            mHelper.putUserLikeRank(value.extend.rank);
                                            mHelper.putTotalScore(value.extend.totalScore);
                                        }
                                        if (mExchangeTimes == 1) {
                                            mTvExchangeBtn.setSelected(true);
                                        }
                                    }
                                }

                                @Override
                                public void onError(Throwable e) {
                                    super.onError(e);
                                    ToastUtils.showToast(getContext(), getString(R.string.welfare_exchange_failed));
                                }
                            }));
                }
            } else {
                startActivity(new Intent(getContext(), LoginActivity.class));
            }

            LogEventModel eventModel = new LogEventModel("6_welfare_detail", "6_welfare_detail_btn_exchange");
            eventModel.expand1 = mHelper.getLoginPassport();
            eventModel.expand2 = String.valueOf(mActivityId);
            RequestManager.onEvent(eventModel);
        } else {
            showDetailWindow();
            LogEventModel eventModel = new LogEventModel("6_welfare_detail", "6_welfare_detail_btn_more");
            eventModel.expand1 = String.valueOf(mActivityId);
            RequestManager.onEvent(eventModel);
        }
    }

    private void startSucceedAnim() {
        if (mAnimView.getVisibility() != View.VISIBLE) {
            mAnimView.setVisibility(View.VISIBLE);
            if (mAnimationDrawable == null) {
                mAnimDuration = 0;
                mAnimationDrawable = (AnimationDrawable) mAnimView.getDrawable();
                int frameCount = mAnimationDrawable.getNumberOfFrames();
                for (int index = 0; index < frameCount; index++) {
                    mAnimDuration += mAnimationDrawable.getDuration(index);
                }
            }
            if (mAnimationDrawable.isRunning()) {
                mAnimationDrawable.stop();
            }
            mAnimationDrawable.start();
            mHandler.sendEmptyMessageDelayed(0, mAnimDuration);
        }


    }

    private void showExchangeFailedTip(int status, String message) {
        String errMsg = message;
        switch (status) {
            case IP_TOO_FREQUENT:
                errMsg = getString(R.string.welfare_exchange_ip_frequency);
                break;
            case PASSPORT_TOO_FREQUENT:
                errMsg = getString(R.string.welfare_exchange_account_frequency);
                break;
            case ACTIVITY_NOT_BEGIN:
                errMsg = getString(R.string.welfare_exchange_not_start);
                break;
            case ACTIVITY_ALREADY_END:
                errMsg = getString(R.string.welfare_exchange_end);
                mTvExchangeBtn.setSelected(true);
                break;
            case ACTIVITY_NOT_EXIST:
            case ACTIVITY_NO_LEFT:
                errMsg = getString(R.string.welfare_exchange_sell_over);
                mTvExchangeBtn.setSelected(true);
                break;
        }
        ToastUtils.showToast(getContext(), errMsg);
    }

    private void showScoreTip() {
        if (mScoreTipLayout.getVisibility() != View.VISIBLE) {
            mScoreTipLayout.setVisibility(View.VISIBLE);
            mScoreTipLayout.startAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.popup_enter));
            mCompositeDisposable.add(Observable.timer(3, TimeUnit.SECONDS, AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            mScoreTipLayout.startAnimation(AnimationUtils.loadAnimation(getContext()
                                    , R.anim.popup_exit_center));
                            mScoreTipLayout.setVisibility(View.GONE);
                        }
                    }));
        }

    }

    private PopupWindow mMoreDetailWindow;

    private void showDetailWindow() {
        if (mMoreDetailWindow == null) {
            View contentView = mLayoutInflater.inflate(R.layout.dialog_welfare_detail, null);
            TextView detailMoreText = (TextView) contentView.findViewById(R.id.more_detail_text);
            detailMoreText.setText(mProductName + "\n" + mProductDetailIntro);
            mMoreDetailWindow = new PopupWindow(contentView, ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT, true);
            mMoreDetailWindow.setBackgroundDrawable(ResourcesCompat.getDrawable(getResources(), R.drawable.dialog_more_detail_bg, null));
            mMoreDetailWindow.setTouchable(true);
            mMoreDetailWindow.setFocusable(true);
            mMoreDetailWindow.setOutsideTouchable(true);
            mMoreDetailWindow.setAnimationStyle(R.style.PopupAnimation);
            mMoreDetailWindow.setContentView(contentView);
        }
        mMoreDetailWindow.showAtLocation(getView(), Gravity.CENTER, 0, 0);
    }

}
