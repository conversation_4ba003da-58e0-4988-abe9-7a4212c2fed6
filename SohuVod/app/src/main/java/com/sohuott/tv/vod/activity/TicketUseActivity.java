package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.Ticket;
import com.sohuott.tv.vod.lib.model.TicketUse;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.PostHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohu.lib_utils.FormatUtils;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.videodetail.activity.service.VideoDetailServiceManger;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.HashMap;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by yizhang210244 on 2017/5/17.
 * <p>
 * Modified by music on 2022/1/11
 */

public class TicketUseActivity extends BaseActivity implements View.OnFocusChangeListener {

    private GlideImageView mUser_avatar;
    private TextView mUserName;
    private TextView mUserLoginType;
    private ImageView mUserVipLogo;
    private ImageView mLoginType;
    //    private TextView mMemType;
//    private TextView mTicket;
    private GlideImageView mMovieAlbum;
    private TextView mMovieTitle;
    private TextView mMessage;
    private Button mOkButton;
    private Button mCancelButton;
    private LoginUserInformationHelper mHelper;
    private long mAid = -1;
    private long mVid = -1;
    private String mAlbumUrl = null;
    private String mVideoName = "";
    private int mWidthUser;
    private int mHeightUser;
    private int mWidthMovieAlbum;
    private int mHeightMovieAlbum;

    HashMap<String, String> pathInfo = new HashMap<String, String>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_ticket_use);
        mMovieAlbum = (GlideImageView) findViewById(R.id.movie_album);
        mMessage = (TextView) findViewById(R.id.message_content);
        mOkButton = (Button) findViewById(R.id.ok);
        mCancelButton = (Button) findViewById(R.id.cancel);
        mUser_avatar = (GlideImageView) findViewById(R.id.user_avatar);
        mUserName = (TextView) findViewById(R.id.user_name);
        mUserLoginType = (TextView) findViewById(R.id.user_login_type);
        mUserVipLogo = (ImageView) findViewById(R.id.user_vip_logo);
//        mMemType = (TextView) findViewById(R.id.mem_type);
//        mTicket = (TextView) findViewById(R.id.ticket);
        mMovieTitle = (TextView) findViewById(R.id.movie_title);
        mLoginType = (ImageView) findViewById(R.id.login_type);
        mWidthUser = getResources().getDimensionPixelSize(R.dimen.x74);
        mHeightUser = getResources().getDimensionPixelSize(R.dimen.y74);
        mWidthMovieAlbum = getResources().getDimensionPixelSize(R.dimen.x380);
        mHeightMovieAlbum = getResources().getDimensionPixelSize(R.dimen.y565);

        mHelper = LoginUserInformationHelper.getHelper(getApplicationContext());
        parseIntent(getIntent());

        if (!TextUtils.isEmpty(mAlbumUrl)) {
            Glide.with(this)
                    .load(mAlbumUrl)
                    .transform(new RoundedCorners(getResources().getDimensionPixelOffset(R.dimen.x10)))
                    .into(mMovieAlbum);
        }
        mMovieTitle.setText(mVideoName);
        getUserTicket();

        if (mHelper.getIsLogin()) {
            final String avatar = mHelper.getLoginPhoto();
            if (null != avatar && !avatar.trim().equals("")) {
                mUser_avatar.setCircleImageRes(avatar);
            }

            mUserName.setText(mHelper.getNickName());
            displayUserInfo();

        } else {
            mLoginType.setVisibility(View.GONE);
        }

        pathInfo.put("pageId", "1008");


        RequestManager.getInstance().onEvent("6_ticket_use", "100001", null, null, null, null, null);
        setPageName("6_ticket_use");
    }

    private void displayUserInfo() {
        mLoginType.setVisibility(View.VISIBLE);
        switch (Util.getSouthMediaLoginType(mHelper.getUtype())) {
            case 4:
                mLoginType.setBackgroundResource(R.drawable.login_type_weibo);
                break;
            case 1:
                mLoginType.setBackgroundResource(R.drawable.login_type_wechat);
                break;
            case 2:
                mLoginType.setBackgroundResource(R.drawable.login_type_qq);
                break;
            case 3:
                mLoginType.setBackgroundResource(R.drawable.login_type_sohu);
                break;
        }
        mUserLoginType.setText(mHelper.getUtype());
        if (mHelper.isVip()) {
            String vipDate = FormatUtils.formatDate(Long.valueOf(mHelper.getVipTime()));
            mUserLoginType.setText("会员有效期：" + vipDate + "  |  观影券：" + mHelper.getUserTicketNumber());
            mUserLoginType.setTextColor(Color.parseColor("#DEBB99"));
            mUserLoginType.setAlpha(0.7f);
            mUserName.setTextColor(Color.parseColor("#DEBB99"));
            mUserVipLogo.setVisibility(View.VISIBLE);
        } else {
            if (!TextUtils.isEmpty(mHelper.getUserTicketNumber()) && !TextUtils.equals(mHelper.getUserTicketNumber(), "0张")) {
                mUserLoginType.setText("已冻结，续费后解冻！并赠送新券。");
                mUserVipLogo.setVisibility(View.VISIBLE);
                mUserVipLogo.setBackgroundResource(R.drawable.top_bar_vip_expired);
            } else {
//                mTicketDesc.setText("，开通会员，立即赠券！");
                mUserLoginType.setText("普通用户");
            }
        }
    }


    private void parseIntent(Intent intent) {
        if (null != intent) {
            mAid = intent.getLongExtra(ParamConstant.PARAM_AID, -1);
            mVid = intent.getLongExtra(ParamConstant.PARAM_AID, -1);
            mAlbumUrl = intent.getStringExtra(ParamConstant.PARAM_ALBUM_POSTER);
            mVideoName = intent.getStringExtra(ParamConstant.PARAM_ALBUM_TITLE);
        }
    }

    private void getUserTicket() {
        NetworkApi.getVideoDetailUserTicket(mHelper.getLoginPassport(), mHelper.getLoginToken(),
                new DisposableObserver<Ticket>() {
                    @Override
                    public void onNext(Ticket ticket) {
                        if (null != ticket) {
                            int status = ticket.status;
                            String message = ticket.message;
                            Ticket.TicketData data = ticket.data;

                            if (status == 200 && null != data) {
                                String number = data.getNumber();
                                number = number.trim();
                                if (!TextUtils.isEmpty(number)) {
                                    String str = "本片需要观影券1张，即可免费观看完整影片，兑换后2天有效，你有" + number + "观影券，是否兑换？";
                                    mMessage.setText(str);
                                    setTicketListen();
//                                    mTicket.setText("  观影券"+ mHelper.getUserTicketNumber());
                                    mHelper.putUserTicketNumber(Util.getTicketNumber(number));
                                    PostHelper.postTicketEvent();
                                } else {
                                    mMessage.setText("你暂时没有观影券!");
                                    setEmptyTicketListen();
                                }
                            } else {
                                mMessage.setText(message);
                                setEmptyTicketListen();
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        mMessage.setText("获取观影券失败！");
                        setEmptyTicketListen();
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    private void setEmptyTicketListen() {
        mOkButton.setVisibility(View.VISIBLE);
        mCancelButton.setVisibility(View.GONE);
        mOkButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finishActivity(false);
            }
        });
    }

    private void setTicketListen() {
        mOkButton.setOnFocusChangeListener(this);
        mCancelButton.setOnFocusChangeListener(this);
        mOkButton.setVisibility(View.VISIBLE);
        mCancelButton.setVisibility(View.VISIBLE);
        mOkButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getVideoDetailTicketUse();
            }
        });
        mCancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RequestManager.getInstance().onEvent("6_ticket_use", "6_ticket_use_cancel",
                        String.valueOf(mAid), String.valueOf(mVid), null, null, null);

                HashMap memoInfo = new HashMap();
                memoInfo.put("vid", mVid);
                memoInfo.put("playlistid", mAid);
                RequestManager.getInstance().onAllEvent(new EventInfo(10203, "clk"), pathInfo, null, memoInfo);
                finishActivity(false);
            }
        });
    }

    public void getVideoDetailTicketUse() {
        HashMap memoInfo = new HashMap();
        memoInfo.put("vid", mVid);
        memoInfo.put("playlistid", mAid);
        RequestManager.getInstance().onAllEvent(new EventInfo(10202, "clk"), pathInfo, null, memoInfo);
        NetworkApi.getVideoDetailTicketUse(mHelper.getLoginPassport(), mHelper.getLoginToken(), mAid, mVid,
                new DisposableObserver<TicketUse>() {
                    @Override
                    public void onNext(com.sohuott.tv.vod.lib.model.TicketUse response) {
                        if (response.status == 200) {
                            ToastUtils.showToast(TicketUseActivity.this, getResources().getString(R.string.video_detail_ticket_ok));
                            int number = Util.getTicketNumberInt(mHelper.getUserTicketNumber());
                            if (number > 0) {
                                mHelper.putUserTicketNumber(String.valueOf(number - 1) + "张");
                                PostHelper.postTicketEvent();
                            }
                            RequestManager.getInstance().onEvent("6_ticket_use", "6_ticket_use_success",
                                    String.valueOf(mAid), String.valueOf(mVid), null, null, null);

                            HashMap<String, String> memoInfo = new HashMap<String, String>();
                            memoInfo.put("single", "0");
                            memoInfo.put("vip", "0");
                            memoInfo.put("coupon", "1");
                            memoInfo.put("login", "1");

                            RequestManager.getInstance().onAllEvent(new EventInfo(10201, "slc"), pathInfo, null, memoInfo);

                        } else {
                            ToastUtils.showToast(TicketUseActivity.this, response.message);
                            RequestManager.getInstance().onEvent("6_ticket_use", "6_ticket_use_failure",
                                    String.valueOf(mAid), String.valueOf(mVid), null, null, null);
                        }
                        finishActivity(true);
                    }

                    @Override
                    public void onError(Throwable e) {
                        ToastUtils.showToast(TicketUseActivity.this, "兑换失败！");
                        RequestManager.getInstance().onEvent("6_ticket_use", "6_ticket_use_failure",
                                String.valueOf(mAid), String.valueOf(mVid), null, null, null);
                        finishActivity(true);
                    }

                    @Override
                    public void onComplete() {
                    }
                });
    }

    private void finishActivity(boolean isRePlay) {
        VideoDetailServiceManger.getInstants().onStartActivityPlay(isRePlay);
        finish();
    }

    @Override
    public void onFocusChange(View view, boolean hasFocus) {
        if (view instanceof Button) {
            if (hasFocus) {
                view.animate().scaleX(1.1f).scaleY(1.1f).setDuration(150).start();
            } else {
                view.animate().scaleX(1.0f).scaleY(1.0f).setDuration(100).start();
            }
        }
    }
}
