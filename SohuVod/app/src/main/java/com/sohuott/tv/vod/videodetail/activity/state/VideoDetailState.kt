package com.sohuott.tv.vod.videodetail.activity.state

import android.text.TextUtils
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.sh.ott.video.util.serverValueTransformMedia
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.account.payment.EducationPrivilege
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger
import com.sohuott.tv.vod.app.config.ResolutionInfo
import com.sohuott.tv.vod.lib.model.AlbumInfo
import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel
import com.sohuott.tv.vod.lib.model.EpisodeVideos
import com.sohuott.tv.vod.lib.model.PermissionCheck
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo
import com.sohuott.tv.vod.lib.model.PgcPlayList
import com.sohuott.tv.vod.lib.model.SouthMediaCheckResult
import com.sohuott.tv.vod.lib.model.TopInfo
import com.sohuott.tv.vod.lib.model.VideoDetailFilmCommodities
import com.sohuott.tv.vod.lib.model.VideoInfo
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.videodetail.activity.LogoInfo
import com.sohuott.tv.vod.videodetail.activity.addPgcVideoPlayInfo
import com.sohuott.tv.vod.videodetail.activity.addVideoPlayInfo
import com.sohuott.tv.vod.videodetail.activity.control.VideoPlayMenuBean
import com.sohuott.tv.vod.videodetail.activity.conversionPgcVideoPlayResolutionInfo
import com.sohuott.tv.vod.videodetail.activity.conversionVideoPlayResolutionInfo
import com.sohuott.tv.vod.videodetail.activity.model.DrmPlayInfoData
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.VideoInfoOnlySeeItem
import java.util.Arrays
import kotlin.math.min

data class VideoDetailState(
    //用户栏数据
    val topData: Async<TopInfo> = Uninitialized,
    val drmPlayInfoData: Async<DrmPlayInfoData> = Uninitialized,
    //是否登录
    val isLogin: Boolean = false,
    //是否是会员
    val isVip: Boolean = false,
    //支付相关信息
    val videoDetailFilmCommodities: Async<VideoDetailFilmCommodities?> = Uninitialized,
    //vrs 专辑
    val vrsAlbumInfo: Async<AlbumInfo> = Uninitialized,
    //pgc 专辑
    val pgcAlbumInfo: Async<PgcAlbumInfo> = Uninitialized,
    //视频信息
    val videoInfoData: Async<VideoInfo> = Uninitialized,
    //视频类型
    val videoType: Int = Constant.DATA_TYPE_VRS,
    //窗口模式
    val videoWindowType: Int = VIDEO_WINDOW_TYPE_SCALE,
    //是否是一直全屏
    val videoWindowAlwaysFull: Boolean = false,
    val mCurrentSelectOnlySeeMenuItem: ScaleContentOnlySeeMenuItem? = null,
    val videoDetailRecommendModel: Async<VideoDetailRecommendModel> = Uninitialized,
    val pgcPlayList: Async<PgcPlayList> = Uninitialized,
    val enableCollect: Boolean = false,
    val requestPermissionCheck: Boolean = false,
    val edVideoPermissionCheck: Async<PermissionCheck> = Uninitialized,
    val videoPermissionCheck: Async<PermissionCheck> = Uninitialized,
    val educationPrivilege: Async<EducationPrivilege> = Uninitialized,
    //下一集信息
    val nextEpisodeInfo: MutableList<Any>? = null,
    //片花下一集信息
    val trailerEpisodeVideos: EpisodeVideos? = null,
    //南传鉴权信息
    val southMediaCheckResult: Async<Int> = Uninitialized,
    //是否是 dlna 投屏
    val isDlna: Boolean = false,
    //是否来源于搜狐视频投屏
    val isShDlna: Boolean = false

) : MavericksState {


    val pgcVideoInfoResult: Async<MutableList<ResolutionInfo>>
        get() {
            val info = pgcPlayList
            return when (info) {
                is Success<PgcPlayList> -> {
                    val playinfo=info.invoke().playinfo
                    if (playinfo==null){
                        Fail(Throwable(message = "pgc playinfo is null"))
                    }else{
                        Success(info.invoke().playinfo.addPgcVideoPlayInfo(pgcAlbumInfo.invoke()))
                    }
                }

                is Loading -> {
                    Loading()
                }

                is Fail -> {
                    Fail(info.error)
                }

                else -> {
                    Uninitialized
                }

            }
        }

    val detailVideoResult: Async<VideoPlayInfoResponse>
        get() {
            val videoInfo = videoInfoData
            when (videoInfo) {
                is Success<VideoInfo> -> {
                    val data = videoInfo.invoke()
                    val isisTrailer =
                        data?.data?.tvStype != 1 && data?.data?.tvStype != 38 && data?.data?.categoryCode != Constant.CATECODE_MOVIE
                    var videoOrder = data?.data?.videoOrder
                    val albumData = getAlbumInfo
                    val aid = albumData?.data?.id
                    var videoCount = 0
                    if (videoWindowAlwaysFull) {
                        if (data?.data?.tvIsEarly == 0) {
                            videoCount = data.extend.maxVideoOrder + data.extend.trailerAppendCount
                        } else {
                            videoCount = data?.extend?.maxVideoOrder ?: 0
                        }

                    } else {
                        if (data?.data?.categoryCode == Constant.CATECODE_MOVIE) {
                            videoCount = data.extend.maxVideoOrder + data.extend.trailerCount
                        } else {
                            videoCount =
                                (data?.extend?.maxVideoOrder
                                    ?: 0) + (data?.extend?.trailerAppendCount
                                    ?: 0) + (data?.extend?.fanwaiCount ?: 0)
                        }

                    }

                    if (isisTrailer) {
                        videoOrder = if (data?.data?.playlistId != aid) {
                            data?.data?.videoOrder
                        } else {
                            if (data?.data?.categoryCode == Constant.CATECODE_MOVIE) {
                                data.data.videoOrder + data.extend.maxVideoOrder
                            } else {
                                data?.data?.tvFormalOrder
                            }
                        }
                    } else {
                        videoOrder = if (data?.data?.tvStype == 38) {
                            //番外和彩蛋
                            data.extend.maxVideoOrder + data.extend.relativeVideoOrder + data.extend.trailerAppendCount
                        } else {
                            data?.data?.videoOrder
                        }
                    }

                    // model.extend.recommendList == null?null:model.extend.recommendList
                    val recommendList = albumData?.extend?.recommendList ?: mutableListOf()
                    recommendList.subList(
                        0,
                        min(5.0, recommendList.size.toDouble() ?: 0.0).toInt()
                    )

                    val videoData = data?.data
                    val playInfo = videoData?.playInfo
                    val feeType = if (data?.data?.fee == 2) {
                        FEE_ALBUM
                    } else {
                        if (data?.data?.tvSetIsFee == 1) {
                            FEE_VIDEO
                        } else {
                            FEE_FREE
                        }
                    }
                    val infos = mutableListOf<VideoPlayResolutionInfo>()
                    infos.addAll(
                        playInfo?.conversionVideoPlayResolutionInfo() ?: mutableListOf(
                            VideoPlayResolutionInfo()
                        )
                    )
                    val resolutionInfos = playInfo?.addVideoPlayInfo()
                    return Success<VideoPlayInfoResponse>(
                        VideoPlayInfoResponse(
                            tvPlayType = data?.data?.tvPlayType?:0,
                            videoLength = data?.data?.tvLength?.toLong(),
                            cateId = data?.data?.categoryId,
                            cateCode = data?.data?.categoryCode,
                            maxVideoOrder = data?.extend?.maxVideoOrder,
                            varietyPeriod = data?.data?.varietyPeriod,
                            tvInsertAdTime = data?.data?.tvAdTimeDouble ?: 0.0,
                            videoExtendsPic = data?.data?.videoExtendsPic_240_330,
                            videoExtendsPic_640 = data?.data?.videoExtendsPic_640_360,
                            tvVerPic = data?.extend?.tvVerPic,
                            albumTVName = data?.extend?.albumTVName,
                            trailerTVName = data?.extend?.trailerTVName,
                            isExtendNull = data?.extend == null,
                            feeType = feeType,
                            isMemberPlay = data?.data?.isMemberPlay ?: 0,
                            trySeeTime = data?.data?.trySeeTime ?: 0L,
                            logoInfo = LogoInfo().also {
                                it.dimension = data?.data?.logoInfo?.dimension
                                it.logo = data?.data?.logoInfo?.logo ?: 0
                                it.logoleft = data?.data?.logoInfo?.logoleft ?: 0
                                it.height = data?.data?.logoInfo?.height ?: 0f
                                it.width = data?.data?.logoInfo?.width ?: 0f
                                it.logoSideMargin = data?.data?.logoInfo?.side_margin ?: 0f
                                it.logoTopMargin = data?.data?.logoInfo?.top_margin ?: 0f
                                it.orientation = data?.data?.logoInfo?.orientation ?: 0
                            },

                            videoTvName = data?.data?.tvName,
                            videoVid = data?.data?.tvVerId?.toLong() ?: 0,
                            sortOrder = data?.extend?.sortOrder ?: 0,
                            isisTrailer = isisTrailer,
                            videoOrder = videoOrder ?: 0,
                            playlistId = data?.data?.playlistId ?: 0,
                            tvStype = data?.data?.tvStype ?: 0,
                            recommendList = recommendList,
                            videoCount = videoCount,
                            latestVideoCount = data?.data?.latestVideoCount ?: 0,
                            isLastEpisode = data?.data?.latestVideoCount == data?.data?.videoOrder,
                            videoType = videoType,
                            isShowTitle = albumData?.data?.isShowTitle ?: 0,
                            tvOttIsFee = data?.data?.tvOttIsFee ?: 0,
                            tvStartTime = data?.data?.tvStartTime,
                            tvEndTime = data?.data?.tvEndTime,
                            lookHimSet = data?.data?.lookHimSet,
                            videoPlayResolutionInfo = infos,
                            resolutionInfos = resolutionInfos
                        )
                    )
                }

                is Fail<VideoInfo> -> {
                    return Fail(videoInfo.error)
                }

                is Loading -> {
                    return Loading()
                }

                else -> {
                    return Uninitialized
                }
            }
        }


    val videoPayInfo: VideoPayInfoResponse?
        get() {
            val payInfoResponse = VideoPayInfoResponse()
            if (videoDetailFilmCommodities !is Success) return null
            val payInfo = videoDetailFilmCommodities.invoke()
            val buttons = payInfo?.data?.buttons
            payInfoResponse.buttonsNull = buttons?.isEmpty() == true
            if (buttons == null) {
                payInfoResponse.isEduCate = false
            } else {
                payInfoResponse.isEduCate = payInfo?.cateCode == Constant.EDU_CATE_CODE
                for (i in buttons.indices) {
                    val type: String = buttons[i].type
                    if (buttons[i].type == "ticket") { //观影券
                        payInfoResponse.isTicket = true
                        if (buttons[i].data != null) {
                            payInfoResponse.ticketCount = buttons[i].data.count
                        }
                    } else if (type == "video") { //单片
                        payInfoResponse.isSingle = true
                    } else if (type == "member") { //会员
                        payInfoResponse.isMember = true
                    } else if (type == "album") { //专辑
                        payInfoResponse.isSingle = true
                    }
                }
            }
            payInfoResponse.feePayVid = payInfo?.data?.course_fee_vid ?: 0

            val playRequire = payInfo?.data?.play_require
            payInfoResponse.playRequire = playRequire
            if (!payInfoResponse.isTicket) {
                playRequire?.let {
                    if (it.contains("ticket")) {
                        payInfoResponse.isTicket = true
                    }
                }
            }

            if (payInfoResponse.isTicket || payInfoResponse.isSingle || payInfoResponse.isMember) {
                if (UserLoginHelper.getInstants().getIsLogin() && UserLoginHelper.getInstants()
                        .isVip() && (payInfoResponse.isTicket || payInfoResponse.isMember)
                ) {
                    payInfoResponse.payText = "用券看"
                } else {
                    payInfoResponse.payText = "购买"
                }
            }
            if (payInfo?.data?.user_info != null && payInfo.data.user_info?.expire_in != 0L && payInfo.data.user_info?.buy_status == 1 && payInfo.data.user_info?.buy_type != 3) {
                payInfoResponse.time = payInfo.data.user_info.expire_in ?: 0
                payInfoResponse.isShowTime = true
            } else {
                payInfoResponse.isShowTime = false
            }
            return payInfoResponse
        }


}

private val VideoDetailState.getAlbumInfo: AlbumInfo?
    get() = if (videoType != Constant.DATA_TYPE_VRS) {
        pgcAlbumInfo()?.convertToAlbumInfo()
    } else {
        vrsAlbumInfo()
    }


/**
 * 会员信息
 */
data class VideoPayInfoResponse(
    //会员
    var isMember: Boolean = false,
    //单点
    var isSingle: Boolean = false,
    //用券
    var isTicket: Boolean = false,
    //券数量
    var ticketCount: Int = 0,
    //是否显示剩余时间
    var isShowTime: Boolean = false,
    var time: Long = 0,
    var payText: String? = null,
    //跳转支付使用
    var feePayVid: Int? = 0,
    //是否是教育
    var isEduCate: Boolean = false,
    var playRequire: String? = null,

    var buttonsNull: Boolean = false,

    ) {
}

/**
 * 详情页 成功失败
 */
sealed class VideoDetailViewResult {


    data class Success(val response: VideoInfoResponse) : VideoDetailViewResult()


    data class Fail(val code: Int = 0, val errorMessage: String = "") : VideoDetailViewResult()
    data object Loading : VideoDetailViewResult()
}


/**
 * 播放信息
 */
data class VideoPlayInfoResponse(
    /**
     * 0非drm 1搜狐drm 3外采drm
     */
    var tvPlayType:Int?=0,
    //当前播放地址
    var currentUrl: String? = null,
    var videoTvName: String? = null,
    var videoLength: Long? = null,
    var videoVid: Long = 0,
    var logoInfo: LogoInfo? = null,
    var videoPlayMenuBean: VideoPlayMenuBean? = null,
    var sortOrder: Int = 0,
    var videoOrder: Int = 0,
    var playlistId: Int = 0,
    var tvStype: Int = 0,
    var videoCount: Int = 0,
    //是否是最后一集
    var isLastEpisode: Boolean = false,
    var latestVideoCount: Int = 0,
    var isisTrailer: Boolean = false,
    var recommendList: MutableList<AlbumInfoRecommendModel>? = null,
    var videoType: Int = Constant.DATA_TYPE_VRS,
    var isShowTitle: Int = 0,
    var tvOttIsFee: Int = 0,
    var videoInfoOnlySeeItem: MutableList<VideoInfoOnlySeeItem?>? = null,
    var videoPlayResolutionInfo: MutableList<VideoPlayResolutionInfo>? = null,
    var resolutionInfos: MutableList<ResolutionInfo>? = null,
    var tvStartTime: String? = null,
    var tvEndTime: String? = null,
    var lookHimSet: String? = null,
    var feeType: Int = FEE_FREE,
    var isMemberPlay: Int = 0,
    var trySeeTime: Long = 0,
    var isExtendNull: Boolean = false,
    var albumTVName: String? = null,
    var trailerTVName: String? = null,
    var tvVerPic: String? = null,
    var videoExtendsPic: String? = null,
    var videoExtendsPic_640: String? = null,
    var cateId: Int? = null,
    var cateCode: Int? = null,
    var maxVideoOrder: Int? = null,
    var varietyPeriod: String? = null,
    var tvInsertAdTime: Double? = null,
)


/**
 * 小窗
 */
const val VIDEO_WINDOW_TYPE_SCALE = 0

/**
 * 悬浮
 */
const val VIDEO_WINDOW_TYPE_FLAT = 1

/**
 * 全屏
 */
const val VIDEO_WINDOW_TYPE_FULL = 2


const val FEE_ALBUM = 1 //电影
const val FEE_VIDEO = 2 //电视剧
const val FEE_FREE = 0


const val SOUTHMEDIA_CHECK_SUCCESS = 0
const val SOUTHMEDIA_CHECK_FAIL = 1
