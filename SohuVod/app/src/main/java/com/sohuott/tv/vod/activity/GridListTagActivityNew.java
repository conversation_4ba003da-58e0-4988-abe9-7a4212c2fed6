package com.sohuott.tv.vod.activity;

import static com.sohuott.tv.vod.widget.TopBar.INDEX_SEARCH_BUTTON;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.GridListLeftAdapterNew;
import com.sohuott.tv.vod.adapter.GridListTagAdapterNew;
import com.sohuott.tv.vod.customview.LoadingView;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.lib.model.GridListTagMenuModel;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.presenter.GridListTagPresenterImplNew;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.CustomRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.GridListTagViewNew;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.widget.TopBar;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wenjingbian on 2017/6/26.
 */

public class GridListTagActivityNew extends BaseActivity implements GridListTagViewNew,
        GridListLeftAdapterNew.IGridListLeftListener, TopBar.TopBarFocusController {

    private static final int MSG_REFRESH = 1;

    private LoadingView mParentLoadingView, mTagLoadingView;
    private LinearLayout mParentErrorView, mTagErrorView;
    private RelativeLayout mTagView;
    private TopBar mTopBar;
    private TextView tv_tag_type, tv_tag_curr_line, tv_tag_divider, tv_tag_sum_line, tv_error_hint;
    private CustomRecyclerView mTagList;
    private CustomLinearRecyclerView mLeftList;
    private FocusBorderView mFocusView;

    private CustomGridLayoutManager mTagLayoutManager;

    private GridListLeftAdapterNew mLeftAdapter;
    private GridListTagAdapterNew mTagAdapter;

    private GridListTagPresenterImplNew mPresenterImpl;
    private MyHandler mHandler;

    private int mSubCateCode;
    private boolean isEnableScrollListener;
    private boolean isFromLeft;
    private boolean isFirstEnter = true;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_list_grid_tag_new);

        initView();
        initData();

        RequestManager.getInstance().onGridTagListNewExposureEvent();
        setPageName("6_grid_list_tag");
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);

        initView();
        initData();
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateTopBar();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //release objects
        mTopBar = null;
        if (mLeftAdapter != null) {
            mLeftAdapter.releaseAll();
            mLeftAdapter = null;
        }
        if (mTagAdapter != null) {
            mTagAdapter.releaseAll();
            mTagAdapter = null;
        }
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
    }

    @Override
    public void displayTagErrorView(String errorStr) {
        mTagErrorView.setVisibility(View.VISIBLE);
        tv_error_hint.setText(errorStr);
        mTagLoadingView.setVisibility(View.GONE);
        mTagList.setVisibility(View.GONE);
    }

    @Override
    public void displayTagLoadingView() {
        mTagLoadingView.setVisibility(View.VISIBLE);
        mTagErrorView.setVisibility(View.GONE);
        mTagList.setVisibility(View.GONE);
    }

    @Override
    public void displayTagView(AllLabel.Data data) {
        if (mTagAdapter == null) {
            return;
        }
        mTagList.setVisibility(View.VISIBLE);
        mTagErrorView.setVisibility(View.GONE);
        mTagLoadingView.setVisibility(View.GONE);
        mTagList.setVisibility(View.VISIBLE);

        if (data != null && data.result != null && data.result.size() > 0) {
            mTagAdapter.setLabelList(data.result);
            mTagAdapter.notifyDataSetChanged();

            tv_tag_sum_line.setVisibility(View.VISIBLE);
            tv_tag_sum_line.setText(data.count % 4 == 0 ? data.count / 4 + "行" : data.count / 4 + 1 + "行");
        } else {
            mTagErrorView.setVisibility(View.VISIBLE);
            tv_error_hint.setText(getResources().getString(R.string.data_empty));
        }

        if (mTagList != null && mTagLayoutManager != null && mTagLayoutManager.findFirstCompletelyVisibleItemPosition() != 0) {
            mTagList.smoothScrollToPosition(0);
        }

        isEnableScrollListener = true;
        mLeftAdapter.setEnabledRightKey(true);
    }

    @Override
    public void addTagItems(AllLabel.Data data) {
        if (mTagAdapter == null || data != null && data.count <= 0) {
            return;
        }

        isEnableScrollListener = true;
        if (data.result != null && data.result.size() > 0) {
            mTagAdapter.addLabelItems(data.result);
        }
    }

    @Override
    public void addTagItemsError() {
        isEnableScrollListener = true;
        ToastUtils.showToast(this, "获取新数据失败，请稍后重试！");
    }

    @Override
    public void displayLeftListView(List<GridListTagMenuModel.DataEntity> data) {
        if(mLeftAdapter == null){
            return;
        }
        mTagView.setVisibility(View.VISIBLE);
        mParentErrorView.setVisibility(View.GONE);
        mParentLoadingView.setVisibility(View.GONE);

        List<GridListTagMenuModel.DataEntity> dataList = new ArrayList<>();
        if (mLeftAdapter != null && data != null && data.size() > 0) {
            //add default item
            GridListTagMenuModel.DataEntity dataEntity = new GridListTagMenuModel.DataEntity();
            dataEntity.ishave_label = "1";
            dataEntity.name = "全部";
            dataEntity.id = -1;
            dataList.add(dataEntity);

            for (GridListTagMenuModel.DataEntity tmpData : data) {
                if (!TextUtils.isEmpty(tmpData.ishave_label)
                        && TextUtils.equals(tmpData.ishave_label, "1")) {
                    dataList.add(tmpData);
                }
            }
            mLeftAdapter.setDataList(dataList);
            mLeftAdapter.notifyDataSetChanged();
        }

        if (dataList.size() > 0) {
            mSubCateCode = dataList.get(0).id;
            mHandler.removeMessages(MSG_REFRESH);
            mHandler.sendEmptyMessage(MSG_REFRESH);
        }
        tv_tag_type.setText("影视标签");
    }

    @Override
    public void displayParentErrorView() {
        mParentErrorView.setVisibility(View.VISIBLE);
        mParentLoadingView.setVisibility(View.GONE);
        mTagView.setVisibility(View.GONE);
    }

    @Override
    public void onChangedTabListener(int subCateId) {
        mSubCateCode = subCateId;
        mHandler.removeMessages(MSG_REFRESH);
        mHandler.sendEmptyMessageDelayed(MSG_REFRESH, 500);
    }

    @Override
    public boolean onFocusDown() {
        View focusedView = getCurrentFocus();
        if (mTopBar.getChildViewIndex(focusedView) != -1) {
            if (isFromLeft) {
                if (mLeftList != null && mLeftList.getChildAt(0) != null) {
                    mLeftList.getChildAt(0).requestFocus();
                }
            } else {
                if (mTagList != null && mTagList.getVisibility() == View.VISIBLE
                        && mTagList.getChildAt(0) != null) {
                    mTagList.getChildAt(0).requestFocus();
                }
            }
            return true;
        }
        return false;
    }

    public boolean focusTagList() {
        if (mTagList == null || mTagList.getVisibility() != View.VISIBLE) {
            return false;
        }
        if (mTagList.findViewHolderForAdapterPosition(mTagLayoutManager.findFirstCompletelyVisibleItemPosition()) != null
                && mTagList.findViewHolderForAdapterPosition(mTagLayoutManager.findFirstCompletelyVisibleItemPosition()).itemView != null) {
            mTagList.findViewHolderForAdapterPosition(mTagLayoutManager.findFirstCompletelyVisibleItemPosition()).itemView.requestFocus();
            return true;
        }
        return false;
    }

    public void setCurrLineTxt(int focusedPos) {
        if (focusedPos < 0) {
            tv_tag_curr_line.setVisibility(View.INVISIBLE);
            tv_tag_divider.setVisibility(View.INVISIBLE);
            return;
        }

        if (tv_tag_curr_line.getVisibility() != View.VISIBLE) {
            tv_tag_curr_line.setVisibility(View.VISIBLE);
        }
        if (tv_tag_divider.getVisibility() != View.VISIBLE) {
            tv_tag_divider.setVisibility(View.VISIBLE);
        }
        tv_tag_curr_line.setText(StringUtil.toString(focusedPos / 4 + 1));
    }

    public void setSelectedPos() {
        if (mLeftAdapter == null) {
            return;
        }

        int selectedPos = Math.max(mLeftAdapter.getSelectedPos(), 0);
        if (mLeftList != null && mLeftList.findViewHolderForAdapterPosition(selectedPos) != null
                && mLeftList.findViewHolderForAdapterPosition(selectedPos).itemView != null) {
            mLeftList.findViewHolderForAdapterPosition(selectedPos).itemView.requestFocus();
        }
    }

    public void focusOnTopBar() {
        if (mTopBar != null) {
            mTopBar.focusChildView(INDEX_SEARCH_BUTTON);
        }
    }

    public void setFocusRoute(boolean isFromLeft) {
        this.isFromLeft = isFromLeft;
    }

    private void initView() {
        mTopBar = (TopBar) findViewById(R.id.top_bar);
        tv_tag_type = (TextView) findViewById(R.id.tv_grid_tag_type);
        tv_tag_curr_line = (TextView) findViewById(R.id.tv_tag_curr_line);
        tv_tag_divider = (TextView) findViewById(R.id.tv_tag_divider);
        tv_tag_sum_line = (TextView) findViewById(R.id.tv_tag_sum_line);
        mTagList = (CustomRecyclerView) findViewById(R.id.rv_tag_list);
        mLeftList = (CustomLinearRecyclerView) findViewById(R.id.rv_tag_left_list);
        mParentLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mParentErrorView = (LinearLayout) findViewById(R.id.err_view);
        mTagLoadingView = (LoadingView) findViewById(R.id.layout_tag_loading_view);
        mTagErrorView = (LinearLayout) findViewById(R.id.layout_tag_error_view);
        mTagView = (RelativeLayout) findViewById(R.id.layout_tag_view);
        mFocusView = (FocusBorderView) findViewById(R.id.focus_border_view);
        tv_error_hint = (TextView) mTagErrorView.findViewById(R.id.error_hint);

        mTopBar.setTopBarFocusListener(this);
        //init mLeftLayoutManager
        CustomLinearLayoutManager mLeftLayoutManager = new CustomLinearLayoutManager(this);
        mLeftLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mLeftLayoutManager.setCustomPadding(getResources().getDimensionPixelOffset(R.dimen.y80),
                getResources().getDimensionPixelOffset(R.dimen.y80));
        mLeftList.setLayoutManager(mLeftLayoutManager);
        //init mLeftAdapter
        mLeftAdapter = new GridListLeftAdapterNew(this, mLeftList);
        mLeftAdapter.setGridListLeftListener(this);
        mLeftList.setAdapter(mLeftAdapter);

        //init mTagLayoutManager
        mTagLayoutManager = new CustomGridLayoutManager(this, 4);
        mTagLayoutManager.setCustomPadding(getResources().getDimensionPixelOffset(R.dimen.y320),
                getResources().getDimensionPixelOffset(R.dimen.y320));
//        mTagList.setPadding(0, getResources().getDimensionPixelSize(R.dimen.y120),
//                0, getResources().getDimensionPixelSize(R.dimen.y120));
        mTagList.setLayoutManager(mTagLayoutManager);
        mTagList.setOnScrollListener(new FinishScrollListener());
        mTagList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y50);
            }
        });
        //init mTagAdapter
        mTagAdapter = new GridListTagAdapterNew(this, mTagList);
        mTagAdapter.setFocusView(mFocusView);
        mTagList.setAdapter(mTagAdapter);

        displayParentLoadingView();
    }

    private void initData() {
        mHandler = new MyHandler();

        mPresenterImpl = new GridListTagPresenterImplNew(this);
        mPresenterImpl.setGridListTagView(this);
        mPresenterImpl.requestLeftList();
    }

    private void displayParentLoadingView() {
        mParentLoadingView.setVisibility(View.VISIBLE);
        mParentErrorView.setVisibility(View.GONE);
        mTagView.setVisibility(View.GONE);
    }

    private void updateTopBar() {
        mTopBar.updateMessageView();
        mTopBar.updateVipText();
        mTopBar.updateVipImage();
        mTopBar.updateUserView();
    }

    @SuppressLint("HandlerLeak")
    private class MyHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == MSG_REFRESH) {
                tv_tag_sum_line.setText("");
                isEnableScrollListener = false;
                mPresenterImpl.requestTagList(mSubCateCode);
                if (isFirstEnter) {
                    isFirstEnter = false;
                } else {
                    RequestManager.getInstance().onGridTagListNewClickEvent(mSubCateCode);
                }
            }
        }
    }

    private class FinishScrollListener extends RecyclerView.OnScrollListener {
        @Override
        public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);

            if (!isEnableScrollListener) {
                return;
            }

            int lastVisibleItemPosition = mTagLayoutManager.findLastVisibleItemPosition() + 1;
            int modelsCount = mTagAdapter.getItemCount();

            if (lastVisibleItemPosition + 6 >= modelsCount) {
                isEnableScrollListener = false;
                mPresenterImpl.requestMoreTagListData(mSubCateCode);
            }
        }

        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);

            if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                return;
            }

            if (mTagList == null || mFocusView == null || mTagList.getFocusedChild() == null) {
                return;
            }

            RecyclerView.ViewHolder viewHolder = mTagList.getChildViewHolder(
                    mTagList.getFocusedChild());
            if (viewHolder != null) {
                mFocusView.setFocusView(viewHolder.itemView);
                FocusUtil.setFocusAnimator(viewHolder.itemView, mFocusView, FocusUtil.HOME_SCALE, 100);
            }
            assert viewHolder != null;
            setCurrLineTxt(mTagList.getChildAdapterPosition(viewHolder.itemView));
        }
    }

}
