package com.sohuott.tv.vod.activity.launcher

import GsonConverter
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.google.gson.Gson
import com.sohu.lib_utils.PrefUtil
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.app.AppConstants
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.model.PrivacyInfo
import kotlinx.coroutines.coroutineScope
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class PrivacyAgreementRepository {

    suspend fun fetchPrivacyInfo(): State<PrivacyInfo> {
        //是否展示标识
        val isAlert = PrefUtil.getInt(AppConstants.KEY_ALERT_PRIVACY_VALUE, 1)
        //协议版本
        val version = PrefUtil.getInt(AppConstants.KEY_ALERT_PRIVACY_VERSION, 0)
        //数据
        val json = PrefUtil.getString(AppConstants.KEY_ALERT_PRIVACY_DATA, "")
        //不展示返回空数据
        if (isAlert == 0) {
            return State.empty()
        }
        //本地数据不为空
        if (json.isNotEmpty()) {
            //解析数据
            val data: PrivacyInfo? = Gson().fromJson(json, PrivacyInfo::class.java)
            //判断数据是否为空
            if (data?.version == null) {
                return State.empty()
            }
            //比较版本
            if (data.version > version) {
                return State.success(data)
            }
            return State.empty()
        }
        return requestSavePrivacyInfo()
    }

    suspend fun requestSavePrivacyInfo(): State<PrivacyInfo> {
        return coroutineScope {
            val data =
                Get<PrivacyInfo?>("${RetrofitApi.get().retrofitHost.baseHost}common/getConfigInfo.json?key=privacy_agreement") {
                    setHeaders(HeaderHelper.getHeaders().toHeaders())
                    setClient {
                        trustSSLCertificate()
                        connectionSpecs(
                            listOf(
                                ConnectionSpec.MODERN_TLS,
                                ConnectionSpec.COMPATIBLE_TLS,
                                ConnectionSpec.CLEARTEXT
                            )
                        )
                    }
                    converter = GsonConverter()
                }.await()
            if (data != null) {
                PrefUtil.putString(AppConstants.KEY_ALERT_PRIVACY_DATA, Gson().toJson(data))
                State.success(data)
            } else {
                State.empty()
            }
        }
    }
}