package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.model.SearchResult;

import java.util.HashMap;
import java.util.Map;

import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by feng<PERSON><PERSON> on 17-6-22.
 */

public class SearchResultPresenter implements SearchResultContract.Presenter {

    private static final String TAG =  SearchResultPresenter.class.getSimpleName();

    private Map<Integer, Disposable> disposableMap;

    private SearchResultContract.SearchResultView searchResultView;

    public SearchResultPresenter(SearchResultContract.SearchResultView searchResultView) {
        this.searchResultView = searchResultView;
        disposableMap = new HashMap<>();
    }

    @Override
    public void getSearchResult(String query, final int type, int page, int pageSize) {
        DisposableObserver<SearchResult> disposableObserver = NetworkApi.getSearchResult(query, type, page, pageSize, new DisposableObserver<SearchResult>() {
            @Override
            public void onNext(SearchResult value) {
                if(searchResultView != null) {
                    if(value != null && value.getData() != null) {
                        searchResultView.showSearchResult(value.getData(), type);
                    } else {
                        searchResultView.showSearchResult(null, type);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                if(searchResultView != null) {
                    searchResultView.showSearchResult(null, type);
                }
            }

            @Override
            public void onComplete() {

            }
        });
        disposableMap.put(type, disposableObserver);
    }


    @Override
    public void cancel(int type) {
        Disposable disposableObserver = disposableMap.remove(type);
        if(disposableObserver != null && !disposableObserver.isDisposed()) {
            disposableObserver.dispose();
        }
    }

}
