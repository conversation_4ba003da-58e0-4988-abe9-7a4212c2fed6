package com.sohuott.tv.vod.view.scalemenu.presenter

import android.content.Context
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.setPadding
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.getResDrawable
import com.sohuott.tv.vod.activity.base.isVisible
import com.sohuott.tv.vod.activity.base.scaleXY
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentMoreMenuItem

/**
 * 更多功能
 */
class ScaleContentMenuMorePresenter constructor(private val context: Context) :
    DefaultPresenter(R.layout.item_sacle_menu_more_layout) {
    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as ScaleContentMoreMenuItem
        val title = viewHolder.getView<TextView>(R.id.tv_sacle_menu_more_title)
        val layout = viewHolder.getView<ConstraintLayout>(R.id.cl_sacle_menu_more)
        val parentLayout = viewHolder.getView<ConstraintLayout>(R.id.cl_parent_layout_more)
        val content = viewHolder.getView<TextView>(R.id.tv_sacle_menu_more)
        title.isVisible(item.hasTitle)
        item.titleContent?.let {
            title.text = it
            title.isFocusable=false
        }
        content.text = item.content
        if (item.hasCurrentSelected) {
            content.setTextColor(
                ContextCompat.getColor(
                    context, R.color.tv_color_ff6247
                )
            )
        } else {
            content.setTextColor(
                ContextCompat.getColor(
                    context, R.color.tv_color_e8e8ff
                )
            )
        }
        layout.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                content.setTextColor(
                    ContextCompat.getColor(
                        context, R.color.tv_color_e8e8ff
                    )
                )
                layout.background = context.getResDrawable(R.drawable.bg_select_focus_e4705c_radius_11)
                v.scaleXY(1.1f)
            } else {
                if (item.hasCurrentSelected) {
                    content.setTextColor(
                        ContextCompat.getColor(
                            context, R.color.tv_color_ff6247
                        )
                    )
                } else {
                    content.setTextColor(
                        ContextCompat.getColor(
                            context, R.color.tv_color_e8e8ff
                        )
                    )
                }
                layout.background = context.getResDrawable(R.drawable.bg_radius_10_color_2effffff)
                v.scaleXY(1f)
            }
        }

        if (item.titleContent.equals("片头片尾")) {
            parentLayout.setPadding(content.resources.getDimension(R.dimen.x29).toInt(), 0, 0, 0)
        } else {
            parentLayout.setPadding(0)
        }

    }
}