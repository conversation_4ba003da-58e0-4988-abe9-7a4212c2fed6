package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.KeyEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.LiveTvStatusModel;
import com.sohuott.tv.vod.presenter.IBasePresenter;
import com.sohuott.tv.vod.presenter.LiveTvStatusPresenter;
import com.sohuott.tv.vod.utils.LoadPicture;
import com.sohuott.tv.vod.utils.ParamConstant;

import java.io.IOException;
import java.lang.ref.WeakReference;

/**
 * 电视直播页面
 *
 * <AUTHOR>
 * @Date Created on 2020/1/8.
 */
public class LiveTvActivity extends BaseActivity implements IBasePresenter.IDataListener<LiveTvStatusModel> {

    private String mLiveTvUrl;
    private String mLiveTvUrlBakup;
    private String mBgUrl;
    private int mRoomId;
    private SurfaceView mSurfaceView;
    private TextView mTextView;
    private MediaPlayer mMediaPlayer;
    private LiveTvStatusPresenter mPresenter;

    private Bitmap mCloseBgBitmap = null;

    private boolean loop_flag = true;

    private static final int LIVE_STATUS_WHAT = 1;
    private static final int GET_STATUS_TIME = 10000;

    private static class InnerHandler extends Handler {
        private WeakReference<LiveTvActivity> mActivityWrapper;

        InnerHandler(LiveTvActivity activity) {
            mActivityWrapper = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            LiveTvActivity ac = mActivityWrapper.get();
            if (ac != null) {
                LibDeprecatedLogger.d("Receive a message: " + msg.what);
                switch (msg.what) {
                    case LIVE_STATUS_WHAT: //定时刷直播流状态
                        Integer liveStatus = (Integer) msg.obj;
                        LibDeprecatedLogger.d("Live status handle: " + liveStatus + "," + ac.loop_flag);
                        if (liveStatus == 0) {
                            ac.stopPlayer();
                        } else if (ac.loop_flag) {
                            ac.getLiveTvStatus();
                        }
                        break;
                    case LoadPicture.HANDLER_WHAT: //获得背景图信息
                        byte[] imageBg = (byte[]) msg.obj;
                        if (imageBg != null) {
                            ac.mCloseBgBitmap = BitmapFactory.decodeByteArray(imageBg, 0, imageBg.length);
                        }
                        break;

                }
            }
        }
    }

    private Handler mHandler = new InnerHandler(this);

    @Override
    public void onCreate(Bundle savedInstanceState) {
        LibDeprecatedLogger.i("onCreate");
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_live_tv);
        loop_flag = true;
        mMediaPlayer = new MediaPlayer();
        this.initData();
        this.initView();
    }

    @Override
    protected void onStart() {
        LibDeprecatedLogger.i("onStart");
        try {
            this.startPlayer();
        } catch (IOException e) {
            LibDeprecatedLogger.e("Start player fail!", e);
        }
        super.onStart();
    }

    private void initView() {
        mSurfaceView = (SurfaceView) this.findViewById(R.id.sv_liveTv_player);
        mTextView = (TextView) this.findViewById(R.id.tv_liveTv_loading);
        mTextView.setVisibility(View.VISIBLE);
    }

    private void initData() {
        mLiveTvUrl = getIntent().getStringExtra(ParamConstant.PARAM_LIVE_TV_URL);
        mLiveTvUrlBakup = getIntent().getStringExtra(ParamConstant.PARAM_LIVE_TV_URL_BAKUP);
        mRoomId = getIntent().getIntExtra(ParamConstant.PARAM_LIVE_ROOM_ID, -1);
        mBgUrl = getIntent().getStringExtra(ParamConstant.PARAM_LIVE_TV_PIC);
        LibDeprecatedLogger.d("Init data. Url: " + mLiveTvUrl + ", roomId: " + mRoomId + ", bg: " + mBgUrl);
        LibDeprecatedLogger.d("Init data. Url bakup: " + mLiveTvUrlBakup);
        // 轮询获取播放状态
        mPresenter = new LiveTvStatusPresenter(this, mRoomId);
        this.getLiveTvStatus();
        // 下载无播内容时背景图
        if (mBgUrl != null) {
            LoadPicture pictureUtil = new LoadPicture(this, mHandler);
            pictureUtil.load(mBgUrl);
//            pictureUtil.setLoadUrl(mBgUrl);
//            mHandler.post(pictureUtil);
        }
    }

    private void getLiveTvStatus() {
        LibDeprecatedLogger.d("Get live status!");
        mHandler.postDelayed(mPresenter, GET_STATUS_TIME);
    }

    private void startPlayer() throws IOException {
        String liveUrl = this.urlSelector();
        if (liveUrl == null) {
            LibDeprecatedLogger.w("Player url is empty");
            return;
        }
//        if (mLiveTvUrl == null || mLiveTvUrl.isEmpty()) {
//            AppLogger.w("Player url is empty");
//            return;
//        }
//        mMediaPlayer.setDataSource(mLiveTvUrl);
        LibDeprecatedLogger.w("Player url is:" + liveUrl);
        mMediaPlayer.setDataSource(liveUrl);
        mMediaPlayer.prepareAsync();
        final SurfaceHolder surfaceHolder = mSurfaceView.getHolder();
        surfaceHolder.setFixedSize(mMediaPlayer.getVideoWidth(), mMediaPlayer.getVideoHeight());
        mMediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                LibDeprecatedLogger.d("Player Prepared and ready play!");
                mMediaPlayer.setDisplay(surfaceHolder);
                mMediaPlayer.start();
                mTextView.setVisibility(View.GONE);
            }
        });
        mMediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
            @Override
            public boolean onError(MediaPlayer mp, int what, int extra) {
                LibDeprecatedLogger.e("MediaPlayer error! what: " + what);
                LibDeprecatedLogger.e("MediaPlayer error! extra: " + extra);
                return false;
            }
        });
        mMediaPlayer.setOnBufferingUpdateListener(new MediaPlayer.OnBufferingUpdateListener() {
            @Override
            public void onBufferingUpdate(MediaPlayer mp, int percent) {
                LibDeprecatedLogger.d("Player BufferingUpdate： " + percent);
            }
        });
        mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                LibDeprecatedLogger.d("Player Completion！ ");
            }
        });
        mMediaPlayer.setOnInfoListener(new MediaPlayer.OnInfoListener() {
            @Override
            public boolean onInfo(MediaPlayer mp, int what, int extra) {
                LibDeprecatedLogger.d("MediaPlayer info! what: " + ",extra: " + extra);
                return false;
            }
        });
        mMediaPlayer.setOnSeekCompleteListener(new MediaPlayer.OnSeekCompleteListener() {
            @Override
            public void onSeekComplete(MediaPlayer mp) {
                LibDeprecatedLogger.d("MediaPlayer seek complete!");
            }
        });
    }

    /**
     * 主、备直播地址的选择
     *
     * @return
     */
    private String urlSelector() {
        String adapterReuslt = null;
        String deviceModel = Build.MODEL;
        // 极米适配使用备份直播地址
        if (deviceModel != null && deviceModel.contains("XGIMI")) {
            LibDeprecatedLogger.d("Live device: " + deviceModel);
            adapterReuslt = mLiveTvUrlBakup;
        }

        if (adapterReuslt != null && !adapterReuslt.isEmpty()) {
            LibDeprecatedLogger.d("Live fit url: " + adapterReuslt);
            return adapterReuslt;
        } else if (mLiveTvUrl != null && !mLiveTvUrl.isEmpty()) {
            return mLiveTvUrl;
        } else if (mLiveTvUrlBakup != null && !mLiveTvUrlBakup.isEmpty()) {
            return mLiveTvUrlBakup;
        }
        return null;
    }

    private void stopPlayer() {
        LibDeprecatedLogger.d("Stop live player!");
        if (mMediaPlayer != null && mMediaPlayer.isPlaying()) {
            mMediaPlayer.stop();
            mMediaPlayer.release();
            mMediaPlayer = null;
        }
        if (mCloseBgBitmap != null) {
            LibDeprecatedLogger.d("Live stop and change background!");
            mSurfaceView.setBackgroundDrawable(new BitmapDrawable(mCloseBgBitmap));
            mTextView.setVisibility(View.GONE);
        } else {
            LibDeprecatedLogger.d("Live stop and display text!");
            mTextView.setText(R.string.live_tv_end);
            mTextView.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void onResume() {
        LibDeprecatedLogger.i("onResume");
        super.onResume();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        LibDeprecatedLogger.i("onNewIntent");
        super.onNewIntent(intent);
    }

    @Override
    protected void onPause() {
        LibDeprecatedLogger.i("onPause");
        super.onPause();
    }

    @Override
    protected void onStop() {
        LibDeprecatedLogger.i("onStop");
        if (mMediaPlayer != null && mMediaPlayer.isPlaying()) {
            mMediaPlayer.stop();
            mMediaPlayer.release();
            mMediaPlayer = null;
        }
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        LibDeprecatedLogger.i("onDestroy");
        if (mPresenter != null) {
            mPresenter.release();
        }
        if (mCloseBgBitmap != null && !mCloseBgBitmap.isRecycled()) {
            mCloseBgBitmap.recycle();
            mCloseBgBitmap = null;
        }
        loop_flag = false;
        super.onDestroy();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP) {
            this.finish();
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void getData(LiveTvStatusModel data) {
        LibDeprecatedLogger.d(data == null ? "data is null" : data.toString());
        Integer liveStatus = -1;
        if (data == null || data.getData() == null) {
            liveStatus = -1;
        } else {
            try {
                liveStatus = Integer.parseInt(data.getData());
            } catch (NumberFormatException e) {
                LibDeprecatedLogger.w("Live status error!", e);
            }
        }
        Message msg = Message.obtain();
        msg.what = LIVE_STATUS_WHAT;
        msg.obj = liveStatus;
        mHandler.sendMessage(msg);

    }
}
