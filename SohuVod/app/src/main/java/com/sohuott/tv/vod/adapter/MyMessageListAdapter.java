package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.ServerMessage;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.List;

/**
 * Created by wenjingbian on 2016/3/24.
 */
public class MyMessageListAdapter extends RecyclerView.Adapter<MyMessageListAdapter.ViewHolder> {

    private List<ServerMessage.Data> mMessageList;

    private Context mContext;
    private FocusBorderView mFocusBorderView;
    private OnItemListen mOnItemListen;
    private int mSelectedPos = 0;
    private RecyclerView mRecyclerView;

    public static interface OnItemListen{
        void onClick(int position);
    }

    public MyMessageListAdapter(Context context,RecyclerView recyclerView, List<ServerMessage.Data> messageList) {
        this.mContext = context.getApplicationContext();
        this.mMessageList = messageList;
        this.mRecyclerView = recyclerView;
    }

    public void setOnItemListen(OnItemListen listen){
        mOnItemListen = listen;
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        mFocusBorderView = focusView;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.my_message_item, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(ViewHolder mViewHolder, int position) {
        mViewHolder.tv_title.setText(mMessageList.get(position).name);
        mViewHolder.tv_content.setText(mMessageList.get(position).content);
        mViewHolder.tv_date.setText(mMessageList.get(position).createTime);
        mViewHolder.iv_icon.setImageRes(mMessageList.get(position).picUrl,mContext.getResources().getDrawable(R.drawable.ic_message_default),mContext.getResources().getDrawable(R.drawable.ic_message_default));
        if(position == mSelectedPos){
            mViewHolder.itemView.requestFocus();
        }
    }



    @Override
    public int getItemCount() {
        int totalCount = mMessageList == null ? 0 : mMessageList.size();
        return totalCount;
    }


    public void setMessageList(List<ServerMessage.Data> messageList) {
        mMessageList = messageList;
    }

    public List<ServerMessage.Data> getMessageList() {
        return mMessageList;
    }

    public class ViewHolder extends RecyclerView.ViewHolder{
        public TextView tv_title, tv_content, tv_date;
        public GlideImageView iv_icon;

        public ViewHolder(View itemView) {
            super(itemView);
            tv_title = (TextView) itemView.findViewById(R.id.tv_title);
            tv_content = (TextView) itemView.findViewById(R.id.tv_content);
            tv_date = (TextView) itemView.findViewById(R.id.tv_date);
            iv_icon = (GlideImageView) itemView.findViewById(R.id.iv_icon);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(mOnItemListen != null){
                        mOnItemListen.onClick(getAdapterPosition());
                    }
                }
            });

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if(hasFocus){
                        if(mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                            if(mFocusBorderView != null){
                                mFocusBorderView.setFocusView(v);
                            }
                            FocusUtil.setFocusAnimator(v, mFocusBorderView);
                        }
                        mSelectedPos = getAdapterPosition();
                    }else {
                        if(mFocusBorderView != null){
                            mFocusBorderView.setUnFocusView(v);
                        }
                        FocusUtil.setUnFocusAnimator(v);
                    }
                }
            });

        }
    }
}
