package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

/**
 * Created by feng<PERSON><PERSON> on 17-7-14.
 */

public class SearchInputLinearlayout extends LinearLayout {

    public SearchInputLinearlayout(Context context) {
        super(context);
    }

    public SearchInputLinearlayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SearchInputLinearlayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public View focusSearch(int direction) {
        return super.focusSearch(direction);
    }

    @Override
    public View focusSearch(View focused, int direction) {
        return super.focusSearch(focused, direction);
    }

    @Override
    public boolean requestFocus(int direction, Rect previouslyFocusedRect) {
        return super.requestFocus(direction, previouslyFocusedRect);
    }

}
