package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import com.sh.ott.video.base.component.ShPlayerConstants
import com.sh.ott.video.player.PlayerConstants
import com.sohuott.tv.vod.activity.base.gone

/**
 * 暂停广告展示
 */
class VideoAdPauseImageComponent constructor(context: Context) :
    BaseVideoAdPauseComponent(context) {


    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        super.onPlayStateChanged(playState, extras)
        if (playState != PlayerConstants.VideoState.PAUSED && adType == ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_PAUSE) {
            gone()
            pauseImageView?.gone()
            pauseTextView?.gone()
            tvPauseImageViewTips?.gone()
        }
    }
}