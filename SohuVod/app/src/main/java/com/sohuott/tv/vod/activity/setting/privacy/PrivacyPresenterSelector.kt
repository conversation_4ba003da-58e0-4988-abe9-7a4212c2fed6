package com.sohuott.tv.vod.activity.setting.privacy

import android.content.Context
import androidx.leanback.widget.ListRow
import androidx.leanback.widget.Presenter
import androidx.leanback.widget.PresenterSelector
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingHeaderItem
import com.sohuott.tv.vod.lib.model.privacy.PrivacySettingTipItem

/**
 *
 * @Description
 * @date 2022/3/22 14:35
 * <AUTHOR>
 * @Version 1.0
 */
class PrivacyPresenterSelector(private val context: Context?) : PresenterSelector() {
    override fun getPresenter(item: Any?): Presenter {
        when (item) {
            is ListRow -> {
                return PrivacySwitchListRowPresenter(context).apply {
//                    shadowEnabled=false
                    //放大效果关键设置
                    selectEffectEnabled=false
                    isKeepChildForeground=false
//                    enableChildRoundedCorners(false)
                }
            }
            is PrivacySettingTipItem -> {
                return PrivacyTipPresenter()
            }
            is PrivacySettingHeaderItem -> {
                return PrivacyHeaderPresenter()
            }
        }
        throw RuntimeException("PrivacyPresenterSelector is not found this item ")
    }
}