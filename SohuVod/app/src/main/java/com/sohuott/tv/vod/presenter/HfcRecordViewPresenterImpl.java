package com.sohuott.tv.vod.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.Collection;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.BookedRecord;
import com.sohuott.tv.vod.lib.model.BookedRecordResult;
import com.sohuott.tv.vod.lib.model.SubjectIdInfo;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.model.VideoFavorListBean;
import com.sohuott.tv.vod.lib.service.PlayHistoryService;
import com.sohuott.tv.vod.lib.service.ThirdPartyService;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.utils.CollectionRecordHelper;
import com.sohuott.tv.vod.view.HfcRecordView;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;

import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_BOOKED;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_COLLECTION;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_FAVOR;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_HISTORY;

/**
 * Created by wenjingbian on 2017/4/1.
 */

public class HfcRecordViewPresenterImpl implements CollectionRecordHelper.ICollectionRecordListener {
    private static final int RECOMMEND_LIST_COUNT = 5;

    private HfcRecordView mHfcRecordView;

    private Context mContext;

    private PlayHistoryService mPlayHistoryService;

    private CollectionRecordHelper mCollectionRecordHelper;

    public HfcRecordViewPresenterImpl(Context context) {
        this.mContext = context;
        mPlayHistoryService = new PlayHistoryService(context);
        mCollectionRecordHelper = new CollectionRecordHelper(context, false);
        mCollectionRecordHelper.setICollectionListener(this);
    }

    @Override
    public void onAdd(int aid, boolean isSuccess) {
        //do Nothing
    }

    @Override
    public void onDelete(int aid, boolean isSuccess) {
        if (aid == CollectionRecordHelper.ALL_DELETED_RECORDS_ID) {
            mHfcRecordView.hideParentLoadingView();
            if (!isSuccess) {
                ToastUtils.showToast(mContext, "追剧收藏取消出现错误");
            }
        } else {
            if (isSuccess) {
                mHfcRecordView.updateCollectionRecordView(aid);
            } else {
                mHfcRecordView.updateCollectionRecordFailedView(aid);
            }
        }
    }

    @Override
    public void onRequestData(List<?> dataList) {
        if (dataList != null) {
            mHfcRecordView.updateCollectionRecordView((List<Collection>) dataList);
        } else {
            mHfcRecordView.showChildErrorView(LIST_INDEX_COLLECTION);
        }
    }

    @Override
    public void onRequestMoreData(List<?> dataList) {
        if (dataList != null && !dataList.isEmpty()) {
            mHfcRecordView.onAddMoreCollectionRecord((List<Collection>) dataList);
        }
    }

    @Override
    public void onSelectRecord(boolean isCollectRecordInChildVersion) {
        //do nothing, just for child version
    }

    public void setHfcRecordView(HfcRecordView hfcRecordView) {
        this.mHfcRecordView = hfcRecordView;
    }

    /**
     * Request history records
     *
     * @param isEdu   是否是教育资源
     * @param onlyVRS 是否只获取VRS历史
     */
    public void requestHistoryList(boolean isEdu, final boolean onlyVRS) {
        PlayHistoryService.PlayHistoryListener listener = new PlayHistoryService.PlayHistoryListener() {
            @Override
            public void onSuccess(List<PlayHistory> playHistoryList) {
                LibDeprecatedLogger.d("requestHistoryList(): onSuccess()");
                if (playHistoryList == null) {
                    mHfcRecordView.showEmptyView();
                    return;
                }
                mHfcRecordView.updateHistoryRecordView(playHistoryList, onlyVRS);
            }

            @Override
            public void onFail(String reason, List<PlayHistory> playHistoryList) {
                LibDeprecatedLogger.e("requestHistoryList(): onError()--" + reason);
                mHfcRecordView.showChildErrorView(LIST_INDEX_HISTORY);
            }
        };
        if (isEdu) {
            mPlayHistoryService.getAllEduHistory(listener);
        } else {
            if (onlyVRS) {
                mPlayHistoryService.getAllVrsPlayHistory(listener);
            } else {
                mPlayHistoryService.getAllPlayHistory(listener);
            }
        }
    }

    public void requestMoreHistoryList(final boolean onlyVRS) {
        PlayHistoryService.PlayHistoryListener listener = new PlayHistoryService.PlayHistoryListener() {
            @Override
            public void onSuccess(List<PlayHistory> playHistoryList) {
                LibDeprecatedLogger.d("requestMoreHistoryList(): onSuccess()");
                mHfcRecordView.onAddMoreHistoryRecord(playHistoryList, onlyVRS);
            }

            @Override
            public void onFail(String reason, List<PlayHistory> playHistoryList) {
                LibDeprecatedLogger.e("requestMoreHistoryList(): onError()--" + reason);
            }
        };
        mPlayHistoryService.getMorePlayHistory(onlyVRS, listener);
    }

    /**
     * Request collection data by network
     */
    public void requestCollectionList(boolean isEdu) {
        mCollectionRecordHelper.requestRecordList(isEdu);
    }

    public void requestMoreCollectionList() {
        mCollectionRecordHelper.requestMoreRecordListFromCloud();
    }

    /**
     * Request favor data with passport
     *
     * @param key user passport when login got.
     */
    public void requestFavorList(String key, boolean isLogin) {
        if (isLogin) {
            NetworkApi.getVideoListForFavorInPassport(key, new FavorObserver());
        } else {
            NetworkApi.getVideoListForFavorInDeviceId(key, new FavorObserver());
        }
    }

    public void requestBookedVideoList(String passport, String token) {
        NetworkApi.getBookedVideoList(passport, token, new Observer<BookedRecord>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(BookedRecord value) {
                LibDeprecatedLogger.d("requestBookedVideoList(): onNext().");
                if (value != null && value.getData() != null) {
                    mHfcRecordView.updateBookedRecordView(value.getData());
                } else {
                    mHfcRecordView.showChildErrorView(LIST_INDEX_BOOKED);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestBookedVideoList(): onError()--" + e.getMessage());
                mHfcRecordView.showChildErrorView(LIST_INDEX_BOOKED);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestBookedVideoList(): onComplete().");
            }
        });
    }

    /**
     * Request history record by its id
     *
     * @param dataType data type of the appointed resource
     * @param id       album id or video id of the appointed resource
     */
    public void requestHistoryItemById(int dataType, final int id) {
        mPlayHistoryService.getPlayHistoryById(dataType, id, new PlayHistoryService.PlayHistoryListener() {
            @Override
            public void onSuccess(List<PlayHistory> playHistoryList) {
                LibDeprecatedLogger.d("requestHistoryItemById(): onSuccess()");
                if (playHistoryList != null && playHistoryList.size() > 0) {
                    mHfcRecordView.updateHistoryItemView(playHistoryList.get(0));
                } else {
                    LibDeprecatedLogger.d("History record named " + id + "has been deleted from other devices.");
                }
            }

            @Override
            public void onFail(String reason, List<PlayHistory> playHistoryList) {
                LibDeprecatedLogger.e("Failed to get history record by id, reason is " + reason);
            }
        });
    }

    public Observable<PlayHistory> requestLocalHistoryById(int dataType, int id) {
        return Observable.create(new ObservableOnSubscribe<PlayHistory>() {
            @Override
            public void subscribe(ObservableEmitter<PlayHistory> e) throws Exception {
                mPlayHistoryService.getPlayHistoryById(dataType, id, new PlayHistoryService.PlayHistoryListener() {
                    @Override
                    public void onSuccess(List<PlayHistory> playHistoryList) {
                        if (playHistoryList != null && playHistoryList.size() > 0) {
                            e.onNext(playHistoryList.get(0));
                        }
                        e.onComplete();
                    }

                    @Override
                    public void onFail(String reason, List<PlayHistory> playHistoryList) {
                        e.onError(new Throwable(reason));
                    }
                });
            }
        });

    }

    /**
     * Request personal recommended data by network with the certain track entry.
     *
     * @param trackEntry different trackEntry strings
     *                   TRACK_ENTRY_HISTORY = "4"
     *                   TRACK_ENTRY_COLLECTION = "5"
     *                   TRACK_ENTRY_FAVOR = "6"
     */
    public void requestPersonalRecommendList(final String trackEntry) {
        NetworkApi.getPersonalRecommend(UserInfoHelper.getGid(), 0, trackEntry, RECOMMEND_LIST_COUNT,
                new DisposableObserver<VideoDetailRecommend>() {
                    @Override
                    public void onNext(VideoDetailRecommend value) {
                        LibDeprecatedLogger.d("requestPersonalRecommendList(): onNext()");
                        if (value != null && value.getStatus() == 0) {
                            LibDeprecatedLogger.d("request personal recommended list successfully.");
                            mHfcRecordView.updatePersonalRecommendView(trackEntry, value.getData());
                        } else {
                            //TODO: Catch exception
                            LibDeprecatedLogger.d("Fail to request personal recommended list.");
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.e("requestPersonalRecommendList(): onError()--" + e.toString());
                        //TODO: Catch exception
                    }

                    @Override
                    public void onComplete() {
                        LibDeprecatedLogger.d("requestPersonalRecommendList(): onComplete()");
                    }
                });
    }

    public void clearLocalHistoryData(boolean iEdu) {
        mHfcRecordView.showParentLoadingView();
        mPlayHistoryService.deleteLocalPlayHistory(iEdu, new PlayHistoryService.PlayHistoryListener() {
            @Override
            public void onSuccess(List<PlayHistory> playHistoryList) {
                LibDeprecatedLogger.d("Delete all history data successfully.");
                mHfcRecordView.hideParentLoadingView();
            }

            @Override
            public void onFail(String reason, List<PlayHistory> playHistoryList) {
                mHfcRecordView.hideParentLoadingView();
                ToastUtils.showToast(mContext, "本地历史记录删除出现错误");
            }
        });
    }

    /**
     * Clear all history records
     */
    public void clearAllHistoryData(boolean isEdu) {
        mHfcRecordView.showParentLoadingView();
        mPlayHistoryService.deleteAllPlayHistory(isEdu, new PlayHistoryService.PlayHistoryListener() {
            @Override
            public void onSuccess(List<PlayHistory> playHistoryList) {
                LibDeprecatedLogger.d("Delete all history data successfully.");
                mHfcRecordView.hideParentLoadingView();
                if (ThirdPartyService.getService(mContext) != null) {
                    ThirdPartyService.getService(mContext).historyDeleteAll();
                    ThirdPartyService.release();
                }
            }

            @Override
            public void onFail(String reason, List<PlayHistory> playHistoryList) {
                mHfcRecordView.hideParentLoadingView();
                ToastUtils.showToast(mContext, "历史记录删除出现错误");
            }
        });
    }

    public void clearAllCollectionData(boolean isEud) {
        mHfcRecordView.showParentLoadingView();
        mCollectionRecordHelper.deleteAllRecord(isEud);
    }

    public void clearLocalCollectionData(boolean isEdu) {
        mCollectionRecordHelper.deleteAllLocalRecord(isEdu);
    }

    public void clearAllBookedRecord(String passport, String token) {
        mHfcRecordView.showParentLoadingView();
        NetworkApi.cancelAllBookedVideo(passport, token, new Observer<BookedRecordResult>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(BookedRecordResult value) {
                LibDeprecatedLogger.d("clearAllBookedRecord(): onNext().");
                if (value != null && value.getData() != null && value.getData().getOperResult() != null
                        && value.getData().getOperResult().size() > 0) {
                    for (BookedRecordResult.DataBean.OperResultBean tmpResult : value.getData().getOperResult()) {
                        if (tmpResult != null && tmpResult.isResult()) {
                            continue;
                        } else {
                            break;
                        }
                    }
                    LibDeprecatedLogger.d("Cancel all booked records successfully.");
                    mHfcRecordView.hideParentLoadingView();
                    return;
                }
                mHfcRecordView.hideParentLoadingView();
                ToastUtils.showToast(mContext, mContext.getResources().getString(R.string.txt_fragment_booked_delete_fail));

            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("clearAllBookedRecord(): onError()--" + e.getMessage());
                mHfcRecordView.hideParentLoadingView();
                ToastUtils.showToast(mContext, mContext.getResources().getString(R.string.txt_fragment_booked_delete_fail));
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("clearAllBookedRecord(): onComplete().");
            }
        });
    }

    public void clearBookedRecord(String passport, final String aid, String token) {
        NetworkApi.cancelBookedVideoById(passport, aid, token, new Observer<BookedRecordResult>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(BookedRecordResult value) {
                LibDeprecatedLogger.d("clearBookedRecord(): onNext().");
                if (value != null && value.getData() != null && value.getData().getOperResult() != null
                        && value.getData().getOperResult().size() > 0) {
                    if (value.getData().getOperResult().get(0).isResult()) {
                        mHfcRecordView.updateBookedRecordView(aid);
                    } else {
                        mHfcRecordView.updateBookedRecordFailedView(aid);
                    }
                } else {
                    mHfcRecordView.updateBookedRecordFailedView(aid);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("clearBookedRecord(): onError()--" + e.getMessage());
                mHfcRecordView.updateBookedRecordFailedView(aid);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("clearBookedRecord(): onComplete().");
            }
        });
    }

    /**
     * Request to clear the appointed history record
     *
     * @param dataType
     * @param id       data position in data source list that will be cleared.
     */
    public void clearHistoryRecord(int dataType, int id, int aid, int vid, boolean isEdu) {
        if (mPlayHistoryService == null) {
            return;
        }
        mPlayHistoryService.deletePlayHistory(dataType, id, aid, vid, isEdu, new HistoryDeleteListener(id, dataType));
    }

    /**
     * Request to clear the appointed collection record
     *
     * @param aid albumId you want to cancel collection
     */
    public void clearCollectionRecordItem(int aid, boolean isChased, boolean isEdu) {
        mCollectionRecordHelper.deleteRecordById(aid, isChased, isEdu);
    }

    public void requestSubjectId() {
        NetworkApi.getSubjectIdInfo(new Observer<SubjectIdInfo>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(SubjectIdInfo value) {
                LibDeprecatedLogger.d("requestSubjectId(): onNext().");
                if (value != null && !TextUtils.isEmpty(value.getData())) {
                    mHfcRecordView.setSubjectIdInfo(value.getData());
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestSubjectId(): onError()--" + e.getMessage());
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestSubjectId(): onComplete().");
            }
        });
    }

    private class HistoryDeleteListener implements PlayHistoryService.PlayHistoryListener {

        private int id;
        private int dataType;

        public HistoryDeleteListener(int id, int dataType) {
            this.id = id;
            this.dataType = dataType;
        }

        @Override
        public void onSuccess(List<PlayHistory> playHistoryList) {
            LibDeprecatedLogger.d("HistoryDeleteListener, onSuccess()");
            mHfcRecordView.updateHistoryRecordView(id);

            if (ThirdPartyService.getService(mContext) != null && dataType == Constant.DATA_TYPE_VRS) {
                PlayHistory playHistory = new PlayHistory();
                playHistory.setAlbumId(id);
                playHistory.setDataType(dataType);
                ThirdPartyService.getService(mContext).historyDelete(playHistory);
                ThirdPartyService.release();
            }
        }

        @Override
        public void onFail(String reason, List<PlayHistory> playHistoryList) {
            LibDeprecatedLogger.d("HistoryDeleteListener, onFail()");
            mHfcRecordView.updateHistoryRecordFailedView(id);
        }
    }

    private class FavorObserver implements Observer<VideoFavorListBean> {
        @Override
        public void onSubscribe(Disposable d) {

        }

        @Override
        public void onNext(VideoFavorListBean value) {
            LibDeprecatedLogger.d("FavorObserver, onNext()");
            if (value != null && value.status == 0) {
                LibDeprecatedLogger.d("Request favor data successfully.");
                //Cache favor data list and update view
                mHfcRecordView.updateFavorRecordView(value.data.result);
            } else {
                LibDeprecatedLogger.d("Fail to get favor data.");
                mHfcRecordView.showChildErrorView(LIST_INDEX_FAVOR);
            }
        }

        @Override
        public void onError(Throwable e) {
            LibDeprecatedLogger.e("FavorObserver, onError(): " + e);
            mHfcRecordView.showChildErrorView(LIST_INDEX_FAVOR);
        }

        @Override
        public void onComplete() {
            LibDeprecatedLogger.d("FavorObserver, onComplete()");
        }
    }

}
