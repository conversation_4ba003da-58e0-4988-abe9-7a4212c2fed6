package com.sohuott.tv.vod.view;

import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

/**
 * Created by feng<PERSON><PERSON> on 17-7-4.
 */

public class SearchInputRecyclerView extends RecyclerView {

    private View lastFocusedView;

    public SearchInputRecyclerView(Context context) {
        super(context);
        init(context);
    }

    public SearchInputRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public SearchInputRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

    private void init(Context context) {
    }

    public boolean onPressRightKeyRequestFocus() {
        if(getChildCount() == 0) {
            return false;
        }
        if(lastFocusedView != null) {
            return lastFocusedView.requestFocus();
        }
        View view = findFirstFocusableView(this);
        if(view != null) {
            return view.requestFocus();
        }
        return false;
    }

    private View findFirstFocusableView(View view) {
        if(view != null && view.hasFocusable()) {
            if(view.isFocusable()) {
                return view;
            }
            if(view instanceof ViewGroup) {
                for(int i = 0; i < ((ViewGroup) view).getChildCount(); i++) {
                    View result = findFirstFocusableView(((ViewGroup) view).getChildAt(i));
                    if(result != null) {
                        return result;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void setAdapter(Adapter adapter) {
        super.setAdapter(adapter);
        lastFocusedView = null;
    }

    public void setLastFocusedView(View lastFocusedView) {
        this.lastFocusedView = lastFocusedView;
    }

    public View getLastFocusedView() {
        return lastFocusedView;
    }

}
