package com.sohuott.tv.vod.view;

import android.animation.AnimatorSet;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.utils.FocusUtil;

public class FocusBorderView extends View {
    public static final int FOCUS_TYPE_NORMAL = 0;
    public static final int FOCUS_TYPE_CORNER = 1;
    public static final int FOCUS_TYPE_ROUND = 2;

    private static final String TAG = "FocusView";

    private View mFocusView;
    private AnimatorSet mAnimatorSet = new AnimatorSet();
    //    private View mUnFocusView;
    private int[] mFocusLocation = new int[2];
    private int[] mLocation = new int[2];
    private Drawable mDrawable;
    private float mScaleUp = 1f;

    private int mLeftPixel;
    private int mTopPixel;

    private int mRoundImgPadding;
    private int mViewType = FOCUS_TYPE_NORMAL;
    private Paint mPaint;
    private float mCornerSize;
    private int mMarginWidth = 0;

    public FocusBorderView(Context context) {
        super(context);
        init();
    }

    public FocusBorderView(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.FocusBorderView);
        mViewType = typedArray.getInteger(R.styleable.FocusBorderView_viewType, FOCUS_TYPE_NORMAL);
        init();
    }

    void init() {
        updateFocusView();
//        mLeftPixel = (int) getResources().getDimension(R.dimen.x2);
//        mTopPixel = (int) getResources().getDimension(R.dimen.y2);

        //长虹628 焦点偏小问题
        String deviceId = Build.ID == null ? "" : Build.ID;
        String model = Build.MODEL == null ? "" : Build.MODEL;

        if (model.contains("ChangHong Android TV") && deviceId.contains("KOT49H")) {
            mLeftPixel = -13;
            mTopPixel = -13;
        } else {
            mLeftPixel = 0;
            mTopPixel = 0;
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        draw(canvas, mFocusView, mScaleUp);
    }

    public void draw(Canvas canvas, View view, float scale) {
        if (view != null) {
            if (view.getId() == R.id.episode_tv || view.getId() == R.id.episode_item) {
                return;
            }
            LibDeprecatedLogger.d(view.toString());
            canvas.save();
            if (null == mLocation) {
                mLocation = new int[2];
            }
            if (null == mFocusLocation) {
                mFocusLocation = new int[2];
            }
            getLocationInWindow(mLocation);
            view.getLocationInWindow(mFocusLocation);

//			int width = (int)(view.getWidth() * scale);
//			int height = (int)(view.getHeight() * scale);
            int width = view.getWidth();
            int height = view.getHeight();

//			int left = (int)(mFocusLocation[0]-mLocation[0]-width*(scale-1)/2);
//			int top = (int)(mFocusLocation[1]-mLocation[1]-height*(scale-1)/2);
            int left = mFocusLocation[0] - mLocation[0];
            int top = mFocusLocation[1] - mLocation[1];
            canvas.translate(left, top);
                canvas.scale(scale, scale);

            switch (mViewType) {
                case FOCUS_TYPE_NORMAL:
                    Rect padding = new Rect();
                    mDrawable.getPadding(padding);
                    mDrawable.setBounds(-padding.left + mLeftPixel + mRoundImgPadding, -padding.top + mTopPixel + mRoundImgPadding,
                            width + padding.right - mLeftPixel - mRoundImgPadding,
                            height + padding.bottom - mTopPixel - mRoundImgPadding);
                    mDrawable.draw(canvas);
                    break;
                case FOCUS_TYPE_CORNER:
                case FOCUS_TYPE_ROUND:
                    RectF bound = new RectF(mLeftPixel + mRoundImgPadding-mMarginWidth, mTopPixel + mRoundImgPadding-mMarginWidth,
                            width - mLeftPixel - mRoundImgPadding+mMarginWidth, height - mTopPixel - mRoundImgPadding+mMarginWidth);
                    canvas.drawRoundRect(bound, mViewType == FOCUS_TYPE_CORNER ? mCornerSize : height / 2, mViewType == FOCUS_TYPE_CORNER ? mCornerSize : height / 2, mPaint);
                    break;
            }
            canvas.save();
            canvas.restore();
        } else if (mDrawable != null) {
            mDrawable.setBounds(0, 0, 0, 0);
            mDrawable.draw(canvas);
            canvas.save();
            canvas.restore();
        }
    }

    /***
     * 默认设置焦点图，直接根据mIsRoundCorner参数控制焦点显示区域
     * 非圆角：不需要对焦点进行特殊处理，直接显示在view显示区域
     * 圆角：焦点显示在view显示区域减去默认padding的区域
     * @param view
     */
    public void setFocusView(View view) {
        mMarginWidth = 0;
        mFocusView = view;
        mCornerSize = getResources().getDimensionPixelSize(R.dimen.child_round_img_radius);
        mRoundImgPadding = mViewType == FOCUS_TYPE_CORNER ?
                getResources().getDimensionPixelSize(R.dimen.child_round_img_padding) : 0;
        if (mAnimatorSet != null && mAnimatorSet.isRunning()) {
            mAnimatorSet.end();
        }
        mAnimatorSet = new AnimatorSet();
        invalidate();
    }


    /***
     * 默认设置焦点图
     * @param view
     * @param excludePadding false:不需要对焦点进行特殊处理，直接显示在view显示区域
     *                       true:焦点显示在view显示区域减去默认padding的区域
     */
    public void setFocusView(View view, boolean excludePadding) {
        setFocusView(view, excludePadding, 0, mViewType);
    }

    /***
     * 设置焦点图
     * @param view
     * @param excludePadding false:不需要对焦点进行特殊处理，直接显示在view显示区域
     *                       true:焦点显示在view显示区域减去默认padding的区域
     * @param cornerType 焦点状态
     */
    public void setFocusView(View view, boolean excludePadding, int cornerType) {
        setFocusView(view, excludePadding, 0, cornerType);
    }

    /***
     * 设置焦点图
     * @param view
     * @param excludePadding false:不需要对焦点进行特殊处理，直接显示在view显示区域
     *                       true:焦点显示在view显示区域减去默认padding的区域
     * @param cornerType 焦点状态
     */
    public void setFocusView(View view, boolean excludePadding, int marginId, int cornerType) {
        mViewType = cornerType;
        mFocusView = view;
        mRoundImgPadding = excludePadding ? getResources().getDimensionPixelSize(R.dimen.child_round_img_padding) : 0;
        mMarginWidth = marginId == 0 ? 0 : getResources().getDimensionPixelSize(marginId);
        mCornerSize = getResources().getDimensionPixelSize(R.dimen.child_round_img_radius);
        if (mAnimatorSet != null && mAnimatorSet.isRunning()) {
            mAnimatorSet.end();
        }
        mAnimatorSet = new AnimatorSet();
        invalidate();
    }


    /***
     *
     * @param view
     * @param focusType
     */
    public void setFocusViewWithTypeChange(View view, int focusType) {
        mFocusView = view;
        mViewType = focusType;
        updateFocusView();

        if (mAnimatorSet != null && mAnimatorSet.isRunning()) {
            mAnimatorSet.end();
        }
        mAnimatorSet = new AnimatorSet();
        invalidate();
    }

    public void setUnFocusView(View view) {
        if (mFocusView == view) {
            mFocusView = null;
            if (mAnimatorSet != null && mAnimatorSet.isRunning()) {
                mAnimatorSet.end();
            }
            mAnimatorSet = new AnimatorSet();
            invalidate();
        }
    }

    public void clearFocus() {
        if (mFocusView != null) {
            if (mAnimatorSet != null && mAnimatorSet.isRunning()) {
                mAnimatorSet.end();
            }
            FocusUtil.setUnFocusAnimator(mFocusView);
        }
        mFocusView = null;
        invalidate();
    }

    public void setScaleUp(float scale) {
        mScaleUp = scale;
        invalidate();
    }

    public void setDrawable(int id) {
        mDrawable = getResources().getDrawable(id);
    }

    public View getFocusView() {
        return mFocusView;
    }

    public AnimatorSet getAnimatorSet() {
        return mAnimatorSet;
    }

    public void setRoundCorner(boolean roundCorner) {
        mViewType = roundCorner ? FOCUS_TYPE_CORNER : FOCUS_TYPE_NORMAL;
        updateFocusView();
    }

    private void updateFocusView() {
        if (mViewType == FOCUS_TYPE_NORMAL) {
            mDrawable = getResources().getDrawable(R.drawable.ic_focus);
        } else {
            mDrawable = null;
            mPaint = new Paint();
            mPaint.setStyle(Paint.Style.STROKE);
            mPaint.setColor(getResources().getColor(R.color.child_focus_border));
            mPaint.setStrokeWidth(getResources().getDimensionPixelSize(R.dimen.child_focus_border_stroke_width));
            mPaint.setAntiAlias(true);
            mCornerSize = getResources().getDimensionPixelSize(R.dimen.child_round_img_radius);
        }
    }


    /**
     * 以下目前针对少儿详情页
     * @param view
     */
    public void setFocusViewWithMargin(View view){
        setFocusViewWithMargin(view,R.dimen.x6,R.dimen.child_round_img_radius);
    }
    public void setFocusViewWithMargin(View view,int marginId){
        setFocusViewWithMargin(view,marginId,R.dimen.child_round_img_radius);
    }
    public void setFocusViewWithMargin(View view,int marginId,int cornerId){
        mFocusView = view;
        mRoundImgPadding = 0;
        mMarginWidth = getResources().getDimensionPixelSize(marginId);
        mCornerSize = getResources().getDimensionPixelSize(cornerId);
        if (mAnimatorSet != null && mAnimatorSet.isRunning()) {
            mAnimatorSet.end();
        }
        mAnimatorSet = new AnimatorSet();
        invalidate();
    }

    //不设margin，只设置corner大小 by zxf
    public void setFocusViewWithCustomCorner(View view,int cornerId){
        mFocusView = view;
        mRoundImgPadding = 0;
        mMarginWidth = 0;
        mCornerSize = getResources().getDimensionPixelSize(cornerId);
        if (mAnimatorSet != null && mAnimatorSet.isRunning()) {
            mAnimatorSet.end();
        }
        mAnimatorSet = new AnimatorSet();
        invalidate();
    }
}
