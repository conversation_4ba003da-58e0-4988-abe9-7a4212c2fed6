package com.sohuott.tv.vod.presenter;

import android.content.Context;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.BookedRecordResult;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.view.ComingSoonAdapterView;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by wenjingbian on 2017/7/4.
 */

public class ComingSoonAdapterPresenterImpl {

    private Context mContext;

    private ComingSoonAdapterView mView;
    private LoginUserInformationHelper mHelper;

    public ComingSoonAdapterPresenterImpl(Context context) {
        this.mContext = context;
        mHelper = LoginUserInformationHelper.getHelper(context);
    }

    public void setView(ComingSoonAdapterView view) {
        this.mView = view;
    }

    public void addNewBookedRecord(final int aid) {
        NetworkApi.addNewBookedVideo(mHelper.getLoginPassport(), String.valueOf(aid), mHelper.getLoginToken(), new Observer<BookedRecordResult>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(BookedRecordResult value) {
                LibDeprecatedLogger.d("addNewBookedRecord(): onNext().");
                if (value != null && value.getData() != null && value.getData().getOperResult() != null
                        && value.getData().getOperResult().size() > 0) {
                    BookedRecordResult.DataBean.OperResultBean result = value.getData().getOperResult().get(0);
                    if(result != null && result.isResult()){
                        mView.onAddBookedRecordSuccess(aid);
                    }else{
                        mView.onAddBookRecordError(aid);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("addNewBookedRecord(): onError()--" + e.getMessage());
                mView.onAddBookRecordError(aid);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("addNewBookedRecord(): onComplete().");
            }
        });
    }

    public void cancelBookedRecord(final int aid) {
        NetworkApi.cancelBookedVideoById(mHelper.getLoginPassport(), String.valueOf(aid), mHelper.getLoginToken(),new Observer<BookedRecordResult>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(BookedRecordResult value) {
                LibDeprecatedLogger.d("cancelBookedVideoById(): onNext().");
                if (value != null && value.getData() != null && value.getData().getOperResult() != null
                        && value.getData().getOperResult().size() > 0) {
                    BookedRecordResult.DataBean.OperResultBean result = value.getData().getOperResult().get(0);
                    if(result != null && result.isResult()){
                        mView.onCancelBookedRecordSuccess(aid);
                    }else{
                        mView.onCancelBookedRecordError(aid);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("cancelBookedRecord(): onError()--" + e.getMessage());
                mView.onCancelBookedRecordError(aid);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("cancelBookedRecord(): onComplete().");
            }
        });
    }
}
