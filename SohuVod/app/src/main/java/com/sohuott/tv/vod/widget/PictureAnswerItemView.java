package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohu.lib_utils.Md5Utils;

import java.io.File;

/**
 * Created by yizhang210244 on 2018/4/28.
 */

public class PictureAnswerItemView extends RelativeLayout{
    private GlideImageView mGlideImageView;
    private TextView mAnswerNumberTextView;
    private ImageView mCoverImage;

    public PictureAnswerItemView(Context context) {
        super(context);
        init(context);
    }

    public PictureAnswerItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public PictureAnswerItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context){
        LayoutInflater.from(context).inflate(R.layout.view_picture_answer_item, this, true);
        setFocusable(true);
        setBackgroundResource(R.drawable.answer_pic_btn_selector);
        mGlideImageView = (GlideImageView) findViewById(R.id.picture_view);
        mAnswerNumberTextView = (TextView) findViewById(R.id.answer_number);
        mCoverImage = (ImageView) findViewById(R.id.cover_image);
        mAnswerNumberTextView.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if(keyCode == KeyEvent.KEYCODE_DPAD_LEFT ||
                        keyCode == KeyEvent.KEYCODE_DPAD_RIGHT ||
                        keyCode == KeyEvent.KEYCODE_DPAD_UP ||
                        keyCode == KeyEvent.KEYCODE_DPAD_DOWN){
                    return true;
                }
                return false;
            }
        });
    }

    /**
     *
     * @param imageUrl 图片答案
     * @param number 回答此选项的人数
     * @param isQuestion 是是问题还是答案 ture 问题，false 答案
     * @param type  只有是答案的时候type有用，0 normal ,1答对 2答错。
     */
    public void setData(String imageUrl,String number, boolean isQuestion,int type){
        if(!isQuestion){
            setFocusable(false);
            setBackgroundResource(R.color.transparent);
            mCoverImage.setVisibility(VISIBLE);
            if(type == 1){
                mCoverImage.setBackgroundResource(R.drawable.answer_pic_right);
            }else if(type == 2){
                mCoverImage.setBackgroundResource(R.drawable.answer_pic_wrong);
            }
            mAnswerNumberTextView.setText(number);
        }
        if(!TextUtils.isEmpty(imageUrl)){
//            mGlideImageView.setImageRes(imageUrl);
            int radius = getContext().getResources().getDimensionPixelSize(R.dimen.x20);
            String titleMd5 = Md5Utils.getMD5Str(imageUrl);
            String localPic = Util.getLivePlayExamDir(getContext()) + File.separator + titleMd5 +".jpg";
            File file = new File(localPic);
            if(file.exists()){
                LibDeprecatedLogger.e("set local pic title");
                mGlideImageView.setCornerImageRes(localPic,radius,
                        R.drawable.bg_upgrade,
                        R.drawable.bg_upgrade
                );
            }else {
                LibDeprecatedLogger.e("set net pic title");
                mGlideImageView.setCornerImageRes(imageUrl,radius,
                        R.drawable.bg_upgrade,
                        R.drawable.bg_upgrade
                );
            }

//            mGlideImageView.setImageRes(imageUrl);
        }
    }

    public void setAnswerSelected(){
        mAnswerNumberTextView.setFocusable(true);
        mAnswerNumberTextView.requestFocus();
        mCoverImage.setVisibility(VISIBLE);
        mCoverImage.setBackgroundResource(R.drawable.answer_pic_right);
    }

}
