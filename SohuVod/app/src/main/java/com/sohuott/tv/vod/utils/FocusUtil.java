package com.sohuott.tv.vod.utils;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;


import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.view.FocusBorderView;

/**
 * Created by f<PERSON><PERSON><PERSON> on 16-3-25.
 */
public class FocusUtil {

    public static final int FOCUS_ANIM_TIME = 300;
    public static final float HOME_SCALE = 1.07f;

    private static AnimatorSet mAnimationSet;

    public static void setFocusAnimator(View focusView, FocusBorderView focusBorderView) {
        setFocusAnimator(focusView, focusBorderView, HOME_SCALE);
    }

    public static void setFocusAnimator(View focusView, FocusBorderView focusBorderView, float scale) {
        LibDeprecatedLogger.d("FocusUtil setFocusAnimator");
        setFocusAnimator(focusView, focusBorderView, scale, FOCUS_ANIM_TIME);
    }

    public static void clearAnimation() {
        LibDeprecatedLogger.d("clearAnimation==");
        if (mAnimationSet != null) {
            mAnimationSet.cancel();
            mAnimationSet.setTarget(null);
        }
    }

    public static void setFocusAnimator(View focusView, float scale, int animTime) {
        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(focusView, "scaleX", 1f, scale)
                .setDuration(animTime);
        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(focusView, "scaleY", 1f, scale)
                .setDuration(animTime);
        if(mAnimationSet != null && mAnimationSet.isRunning()){
            mAnimationSet.end();
        }
        mAnimationSet = new AnimatorSet() ;
        mAnimationSet.playTogether(scalexAnimator, scaleyAnimator);
        mAnimationSet.start();
    }

    public static void setFocusAnimator(View focusView, FocusBorderView focusBorderView,
                                        float scale, int animTime) {
        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(focusView, "scaleX", 1f, scale)
                .setDuration(animTime);
        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(focusView, "scaleY", 1f, scale)
                .setDuration(animTime);
        ObjectAnimator focusAnimator = null;
        if(focusBorderView != null) {
            focusAnimator = ObjectAnimator.ofFloat(focusBorderView, "ScaleUp",
                    new float[] { 1f, scale }).setDuration(animTime);
        }

        AnimatorSet set = focusBorderView != null ? focusBorderView.getAnimatorSet():new AnimatorSet() ;
        if(focusAnimator != null) {
            set.playTogether(scalexAnimator, scaleyAnimator, focusAnimator);
        }else {
            set.playTogether(scalexAnimator, scaleyAnimator);
        }
        set.start();
    }

    public static void setUnFocusAnimator(View focusView) {
        LibDeprecatedLogger.d("FocusUtil setUnFocusAnimator");
        setUnFocusAnimator(focusView, FOCUS_ANIM_TIME);
    }

    public static void setUnFocusAnimator(View focusView, int animTime) {
        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(focusView, "scaleX", focusView.getScaleX(), 1f)
                .setDuration(animTime);
        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(focusView, "scaleY", focusView.getScaleY(), 1f)
                .setDuration(animTime);
        AnimatorSet set = new AnimatorSet();
        set.playTogether(scalexAnimator, scaleyAnimator);
        set.start();
    }

    public static class FocusChangeNoAnimListener implements View.OnFocusChangeListener {

        private FocusBorderView mFocusBorderView;

        public FocusChangeNoAnimListener(FocusBorderView focusBorderView) {
            mFocusBorderView = focusBorderView;
        }

        public FocusChangeNoAnimListener(FocusBorderView focusBorderView, float scale) {
            mFocusBorderView = focusBorderView;
            if(mFocusBorderView != null) {
                mFocusBorderView.setScaleUp(scale);
            }
        }

        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus) {
                if (mFocusBorderView != null) {
                    mFocusBorderView.setFocusView(v);
                    FocusUtil.setFocusAnimator(v, mFocusBorderView, 1f);
                }
            } else {
                if (mFocusBorderView != null) {
                    mFocusBorderView.setUnFocusView(v);
                }
            }
        }
    }

}
