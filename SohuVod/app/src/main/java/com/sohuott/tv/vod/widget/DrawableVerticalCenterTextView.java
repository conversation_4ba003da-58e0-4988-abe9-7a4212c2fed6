package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

/**
 * Created by hpb on 2017/7/19.
 * keep left/right drawable on center
 */
public class DrawableVerticalCenterTextView extends TextView {

    public DrawableVerticalCenterTextView(Context context) {
        this(context, null);
    }

    public DrawableVerticalCenterTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    private int dWidth, dHeight;

    public DrawableVerticalCenterTextView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.DvcTextView);
        dWidth = typedArray.getDimensionPixelOffset(R.styleable.DvcTextView_drawableWidth, 0);
        dHeight = typedArray.getDimensionPixelOffset(R.styleable.DvcTextView_drawableHeight, 0);
        typedArray.recycle();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        Drawable[] mDrawables = getCompoundDrawables();
        if (mDrawables != null) {
            Drawable drawableLeft = mDrawables[0];
            if (drawableLeft != null) {
                setBounds(drawableLeft);
            }
            Drawable drawableRight = mDrawables[2];
            if (drawableRight != null) {
                setBounds(drawableRight);
            }
        }
        super.onDraw(canvas);
    }

    private void setBounds(Drawable drawable) {
        Paint.FontMetricsInt fm = getPaint().getFontMetricsInt();
        int textHeight = fm.bottom - fm.top;
        int drawableHeight = dHeight > 0 ? dHeight : drawable.getIntrinsicHeight();
        int drawableTop = (textHeight - drawableHeight) >> 2;
        int drawableWidth = dWidth > 0 ? dWidth : drawable.getIntrinsicWidth();

        int left = 0;
        if (getGravity() == Gravity.CENTER ||
                getGravity() == Gravity.CENTER_HORIZONTAL) {
            int padding = getCompoundDrawablePadding();
            left = getMeasuredWidth() / 2 - padding - drawableWidth;
            if (left < 0) {
                left = 0;
            }
        }
        LibDeprecatedLogger.d("drawableLeft:" + left + "," + drawableTop + "," + (left + drawableWidth) + "," + (drawableHeight + drawableTop));
        drawable.setBounds(left, drawableTop,
                left + drawableWidth, drawableHeight + drawableTop);
    }
}
