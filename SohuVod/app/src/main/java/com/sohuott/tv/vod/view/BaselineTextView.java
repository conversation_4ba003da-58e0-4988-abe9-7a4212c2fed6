package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.util.Log;
import android.widget.TextView;

import com.sohuott.tv.vod.R;

/**
 * Created by yizhang210244 on 2017/6/6.
 */

public class BaselineTextView extends TextView {

    public BaselineTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onDraw(Canvas canvas) {

        Log.e("BaselineTextView","font bottom:" + getPaint().getFontMetricsInt().bottom +
                "  \ndescent:" + getPaint().getFontMetricsInt().descent +
                " \nascent:" + getPaint().getFontMetricsInt().ascent +
                " \ntop:" + getPaint().getFontMetricsInt().top +
                " \nbaseline:" + getBaseline() + ",leading" + getPaint().getFontMetricsInt().leading);


        Log.e("BaselineTextView","textview bottom:" + getBottom() +
                " \ntop:" + getTop() +
                " \nbaseline:" + getBaseline());


        int yOffset = getBaseline() - getHeight() + getPaint().getFontMetricsInt().descent/2 ;

//        int yOffset = getBaseline() - getPaint().getFontMetricsInt().ascent;
        canvas.translate(0, yOffset);
        super.onDraw(canvas);
    }

}
