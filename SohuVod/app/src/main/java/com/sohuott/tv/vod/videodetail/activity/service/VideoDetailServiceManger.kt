package com.sohuott.tv.vod.videodetail.activity.service

class VideoDetailServiceManger : VideoDetailService {

    private val mServices = mutableListOf<VideoDetailService>()

    fun addService(service: VideoDetailService) {
        mServices.add(service)
    }

    fun removeService(service: VideoDetailService) {
        mServices.remove(service)
    }

    fun getServices(): MutableList<VideoDetailService> {
        return mServices
    }

    companion object {
        private var mManger: VideoDetailServiceManger? = null

        @JvmStatic
        fun getInstants(): VideoDetailServiceManger {
            mManger ?: synchronized(this) {
                mManger ?: also {
                    mManger = VideoDetailServiceManger()
                }
            }
            return mManger!!
        }
    }

    override fun onStartActivityPlay(isRePlay: Boolean) {
        mServices.forEach {
            it.onStartActivityPlay(isRePlay)
        }
    }
}