package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.HotSearchNew;
import com.sohuott.tv.vod.lib.model.SearchSuggest;
import java.util.List;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by f<PERSON><PERSON><PERSON> on 17-6-16.
 */

public class HotSearchPresenter implements HotSearchContract.Presenter {

    private static final int MAX_HOT_SEARCH_COUNT = 10;

    private HotSearchContract.HotSearchView hotSearchView;

    public HotSearchPresenter(HotSearchContract.HotSearchView hotSearchView) {
        this.hotSearchView = hotSearchView;
    }

    @Override
    public void getHotSearch() {
        NetworkApi.getHotSearchNew(new DisposableObserver<HotSearchNew>() {
            @Override
            public void onNext(HotSearchNew response) {
                if (null != response) {
                    List<HotSearchNew.DataBean> data = response.getData();
                    if(hotSearchView != null) {
                        if (data != null && data.size() > MAX_HOT_SEARCH_COUNT) {
                            hotSearchView.showHotSearch(data.subList(0, MAX_HOT_SEARCH_COUNT));
                        } else {
                            hotSearchView.showHotSearch(data);
                        }
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("getHotSearch() Error: " + e.getMessage(), e);
                if(hotSearchView != null) {
                    hotSearchView.showHotSearch(null);
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("getHotSearch(): onComplete()");
            }
        });
    }


    @Override
    public void getSuggestSearch(final String keyword) {
        NetworkApi.getSearchSuggest(keyword,
                new DisposableObserver<SearchSuggest>() {
                    @Override
                    public void onNext(SearchSuggest response) {
                        if(hotSearchView != null) {
                            if (null != response && response.getData() != null) {
                                List<HotSearchNew.DataBean> data = response.getData().getR();
                                for (int i = 0; i < data.size(); i++) {
                                    if (data.get(i).getIsAudit() == 0) {
                                        data.remove(i);
                                    }
                                }
                                hotSearchView.showSearchSuggest(data, keyword);
                            } else {
                                hotSearchView.showSearchSuggest(null, keyword);
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.e("getSuggestSearch()Error(): " + e.getMessage(), e);
                        if(hotSearchView != null) {
                            hotSearchView.showSearchSuggest(null, keyword);
                        }
                    }

                    @Override
                    public void onComplete() {
                        LibDeprecatedLogger.d("getSuggestSearch(): onComplete()");
                    }
                });
    }


}
