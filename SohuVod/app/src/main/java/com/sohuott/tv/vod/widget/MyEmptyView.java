package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.graphics.Rect;

import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.HistoryFavorCollectionRecordAdapter;
import com.sohuott.tv.vod.adapter.MyEmptyViewAdapter;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.util.List;

import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_BOOKED;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_COLLECTION;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_CONSUME_RECORD;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_FAVOR;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_HISTORY;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Empty view used on various pages, including History, Favor, Collection and Consume Record.
 * <p>
 * Created by wenjingbian on 2017/5/18.
 */

public class MyEmptyView extends RelativeLayout implements MyEmptyViewAdapter.IEmptyViewFocus {

    public static final int TAG_LOGIN = 1;
    public static final int TAG_COMING_SOON = 2;

    private Context mContext;

    //Message text
    private TextView tv_my_empty_view;
    //Login button
    private Button btn_my_empty_view;
    //Promote video list
    private CustomLinearRecyclerView clrv_my_empty_view;

    private MyEmptyViewAdapter mAdapter;
    private HistoryFavorCollectionRecordAdapter.FocusController mFocusController;

    //Selected item id on the left list
    private int mParentTag;
    private int mSubjectId;

    public MyEmptyView(Context context) {
        super(context);
        this.mContext = context;
        initView();
        initListeners();
    }

    public MyEmptyView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        initView();
        initListeners();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mFocusController = null;
        mContext = null;
        if (mAdapter != null) {
            mAdapter.releaseAll();
            mAdapter = null;
        }
        FocusUtil.clearAnimation();
    }

    @Override
    public void onEmptyViewFocus() {
        if (btn_my_empty_view != null && btn_my_empty_view.getVisibility() == VISIBLE) {
            btn_my_empty_view.requestFocus();
        }
    }

    /**
     * Set selected item id on the left list
     *
     * @param parentTag tag value of the selected item
     */
    public void setParentTag(int parentTag) {
        LoginUserInformationHelper helper = LoginUserInformationHelper.getHelper(mContext);
        boolean isLogin = helper.getIsLogin();
        this.mParentTag = parentTag;

        if (mAdapter != null) {
            mAdapter.setLeftTag(parentTag);
        }

        //Set message text according to the selected item id
        switch (parentTag) {
            case LIST_INDEX_HISTORY:
                if (isLogin) {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_history_empty_msg));
                } else {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_history_login_msg));
                }
                break;
            case LIST_INDEX_COLLECTION:
                if (isLogin) {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_collection_empty_msg));
                } else {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_collection_login_msg));
                }
                break;
            case LIST_INDEX_BOOKED:
                if (isLogin) {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_book_empty_msg));
                } else {
                    setMsgTxt(getResources().getString(R.string.txt_fragment_book_login_msg));
                }
                break;
            case LIST_INDEX_FAVOR:
                setMsgTxt(getResources().getString(R.string.txt_fragment_favor_empty_msg));
                break;
            case LIST_INDEX_CONSUME_RECORD:
                setMsgTxt(getResources().getString(R.string.txt_fragment_consume_record_empty_msg));
                break;
            default:
                break;
        }
    }

    /**
     * Set focus for MyEmptyView
     * <p>
     * If button is visible, button will request focus
     * Else the first item of promote list will request focus
     */
    public void focusAtPos() {
        if (btn_my_empty_view != null && btn_my_empty_view.getVisibility() == VISIBLE) {
            btn_my_empty_view.requestFocus();
        } else if (clrv_my_empty_view != null && clrv_my_empty_view.getChildAt(0) != null) {
            clrv_my_empty_view.getChildAt(0).requestFocus();
        }
    }

    /**
     * Set data source of promote list
     *
     * @param dataSource list value
     */
    public void setListView(List<?> dataSource) {
        if (clrv_my_empty_view == null || dataSource == null || dataSource.size() <= 0 || mAdapter == null) {
            return;
        }

        //If the child item of promote list not get focus, will notify data changed directly.
        if (clrv_my_empty_view.getFocusedChild() == null) {
            //if list's size less than 5,  will add new instances to make it standard.
            if (dataSource.size() != 5) {
                for (int i = 0; i < 5 - dataSource.size(); i++) {
                    if (dataSource.get(0) instanceof ListAlbumModel) {
                        ((List<ListAlbumModel>) dataSource).add(new ListAlbumModel());
                    } else if (dataSource.get(0) instanceof VideoDetailRecommend.DataEntity) {
                        ((List<VideoDetailRecommend.DataEntity>) dataSource).add(new VideoDetailRecommend.DataEntity());
                    }
                }
            }
            mAdapter.setDataSource(dataSource);
            mAdapter.notifyDataSetChanged();
        } else { //If the child item promote list get focus, will update item one by one
            if (dataSource.get(0) instanceof ListAlbumModel) {
                updateVipRecommendList((List<ListAlbumModel>) dataSource);
            } else if (dataSource.get(0) instanceof VideoDetailRecommend.DataEntity) {
                updatePersonalRecommendList((List<VideoDetailRecommend.DataEntity>) dataSource);
            }
        }
    }

    public void setSubjectId(int subjectId) {
        this.mSubjectId = subjectId;
    }

    /**
     * Set button's visibility
     *
     * @param isVisible true means VISIBLE, false means GONE or INVISIBLE
     */
    public void setBtnVisibility(boolean isVisible) {
        if (btn_my_empty_view == null) {
            return;
        }

        RelativeLayout.LayoutParams layoutParams = (LayoutParams) tv_my_empty_view.getLayoutParams();
        if (isVisible) {
            btn_my_empty_view.setVisibility(VISIBLE);
            layoutParams.setMargins(0, 0, 0, getContext().getResources().getDimensionPixelOffset(R.dimen.y297));
        } else {
            btn_my_empty_view.setVisibility(GONE);
            layoutParams.setMargins(0, 0, 0, getContext().getResources().getDimensionPixelOffset(R.dimen.y204));
        }
        tv_my_empty_view.setLayoutParams(layoutParams);
    }

    public void setBtnText(String text) {
        if (!TextUtils.isEmpty(text)) {
            btn_my_empty_view.setText(text);
        }
    }

    public void setBtnListener(final int tag) {
        btn_my_empty_view.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (tag == TAG_LOGIN) {
                    ActivityLauncher.startLoginActivity(mContext);
                    //Action event
                    RequestManager.getInstance().onUserRelatedLoginBtnClickEvent(mParentTag);
                } else if (tag == TAG_COMING_SOON) {
                    ActivityLauncher.startCommingSoonActivity(mContext, mSubjectId);
                    RequestManager.getInstance().onBookedGoToComingSoonClickEvent();
                }
            }
        });
        btn_my_empty_view.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    FocusUtil.setFocusAnimator(v, 1.07f, 300);
                } else {
                    FocusUtil.setUnFocusAnimator(v);
                }
            }
        });
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        if (mAdapter != null) {
            mAdapter.setFocusBorderView(focusBorderView);
        }
    }

    public void setFocusController(HistoryFavorCollectionRecordAdapter.FocusController focusController) {
        this.mFocusController = focusController;
        if (mAdapter != null) {
            mAdapter.setFocusController(focusController);
        }
    }

    /**
     * Initialize view
     */
    private void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.layout_my_empty_view, this, true);
        //init views
        tv_my_empty_view = (TextView) findViewById(R.id.tv_my_empty_view);
        btn_my_empty_view = (Button) findViewById(R.id.btn_my_empty_view);
        clrv_my_empty_view = (CustomLinearRecyclerView) findViewById(R.id.clrv_my_empty_view);
        clrv_my_empty_view.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.right = getResources().getDimensionPixelSize(R.dimen.x8);
            }
        });

        clrv_my_empty_view.setChildDrawingOrderCallback(new RecyclerView.ChildDrawingOrderCallback() {
            @Override
            public int onGetChildDrawingOrder(int childCount, int i) {
                int pos = clrv_my_empty_view.indexOfChild(clrv_my_empty_view.getFocusedChild());
                if (pos < 0 || pos >= childCount - 1) {
                    return i;
                }

                if (pos == i) {
                    return i + 1;
                } else if (i == pos + 1) {
                    return i - 1;
                } else {
                    return i;
                }
            }
        });

        if (mAdapter == null) {
            CustomLinearLayoutManager layoutManager = new CustomLinearLayoutManager(mContext);
            layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
            clrv_my_empty_view.setLayoutManager(layoutManager);
            mAdapter = new MyEmptyViewAdapter(mContext, clrv_my_empty_view);
            mAdapter.setIEmptyViewFocus(this);
            clrv_my_empty_view.setAdapter(mAdapter);
        }
        mAdapter.setDataSource(null);
        mAdapter.notifyDataSetChanged();
    }

    /**
     * Initialize listeners on the child views
     */
    private void initListeners() {
        btn_my_empty_view.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT || keyCode == KeyEvent.KEYCODE_BACK) {
                        if (mFocusController != null) {
                            mFocusController.onFocusSelected(mParentTag);
                            return true;
                        }
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                        return true;
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                        if (clrv_my_empty_view != null && clrv_my_empty_view.getChildAt(0) != null) {
                            clrv_my_empty_view.getChildAt(0).requestFocus();
                            return true;
                        }
                    }
                }
                return false;
            }
        });
    }

    /**
     * Set message text
     *
     * @param msgStr message text you want to set
     */
    private void setMsgTxt(String msgStr) {
        if (tv_my_empty_view == null || TextUtils.isEmpty(msgStr)) {
            return;
        }

        tv_my_empty_view.setText(msgStr);
    }

    /**
     * Update vip recommend list one by one
     *
     * @param list list value you want to update
     */
    private void updateVipRecommendList(List<ListAlbumModel> list) {
        if (list == null || list.size() <= 0) {
            return;
        }

        CornerTagImageView cornerTagImageView;
        TextView tvTitle;
        for (int i = 0; i < list.size(); i++) {
            final ListAlbumModel listAlbumModel = list.get(i);
            cornerTagImageView = (CornerTagImageView) clrv_my_empty_view.getChildAt(i).findViewById(R.id.ctiv_hfc_video_poster);
            cornerTagImageView.setImageRes(listAlbumModel.tvVerPic);
            cornerTagImageView.setCornerTypeWithType(listAlbumModel.tvIsFee, listAlbumModel.ottFee, listAlbumModel.useTicket, listAlbumModel.paySeparate, listAlbumModel.cornerType);
            tvTitle = (TextView) clrv_my_empty_view.getChildAt(i).findViewById(R.id.tv_item_hfc_title);
            tvTitle.setText(listAlbumModel.tvName);
            clrv_my_empty_view.getChildAt(i).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ActivityLauncher.startVideoDetailActivity(getContext(), listAlbumModel.id, 0, Constant.PAGE_MY_VIP_RECOMMEND);
                }
            });
        }
    }

    /**
     * Update personal recommend list one by one
     *
     * @param list list value you want to update
     */
    private void updatePersonalRecommendList(List<VideoDetailRecommend.DataEntity> list) {
        if (list == null || list.size() <= 0) {
            return;
        }

        CornerTagImageView cornerTagImageView;
        TextView tvSubTitle;
        TextView tvTitle;
        String latestVideoCount;
        int tvSets;
        for (int i = 0; i < list.size(); i++) {
            final VideoDetailRecommend.DataEntity dataEntity = list.get(i);
            cornerTagImageView = (CornerTagImageView) clrv_my_empty_view.getChildAt(i).findViewById(R.id.ctiv_hfc_video_poster);
            cornerTagImageView.setImageRes(dataEntity.getTvVerPic());
            tvTitle = (TextView) clrv_my_empty_view.getChildAt(i).findViewById(R.id.tv_item_hfc_title);
            tvTitle.setText(dataEntity.getTvName());
            tvSubTitle = (TextView) clrv_my_empty_view.getChildAt(i).findViewById(R.id.tv_item_hfc_sub_title);
            latestVideoCount = dataEntity.getLatestVideoCount();
            tvSets = dataEntity.getTvSets();
            if (latestVideoCount.equals(String.valueOf(tvSets))) {
                tvSubTitle.setText(getResources().getString(R.string.txt_activity_user_related_set_pre_sum)
                        + tvSets + getResources().getString(R.string.txt_activity_user_related_set_suf));
            } else {
                tvSubTitle.setText(getResources().getString(R.string.txt_activity_user_related_set_pre_update)
                        + latestVideoCount + getResources().getString(R.string.txt_activity_user_related_set_suf));
            }
            clrv_my_empty_view.getChildAt(i).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ActivityLauncher.startVideoDetailActivity(getContext(), dataEntity.getId(), 0, Constant.PAGE_MY_PERSONAL_RECOMMEND);
                }
            });
        }
    }
}
