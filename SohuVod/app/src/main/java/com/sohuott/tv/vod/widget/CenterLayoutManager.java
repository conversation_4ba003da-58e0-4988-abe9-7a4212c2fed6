package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.DisplayMetrics;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;


/**
 * extend LinearSmoothScroller to place item in the recyclerView middle
 *
 * <AUTHOR>
 * @date 2017/9/19
 */
public class CenterLayoutManager extends LinearLayoutManager {

    private SmoothScroller smoothScroller;

    public CenterLayoutManager(Context context) {
        super(context);
        smoothScroller = new SmoothScroller(context);
    }

    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView,
                                       RecyclerView.State state, final int position) {
        smoothScroller.setTargetPosition(position);
        startSmoothScroll(smoothScroller);
    }


    private class SmoothScroller extends LinearSmoothScroller {

        private static final float MILLISECONDS_PER_INCH = 100f;
        private float milliSecondsPerInch = -1;
        private int dtCenter;

        public SmoothScroller(Context context) {
            super(context);
        }

        @Override
        public int calculateDtToFit(int viewStart, int viewEnd, int boxStart, int boxEnd, int snapPreference) {
            dtCenter = ((boxStart + boxEnd) / 2) - ((viewStart + viewEnd) / 2);
            LibDeprecatedLogger.d("dtCenter="+dtCenter);
            return dtCenter;
        }


//        @Override
//        protected int calculateTimeForDeceleration(int dx) {
//            return 200;
//        }

        protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
            return (milliSecondsPerInch > 0 ? milliSecondsPerInch : MILLISECONDS_PER_INCH) / displayMetrics.densityDpi;
        }
    }

}
