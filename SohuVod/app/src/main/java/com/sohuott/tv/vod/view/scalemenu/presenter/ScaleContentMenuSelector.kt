package com.sohuott.tv.vod.view.scalemenu.presenter

import android.content.Context
import androidx.leanback.widget.Presenter
import androidx.leanback.widget.PresenterSelector
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentAnthologyMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentSpeedMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentMoreMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentClarityMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem

class ScaleContentMenuSelector constructor(private val context: Context) : PresenterSelector() {
    override fun getPresenter(item: Any?): Presenter {
        when (item) {
            //选集
            is ScaleContentAnthologyMenuItem -> {
                //是否是选集范围
                return if (item.isRange) {
                    ScaleContentMenuAnthologyRangePresenter(context)
                } else {
                    ScaleContentMenuAnthologyPresenter(context)
                }
            }
            //清晰度
            is ScaleContentClarityMenuItem -> {
                return ScaleContentMenuClarityPresenter(context)
            }
            //倍速
            is ScaleContentSpeedMenuItem -> {
                return ScaleContentMenuSpeedPresenter(context)
            }

            //更多功能
            is ScaleContentMoreMenuItem -> {
                return ScaleContentMenuMorePresenter(context)
            }
            //只看他
            is ScaleContentOnlySeeMenuItem -> {
                return ScaleContentMenuOnlySeePresenter(context)
            }

        }
        throw RuntimeException("ScaleContentMenuSelector is not found this item:$item ")
    }
}