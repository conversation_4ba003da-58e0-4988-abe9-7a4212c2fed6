package com.sohuott.tv.vod.activity;

import static com.sohuott.tv.vod.lib.push.event.RefreshUserEvent.PAGE_PAY;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.ItemBridgeAdapter;
import androidx.leanback.widget.OnChildViewHolderSelectedListener;
import androidx.leanback.widget.VerticalGridView;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.load.model.LazyHeaders;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.google.gson.Gson;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.FontUtils;
import com.sohu.lib_utils.FormatUtils;
import com.sohu.lib_utils.PrefUtil;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.common.Listener;
import com.sohuott.tv.vod.account.login.CarouselLogin;
import com.sohuott.tv.vod.account.login.ConfigInfo;
import com.sohuott.tv.vod.account.login.Login;
import com.sohuott.tv.vod.account.login.LoginApi;
import com.sohuott.tv.vod.account.login.PollingLoginHelper;
import com.sohuott.tv.vod.account.payment.PayApi;
import com.sohuott.tv.vod.activity.setting.ShowPrivacyWebViewActivity;
import com.sohuott.tv.vod.app.AppConstants;
import com.sohuott.tv.vod.fragment.CustomItemBridgeAdapter;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.db.greendao.DaoSessionInstance;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.db.greendao.User;
import com.sohuott.tv.vod.lib.db.greendao.UserDao;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.Commodity;
import com.sohuott.tv.vod.lib.model.FilmCommodities;
import com.sohuott.tv.vod.lib.model.LoginQrModel;
import com.sohuott.tv.vod.lib.model.PayInfoData;
import com.sohuott.tv.vod.lib.model.PayPoster;
import com.sohuott.tv.vod.lib.model.PermissionCheck;
import com.sohuott.tv.vod.lib.model.VideoDetailFilmCommodities;
import com.sohuott.tv.vod.lib.push.base.BaseMessageData;
import com.sohuott.tv.vod.lib.push.event.LoginSuccessEvent;
import com.sohuott.tv.vod.lib.push.event.PayEvent;
import com.sohuott.tv.vod.lib.push.event.RefreshUserEvent;
import com.sohuott.tv.vod.lib.push.event.ScanSuccessEvent;
import com.sohuott.tv.vod.lib.push.event.SyncPlayHistoryEvent;
import com.sohuott.tv.vod.lib.push.event.TicketEvent;
import com.sohuott.tv.vod.lib.service.PlayHistoryService;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.PostHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.AgreementPresenter;
import com.sohuott.tv.vod.presenter.launcher.PayCommoditiesPresenter;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.LoadQrPicture;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.utils.SouthMediaUtil;
import com.sohuott.tv.vod.videodetail.activity.service.VideoDetailServiceManger;
import com.sohuott.tv.vod.view.AgreementView;
import com.sohuott.tv.vod.widget.GlideImageView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.functions.Consumer;
import io.reactivex.observers.DisposableObserver;

import static com.sohuott.tv.vod.lib.push.event.RefreshUserEvent.PAGE_PAY;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.leanback.widget.ArrayObjectAdapter;
import androidx.leanback.widget.ItemBridgeAdapter;
import androidx.leanback.widget.OnChildViewHolderSelectedListener;
import androidx.leanback.widget.Presenter;
import androidx.leanback.widget.VerticalGridView;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by xianrongchen on 2016/3/1.
 * <p>
 * Modified by music on 2022/1/11
 */
public class PayActivity extends BaseActivity implements View.OnClickListener {
    //未知来源
    public static final long PAY_SOURCE_UNKNOWN = 1100010000;
    //APK外-播放器
    public static final long PAY_SOURCE_PLAYER_EXTERNAL_MANUFACTURER = 1100010001;
    //APK内-播放器
    public static final long PAY_SOURCE_PLAYER_YUE_TING = 1100010002;
    //APK内-详情页
    public static final long PAY_SOURCE_DETAIL = 1100010003;
    //APK内-状态栏 如首页开通会员
    public static final long PAY_SOURCE_TOP_BAR_STAT = 1100010004;
    //APK内 我的悦厅页面开通会员
    public static final long PAY_SOURCE_MY_YUE_TING_OPEN_VIP = 1100010005;
    //APK内 我的页面开通会员
    public static final long PAY_SOURCE_MY_OPEN_VIP = 1100010006;
    //APK内 首页运营推荐位
    public static final long PAY_SOURCE_HOME_RECOMMEND = 1100010007;
    //APK内 启动页面
    public static final long PAY_SOURCE_BOOT_ACTIVITY = 1100010008;
    //APK内 消息页面
    public static final long PAY_SOURCE_MESSAGE_ACTIVITY = 1100010009;
    //APK内 手机遥控超级会员启动支付
    public static final long PAY_SOURCE_PHONE_REMOTE = 1100010010;
    //APK内 积分页面启动支付页
    public static final long PAY_SOURCE_POINT_FRAGMENT = 1100010011;
    //APK 从全局外部dialog启动到支付页
    public static final long PAY_SOURCE_HOTSPOT = 1100010012;
    /// APK内 登录页面启动支付页面
    public static final long PAY_SOURCE_LOGIN = 1100010013;
    //APK内 活动页面支付二维码
    public static final long PAY_SOURCE_RN_QR = 1100010014;
    //APK内 活动页面启动支付页面
    public static final long PAY_SOURCE_RN = 1100010016;
    //APK 从全局内部dialog启动到支付页
    public static final long PAY_SOURCE_INSIDE_HOTSPOT = 1100010015;
    //APK 从全局内部家长管理启动到支付页
    public static final long PAY_SOURCE_CHILD_CONTROL = 1100010016;
    //apk 少儿详情页到支付页
    public static final long PAY_SOURCE_CHILD_VIDEO_DETAIL = 1100010017;
    //apk内 我的相关顶部栏跳转到支付页
    public static final long PAY_SOURCE_USER_RELATED_HEADER_VIEW = 1100010018;

    private static final int TYPE_DEFAULT = 0;
    private static final int TYPE_SINGLE = 1;
    private static final int TYPE_MULTI = 2;

    private static final int MEM_MSG_SCAN_SUCCESS = 10;
    private static final int MSG_SCAN_FAILURE = 11;
    private static final int MSG_LOGIN_SUCCESS = 12;
    private static final int MSG_CAROUSEL_USER_INFO = 13;
    private static final int MSG_SAVE_USER_INFO_TO_DB = 14;
    private static final int SINGLE_MSG_SCAN_SUCCESS = 15;
    private static final int MSG_REFRESH_QRCODE = 16;

    private static final int QRCODE_PICTURE_SIZE = 420;


    private int mCarouselTime;
    boolean mIsFromBootActivity;
    private boolean mIsScanSuccess;
    private boolean mIsSinglePurchase;
    private boolean mIsLogin; // is login before enter this page

    private int mAId;
    private int mVId;
    private int mCategoryId;
    private int mEnterPayType = 0;
    private boolean mIsChild = false;
    private String mSource;

    private String mVideoName = "";
    private String mVideoPoster = "";

    private RelativeLayout mUserContainer;
    private RelativeLayout mSingleContainer;

    private TextView mUserName;
    private TextView mUserLoginType;
    private ImageView mUserVipLogo;

    private Button mUserLogin;
    //    private TextView mMemType;
//    private TextView mTicket;
//    private TextView mTicketDesc;
//    private TextView mPayTipText;
    private TextView mPayTipText2;
    //    private TextView mQrCodeScanSuccessTip1;
//    private TextView mQrCodeScanSuccessTip2;
//    private TextView mSinglePayTipText;
//    private TextView mSinglePayTipText2;
//    private TextView mSingleQrCodeScanSuccessTip1;
//    private TextView mSingleQrCodeScanSuccessTip2;
    private TextView mSingleVideoName;
    private TextView mSinglePrice;
    private TextView mSinglePricePrefix;
    private TextView mSingleQrcodePrice;
    private TextView mSingleTip;

    private GlideImageView mUserAvatar;
    private GlideImageView mPayOperationImage;
    private GlideImageView mSinglePoster;
    private GlideImageView mBackground;

    private ImageView mQrCodeImage;
    private ImageView mSingleQrCodeImage;
    private ImageView mQrCodeScanSuccessImage;
    private ImageView mSingleQrCodeScanSuccessImage;
    private ImageView mPayTipImage;
    private ImageView mSinglePayTipImage;

    private User mUser;
    private LoginUserInformationHelper mHelper;
    private UserDao mUserDao;

    private MyHandler mHandler;

    private View mVipContentLayout;
    private View mSingleContentLayout;
    private View mPayDownLayout;
    private View mRootView;
    private RelativeLayout mQrcodeContainer;
    private View mQrcodeSingleContainer;

    //    private TextView mAgreementTipsText;
    private AgreementView mAgreementView;

    PollingLoginHelper mPollingLoginHelper;
    private long mPaySourceComeFrom = PAY_SOURCE_UNKNOWN;
    private CompositeDisposable mCompositeDisposable = new CompositeDisposable();
    private boolean isSkyworthPay = false;

    //new

    private VerticalGridView mCommoditiesListView;
    private ArrayObjectAdapter mCommoditiesArrayObjectAdapter;
    private PayCommoditiesPresenter mCommoditiesPresenter;
    private ItemBridgeAdapter mBridgeAdapter;

    private Button mActivation, mAgreement, mPayInfo;
    private TextView mPayCommodityPrice;
    private ImageView mLoginType;
    private TextView mSingleTips3;

    private ViewGroup mPayCover;

    private static boolean isEverClickedAutoSign;

    private List<Commodity.DataEntity.CommoditiesEntity> mCommoditiesEntityList;
    private FilmCommodities.DataEntity.ComiditiesEntity mSingleCommodity;
    private int mCommodityId, mIsSign;
    private String mEncrypt;

    HashMap<String, String> pathInfo = new HashMap<String, String>();


    private TextView mPayTipPayEE, mPayTipProvider, mPayTipContent;

    private String mPayTipAutoSign;//展开后文字说明

    private int mCurrentPosition;//当前选中的商品位置
    private int mAutoSignPosition;//连续包月商品位置

    private SurfaceView mSV;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SouthMediaUtil.southNewMediaCheck(this);

        if (isFullScreen()) {
            setContentView(R.layout.activity_pay_full);
            if (!mVideoPoster.trim().equals("")) {
                mBackground = (GlideImageView) findViewById(R.id.pay_root_background);
                mBackground.setImageRes(mVideoPoster);
            }
        } else {
            setContentView(R.layout.activity_pay);
        }

        mHelper = LoginUserInformationHelper.getHelper(getApplicationContext());
        mUserDao = DaoSessionInstance.getDaoSession(this).getUserDao();
        mPollingLoginHelper = new PollingLoginHelper();
        initView();

        setDisplay();

        mHandler = new MyHandler(this);
//        RequestManager.getInstance().onEvent("5_pay", "100001", null, null, null, null, null);

        setPageName("5_pay");
        mIsLogin = mHelper.getIsLogin();
    }

    //设置窗口大小
    private void setDisplay() {
        //设置弹出窗口与屏幕对齐
        Window win = this.getWindow();
        int density = (int) (getResources().getDisplayMetrics().density);
        //设置内边距，这里设置为0
        win.getDecorView().setPadding(1 * density, 1 * density, 1 * density, 1 * density);
        WindowManager.LayoutParams lp = win.getAttributes();
        //设置窗口宽度
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        //设置窗口高度
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        //设置Dialog位置
        lp.gravity = Gravity.TOP | Gravity.LEFT;
        win.setAttributes(lp);
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (getIntent() != null) {
            setIntent(intent);
        }
        initData();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isSkyworthPay) {
            return;
        }

        initData();

        getCfgInfo();
        if (!isFullScreen()) {
            //new
            getPrivilegeCommodities();
        } else {
            freshQrcode(0, 0, "");
        }


        if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE) {
            mUserLogin.requestFocus();
        }

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }

        pageExposure(10135, "imp");
    }

    private void pageExposure(int eventId, String type) {

        if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE) {
            pathInfo.put("pageId", "1007");
        } else if (mEnterPayType == Constant.PAY_ENTER_TYPE_VIP || mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE) {
            pathInfo.put("pageId", "1006");
        }

        HashMap<String, String> memoInfo = new HashMap<String, String>();
        memoInfo.put("isLogin", mHelper.getIsLogin() ? "1" : "0");
        RequestManager.getInstance().onAllEvent(new EventInfo(eventId, type), pathInfo, null, null);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isSkyworthPay) {
            return;
        }
        if (!isFullScreen()) {
            mCommoditiesArrayObjectAdapter.clear();
        }
        mSingleCommodity = null;
        mCommoditiesEntityList = null;
        EventBus.getDefault().unregister(this);
        mHandler.removeMessages(MSG_CAROUSEL_USER_INFO);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (isSkyworthPay) {
            return;
        }
        mHandler.removeMessages(MSG_CAROUSEL_USER_INFO);
        mHandler = null;
        mPollingLoginHelper.onDestroy();
        mCompositeDisposable.clear();
        if (mAgreementView != null) {
            mAgreementView.release();
        }
    }

    @Override
    public void finish() {
        super.finish();
        //在此时设置转场动画
        overridePendingTransition(R.anim.dialog_scale_in, R.anim.dialog_scale_out);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (isSkyworthPay) {
            return super.onKeyUp(keyCode, event);
        }
        if (keyCode == KeyEvent.KEYCODE_BACK) {

            //用户同意点击续费，且当前在续费item，且展开状态，按back键收回
            if (mCurrentPosition == mAutoSignPosition) {
                RecyclerView.ViewHolder viewHolder = mCommoditiesListView.findViewHolderForAdapterPosition(mCurrentPosition);
                if (viewHolder != null && viewHolder.itemView.getHeight() != getResources().getDimensionPixelSize(R.dimen.y168)) {
                    updateItemView(mCurrentPosition);
                    return true;
                }
            }
            if (mIsFromBootActivity) {
                ActivityLauncher.startHomeActivity(this);
            }
//            else if (PlayerActivity.INTENT_SOURCE.equals(mSource)) {
//                AppLogger.d("Back to Player Activity! ");
//                Intent intent = new Intent(this, PlayerActivity.class);
//                // 将结果返回
//                setResult(PlayerActivity.INTENT_REQUEST_CODE, intent);
//            }
            else {
                if (!mHelper.getIsLogin()) {
                    quitQrcode();
                }
            }
            finishActivity(false);
            return true;
        } else if (keyCode == KeyEvent.KEYCODE_MENU) {
            //show popup window
//            mAgreementView.show(mAgreementTipsText);
            return true;
        } else {
            return super.onKeyUp(keyCode, event);
        }
    }

    private void finishActivity(boolean isRePlay) {
        VideoDetailServiceManger.getInstants().onStartActivityPlay(isRePlay);
        finish();
    }

    @Override
    protected boolean isEventBusAvailable() {
        return true;
    }

    @Subscribe
    public void onEventMainThread(TicketEvent event) {
        if (isSkyworthPay) {
            return;
        }
        if (null == event) {
            return;
        }
        displayUserInfo();
    }

    private volatile boolean mIsPollingLogin;

    @Subscribe
    public void onEventMainThread(ScanSuccessEvent event) {
        if (isSkyworthPay) {
            return;
        }
        if (null == event) {
            return;
        }
        if (event.getType() == 0) {
            sendEmptyMessage(MEM_MSG_SCAN_SUCCESS);
        } else {
            sendEmptyMessage(SINGLE_MSG_SCAN_SUCCESS);
        }
        if (!mHelper.getIsLogin()) {
            /**
             * login scan succeed
             */
            mIsPollingLogin = true;
            mPollingLoginHelper.startPolling(this, mPollingToken, mQrcode, new Listener<Login>() {
                @Override
                public void onSuccess(Login response) {
                    LibDeprecatedLogger.d("passportPolling: onSuccess");
                    if (null != response) {
                        Login.LoginData data = response.getData();
                        String message = response.getMessage();
                        int status = response.getStatus();
                        if (status == 200 && null != data) {
                            mIsPollingLogin = false;
                            PostHelper.postLoginSuccessEvent();

                            String loginType = "";
                            if (response.getData().getUType().equals("1")) {
                                loginType = "1";
                            } else if (response.getData().getUType().equals("2")) {
                                loginType = "2";
                            } else if (response.getData().getUType().equals("3")) {
                                loginType = "3";
                            } else if (response.getData().getUType().equals("4")) {
                                loginType = "5";
                            } else if (response.getData().getUType().equals("5")) {
                                loginType = "4";
                            }
                            HashMap memoInfo = new HashMap();
                            memoInfo.put("method", loginType);
                            RequestManager.getInstance().onAllEvent(new EventInfo(10132, "slc"), pathInfo, null, memoInfo);
                        } else {
                            ToastUtils.showToast2(PayActivity.this, message);
                        }
                    }
                }

                @Override
                public void onError(Throwable e) {
                    LibDeprecatedLogger.w("polling login error:" + e.getMessage());
                }
            });
        }
        mIsScanSuccess = true;
    }

    @Subscribe
    public void onEventMainThread(LoginSuccessEvent event) {
        if (isSkyworthPay) {
            return;
        }
        if (null == event) {
            return;
        }
        sendEmptyMessage(MSG_LOGIN_SUCCESS);
        syncPlayHistory();
    }

    private void sendEmptyMessage(int what) {
        if (mHandler != null) {
            mHandler.sendEmptyMessage(what);
        }
    }

    private void sendEmptyMessageDelayed(int what, long delayMillis) {
        if (mHandler != null) {
            mHandler.sendEmptyMessageDelayed(what, delayMillis);
        }
    }

    @Subscribe
    public void onEventMainThread(SyncPlayHistoryEvent event) {
        if (isSkyworthPay) {
            return;
        }
        if (null == event) {
            return;
        }
        ToastUtils.showToast2(this, getApplicationContext().getResources().getString(R.string.txt_activity_input_login_success_tip));
        if ((mPaySourceComeFrom == PAY_SOURCE_DETAIL
                || mPaySourceComeFrom == PAY_SOURCE_PLAYER_YUE_TING
                || mPaySourceComeFrom == PAY_SOURCE_CHILD_VIDEO_DETAIL
                || mIsFromBootActivity)
                && (mHelper.isVip() && (System.currentTimeMillis()) <= Long.valueOf(mHelper.getVipTime()))) {
            if (mEnterPayType != Constant.PAY_ENTER_TYPE_SINGLE) {
//                if(mVipContentLayout.isFocused()){
                finishActivity(true);
//                }
            }
            if (mIsFromBootActivity) {
                ActivityLauncher.startHomeActivity(this);
            }
        }
    }

    @Subscribe
    public void onEventMainThread(RefreshUserEvent event) {
        if (isSkyworthPay) {
            return;
        }
        if (null == event || event.getPageIndex() != PAGE_PAY) {
            return;
        }
        if (mEnterPayType != Constant.PAY_ENTER_TYPE_SINGLE) {
            finishActivity(true);

        }
    }

    @Subscribe
    public void onEventMainThread(PayEvent event) {
        if (isSkyworthPay) {
            return;
        }
        if (null == event) {
            return;
        }
        if (mIsFromBootActivity) {
            ActivityLauncher.startHomeActivity(this);
        }
        if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE && event.getType() == 0) {
            return;
        }
        finishActivity(true);

    }

    private void initView() {
        //new
        mPayCover = (ConstraintLayout) findViewById(R.id.pay_cover);
        mCommoditiesListView = (VerticalGridView) findViewById(R.id.pay_commodities);
        mPayCommodityPrice = (TextView) findViewById(R.id.pay_tip_txt3);

        mRootView = findViewById(R.id.pay_root_view);
        mUserContainer = (RelativeLayout) findViewById(R.id.user_container);
        mSingleContainer = (RelativeLayout) findViewById(R.id.single_purchase_container);
        mUserAvatar = (GlideImageView) findViewById(R.id.user_avatar);
        mPayOperationImage = (GlideImageView) findViewById(R.id.operation_image);
        mSinglePoster = (GlideImageView) findViewById(R.id.single_poster);
        mQrcodeContainer = findViewById(R.id.qrcode_container);
        mQrcodeSingleContainer = findViewById(R.id.single_qrcode_container);

        mUserName = (TextView) findViewById(R.id.user_name);
        mUserLoginType = (TextView) findViewById(R.id.user_login_type);
        mUserVipLogo = (ImageView) findViewById(R.id.user_vip_logo);
        mUserLogin = (Button) findViewById(R.id.pay_login_btn);
        mLoginType = (ImageView) findViewById(R.id.login_type);

        mActivation = (Button) findViewById(R.id.vip_activation);
        mAgreement = (Button) findViewById(R.id.vip_privacy_agreement);
        mPayInfo = findViewById(R.id.pay_info);
        mPayTipProvider = findViewById(R.id.pay_tip_provider);
        mPayTipPayEE = findViewById(R.id.pay_tip_payee);
        mPayTipContent = findViewById(R.id.pay_tip_content);


        if (!isFullScreen()) {
            mUserLogin.setOnClickListener(this);
            mActivation.setOnClickListener(this);
            mAgreement.setOnClickListener(this);
            mPayInfo.setOnClickListener(this);
        }

//        mMemType = (TextView) findViewById(R.id.mem_type);
//        mTicket = (TextView) findViewById(R.id.ticket);
//        mTicketDesc = (TextView) findViewById(R.id.ticket_desc);
//        mPayTipText = (TextView) findViewById(R.id.pay_tip_txt);
        mPayTipText2 = (TextView) findViewById(R.id.pay_tip_txt2);
//        mQrCodeScanSuccessTip1 = (TextView) findViewById(R.id.qrcode_scan_success_txt_tip1);
//        mQrCodeScanSuccessTip2 = (TextView) findViewById(R.id.qrcode_scan_success_txt_tip2);
//        mSinglePayTipText = (TextView) findViewById(R.id.single_pay_tip_txt);
//        mSinglePayTipText2 = (TextView) findViewById(R.id.single_pay_tip_txt2);
//        mSingleQrCodeScanSuccessTip1 = (TextView) findViewById(R.id.single_qrcode_scan_success_txt_tip1);
//        mSingleQrCodeScanSuccessTip2 = (TextView) findViewById(R.id.single_qrcode_scan_success_txt_tip2);
        mSingleVideoName = (TextView) findViewById(R.id.single_video_name);
        mSinglePrice = (TextView) findViewById(R.id.single_price);
        mSinglePricePrefix = (TextView) findViewById(R.id.single_price_prefix);
        mSingleQrcodePrice = (TextView) findViewById(R.id.single_pay_tip_txt3);
        mSingleTip = (TextView) findViewById(R.id.single_tip_2);
        mSingleTips3 = (TextView) findViewById(R.id.single_tip_3);

        mQrCodeImage = (ImageView) findViewById(R.id.qrcode_image);
        mSingleQrCodeImage = (ImageView) findViewById(R.id.single_qrcode_image);
        mQrCodeScanSuccessImage = (ImageView) findViewById(R.id.qrcode_scan_success_image);
        mVipContentLayout = findViewById(R.id.vip_content_layout);
        mSingleContentLayout = findViewById(R.id.single_content_layout);
        mPayDownLayout = findViewById(R.id.pay_down_layout);
        mVipContentLayout.setOnClickListener(this);
        mSingleContentLayout.setOnClickListener(this);

        setTypeface(mPayTipText2);
        setTypeface(mPayCommodityPrice);
        setTypeface(mSinglePricePrefix);
        setTypeface(mSingleQrcodePrice);
        setTypeface(mSinglePrice);

        // tianmao partner
        if (Util.getPartnerNo(this).equals("80151042")) {
//            mPayTipText2.setText(getApplicationContext().getResources().getString(R.string.txt_activity_pay_qrcode_title4));
//            mSinglePayTipText2.setText(getApplicationContext().getResources().getString(R.string.txt_activity_pay_qrcode_title4));
            mPayTipImage.setImageResource(R.drawable.ic_wechat2);
            mSinglePayTipImage.setImageResource(R.drawable.ic_wechat2);
        }

//        mAgreementTipsText = (TextView) findViewById(R.id.fee_agreement_tv);
        mAgreementView = new AgreementView(this, AgreementPresenter.NAME_FEE);

        mRootView.getViewTreeObserver().addOnGlobalFocusChangeListener(new ViewTreeObserver.OnGlobalFocusChangeListener() {
            @Override
            public void onGlobalFocusChanged(View oldFocus, View newFocus) {
                LibDeprecatedLogger.d("onGlobalFocusChanged, oldFocus = " + oldFocus + ", newFocus = " + newFocus);
            }
        });

        if (!isFullScreen()) {
            mUserLogin.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View view, int i, KeyEvent keyEvent) {
                    if (keyEvent.getAction() == KeyEvent.ACTION_DOWN &&
                            keyEvent.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN &&
                            mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE) {
                        return true;
                    } else {
                        return false;
                    }
                }
            });
        }

        //焦点到订购信息时，恢复布局
        //焦点到立即登录时，恢复布局


        mUserLogin.setOnFocusChangeListener((v, hasfocus) -> {
            if (mCurrentPosition == mAutoSignPosition) {
                updateItemView(mCurrentPosition);
            }
        });
        mPayInfo.setOnFocusChangeListener((v, hasFocus) -> {
            if (mCurrentPosition == mAutoSignPosition) {
                updateItemView(mCurrentPosition);
            }
        });
        //TCL-CN-T963-s11机型透明背景图片会挖空activity背景，这里使用surfaceview设置背景
        String MANUFACTURER = Build.MANUFACTURER;
        String MODEL = Build.MODEL;
        if (TextUtils.equals(MANUFACTURER, "TCL Multimedia") && TextUtils.equals(MODEL, "ak30a5")) {
            mSV = findViewById(R.id.surf_background);
            mSV.setVisibility(View.VISIBLE);
            mSV = findViewById(R.id.surf_background);
            mSV.setZOrderOnTop(true);
            mSV.setZOrderMediaOverlay(true);
//        mSV.getHolder().setFormat(PixelFormat.TRANSPARENT);
            mSV.getHolder().addCallback(new SurfaceHolder.Callback() {
                @Override
                public void surfaceCreated(@NonNull SurfaceHolder surfaceHolder) {
                    Paint paint = new Paint();
                    paint.setAntiAlias(true);
                    paint.setStyle(Paint.Style.STROKE);
//                Bitmap bitmap =   BitmapFactory.decodeResource(getResources(),
//                        R.drawable.bg_pay_qrcode);
                    Canvas canvas = surfaceHolder.lockCanvas();  // 先锁定当前surfaceView的画布
//                canvas.drawBitmap(bitmap, 0, 0, paint); //执行绘制操作
                    canvas.drawColor(Color.parseColor("#28222A"));
                    surfaceHolder.unlockCanvasAndPost(canvas); // 解除锁定并显示在界面上

                }

                @Override
                public void surfaceChanged(@NonNull SurfaceHolder surfaceHolder, int i, int i1, int i2) {

                }

                @Override
                public void surfaceDestroyed(@NonNull SurfaceHolder surfaceHolder) {

                }
            });
        }


    }

    private void setTypeface(View view) {
        FontUtils.setTypeface(this, view);
    }

    private void getPrivilegeCommodities() {
        NetworkApi.getPrivilegeCommodities(mHelper.getIsLogin(), "6", mHelper.getLoginPassport(),
                new DisposableObserver<Commodity>() {
                    @Override
                    public void onNext(Commodity value) {
                        if (null != value) {
                            if (value.getStatus() == 200) {
                                mCommoditiesEntityList = value.getData().getCommodities();
                                mCommoditiesArrayObjectAdapter.clear();
                                if (mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE) {
                                    if (mSingleCommodity != null) {
                                        mCommoditiesArrayObjectAdapter.addAll(0, mCommoditiesEntityList);
                                        mCommoditiesArrayObjectAdapter.add(mSingleCommodity);
                                    }
                                } else {
                                    mCommoditiesArrayObjectAdapter.addAll(0, mCommoditiesEntityList);
                                }
                            } else {
                                //error view
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        //error view
                    }

                    @Override
                    public void onComplete() {

                    }
                });


    }


    private void displayUserInfo() {
        if (mHelper.getIsLogin()) {
            final String avatar = mHelper.getLoginPhoto();
            if (null != avatar && !avatar.trim().equals("")) {
                mUserAvatar.setCircleImageRes(avatar);
            }
            mUserName.setText(mHelper.getNickName());
            if (isFullScreen()) { //?
//                mUserContainer.setVisibility(View.INVISIBLE);
            } else {
                mUserLogin.setVisibility(View.GONE);
                mLoginType.setVisibility(View.VISIBLE);
//                mUserContainer.setVisibility(View.VISIBLE);
            }
//            mUnloginTips.setVisibility(View.GONE);

        } else {

//            mUserContainer.setVisibility(View.INVISIBLE);
            if (isFullScreen()) {
//                mUnloginTips.setVisibility(View.GONE);
            } else {
                mLoginType.setVisibility(View.GONE);
                mUserName.setText("未登录");
                mUserLoginType.setText("登录同步会员特权");
//                mUnloginTips.setVisibility(View.VISIBLE);
                if (mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE) {
//                    mUnloginTips.setText("超级会员，登录后免费看；非会员用户，请直接购买超级会员，或选择单片购买。");
                } else if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE) {
//                    mUnloginTips.setText("请选择单片购买。");
                } else {
//                    mUnloginTips.setText("超级会员，登录后免费看；非会员用户，请直接购买超级会员。");
                }
            }
        }
        if (mHelper.isVip()) {
//            mPayTipText.setText(getApplicationContext().getResources().getString(R.string.txt_activity_pay_qrcode_title2));
        }


        if (!mHelper.getIsLogin()) {
            return;
        }
        mUserLoginType.setText(mHelper.getUtype());

        switch (Util.getSouthMediaLoginType(mHelper.getUtype())) {
            case 4:
                mLoginType.setBackgroundResource(R.drawable.login_type_weibo);
                break;
            case 1:
                mLoginType.setBackgroundResource(R.drawable.login_type_wechat);
                break;
            case 2:
                mLoginType.setBackgroundResource(R.drawable.login_type_qq);
                break;
            case 3:
                mLoginType.setBackgroundResource(R.drawable.login_type_sohu);
                break;
        }

        if (mHelper.isVip()) {
            String vipDate = FormatUtils.formatDate(Long.valueOf(mHelper.getVipTime()));
            mUserLoginType.setText("会员有效期：" + vipDate + "  |  观影券：" + mHelper.getUserTicketNumber());
            mUserLoginType.setTextColor(Color.parseColor("#DEBB99"));
            mUserLoginType.setAlpha(0.7f);
            mUserName.setTextColor(Color.parseColor("#DEBB99"));
            mUserVipLogo.setVisibility(View.VISIBLE);
            mUserVipLogo.setImageResource(R.drawable.top_bar_vip);
//            mMemType.setText("  " + getApplicationContext().getResources().getString(R.string.txt_activity_has_login_vip));
//            mMemType.setTextColor(getApplicationContext().getResources().getColor(R.color.pay_vip_user_color));
//            mTicketDesc.setText("，续费会员，立即赠券！");

            if ((System.currentTimeMillis()) > Long.valueOf(mHelper.getVipTime())) {
                mUserLoginType.setText("会员已过期，观影券冻结" + mHelper.getUserTicketNumber() + "，续费解冻并赠新券。");
                mUserVipLogo.setVisibility(View.VISIBLE);
                mUserVipLogo.setImageResource(R.drawable.top_bar_vip_expired);
                mUserName.setTextColor(Color.parseColor("#E8E8E8"));
                mUserLoginType.setTextColor(Color.parseColor("#E8E8E8"));
                mUserLoginType.setAlpha(0.7f);
            }
        } else {

//                mTicketDesc.setText("，开通会员，立即赠券！");
            mUserLoginType.setText("普通用户");
            mUserVipLogo.setVisibility(View.GONE);
//            mMemType.setText("  " + getApplicationContext().getResources().getString(R.string.txt_activity_has_login_common_user));

//            if(mIsChild){
//                mMemType.setTextColor(getApplicationContext().getResources().getColor(R.color.pay_child_user_desc_color));
//            }else {
//                mMemType.setTextColor(getApplicationContext().getResources().getColor(R.color.pay_normal_user_color));
//            }


        }
//        mTicket.setText("  观影券" + mHelper.getUserTicketNumber());
    }

    private boolean isFullScreen() {
        Intent intent = getIntent();
        if (null != intent) {
            if (intent.getData() != null) {
                Uri uri = getIntent().getData();
                mPaySourceComeFrom = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_PAY_SOURCE_COME_FROM));
                mVideoPoster = uri.getQueryParameter(ParamConstant.PARAM_ALBUM_POSTER);
            } else {
                mPaySourceComeFrom = getIntent().getLongExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, PAY_SOURCE_UNKNOWN);
                mVideoPoster = intent.getStringExtra(ParamConstant.PARAM_ALBUM_POSTER);
            }
        }
        return mPaySourceComeFrom == PAY_SOURCE_HOME_RECOMMEND && !mVideoPoster.equals("");
    }

    private void initData() {
        Intent intent = getIntent();
        if (null != intent) {
            if (intent.getData() != null) {
                Uri uri = getIntent().getData();
                if (!TextUtils.isEmpty(uri.getQueryParameter("backhome"))) {
                    mIsFromBootActivity = uri.getQueryParameter("backhome").equals("1");
                }
                mIsChild = Util.convertStringToBoolean(uri.getQueryParameter(ParamConstant.PARAM_CHILD));
                mAId = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_AID));
                mVId = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_VID));
                mCategoryId = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_CATEGORY_ID));
                mVideoName = uri.getQueryParameter(ParamConstant.PARAM_ALBUM_TITLE);
                mVideoPoster = uri.getQueryParameter(ParamConstant.PARAM_ALBUM_POSTER);
                mEnterPayType = Util.convertStringToInt(uri.getQueryParameter(ParamConstant.PARAM_PAY_TYPE));
                LibDeprecatedLogger.d("mEnterPayType = " + mEnterPayType);
            } else {
                mIsFromBootActivity = intent.getBooleanExtra(ParamConstant.PARAM_IS_FROM_BOOTACTIVITY, false);
                mPaySourceComeFrom = intent.getLongExtra(ParamConstant.PARAM_PAY_SOURCE_COME_FROM, PAY_SOURCE_UNKNOWN);
                mAId = intent.getIntExtra(ParamConstant.PARAM_AID, 0);
                mVId = intent.getIntExtra(ParamConstant.PARAM_VID, 0);
                mCategoryId = intent.getIntExtra(ParamConstant.PARAM_CATEGORY_ID, 0);
                mVideoName = intent.getStringExtra(ParamConstant.PARAM_ALBUM_TITLE);
                mVideoPoster = intent.getStringExtra(ParamConstant.PARAM_ALBUM_POSTER);
                mEnterPayType = intent.getIntExtra(ParamConstant.PARAM_PAY_TYPE, 0);
                mIsChild = intent.getBooleanExtra(ParamConstant.PARAM_CHILD, false);
                mSource = intent.getStringExtra(ParamConstant.PARAM_SOURCE);
                LibDeprecatedLogger.d("Intent from: " + mSource);
            }
        }

        if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE || mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE) {
            if (mAId == 0 && mVId == 0) {
                mEnterPayType = Constant.PAY_ENTER_TYPE_VIP;
            }
        }

        switch (mEnterPayType) {
            case Constant.PAY_ENTER_TYPE_SINGLE:
                mSingleContentLayout.setVisibility(View.VISIBLE);
                mSingleContentLayout.setFocusable(false);
                mVipContentLayout.setVisibility(View.GONE);
                mPayDownLayout.setVisibility(View.GONE);
                break;
            case Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE:
//                mSingleContentLayout.setVisibility(View.VISIBLE);
                mVipContentLayout.setVisibility(View.VISIBLE);
//                mPayDownLayout.setVisibility(View.VISIBLE);
                break;
            default:
                mSingleContentLayout.setVisibility(View.GONE);
                mVipContentLayout.setVisibility(View.VISIBLE);
                mPayDownLayout.setVisibility(View.GONE);
                break;
        }


        if ((mAId != 0 || mVId != 0) && (!mVideoPoster.isEmpty() || !mVideoName.isEmpty())) {
            mIsSinglePurchase = true;
            mHelper.putIsSinglePurchase(mIsSinglePurchase);
        } else {
            mIsSinglePurchase = false;
        }

        if (!isFullScreen()) {
            displayUserInfo();
        }

        if (mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE || mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE) {

//            mSinglePayTipText.setText(getApplicationContext().getResources().getString(R.string.txt_activity_pay_qrcode_title5));
        }

//        if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE) {
//            freshQrcode(0, 0, "");
//        }


//        freshQrcode();


        if (mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE || mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE) {
//            mPayOperationImage.setVisibility(View.VISIBLE);
//            getPoster();
            mSingleContainer.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(mVideoName)) {
                mSingleVideoName.setText(mVideoName);
            }

            if (mAId == 0 && mVId == 0) {
                String text = 6 + getApplicationContext().getResources().getString(R.string.txt_activity_pay_unit);
//                mSinglePrice.setText(text);
            } else {
                if (mHelper.getIsLogin()) {
                    loadKey();
                }
                getSinglePurchasePrice();
            }
        } else if (mEnterPayType == Constant.PAY_ENTER_TYPE_TICKET) {
            getPoster();
            enterTicketUseActivity();
        } else {
//            mPayOperationImage.setVisibility(View.VISIBLE);
            mSingleContainer.setVisibility(View.GONE);
//            getPoster();
        }


        if (!isFullScreen()) {
            mCommoditiesPresenter = new PayCommoditiesPresenter();
            mCommoditiesArrayObjectAdapter = new ArrayObjectAdapter(mCommoditiesPresenter);
            mBridgeAdapter = createItemBridgeAdapter(mCommoditiesArrayObjectAdapter);
            mCommoditiesListView.setAdapter(mBridgeAdapter);
            mCommoditiesListView.requestFocus();
            mCommoditiesListView.setVerticalSpacing(8);

            mCommoditiesListView.setOnChildViewHolderSelectedListener(new OnChildViewHolderSelectedListener() {
                @Override
                public void onChildViewHolderSelected(RecyclerView parent, RecyclerView.ViewHolder child, int position, int subposition) {
                    super.onChildViewHolderSelected(parent, child, position, subposition);
                    LibDeprecatedLogger.d("position : " + position);

                    mCurrentPosition = position;

                    updateItemView(mAutoSignPosition);

                    Message msg = new Message();
                    msg.what = MSG_REFRESH_QRCODE;
                    msg.arg1 = position;
                    mHandler.removeMessages(MSG_REFRESH_QRCODE);
                    mHandler.sendMessageDelayed(msg, 300);

                }
            });
        }

        try {
            PayInfoData payInfo = new Gson().fromJson(PrefUtil.getString(AppConstants.KEY_PAY_INFO, ""), PayInfoData.class);
            mPayTipPayEE.setText(payInfo.getPayee());
            mPayTipProvider.setText(payInfo.getProvider());
            mPayTipContent.setText(payInfo.getContent());
            mPayTipAutoSign = payInfo.getAuto_sign();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //还原itemView高度的布局
    private void updateItemView(int position) {
        RecyclerView.ViewHolder viewHolder = mCommoditiesListView.findViewHolderForAdapterPosition(position);
        if (viewHolder != null && viewHolder.itemView.getHeight() != getResources().getDimensionPixelSize(R.dimen.y168)) {
            View itemView = viewHolder.itemView;
            itemView.getLayoutParams().height = getResources().getDimensionPixelSize(R.dimen.y168);
            itemView.requestLayout();

            TextView descTextView = itemView.findViewById(R.id.pay_commodity_desc);
            Commodity.DataEntity.CommoditiesEntity item = mCommoditiesEntityList.get(position);
            if (descTextView != null && item != null) {
                descTextView.setSingleLine(true);
                descTextView.setText(item.getDescription());
            }

            View lineView = itemView.findViewById(R.id.line);
            if (lineView != null) {
                lineView.setVisibility(View.GONE);
            }
        }
    }

    private void freshQrcode(int commodityId, int sig, String encrypt) {
        String qrCodeImageUrl = "";
        String singleQrCodeImageUrl = "";
        qrCodeImageUrl = UrlWrapper.getScanPrepayQrCodeUrl(QRCODE_PICTURE_SIZE, QRCODE_PICTURE_SIZE,
                UserInfoHelper.getGid(), mHelper.getLoginPassport(), mHelper.getLoginToken(), commodityId, mAId, mVId, sig, encrypt);
        if (mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE) {
            singleQrCodeImageUrl = UrlWrapper.getScanPrepayQrCodeUrlForSingleAlbum(QRCODE_PICTURE_SIZE, QRCODE_PICTURE_SIZE,
                    UserInfoHelper.getGid(), mHelper.getLoginPassport(), mHelper.getLoginToken(), mAId, mVId, mCategoryId, encrypt);
            if (null != qrCodeImageUrl && !qrCodeImageUrl.trim().equals("")) {
                LoadQrPicture normalLoadPictrue = new LoadQrPicture(this, mLoadSucceedConsumer);
                normalLoadPictrue.setPaySourceComeFrom(mPaySourceComeFrom);
                if (commodityId != 0) {
                    normalLoadPictrue.getPicture(qrCodeImageUrl, mQrCodeImage);
                } else {
                    normalLoadPictrue.getPicture(singleQrCodeImageUrl, mQrCodeImage);
                }
                RequestManager.getInstance().onEvent("5_pay", "5_pay_vipQR", null, null, null, null, null);
            }
            if (null != singleQrCodeImageUrl && !singleQrCodeImageUrl.trim().equals("")) {
                LoadQrPicture normalLoadPictrue = new LoadQrPicture(this, mLoadSucceedConsumer);
                normalLoadPictrue.setPaySourceComeFrom(mPaySourceComeFrom);
                normalLoadPictrue.getPicture(singleQrCodeImageUrl, mSingleQrCodeImage);
                RequestManager.getInstance().onEvent("5_pay", "5_pay_singleQR", null, null, null, null, null);
            }

        } else if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE) {
            singleQrCodeImageUrl = UrlWrapper.getScanPrepayQrCodeUrlForSingleAlbum(QRCODE_PICTURE_SIZE, QRCODE_PICTURE_SIZE,
                    UserInfoHelper.getGid(), mHelper.getLoginPassport(), mHelper.getLoginToken(), mAId, mVId, mCategoryId, encrypt);
            if (null != singleQrCodeImageUrl && !singleQrCodeImageUrl.trim().equals("")) {
                LoadQrPicture normalLoadPictrue = new LoadQrPicture(this, mLoadSucceedConsumer);
                normalLoadPictrue.setPaySourceComeFrom(mPaySourceComeFrom);
                normalLoadPictrue.getPicture(singleQrCodeImageUrl, mSingleQrCodeImage);
                RequestManager.getInstance().onEvent("5_pay", "5_pay_singleQR", null, null, null, null, null);
            }
        } else {
            if (null != qrCodeImageUrl && !qrCodeImageUrl.trim().equals("")) {
                LoadQrPicture loadQrPicture = new LoadQrPicture(this, mLoadSucceedConsumer);
                loadQrPicture.setPaySourceComeFrom(mPaySourceComeFrom);
                loadQrPicture.getPicture(qrCodeImageUrl, mQrCodeImage);
                RequestManager.getInstance().onEvent("5_pay", "5_pay_vipQR", null, null, null, null, null);
            }
        }
    }

    private void quitQrcode() {
        LoginApi.getQrcodeQuit(new Listener<BaseMessageData>() {
            @Override
            public void onSuccess(BaseMessageData response) {
                LibDeprecatedLogger.d("getQrcodeQuit()response: " + response);
                if (null != response) {
                    String message = response.getMessage();
                    int status = response.getStatus();

                    if (status == 200) {

                    } else {
                        ToastUtils.showToast2(PayActivity.this, message);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("getQrcodeQuit()Error: " + e.getMessage(), e);
            }
        });
    }

    private void getPoster() {
        DisposableObserver disposableObserver = new DisposableObserver<PayPoster>() {
            @Override
            public void onNext(PayPoster response) {
                LibDeprecatedLogger.d("getPayPoster()response: " + response);
                if (null != response) {
                    PayPoster.DataEntity data = response.getData();
                    String message = response.getMessage();
                    int status = response.getStatus();
                    if (status == 0 && null != data) {

                        if (!TextUtils.isEmpty(data.getSnm_pic())) {
                            if (mPayOperationImage != null) {
                                mPayOperationImage.setImageRes(data.getSnm_pic(), getResources().getDrawable(R.color.colorTransparent), getResources().getDrawable(R.color.colorTransparent));
                            }
                        } else {
                            setDefaultOperation();
                        }

                    } else {
                        ToastUtils.showToast2(PayActivity.this, message);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("getPayPoster()Error: " + e.getMessage(), e);

                setDefaultOperation();
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("getPayPoster(): onComplete()");
            }
        };
        NetworkApi.getPayPoster(Constant.KEY_VOD_PAY, disposableObserver);
        mCompositeDisposable.add(disposableObserver);
    }

    private void getSinglePurchasePrice() {
        DisposableObserver disposableObserver = new DisposableObserver<FilmCommodities>() {
            @Override
            public void onNext(FilmCommodities response) {
                LibDeprecatedLogger.d("getCommodities()response: " + response);
                if (null != response) {
                    FilmCommodities.DataEntity data = response.getData();
                    String message = response.getMessage();
                    int status = response.getStatus();

                    if (status == 200 && null != data) {
                        if (mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE && data.getComidities() != null &&
                                data.getComidities().size() > 0) {
                            mSingleCommodity = data.getComidities().get(0);
                            mSingleCommodity.setName(mVideoName);
                            if (mCommoditiesEntityList != null) {
                                mCommoditiesArrayObjectAdapter.addAll(0, mCommoditiesEntityList);
                                mCommoditiesArrayObjectAdapter.add(mSingleCommodity);
                            }
                            return;
                        }

                        if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE &&
                                data.getComidities() != null &&
                                data.getComidities().size() > 0) {
                            mSingleCommodity = data.getComidities().get(0);
                            freshQrcode(0, 0, mSingleCommodity.getCommodityEncrypt());
                        }
                        List<FilmCommodities.DataEntity.CateComoditiesEntity> list = data.getCate_comodities();
                        if (null != list && list.size() > 0) {
                            FilmCommodities.DataEntity.CateComoditiesEntity entity = list.get(0);
                            int price = entity.getPrice();
                            String result = "";
                            if (price % 100 == 0) {
                                result = price / 100 + "";
                            } else if (price % 10 == 0) {
                                result = ((float) (price / 10)) / 10 + "";
                            } else {
                                result = (float) price / 100 + "";
                            }
                            String text = result;
                            mSinglePrice.setText(text);
                            mSingleQrcodePrice.setText(text);
                            if (!mVideoPoster.trim().equals("")) {
//                                mSinglePoster.setImageRes(mVideoPoster);

                                GlideUrl glideUrl = new GlideUrl(mVideoPoster, new LazyHeaders.Builder()
                                        .addHeader("ImageTag", "PayActivity")
                                        .build());
                                Glide.with(PayActivity.this)
                                        .load(glideUrl)
                                        .transform(new RoundedCorners(getResources().getDimensionPixelOffset(R.dimen.x10)))
                                        .into(mSinglePoster);
                            }

                            if (mCategoryId != 21) { //教育类型没有别端权益
                                mSingleTips3.setText("可在TV+搜狐视频（手机/Pad/PC）多端观看");
                            }
                            mSingleTip.setText("观看有效期：" + entity.getPrivilegeAmount() + "天");

                        }
                    } else {
                        ToastUtils.showToast2(PayActivity.this, message);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("getCommodities()Error: " + e.getMessage(), e);
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("getCommodities(): onComplete()");
            }
        };
        NetworkApi.getCommodities(mCategoryId, mHelper.getIsLogin(), mAId, mVId, mHelper.getLoginPassport(), mHelper.getLoginToken(), disposableObserver);
        mCompositeDisposable.add(disposableObserver);
    }

    private void loadKey() {
        PayApi.getFilmCheckPermission(mHelper.getLoginPassport(),
                mHelper.getLoginToken(), mAId, mVId, 0, new Listener<PermissionCheck>() {
                    @Override
                    public void onSuccess(PermissionCheck permissionCheck) {
                        LibDeprecatedLogger.d("getFilmCheckPermission(): onSuccess = " + permissionCheck);
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.e("getFilmCheckPermission()Error: " + e.getMessage(), e);
                    }
                });
    }

    //    private volatile  DisposableObserver mPollingObserver;
    private String mPollingToken;
    private String mQrcode;
    Consumer<LoginQrModel> mLoadSucceedConsumer = new Consumer<LoginQrModel>() {
        @Override
        public void accept(LoginQrModel qrModel) {
            if (qrModel == null) {
                LibDeprecatedLogger.w("Qr model is null !");
                return;
            } else if (qrModel.getQrcode() == null) {
                LibDeprecatedLogger.w("Qrcode is null !");
                return;
            } else if (qrModel.getToken() == null) {
                mQrcode = qrModel.getQrcode();
                LibDeprecatedLogger.w("Token is null !");
                return;
            }
            mQrcode = qrModel.getQrcode();
            mPollingToken = qrModel.getToken();

        }
    };

    private void getCfgInfo() {
        LoginApi.getCfgInfo(new Listener<com.sohuott.tv.vod.account.login.ConfigInfo>() {
            @Override
            public void onSuccess(com.sohuott.tv.vod.account.login.ConfigInfo response) {
                LibDeprecatedLogger.d("getCfgInfo(): response = " + response);
                if (null != response) {
                    ConfigInfo.DataEntity data = response.getData();
                    String message = response.getMessage();
                    int status = response.getStatus();

                    if (status == 200 && null != data) {
                        mCarouselTime = data.getRtime();
                        sendEmptyMessageDelayed(MSG_CAROUSEL_USER_INFO, mCarouselTime);
                    } else {
                        ToastUtils.showToast2(PayActivity.this, message);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("getCfgInfo(): onError() = " + e.toString());
            }
        });
    }

    private void getUserInfo(final String qrcode, final int type, String passport, String token, long aid, long vid, long tvid) {
        PayApi.getCarouselLogin(this, qrcode, type, passport, token, aid, vid, tvid, new Listener<CarouselLogin>() {
            @Override
            public void onSuccess(CarouselLogin response) {
                LibDeprecatedLogger.d("getCarouselLogin(): response = " + response);
                boolean isVipMem = false;
                if (null != response) {
                    CarouselLogin.DataEntity data = response.getData();
                    int status = response.getStatus();

                    if (status == 200 && null != data) {
                        CarouselLogin.DataEntity.ScanResult scanResult = data.getScanResult();

                        if (null != scanResult) {
                            if (scanResult.getRenew() != null && scanResult.getRenew().equalsIgnoreCase("ok")) {
                                PostHelper.postScanSuccessEvent(0);
                            }
                            if (scanResult.getTpay() != null && scanResult.getTpay().equalsIgnoreCase("ok")) {
                                PostHelper.postScanSuccessEvent(1);
                            }

                        }

                        CarouselLogin.DataEntity.UserInfoEntity userInfo = data.getUserInfo();

                        if (null != userInfo) {
                            List<Login.LoginData.Privilege> privileges = userInfo.getPrivileges();
                            String ticketNumber = "";
                            if (null != userInfo.ticket
                                    && !TextUtils.isEmpty(userInfo.ticket.number)
                                    && !userInfo.ticket.number.trim().equals("null")) {
                                ticketNumber = Util.getTicketNumber(userInfo.ticket.number.trim());
                            }
                            final User user = userInfo.getSohuUser();

                            if (null != privileges && privileges.size() > 0) {
                                for (int i = 0; i < privileges.size(); i++) {
                                    Login.LoginData.Privilege privilege = privileges.get(i);
                                    long id = privilege.getId();
                                    long expireIn = privilege.getExpireIn();

                                    if (id == Constant.PRIVILEGE_ID_SOHU_MOVIE && expireIn > 0) {
                                        if (!privilege.getTime().equals(mHelper.getVipTime())) {
                                            if (mHelper.getIsLogin()) {
                                                isVipMem = true;
                                                if (mHelper.isVip()) {
                                                    ToastUtils.showToast2(PayActivity.this,
                                                            PayActivity.this.getApplicationContext().getResources().getString(R.string.txt_activity_pay_renew_success_tip));
                                                } else {
                                                    ToastUtils.showToast2(PayActivity.this,
                                                            PayActivity.this.getApplicationContext().getResources().getString(R.string.txt_activity_pay_member_success_tip));
                                                }
                                                HashMap<String, String> memoInfo = new HashMap<String, String>();
                                                memoInfo.put("single", "0");
                                                memoInfo.put("vip", "1");
                                                memoInfo.put("coupon", "0");
                                                memoInfo.put("login", "1");
                                                RequestManager.getInstance().onAllEvent(new EventInfo(10201, "slc"), pathInfo, null, memoInfo);
                                            }

                                            saveUserInfo(user, LoginUserInformationHelper.SUPER_VIP_MEMBER, privilege.getTime(), String.valueOf(expireIn));
                                            mUser = user;
                                            mHelper.putSohuUserInformation(user);
                                            sendEmptyMessage(MSG_SAVE_USER_INFO_TO_DB);
                                            PostHelper.postLoginSuccessEvent();
                                            if (!mHelper.getIsLogin()) {
//                                                PostHelper.postLoginSuccessEvent();
                                            } else {
                                                PostHelper.postRefreshUserEvent(PAGE_PAY);
                                                PostHelper.postPayEvent(0);
                                            }
                                            if (!TextUtils.isEmpty(ticketNumber)) {
                                                mHelper.putUserTicketNumber(ticketNumber);
                                                PostHelper.postTicketEvent();
                                            }
//                                            UserApi.getUserTicket(PayActivity.this);
//                                            UserApi.getUserLikeRank(PayActivity.this);
                                            loadCommodityData();
                                            break;
                                        }
                                    } else if (i == privileges.size() - 1 && mHelper.getVipTime().equals("0")) {
                                        saveNotVipInfo(user, ticketNumber);
                                    }
                                }
                            } else {
                                saveNotVipInfo(user, ticketNumber);
                            }
                        }

                        int stateInfo = data.getStateInfo();
                        LibDeprecatedLogger.e("stateInfo=" + stateInfo + "/isVipMem=" + isVipMem);
                        if (stateInfo == 1 && !isVipMem) {
                            if (mHelper.getIsSinglePurchase()) {
                                mHelper.putIsSinglePurchase(false);

                                loadKey();
                                HashMap<String, String> memoInfo = new HashMap<String, String>();
                                memoInfo.put("single", "1");
                                memoInfo.put("vip", "0");
                                memoInfo.put("coupon", "0");
                                memoInfo.put("login", "1");
                                RequestManager.getInstance().onAllEvent(new EventInfo(10201, "slc"), pathInfo, null, memoInfo);
                                ToastUtils.showToast2(PayActivity.this,
                                        PayActivity.this.getApplicationContext().getResources().getString(R.string.txt_activity_pay_single_purchase_success_tip));
                                PostHelper.postPayEvent(1);
                            }
                        }

                    } else if (status == 40010) {
                        LibDeprecatedLogger.d("二维码过期");
                        mHandler.removeMessages(MSG_CAROUSEL_USER_INFO);
                        if (mCommodityId != 0) {
                            freshQrcode(mCommodityId, mIsSign, mEncrypt);
                            getCfgInfo();
                        }

                    }
                }
            }

            @Override
            public void onError(Throwable e) {

            }
        });
    }

    private void setDefaultOperation() {
        mPayOperationImage.setBackgroundResource(R.drawable.bg_pay_operation);
    }

    private void enterTicketUseActivity() {
        if (mHelper.getIsLogin() && mHelper.isVip() && mEnterPayType == Constant.PAY_ENTER_TYPE_TICKET) {
            ActivityLauncher.startTicketUseActivity(PayActivity.this, mVideoName, mVideoPoster, mAId, mVId);
            finishActivity(true);
        }
    }

    // 保存用户信息
    private void saveUserInfo(User sohuUser, int vipStatus, String vipTime, String vipExpireIn) {
        if (null != mHelper) {
            mHelper.putUserGrade(vipStatus);// vip级别 - 0: 初级用户，1: 影视会员
            mHelper.putVipTime(vipTime);// vip会员到期时间
            mHelper.putVipExpireIn(vipExpireIn);// vip会员剩余时间
        }

        // 0: 初级用户，1: 影视会员
        sohuUser.setVipStatus(Integer.valueOf(vipStatus));
        sohuUser.setVipTime(vipTime);
        sohuUser.setVipExpireIn(vipExpireIn);
    }

    private void syncPlayHistory() {
        PlayHistoryService hisService = new PlayHistoryService(this);
        hisService.getAllPlayHistory(new PlayHistoryService.PlayHistoryListener() {

            @Override
            public void onSuccess(List<PlayHistory> playHistoryList) {
                PostHelper.postSyncPlayHistoryEvent();
            }

            @Override
            public void onFail(String reason, List<PlayHistory> playHistoryList) {
                PostHelper.postSyncPlayHistoryEvent();
            }
        });
        hisService.getAllEduHistory(null);
    }

    private void saveNotVipInfo(User user, String ticketNumber) {
        saveUserInfo(user, LoginUserInformationHelper.COMMON_USER, "0", "0");
        if (!mHelper.getIsLogin()) {
            PostHelper.postLoginSuccessEvent();
        }

        mUser = user;
        mHelper.putSohuUserInformation(user);
        sendEmptyMessage(MSG_SAVE_USER_INFO_TO_DB);
        if (!TextUtils.isEmpty(ticketNumber)) {
            mHelper.putUserTicketNumber(ticketNumber);
            PostHelper.postTicketEvent();
        }
//        UserApi.getUserTicket(this);

//        UserApi.getUserLikeRank(this);
        RequestManager.getInstance().updatePasspost(mHelper.getLoginPassport(), mHelper.getUserGrade() + "");
    }


    // 会员权益
    private void loadCommodityData() {
        StringBuffer url = new StringBuffer();
        url.append(String.format(UrlWrapper.getCommoditiesUrl(), mAId, mVId));
        if (mHelper.getIsLogin()) {
            url.append("&passport=" + mHelper.getLoginPassport());
            url.append("&token=" + mHelper.getLoginToken());
        }
        PayApi.postVideoDetailFilmCommodities(url.toString(), new Listener<VideoDetailFilmCommodities>() {
            @Override
            public void onSuccess(VideoDetailFilmCommodities response) {
                LibDeprecatedLogger.d("postVideoDetailFilmCommodities(): response = " + response);
                if (response != null
                        && response.data != null
                        && response.data.user_info != null
                        && response.data.user_info.expire_in != 0
                        && response.data.user_info.buy_status == 1
                        && response.data.user_info.buy_type != 3) {
                    finishActivity(true);
                } else {
                    enterTicketUseActivity();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("postVideoDetailFilmCommodities()Error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.vip_content_layout:
            case R.id.single_content_layout:
//                showAgreementWindow();
                break;
            case R.id.vip_activation:
                ActivityLauncher.startRenewActivity(this, RenewActivity.ACTYPE_EXCHANGE);

                RequestManager.getInstance().onAllEvent(new EventInfo(10199, "clk"), pathInfo, null, null);
                break;
            case R.id.vip_privacy_agreement:
//                mAgreementView.show(mAgreement);
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.PAY);
                RequestManager.getInstance().onAllEvent(new EventInfo(10200, "clk"), pathInfo, null, null);
                break;
            case R.id.pay_login_btn:
                ActivityLauncher.startLoginActivity(this);
                break;
            case R.id.pay_info:
                ActivityLauncher.startPayInfoActivity(this);
                break;
            default:
                break;
        }
    }

    //点击连续包月itemView展开
    private CustomItemBridgeAdapter createItemBridgeAdapter(ArrayObjectAdapter adapter) {
        return new CustomItemBridgeAdapter(adapter) {
            @Override
            public OnItemViewClickedListener getOnItemViewClickedListener() {
                return (focusView, itemViewHolder, item) -> {
                    if (item instanceof Commodity.DataEntity.CommoditiesEntity) {
                        if (((Commodity.DataEntity.CommoditiesEntity) item).isAgent()) {
                            mPayCover.setVisibility(View.GONE);
                            isEverClickedAutoSign = true;
                            if (itemViewHolder instanceof PayCommoditiesPresenter.ViewHolder) {
                                ((TextView) itemViewHolder.view.findViewById(R.id.pay_tip_auto_sign)).setText(mPayTipAutoSign);
                                ((TextView) itemViewHolder.view.findViewById(R.id.pay_commodity_desc)).setSingleLine(false);
                                markPriceRed(((TextView) itemViewHolder.view.findViewById(R.id.pay_commodity_desc)), (Commodity.DataEntity.CommoditiesEntity) item);
                                itemViewHolder.view.findViewById(R.id.line).setVisibility(View.VISIBLE);
                                //WRAPCONTENT展开
                                itemViewHolder.view.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
                            }
                        }
                    }
                };
            }
        };
    }

    //下拉时，标红价格
    private void markPriceRed(TextView descTextView, Commodity.DataEntity.CommoditiesEntity commoditiesEntity) {
        int startIndex = commoditiesEntity.getDescription().indexOf(String.valueOf(commoditiesEntity.getOriPrice() / 100));
        int endIndex = startIndex + String.valueOf(commoditiesEntity.getOriPrice() / 100).length();

        if (startIndex != -1) {
            // 获取原始描述
            String originalDescription = commoditiesEntity.getDescription();

            // 在指定位置前后添加空格
            String modifiedDescription = originalDescription.substring(0, startIndex) + " "
                    + originalDescription.substring(startIndex, endIndex) + " "
                    + originalDescription.substring(endIndex);

            // 更新 startIndex 和 endIndex 以反映添加的空格
            startIndex++;
            endIndex++;

            // 将 getOriPrice 在 getDescription 中的部分标为红色
            SpannableStringBuilder spannable = new SpannableStringBuilder(modifiedDescription);
            spannable.setSpan(new ForegroundColorSpan(Color.parseColor("#FA253D")), startIndex, endIndex, SpannableStringBuilder.SPAN_EXCLUSIVE_EXCLUSIVE);

            descTextView.setText(spannable);
        } else {
            descTextView.setText(commoditiesEntity.getDescription());
        }
    }

    private class MyHandler extends Handler {
        private WeakReference<PayActivity> activityReference;

        public MyHandler(PayActivity ref) {
            super();
            activityReference = new WeakReference<PayActivity>(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            PayActivity ref = activityReference.get();
            if (null == ref) {
                return;
            }

            int what = msg.what;
            switch (what) {
                case MEM_MSG_SCAN_SUCCESS:
                    break;

                case SINGLE_MSG_SCAN_SUCCESS:
                    break;

                case MSG_SCAN_FAILURE:
                    ref.mIsScanSuccess = false;
                    break;
                case MSG_LOGIN_SUCCESS:
//                    if (null != ref.mQrCodeScanSuccessTip2) {
//                        ref.mQrCodeScanSuccessTip2.setVisibility(View.VISIBLE);
//                    }
                    initData();
                    if (!isFullScreen()) {
                        getPrivilegeCommodities();
                    }

                    if (mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE || mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE) {
//                        mSinglePayTipText.setText(getApplicationContext().getResources().getString(R.string.txt_activity_pay_qrcode_title5));
                    } else if (mHelper.isVip()) {
//                        mPayTipText.setText(getApplicationContext().getResources().getString(R.string.txt_activity_pay_qrcode_title2));
                    }
//                    ref.mUnloginTips.setVisibility(View.GONE);
//                    ref.mUserContainer.setVisibility(View.VISIBLE);
                    break;
                case MSG_CAROUSEL_USER_INFO:
                    if (ref.mHelper.getIsLogin() && (ref.mEnterPayType == Constant.PAY_ENTER_TYPE_VIP_AND_SINGLE ||
                            ref.mEnterPayType == Constant.PAY_ENTER_TYPE_SINGLE)) {
                        getUserInfo(mQrcode, TYPE_MULTI, ref.mHelper.getLoginPassport(), ref.mHelper.getLoginToken(), ref.mAId, ref.mVId, 0);
                    } else {
                        if (!mIsPollingLogin || ref.mHelper.getIsLogin()) {
                            getUserInfo(mQrcode, TYPE_MULTI, ref.mHelper.getLoginPassport(), ref.mHelper.getLoginToken(), 0, 0, 0);
                        }
                    }

                    mHandler.removeMessages(MSG_CAROUSEL_USER_INFO);
                    sendEmptyMessageDelayed(MSG_CAROUSEL_USER_INFO, ref.mCarouselTime);
                    break;
                case MSG_SAVE_USER_INFO_TO_DB:
                    ref.mUserDao.insertOrReplace(ref.mUser);
                    break;
                case MSG_REFRESH_QRCODE:
                    int position = msg.arg1;
                    if (position == -1) {
                        return;
                    }
                    //请求对应二维码
                    if (mCommoditiesEntityList != null && mCommoditiesEntityList.size() > position) {
                        mCommodityId = mCommoditiesEntityList.get(position).getId();
                        mIsSign = mCommoditiesEntityList.get(position).isAgent() ? 1 : 0;
                        mEncrypt = mCommoditiesEntityList.get(position).getCommodityEncrypt();
                        if (mIsSign == 1) {
                            mAutoSignPosition = position;
                            if (!isEverClickedAutoSign) {
                                mPayCover.setVisibility(View.VISIBLE);
                            }
                        } else {
                            mPayCover.setVisibility(View.GONE);
                        }
                        freshQrcode(mCommodityId, mIsSign, mEncrypt);
                        mPayCommodityPrice.setText(Util.formatPrice(mCommoditiesEntityList.get(position).getPrice() / 100.0));
                    } else if (mSingleCommodity != null) {
                        mEncrypt = mSingleCommodity.getCommodityEncrypt();
                        freshQrcode(0, 0, mEncrypt);
                        mPayCommodityPrice.setText(Util.formatPrice(mSingleCommodity.getPrice() / 100.0));
                    }
                    break;
                default:
                    break;
            }
        }
    }

}
