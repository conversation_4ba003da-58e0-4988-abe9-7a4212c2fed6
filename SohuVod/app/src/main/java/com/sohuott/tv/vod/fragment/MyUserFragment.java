package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.activity.PayActivity;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.account.common.Listener;
import com.sohuott.tv.vod.account.login.Login;
import com.sohuott.tv.vod.account.user.UserApi;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.Logout;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.PostHelper;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohu.lib_utils.FormatUtils;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.view.ExitLoginDialog;
import com.sohu.lib_utils.StringUtil;

import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_MY;
import static com.sohuott.tv.vod.lib.utils.Constant.CHILD_SOURCE;
import static com.sohuott.tv.vod.lib.utils.Constant.PRIVILEGE_ID_SOHU_MOVIE;

import androidx.annotation.Nullable;

/**
 * Created by wenjingbian on 2017/5/12.
 */

public class MyUserFragment extends BaseFragment {

    private RelativeLayout mMyUserView;
    private LoadingView mLoadingView;

    private TextView tv_my_nickname, tv_my_validation_title, tv_my_validation_ctn, snm_account,
            tv_my_ticket_title, tv_my_ticket_ctn, tv_my_type, tv_my_freeze_ticket, tv_my_privilege, tv_my_login_method;
    private Button btn_my_buy, btn_my_login;
    private ImageView iv_my_golden_vip;

    private LoginUserInformationHelper mHelper;
    private int mSource;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View myView;

            myView = inflater.inflate(R.layout.fragment_my_user, container, false);
            mSource = Constant.HOME_SOURCE;
        initView(myView);
        RequestManager.getInstance().onMyUserExposureEvent();
        setSubPageName("6_my_user");
        return myView;
    }

    @Override
    public void onResume() {
        super.onResume();
        initData();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mHelper = null;
    }

    public void requestDefaultFocus() {
        if (btn_my_buy != null) {
            btn_my_buy.requestFocus();
        }
    }

    private void initView(View view) {
        mMyUserView = (RelativeLayout) view.findViewById(R.id.layout_my_user);
        mLoadingView = (LoadingView) view.findViewById(R.id.detail_loading_view);

        tv_my_nickname = (TextView) view.findViewById(R.id.tv_my_nickname);
        tv_my_validation_title = (TextView) view.findViewById(R.id.tv_my_validation_title);
        tv_my_validation_ctn = (TextView) view.findViewById(R.id.tv_my_validation_ctn);
        tv_my_ticket_title = (TextView) view.findViewById(R.id.tv_my_ticket_title);
        tv_my_ticket_ctn = (TextView) view.findViewById(R.id.tv_my_ticket_ctn);
        tv_my_type = (TextView) view.findViewById(R.id.tv_my_type);
        tv_my_freeze_ticket = (TextView) view.findViewById(R.id.tv_my_freeze_ticket);
        tv_my_privilege = (TextView) view.findViewById(R.id.tv_my_privilege);
        tv_my_login_method = (TextView) view.findViewById(R.id.tv_my_login_method);
        btn_my_buy = (Button) view.findViewById(R.id.btn_my_buy);
        btn_my_login = (Button) view.findViewById(R.id.btn_my_login);
        iv_my_golden_vip = (ImageView) view.findViewById(R.id.iv_my_golden_vip);
        snm_account = (TextView) view.findViewById(R.id.snm_account);
        snm_account.setText("牌照账号 : SNM_" + UserInfoHelper.getGid());
        MyClickListener myClickListener = new MyClickListener();
        btn_my_buy.setOnClickListener(myClickListener);
        btn_my_login.setOnClickListener(myClickListener);

        btn_my_buy.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                    btn_my_login.requestFocus();
                    return true;
                }
                return false;
            }
        });

        btn_my_login.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && event.getAction() == KeyEvent.ACTION_DOWN) {
                    if (getActivity() instanceof ListUserRelatedActivity) {
                        ((ListUserRelatedActivity) getActivity()).focusLeftItem(LIST_INDEX_MY);
                    }
                    return true;
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
                    btn_my_buy.requestFocus();
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                    return true;
                }
                return false;
            }
        });

        if (mSource == Constant.CHILD_SOURCE) {
            btn_my_buy.requestFocus();
        }
    }

    private void initData() {
        mHelper = LoginUserInformationHelper.getHelper(getContext());
        if (mHelper.getIsLogin()) {
            //display loading view
            mLoadingView.setVisibility(View.VISIBLE);
            mMyUserView.setVisibility(View.GONE);
            //request userInfo
            requestUserInfo();
        } else {
            displayUnloginView();
        }
    }

    private void requestUserInfo() {
        UserApi.refreshUser(getActivity(), new Listener<Login>() {
            @Override
            public void onSuccess(Login login) {
                LibDeprecatedLogger.d("requestUserInfo(): onNext().");
                if (mHelper == null) {
                    return;
                }
                if (login != null && login.getStatus() == 200 && login.getData() != null) {
                    Login.LoginData loginData = login.getData();
                    mHelper.putUserTicketNumber(loginData.getTicket() != null
                            ? Util.getTicketNumber(loginData.getTicket().getNumber()) : "0张");
                    List<Login.LoginData.Privilege> privilegeList = loginData.getPrivileges();
                    if (privilegeList != null && privilegeList.size() > 0) {
                        for (Login.LoginData.Privilege tmpPrivilege : privilegeList) {
                            if (tmpPrivilege != null && tmpPrivilege.getId() == PRIVILEGE_ID_SOHU_MOVIE
                                    && tmpPrivilege.getExpireIn() > 0) {
                                mHelper.putUserGrade(1);
                                if (StringUtil.isEmpty(mHelper.getUtype())) {
                                    mHelper.putUtype(loginData.getUTypeName());
                                }
                                mHelper.putVipTime(String.valueOf(tmpPrivilege.getTime()));
                                mHelper.putVipExpireIn(String.valueOf(tmpPrivilege.getExpireIn()));
                                displayVipView();
                                break;
                            }
                        }
                    }

                    if (!mHelper.isVip()) {
                        displayCommonView();
                    }
                } else {
                    loadLocalUserData();
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("requestUserInfo(): onError()--" + e.getMessage());
                loadLocalUserData();
            }
        });
    }

    private void loadLocalUserData() {
        if (isDetached()) {
            return;
        }
        ToastUtils.showToast(getContext(), "会员状态更新失败，请返回重试。");
        if (mHelper != null && mHelper.isVip()) {
            displayVipView();
        } else {
            displayCommonView();
        }
    }

    @Override
    public void onViewAttachedToWindow(View v) {
        super.onViewAttachedToWindow(v);
        if (mSource == CHILD_SOURCE) {
            requestDefaultFocus();
        }
    }

    private void displayVipView() {
        if (!isAdded()) {
            return;
        }
        mLoadingView.setVisibility(View.GONE);
        mMyUserView.setVisibility(View.VISIBLE);

        tv_my_nickname.setText(mHelper.getNickName());
        tv_my_type.setVisibility(View.GONE);
        tv_my_freeze_ticket.setVisibility(View.GONE);
        tv_my_validation_title.setVisibility(View.VISIBLE);
        tv_my_validation_ctn.setVisibility(View.VISIBLE);
        tv_my_validation_ctn.setText(FormatUtils.formatDate(Long.valueOf(mHelper.getVipTime())));
        tv_my_ticket_title.setVisibility(View.VISIBLE);
        tv_my_ticket_ctn.setVisibility(View.VISIBLE);
        tv_my_ticket_ctn.setText(mHelper.getUserTicketNumber());
        tv_my_login_method.setText(mHelper.getUtype());
        tv_my_privilege.setText(getResources().getString(R.string.txt_fragment_my_user_vip_title));
        btn_my_buy.setText(getResources().getString(R.string.txt_fragment_my_user_vip_btn));
        btn_my_login.setText(getResources().getString(R.string.txt_fragment_my_user_logout_btn));
        iv_my_golden_vip.setImageDrawable(getResources().getDrawable(R.drawable.ic_my_golden_vip_login));

        if (mSource == CHILD_SOURCE) {
            requestDefaultFocus();
        }
    }

    private void displayCommonView() {
        if (!isAdded()) {
            return;
        }
        mLoadingView.setVisibility(View.GONE);
        mMyUserView.setVisibility(View.VISIBLE);

        tv_my_nickname.setText(mHelper.getNickName());
        tv_my_type.setVisibility(View.VISIBLE);
        tv_my_type.setText(getResources().getString(R.string.txt_fragment_my_user_common_type));
        if (!TextUtils.isEmpty(mHelper.getUserTicketNumber()) && !TextUtils.equals(mHelper.getUserTicketNumber(), "0张")) {
            tv_my_freeze_ticket.setVisibility(View.VISIBLE);
            tv_my_freeze_ticket.setText("观影券：冻结券" + mHelper.getUserTicketNumber() + ",续费后解冻。");
        }
        tv_my_validation_title.setVisibility(View.GONE);
        tv_my_validation_ctn.setVisibility(View.GONE);
        tv_my_ticket_title.setVisibility(View.GONE);
        tv_my_ticket_ctn.setVisibility(View.GONE);
        tv_my_privilege.setText(getResources().getString(R.string.txt_fragment_my_user_common_title));
        tv_my_login_method.setText(mHelper.getUtype());
        btn_my_buy.setText(getResources().getString(R.string.txt_fragment_my_user_common_btn));
        btn_my_login.setText(getResources().getString(R.string.txt_fragment_my_user_logout_btn));
        iv_my_golden_vip.setImageDrawable(getResources().getDrawable(R.drawable.ic_my_golden_vip_unlogin));
    }

    private void displayUnloginView() {
        if (!isAdded()) {
            return;
        }
        mLoadingView.setVisibility(View.GONE);
        mMyUserView.setVisibility(View.VISIBLE);

        tv_my_nickname.setText("");
        tv_my_type.setVisibility(View.VISIBLE);
        tv_my_type.setText(getResources().getString(R.string.txt_fragment_my_user_login_msg));
        tv_my_freeze_ticket.setVisibility(View.GONE);
        tv_my_validation_title.setVisibility(View.GONE);
        tv_my_validation_ctn.setVisibility(View.GONE);
        tv_my_ticket_title.setVisibility(View.GONE);
        tv_my_ticket_ctn.setVisibility(View.GONE);
        tv_my_privilege.setText(getResources().getString(R.string.txt_fragment_my_user_common_title));
        tv_my_login_method.setText("");
        btn_my_buy.setText(getResources().getString(R.string.txt_fragment_my_user_common_btn));
        btn_my_login.setText(getResources().getString(R.string.txt_fragment_my_user_login_btn));
        iv_my_golden_vip.setImageDrawable(getResources().getDrawable(R.drawable.ic_my_golden_vip_unlogin));
    }

    private void requestLogout() {
        NetworkApi.getLogout(new Observer<Logout>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(Logout value) {
                LibDeprecatedLogger.d("requestLogout(): onNext()");
                if (getActivity() != null && (getActivity() instanceof ListUserRelatedActivity) && ((ListUserRelatedActivity) getActivity()).getLeftSelectedTag() != LIST_INDEX_MY) {
                    return;
                }

                if (mHelper != null && null != value) {
                    int status = value.getStatus();

                    if (status == 200) {
                        mHelper.clearLoginStatus();
                        PostHelper.postLogoutEvent();
                        displayUnloginView();
                        //clear cached data in SharedPreference.
                        mHelper.clearLoginStatus();
                        RequestManager.getInstance().updatePasspost(mHelper.getLoginPassport(), "0");
                    } else {
                        ToastUtils.showToast2(getActivity(), getResources().getString(R.string.txt_fragment_my_user_logout_fail));
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                if (isVisible()) {
                    LibDeprecatedLogger.e("requestLogout(): onError()--" + e.getMessage());
                    ToastUtils.showToast2(getActivity(), getResources().getString(R.string.txt_fragment_my_user_logout_fail));
                }
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("requestLogout(): onComplete()");
            }
        });
    }

    private void logOut() {
        ExitLoginDialog dialog = new ExitLoginDialog(this.getActivity(), new ExitLoginDialog.ExitListener() {
            @Override
            public void onExit(boolean isConfirm) {
                if (isConfirm) {
                    requestLogout();
                    RequestManager.getInstance().onMyUserLoginBtnClickEvent("Logout");
                }
            }
        });
        dialog.show();

    }

    private class MyClickListener implements View.OnClickListener {

        @Override
        public void onClick(View view) {
            if (view.getId() == R.id.btn_my_login) {
                if (mHelper != null && mHelper.getIsLogin()) {
                    logOut();
                } else {
                    ActivityLauncher.startLoginActivity(getContext(), mSource);
                    RequestManager.getInstance().onMyUserLoginBtnClickEvent("Login");
                }
            } else if (view.getId() == R.id.btn_my_buy) {

                ActivityLauncher.startPayActivity(getContext(), PayActivity.PAY_SOURCE_MY_YUE_TING_OPEN_VIP);
                if (mHelper != null && mHelper.isVip()) {
                    RequestManager.getInstance().onMyUserBuyBtnClickEvent("Renewal");
                } else {
                    RequestManager.getInstance().onMyUserBuyBtnClickEvent("Open");
                }
            }
        }
    }
}
