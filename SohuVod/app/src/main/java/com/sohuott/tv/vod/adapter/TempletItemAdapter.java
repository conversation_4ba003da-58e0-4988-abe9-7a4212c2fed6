package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.graphics.Color;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.GlideImageView;
import com.sohuott.tv.vod.widget.PlayingView;

import java.util.List;

/**
 * Created by music on 17-9-9.
 */

public class TempletItemAdapter extends RecyclerView.Adapter<TempletItemAdapter.TempletItemHolder> {

    private int mPage;//当前页
    private int mPageCount;//总页数
    private int mSelected = 0;//焦点走过的item的位置
    public int mPlayingSelected = -1;//正在播放的item的位置
    public int mLastPlayingSelected = -1;
    private int mPlayingPage = 0; //正在播放的page页数

    private boolean mFirst = true;
    private boolean mIsPgc = false;

    private Context mContext;

    private OnKeyChange onKeyChange;

    private CustomLinearRecyclerView customLinearRecyclerView;

    private FocusBorderView focusBorderView;

    private List<ListAlbumModel> mListData;

    public interface OnKeyChange{
        void keyChange(int direction, int page, boolean isNeedScroll);
        void focusChange(View view, boolean b);
        void onKeyRight(int page);
        void onVideoSelected(ListAlbumModel model, int page, int position);
        void hideLoading();
    }

    public void releaseAll() {
        if (mListData != null) {
            mListData.clear();
            mListData = null;
        }

        customLinearRecyclerView = null;
        focusBorderView = null;
        mContext = null;
    }


    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.focusBorderView = focusBorderView;
    }

    public void setCustomLinearRecyclerView(CustomLinearRecyclerView customLinearRecyclerView) {
        this.customLinearRecyclerView = customLinearRecyclerView;
    }

    public void setmListData(List<ListAlbumModel> mListData) {
        this.mListData = mListData;
        onKeyChange.hideLoading();
    }

    public void setOnKeyChange(OnKeyChange onKeyChange) {
        this.onKeyChange = onKeyChange;
    }

    public TempletItemAdapter(Context context, int page, int pageCount, boolean isPgc) {
        this.mContext = context;
        this.mPage = page;
        this.mPageCount = pageCount;
        this.mIsPgc = isPgc;
    }

    public void setmPlayingPage(int mPlayingPage) {
        this.mPlayingPage = mPlayingPage;
    }

    //设置焦点
    //如果当前有播放的item，优先滚到播放item上
    public void setSelected() {
        if (mPlayingSelected != -1) {//当前页有正在播放的item
            if (((CustomLinearLayoutManager)customLinearRecyclerView.getLayoutManager()).findFirstVisibleItemPosition() <= mPlayingSelected &&
                    ((CustomLinearLayoutManager)customLinearRecyclerView.getLayoutManager()).findLastVisibleItemPosition() >= mPlayingSelected) {
                customLinearRecyclerView.findViewHolderForAdapterPosition(mPlayingSelected).itemView.requestFocus();
            } else {
                if (customLinearRecyclerView.findViewHolderForAdapterPosition(mSelected) == null) {
                    customLinearRecyclerView.findViewHolderForAdapterPosition(((CustomLinearLayoutManager)customLinearRecyclerView.getLayoutManager()).findFirstVisibleItemPosition()).itemView.requestFocus();
                } else {
                    customLinearRecyclerView.findViewHolderForAdapterPosition(mSelected).itemView.requestFocus();
                }
            }
        } else {
            if (customLinearRecyclerView == null || customLinearRecyclerView.findViewHolderForAdapterPosition(mSelected) == null) {
                return;
            }
            customLinearRecyclerView.findViewHolderForAdapterPosition(mSelected).itemView.requestFocus();
        }
    }

    public void setSelected(int position) {
        mSelected = position;
    }

    public void clearSelected() {
        mSelected = 0;
//        mPlayingSelected = -1;
    }

    //获取正在播放的item的位置
    public int getPlayingItemPosition() {
        return mPlayingSelected;
    }

    private String converIntToString(int num) {
        if (num < 0) {
            return "";
        } else if (num < 10) {
            return "0" + num;
        } else {
            return String.valueOf(num);
        }
    }

    //寻找下一个可播放的item
    public void requestNextFocus(int position) {
        LibDeprecatedLogger.d("mPage" + mPage + "," + "position" + position);
        mSelected = position;
//        customLinearRecyclerView.findViewHolderForAdapterPosition(mSelected).itemView.requestFocus();
        onKeyChange.onVideoSelected(mListData.get(position),mPage,position);
        mLastPlayingSelected = mPlayingSelected;
        mPlayingSelected = position;
        setNotPlayingUI((TempletItemHolder) customLinearRecyclerView.findViewHolderForAdapterPosition(mLastPlayingSelected));
        setPlayingUI((TempletItemHolder)customLinearRecyclerView.findViewHolderForAdapterPosition(mPlayingSelected));
    }

    @Override
    public TempletItemHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (Util.getSystemModel().equals("MiTV")) {
            return new TempletItemHolder(LayoutInflater.from(mContext).inflate(R.layout.item_templet_videolist_xiaomi, parent, false));
        } else {
            return new TempletItemHolder(LayoutInflater.from(mContext).inflate(R.layout.item_templet_videolist, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(TempletItemHolder holder, int position) {
        if (mIsPgc) {
            holder.title.setText(mListData.get(position).videoTitle);

            holder.poster.setImageRes(mListData.get(position).smallCover);

        } else {
            holder.title.setText(mListData.get(position).tvName);
            holder.poster.setImageRes(mListData.get(position).tvHorBigPic);
        }

        holder.poster.setClearWhenDetached(false);


        if (mPage == 0 && position == 0 && mFirst) {
            onKeyChange.onVideoSelected(mListData.get(position), mPage,position);
            holder.itemView.requestFocus();
            mFirst = false;
            mPlayingSelected = 0;
            mPlayingPage = mPage;
            setPlayingUI(holder);
        } else {
            if (mPlayingPage == mPage && mPlayingSelected == position) {
                setPlayingUI(holder);
            } else {
                setNotPlayingUI(holder);
            }
        }

    }

    public void setPlayingUI(TempletItemHolder holder) {
        if (holder != null) {
            holder.title.setTextColor(Color.parseColor("#f1f1f1"));

            holder.playingView.show();
        }
    }

    public void setNotPlayingUI(TempletItemHolder holder) {
        if (holder != null) {
            holder.title.setTextColor(Color.parseColor("#c8c8c8"));

            holder.playingView.hide();
        }
    }


    @Override
    public int getItemCount() {
        return mListData == null ? 0 : mListData.size();
    }

    public class TempletItemHolder extends RecyclerView.ViewHolder{
        public TextView title;

        public PlayingView playingView;


        GlideImageView poster;

        public TempletItemHolder(final View itemView) {
            super(itemView);

            title = (TextView) itemView.findViewById(R.id.cs_item_title);
            poster = (GlideImageView) itemView.findViewById(R.id.item_poster);
            playingView = (PlayingView) itemView.findViewById(R.id.item_playing);

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View view, int i, KeyEvent keyEvent) {
                    if (keyEvent.getAction() == KeyEvent.ACTION_DOWN){
                        if (i == KeyEvent.KEYCODE_DPAD_UP){
                            if (getAdapterPosition() == 0) {
                                if (mPage == 0) {
                                    itemView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_y));
                                    return true;
                                }
                                onKeyChange.keyChange(1, mPage, true);
                            } else {
                                onKeyChange.keyChange(1, mPage, false);
                            }
                        } else if (i == KeyEvent.KEYCODE_DPAD_DOWN) {
                            if (getAdapterPosition() == getItemCount() -1) {
                                if (mPage == mPageCount) {
                                    itemView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.shake_y));
                                    return true;
                                }
                                onKeyChange.keyChange(0, mPage, true);
                            } else {
                                onKeyChange.keyChange(0, mPage, false);
                            }
                        } else if (i == KeyEvent.KEYCODE_DPAD_RIGHT) {
                            onKeyChange.onKeyRight(mPage);
                        }

                    }
                    return false;
                }
            });

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean hasFocus) {
                    mSelected = getAdapterPosition();
                    title.setSelected(hasFocus);
                    if (hasFocus) {
                        if (customLinearRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                            if (!Util.getSystemModel().equals("MiTV")) {
                                if (Util.getPartnerNo(mContext) != Constant.PARTNER_NO_FOR_TOUCH) {
                                    focusBorderView.setFocusView(itemView);
                                }
                            }

                        } else if (customLinearRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_SETTLING) {
                            if (!Util.getSystemModel().equals("MiTV")) {
                                if (Util.getPartnerNo(mContext) != Constant.PARTNER_NO_FOR_TOUCH) {
                                    if (getAdapterPosition() == 0) {
                                        focusBorderView.setFocusView(itemView);
                                    }
                                }
                            }
                        }
                        title.setMarqueeRepeatLimit(-1);
                        title.setEllipsize(TextUtils.TruncateAt.MARQUEE);

                    } else {
                        focusBorderView.setUnFocusView(itemView);
                        title.setEllipsize(TextUtils.TruncateAt.END);
                    }
                    LibDeprecatedLogger.d(hasFocus+"");
                }
            });

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    mLastPlayingSelected = mPlayingSelected;

                    if (mLastPlayingSelected != -1) {
                        if (((TempletItemHolder)customLinearRecyclerView.findViewHolderForAdapterPosition(mLastPlayingSelected)) != null) {

                            setNotPlayingUI(((TempletItemHolder)customLinearRecyclerView.findViewHolderForAdapterPosition(mLastPlayingSelected)));

                        }
                    }

                    mPlayingSelected = getAdapterPosition();


                    onKeyChange.onVideoSelected(mListData.get(getAdapterPosition()),mPage,getAdapterPosition());


                    setPlayingUI(((TempletItemHolder)customLinearRecyclerView.findViewHolderForAdapterPosition(mPlayingSelected)));
                }
            });
        }
    }
}