package com.sohuott.tv.vod.activity.setting.play

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.getResDrawable
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible

/**
 *
 * @Description
 * @date 2022/3/22 14:39
 * <AUTHOR>
 * @Version 1.0
 */
class SettingImgTipsItemPresenter(private val context: Context) :
    DefaultPresenter(R.layout.item_setting_img_tips_layout) {

    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as SettingItem
        if (item.type == SETTING_TYPE_ITEM_IMG_TIPS) {
            val name = viewHolder.getView<TextView>(R.id.tv_privacy_switch_name)
            val icon = viewHolder.getView<AppCompatImageView>(R.id.iv_privacy_switch)
            val layout = viewHolder.getView<ConstraintLayout>(R.id.layout_tips_privacy_switch)
            val imgTips = viewHolder.getView<AppCompatImageView>(R.id.iv_setting_img_tips)
            if (item.isShowImgTips) {
                imgTips.visible()
                imgTips.setImageDrawable(context?.getResDrawable(item.imgTipsResId))
            } else {
                imgTips.gone()
            }
            layout.isFocusable = item.focusable
            if (item.focusable) {
                layout.background = context.getResDrawable(R.drawable.login_item_bg_selector)
            } else {
                layout.background = context.getResDrawable(R.drawable.login_item_bg_no_have_focus)
            }
            name.text = item.content
            layout.setOnFocusChangeListener { v, hasFocus ->
                name.isSelected = hasFocus
                icon.isSelected = hasFocus
                if (hasFocus) {
                    v.animate().scaleX(1.1f).scaleY(1.1f).setDuration(300).start()
                } else {
                    v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(300).start()
                }
            }
            setShowIcon(icon, item.isSelected)
        }

    }

    private fun setShowIcon(icon: View, isSwitch: Boolean) {
        icon.visibility =
            if (isSwitch) View.VISIBLE else View.GONE

    }

}