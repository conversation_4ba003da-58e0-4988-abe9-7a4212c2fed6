package com.sohuott.tv.vod.videodetail.activity.repository

import GsonConverter
import androidx.lifecycle.scopeNetLife
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.activity.launcher.Message
import com.sohuott.tv.vod.activity.launcher.State
import com.sohuott.tv.vod.lib.model.VideoDetailFilmCommodities
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.UrlWrapper
import com.sohuott.tv.vod.videodetail.activity.PayInfoResponse
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.coroutineScope
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class PayRepository {

    suspend fun requestPayInfo(
        aid: Int? = 0,
        vid: Int ?= 0,
        cateCode: Int = 0,
        isLogin: Boolean = false,
        mUserLoginToken: String = "",
        passport: String = "",
    ): Deferred<VideoDetailFilmCommodities?> {
        return coroutineScope {
            var url = String.format(
                UrlWrapper.getCommoditiesUrl(),
                aid,
                vid
            )
            if (cateCode > 0) {
                url= url.plus("&cateCode=$cateCode")
            }
            if (isLogin) {
                url= url.plus("&passport=$passport")
                url= url.plus("&token=$mUserLoginToken")
            }
            Get<VideoDetailFilmCommodities?>(url) {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader(HeaderHelper.getCurlOpenKey(),"true")
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.MODERN_TLS,
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
            }
        }
    }

}