package com.sohuott.tv.vod.app;

import static com.xiaomi.mistatistic.sdk.MiStatInterface.UPLOAD_POLICY_REALTIME;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.Application;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.LinkProperties;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.os.Build;
import android.text.TextUtils;
import android.webkit.WebView;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lib_statistical.manager.RequestManager;
import com.sh.ott.video.monitor.ShMonitorManger;
import com.sh.ott.video.monitor.api.SohuMonitor;
import com.sohu.lib_utils.NetworkUtils;
import com.sohu.lib_utils.PrefUtil;
import com.sohu.lib_utils.XXTEA;
import com.sohu.ott.base.lib_user.UserApp;
import com.sohu.ott.base.lib_user.UserConfigHelper;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.BuildConfig;
import com.sohuott.tv.vod.account.login.LoginApi;
import com.sohuott.tv.vod.account.user.UserApi;
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.api.RetrofitApi;
import com.sohuott.tv.vod.lib.api.host.IRetrofitHost;
import com.sohuott.tv.vod.lib.api.host.NcRetrofitHost;
import com.sohuott.tv.vod.lib.base.ActivityManagerUtil;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AdvertisingCopyModel;
import com.sohuott.tv.vod.lib.model.PlayParams;
import com.sohuott.tv.vod.lib.model.ResetData;
import com.sohuott.tv.vod.lib.model.UlrChangeInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.service.PatchDownloadService;
import com.sohuott.tv.vod.utils.ActionUtil;
import com.sohuott.tv.vod.utils.SDKUtil;
import com.sohuott.tv.vod.videodetail.activity.VideoDetailExtKt;
import com.tencent.bugly.crashreport.CrashReport;
import com.xiaomi.mistatistic.sdk.MiStatInterface;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.gd.snmottclient.SNMOTTClient;
import cn.gd.snmottclient.util.SNMOTTSDKCallBack;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by fenglei on 16-10-12.
 */
public class SohuAppUtil {

    public static final boolean DEBUG_LOG_FILE = false;

    private static boolean IS_EXIT = false;


    public static void initApplication(Context context) {
        IS_EXIT = false;

        if (Util.isSameProcess(context, context.getPackageName())) {
            App.onCreate(context);
            initLogSdk(context);

//            AppLogger.init(context);
//            PlaySettingHelper.initContext(context);

//            Utils.INSTANCE.init((Application) context);


            // Set log by Jason
//            UserApp.INSTANCE.init(context);
//            UserInfoHelper.generateGID();
//            UserInfoHelper.setUa();
//            AppVodDatabaseManger.Companion.getInstance().init(context);
//            OkHttpClientInstance.init(context);
        }
    }


    public static void init(final Context context) {
        if (Util.isSameProcess(context, context.getPackageName())) {
            App.initCrash();
            App.initXCrash();
            changeUrl(context); // TODO 临时注释 待优化
            RetrofitApi.get().initHost(NcRetrofitHost.class);
            initSnmSdk((Application) context);// TODO 临时注释 待优化
            clearLoginUserData(context);
            getAdCopy(); // TODO 临时注释 待优化
            HomeData.getLocationConfigInfo(context); // TODO 临时注释 待优化
            initPlayer(context);

            // init xiaomi log update
            if (Util.getPartnerNo(context).equals(Constant.PARTNER_NO_XIAOMI_STORE_CHANNEL)) {
                MiStatInterface.initialize(context, "2882303761517411177", "5191741170177", Util.getPartnerNo(context));
                MiStatInterface.setUploadPolicy(UPLOAD_POLICY_REALTIME, 0);
            }
            startNetCheck(context);

            //修复长虹638 home键导致小窗播放，但画面全屏
//            if(!TextUtils.isEmpty(Build.ID)&&Build.ID.equals("LMY47V")&&
//                    Build.MODEL.equals("ChangHong Android TV")){
//                AppLogger.d("skipAd in the first play");
//                Util.setFirstSkipAd(context,true);
//            }
            if (LoginUserInformationHelper.getHelper(context).getIsLogin()) {
                UserApi.getUserAdPayMsg(context);
            }
//            initPlayerMonitor(context);
            App.onCreatePrivacy();
        }
    }

    private static void initSnmSdk(Application application) {

        SNMOTTClient.getInstance().initSdk(application,
                getSnmInitParams(application),
                new SNMOTTSDKCallBack() {
                    @Override
                    public void onSuccess(String s) {
                        // 初始化成功
                        LibDeprecatedLogger.d("初始化成功 : " + s);
//                        SouthMediaUtil.southNewMediaCheck(application);
                    }

                    @Override
                    public void onFailure(String s) {
                        // 初始化失败
                        LibDeprecatedLogger.d("初始化失败 : " + s);
//                        SouthMediaUtil.southNewMediaCheck(application);
                    }
                });

    }

    private static String getSnmInitParams(Context context) {
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        HashMap<String, String> params = new HashMap<>();

        params.put("appid", UserApp.Config.SNM_SDK_APP_ID);
        params.put("secretkey", UserApp.Config.SNM_SDK_SECRET_KEY);
        params.put("pt", "SNMYT");
        params.put("channel", Util.getPartnerNo(context));
        params.put("source", "snm_sohu");
        params.put("versionname", Util.getVersionName(context));
        params.put("versioncode", String.valueOf(Util.getVersionCode(context)));

        LibDeprecatedLogger.e("snm init params : " + gson.toJson(params));
        return gson.toJson(params);
    }

    private static void getAdCopy() {
        NetworkApi.getAdvertisingCopy(new DisposableObserver<AdvertisingCopyModel>() {
            @Override
            public void onNext(AdvertisingCopyModel value) {
                HomeData.setAdvertisingCopy(value);
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
    }

    private static HashMap<String, String> initLogHeaders(Context context) {
        HashMap<String, String> map = new HashMap<>();
        map.put("uid", UserInfoHelper.getGid());
        //产品号
        map.put("pro", "80");
        map.put("pro_type", "80");
        map.put("pro_form", "APK");
        map.put("channel_id", Util.getPartnerNo(context));
        if (App.getDebug()) {
            map.put("apk_type", "11");
        } else {
            map.put("apk_type", "10");
        }
        //平台号
        map.put("platform", String.valueOf(Util.getPlatformId(context)));
        map.put("cv", Util.getVersionName(context));
//        map.put("md", DeviceConstant.getDeviceMacAddress(context));
        map.put("md", "md_" + XXTEA.encryptToBase64String(UserInfoHelper.getUserDeviceMacAddress(), XXTEA.KEY.getBytes()));
        map.put("pn", Build.MANUFACTURER);
        map.put("startid", String.valueOf(System.currentTimeMillis()));
        map.put("passport", PrefUtil.getString("login_user_information", "loginPassport", ""));
        map.put("feetype", String.valueOf(PrefUtil.getInt("login_user_information", "userGrade", 0)));
        map.put("isonline", "true");
        map.put("tk", Util.getPlatformCode(context));
        map.put("licence_id", "SOUTH_MEDIA");
        return map;
    }

    public static void initLogSdk(final Context context) {
        // Initialize log sdk
        RequestManager.getInstance().init(context, App.getDebug(), Util.getPartnerNo(context));
        RequestManager.getInstance().setCommonHeader(initLogHeaders(context));

//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//                CrashReport.UserStrategy strategy = new CrashReport.UserStrategy(context);
//                strategy.setAppChannel(Util.getPartnerNo(context));  //设置渠道
//                strategy.setAppVersion(String.valueOf(Util.getVersionName(context)));      //App的版本
//                strategy.setAppPackageName(context.getPackageName());  //App的包名
//                CrashReport.initCrashReport(context, "900029252", false, strategy);
//                CrashReport.putUserData(context, "platformid",
//                        String.valueOf(Util.getPlatformId(context)));
//                CrashReport.setUserId(UserInfoHelper.getGid());
//            }
//        }).start();


    }

    private static void initPlayer(Context context) {
        // Player log by Jason
//        SDKUtil.onApplicationCreate(context);
//        OttHost.ottApi = UrlWrapper.AD_API_URL;
        initPlayerParams(context); // TODO 临时注释 待优化
//        SohuVideoPlayer.init(context);
//        DecSohuBinaryFile.dec2SBF(context, null);//todo 先注释掉
//        SdkFactory.getInstance().setDebugLogStatus(true);
//        SdkFactory.getInstance().prepare(context, UrlFactory.host, Util.getPartnerNo(context), Util.getProductKey(UrlWrapper.IS_CIBN));
//        AdTsManger.getInstants().initContext(context);
    }

    private static void initPlayerParams(final Context context) {
        NetworkApi.getPlayParams(getUa(context), new DisposableObserver<PlayParams>() {
            @Override
            public void onNext(PlayParams response) {
                if (response != null && response.status == 0) {
                    /**
                     * For ChangHong 628 and 638 By Jason
                     */
                    String deviceId = Build.ID == null ? "" : Build.ID;
                    String model = Build.MODEL == null ? "" : Build.MODEL;
                    if (model.contains("ChangHong Android TV") &&
                            (deviceId.contains("KOT49H") || deviceId.contains("LMY47V"))) {
//                        AppLogger.w("Change SOHU Player! TEMP!");
//                        response.data = 0;
                    } else if (deviceId.contains("KTU84M")) {
                        // For ChangHong 5508
                        LibDeprecatedLogger.w("Change System Player! TEMP!");
                        // response.data = 1;
                    }
                    LibDeprecatedLogger.d("Player choice" + response.data);
                    PlaySettingHelper.setPlayType(response.data);
                    Util.setDefaultPlayParams(context, response.data);
                    if (!PlaySettingHelper.getPlayTypeHasChange()) {
                        Util.setPlayParams(context, response.data);
                    }
                    VideoDetailExtKt.setPlayerChange(context, Util.getPlayParams(context));
//                    SoHuApplication.setPlayerFactory(Util.getPlayParams(context));
//                    ShPlayerConfig.filmPlayerFactory=SystemPlayerFactory.create()
//                    PlaySettingHelper.setPlaySpeedIsOpen(Util.getPlayParams(context) == 0);
                    if (response.extend != null) {
                        LibDeprecatedLogger.d("Player extend data(h265,dts,huaping,adFlag):" +
                                response.extend.h265 + "," + response.extend.dts + "," + response.extend.huaping + "," + response.extend.adFlag);
                        Util.setDefaultH265(context, response.extend.h265);
                        if (!PlaySettingHelper.getH265HasChange()) {
                            Util.setH265(context, response.extend.h265);
                        }
                        UserConfigHelper.setUserDrmPlayParams(response.extend.filterDRM);
                        Util.setH265Vers(context, response.extend.h265Vers);
                        Util.setDtsParams(context, response.extend.dts);
                        Util.setHuapingParams(context, response.extend.huaping);
                        Util.setADFlag(response.extend.adFlag);
                        Util.setHttpsParams(context, response.extend.https);
                        Util.setCourseParams(context, response.extend.isShowBuyCourse);
                    }

                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("getPlayParams(): onError() = " + e.toString());
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("getPlayParams(): onComplete()");
            }
        });
    }

    private static String getUa(Context context) {
        String ua = "";
        //todo 替换方案  可以减少1秒耗时
        try {
            WebView webview = new WebView(context);
            ua = webview.getSettings().getUserAgentString();
        } catch (Exception e) {
            LibDeprecatedLogger.e("Get Web info fail !", e);
        }
        return ua == null ? "" : ua;
    }


    public static void clearLoginUserData(Context context) {
        if (Util.isFirstEnterApp(context) && NetworkUtils.checkNetwork(context)) {
            resetData(context);
        }
    }

    private static void resetData(final Context context) {
        NetworkApi.getResetData(new DisposableObserver<ResetData>() {
            @Override
            public void onNext(ResetData response) {
                LibDeprecatedLogger.d("getResetData(): response = " + response);
                if (null != response) {
                    int status = response.getStatus();

                    if (status == 200) {
                        Util.setFirstEnterApp(context, false);
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.d("getResetData(): onError() = " + e.toString());
            }

            @Override
            public void onComplete() {
                LibDeprecatedLogger.d("getResetData(): onComplete()");
            }
        });
    }


    public static void exitApp(Context context) {
        if (IS_EXIT) {
            LibDeprecatedLogger.w("Stopping!!! Waitting!!!");
            return;
        }
        IS_EXIT = true;
        SDKUtil.onApplicationDestroy();
        stopLogSdk(context);
//        SohuMonitorManger.getInstance().destroy();
        ActivityManagerUtil.clearActivityList();
        Intent intent = new Intent(context, PatchDownloadService.class);
        PendingIntent pendingIntent = PendingIntent.getService(
                context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        AlarmManager am = (AlarmManager) context.getSystemService(Activity.ALARM_SERVICE);
        am.cancel(pendingIntent);
        killProcess(context);
    }


    /**
     * Kill all process about my APP. Running this, when exit or home out.
     *
     * @param context
     */
    private static void killProcess(Context context) {
        LibDeprecatedLogger.d("Ready to kill all process!");
        /**
         * 暂时无法进行延迟处理，在开始exit操作后没有进行kill进程前，
         * 重新进入app无法重新开启已退出的sdk等任务(无法判断哪些已经执行完)
         */

        handlerKill(context);
    }

    private static void handlerKill(Context context) {
        LibDeprecatedLogger.d("Start to kill all process!");
        ActivityManager activityManager = (ActivityManager) context.getSystemService(context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        if (appProcesses != null && !appProcesses.isEmpty()) {
            for (ActivityManager.RunningAppProcessInfo processInfo : appProcesses) {
                if (null != processInfo && null != processInfo.processName
                        && processInfo.processName.contains("com.sohuott.tv.vod:")) {
                    LibDeprecatedLogger.d("Kill process: " + processInfo.processName);
                    android.os.Process.killProcess(processInfo.pid);
                }
            }
        }
        android.os.Process.killProcess(android.os.Process.myPid());
    }




    public static void stopLogSdk(Context context) {
        LibDeprecatedLogger.d("Stop logSDK and release it!");
        if (RequestManager.getInstance() != null) {
            RequestManager.getInstance().stop();
        }

    }


    private static void startNetCheck(final Context context) {

        if (Build.VERSION.SDK_INT >= 24) {
            LibDeprecatedLogger.d("startNetCheck");
            final ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

            connectivityManager.requestNetwork(new NetworkRequest.Builder().build(), new ConnectivityManager.NetworkCallback() {
                @Override
                public void onAvailable(Network network) {
                    super.onAvailable(network);
                    LibDeprecatedLogger.d("onAvailable");
                    if (null != context) {
                        Intent broadcast = new Intent(ActionUtil.ACTION_NET_CHANGED);
                        context.sendBroadcast(broadcast);
                    }

                }

                @Override
                public void onLost(Network network) {
                    super.onLost(network);
                    LibDeprecatedLogger.d("onLost");
                    if (null != context) {
                        Intent broadcast = new Intent(ActionUtil.ACTION_NET_CHANGED);
                        context.sendBroadcast(broadcast);
                    }
                }

                @Override
                public void onLosing(Network network, int maxMsToLive) {
                    super.onLosing(network, maxMsToLive);
                    LibDeprecatedLogger.d("onLosing");
                    if (null != context) {
                        Intent broadcast = new Intent(ActionUtil.ACTION_NET_CHANGED);
                        context.sendBroadcast(broadcast);
                    }
                }

                @Override
                public void onLinkPropertiesChanged(Network network, LinkProperties linkProperties) {
                    super.onLinkPropertiesChanged(network, linkProperties);
                    LibDeprecatedLogger.d("onLinkPropertiesChanged");
                }

                @Override
                public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
                    super.onCapabilitiesChanged(network, networkCapabilities);
                    LibDeprecatedLogger.d("onCapabilitiesChanged");
                }
            });
        }

    }

    /**
     * Open or Close App Log
     *
     * @param mode Open or Close
     */
    public static void changeLogMode(boolean mode) {
        AppLogger.setDebug(mode);
    }


    /**
     * 更改baseUrl
     */
    private static void changeUrl(Context context) {
        NetworkApi.getUrlChangeinfo(new DisposableObserver<UlrChangeInfo>() {
            @Override
            public void onNext(UlrChangeInfo value) {
                Map<String, Object> urlMap = value.getData();
                if (urlMap == null || urlMap.isEmpty()) {
                    getGid(context);
                    return;
                }
                if (!((Boolean) urlMap.get("is_change"))) {
                    getGid(context);
                    return;
                }
                IRetrofitHost host = RetrofitApi.get().getRetrofitHost();
                String baseHost = (String) urlMap.get(host.getBaseHost());
                if (baseHost != null) {
                    host.setBaseHost(baseHost);
                }
                String PassportHost = (String) urlMap.get(host.getPassportHost());
                if (PassportHost != null) {
                    host.setPassportHost(PassportHost);
                }

                String PassportPollingHost = (String) urlMap.get(host.getPassportPollingHost());
                if (PassportPollingHost != null) {
                    host.setPassportPollingHost(PassportPollingHost);
                }

                String PassportPollingTokenHost = (String) urlMap.get(host.getPassportPollingTokenHost());
                if (PassportPollingTokenHost != null) {
                    host.setPassportPollingTokenHost(PassportPollingTokenHost);

                }

                String UserHost = (String) urlMap.get(host.getUserHost());
                if (UserHost != null) {
                    host.setUserHost(UserHost);

                }

                String PlayerHost = (String) urlMap.get(host.getPlayerHost());
                if (PlayerHost != null) {
                    host.setPlayerHost(PlayerHost);
                }
                getGid(context);
            }

            @Override
            public void onError(Throwable e) {
                AppLogger.INSTANCE.e(e.toString());
                getGid(context);
            }

            @Override
            public void onComplete() {

            }
        });
    }

    private static void getGid(Context context) {
        if (TextUtils.isEmpty(UserInfoHelper.getPgid())) {
            LoginApi.getPassportGid(context, null);
        }
    }


    public static void initPlayerMonitor(Context mContext) {
        ShMonitorManger.getInstance().initMonitor(mContext, BuildConfig.VERSION_NAME, BuildConfig.FLAVOR, "2E53FC84F749D7F07FCC2A52C2547395");
        //暂时使用这种关闭
    }

    public static void destroyPlayerMonitor() {
//        SohuMonitorManger.getInstance().destroy();
    }
}

