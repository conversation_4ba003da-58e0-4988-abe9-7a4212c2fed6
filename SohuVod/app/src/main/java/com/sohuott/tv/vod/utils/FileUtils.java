package com.sohuott.tv.vod.utils;

import android.os.Environment;
import android.text.TextUtils;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.task.IDownloadListener;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.SocketException;

import okhttp3.OkHttpClient;
import okhttp3.Request;

/**
 * Created by <PERSON> on 2017/5/19.
 */

public class FileUtils {

    public static final String TAG = "FileUtils";

    public static final int REQUEST_TIMEOUT = 5000;

    public static final int HTTP_OK = 200;

    /**
     * BKDRHash for file URL
     *
     * @param url
     * @return
     */
    public static String toBKDRHash(String url) {
        if (url == null) {
            return null;
        }
        int seed = 131;
        int hash = 0;
        int urlSize = url.length();
        for (int i = 0; i < urlSize; i++) {
            hash = (hash * seed) + url.charAt(i);
        }
        int result = hash & 0x7FFFFFFF;
        return result + "";
    }

    /**
     * Download file from net. If fileSuffix is null, don't rename file after download it.
     *
     * @param progressListener Download progress
     * @param urlStr
     * @param localFile        Local storage File.
     * @param fileSuffix       The file suffix
     * @return Result of download
     */
    public static boolean download(IDownloadListener progressListener, String urlStr, File localFile, String fileSuffix) {
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            if (localFile.exists()) {
                localFile.delete();
            }
            localFile.createNewFile();

            Request request = new Request.Builder().url(urlStr).build();
            OkHttpClient client = new OkHttpClient();
            okhttp3.Response response = client.newCall(request).execute();

            int responseCode = response.code();
            long totalSize = response.body().contentLength();
            AppLogger.d(TAG, "Download request: " + urlStr);
            AppLogger.d(TAG, "Download http response code: " + responseCode);
            AppLogger.d(TAG, "Download http response size: " + totalSize);
            if (progressListener != null) {
                progressListener.setNetworkResponseCode(responseCode);
            }
            if (responseCode == HTTP_OK && totalSize > 0) {
                inputStream = response.body().byteStream();
                outputStream = new FileOutputStream(localFile);
                int bufferLength;
                int downloadedSize = 0;
                byte[] buffer = new byte[1024];
                while ((bufferLength = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, bufferLength);
                    downloadedSize += bufferLength;
                    if (progressListener != null) {
                        progressListener.updateProgress((int) ((double) downloadedSize / totalSize * 100.0));
                    }
                }
                if (downloadedSize == totalSize) {
                    return TextUtils.isEmpty(fileSuffix) ? true : rename(localFile, fileSuffix);
//                    if (!TextUtils.isEmpty(fileSuffix)) {
//                        File file = new File(localPath + fileSuffix);
//                        downloadFile.renameTo(file);
//                    }
//                    return true;
                }
            }
            response.close();
        } catch (SocketException e) {
            AppLogger.e(TAG, "Timeout when connect to network: " + e);
        } catch (Exception e) {
            AppLogger.e(TAG, "Exception during downloadApkFile(): " + e);
        } finally {
            try {
                if (inputStream != null)
                    inputStream.close();
                if (outputStream != null)
                    outputStream.close();
            } catch (IOException e) {
                AppLogger.e(TAG, "Exception during closing stream, " + e);
            }
        }
        return false;
    }

    /**
     * Check the validity of the SD card
     *
     * @param relativePath
     * @return If there is not sd, return null; If this sd is invalid, return null. Otherwise return storage directory.
     */
    public static File isExistSD(String relativePath) {
        boolean isExist = Environment.getExternalStorageState().equals(android.os.Environment.MEDIA_MOUNTED);
        if (!isExist) {
            AppLogger.w(TAG, "There is not sd !");
            return null;
        }
        relativePath = relativePath == null ? "" : File.separator + relativePath;
        String localDir = Environment.getExternalStorageDirectory().getAbsolutePath() + relativePath;
        File fileDir = new File(localDir);
        if (!fileDir.exists() && !fileDir.isDirectory()) {
            fileDir.mkdir();
            AppLogger.d(TAG, localDir + "  is created !");
        }
        return fileDir;
    }

    /**
     * Check the free space for device storage.
     *
     * @return Returns the number of usable free bytes on the SD Card.
     */
    public static long spaceScale() {
        boolean isExist = Environment.getExternalStorageState().equals(android.os.Environment.MEDIA_MOUNTED);
        if (!isExist) {
            AppLogger.w(TAG, "There is not sd !");
            return 0;
        }
        long usableSpace = Environment.getExternalStorageDirectory().getUsableSpace();
        return usableSpace;
    }

    /**
     * Add suffix for local file.
     *
     * @param localFile
     * @param fileSuffix
     * @return
     */
    public static boolean rename(File localFile, String fileSuffix) {
        if (localFile.exists() && !TextUtils.isEmpty(fileSuffix)) {
            String fileName = localFile.getAbsolutePath() + "." + fileSuffix;
            File newFile = new File(fileName);
            if (newFile.isFile() && newFile.exists()) {
                newFile.delete();
            }
            return localFile.renameTo(newFile);
        }
        AppLogger.w(TAG, "File rename fail !");
        return false;
    }

    public static final String MP4SUFFIX = ".mp4";

    /**
     * Clean old resource on the storage
     *
     * @param dirFile
     * @param oldTime
     */
    public static void deleteOldResource(File dirFile, long oldTime) {
        if (dirFile.exists()) {
            File[] files = dirFile.listFiles();
            long currentTime = System.currentTimeMillis();
            int count = 0;
            for (File file : files) {
                long lastModifiedTime = file.lastModified();
                if (currentTime - lastModifiedTime > oldTime) {
                    if (file.delete()) {
                        count++;
                    }
                }
            }
            AppLogger.d(TAG, "Clean old resource size: " + count);
        }
    }

}
