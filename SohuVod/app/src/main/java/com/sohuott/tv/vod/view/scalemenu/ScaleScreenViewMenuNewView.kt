package com.sohuott.tv.vod.view.scalemenu

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.FocusFinder
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.DiffCallback
import androidx.leanback.widget.ItemBridgeAdapter
import com.lib_dlna_core.SohuDlnaManger
import com.lib_statistical.CLICK
import com.lib_statistical.IMP
import com.lib_statistical.addPushEvent
import com.lib_statistical.getInfoEvent
import com.lib_viewbind_ext.viewBinding
import com.sh.ott.video.player.PlayerConstants

import com.sohu.ott.base.lib_user.UserConstants
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper.setClarityHasChange
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger
import com.sohuott.tv.vod.databinding.LayoutScaleMenuNewViewBinding
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper
import com.sohuott.tv.vod.lib.utils.ToastUtils
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.view.FocusBorderView
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentClarityMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentMoreMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentSpeedMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleMenuItem
import com.sohuott.tv.vod.view.scalemenu.presenter.*

/**
 * 全屏播控菜单
 */
class ScaleScreenViewMenuNewView constructor(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int
) :
    ConstraintLayout(context, attrs, defStyleAttr) {

    private lateinit var mScaleMenuViewPresenter: ScaleMenuViewPresenter
    private val mViewBinding: LayoutScaleMenuNewViewBinding by viewBinding(
        LayoutScaleMenuNewViewBinding::bind
    )

    private var mArrayAdapter: ArrayObjectAdapter? = null
    private var mAdapter: ItemBridgeAdapter? = null
    private var mMenuContentArrayAdapter: ArrayObjectAdapter? = null
    private var mMenuContentAdapter: ItemBridgeAdapter? = null

    private var mListener: ScaleNewMenuViewClickListener? = null

    private var mMenuItemData = mutableListOf<ScaleMenuItem>()
    private var mScaleContentSpeedMenuItem = mutableListOf<ScaleContentSpeedMenuItem>()
    private var mScaleContentMoreMenuItem = mutableListOf<ScaleContentMoreMenuItem>()
    private var mScaleContentClarityMenuItem = mutableListOf<ScaleContentClarityMenuItem>()
    private var mScaleContentOnlySeeMenuItem = mutableListOf<ScaleContentOnlySeeMenuItem>()

    private var mLoginHelper = LoginUserInformationHelper.getHelper(context.applicationContext);

    private var mClarityCash = mutableMapOf<Int, Int>()
    private var mCurrentClarity = -1;

    private var mAutoClarity = -1;

    public var videoOrientation = 0

    private var haveOnlySeeMenuItem = false

    var selectItemName = MENU_ITEM_EPISODE

    var oldPosition = 0

    /**
     * 此变量是为了防止 选择菜单时setOnChildSelectedListener 方法调用两次 导致曝光上报两次问题
     */
    var hasSelectPushImpEvent = false

    //剧集类型 0:VRS 2:PGC
    var dataType = Constant.DATA_TYPE_VRS


    constructor(context: Context) : this(context, null, 0)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    init {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.layout_scale_menu_new_view, this, true)
        isFocusable = false
        isEnabled = false
        initMenuData()
        initMenuAdapter()
        updateMenuData()
        initContentMenuAdapter()
    }

    /**
     * 是否展示只看他
     */
    fun updateHaveOnlySee(haveOnlySeeMenuItem: Boolean) {

        this.haveOnlySeeMenuItem = haveOnlySeeMenuItem
        if (SohuDlnaManger.getInstance().getIsDlna()) {
            this.haveOnlySeeMenuItem = false
        }
        initMenuData()
    }

    fun getIsUseOnlySeeModel(): Boolean {
        return isUseOnlySeeModel
    }

    fun setIsUseOnlySeeModel(isUseOnlySeeModel: Boolean) {
        this.isUseOnlySeeModel = isUseOnlySeeModel
    }


    fun setCurrentOnlySeeImg(url: MutableList<String>?) {
        onlySeeNameImgUrl = url
    }


    /**
     * 初始化菜单数据
     */
    fun initMenuData() {
        selectItemName = MENU_ITEM_EPISODE
        if (mMenuItemData.isNotEmpty()) {
//            oldPosition = mViewBinding.leanbackScaleMenuView.selectedPosition
            //indexOutOfBound
            selectItemName = mMenuItemData[if (oldPosition == -1|| oldPosition >= mMenuItemData.size) 0 else oldPosition].name
                ?: MENU_ITEM_EPISODE
        }
        mMenuItemData.clear()
        mMenuItemData.add(
            ScaleMenuItem(
                MENU_ITEM_EPISODE,
                null,
                TextUtils.equals(MENU_ITEM_EPISODE, selectItemName)
            )
        )
        mMenuItemData.add(
            ScaleMenuItem(
                MENU_ITEM_CLARITY,
                null,
                TextUtils.equals(MENU_ITEM_CLARITY, selectItemName)
            )
        )
        //当 系统播放器时不展示
        if (PlaySettingHelper.getPlaySpeedIsOpen() && Util.getPlayParams(context) == 0) {
            mMenuItemData.add(
                ScaleMenuItem(
                    MENU_ITEM_SPEED,
                    null,
                    TextUtils.equals(MENU_ITEM_SPEED, selectItemName)
                )
            )
        }
        if (haveOnlySeeMenuItem) {
            //只看他
            mMenuItemData.add(
                ScaleMenuItem(
                    MENU_ITEM_ONLY_SEE,
                    if (isUseOnlySeeModel) onlySeeNameImgUrl else null,
                    TextUtils.equals(MENU_ITEM_ONLY_SEE, selectItemName)
                )
            )
        }

        if (Util.getPlayParams(context) == 0) {
            mMenuItemData.add(
                ScaleMenuItem(
                    MENU_ITEM_MORE, null, TextUtils.equals(MENU_ITEM_MORE, selectItemName)
                )
            )
        }
        updateMenuData()
    }

    private var onlySeeNameImgUrl: MutableList<String>? = null

    /**
     * 是否是只看TA模式
     */
    var isUseOnlySeeModel = false

    /**
     * 开启只看TA的具体名称 刷新presenter使用
     */
    private var onlySeeName = "-1"

    /**
     * 获取只看TA的标签名称
     */
    private fun getOnlySeeItTabName(): String {
        return " 只看TA"
    }

    private fun updateMenuData() {
        mArrayAdapter?.setItems(mMenuItemData, object : DiffCallback<ScaleMenuItem>() {
            override fun areItemsTheSame(oldItem: ScaleMenuItem, newItem: ScaleMenuItem): Boolean {
                AppLogger.i(

                    " areItemsTheSame fun oldItem is :$oldItem  \n newItem is :$newItem"
                )
                return TextUtils.equals(
                    oldItem.name,
                    newItem.name
                )
            }

            override fun areContentsTheSame(
                oldItem: ScaleMenuItem,
                newItem: ScaleMenuItem
            ): Boolean {
                AppLogger.i(
                    "ScaleMenu areContentsTheSame fun oldItem is :$oldItem  \n newItem is :$newItem"
                )
                return oldItem.hasSelect == newItem.hasSelect && oldItem.imagesUrl.toString() == newItem.imagesUrl.toString()
            }
        })
    }

    public fun setStopViewShow() {
        isFocusable = false
    }


    /**
     * 初始化菜单adapter
     */
    private fun initMenuAdapter() {
        mScaleMenuViewPresenter = ScaleMenuViewPresenter(context = context)
        mArrayAdapter = ArrayObjectAdapter(mScaleMenuViewPresenter)
        mAdapter = ItemBridgeAdapter(mArrayAdapter)
        mViewBinding.leanbackScaleMenuView.adapter = mAdapter
//        mViewBinding.leanbackScaleMenuView.horizontalSpacing =
//            resources.getDimension(R.dimen.x60).toInt()
        mViewBinding.leanbackScaleMenuView.setOnChildSelectedListener { parent, view, position, id ->
            if (!hasClear) {
                mMenuContentArrayAdapter?.clear()
            }
            mViewBinding.leanbackScaleContentMenuView.gone()
            mListener?.onSelectItemPosition(position)
            oldPosition = position
            mViewBinding.leanbackScaleMenuView.post {
                initMenuData()
            }
            updateSelectItem(position, true)
//            val item = mArrayAdapter?.get(position) as ScaleMenuItem
//            when (item.name) {
//                "选集" -> {
//                    initMenuData("选集")
//                    if (!isFocusable) {
//                        return@setOnChildSe
            //
            //                        lectedListener
//                    }
//                    mViewBinding.leanbackScaleContentMenuView.gone()
//                    mViewBinding.episodeLayout.visible()
//                }
//                //清晰度
//                "清晰度" -> {
//
//                    initMenuData("清晰度")
//                    if (!isFocusable) {
//                        return@setOnChildSelectedListener
//                    }
//                    mViewBinding.episodeLayout.gone()
//                    mViewBinding.leanbackScaleContentMenuView.visible()
//                    updateSpeedData()
//                }
//                //倍速
//                "倍速" -> {
////                    mViewBinding.leanbackScaleContentMenuHeaderView.gone()
//                    initMenuData("倍速")
//                    if (!isFocusable) {
//                        return@setOnChildSelectedListener
//                    }
//                    mViewBinding.episodeLayout.gone()
//                    mViewBinding.leanbackScaleContentMenuView.visible()
//                    updateClarity()
//                }
//                //更多功能
//                "更多功能" -> {
////                    mViewBinding.leanbackScaleContentMenuHeaderView.gone()
//                    initMenuData("更多功能")
//                    if (!isFocusable) {
//                        return@setOnChildSelectedListener
//                    }
//                    mViewBinding.episodeLayout.gone()
//                    mViewBinding.leanbackScaleContentMenuView.visible()
//                    updateMore()
//                }
//            }
        }
    }

    /**
     * 初始化选择菜单的具体功能adapter
     */
    private fun initContentMenuAdapter() {
        mMenuContentArrayAdapter = ArrayObjectAdapter(ScaleContentMenuSelector(context))
        mMenuContentAdapter = ItemBridgeAdapter(mMenuContentArrayAdapter)
        mViewBinding.leanbackScaleContentMenuView.adapter = mMenuContentAdapter
        mViewBinding.leanbackScaleContentMenuView.horizontalSpacing =
            resources.getDimension(R.dimen.x18).toInt()

        mMenuContentAdapter?.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
            override fun onAttachedToWindow(viewHolder: ItemBridgeAdapter.ViewHolder?) {
                super.onAttachedToWindow(viewHolder)
                LibDeprecatedLogger.i("mMenuContentAdapter setAdapterListener onAttachedToWindow fun")
            }

            override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder?) {
                super.onCreate(viewHolder)
                when (viewHolder?.presenter) {
                    //清晰度
                    is ScaleContentMenuSpeedPresenter -> {
                        viewHolder.itemView.findViewById<ConstraintLayout>(R.id.cl_scale_menu_speed)
                            .setOnClickListener {
                                val item = viewHolder.item
                                item as ScaleContentSpeedMenuItem
                                item.content?.let {
                                    mListener?.onClickSpeed(getCurrentPlayRateStringToInt(it))
                                    ToastUtils.showToast2(context, "已切换${it}速播放")
                                }
                                val options = if (item.content?.contains("0.8") == true) {
                                    1
                                } else if (item.content?.contains("1.0") == true) {
                                    2
                                } else if (item.content?.contains("1.25") == true) {
                                    3
                                } else if (item.content?.contains("1.5") == true) {
                                    4
                                } else if (item.content?.contains("2.0") == true) {
                                    5
                                } else {
                                    2
                                }
                                val pageId =
                                    if (SohuDlnaManger.getInstance().getIsDlna()) "1062" else "1045"
                                addPushEvent(10298, CLICK, pathInfo = getInfoEvent {
                                    it["pageId"] = pageId
                                }, null, memoInfo = getInfoEvent {
                                    it["options"] = options
                                })
                            }

                    }

                    is ScaleContentMenuMorePresenter -> {
                        viewHolder.itemView.findViewById<ConstraintLayout>(R.id.cl_sacle_menu_more)
                            .setOnClickListener {
                                var memoInfo: HashMap<String, Any>? = null
                                val item = viewHolder.item as ScaleContentMoreMenuItem
                                item.content?.let {
                                    if (TextUtils.equals(item.content, "跳过")) {
                                        mListener?.isSkipStartAndEnd(true)
                                        memoInfo = getInfoEvent {
                                            it["跳过片头片尾"] = 1
                                        }
                                    }
                                    if (TextUtils.equals(item.content, "不跳过")) {
                                        mListener?.isSkipStartAndEnd(false)
                                        memoInfo = getInfoEvent {
                                            it["跳过片头片尾"] = 2
                                        }
                                    }

                                    if (TextUtils.equals(item.content, "拉伸")) {
                                        mListener?.isPlayerFullScreen(PlayerConstants.ScreenAspectRatio.MATCH_PARENT)
                                        memoInfo = getInfoEvent {
                                            it["size"] = 3
                                        }
                                    }

                                    if (TextUtils.equals(item.content, "满屏")) {
                                        mListener?.isPlayerFullScreen(PlayerConstants.ScreenAspectRatio.CENTER_CROP)
                                        memoInfo = getInfoEvent {
                                            it["size"] = 2
                                        }
                                    }
                                    if (TextUtils.equals(item.content, "默认")) {
                                        mListener?.isPlayerFullScreen(PlayerConstants.ScreenAspectRatio.DEFAULT)
                                        memoInfo = getInfoEvent {
                                            it["size"] = 1
                                        }
                                    }
                                }
                                val pageId =
                                    if (SohuDlnaManger.getInstance().getIsDlna()) "1063" else "1045"

                                addPushEvent(10300, CLICK, pathInfo = getInfoEvent {
                                    it["pageId"] = pageId
                                }, null, memoInfo = memoInfo)
                            }
                    }

                    is ScaleContentMenuOnlySeePresenter -> {
                        viewHolder.itemView.findViewById<ConstraintLayout>(R.id.cl_only_see_all)
                            .setOnClickListener {
                                val item = viewHolder.item as ScaleContentOnlySeeMenuItem
                                when (item.id) {
                                    "0" -> {
                                        isUseOnlySeeModel = false
                                        onlySeeNameImgUrl = null
                                        ToastUtils.showLongToast(context, item.name)
                                    }

                                    else -> {
                                        isUseOnlySeeModel = true
                                        onlySeeNameImgUrl = item.imagesUrl
                                        ToastUtils.showLongToast(context, "只看" + item.name)
                                    }
                                }
                                mListener?.onSelectOnlySeeItemPosition(viewHolder.adapterPosition)
                                initMenuData()
                            }
                    }

                    //清晰度
                    is ScaleContentMenuClarityPresenter -> {
                        viewHolder.itemView.findViewById<ConstraintLayout>(R.id.cl_scale_menu_clarity)
                            .setOnClickListener {
                                //options：1=炫彩；2=蓝光、3=超清，4=高清，5=标清
                                var option = 1
                                val item = viewHolder.item as ScaleContentClarityMenuItem
                                PlaySettingHelper.setPlayAutoClarityIsOpen(false)
                                when (item.content) {
                                    "炫彩HDR" -> {
                                        option = 1
                                        if (mClarityCash.get(Constant.DEFINITION_HDR) != null) {
                                            mListener?.setClarity(Constant.DEFINITION_HDR)
                                        } else {
                                            mListener?.setClarity(Constant.DEFINITION_HDR265)

                                        }

//                                        if (mLoginHelper?.isVip == false) {
//                                            ActivityLauncher.startPayActivity(context)
//                                            return@setOnClickListener
//                                        }
//                                        PlaySettingHelper.setPlayClarity(Constant.DEFINITION_HDR)
                                    }

                                    "蓝光1080P" -> {
                                        option = 2

                                        if (mClarityCash.get(Constant.DEFINITION_BLUE) != null) {
                                            mListener?.setClarity(Constant.DEFINITION_BLUE)
                                        } else if (mClarityCash.get(Constant.DEFINITION_BLUE265) != null) {
                                            mListener?.setClarity(Constant.DEFINITION_BLUE265)
                                        } else if (mClarityCash.get(Constant.DEFINITION_ORIGIN) != null) {
                                            mListener?.setClarity(Constant.DEFINITION_ORIGIN)
                                        } else {
                                            mListener?.setClarity(Constant.DEFINITION_ORIGIN265)
                                        }
//                                        PlaySettingHelper.setPlayClarity(Constant.DEFINITION_BLUE)
//                                        if (mLoginHelper?.isVip == false) {
//                                            ActivityLauncher.startPayActivity(context)
//                                            return@setOnClickListener
//                                        }

                                    }

                                    "超清" -> {
                                        option = 3

                                        if (mClarityCash.get(Constant.DEFINITION_SUPER) != null) {
                                            mListener?.setClarity(Constant.DEFINITION_SUPER)
                                        } else {
                                            mListener?.setClarity(Constant.DEFINITION_SUPER265)
                                        }
//                                        if (mLoginHelper?.isLogin == false) {
//                                            ActivityLauncher.startLoginActivity(context)
//                                            return@setOnClickListener
//                                        }
//                                        PlaySettingHelper.setPlayClarity(Constant.DEFINITION_SUPER)
                                    }

                                    "高清" -> {
                                        option = 4
                                        if (mClarityCash.get(Constant.DEFINITION_HIGH) != null) {
                                            mListener?.setClarity(Constant.DEFINITION_HIGH)
                                        } else {
                                            mListener?.setClarity(Constant.DEFINITION_HIGH265)
                                        }
//                                        PlaySettingHelper.setPlayClarity(Constant.DEFINITION_HIGH)
                                    }

                                    "标清" -> {
                                        option = 5
                                        if (mClarityCash.get(Constant.DEFINITION_FLUENT) != null) {
                                            mListener?.setClarity(Constant.DEFINITION_FLUENT)
                                        } else {
                                            mListener?.setClarity(Constant.DEFINITION_FLUENT265)
                                        }
//                                        PlaySettingHelper.setPlayClarity(Constant.DEFINITION_FLUENT)
                                    }

                                    else -> {
                                        if (item.content?.contains("自动") == true) {
                                            PlaySettingHelper.setPlayAutoClarityIsOpen(true)
                                            setClarityHasChange(true)

                                            mListener?.setClarity(-1)
                                            context?.let {
                                                ToastUtils.showToast(
                                                    it,
                                                    "已经为您切换为自动清晰度"
                                                )
                                            }
//                                        PlaySettingHelper.setPlayClarity(Constant.DEFINITION_HDR)
//                                        mListener?.setClarity(Constant.DEFINITION_HDR)
                                        }
                                    }

                                }
                                val pageId =
                                    if (SohuDlnaManger.getInstance().getIsDlna()) "1060" else "1045"

                                addPushEvent(10294, CLICK, pathInfo = getInfoEvent {
                                    it["pageId"] = pageId
                                }, null, memoInfo = getInfoEvent {
                                    if (!PlaySettingHelper.getPlayAutoClarityIsOpen()) {
                                        it["options"] = option
                                    }
                                    //：1=登录；0=未登录
                                    it["is_login"] = if (mLoginHelper.isLogin) 1 else 0
                                    it["is_vip"] =
                                        if (mLoginHelper.isVip && System.currentTimeMillis() < java.lang.Long.valueOf(
                                                mLoginHelper.getVipTime()
                                            )
                                        ) 1 else 0
                                    //是否自动：0=自动；1=手动
                                    it["is_automatic"] =
                                        if (PlaySettingHelper.getPlayAutoClarityIsOpen()) 0 else 1
                                })
                            }
                        viewHolder.itemView.findViewById<TextView>(R.id.tv_scale_menu_clarity_title)
                            .setOnClickListener {
                                val item = viewHolder.item as ScaleContentClarityMenuItem
                                when (item.titleContent) {
                                    "了解炫彩HDR" -> {
                                        mListener?.onClickHdr()
                                        val pageId = if (SohuDlnaManger.getInstance()
                                                .getIsDlna()
                                        ) "1061" else "1045"
                                        addPushEvent(10295, IMP, pathInfo = getInfoEvent {
                                            it["pageId"] = pageId
                                        }, null, memoInfo = null)
                                    }
                                }

                            }
                    }

                }
            }
        })
    }


    /**
     * 设置初始化选中状态并且请求焦点
     */
    fun setInitSelected(focus: Boolean) {
        setInitSelected(focus, 0)
    }

    fun setHaveClarity(map: MutableMap<Int, Int>, current: Int) {
        mClarityCash.clear()
        mClarityCash.putAll(map)
        mCurrentClarity = current;
        if (mAutoClarity == -1) {
            mAutoClarity = current;
        }
    }

    fun setInitSelected(focus: Boolean, position: Int) {
        isFocusable = focus
        isEnabled = focus
        mScaleMenuViewPresenter.setIsCanFocus(focus)
        if (focus) {
            setSelectItem(position)
            mViewBinding.leanbackScaleMenuView.requestFocus()
        } else {
            setSelectItem(-1)
        }
        if (mViewBinding.leanbackScaleMenuView.selectedPosition == position) {
            hasSelectPushImpEvent = true
            updateSelectItem(position, false)
        } else {
            initMenuData()
        }
        visibility = VISIBLE
    }

    fun setSelectItem(position: Int) {
        oldPosition = position
        mViewBinding.leanbackScaleMenuView.selectedPosition = position
    }

    private fun updateSelectItem(position: Int, isClear: Boolean) {
        val item = mArrayAdapter?.get(position) as ScaleMenuItem
//        mMenuItemData.forEach {
//            it.hasSelect = TextUtils.equals(item.name, it.name)
//        }
        LibDeprecatedLogger.i("ScaleMenu hasClear is:$hasClear    isFocusable is :$isFocusable   mMenuItemData data is ${mMenuItemData.toString()}")
//        updateMenuData()
        when (item.name) {
            MENU_ITEM_EPISODE -> {
                if (!isFocusable) {
                    return
                }
                mViewBinding.leanbackScaleContentMenuView.gone()
                mViewBinding.episodeLayout.visible()
                mViewBinding.episodeLayout.bringToFront()
                if (!hasSelectPushImpEvent) return
                val pageId = if (SohuDlnaManger.getInstance().getIsDlna()) "1059" else "1045"
                addPushEvent(10293, IMP, pathInfo = getInfoEvent {
                    it["pageId"] = pageId
                }, null, null)
                hasSelectPushImpEvent = false
            }
            //清晰度
            MENU_ITEM_CLARITY -> {
                if (!isFocusable) {
                    return
                }
                initSpeedData()
                updateSpeedData()
                mViewBinding.episodeLayout.gone()
                mViewBinding.leanbackScaleContentMenuView.visible()
                if (!hasSelectPushImpEvent) return

                val pageId = if (SohuDlnaManger.getInstance().getIsDlna()) "1060" else "1045"
                addPushEvent(10308, IMP, pathInfo = getInfoEvent {
                    it["pageId"] = pageId
                }, null, null)
                hasSelectPushImpEvent = false

            }
            //倍速
            MENU_ITEM_SPEED -> {
//                    mViewBinding.leanbackScaleContentMenuHeaderView.gone()
                if (!isFocusable) {
                    return
                }

//                if (isClear) {
//                    mMenuContentArrayAdapter?.clear()
//                }
                initClarity()
                updateClarity()
                mViewBinding.episodeLayout.gone()
                mViewBinding.leanbackScaleContentMenuView.visible()
                if (!hasSelectPushImpEvent) return

                val pageId = if (SohuDlnaManger.getInstance().getIsDlna()) "1062" else "1045"
                addPushEvent(10297, IMP, pathInfo = getInfoEvent {
                    it["pageId"] = pageId
                }, null, null)
                hasSelectPushImpEvent = false

            }

            MENU_ITEM_ONLY_SEE -> {
                if (!isFocusable) {
                    return
                }

                try {
                    updateOnlySee()
                } catch (e: Exception) {
                    LibDeprecatedLogger.e("updateOnlySee Exception ${e.localizedMessage}")
                }
                mViewBinding.episodeLayout.gone()
                mViewBinding.leanbackScaleContentMenuView.visible()
                if (!hasSelectPushImpEvent) return

                addPushEvent(10327, IMP, pathInfo = getInfoEvent {
                    it["pageId"] = 1045
                }, null, null)
                hasSelectPushImpEvent = false


            }
            //更多功能
            MENU_ITEM_MORE -> {
//                    mViewBinding.leanbackScaleContentMenuHeaderView.gone()
                if (!isFocusable) {
                    return
                }
                initMore()
                updateMore()
                mViewBinding.episodeLayout.gone()
                mViewBinding.leanbackScaleContentMenuView.visible()
                if (!hasSelectPushImpEvent) return
                val pageId = if (SohuDlnaManger.getInstance().getIsDlna()) "1063" else "1045"
                addPushEvent(10299, IMP, pathInfo = getInfoEvent {
                    it["pageId"] = pageId
                }, null, null)
                hasSelectPushImpEvent = false
            }
        }
    }

    private var hasClear = false

    //事件分发
    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.action == KeyEvent.ACTION_DOWN) {
            when (event.keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    visibility = GONE
                    mListener?.onBackClick()
                    return true
                }

                KeyEvent.KEYCODE_DPAD_UP -> {
                    //查找down事件下一个焦点是否为空 判断拦截  主要目的是拦截 播控菜单出界 不显示焦点问题
                    FocusFinder.getInstance().findNextFocus(this, findFocus(), View.FOCUS_UP)
                        ?: return true

                    //判断当前是否是只看他功能Item
                    if (TextUtils.equals(
                            mMenuItemData[mViewBinding.leanbackScaleMenuView.selectedPosition].name,
                            MENU_ITEM_ONLY_SEE
                        )
                    ) {
                        //设置默认的只看他焦点选中
                        var selectPosition = -1
                        //查找已经选中的 位置
                        for (item in mScaleContentOnlySeeMenuItem) {
                            selectPosition++
                            //找到重新设置焦点选中
                            if (item.hasCurrentSelected) {
                                mViewBinding.leanbackScaleContentMenuView.post {
                                    try {
                                        mViewBinding.leanbackScaleContentMenuView.scrollToPosition(
                                            selectPosition
                                        )
                                    } catch (e: Exception) {
                                        LibDeprecatedLogger.e("leanbackScaleContentMenuView scrollToPosition  Exception :$e")
                                    }
                                }
                                break
                            }
                        }
                    }
                    return super.dispatchKeyEvent(event)
                }

                KeyEvent.KEYCODE_DPAD_RIGHT,
                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    hasSelectPushImpEvent = false
                    hasClear = false
                    val focusNext = if (event.keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                        View.FOCUS_RIGHT
                    } else {
                        View.FOCUS_LEFT
                    }

                    var isMore = false
                    if (focusNext == View.FOCUS_RIGHT) {
                        for (item in mMenuItemData) {
                            //找到重新设置焦点选中
                            if (item.hasSelect && item.name.equals("$MENU_ITEM_MORE")) {
                                isMore = true
                            }
                        }
                    }

                    if (mViewBinding.leanbackScaleMenuView.hasFocus() && !mViewBinding.leanbackScaleContentMenuView.hasFocus() && FocusFinder.getInstance()
                            .findNextFocus(this, findFocus(), focusNext) != null&&!isMore
                    ) {
                        hasSelectPushImpEvent = true
                        hasClear = true
                        mMenuContentArrayAdapter?.clear()
                        LibDeprecatedLogger.i(" dispatchKeyEvent fun mMenuContentArrayAdapter clear ")
                    }
                }
            }
        }
        return super.dispatchKeyEvent(event)
    }


    fun setOnlySee(sees: MutableList<ScaleContentOnlySeeMenuItem>?) {
        sees?.let {
            mScaleContentOnlySeeMenuItem.clear()
            mScaleContentOnlySeeMenuItem.addAll(sees)
        }
        AppLogger.i("ScaleContentOnlySeeMenuItem data is ${mScaleContentOnlySeeMenuItem.toString()}")
    }

    private fun updateOnlySee() {
        mMenuContentArrayAdapter?.setItems(mScaleContentOnlySeeMenuItem, object :
            DiffCallback<ScaleContentOnlySeeMenuItem>() {

            override fun areItemsTheSame(
                oldItem: ScaleContentOnlySeeMenuItem,
                newItem: ScaleContentOnlySeeMenuItem
            ): Boolean {
                return oldItem.name == newItem.name
            }

            override fun areContentsTheSame(
                oldItem: ScaleContentOnlySeeMenuItem,
                newItem: ScaleContentOnlySeeMenuItem
            ): Boolean {
                return oldItem.hasCurrentSelected == newItem.hasCurrentSelected
            }

        })
    }

    private fun initMore() {
        mScaleContentMoreMenuItem.clear()
        if (videoOrientation == 0) {
            mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
                hasTitle = true
                titleContent = "画面尺寸"
                content = "默认"
                hasCurrentSelected =
                    PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.DEFAULT
            }
            )
            mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
                content = "满屏"
                hasCurrentSelected =
                    PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.CENTER_CROP
            }
            )
            mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
                content = "拉伸"
                hasCurrentSelected =
                    PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.MATCH_PARENT
            }
            )
        }
        mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
            hasTitle = true
            titleContent = "片头片尾"
            content = "跳过"
            hasCurrentSelected = PlaySettingHelper.getNeedSkipHeaderAndEnd()
        }
        )
        mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
            content = "不跳过"
            hasCurrentSelected = !PlaySettingHelper.getNeedSkipHeaderAndEnd()
         }
        )
    }

    //更多功能
    private fun updateMore() {
        mMenuContentArrayAdapter?.setItems(mScaleContentMoreMenuItem, object :
            DiffCallback<ScaleContentMoreMenuItem>() {
            override fun areItemsTheSame(
                oldItem: ScaleContentMoreMenuItem,
                newItem: ScaleContentMoreMenuItem
            ): Boolean {
                return oldItem.content == newItem.content
            }

            override fun areContentsTheSame(
                oldItem: ScaleContentMoreMenuItem,
                newItem: ScaleContentMoreMenuItem
            ): Boolean {
                return oldItem.hasCurrentSelected == newItem.hasCurrentSelected

            }

        })

    }

    private fun initClarity() {
        mScaleContentSpeedMenuItem.clear()
        val speed = getCurrentPlayRateToString()
        AppLogger.v("获取设置的倍速:${speed}")
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = PLAY_RATE_LOW
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = PLAY_RATE_NORMAL
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = PLAY_RATE_HIGH
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = PLAY_RATE_ONEHALF
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = PLAY_RATE_TWO
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )

    }

    //更新倍速
    private fun updateClarity() {
        mMenuContentArrayAdapter?.setItems(mScaleContentSpeedMenuItem, object :
            DiffCallback<ScaleContentSpeedMenuItem>() {
            override fun areItemsTheSame(
                oldItem: ScaleContentSpeedMenuItem,
                newItem: ScaleContentSpeedMenuItem
            ): Boolean {
                return oldItem.content == newItem.content
            }

            override fun areContentsTheSame(
                oldItem: ScaleContentSpeedMenuItem,
                newItem: ScaleContentSpeedMenuItem
            ): Boolean {
                return oldItem.hasCurrentSelected == newItem.hasCurrentSelected
            }

        })

//        mMenuContentArrayAdapter?.clear()
//        mMenuContentArrayAdapter?.add(ScaleContentSpeedMenuItem().apply {
//            content = PLAY_RATE_LOW
//            hasCurrentSelected = TextUtils.equals(getCurrentPlayRateToString(), content)
//        }
//        )
//        mMenuContentArrayAdapter?.add(ScaleContentSpeedMenuItem().apply {
//            content = PLAY_RATE_NORMAL
//            hasCurrentSelected = TextUtils.equals(getCurrentPlayRateToString(), content)
//        }
//        )
//        mMenuContentArrayAdapter?.add(ScaleContentSpeedMenuItem().apply {
//            content = PLAY_RATE_HIGH
//            hasCurrentSelected = TextUtils.equals(getCurrentPlayRateToString(), content)
//        }
//        )
//        mMenuContentArrayAdapter?.add(ScaleContentSpeedMenuItem().apply {
//            content = PLAY_RATE_ONEHALF
//            hasCurrentSelected = TextUtils.equals(getCurrentPlayRateToString(), content)
//        }
//        )
//        mMenuContentArrayAdapter?.add(ScaleContentSpeedMenuItem().apply {
//            content = PLAY_RATE_TWO
//            hasCurrentSelected = TextUtils.equals(getCurrentPlayRateToString(), content)
//        }
//        )
    }

    private fun initSpeedData() {
        mScaleContentClarityMenuItem.clear()
        val isAutoClarity = PlaySettingHelper.getPlayAutoClarityIsOpen()
        if (!TeenagersManger.isTeenager()) {
            if (mClarityCash.get(Constant.DEFINITION_HDR) != null || mClarityCash.get(Constant.DEFINITION_HDR265) != null) {
                mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
                    hasTitle = true
                    content = "炫彩HDR"
                    titleContent = "了解炫彩HDR"
                    isVip = true
                    isMustLogin = true
                    isShowImgTips = true
                    hasCurrentSelected =
                        (mCurrentClarity == Constant.DEFINITION_HDR || mCurrentClarity == Constant.DEFINITION_HDR265) && !isAutoClarity
                }
                )
            }
            if (mClarityCash.get(Constant.DEFINITION_ORIGIN) != null || mClarityCash.get(
                    Constant.DEFINITION_ORIGIN265
                ) != null || mClarityCash.get(
                    Constant.DEFINITION_BLUE265
                ) != null || mClarityCash.get(
                    Constant.DEFINITION_BLUE
                ) != null
            ) {
                mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
                    content = "蓝光1080P"
                    isVip = dataType == Constant.DATA_TYPE_VRS //VRS需要会员，PGC不需要
                    isMustLogin = true
                    if (dataType == Constant.DATA_TYPE_VRS) {
                        isShowImgTips = true
                    } else {
                        isShowImgTips = !mLoginHelper.isLogin
                    }
                    hasCurrentSelected =
                        (mCurrentClarity == Constant.DEFINITION_ORIGIN || mCurrentClarity == Constant.DEFINITION_ORIGIN265 || mCurrentClarity == Constant.DEFINITION_BLUE || mCurrentClarity == Constant.DEFINITION_BLUE265) && !isAutoClarity
                }
                )
            }


        }
        if (mClarityCash.get(Constant.DEFINITION_SUPER) != null || mClarityCash.get(
                Constant.DEFINITION_SUPER265
            ) != null
        ) {
            mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
                content = "超清"
                isVip = false
                isMustLogin = true
                if (SohuDlnaManger.getInstance().getIsDlna()) {
                    isShowImgTips = false
                } else {
                    isShowImgTips = !mLoginHelper.isLogin
                }
                hasCurrentSelected =
                    (mCurrentClarity == Constant.DEFINITION_SUPER || mCurrentClarity == Constant.DEFINITION_SUPER265) && !isAutoClarity
            }
            )
        }
        if (mClarityCash.get(Constant.DEFINITION_HIGH) != null || mClarityCash.get(
                Constant.DEFINITION_HIGH265
            ) != null
        ) {
            mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
                content = "高清"
                hasCurrentSelected =
                    (mCurrentClarity == Constant.DEFINITION_HIGH || mCurrentClarity == Constant.DEFINITION_HIGH265) && !isAutoClarity
            }
            )
        }

        if (mClarityCash.get(Constant.DEFINITION_FLUENT) != null || mClarityCash.get(
                Constant.DEFINITION_FLUENT265
            ) != null
        ) {
            mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
                content = "标清"
                hasCurrentSelected =
                    (mCurrentClarity == Constant.DEFINITION_FLUENT || mCurrentClarity == Constant.DEFINITION_FLUENT265) && !isAutoClarity
            }
            )
        }
        val mPartnerNo = Util.getPartnerNo(context)
        //针对长虹机型判断不初始化cornet库 所以需不展示清晰度设置
//        if (mClarityCash.size > 1 && !SohuDlnaManger.getInstance().getIsDlna()&&!TextUtils.equals("80151103", mPartnerNo)) {
//            mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
//                content =
//                    if (mAutoClarity != -1 && isAutoClarity) "自动(${getAutoClarityContent()})" else "自动清晰度"
//                hasCurrentSelected = isAutoClarity
//            }
//            )
//        }
    }


    fun setAutoClarityChange(clarity: Int) {
        if (PlaySettingHelper.getPlayAutoClarityIsOpen()) {
            mAutoClarity = clarity
            initSpeedData()
        } else {
            mAutoClarity = -1
        }
    }


    /**
     * 添加清晰度数据
     */
    private fun updateSpeedData() {
        AppLogger.v("PlaySettingHelper.getPlayClarity${PlaySettingHelper.getPlayClarity()}")
        mMenuContentArrayAdapter?.setItems(mScaleContentClarityMenuItem, object :
            DiffCallback<ScaleContentClarityMenuItem>() {
            override fun areItemsTheSame(
                oldItem: ScaleContentClarityMenuItem,
                newItem: ScaleContentClarityMenuItem
            ): Boolean {
                return oldItem.content == newItem.content
            }

            override fun areContentsTheSame(
                oldItem: ScaleContentClarityMenuItem,
                newItem: ScaleContentClarityMenuItem
            ): Boolean {
                return oldItem.hasCurrentSelected == newItem.hasCurrentSelected &&
                        oldItem.isShowImgTips == newItem.isShowImgTips &&
                        oldItem.isVip == newItem.isVip &&
                        oldItem.isMustLogin == newItem.isMustLogin &&
                        oldItem.isShowImgTips == newItem.isShowImgTips

            }

        })

    }

    private fun getCurrentPlayRateToString(): String {
        return when (PlaySettingHelper.getPlaySpeed()) {
            UserConstants.PE_PLAY_RATE_LOW -> UserConstants.PLAY_RATE_LOW
            UserConstants.PE_PLAY_RATE_NORMAL -> UserConstants.PLAY_RATE_NORMAL
            UserConstants.PE_PLAY_RATE_HIGH -> UserConstants.PLAY_RATE_HIGH
            UserConstants.PE_PLAY_RATE_ONEHALF -> UserConstants.PLAY_RATE_ONEHALF
            UserConstants.PE_PLAY_RATE_TWO -> UserConstants.PLAY_RATE_TWO
            else -> {
                AppLogger.v("获取倍速出错！")
                PLAY_RATE_NORMAL
            }
        }
    }

    private fun getCurrentPlayRateStringToInt(str: String): Float {
        return when (str) {
            UserConstants.PLAY_RATE_LOW -> UserConstants.PE_PLAY_RATE_LOW
            UserConstants.PLAY_RATE_NORMAL -> UserConstants.PE_PLAY_RATE_NORMAL
            UserConstants.PLAY_RATE_HIGH -> UserConstants.PE_PLAY_RATE_HIGH
            UserConstants.PLAY_RATE_ONEHALF -> UserConstants.PE_PLAY_RATE_ONEHALF
            UserConstants.PLAY_RATE_TWO -> UserConstants.PE_PLAY_RATE_TWO
            else -> {
                AppLogger.v("切换倍速出错！ str:$str")
                UserConstants.PE_PLAY_RATE_HIGH
            }
        }
    }

    companion object {
        const val PLAY_RATE_LOW = "0.8倍"
        const val PLAY_RATE_NORMAL = "1.0倍"
        const val PLAY_RATE_HIGH = "1.25倍"
        const val PLAY_RATE_ONEHALF = "1.5倍"
        const val PLAY_RATE_TWO = "2.0倍"


        const val MENU_ITEM_EPISODE = "选集"
        const val MENU_ITEM_CLARITY = "清晰度"
        const val MENU_ITEM_SPEED = "倍速"
        const val MENU_ITEM_ONLY_SEE = "只看TA"
        const val MENU_ITEM_MORE = "更多功能"

    }

    interface ScaleNewMenuViewClickListener {
        //清晰度
        fun onClickSpeed(currentPlayRateStringToInt: Float)

        fun isSkipStartAndEnd(isSkip: Boolean)

        fun isPlayerFullScreen(isFull: Int)

        fun setClarity(clarity: Int)

        fun onClickHdr()

        fun onSelectItemPosition(position: Int)

        fun onBackClick()

        fun onSelectOnlySeeItemPosition(position: Int)
    }

    fun setOnScaleNewMenuViewClickListener(listener: ScaleNewMenuViewClickListener) {
        mListener = listener
    }

    /**
     * ----------- 以下代码为 剧集代码 -------------------------
     */

    //播放器数据加载后使用需要重新加载数据
    fun episodeLayoutUpdate(
        aid: Int,
        vid: Int,
        dataType: Int,
        catecode: Int,
        sortOrder: Int,
        layoutType: Int,
        isTrailer: Boolean,
        totalCount: Int,
        videoOrder: Int,
        recommendList: List<AlbumInfoRecommendModel>?
    ) {
        this.dataType = dataType
        if (totalCount >= 1) {
            if (true
            ) {
                mViewBinding.episodeLayout.setVisibility(VISIBLE)
                mViewBinding.episodeLayout.setEpisodeIsSelected(true)
                mViewBinding.episodeLayout.initFromMenuView(
                    aid,
                    vid,
                    dataType,
                    catecode,
                    sortOrder,
                    layoutType,
                    isTrailer,
                    totalCount,
                    videoOrder,
                    recommendList
                )
            } else {
                mViewBinding.episodeLayout.updateSelectAfterPlay(videoOrder, true, vid)
            }
        } else {
            mViewBinding.episodeLayout.setVisibility(GONE)
            mViewBinding.episodeLayout.setEpisodeType(layoutType)
        }
    }

    fun getEpisodeSortOrder(): Int {
        return mViewBinding.episodeLayout.getSortOrder()
    }

    fun getEpisodeType(): Int {
        return mViewBinding.episodeLayout.getEpisodeType()
    }

    fun getEpisodeTotalCount(): Int {
        return mViewBinding.episodeLayout.getTotalCount()
    }

    fun getPageSize(): Int {
        return mViewBinding.episodeLayout.getPageSize()
    }

    fun isLastEpisode(videoOrder: Int): Int {
        return mViewBinding.episodeLayout.isLastEpisode(videoOrder)
    }

    fun getEpisodeVideoOrder(): Int {
        return if (mViewBinding.episodeLayout.getVisibility() == VISIBLE) {
            mViewBinding.episodeLayout.getEpisodeVideoOrder()
        } else {
            1
        }
    }


    fun setDefaultFocus(videoOrder: Int, vid: Int) {
        if (mViewBinding.episodeLayout != null && mViewBinding.episodeLayout.getVisibility() == VISIBLE) {
            mViewBinding.episodeLayout.updateSelectAfterPlay(videoOrder, true, vid)
            mViewBinding.episodeLayout.setEpisodeFragmentFoucus()
        }
    }


    fun setFocusBorderView(focusBorderView: FocusBorderView) {
        mViewBinding.episodeLayout.setFocusBorderView(focusBorderView)
    }


    /**
     * ----------- 以上代码为 剧集代码 -------------------------
     */


}