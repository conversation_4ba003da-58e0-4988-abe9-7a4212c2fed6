package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.FocusFinder
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.DiffCallback
import androidx.leanback.widget.HorizontalGridView
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.lib_dlna_core.SohuDlnaManger
import com.lib_dlna_core.center.DlnaMediaModel
import com.lib_statistical.CLICK
import com.lib_statistical.IMP
import com.lib_statistical.addPushEvent
import com.lib_statistical.getInfoEvent
import com.sh.ott.video.ad.AdTsManger
import com.sh.ott.video.contor.IShControlComponent
import com.sh.ott.video.contor.ShVideoViewController
import com.sh.ott.video.player.PlayerConfig
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.sofa.SofaPlayerLogger
import com.sohu.ott.base.lib_user.UserConfigHelper
import com.sohu.ott.base.lib_user.UserConstants
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohu.ott.lib_widget.SoHuVideoProgressBar.SecondListBean
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.PayActivity
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper.setPlaySpeed
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger
import com.sohuott.tv.vod.app.App
import com.sohuott.tv.vod.app.config.ResolutionInfo
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.ToastUtils
import com.sohuott.tv.vod.ui.EpisodeLayoutNew
import com.sohuott.tv.vod.utils.ActivityLauncher
import com.sohuott.tv.vod.utils.ParamConstant
import com.sohuott.tv.vod.videodetail.activity.playerIsNotStart
import com.sohuott.tv.vod.videodetail.activity.state.OnlySeeData
import com.sohuott.tv.vod.videodetail.activity.state.ResolutionApp
import com.sohuott.tv.vod.videodetail.activity.state.VideoPlayResolutionInfo
import com.sohuott.tv.vod.view.FocusBorderView
import com.sohuott.tv.vod.view.scalemenu.ScaleScreenViewMenuNewView
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentClarityMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentMoreMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentSpeedMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.VideoInfoOnlySeeTimeItem
import com.sohuott.tv.vod.view.scalemenu.presenter.ScaleContentMenuClarityPresenter
import com.sohuott.tv.vod.view.scalemenu.presenter.ScaleContentMenuMorePresenter
import com.sohuott.tv.vod.view.scalemenu.presenter.ScaleContentMenuOnlySeePresenter
import com.sohuott.tv.vod.view.scalemenu.presenter.ScaleContentMenuSelector
import com.sohuott.tv.vod.view.scalemenu.presenter.ScaleContentMenuSpeedPresenter
import com.sohuott.tv.vod.view.scalemenu.presenter.ScaleMenuViewPresenter
import com.sohuott.tv.vod.widget.EpisodeHorzTabView
import com.tcl.auth.deviceinfo.SqlCommon
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.Math.abs

/**
 * 播放器配置 倍速等
 */
class VideoPlayMenuConfigComponent(
    context: Context
) :
    BaseVideoProgressComponent(context), IShControlComponent {
    var mIControlComponentHide: IControlComponentHide? = null

    private var aid = 0
    private var vid = 0
    private var oldPosition = 0
    private var selectItemName = UserConstants.MENU_ITEM_EPISODE
    private var mMenuItemData = mutableListOf<ScaleMenuItem>()
    private var mArrayAdapter: ArrayObjectAdapter? = null
    private var mAdapter: ItemBridgeAdapter? = null
    private var mScaleMenuViewPresenter: ScaleMenuViewPresenter
    private var mVideoPlayMenuBean: OnlySeeData? = null
    private var hasOnlySee: Boolean = false
    private var mScaleContentSpeedMenuItem = mutableListOf<ScaleContentSpeedMenuItem>()
    private var mScaleContentMoreMenuItem = mutableListOf<ScaleContentMoreMenuItem>()
    private var mScaleContentOnlySeeMenuItem = mutableListOf<ScaleContentOnlySeeMenuItem>()
    private var mScaleContentClarityMenuItem = mutableListOf<ScaleContentClarityMenuItem>()


    private var mMenuContentArrayAdapter: ArrayObjectAdapter? = null
    private var mMenuContentAdapter: ItemBridgeAdapter? = null

    private var tabMenu: HorizontalGridView? = null
    private var tabContext: HorizontalGridView? = null
    private var episodeLayout: EpisodeLayoutNew? = null

    /**
     * 此变量是为了防止 选择菜单时setOnChildSelectedListener 方法调用两次 导致曝光上报两次问题
     */
    var hasSelectPushImpEvent = false

    var videoOrientation = 0

    private var menuHasClear = false

    private var mCurrentSelectOnlySeeMenuItem: ScaleContentOnlySeeMenuItem? = null
    private val mProSeeList: MutableList<SecondListBean> = mutableListOf()
    private val mOnlySeeList: MutableList<ScaleContentOnlySeeMenuItem> = mutableListOf()
    private var onlySeeNameImgUrl: MutableList<String>? = null

    var videoType: Int = Constant.DATA_TYPE_VRS

    /**
     * 是否是只看TA模式
     */
    var isUseOnlySeeModel = false

    private var mPlayMenuConfigListener: PlayMenuConfigListener? = null
    private var resolutionApp = mutableMapOf<ResolutionApp, VideoPlayResolutionInfo>()

    private val isDlna: Boolean
        get() {
            return SohuDlnaManger.getInstance().getIsDlna()
        }
    var dlnaModel: DlnaMediaModel? = null

    private val hasLogin: Boolean
        get() {
            return if (isDlna) dlnaModel?.isLogin ?: false else UserLoginHelper.getInstants()
                .getIsLogin()
        }
    private val hasVip: Boolean
        get() {
            return if (isDlna) dlnaModel?.isVip ?: false else UserLoginHelper.getInstants().isVip()
        }
    private val vipTime: String?
        get() {
            return UserLoginHelper.getInstants().getVipTime()
        }
    private val hasTeenager = TeenagersManger.isTeenager()

    private var mVideoOrder: Int = 0
    private var mVideoCount: Int = 0
    private lateinit var mHandler: Handler
    private var mCompletionRunnable: Runnable? = null


    private lateinit var lifecycleOwner: LifecycleOwner
    private val lifecycleScope: CoroutineScope
        get() = lifecycleOwner.lifecycleScope

    private var delayDisplayJob: Job? = null
    private val delayTime = 200L


    init {
        gone()
        mHandler = Handler(Looper.getMainLooper())
        mCompletionRunnable = CompletionRunnable()
        val view = layoutInflater.inflate(R.layout.video_component_config_menu, this, true)
        tabMenu = findViewById<HorizontalGridView>(R.id.leanback_scale_menu_view)
        tabContext = findViewById<HorizontalGridView>(R.id.leanback_scale_content_menu_view)
        episodeLayout = findViewById<EpisodeLayoutNew>(R.id.episode_layout)
        mScaleMenuViewPresenter = ScaleMenuViewPresenter(context = context)
        mArrayAdapter = ArrayObjectAdapter(mScaleMenuViewPresenter)
        mAdapter = ItemBridgeAdapter(mArrayAdapter)
        tabMenu?.adapter = mAdapter
        mMenuContentArrayAdapter = ArrayObjectAdapter(ScaleContentMenuSelector(context))
        mMenuContentAdapter = ItemBridgeAdapter(mMenuContentArrayAdapter)
        tabContext?.adapter = mMenuContentAdapter
        tabContext?.horizontalSpacing =
            resources.getDimension(R.dimen.x18).toInt()
        setOnChildSelectedListener()
        setAdapterListener()
    }

    fun setLifecycleOwner(owner: LifecycleOwner) {
        this.lifecycleOwner = owner
    }

    fun getIsShow(): Boolean {
        return visibility == VISIBLE
    }

    fun removeCallBacks() {
        if (!isNeedDelayChangeNextSegments) {
            mCompletionRunnable?.let { mHandler.removeCallbacks(it) }
            mHandler.removeCallbacksAndMessages(null)
        }
    }

    fun initData(hasOnlySee: Boolean, initBean: ScaleContentOnlySeeMenuItem?) {
        removeCallBacks()
        isSendMessageCompleted = false
        segmentStartIndex = 0
        if (TextUtils.equals(initBean?.id, mCurrentSelectOnlySeeMenuItem?.id)) {
            mCurrentSelectOnlySeeMenuItem = initBean
            isNeedDelayChangeNextSegments = false
        } else {
            mCurrentSelectOnlySeeMenuItem = initBean
        }

        setCurrentOnlySeeImg(mCurrentSelectOnlySeeMenuItem?.imagesUrl)
        oldPosition = 0
        selectItemName = UserConstants.MENU_ITEM_EPISODE
        this.hasOnlySee = hasOnlySee
//        val videoInfoOnlySeeItem=initBean?.videoInfoOnlySeeItem
        //如果mCurrentSelectOnlySeeMenuItem 不为空 证明 之前有选中的 只看他片段  进行对比当前videoinfo中是否存在  不存在设置观看全部
        //如果mCurrentSelectOnlySeeMenuItem 不为空 证明 之前有选中的 只看他片段  进行对比当前videoinfo中是否存在  不存在设置观看全部
//        if (getIsOnlySeeModel()) {
//            var isFind = false
//            if (videoInfoOnlySeeItem != null) {
//                for ((starId) in videoInfoOnlySeeItem) {
//                    if (starId == mCurrentSelectOnlySeeMenuItem?.id) {
//                        isFind = true
//                        break
//                    }
//                }
//            }
//            //没有找到对应片段  恢复状态及ui
//        val isFind = mVideoPlayMenuBean?.isFind
//        i("seekOnlySee", "addVideoInfo fun is find getId equals is :$isFind")
//        if (isFind == false) {
//            //之前选择的只看某个片段没有找到提示用户
//            if (mCurrentSelectOnlySeeMenuItem != null) {
//                ToastUtils.showLongToast(
//                    context,
//                    "本集无" + mCurrentSelectOnlySeeMenuItem?.name + "，已为您播放完整版"
//                )
//                mCurrentSelectOnlySeeMenuItem = null
//            }
//                mCurrentSelectOnlySeeMenuItem = null
////                mPlaterMenuNewView.setCurrentOnlySeeImg(null)
////                mScaleScreenViewMenuNewView.setCurrentOnlySeeImg(null)
////                mPlaterMenuNewView.initMenuData()
////                mScaleScreenViewMenuNewView.initMenuData()
//            }
//        }
        initMenuData()
    }

    private fun setOnChildSelectedListener() {
        tabMenu?.setOnChildSelectedListener { parent, view, position, id ->
            try {
                val item = mArrayAdapter?.get(position) as ScaleMenuItem
                if (!menuHasClear) {
                    mMenuContentArrayAdapter?.clear()
                }
                LibDeprecatedLogger.d("oldPosition:$oldPosition , position:$position")
                if (oldPosition == position) return@setOnChildSelectedListener
                oldPosition = position
                selectItemName = item.name ?: UserConstants.MENU_ITEM_EPISODE
                initMenuData()
                updateTabItem()
                updateSelect(selectItemName, true)
            } catch (e: Throwable) {
                AppLogger.e(e.localizedMessage)
            }
        }
    }

    private fun updateSelect(name: String, delay: Boolean = false) {
        LibDeprecatedLogger.d("name: $name， selectItemName：$selectItemName")
        delayDisplayJob?.cancel()
        when (name) {
            UserConstants.MENU_ITEM_EPISODE -> {
                delayDisplayJob = lifecycleScope.launch {
                    if (delay) {
                        delay(delayTime)
                    }
                    tabContext?.gone()
                    episodeLayout?.visible()
                    episodeLayout?.bringToFront()
//                episodeLayout?.requestFocus()
                    if (!hasSelectPushImpEvent) return@launch
                    val pageId = if (SohuDlnaManger.getInstance().getIsDlna()) "1059" else "1045"
                    addPushEvent(10293, IMP, pathInfo = getInfoEvent {
                        it["pageId"] = pageId
                    }, null, null)
                    hasSelectPushImpEvent = false
                }
            }

            UserConstants.MENU_ITEM_CLARITY -> {
                delayDisplayJob = lifecycleScope.launch {
                    if (delay) {
                        delay(delayTime)
                    }
//                    mMenuContentArrayAdapter?.clear()
                    episodeLayout?.gone()
                    tabContext?.visible()
                    initClarityData()
                    updateClarityData()
                    if (!hasSelectPushImpEvent) return@launch
                    val pageId = if (SohuDlnaManger.getInstance().getIsDlna()) "1060" else "1045"
                    addPushEvent(10308, IMP, pathInfo = getInfoEvent {
                        it["pageId"] = pageId
                    }, null, null)
                    hasSelectPushImpEvent = false
                }
            }

            UserConstants.MENU_ITEM_SPEED -> {
                delayDisplayJob = lifecycleScope.launch {
                    if (delay) {
                        delay(delayTime)
                    }
//                    mMenuContentArrayAdapter?.clear()
                    episodeLayout?.gone()
                    tabContext?.visible()
                    initSpeed()
                    updateSpeed()
                    if (!hasSelectPushImpEvent) return@launch
                    val pageId = if (SohuDlnaManger.getInstance().getIsDlna()) "1062" else "1045"
                    addPushEvent(10297, IMP, pathInfo = getInfoEvent {
                        it["pageId"] = pageId
                    }, null, null)
                    hasSelectPushImpEvent = false
                }
            }

            UserConstants.MENU_ITEM_ONLY_SEE -> {
                delayDisplayJob = lifecycleScope.launch {
                    if (delay) {
                        delay(delayTime)
                    }
//                    mMenuContentArrayAdapter?.clear()
                    episodeLayout?.gone()
                    try {
                        updateOnlySee()
                    } catch (e: Exception) {
                        LibDeprecatedLogger.e("updateOnlySee Exception ${e.localizedMessage}")
                    }
                    tabContext?.visible()
                    if (!hasSelectPushImpEvent) return@launch

                    addPushEvent(10327, IMP, pathInfo = getInfoEvent {
                        it["pageId"] = 1045
                    }, null, null)
                    hasSelectPushImpEvent = false
                }
            }

            UserConstants.MENU_ITEM_MORE -> {
                delayDisplayJob = lifecycleScope.launch {
                    if (delay) {
                        delay(delayTime)
                    }
//                    mMenuContentArrayAdapter?.clear()
                    episodeLayout?.gone()
                    initMore()
                    updateMore()
                    tabContext?.visible()
                    if (!hasSelectPushImpEvent) return@launch
                    val pageId = if (SohuDlnaManger.getInstance().getIsDlna()) "1063" else "1045"
                    addPushEvent(10299, IMP, pathInfo = getInfoEvent {
                        it["pageId"] = pageId
                    }, null, null)
                    hasSelectPushImpEvent = false
                }
            }
        }

    }

    private fun setAdapterListener() {
        mMenuContentAdapter?.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
            override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder?) {
                super.onCreate(viewHolder)
                when (viewHolder?.presenter) {
                    //清晰度
                    is ScaleContentMenuClarityPresenter -> {
                        viewHolder.itemView.findViewById<ConstraintLayout>(R.id.cl_scale_menu_clarity)
                            .setOnClickListener {
                                try {
                                    //options：1=炫彩；2=蓝光、3=超清，4=高清，5=标清
                                    var option = 1
                                    val item = viewHolder.item as ScaleContentClarityMenuItem
                                    val info = resolutionInfos.find { item.content == it.name }
                                    PlaySettingHelper.setPlayAutoClarityIsOpen(false)


                                    when (info?.id) {
                                        ResolutionApp.APP_NORMAL.appValue -> {
                                            option = 5
                                            setCurrentClarity(info)
                                            mPlayMenuConfigListener?.onClickResolution(info)
                                        }

                                        ResolutionApp.APP_HIGH.appValue -> {
                                            option = 4
                                            setCurrentClarity(info)
                                            mPlayMenuConfigListener?.onClickResolution(info)
                                        }

                                        ResolutionApp.APP_SUPER.appValue -> {
                                            option = 3
                                            if (!hasLogin) {
                                                hide()
                                                mPlayMenuConfigListener?.onClickResolutionStartLogin(
                                                    info
                                                )
                                                ActivityLauncher.startLoginActivity(context)
                                                return@setOnClickListener
                                            }
                                            setCurrentClarity(info)
                                            mPlayMenuConfigListener?.onClickResolution(info)
                                        }

                                        ResolutionApp.MEDIA_BLUE.appValue -> {
                                            option = 2
                                            if (!hasVip) {
                                                if (isDlna) {
                                                    ToastUtils.showToast2(
                                                        context,
                                                        "请在手机端登录会员后选择此清晰度"
                                                    )
                                                } else {
                                                    hide()
//                                                mPlayMenuConfigListener?.onClickResolution(info)
                                                    mPlayMenuConfigListener?.onClickResolutionStartLogin(
                                                        info
                                                    )
                                                    ActivityLauncher.startPayActivityWithAidVid(
                                                        context,
                                                        aid,
                                                        vid,
                                                        ParamConstant.PAGE_SOURCE_PLAYER,
                                                        PayActivity.PAY_SOURCE_DETAIL,
                                                        false
                                                    )
                                                }
                                                return@setOnClickListener
                                            }
                                            setCurrentClarity(info)
                                            mPlayMenuConfigListener?.onClickResolution(info)
                                        }

                                        ResolutionApp.APP_ORIGINAL_HDR.appValue -> {
                                            option = 1
                                            if (!hasVip) {
                                                if (isDlna) {
                                                    ToastUtils.showToast2(
                                                        context,
                                                        "请在手机端登录会员后选择此清晰度"
                                                    )
                                                } else {
                                                    hide()
//                                                mPlayMenuConfigListener?.onClickResolution(info)
                                                    mPlayMenuConfigListener?.onClickResolutionStartLogin(
                                                        info
                                                    )
                                                    ActivityLauncher.startPayActivityWithAidVid(
                                                        context,
                                                        aid,
                                                        vid,
                                                        ParamConstant.PAGE_SOURCE_PLAYER,
                                                        PayActivity.PAY_SOURCE_DETAIL,
                                                        false
                                                    )
                                                }
                                                return@setOnClickListener
                                            }
                                            setCurrentClarity(info)
                                            mPlayMenuConfigListener?.onClickResolution(info)
                                        }

                                        ResolutionApp.APP_4K.appValue -> {
                                            option = 1

                                            if (!hasVip) {
                                                if (isDlna) {
                                                    ToastUtils.showToast2(
                                                        context,
                                                        "请在手机端登录会员后选择此清晰度"
                                                    )
                                                } else {
                                                    hide()
//                                                mPlayMenuConfigListener?.onClickResolutionAppStartLogin(
//                                                    ResolutionApp.APP_ORIGINAL_HDR
//                                                )
                                                    mPlayMenuConfigListener?.onClickResolutionStartLogin(
                                                        info
                                                    )
                                                    ActivityLauncher.startPayActivityWithAidVid(
                                                        context,
                                                        aid,
                                                        vid,
                                                        ParamConstant.PAGE_SOURCE_PLAYER,
                                                        PayActivity.PAY_SOURCE_DETAIL,
                                                        false
                                                    )
                                                }
                                                return@setOnClickListener
                                            }
                                            setCurrentClarity(info)
                                            mPlayMenuConfigListener?.onClickResolution(info)
                                        }

                                    }

                                    val pageId =
                                        if (SohuDlnaManger.getInstance()
                                                .getIsDlna()
                                        ) "1060" else "1045"

                                    addPushEvent(10294, CLICK, pathInfo = getInfoEvent {
                                        it["pageId"] = pageId
                                    }, null, memoInfo = getInfoEvent {
                                        if (!PlaySettingHelper.getPlayAutoClarityIsOpen()) {
                                            it["options"] = option
                                        }
                                        //：1=登录；0=未登录
                                        it["is_login"] = if (hasLogin) 1 else 0
                                        it["is_vip"] =
                                            if (hasVip && System.currentTimeMillis() < java.lang.Long.valueOf(
                                                    vipTime
                                                )
                                            ) 1 else 0
                                        //是否自动：0=自动；1=手动
                                        it["is_automatic"] =
                                            if (PlaySettingHelper.getPlayAutoClarityIsOpen()) 0 else 1
                                    })
                                } catch (e: Throwable) {
                                    AppLogger.e(e.localizedMessage)
                                }
                                hide()
                            }
                        viewHolder.itemView.findViewById<TextView>(R.id.tv_scale_menu_clarity_title)
                            .setOnClickListener {
                                try {
                                    val item = viewHolder.item as ScaleContentClarityMenuItem
                                    val info = resolutionInfos.find { item.content == it.name }
                                    if (!item.hasTitle) return@setOnClickListener
                                    when (info?.id) {
                                        //点击hdr了解清晰度
                                        ResolutionApp.APP_ORIGINAL_HDR.appValue -> {
                                            mPlayMenuConfigListener?.onClickHdrTips()
//                                        mListener?.onClickHdr()
                                            val pageId = if (SohuDlnaManger.getInstance()
                                                    .getIsDlna()
                                            ) "1061" else "1045"
                                            addPushEvent(10295, IMP, pathInfo = getInfoEvent {
                                                it["pageId"] = pageId
                                            }, null, memoInfo = null)
                                            hide()
                                        }
                                    }
                                } catch (e: Throwable) {
                                    AppLogger.e(e.localizedMessage)
                                }
                                hide()

                            }
                    }

                    //倍速
                    is ScaleContentMenuSpeedPresenter -> {
                        viewHolder.itemView.findViewById<ConstraintLayout>(R.id.cl_scale_menu_speed)
                            .setOnClickListener {
                                try {

                                    val item = viewHolder.item
                                    item as ScaleContentSpeedMenuItem
                                    val speed = getCurrentPlayRateStringToInt(item.content)
                                    player?.speed = speed
                                    setPlaySpeed(speed)
                                    ToastUtils.showToast2(context, "已切换${speed}速播放")
                                    val options = if (item.content?.contains("0.8") == true) {
                                        1
                                    } else if (item.content?.contains("1.0") == true) {
                                        2
                                    } else if (item.content?.contains("1.25") == true) {
                                        3
                                    } else if (item.content?.contains("1.5") == true) {
                                        4
                                    } else if (item.content?.contains("2.0") == true) {
                                        5
                                    } else {
                                        2
                                    }
                                    val pageId =
                                        if (SohuDlnaManger.getInstance()
                                                .getIsDlna()
                                        ) "1062" else "1045"
                                    addPushEvent(10298, CLICK, pathInfo = getInfoEvent {
                                        it["pageId"] = pageId
                                    }, null, memoInfo = getInfoEvent {
                                        it["options"] = options
                                    })
                                } catch (e: Throwable) {
                                    AppLogger.e(e.localizedMessage)
                                }

                                hide()
                            }

                    }
                    //更多
                    is ScaleContentMenuMorePresenter -> {
                        viewHolder.itemView.findViewById<ConstraintLayout>(R.id.cl_sacle_menu_more)
                            .setOnClickListener {
                                try {
                                    var memoInfo: HashMap<String, Any>? = null
                                    val item = viewHolder.item as ScaleContentMoreMenuItem
                                    item.content?.let {
                                        if (TextUtils.equals(item.content, "跳过")) {
//                                        mListener?.isSkipStartAndEnd(true)
                                            memoInfo = getInfoEvent {
                                                it["跳过片头片尾"] = 1
                                            }
                                            PlayerConfig.enableSkipStartAndEndPosition = true
                                            PlaySettingHelper.setNeedSkipHeaderAndEnd(true)
                                            mPlayMenuConfigListener?.onSkipStartAndEnd()
                                        }
                                        if (TextUtils.equals(item.content, "不跳过")) {
//                                        mListener?.isSkipStartAndEnd(false)
                                            memoInfo = getInfoEvent {
                                                it["跳过片头片尾"] = 2
                                            }
                                            PlayerConfig.enableSkipStartAndEndPosition = false
                                            PlaySettingHelper.setNeedSkipHeaderAndEnd(false)
                                        }

                                        if (TextUtils.equals(item.content, "拉伸")) {
                                            PlaySettingHelper.setVideoViewLayoutRatioType(
                                                PlayerConstants.ScreenAspectRatio.MATCH_PARENT
                                            )
                                            player?.setScreenAspectRatioType(PlayerConstants.ScreenAspectRatio.MATCH_PARENT)
                                            memoInfo = getInfoEvent {
                                                it["size"] = 3
                                            }
                                        }

                                        if (TextUtils.equals(item.content, "满屏")) {
                                            PlaySettingHelper.setVideoViewLayoutRatioType(
                                                PlayerConstants.ScreenAspectRatio.CENTER_CROP
                                            )
                                            player?.setScreenAspectRatioType(PlayerConstants.ScreenAspectRatio.CENTER_CROP)
                                            memoInfo = getInfoEvent {
                                                it["size"] = 2
                                            }
                                        }
                                        if (TextUtils.equals(item.content, "默认")) {
                                            PlaySettingHelper.setVideoViewLayoutRatioType(
                                                PlayerConstants.ScreenAspectRatio.DEFAULT
                                            )
                                            player?.setScreenAspectRatioType(PlayerConstants.ScreenAspectRatio.DEFAULT)
                                            memoInfo = getInfoEvent {
                                                it["size"] = 1
                                            }
                                        }
                                    }
                                    val pageId =
                                        if (SohuDlnaManger.getInstance()
                                                .getIsDlna()
                                        ) "1063" else "1045"
                                    addPushEvent(10300, CLICK, pathInfo = getInfoEvent {
                                        it["pageId"] = pageId
                                    }, null, memoInfo = memoInfo)
                                    hide()
                                } catch (e: Throwable) {
                                    AppLogger.e(e.localizedMessage)
                                }
                            }
                    }

                    is ScaleContentMenuOnlySeePresenter -> {
                        viewHolder.itemView.findViewById<ConstraintLayout>(R.id.cl_only_see_all)
                            .setOnClickListener {
                                try {
                                    val item = viewHolder.item as ScaleContentOnlySeeMenuItem
                                    when (item.id) {
                                        "0" -> {
                                            isUseOnlySeeModel = false
                                            onlySeeNameImgUrl = null
                                        }

                                        else -> {
                                            isUseOnlySeeModel = true
                                            onlySeeNameImgUrl = item.imagesUrl
                                        }
                                    }

                                    val pos = viewHolder.adapterPosition
                                    if (findNextOnlySeeIndex(mScaleContentOnlySeeMenuItem[pos]) && isUseOnlySeeModel) {
                                        hide()
                                        mPlayMenuConfigListener?.onClickOnlySee(
                                            mScaleContentOnlySeeMenuItem[pos],
                                            0, isNeedDelayChangeNextSegments
                                        )
                                        return@setOnClickListener
                                    }
                                    if (isUseOnlySeeModel) {
                                        ToastUtils.showLongToast(context, "只看" + item.name)
                                    } else {
                                        ToastUtils.showLongToast(context, item.name)
                                    }
                                    LibDeprecatedLogger.d("findNextOnlySeeIndex setSegments  isNeedDelayChangeNextSegments:$isNeedDelayChangeNextSegments  segmentStartIndex:$segmentStartIndex  currentPos:$mCurrentPosition")
                                    hide()
                                    mPlayMenuConfigListener?.onClickOnlySee(
                                        mScaleContentOnlySeeMenuItem[pos],
                                        segmentStartIndex, isNeedDelayChangeNextSegments
                                    )
                                } catch (e: Throwable) {
                                    AppLogger.e(e.localizedMessage)
                                }
                            }
                    }
                }
            }
        })
    }

    fun initMenuData() {
        mMenuItemData.clear()
        mMenuItemData.add(
            ScaleMenuItem(
                UserConstants.MENU_ITEM_EPISODE,
                null,
                TextUtils.equals(UserConstants.MENU_ITEM_EPISODE, selectItemName)
            )
        )
        mMenuItemData.add(
            ScaleMenuItem(
                UserConstants.MENU_ITEM_CLARITY,
                null,
                TextUtils.equals(UserConstants.MENU_ITEM_CLARITY, selectItemName)
            )
        )
        //当 系统播放器时不展示
        if (PlaySettingHelper.getPlaySpeedIsOpen() && !UserConfigHelper.enableSystemPlayer()) {
            mMenuItemData.add(
                ScaleMenuItem(
                    UserConstants.MENU_ITEM_SPEED,
                    null,
                    TextUtils.equals(UserConstants.MENU_ITEM_SPEED, selectItemName)
                )
            )
        }
        if (hasOnlySee == true) {
            //只看他
            mMenuItemData.add(
                ScaleMenuItem(
                    ScaleScreenViewMenuNewView.MENU_ITEM_ONLY_SEE,
                    if (isUseOnlySeeModel) onlySeeNameImgUrl else null,
                    TextUtils.equals(ScaleScreenViewMenuNewView.MENU_ITEM_ONLY_SEE, selectItemName)
                )
            )
        }
        if (!UserConfigHelper.enableSystemPlayer()) {
            mMenuItemData.add(
                ScaleMenuItem(
                    UserConstants.MENU_ITEM_MORE, null, TextUtils.equals(
                        UserConstants.MENU_ITEM_MORE, selectItemName
                    )
                )
            )
            initSpeed()
        }
    }


    fun updateTabItem() {
        mArrayAdapter?.setItems(mMenuItemData, object : DiffCallback<ScaleMenuItem>() {
            override fun areItemsTheSame(oldItem: ScaleMenuItem, newItem: ScaleMenuItem): Boolean {
                return TextUtils.equals(
                    oldItem.name,
                    newItem.name
                )
            }

            override fun areContentsTheSame(
                oldItem: ScaleMenuItem,
                newItem: ScaleMenuItem
            ): Boolean {
                return oldItem.hasSelect == newItem.hasSelect && oldItem.imagesUrl.toString() == newItem.imagesUrl.toString()
            }
        })
    }

    private var mResolutionApp: ResolutionApp? = null

    fun setCurrentClarity(resolutionApp: ResolutionApp) {
        mResolutionApp = resolutionApp
        initClarityData()
    }


    fun setClarity(map: Map<ResolutionApp, VideoPlayResolutionInfo>) {
        resolutionApp.clear()
        resolutionApp.putAll(map)
    }


    fun setCurrentClarity(info: ResolutionInfo) {
        resolutionInfo = info
        initClarityData()
    }

    private var resolutionInfo: ResolutionInfo? = null
    private var resolutionInfos: MutableList<ResolutionInfo> = mutableListOf()
    fun setClarity(infos: MutableList<ResolutionInfo>, aid: Int, vid: Int) {
        this.aid = aid
        this.vid = vid
        resolutionInfos.clear()
        //青少年过滤展示的清晰度
        if (TeenagersManger.isTeenager()) {
            resolutionInfos.addAll(infos.filter { it.teenagerEnable }
                .sortedByDescending { it.priority })
        } else {
            resolutionInfos.addAll(infos.sortedByDescending { it.priority })
        }
    }


    private fun initClarityData() {
        mScaleContentClarityMenuItem.clear()
        resolutionInfos.forEach {
            mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
                hasTitle = it.enableTips
                content = it.name
                titleContent = it.tips ?: ""
                isVip = it.isVip
                isMustLogin = it.isLogin
                //是否显示 会员或者登录角标
                isShowImgTips = if (it.isLogin) {
                    if (it.isVip) {
                        true
                    } else {
                        !hasLogin
                    }
                } else {
                    false
                }
                hasCurrentSelected = it.id == resolutionInfo?.id
//                        (mCurrentClarity == Constant.DEFINITION_HDR || mCurrentClarity == Constant.DEFINITION_HDR265) && !isAutoClarity
            })
        }


//
//
//
//        val isAutoClarity = PlaySettingHelper.getPlayAutoClarityIsOpen()
//
//        if (!TeenagersManger.isTeenager()) {
//            if (resolutionApp[ResolutionApp.APP_ORIGINAL_HDR] != null) {
//                mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
//                    hasTitle = true
//                    content = ResolutionApp.APP_ORIGINAL_HDR.nameValue
//                    titleContent = "了解炫彩HDR"
//                    isVip = true
//                    isMustLogin = true
//                    isShowImgTips = true
//                    hasCurrentSelected = mResolutionApp == ResolutionApp.APP_ORIGINAL_HDR
//                })
//            }
//
//            if (resolutionApp[ResolutionApp.MEDIA_BLUE] != null) {
//                mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
//                    content = ResolutionApp.MEDIA_BLUE.nameValue
//                    isVip = videoType == Constant.DATA_TYPE_VRS //VRS需要会员，PGC不需要
//                    isMustLogin = true
//                    if (videoType == Constant.DATA_TYPE_VRS) {
//                        isShowImgTips = true
//                    } else {
//                        isShowImgTips = !hasLogin
//                    }
//                    hasCurrentSelected = mResolutionApp == ResolutionApp.MEDIA_BLUE
//
//                }
//                )
//            }
//
//
//        }
//
//        if (resolutionApp[ResolutionApp.APP_SUPER] != null) {
//            mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
//                content = ResolutionApp.APP_SUPER.nameValue
//                isVip = false
//                isMustLogin = true
//                if (SohuDlnaManger.getInstance().getIsDlna()) {
//                    isShowImgTips = false
//                } else {
//                    isShowImgTips = !hasLogin
//                }
//                hasCurrentSelected = mResolutionApp == ResolutionApp.APP_SUPER
//
//            }
//            )
//        }
//        if (resolutionApp[ResolutionApp.APP_HIGH] != null) {
//            mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
//                content = ResolutionApp.APP_HIGH.nameValue
//                hasCurrentSelected = mResolutionApp == ResolutionApp.APP_HIGH
//
//            }
//            )
//        }
//
//        if (resolutionApp[ResolutionApp.APP_NORMAL] != null) {
//            mScaleContentClarityMenuItem.add(ScaleContentClarityMenuItem().apply {
//                content = ResolutionApp.APP_NORMAL.nameValue
//                hasCurrentSelected = mResolutionApp == ResolutionApp.APP_NORMAL
//            }
//            )
//        }
    }

    /**
     * 添加清晰度数据
     */
    private fun updateClarityData() {
        mMenuContentArrayAdapter?.setItems(mScaleContentClarityMenuItem, object :
            DiffCallback<ScaleContentClarityMenuItem>() {
            override fun areItemsTheSame(
                oldItem: ScaleContentClarityMenuItem,
                newItem: ScaleContentClarityMenuItem
            ): Boolean {
                return oldItem.content == newItem.content
            }

            override fun areContentsTheSame(
                oldItem: ScaleContentClarityMenuItem,
                newItem: ScaleContentClarityMenuItem
            ): Boolean {
                return oldItem.hasCurrentSelected == newItem.hasCurrentSelected &&
                        oldItem.isShowImgTips == newItem.isShowImgTips &&
                        oldItem.isVip == newItem.isVip &&
                        oldItem.isMustLogin == newItem.isMustLogin &&
                        oldItem.isShowImgTips == newItem.isShowImgTips

            }

        })

    }


    private fun initSpeed() {
        mScaleContentSpeedMenuItem.clear()
        val speed = getCurrentPlayRateToString()
        AppLogger.v("获取设置的倍速:${speed}")
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = UserConstants.PLAY_RATE_LOW
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = UserConstants.PLAY_RATE_NORMAL
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = UserConstants.PLAY_RATE_HIGH
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = UserConstants.PLAY_RATE_ONEHALF
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
        mScaleContentSpeedMenuItem.add(ScaleContentSpeedMenuItem().apply {
            content = UserConstants.PLAY_RATE_TWO
            hasCurrentSelected = TextUtils.equals(speed, content)
        }
        )
    }

    private fun getCurrentPlayRateToString(): String {
        return when (PlaySettingHelper.getPlaySpeed()) {
            UserConstants.PE_PLAY_RATE_LOW -> UserConstants.PLAY_RATE_LOW
            UserConstants.PE_PLAY_RATE_NORMAL -> UserConstants.PLAY_RATE_NORMAL
            UserConstants.PE_PLAY_RATE_HIGH -> UserConstants.PLAY_RATE_HIGH
            UserConstants.PE_PLAY_RATE_ONEHALF -> UserConstants.PLAY_RATE_ONEHALF
            UserConstants.PE_PLAY_RATE_TWO -> UserConstants.PLAY_RATE_TWO
            else -> {
                AppLogger.v("获取倍速出错！")
                UserConstants.PLAY_RATE_NORMAL
            }
        }
    }

    private fun getCurrentPlayRateStringToInt(str: String?): Float {
        return when (str) {
            UserConstants.PLAY_RATE_LOW -> UserConstants.PE_PLAY_RATE_LOW
            UserConstants.PLAY_RATE_NORMAL -> UserConstants.PE_PLAY_RATE_NORMAL
            UserConstants.PLAY_RATE_HIGH -> UserConstants.PE_PLAY_RATE_HIGH
            UserConstants.PLAY_RATE_ONEHALF -> UserConstants.PE_PLAY_RATE_ONEHALF
            UserConstants.PLAY_RATE_TWO -> UserConstants.PE_PLAY_RATE_TWO
            else -> {
                AppLogger.v("切换倍速出错！ str:$str")
                UserConstants.PE_PLAY_RATE_NORMAL
            }
        }
    }


    //更新倍速
    private fun updateSpeed() {
        mMenuContentArrayAdapter?.setItems(mScaleContentSpeedMenuItem, object :
            DiffCallback<ScaleContentSpeedMenuItem>() {
            override fun areItemsTheSame(
                oldItem: ScaleContentSpeedMenuItem,
                newItem: ScaleContentSpeedMenuItem
            ): Boolean {
                return oldItem.content == newItem.content
            }

            override fun areContentsTheSame(
                oldItem: ScaleContentSpeedMenuItem,
                newItem: ScaleContentSpeedMenuItem
            ): Boolean {
                return oldItem.hasCurrentSelected == newItem.hasCurrentSelected
            }

        })
    }


    private fun initMore() {
        mScaleContentMoreMenuItem.clear()
        if (videoOrientation == 0) {
            mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
                hasTitle = true
                titleContent = "画面尺寸"
                content = "默认"
                hasCurrentSelected =
                    PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.DEFAULT
            }
            )
            mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
                content = "满屏"
                hasCurrentSelected =
                    PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.CENTER_CROP
            }
            )
            mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
                content = "拉伸"
                hasCurrentSelected =
                    PlaySettingHelper.getVideoViewLayoutRatioType() == PlayerConstants.ScreenAspectRatio.MATCH_PARENT
            }
            )
        }
        mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
            hasTitle = true
            titleContent = "片头片尾"
            content = "跳过"
            hasCurrentSelected = PlaySettingHelper.getNeedSkipHeaderAndEnd()
        }
        )
        mScaleContentMoreMenuItem.add(ScaleContentMoreMenuItem().apply {
            content = "不跳过"
            hasCurrentSelected = !PlaySettingHelper.getNeedSkipHeaderAndEnd()
        }
        )
    }

    //更多功能
    private fun updateMore() {
        mMenuContentArrayAdapter?.setItems(mScaleContentMoreMenuItem, object :
            DiffCallback<ScaleContentMoreMenuItem>() {
            override fun areItemsTheSame(
                oldItem: ScaleContentMoreMenuItem,
                newItem: ScaleContentMoreMenuItem
            ): Boolean {
                return oldItem.content == newItem.content
            }

            override fun areContentsTheSame(
                oldItem: ScaleContentMoreMenuItem,
                newItem: ScaleContentMoreMenuItem
            ): Boolean {
                return oldItem.hasCurrentSelected == newItem.hasCurrentSelected

            }

        })

    }


    /**
     * ----------- 以下代码为 剧集代码 -------------------------
     */

    //播放器数据加载后使用需要重新加载数据
    fun episodeLayoutUpdate(
        aid: Int,
        vid: Int,
        dataType: Int,
        catecode: Int,
        sortOrder: Int,
        layoutType: Int,
        isTrailer: Boolean,
        totalCount: Int,
        videoOrder: Int,
        recommendList: List<AlbumInfoRecommendModel>?
    ) {
        mVideoOrder = videoOrder
        mVideoCount = totalCount
        if (totalCount >= 1) {
            if (true
            ) {
                episodeLayout?.setVisibility(VISIBLE)
                episodeLayout?.setEpisodeIsSelected(true)
                episodeLayout?.initFromMenuView(
                    aid,
                    vid,
                    dataType,
                    catecode,
                    sortOrder,
                    layoutType,
                    isTrailer,
                    totalCount,
                    videoOrder,
                    recommendList
                )
            } else {
                episodeLayout?.updateSelectAfterPlay(videoOrder, true, vid)
            }
        } else {
            episodeLayout?.setVisibility(GONE)
            episodeLayout?.setEpisodeType(layoutType)
        }
    }

    fun getEpisodeSortOrder(): Int {
        return episodeLayout?.getSortOrder() ?: 1
    }

    fun getEpisodeType(): Int {
        return episodeLayout?.getEpisodeType() ?: -1
    }

    fun getEpisodeTotalCount(): Int {
        return episodeLayout?.getTotalCount() ?: 0
    }

    fun getPageSize(): Int {
        return episodeLayout?.getPageSize() ?: 3
    }

    fun isLastEpisode(videoOrder: Int): Int {
        return episodeLayout?.isLastEpisode(videoOrder) ?: -1
    }

    fun getEpisodeVideoOrder(): Int {
        return if (episodeLayout?.visibility == VISIBLE) {
            episodeLayout?.episodeVideoOrder ?: 1
        } else {
            1
        }
    }


    fun setDefaultFocus(vid: Int) {
        if (episodeLayout?.visibility == VISIBLE) {
            episodeLayout?.updateSelectAfterPlay(mVideoOrder, true, vid)
            episodeLayout?.setEpisodeFragmentFoucus()
        }
    }


    fun setFocusBorderView(focusBorderView: FocusBorderView) {
        episodeLayout?.setFocusBorderView(focusBorderView)
    }


    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        AppLogger.i(
            "ScaleMenu dispatchKeyEvent action :${event?.action} -- keyCode is :${event?.keyCode}"
        )
        if (event?.action == KeyEvent.ACTION_DOWN) {
            showComponent()
            when (event?.keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    hide()
                    return true
                }

//                KeyEvent.KEYCODE_DPAD_UP -> {
//                    //查找down事件下一个焦点是否为空 判断拦截  主要目的是拦截 播控菜单出界 不显示焦点问题
//                    FocusFinder.getInstance().findNextFocus(this, findFocus(), View.FOCUS_UP)
//                        ?: return true
//
//                    //判断当前是否是只看他功能Item
//                    if (TextUtils.equals(
//                            mMenuItemData[mViewBinding.leanbackScaleMenuView.selectedPosition].name,
//                            MENU_ITEM_ONLY_SEE
//                        )
//                    ) {
//                        //设置默认的只看他焦点选中
//                        var selectPosition = -1
//                        //查找已经选中的 位置
//                        for (item in mScaleContentOnlySeeMenuItem) {
//                            selectPosition++
//                            //找到重新设置焦点选中
//                            if (item.hasCurrentSelected) {
//                                mViewBinding.leanbackScaleContentMenuView.post {
//                                    try {
//                                        mViewBinding.leanbackScaleContentMenuView.scrollToPosition(
//                                            selectPosition
//                                        )
//                                    } catch (e: Exception) {
//                                        AppLogger.e("leanbackScaleContentMenuView scrollToPosition  Exception :$e")
//                                    }
//                                }
//                                break
//                            }
//                        }
//                    }
//                    return super.dispatchKeyEvent(event)
//                }

                KeyEvent.KEYCODE_DPAD_RIGHT,
                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    hasSelectPushImpEvent = false
                    menuHasClear = false
                    val focusNext = if (event.keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                        View.FOCUS_RIGHT
                    } else {
                        View.FOCUS_LEFT
                    }
                    if (tabMenu?.hasFocus() == true && tabContext?.hasFocus() == false && FocusFinder.getInstance()
                            .findNextFocus(this, findFocus(), focusNext) != null
                    ) {
                        hasSelectPushImpEvent = true
                        menuHasClear = true
                        mMenuContentArrayAdapter?.clear()
                        LibDeprecatedLogger.i(" dispatchKeyEvent fun mMenuContentArrayAdapter clear ")
                    }
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    val nextFocused = FocusFinder.getInstance().findNextFocus(
                        this, findFocus(), View.FOCUS_DOWN
                    )
                    if (nextFocused == null) {
                        return true
                    } else {
                        nextFocused.requestFocus()
                        return true
                    }

                }

                KeyEvent.KEYCODE_DPAD_UP -> {
                    val nextFocused = FocusFinder.getInstance().findNextFocus(
                        this, findFocus(), View.FOCUS_UP
                    )
                    if (nextFocused == null) {
                        return true
                    } else {
                        if (nextFocused is EpisodeHorzTabView.TabView) {
                            return super.dispatchKeyEvent(event) //选集区间不拦截焦点
                        } else {
                            nextFocused.requestFocus()
                            return true
                        }
                    }
                }
            }
        }
        return super.dispatchKeyEvent(event)
    }

    override fun attachController(controller: ShVideoViewController) {
    }

    override fun onVideoDropFrame(msg: String?) {
        AppLogger.d("onVideoDropFrame setting speed:${PlaySettingHelper.getPlaySpeed()} ")
        if (PlaySettingHelper.getPlaySpeed() > UserConstants.PE_PLAY_RATE_NORMAL) {
            player?.speed = UserConstants.PE_PLAY_RATE_NORMAL
            setPlaySpeed(UserConstants.PE_PLAY_RATE_NORMAL)
            ToastUtils.showToast(
                getContext().getApplicationContext(),
                "设备能力限制播放效果，已为您切回1倍速。"
            );
        }
    }


    override fun onScreenModeChanged(screenMode: Int) {
        if (screenMode != PlayerConstants.ScreenMode.FULL) {
            hide()
        }
    }

    fun setOnlySee(sees: MutableList<ScaleContentOnlySeeMenuItem>?) {
        sees?.let {
            mScaleContentOnlySeeMenuItem.clear()
            mScaleContentOnlySeeMenuItem.addAll(sees)
        }
        AppLogger.i("ScaleContentOnlySeeMenuItem data is ${mScaleContentOnlySeeMenuItem.toString()}")
    }


    fun setCurrentOnlySeeImg(url: MutableList<String>?) {
        onlySeeNameImgUrl = url
    }

    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        super.onPlayStateChanged(playState, extras)
        if (playState.playerIsNotStart()) {
            hideComponent()
        }
    }


    private fun updateOnlySee() {
        mMenuContentArrayAdapter?.setItems(mScaleContentOnlySeeMenuItem, object :
            DiffCallback<ScaleContentOnlySeeMenuItem>() {

            override fun areItemsTheSame(
                oldItem: ScaleContentOnlySeeMenuItem,
                newItem: ScaleContentOnlySeeMenuItem
            ): Boolean {
                return oldItem.name == newItem.name
            }

            override fun areContentsTheSame(
                oldItem: ScaleContentOnlySeeMenuItem,
                newItem: ScaleContentOnlySeeMenuItem
            ): Boolean {
                return oldItem.hasCurrentSelected == newItem.hasCurrentSelected
            }

        })
    }

    fun show() {
        hasSelectPushImpEvent = true
        initMenuData()
        updateTabItem()
        tabMenu?.selectedPosition = oldPosition
        updateSelect(selectItemName)
        tabMenu?.requestFocus()
        if (visibility != VISIBLE) {
            showComponent()
        }
//        controller?.show()
    }

    override fun handleComponentVisibilityChanged(isVisible: Boolean) {
        if (isVisible) {
            visible()
        } else {
            gone()
        }
        mIControlComponentHide?.onMenuControlIsVisible(isVisible)
    }


    fun hide() {
        hideComponent()
//        controller?.hide()
    }

    private var isOnlySeeAutoSeek = true

    private var isSendMessageCompleted = true
    var nextEpisodeInfo: MutableList<Any> = mutableListOf<Any>()

    var index = 0

    override fun onProgressChanged(duration: Long, position: Long) {
        super.onProgressChanged(duration, position)
        findCurrentOnlySeeIndex()
    }

    /**
     * 片段集合的起始位置
     */
    @Volatile
    private var segmentStartIndex = 0
    private var segmentNextStartIndex = 0

    /**
     * 是否需要延迟手动判断下一个片段内再设置segments 和其实位置
     * true 适用当前播放位置在当前segment片段和下一个segment片段时间段在重合范围内
     * false 直接设置segments
     */
    private var isNeedDelayChangeNextSegments = false

    /**
     * 实时进度调用 当前是否在最后一个片段内 并当 isNeedDelayChangeNextSegments 为 true 时 片段结束时 手动设置segments
     */
    @Synchronized
    private fun findCurrentOnlySeeIndex() {
        val data: List<VideoInfoOnlySeeTimeItem> =
            mCurrentSelectOnlySeeMenuItem?.timeArray
                ?: return

        if (isNeedDelayChangeNextSegments) return
        val position = AdTsManger.getInstants().getCalculateOnlySeeTime(mCurrentPosition * 1000)
        for (index in data.indices) {
            //片段起始位置和结束位置
            var start = data[index].start
            var end = data[index].end
            //            int mCurrent= (int) AdTsManger.getInstants().getCalculateHistoryTime(end,true);
            //修正只看他  的 剧享广告增加时间
            start = AdTsManger.getInstants().getCalculateOnlySeeTime(start * 1000)
            end = AdTsManger.getInstants().getCalculateOnlySeeTime(end * 1000)
            //最后一个片段
            LibDeprecatedLogger.d("findCurrentOnlySeeIndex setSegments  segmentStartIndex:$segmentStartIndex  currentPos:$position  start:$start end:$end")
            if (index == data.size - 1) {
                //可以提示点位判断
                if (position >= end - 2 && end > 2) {
                    //如果已经发送了提前结束消息 直接返回
                    segmentStartIndex = index
                    if (isSendMessageCompleted) {
                        return
                    }
                    //最后一集继续播放非只看他片段
                    if (((nextEpisodeInfo.size == 1))
                    ) {
                        return
                    }
                    //如果不是最后一集 调用播放完成处理下一集  否则继续播放非只看他片段
                    if ((nextEpisodeInfo.size == 5)) {
                        //toast 提示
                        val toastName: String? =
                            mCurrentSelectOnlySeeMenuItem?.name
                        if (toastName?.isEmpty() == false) {
                            ToastUtils.showLongToast(
                                context,
                                "本集" + toastName + "已播完，即将播放下一集相关片断！"
                            )
                        }
                        LibDeprecatedLogger.d("findCurrentOnlySeeIndex postDelayed  fun completion：$segmentStartIndex")
                        isSendMessageCompleted = true
                        mHandler.postDelayed(
                            mCompletionRunnable!!, 2000
                        )
                        //调用播放完成逻辑
                    }
                }

            } else {
                if (isNeedDelayChangeNextSegments && position >= end && segmentStartIndex <= index) {
                    var segments: MutableList<Long>? = null
                    mCurrentSelectOnlySeeMenuItem?.let {
                        segments = mutableListOf()
                        it.timeArray?.forEach { time ->
                            segments?.add(
                                AdTsManger.getInstants().getCalculateOnlySeeTime(time.start * 1000)
                            )
                            segments?.add(
                                AdTsManger.getInstants().getCalculateOnlySeeTime(time.end * 1000)
                            )
                        }
                        if (nextEpisodeInfo.size == 1 && segments!!.size > 0 && videoLength > segments!![(segments!!.size - 1)] / 1000) {
                            videoLength?.toLong()?.let {
                                segments!!.removeAt((segments!!.size - 1))
                                segments!!.add(it * 1000)
                            }
                        }
                    }
                    segmentStartIndex = index + 1
                    player?.setSegments(
                        segments, mCurrentSelectOnlySeeMenuItem?.timeArray?.size ?: 0,
                        segmentStartIndex
                    )
                    isNeedDelayChangeNextSegments = false
                    LibDeprecatedLogger.d("findCurrentOnlySeeIndex setSegments  fun segmentStartIndex：$segmentStartIndex")
                    return
                }
                if (!isNeedDelayChangeNextSegments) {
                    if (position in start..end) {
                        segmentStartIndex = index
                        return
                    }
                    if (position < start) {
                        segmentStartIndex = index
                        return
                    }
                }
            }
        }
    }

    inner class CompletionRunnable : Runnable {
        override fun run() {
            LibDeprecatedLogger.d("findCurrentOnlySeeIndex last  fun completion")
            segmentStartIndex = 0
//            controller?.stopUpdateProgress()
//            mCurrentPosition = 0
            player?.completion()
        }
    }


    @Synchronized
    private fun findNextOnlySeeIndex(nextSelectOnlySeeMenuItem: ScaleContentOnlySeeMenuItem): Boolean {
        val data: List<VideoInfoOnlySeeTimeItem> =
            nextSelectOnlySeeMenuItem.timeArray
                ?: return false
        val position = AdTsManger.getInstants().getCalculateOnlySeeTime(mCurrentPosition * 1000)
        if (position > data[data.size - 1].end * 1000) {
            isNeedDelayChangeNextSegments = true
            segmentNextStartIndex = data.size - 1
            //最后一集继续播放非只看他片段
            if (((nextEpisodeInfo.size == 1))
            ) {
                return true
            }
            //toast 提示
            val toastName: String? =
                nextSelectOnlySeeMenuItem.name
            if (toastName?.isEmpty() == false) {
                ToastUtils.showLongToast(
                    context,
                    "本集" + toastName + "已播完，即将播放下一集相关片断！"
                )
            }
            LibDeprecatedLogger.d("findCurrentOnlySeeIndex postDelayed  fun completion：$segmentNextStartIndex")
            mHandler.postDelayed(
                mCompletionRunnable!!, 2000
            )
            return true
        }
        for (index in data.indices) {
            //片段起始位置和结束位置
            var start = data[index].start
            var end = data[index].end
            //            int mCurrent= (int) AdTsManger.getInstants().getCalculateHistoryTime(end,true);
            //修正只看他  的 剧享广告增加时间
            start = AdTsManger.getInstants().getCalculateOnlySeeTime(start * 1000)
            end = AdTsManger.getInstants().getCalculateOnlySeeTime(end * 1000)
            if (position in start..<end) {
                segmentNextStartIndex = index
                isNeedDelayChangeNextSegments = true
                segmentStartIndex = segmentNextStartIndex
                LibDeprecatedLogger.d("findNextOnlySeeIndex  fun segments in StartIndex：$segmentNextStartIndex")
                return false
            }
            if (position < start) {
                segmentNextStartIndex = index
                isNeedDelayChangeNextSegments = true
                segmentStartIndex = segmentNextStartIndex
                LibDeprecatedLogger.d("findNextOnlySeeIndex  fun segments < StartIndex：$segmentNextStartIndex")
                isNeedDelayChangeNextSegments = false
                return false
            }
        }
        return false
    }

    /**
     * 查找只看他对应片段并seek
     */


    fun setPlayMenuConfigListener(listener: PlayMenuConfigListener) {
        mPlayMenuConfigListener = listener
    }

    interface PlayMenuConfigListener {
        /**
         * 只看他
         */
        fun onClickOnlySee(
            item: ScaleContentOnlySeeMenuItem,
            index: Int,
            isNeedDelayChangeNextSegments: Boolean,
        )


        fun onSkipStartAndEnd()

        /**
         * 清晰度
         */
        fun onClickResolution(resolutionApp: ResolutionInfo)


        fun onClickResolutionStartLogin(startResolutionApp: ResolutionInfo)

        /**
         * 了解 hdr
         */
        fun onClickHdrTips()
    }


}