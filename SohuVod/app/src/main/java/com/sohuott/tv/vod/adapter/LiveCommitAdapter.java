package com.sohuott.tv.vod.adapter;

import android.graphics.Color;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.model.LiveCommitModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by yizhang210244 on 2018/5/3.
 */

public class LiveCommitAdapter extends RecyclerView.Adapter{

    private List<LiveCommitModel> mLiveCommitModelList;
    private String mNickName = "";

    public LiveCommitAdapter(List<LiveCommitModel> liveCommitModelList) {
        mLiveCommitModelList = liveCommitModelList;
    }

    public void setNickName(String nickName) {
        mNickName = nickName;
    }

    public void addLiveCommit(LiveCommitModel liveCommitModel){
        if(liveCommitModel != null){
            int pos = 0;
            if(mLiveCommitModelList == null){
                mLiveCommitModelList = new ArrayList<>();
            }
            pos = mLiveCommitModelList.size();
            mLiveCommitModelList.add(liveCommitModel);
            notifyItemInserted(pos);
        }
    }

    private void setSpanBuilder(TextView textView,String username,String commitInfo){
        String first;
        String second;
        if(TextUtils.isEmpty(username)){
            first = ":";
        }else {
            first = username + ":";
        }
        if(TextUtils.isEmpty(commitInfo)){
            second = "";
        }else {
            second = "  "+commitInfo;
        }

        ForegroundColorSpan normalSpan = new ForegroundColorSpan(Color.parseColor("#f3f3f3"));

        int yellow = Color.parseColor("#e7a26f");
        if(TextUtils.equals(mNickName,username)){
            yellow = Color.parseColor("#2ab329");
        }
        ForegroundColorSpan yellowSpan = new ForegroundColorSpan(yellow);
        SpannableStringBuilder builder = new SpannableStringBuilder();
        builder.append(first).append(second);

        builder.setSpan(yellowSpan, 0, first.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        builder.setSpan(normalSpan, first.length(), builder.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        textView.setText(builder);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.commit_list_item, null);
        return new ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        LiveCommitModel liveCommitModel = mLiveCommitModelList.get(position);
        if(viewHolder != null && liveCommitModel != null){
            setSpanBuilder(viewHolder.mTextView,liveCommitModel.getUsername(),liveCommitModel.getCommit());
        }
    }

    @Override
    public int getItemCount() {
        if(mLiveCommitModelList != null){
            return mLiveCommitModelList.size();
        }
        return 0;
    }

    class ViewHolder extends RecyclerView.ViewHolder{
        private TextView mTextView;
        public ViewHolder(View itemView) {
            super(itemView);
            mTextView = (TextView) itemView.findViewById(R.id.commit_text_id);
        }
    }
}
