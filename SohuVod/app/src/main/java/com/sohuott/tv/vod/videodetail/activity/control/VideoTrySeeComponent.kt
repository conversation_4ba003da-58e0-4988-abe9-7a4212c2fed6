package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.videodetail.activity.playerIsPlaying
import com.sohuott.tv.vod.videodetail.activity.state.TrySeeData

/**
 * 试看
 * 版权提示
 * 小窗备案号
 */
class VideoTrySeeComponent constructor(
    context: Context
) : BaseControlComponent(context) {
    private var fullTryTips: ConstraintLayout? = null
    private var scaleTryTipsLayout: LinearLayout? = null
    private var scaleTips: TextView? = null
    private var tryTipsContent: TextView? = null
    private var btnTryTipsContent: TextView? = null
    private var scaleTryTipsTime: TextView? = null

    private var isAlreadyShowTips = false

    var trySeeData: TrySeeData? = null
        set(value) {
            field = value
            refreshLayout()
        }

    /**
     * 版权提示展示线程
     */
    private var mScaleTipsRunnable: ScaleTipsRunnable? = null

    private var screenMode: Int? = PlayerConstants.ScreenMode.NORMAL


    private var isSingle = false
    private var isMember = false
    private var isTicket = false
    private var mTicketCount = 0

    var tv: String = ""
    var btn: String = ""
    var copyrightTips: String? = null
        set(value) {
            removeCallbacks(mScaleTipsRunnable)
            if (value?.isNotEmpty() == true) {
                mScaleTipsRunnable = ScaleTipsRunnable()
            }
            isAlreadyShowTips = false
            refreshLayout()
            field = value
        }

    /**
     * 是否可以显示 南传小窗备案号
     */
    private var isCanShowRecordNumberTv = false
        set(value) {
            field = value
            if (field) {
                scaleRecordName?.visible()
                if (!hasShow()) {
                    show()
                }
            } else {
                if (hasShow()) {
                    scaleRecordName?.gone()
                }
            }
        }

    //南传备案号展示
    var recordNumber: String? = null //南传备案号
        set(value) {
            field = value
            scaleRecordName?.text = field
        }
    private var scaleRecordName: TextView? = null //南传备案号

    private var currentPos: Long = 0

    init {
        layoutInflater.inflate(R.layout.video_component_try_see, this, true)
        fullTryTips = findViewById(R.id.cl__video_full_try_tips_layout)
        scaleRecordName = findViewById(R.id.scale_record_name)
        scaleTryTipsLayout = findViewById(R.id.ll_scale_tips_layout)
        scaleTips = findViewById(R.id.scale_tips)
        tryTipsContent = findViewById(R.id.tv_try_tips_content)
        btnTryTipsContent = findViewById(R.id.btn_try_tips_content)
        scaleTryTipsTime = findViewById(R.id.scale_tips_time)
        gone()
    }

    override fun onScreenModeChanged(screenMode: Int) {
        super.onScreenModeChanged(screenMode)
        this.screenMode = screenMode
        setIsCanShowRecordNumberTv()
        refreshLayout()
    }

    override fun onProgressChanged(duration: Long, position: Long) {
        currentPos = position / 1000
        if (recordNumber.isNullOrEmpty()) {
            isCanShowRecordNumberTv = false
            return
        }
        setIsCanShowRecordNumberTv()
    }

    private fun setIsCanShowRecordNumberTv() {
        isCanShowRecordNumberTv = currentPos < 4 && screenMode == PlayerConstants.ScreenMode.NORMAL
    }

    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        super.onPlayStateChanged(playState, extras)
        if (playState.playerIsPlaying()) {
            refreshLayout()
        } else {
            hide()
        }
    }


    private fun refreshLayout() {
        if (screenMode == PlayerConstants.ScreenMode.FULL && isCanFullShow) {
            scaleTryTipsLayout?.gone()
            if (trySeeData?.enableTry == true) {
                tryTipsContent?.text = tv
                btnTryTipsContent?.text = btn
                fullTryTips?.visible()
                show()
            } else {
                fullTryTips?.gone()
                hide()
            }
            return
        } else if (screenMode == PlayerConstants.ScreenMode.NORMAL) {
            hide()
            fullTryTips?.gone()
            scaleTryTipsLayout?.gone()
            scaleTips?.gone()
            scaleTryTipsTime?.gone()
            if (trySeeData?.enableTry == true && trySeeData?.tryTimeText?.isNotEmpty() == true) {
                scaleTryTipsTime?.text = "试看${trySeeData?.tryTimeText}分钟"
                scaleTryTipsTime?.visible()
                scaleTryTipsLayout?.visible()
                show()
            }
            if (copyrightTips?.isNotEmpty() == true) {
                if (isAlreadyShowTips) {
                    return
                }
                scaleTips?.text = copyrightTips
                scaleTips?.visible()
                scaleTryTipsLayout?.visible()
                show()
                if (!isAlreadyShowTips) {
                    postDelayed(mScaleTipsRunnable, 5000)
                }
            } else {
                hide()
            }
        } else {
            hide()
        }
    }

    fun setMemberPayInfo(
        isSingle: Boolean?,
        isMember: Boolean?,
        isTicket: Boolean?,
        mTicketCount: Int?
    ) {
        this.isSingle = isSingle ?: false
        this.isTicket = isTicket ?: false
        this.isMember = isMember ?: false
        this.mTicketCount = mTicketCount ?: 0
        setTryStartText()
        refreshLayout()
    }

    private fun setTryStartText() {
        if (!UserLoginHelper.getInstants().getIsLogin()) {
            if (isSingle && isMember && isTicket) {
                tv = "正在试看，按OK键开通会员用券观看全片"
                btn = "立即开通"
            } else if (isSingle && isMember) {
                tv = "正在试看，按OK键开通会员观看全片"
                btn = "立即开通"
            } else if ((isSingle || isMember) && isTicket) {
                tv = "正在试看，按OK键开通会员用券观看全片"
                btn = "立即开通"
            } else if (isMember) {
                tv = "正在试看，按OK键开通会员观看全片"
                btn = "立即开通"
            } else if (isSingle) {
                tv = "正在试看，按OK键购买后观看全片"
                btn = "立即购买"
            } else if (isTicket) {
                tv = "正在试看，按OK键开通会员用券观看全片"
                btn = "立即开通"
            }
        } else {
            if (UserLoginHelper.getInstants().isVip()) {
                if (isSingle && isMember && isTicket) {
                    tv = "正在试看，按OK键用券观看全片"
                    btn = "用券观看"
                } else if ((isSingle || isMember) && isTicket) {
                    if (mTicketCount > 0) {
                        tv = "正在试看，按OK键用券观看全片"
                        btn = "用券观看"
                    } else {
                        tv = "正在试看，按0K键续费会员用卷观看全片"
                        btn = "续费赠券"
                    }
                } else if (isSingle) {
                    tv = "正在试看，按OK键购买后观看全片"
                    btn = "立即购买"
                } else if (isTicket) {
                    if (mTicketCount > 0) {
                        tv = "正在试看，按OK键用券观看全片"
                        btn = "用券观看"
                    } else {
                        tv = "正在试看，按0K键续费会员用卷观看全片"
                        btn = "续费赠券"
                    }
                }
            } else {
                if ((isSingle || isMember) && isTicket) {
                    if (mTicketCount > 0) {
                        tv = "正在试看，按OK键续费激活观影券观看全片"
                        btn = "立即续费"
                    } else {
                        tv = "正在试看，按OK键开通会员用券观看全片。"
                        btn = "立即开通"
                    }
                } else if (isSingle && isMember) {
                    tv = "正在试看，按OK键开通会员观看全片。"
                    btn = "立即开通"
                } else if (isMember) {
                    tv = "正在试看，按OK键开通会员观看全片。"
                    btn = "立即开通"
                } else if (isSingle) {
                    tv = "正在试看，按OK键购买后观看全片。"
                    btn = "立即购买"
                } else if (isTicket) {
                    if (mTicketCount > 0) {
                        tv = "正在试看，按OK键续费激活观影券观看全片。"
                        btn = "立即续费"
                    } else {
                        tv = "正在试看，按OK键开通会员用券观看全片。"
                        btn = "立即开通"
                    }
                }
            }
        }
    }

    private fun hide() {
        if (isCanShowRecordNumberTv) {
            return
        }
        gone()
    }

    private fun show() {
        visible()
    }

    fun hasShow(): Boolean {
        return isVisible
    }

    private var isCanFullShow: Boolean = false

    fun canFullShow(isCan: Boolean) {
        this.isCanFullShow = isCan
        if (isCan) {
            refreshLayout()
        } else {
            hide()
        }
    }

    /**
     * 版权提示 Runnable
     */
    inner class ScaleTipsRunnable() : Runnable {
        override fun run() {
            isAlreadyShowTips = true
            scaleTips?.gone()
            mScaleTipsRunnable = null
        }
    }
}