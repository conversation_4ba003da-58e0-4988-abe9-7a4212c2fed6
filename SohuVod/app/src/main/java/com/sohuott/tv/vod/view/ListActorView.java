package com.sohuott.tv.vod.view;

import androidx.recyclerview.widget.RecyclerView;

import com.sohuott.tv.vod.lib.model.ListAlbumModel;

import java.util.List;

/**
 * Created by XiyingCao on 16-1-7.
 */
public interface ListActorView {

    void add(List<ListAlbumModel> models);

    void showLoading();

    void hideLoading();

    void activateLastItemViewListener();

    void disableLastItemViewListener();

    void onError();

    void setListTitle(String title);

    void setListSubTitle(String subTitle);

    void setListCategory(String category);

    void setBackground(String url);

    RecyclerView.Adapter getAdapter();
}
