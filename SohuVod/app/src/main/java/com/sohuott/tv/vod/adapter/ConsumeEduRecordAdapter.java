package com.sohuott.tv.vod.adapter;

import static com.sohuott.tv.vod.activity.ListEduUserRelatedActivity.LIST_INDEX_CONSUME_RECORD;

import android.content.Context;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.sohu.lib_utils.FormatUtils;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ConsumeRecord;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.FocusBorderView;

import java.util.List;

public class ConsumeEduRecordAdapter extends RecyclerView.Adapter<ConsumeEduRecordAdapter.ConsumeRecordViewHolder> {

    private static final String TAG = ConsumeEduRecordAdapter.class.getSimpleName();
    private static final int STATUS_0_1 = -1;
    private static final int STATUS_0 = 0;
    private static final int STATUS_1 = 1;
    private static final int STATUS_2 = 2;
    private static final int STATUS_4 = 4;
    private static final int STATUS_8 = 8;

    private Context mContext;

    private FocusBorderView mFocusBorderView;
    private RecyclerView mRecyclerView;

    private List<ConsumeRecord.DataEntity.ContentEntity> mDataSource;

    private ListEduRecordAdapter.FocusController mFocusController;

    public ConsumeEduRecordAdapter(Context context, RecyclerView recyclerView) {
        this.mContext = context;
        this.mRecyclerView = recyclerView;
    }


    @Override
    public ConsumeRecordViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_consume_record, parent, false);
        ConsumeRecordViewHolder viewHolder = new ConsumeRecordViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(ConsumeRecordViewHolder holder, int position) {
        AppLogger.d(TAG, "position ? " + position);
        AppLogger.d(TAG, "holder.getAdapterPosition() ? " + holder.getAdapterPosition());
        ConsumeRecord.DataEntity.ContentEntity consumeRecord = mDataSource.get(holder.getAdapterPosition());
        int statusCode;
        holder.itemView.setTag(consumeRecord.getAid());
        holder.tv_cr_order_id.setText(consumeRecord.getOrderSn());
        holder.tv_cr_product_name.setText(consumeRecord.getTitle());
        holder.tv_cr_create_time.setText(FormatUtils.formatDate2(
                consumeRecord.getUpdatedAt() > 0 ? consumeRecord.getUpdatedAt() : consumeRecord.getCreatedAt()));

        statusCode = TextUtils.equals(consumeRecord.getPayMethod(), "ticket") ? STATUS_8 : consumeRecord.getStatus();
        holder.tv_cr_order_status.setText(getStatusName(statusCode));
        holder.tv_cr_order_price.setText(getOrderPrice(statusCode, consumeRecord.getPrice()));
        holder.itemView.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.bg_item_consume_record));
    }

    @Override
    public int getItemCount() {
        int itemCount = 0;
        if (mDataSource != null) {
            itemCount = mDataSource.size();
        }
        AppLogger.d(TAG, "itemCount ? " + itemCount);
        return itemCount;
    }

    public void setFocusController(ListEduRecordAdapter.FocusController focusController) {
        this.mFocusController = focusController;
    }

    public void setDataSource(List<ConsumeRecord.DataEntity.ContentEntity> dataSource) {
        this.mDataSource = dataSource;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.mFocusBorderView = focusBorderView;
    }

    public void releaseAll() {
        mContext = null;
        mRecyclerView = null;
        mFocusController = null;
        if (mDataSource != null) {
            mDataSource.clear();
            mDataSource = null;
        }
    }

    private String getStatusName(int statusCode) {
        // 0：未支付，1：支付失败，2：支付成功授权失败，4：支付成功授权成功，8：兑换成功
        switch (statusCode) {
            case STATUS_0_1:
                return mContext.getApplicationContext().getResources().getString(R.string.txt_activity_consume_record_status_unavailable);
            case STATUS_0:
                return mContext.getApplicationContext().getResources().getString(R.string.txt_activity_consume_record_status_wait_for_paying);
            case STATUS_1:
                return mContext.getApplicationContext().getResources().getString(R.string.txt_activity_consume_record_status_fail_paying);
            case STATUS_2:
                return mContext.getApplicationContext().getResources().getString(R.string.txt_activity_consume_record_status_fail_sending);
            case STATUS_4:
                return mContext.getApplicationContext().getResources().getString(R.string.txt_activity_consume_record_status_success_paying);
            case STATUS_8:
                return mContext.getApplicationContext().getResources().getString(R.string.txt_activity_consume_record_status_success_paying_for_ticket);
            default:
                return null;
        }
    }

    private String getOrderPrice(int statusCode, int price) {
        if (statusCode == STATUS_4 || statusCode == STATUS_2) {
            if (price % 100 == 0) {
                return price / 100 + ".0";
            } else {
                return String.valueOf(price * 0.01);
            }
        } else if (statusCode == STATUS_8) {
            return mContext.getApplicationContext().getResources().getString(R.string.txt_activity_consume_record_one_ticket);
        } else {
            return "0";
        }
    }

    class ConsumeRecordViewHolder extends RecyclerView.ViewHolder {

        TextView tv_cr_order_id, tv_cr_product_name, tv_cr_create_time, tv_cr_order_status, tv_cr_order_price;

        public ConsumeRecordViewHolder(final View itemView) {
            super(itemView);
            tv_cr_order_id = (TextView) itemView.findViewById(R.id.tv_cr_order_id);
            tv_cr_product_name = (TextView) itemView.findViewById(R.id.tv_cr_product_name);
            tv_cr_create_time = (TextView) itemView.findViewById(R.id.tv_cr_create_time);
            tv_cr_order_status = (TextView) itemView.findViewById(R.id.tv_cr_order_status);
            tv_cr_order_price = (TextView) itemView.findViewById(R.id.tv_cr_order_price);

            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean hasFocus) {
                    if (hasFocus) {
                        if (mRecyclerView != null && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE
                                && mFocusBorderView != null) {
                            mFocusBorderView.setFocusView(view);
                            FocusUtil.setFocusAnimator(view, mFocusBorderView, 1);

                        }
                    } else {
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(view);
                            FocusUtil.setUnFocusAnimator(view);
                        }
                    }
                }
            });

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && event.getAction() == KeyEvent.ACTION_DOWN) {
                        //just for LeTV(70inch) to remove focus border view
                        notifyDataSetChanged();

                        if (mFocusController != null) {
                            if (mContext instanceof ListUserRelatedActivity) {
                                mFocusController.onFocusSelected(ListUserRelatedActivity.LIST_INDEX_EDU_CONSUME_RECORD);
                            } else {
                                mFocusController.onFocusSelected(LIST_INDEX_CONSUME_RECORD);
                            }
                        }
                        return true;
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (getAdapterPosition() == 0) {
                            return true;
                        } else if (getAdapterPosition() == ((CustomLinearLayoutManager) mRecyclerView.getLayoutManager())
                                .findFirstCompletelyVisibleItemPosition() + 1) {
                            mRecyclerView.scrollToPosition(getAdapterPosition() - 2);
                        }
                    } else if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (getAdapterPosition() == ((CustomLinearLayoutManager) mRecyclerView.getLayoutManager())
                                .findLastCompletelyVisibleItemPosition() - 1) {
                            mRecyclerView.scrollToPosition(getAdapterPosition() + 2);
                        } else if (getAdapterPosition() == getItemCount() - 1) {
                            return true;
                        }
                    } else if ((keyCode == KeyEvent.KEYCODE_ENTER
                            || keyCode == KeyEvent.KEYCODE_DPAD_CENTER)
                            && event.getAction() == KeyEvent.ACTION_UP) {
                        Object aid = v.getTag();
                        LibDeprecatedLogger.d("|Click|Consume Item:" + aid);
                        if (aid != null && aid instanceof Integer) {
                            ActivityLauncher.startVideoDetailDts(mContext, Constant.PAGE_EDU_CONSUME, (Integer) aid, 0, 0, false, 0);
                        }
                    }
                    return false;
                }
            });
        }
    }

}
