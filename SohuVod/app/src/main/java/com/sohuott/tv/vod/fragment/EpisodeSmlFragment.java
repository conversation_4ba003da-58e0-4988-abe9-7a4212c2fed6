package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.leanback.widget.Presenter;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.presenter.launcher.SmallPicPresenter;

/**
 * Created by feng<PERSON><PERSON> on 16-6-28.
 */
public class EpisodeSmlFragment extends BaseEpisodePicFragment {
    private TextView fullTag;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mRootView = (ViewGroup) inflater.inflate(R.layout.fragment_episode_common_layout, container, false);
        initUI();
        if ((mVideoOrder != -1 && mDataType != Constant.DATA_TYPE_PGC) || isMenu){
            currentPage();
            currentOrder();
            getData(mPage);
        }
        return mRootView;
    }

    @Override
    protected void initUI() {
        super.initUI();
        fullTag = mRootView.findViewById(R.id.full_tag);
    }

    @Override
    protected void showFullTag(boolean b) {
        if (b) {
            fullTag.setVisibility(View.VISIBLE);
        } else {
            fullTag.setVisibility(View.GONE);
        }
    }

    @Override
    public Presenter getPresenter() {
        return new SmallPicPresenter();
    }
}
