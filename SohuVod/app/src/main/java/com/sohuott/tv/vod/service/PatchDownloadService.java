package com.sohuott.tv.vod.service;

import android.app.IntentService;
import android.content.Intent;
import android.os.Build;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.sohu.ott.base.lib_user.HeaderHelper;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.app.App;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.UpdateInfo;
import com.sohu.lib_utils.PrefUtil;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.UpdatePresenter;
import com.sohuott.tv.vod.receiver.CancelService;
import com.sohu.lib_utils.ServiceNotificationUtil;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;

/**
 * Created by fenglei on 16-9-26.
 */
public class PatchDownloadService extends IntentService {

    private OkHttpClient client = new OkHttpClient();

    public PatchDownloadService() {
        super("PatchDownload");
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
            startForeground(ServiceNotificationUtil.NOTIFICATION_FOREGROUND_ID, ServiceNotificationUtil.getNotification(getApplicationContext()));
            Intent intent =  new Intent(this, CancelService.class);
            startForegroundService(intent);
        }
        setIntentRedelivery(true);
    }

    @Override
    protected void onHandleIntent(Intent intent) {
        UpdateInfo updateInfo = checkPatchInfo();
        String downloadPath = downloadPatch(updateInfo);
//        if (!TextUtils.isEmpty(downloadPath)) {
//            TinkerInstaller.onReceiveUpgradePatch(getApplicationContext(), downloadPath);
//        }
    }

    private UpdateInfo checkPatchInfo() {
        UpdateInfo updateInfo = null;
        String url = UrlWrapper.getPatchInfoUrl(this, UpdatePresenter.PRODUCT_ID,
                Util.getPartnerNo(this),
                App.getDebug() ? "0" : "1", UserInfoHelper.getGid());
        Request request = new Request.Builder().url(url).
                headers(Headers.of(HeaderHelper.getHeaders())).build();
        try {
            okhttp3.Response response = client.newCall(request).execute();
            Gson gson = new Gson();
            updateInfo = gson.fromJson(response.body().string(), UpdateInfo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return updateInfo;
    }

    private String downloadPatch(UpdateInfo updateInfo) {
        String downloadPath = "";
        if (updateInfo == null || updateInfo.status != 0 || updateInfo.data == null) {
            return downloadPath;
        }
        UpdateInfo.Data data = updateInfo.data;
        if (data.status == 0 || !data.targetVersion.equals(Util.getVersionName(this))
                || TextUtils.isEmpty(data.downloadUrl) || TextUtils.isEmpty(data.md5)) {
            return downloadPath;
        }
        String fileName = data.targetVersion.concat("_patch.apk");
        File patchDir = new File(getFilesDir() + "/patch");

        boolean isMkDir = true;
        if(patchDir.isDirectory()&&patchDir.exists()){
            LibDeprecatedLogger.d("This directory is exist!");
        }else{
            isMkDir = patchDir.mkdirs();
        }
        LibDeprecatedLogger.d("Patch directory mk result:"+isMkDir);
        if(!isMkDir){
            return "";
        }

        File file = new File(patchDir, fileName);
        if (file.exists()) {
            String md5 = PrefUtil.getString( "patch", fileName, "");
            if (data.md5.equals(md5)) {
                return downloadPath;
            }
        }
        if(patchDir != null && patchDir.listFiles() != null){
            for (File patchFile : patchDir.listFiles()) {
                if (patchFile != null && patchFile.exists()) {
                    patchFile.delete();
                }
            }
        }else {
            return "";
        }

        File tmpFile = new File(patchDir, fileName + "_tmp");
        final Request request = new Request.Builder().url(data.downloadUrl).
                headers(Headers.of(HeaderHelper.getHeaders())).build();
        try {
            okhttp3.Response response = client.newCall(request).execute();
            InputStream is = null;
            byte[] buf = new byte[2048];
            int len;
            FileOutputStream fos = null;
            try {
                long total = response.body().contentLength();
                LibDeprecatedLogger.i("total------>" + total);
                is = response.body().byteStream();
                fos = new FileOutputStream(tmpFile);
                while ((len = is.read(buf)) != -1) {
                    fos.write(buf, 0, len);
                }
                fos.flush();
            } catch (IOException e) {
                LibDeprecatedLogger.e(e.getMessage(), e);
            } finally {
                try {
                    if (is != null) {
                        is.close();
                    }
                    if (fos != null) {
                        fos.close();
                    }
                } catch (IOException e) {
                    LibDeprecatedLogger.e(e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        String fileMd5 = Util.getFileMD5(tmpFile);
        if (data.md5.equals(fileMd5)) {
            tmpFile.renameTo(file);
            PrefUtil.putString("patch", fileName, data.md5);
            downloadPath = file.getAbsolutePath();
        } else {
            tmpFile.delete();
        }
        return downloadPath;
    }

}
