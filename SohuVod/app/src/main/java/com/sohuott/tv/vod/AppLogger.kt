package com.sohuott.tv.vod

import android.content.Context
import com.sh.ott.base.logger.imp.LoganImp
import com.sh.ott.logger.Logger
import com.sh.ott.logger.base.LoggerConfig
import com.sohu.lib_utils.FileHelper

object AppLogger {
    private const val TAG = "Sh-App"

    @JvmStatic
    var debug = false
        set(value) {
            field = value
            Logger.changeConsoleLogger(debug)
        }


    @JvmStatic
    fun init(context: Context) {
        val path = context.applicationContext.getFilesDir().getAbsolutePath()
        val size =
            (FileHelper.bytesToMB(FileHelper.getAppInternalFreeSpace(context.applicationContext))).toLong()
        Logger.setLoggerImp(LoganImp())
        Logger.initConfig(config = LoggerConfig().apply {
            //设置俩天
            cacheDays = 2L
            enableConsoleLogger = debug
            cachePath = path
            filePath = path + "/logger"
            encryptKey = "0123456789012345"
            fileSize = 50L
        })
        AppLogger.d("剩余空间大小 :${size}")
    }

    @JvmStatic
    fun d(tag: String?, msg: String?) {

        Logger.d(TAG.plus(tag), msg)
    }

    @JvmStatic
    fun d(msg: String?) {
        Logger.d(TAG, msg)
    }

    @JvmStatic
    fun e(msg: String?) {
        Logger.e(TAG, msg)
    }

    @JvmStatic
    fun e(tag: String?, msg: String?) {
        Logger.e(TAG.plus(tag), msg)
    }

    @JvmStatic
    fun i(msg: String?) {
        Logger.i(TAG, msg)
    }

    @JvmStatic
    fun w(msg: String?) {
        Logger.w(TAG, msg)
    }

    @JvmStatic
    fun w(tag: String?, msg: String?) {
        Logger.w(TAG.plus(tag), msg)
    }

    @JvmStatic
    fun v(msg: String?) {
        Logger.v(TAG, msg)
    }

    @JvmStatic
    fun v(tag: String?, msg: String?) {
        Logger.v(TAG.plus(tag), msg)
    }
}