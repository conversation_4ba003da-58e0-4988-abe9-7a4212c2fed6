package com.sohuott.tv.vod.widget.lb;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.PayActivity;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohu.lib_utils.FormatUtils;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

import java.util.HashMap;

/**
 * Created by music on 2021/9/15.
 */

public class VipUserView extends ConstraintLayout implements View.OnClickListener, View.OnKeyListener{
    private String TAG = "VipUserView";
    private Context mContext;
    private Button mOpenVip, mLogin;
    private TextView mNickName, mTicket, mState;
    private ImageView mAvatar, mVipLogo, mLoginType;

    private LoginUserInformationHelper mHelper;
    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    private HashMap<String, String> pathInfo;


    public VipUserView(Context context) {
        super(context);
        init(context);
    }

    public VipUserView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public VipUserView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        setFocusable(false);
        setFocusableInTouchMode(false);
        this.mContext = context;
        LayoutInflater.from(context).inflate(R.layout.vip_user_view, this, true);
        mAvatar = (ImageView) findViewById(R.id.vip_header_avatar);
        mOpenVip = (Button) findViewById(R.id.vip_header_open_vip);
        mNickName = (TextView) findViewById(R.id.vip_name);
        mVipLogo = (ImageView) findViewById(R.id.vip_user_logo);
        mState = (TextView) findViewById(R.id.vip_state);
        mTicket = (TextView) findViewById(R.id.vip_use_ticket);
        mLogin = (Button) findViewById(R.id.vip_header_login);
        mLoginType = (ImageView) findViewById(R.id.vip_header_login_type);

        mHelper = LoginUserInformationHelper.getHelper(context);
        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
        }

        initListener();
        refresh();
    }

    private void initListener() {
        mOpenVip.setOnKeyListener(this);
        mLogin.setOnKeyListener(this);
        mOpenVip.setOnClickListener(this);
        mLogin.setOnClickListener(this);
    }

    public void setPathInfo(HashMap<String, String> pathInfo) {
        this.pathInfo = pathInfo;
    }

    public void refresh() {
        if (mHelper.getIsLogin()) {
            Glide.with(mContext)
                    .load(mHelper.getLoginPhoto())
                    .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                    .into(mAvatar);

            mLoginType.setVisibility(VISIBLE);
            mNickName.setVisibility(VISIBLE);
            mNickName.setText(mHelper.getNickName());
            mState.setVisibility(VISIBLE);
            mOpenVip.setVisibility(GONE);

            switch (Util.getSouthMediaLoginType(mHelper.getUtype())) {
                case 4:
                    mLoginType.setBackgroundResource(R.drawable.login_type_weibo);
                    break;
                case 1:
                    mLoginType.setBackgroundResource(R.drawable.login_type_wechat);
                    break;
                case 2:
                    mLoginType.setBackgroundResource(R.drawable.login_type_qq);
                    break;
                case 3:
                    mLoginType.setBackgroundResource(R.drawable.login_type_sohu);
                    break;
            }
            Log.d(TAG, "refresh: mHelper.isVip() : " + mHelper.isVip());
            if (mHelper.isVip()) {
                mTicket.setVisibility(VISIBLE);
                mVipLogo.setVisibility(VISIBLE);

                mLogin.setText("续费超级会员");
                String vipDate = FormatUtils.formatDate(Long.valueOf(mHelper.getVipTime()));
                mState.setText("会员有效期：" + vipDate);
                mTicket.setText("观影券：" + mHelper.getUserTicketNumber());
                if ((System.currentTimeMillis()) > Long.valueOf(mHelper.getVipTime())) {
                    mTicket.setVisibility(VISIBLE);
                    mVipLogo.setVisibility(VISIBLE);
                    mVipLogo.setBackgroundResource(R.drawable.top_bar_vip_expired);
                    mState.setText("您的会员已过期");
                    mTicket.setText("观影券冻结：" + mHelper.getUserTicketNumber());
                    mLogin.setText("开通超级会员");
                }
            } else {
                mTicket.setVisibility(GONE);
                mVipLogo.setVisibility(GONE);
                mState.setText("普通用户");
                mLogin.setText("开通超级会员");
            }
        } else {
            mAvatar.setBackgroundResource(R.drawable.vip_header_no_login);
            mOpenVip.setVisibility(VISIBLE);
            mLogin.setVisibility(VISIBLE);
            mNickName.setVisibility(GONE);
            mState.setVisibility(GONE);
            mTicket.setVisibility(GONE);
            mVipLogo.setVisibility(GONE);
            mLoginType.setVisibility(GONE);

            mLogin.setText("立即登录");
            mOpenVip.setText("开通会员");
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == mOpenVip.getId()) {
            if (mHelper.isVip()) {
                RequestManager.getInstance().onAllEvent(new EventInfo(10151, "clk"), pathInfo, null, null);
            } else {
                RequestManager.getInstance().onAllEvent(new EventInfo(10139, "clk"), pathInfo, null, null);
            }

            ActivityLauncher.startPayActivity(mContext, PayActivity.PAY_SOURCE_HOME_RECOMMEND);
        } else if (v.getId() == mLogin.getId()) {
            if (mHelper.getIsLogin()) {
                if (mHelper.isVip()) {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10151, "clk"), pathInfo, null, null);
                } else {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10139, "clk"), pathInfo, null, null);
                }
                ActivityLauncher.startPayActivity(mContext, PayActivity.PAY_SOURCE_HOME_RECOMMEND);

            } else {
                RequestManager.getInstance().onAllEvent(new EventInfo(10136, "clk"), pathInfo, null, null);

                ActivityLauncher.startLoginActivity(mContext, Constant.LAUNCHER_SOURCE, Integer.parseInt(pathInfo.get("pageId")));
            }
        }
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        Log.d(TAG, "onKey: v" + v.toString() + "event" + event.toString());
//        if (event.getAction() == KeyEvent.ACTION_DOWN) {
//            if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
//                if (v.equals(mLogin)) {
//                    mOpenVip.requestFocus();
//                    return true;
//                }
//            }
//        }
        return false;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        Log.d(TAG, "dispatchKeyEvent: " + event.toString());
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Log.d(TAG, "onKeyDown: " + event.toString());
        return super.onKeyDown(keyCode, event);
    }
}
