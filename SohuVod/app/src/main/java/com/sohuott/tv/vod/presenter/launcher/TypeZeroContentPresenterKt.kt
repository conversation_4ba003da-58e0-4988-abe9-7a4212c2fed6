package com.sohuott.tv.vod.presenter.launcher

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import androidx.leanback.widget.Presenter
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleEventObserver
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.google.gson.Gson
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.sh.ott.video.view.ShVideoView
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.activity.launcher.LauncherPlayerManager
import com.sohuott.tv.vod.databinding.ItemTypeZeroLayoutBinding
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.ContentGroup.DataBean.ContentsBean
import com.sohuott.tv.vod.lib.model.HomeRecommendBean
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.model.HomeRecommend
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

class TypeZeroContentPresenterKt(
    private val lifecycle: Lifecycle? = null,
    private val scope: LifecycleCoroutineScope? = null
) : Presenter() {
    private var context: WeakReference<Context>? = null
    private var itemFocusHelper: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null
    private var contentsBean: ContentsBean? = null
    private var originHeight: Int? = null
    private var job: Job? = null

    private val launcherPlayerManager = LauncherPlayerManager()

    val observer = LifecycleEventObserver { source, event ->
        when (event) {
            Lifecycle.Event.ON_PAUSE -> {
                cancelDelay()
            }

            else -> {}
        }
    }

    init {
        lifecycle?.addObserver(observer)
    }

    interface OnTypeZeroPlayListener {
        fun onTypeZeroStartPlay(viewHolder: ViewHolder, manager: LauncherPlayerManager)
        fun onTypeZeroStopPlay(viewHolder: ViewHolder)

        fun cancelPlay(viewHolder: ViewHolder)
    }

    private var onTypeZeroPlayListener: OnTypeZeroPlayListener? = null

    fun setOnTypeZeroPlayListener(onTypeZeroPlayListener: OnTypeZeroPlayListener) {
        this.onTypeZeroPlayListener = onTypeZeroPlayListener
    }


    override fun onCreateViewHolder(parent: ViewGroup?): ViewHolder {
        context = WeakReference(parent?.context)
        itemFocusHelper = itemFocusHelper ?: MyFocusHighlightHelper.BrowseItemFocusHighlight(
            FocusHighlight.ZOOM_FACTOR_SMALL,
            false
        )
        val binding =
            ItemTypeZeroLayoutBinding.inflate(LayoutInflater.from(parent?.context), parent, false)
        return TypeZeroViewHolder(binding)
    }


    override fun onBindViewHolder(viewHolder: ViewHolder?, item: Any?) {
        val vh = viewHolder as TypeZeroViewHolder
        if (item is ContentsBean) {
            contentsBean = item
            vh.binding.root.viewTreeObserver.addOnGlobalLayoutListener {
                originHeight = vh.binding.ivTypeOnePoster.height
            }
            if (contentsBean?.picUrl?.isNotEmpty() == true && context?.get() != null) {
                Glide.with(context?.get()!!)
                    .load(contentsBean?.picUrl)
                    .placeholder(R.drawable.bg_launcher_poster)
                    .transform(RoundedCorners(context?.get()!!.resources?.getDimensionPixelOffset(R.dimen.x10)!!))
                    .into(vh.binding.ivTypeOnePoster)
            }

            vh.binding.tvTypeOneName.text = contentsBean?.name
            vh.binding.tvTypeOneDesc.text = contentsBean?.tvComment
            vh.binding.title.text = contentsBean?.name
            vh.binding.desc.text = contentsBean?.tvComment

            if (contentsBean?.type == HomeRecommend.LABEL_TYPE.toString()) {
                vh.binding.ivTypeOnePoster.setCornerType(true)
            } else {
                if (contentsBean?.albumParam != null) {
                    vh.binding.ivTypeOnePoster.setCornerTypeWithType(
                        contentsBean?.albumParam?.tvIsFee!!.toInt(),
                        contentsBean?.albumParam?.tvIsEarly!!,
                        contentsBean?.albumParam?.useTicket!!,
                        contentsBean?.albumParam?.paySeparate!!,
                        contentsBean?.albumParam?.cornerType!!.toInt()
                    );
                }
                contentsBean?.parameter?.takeIf { it.isNotEmpty() }?.let { parameterJson ->
                    try {
                        val gson = Gson()
                        val parameter = gson.fromJson(
                            parameterJson,
                            HomeRecommendBean.Data.Content.Parameter::class.java
                        )
                        parameter.cornerType?.toIntOrNull()?.let { cornerTypeInt ->
                            vh.binding.ivTypeOnePoster.setPgcCornerTypeWithType(cornerTypeInt)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }

            vh.binding.root.setOnFocusChangeListener { view, hasFocus ->
                itemFocusHelper?.onItemFocused(view, hasFocus)


                if (hasFocus) {
                    vh.binding.typeZeroFocus.post {
                        vh.binding.typeZeroFocus.visibility = View.VISIBLE
                    }
                    vh.binding.tvTypeOneDesc.visible()
                    if (item.parameterPianhua!= null
                        && item.parameterPianhua.isNotEmpty()
                        && Util.getHuapingParams(context!!.get()) != 1
                        && Util.getDynamicVideoParams(context!!.get())
                    ) {
//                        startAnimation(vh, 346,346 + 130, item)
                        onTypeZeroPlayListener?.onTypeZeroStartPlay(
                            viewHolder,
                            launcherPlayerManager
                        )
                        startDelay(vh, item)
                    } else {
                        LibDeprecatedLogger.d("${item.name} no pianhua or huaping is 1")
                    }
                } else {
                    vh.binding.tvTypeOneDesc.gone()
                    cancelDelay()
                    restoreTypeZeroPlayer(vh)
                    vh.binding.rootPlayer.removeAllViews()
//                    onTypeZeroPlayListener?.cancelPlay(viewHolder)
                }
            }

            RequestManager.getInstance().onAllEvent(
                EventInfo(10146, "imp"),
                contentsBean?.pathInfo,
                contentsBean?.objectInfo, null
            )
        }

    }

    private fun startDelay(vh: TypeZeroViewHolder, item: ContentsBean) {
        job = scope?.launch {
            delay(2000L) // 等待2秒
            LibDeprecatedLogger.d("2秒已经过去了")
            startAnimation(vh, 346, 346 + 130, item)
        }
    }

    private fun cancelDelay() {
        valueAnimator?.removeAllListeners()
        valueAnimator?.cancel()
        valueAnimator = null
        job?.cancel()
    }


    override fun onViewDetachedFromWindow(holder: ViewHolder?) {
        super.onViewDetachedFromWindow(holder)
        val vh = holder as TypeZeroViewHolder
        vh.binding.root.viewTreeObserver.removeOnGlobalLayoutListener { vh }
//        onTypeZeroPlayListener?.onTypeZeroStopPlay(vh)
        LibDeprecatedLogger.d("onViewDetachedFromWindow: ${vh.binding.tvTypeOneName.text}")
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder?) {
//        onTypeZeroPlayListener?.onTypeZeroStopPlay(viewHolder as TypeZeroViewHolder)
        Glide.with((viewHolder as TypeZeroViewHolder).binding.ivTypeOnePoster.context).clear(
            viewHolder.binding.ivTypeOnePoster
        )
        LibDeprecatedLogger.d("onUnbindViewHolder: ${viewHolder.binding.tvTypeOneName.text}")
    }

    var valueAnimator: ValueAnimator? = null

    private fun startAnimation(
        vh: TypeZeroViewHolder,
        start: Int,
        end: Int,
        contentsBean: ContentsBean
    ) {
        valueAnimator = ValueAnimator.ofInt(start, end)
        valueAnimator!!.duration = 300
        valueAnimator!!.interpolator = AccelerateInterpolator()
        valueAnimator!!.addUpdateListener { animation: ValueAnimator ->
            vh.binding.rootPlayer.visibility = View.VISIBLE
            val value = animation.animatedValue as Int
            vh.binding.rootPlayer.layoutParams.height = value
            vh.binding.typeZeroFocus.layoutParams.height = value
            vh.binding.rootPlayer.requestLayout()
            vh.binding.typeZeroFocus.requestLayout()
//            vh.binding.playerView.alpha = value * 1.0f / end
            LibDeprecatedLogger.d("value = $value")
        }
        val videoView = ShVideoView(context?.get()!!)
        videoView?.pivotX = 0f
        videoView?.pivotY = 0f
        videoView?.scaleX = 1 / 1.1f
        videoView?.scaleY = 1 / 1.1f
        videoView?.layoutParams =
            ViewGroup.LayoutParams((852 * 1.1f).toInt(), ((346 + 130) * 1.1f).toInt())
        valueAnimator!!.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                vh.binding.rootPlayer.removeAllViews()
                vh.binding.rootPlayer.addView(videoView)
                if (launcherPlayerManager != null) {
                    launcherPlayerManager.setPlayParamsAndPlay(
                        context?.get(),
                        contentsBean,
                        vh,
                        isScale = true,
                        mShVideoView = videoView
                    )
                }
            }

            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                vh.binding.rootPlayer.visibility = View.VISIBLE
//                vh.binding.playerView.alpha = 1f
            }
        })
        valueAnimator!!.start()
    }

    private fun restoreTypeZeroPlayer(viewHolder: TypeZeroViewHolder) {
        if (viewHolder.binding.rootPlayer.visibility == View.VISIBLE) {
            viewHolder.binding.typeZeroFocus.visibility = View.GONE
            viewHolder.binding.typeZeroFocus.layoutParams.height = 346
            viewHolder.binding.rootPlayer.layoutParams.height = 346
            viewHolder.binding.rootPlayer.visibility = View.GONE
            viewHolder.binding.rootPlayer.removeAllViews()
            launcherPlayerManager.releasePlayer()
        }
    }


    class TypeZeroViewHolder(val binding: ItemTypeZeroLayoutBinding) : ViewHolder(binding.root),
        java.io.Serializable
}