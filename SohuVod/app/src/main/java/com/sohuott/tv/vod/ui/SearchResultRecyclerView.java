package com.sohuott.tv.vod.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.view.KeyEvent;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by fengle<PERSON> on 17-6-27.
 */

public class SearchResultRecyclerView extends RecyclerView {


    public SearchResultRecyclerView(Context context) {
        super(context);
        init(context);
    }

    public SearchResultRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public SearchResultRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

    private void init(Context context) {
        setChildDrawingOrderCallback(new ChildDrawingOrderCallback() {
            @Override
            public int onGetChildDrawingOrder(int childCount, int i) {
                int pos = -1;
                for(int index = 0; index < childCount; index++) {
                    if(getChildAt(index).hasFocus()) {
                        pos = index;
                        break;
                    }
                }
                if(pos < 0 || pos >= childCount) {
                    return i;
                }
                if(i == childCount - 1) {
                    return pos;
                }else if(i < pos) {
                    return i;
                }else {
                    return i + 1;
                }
            }
        });
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        // Handle automatic focus changes.

        return super.dispatchKeyEvent(event) || executeKey();
    }

    private boolean executeKey() {
        return getScrollState() != SCROLL_STATE_IDLE;
    }


    //    @Override
//    public void smoothScrollBy(int dx, int dy, Interpolator interpolator) {
////        int minDistance = (int) getResources().getDimension(R.dimen.y375);
////        if(Math.abs(dy) < minDistance) {
////            dy = dy > 0 ? minDistance : -minDistance;
////        }
//        super.smoothScrollBy(dx, dy, new LinearInterpolator());
//    }
}
