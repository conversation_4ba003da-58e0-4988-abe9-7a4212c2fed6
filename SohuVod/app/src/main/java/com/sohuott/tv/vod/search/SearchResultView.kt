package com.sohuott.tv.vod.search

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.View.OnKeyListener
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.BaseGridView
import androidx.leanback.widget.DiffCallback
import androidx.leanback.widget.Presenter
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.drake.net.Get
import com.drake.net.convert.SerializationConverter
import com.drake.net.okhttp.trustSSLCertificate
import com.drake.net.utils.scopeNetLife
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.databinding.FragmentSearchResultBinding
import com.sohuott.tv.vod.fragment.CustomItemBridgeAdapter
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.model.SearchResult
import com.sohuott.tv.vod.presenter.launcher.VideoDetailTypeTwoContentPresenter
import com.sohuott.tv.vod.utils.ActivityLauncher
import com.sohuott.tv.vod.utils.SearchUtil
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import okhttp3.ConnectionSpec
import java.math.BigInteger
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

@SuppressLint("RestrictedApi")
class SearchResultView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr),
    CustomItemBridgeAdapter.OnItemViewClickedListener,
    OnFocusChangeListener,
    OnKeyListener{

    interface OnSearchResultFocusChangeListener {
        fun onSearchResultFocusLeft()
        fun onSearchResultFocusBack()
    }

    var mOnSearchKeyBoardFocusChangeListener: OnSearchResultFocusChangeListener? = null

    fun setOnSearchResultFocusChangeListener(listener: OnSearchResultFocusChangeListener) {
        mOnSearchKeyBoardFocusChangeListener = listener
    }


    private var mBrowseItemFocusHighlight: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    private var lastFocusedView: View? = null
    private val sortFlow = MutableSharedFlow<String>()
    private var searchResultSize = 6
    private var searchResultItems: List<SearchResult.Data.SearchItem>? = null

    private val binding: FragmentSearchResultBinding
    //结果
    private var searchResultArrayAdapter: ArrayObjectAdapter? = null
    private var searchResultItemBridgeAdapter: CustomItemBridgeAdapter? = null
    private var searchResultPresenter: SearchResultPresenterKt? = null

    //相关
    private var searchRelatedArrayAdapter: ArrayObjectAdapter? = null
    private var searchRelatedItemBridgeAdapter: CustomItemBridgeAdapter? = null
    private var searchRelatedPresenter: VideoDetailTypeTwoContentPresenter? = null

    private val SORT_DEFAULT = 0
    private val SORT_TIME = 1
    private val SORT_HOT = 2

    private var currentSort = SORT_DEFAULT

    private var resultTitle: String = ""

    var scope: CoroutineScope? = null

    private var lastFocusedTabView: View? = null



    //埋点
    private var fromPage: Int? = null
    private var originalText: String? = null

    init {

        mBrowseItemFocusHighlight =
            MyFocusHighlightHelper.BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false)

        binding = FragmentSearchResultBinding.inflate(LayoutInflater.from(context), this, true)

        binding.searchResultDefault.isSelected = true

        //结果
        searchResultPresenter = SearchResultPresenterKt()
        searchResultArrayAdapter = ArrayObjectAdapter(searchResultPresenter)
        searchResultItemBridgeAdapter = object: CustomItemBridgeAdapter(searchResultArrayAdapter){
            override fun getOnItemViewClickedListener(): OnItemViewClickedListener {
                return this@SearchResultView
            }
        }
        binding.searchResultList.let {
            it.adapter = searchResultItemBridgeAdapter
            it.setNumColumns(3)
            it.verticalSpacing = 48
            it.horizontalSpacing = 24
//            it.focusScrollStrategy= BaseGridView.FOCUS_SCROLL_ITEM
        }
        //相关
        searchRelatedPresenter = VideoDetailTypeTwoContentPresenter()
        searchRelatedArrayAdapter = ArrayObjectAdapter(searchRelatedPresenter)
        searchRelatedItemBridgeAdapter = object: CustomItemBridgeAdapter(searchRelatedArrayAdapter){
            override fun getOnItemViewClickedListener(): OnItemViewClickedListener {
                return this@SearchResultView
            }
        }
        binding.searchRelativeList.let {
            it.adapter = searchRelatedItemBridgeAdapter
            it.setNumColumns(4)
            it.verticalSpacing = 48
            it.horizontalSpacing = 24
            it.focusScrollStrategy = BaseGridView.FOCUS_SCROLL_ITEM
        }

        binding.searchResultDefault.onFocusChangeListener = this
        binding.searchResultHot.onFocusChangeListener = this
        binding.searchResultNew.onFocusChangeListener = this

        binding.searchResultDefault.setOnKeyListener(this)
        binding.searchResultHot.setOnKeyListener(this)
        binding.searchResultNew.setOnKeyListener(this)

        binding.searchResultMore.setOnFocusChangeListener { v, hasFocus ->
            mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
        }
        binding.searchResultMore.setOnClickListener{
            searchResultArrayAdapter?.size()?.let {
                binding.searchResultList.findViewHolderForAdapterPosition(it - 1)?.itemView?.requestFocus()
                searchResultArrayAdapter?.addAll(it, searchResultItems?.subList(it, (it + 6).coerceAtMost(searchResultItems?.size!!)))
            }
            SearchRequestManager.searchResultMoreClick()
            incrementOutputSize()
        }

        val debouncedFlow = sortFlow.debounce(500)

        (context as? LifecycleOwner)?.lifecycleScope?.launch {
            // 在此处启动协程并执行相关操作
            debouncedFlow.collect { event ->
                if (currentSort != event.toInt() && searchResultArrayAdapter?.size() != 1) {
                    println("处理事件: $event")
                    searchResultSize = 6
                    getData(event.toInt())
                    currentSort = event.toInt()
                }
                SearchRequestManager.searchResultCategoryClick(event)
            }
        }

        binding.searchResultList.setOnCustomVerticalGridViewFocusChangeListener {
            if (lastFocusedTabView == null) {
                binding.searchResultTab.requestDefaultFocus()
            } else {
                lastFocusedTabView?.requestFocus()
            }
        }
    }

    private fun incrementOutputSize() {
        searchResultSize += 6

        binding.searchResultList.layoutParams.height = Math.ceil( searchResultArrayAdapter!!.size() / 3.0).toInt() * 346 + (Math.ceil(searchResultArrayAdapter!!.size() / 3.0).toInt() - 1) * 48
        binding.searchResultList.requestLayout()

        searchResultItems?.let {
            if (searchResultSize > it.size) {
                binding.searchResultMore.visibility = View.GONE
            } else {
                binding.searchResultMore.visibility = View.VISIBLE
            }
        }
    }


    fun requestFirstFocus() {
        if (lastFocusedView == null) {
            if (binding.searchResultList.visibility == View.GONE) {
                binding.searchResultErrorText.requestFocus()
            } else {
                binding.root.scrollTo(0,0)
                val firstItemView = binding.searchResultList.findViewHolderForAdapterPosition(0)?.itemView
                firstItemView?.requestFocus() ?: binding.searchResultList.requestFocus()
            }
        } else {
            lastFocusedView?.requestFocus()
        }
    }

    fun setResultTitle(result: String, fromPage: Int, originalText: String) {
        this.fromPage = fromPage
        this.originalText = originalText
        binding.root.scrollTo(0,0)
        this.resultTitle = result
        currentSort = SORT_DEFAULT
        binding.errorGroup.gone()
        binding.searchResultTitle.setText(getColoredTitle(""), TextView.BufferType.SPANNABLE)
        searchResultSize = 6
        getData(SORT_DEFAULT)
        SearchRequestManager.searchResultCategoryClick("0")
    }

    private fun getData(sort: Int){
        val time = System.currentTimeMillis()
        scope?.cancel()
        scope = scopeNetLife {
            val result = Get<SearchResult>("${RetrofitApi.get().retrofitHost.baseHost}search/search.json"){
                setClient {
                    trustSSLCertificate()
                    connectionSpecs(listOf(
                        ConnectionSpec.MODERN_TLS,
                        ConnectionSpec.COMPATIBLE_TLS,
                        ConnectionSpec.CLEARTEXT))
                }
                converter = SerializationConverter()
                param("query", resultTitle)
                param("pageSize", 30)
                param("page", 1)
                param("sort", sort)
                param("timeStamp", time)
                param("code", md5(resultTitle + time + "09606ac70454ce82"))
                addHeader("api_key", Util.getSohuApiKey(context))
                addHeader("gid", UserInfoHelper.getGid()?:"")
                addHeader("Service_version", Constant.SERVICE_VERSION)
            }.await()
            if (result.data.searchItems.isNullOrEmpty()) {

//                ToastUtils.showToast(context, "搜索结果空数据")
                LibDeprecatedLogger.d("搜索结果空数据")
                binding.errorGroup.visible()
                binding.contentGroup.gone()

                SearchRequestManager.noSearchResultPageExposure(fromPage, originalText)
            } else {
                binding.errorGroup.gone()
                binding.contentGroup.visible()
                binding.searchResultHot.visible()
                binding.searchResultNew.visible()
                binding.searchResultDefault.visible()
                searchResultItems = result.data.searchItems
//                searchResultArrayAdapter?.clear()
//                searchResultArrayAdapter?.addAll(0, searchResultItems?.take(searchResultSize.coerceAtMost(searchResultItems!!.size)))
                searchResultArrayAdapter?.setItems(searchResultItems?.take(searchResultSize.coerceAtMost(searchResultItems!!.size)), object : DiffCallback<SearchResult.Data.SearchItem>(){
                    override fun areItemsTheSame(
                        oldItem: SearchResult.Data.SearchItem,
                        newItem: SearchResult.Data.SearchItem
                    ): Boolean {
                        return true
                    }

                    override fun areContentsTheSame(
                        oldItem: SearchResult.Data.SearchItem,
                        newItem: SearchResult.Data.SearchItem
                    ): Boolean {
                        return false
                    }
                })

//                incrementOutputSize()
                searchResultItems?.let {
                    if (searchResultSize > it.size) {
                        binding.searchResultMore.visibility = View.GONE
                    } else {
                        binding.searchResultMore.visibility = View.VISIBLE
                    }
                }

                binding.searchResultList.layoutParams.height = Math.ceil( searchResultArrayAdapter!!.size() / 3.0).toInt() * 346 + (Math.ceil(searchResultArrayAdapter!!.size() / 3.0).toInt() - 1) * 48
                binding.searchResultList.requestLayout()
                val rightKeyPressed = (context as SearchActivity).rightKeyPressed
                AppLogger.d("rightKeyPressed:$rightKeyPressed")
                if (rightKeyPressed){
                    requestFirstFocus()
                    (context as SearchActivity).rightKeyPressed = false
                }

                binding.searchRelativeTitle.text = "\u201c${result.data.searchItems[0].tvName}\u201d的相关影视推荐"
                if (result.data.relationItems.isNotEmpty()) {
                //相关
                    result.data.relationItems.forEachIndexed { index, relationItem -> relationItem.index = index + 1 }
                    searchRelatedArrayAdapter?.setItems(result.data.relationItems, object : DiffCallback<SearchResult.Data.RelationItem>() {
                        override fun areItemsTheSame(
                            oldItem: SearchResult.Data.RelationItem,
                            newItem: SearchResult.Data.RelationItem
                        ): Boolean {
                            return true
                        }

                        override fun areContentsTheSame(
                            oldItem: SearchResult.Data.RelationItem,
                            newItem: SearchResult.Data.RelationItem
                        ): Boolean {
                            return false
                        }

                    })
                    binding.searchRelativeList.layoutParams.height = Math.ceil(result.data.relationItems.size / 4.0).toInt() * 226 + (Math.ceil(result.data.relationItems.size / 4.0).toInt()) * 48 + 48 + 48
                    binding.searchRelativeList.requestLayout()
                } else {
//                    ToastUtils.showToast(context, "搜索相关空数据")
                    LibDeprecatedLogger.d("搜索相关空数据")
                    binding.searchRelativeTitle.gone()
                }

                SearchRequestManager.searchResultPageExposure(fromPage, originalText)
            }
            LibDeprecatedLogger.d("searchList size: ${result.data.searchItems.size}")
            LibDeprecatedLogger.d("relationList size: ${result.data.relationItems.size}")
        }
    }

    private fun getColoredTitle(text: String) : SpannableStringBuilder {
        val coloredText = "全部\u201c$resultTitle\u201d的结果"
        // 设为 #FF6247
        val colorSpan = ForegroundColorSpan(Color.parseColor("#FF6247"))
        return SpannableStringBuilder(coloredText).apply {
            setSpan(colorSpan, 2, coloredText.length - 3, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        }
    }

    fun md5(input: String): String {
        return try {
            val messageDigest = MessageDigest.getInstance("MD5")
            messageDigest.update(input.toByteArray())
            val digest = messageDigest.digest()
            val bigInteger = BigInteger(1, digest)
            String.format("%032x", bigInteger)
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            ""
        }
    }

    override fun onItemClicked(
        focusView: View?,
        itemViewHolder: Presenter.ViewHolder?,
        item: Any?
    ) {
        when(item){
            is SearchResult.Data.SearchItem -> {
                ActivityLauncher.startVideoDetailActivity(context, item.id, Constant.PAGE_SEARCH)
                SearchUtil.saveSearchHistory(context, item)
                SearchRequestManager.searchResultItemClick(item.id.toString(), resultTitle, fromPage)
            }
            is SearchResult.Data.RelationItem -> {
                ActivityLauncher.startVideoDetailActivity(context, item.id, Constant.PAGE_SEARCH)
                SearchUtil.saveSearchHistory(context, item)
                SearchRequestManager.searchResultRelatedItemClick(item.id.toString(), item.index.toString())
            }

        }
    }


    override fun focusSearch(focused: View?, direction: Int): View? {
        val nextFocus = super.focusSearch(focused, direction)
        if (nextFocus !=null && direction == FOCUS_LEFT && !isDescendantView(nextFocus)) {
            (context as SearchActivity).rightKeyPressed = false
            mOnSearchKeyBoardFocusChangeListener?.onSearchResultFocusLeft()
            lastFocusedView = focused
            return null
        }
        return nextFocus
    }

    private fun isDescendantView(view: View): Boolean {
        var currentView: View? = view
        while (currentView != null) {
            if (currentView.parent === this) {
                return true
            }
            currentView = currentView.parent as? View
        }
        return false
    }

    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_DOWN) {
            (context as SearchActivity).rightKeyPressed = false
            return if (binding.scrollView.scrollY != 0) {
                binding.root.scrollTo(0, 0)
                val firstItemView = binding.searchResultList.findViewHolderForAdapterPosition(0)?.itemView
                firstItemView?.requestFocus() ?: binding.searchResultList.requestFocus()
                return true
            } else {
                mOnSearchKeyBoardFocusChangeListener?.onSearchResultFocusBack()
                lastFocusedView = null
                return true
            }
        }
        return super.dispatchKeyEvent(event)
    }

    override fun onFocusChange(v: View?, hasFocus: Boolean) {
        mBrowseItemFocusHighlight?.onItemFocused(v, hasFocus)
        if (hasFocus) {
            val sort = when(v?.id) {
                R.id.search_result_default -> {
                    binding.searchResultDefault.setTextColor(Color.parseColor("#E8E8FF"))
                    binding.searchResultHot.setTextColor(Color.parseColor("#cce8e8ff"))
                    binding.searchResultNew.setTextColor(Color.parseColor("#cce8e8ff"))
                    SORT_DEFAULT
                }
                R.id.search_result_hot -> {
                    binding.searchResultDefault.setTextColor(Color.parseColor("#cce8e8ff"))
                    binding.searchResultHot.setTextColor(Color.parseColor("#E8E8FF"))
                    binding.searchResultNew.setTextColor(Color.parseColor("#cce8e8ff"))
                    SORT_HOT
                }
                R.id.search_result_new -> {
                    binding.searchResultDefault.setTextColor(Color.parseColor("#cce8e8ff"))
                    binding.searchResultHot.setTextColor(Color.parseColor("#cce8e8ff"))
                    binding.searchResultNew.setTextColor(Color.parseColor("#E8E8FF"))
                    SORT_TIME
                }
                else -> SORT_DEFAULT
            }
            (context as? LifecycleOwner)?.lifecycleScope?.launch {
                // 在此处启动协程并执行相关操作
                sortFlow.emit(sort.toString())
            }
            lastFocusedTabView = v
        }
    }

    override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {

        if (event?.action == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
            when(v?.id) {
                R.id.search_result_default -> {
                    binding.searchResultDefault.setTextColor(Color.parseColor("#FF6247"))
                }
                R.id.search_result_hot -> {
                    binding.searchResultHot.setTextColor(Color.parseColor("#FF6247"))
                }
                R.id.search_result_new -> {
                    binding.searchResultNew.setTextColor(Color.parseColor("#FF6247"))
                }
            }
        }
        return false
    }

    fun clearData() {
        searchResultArrayAdapter?.clear()
        searchRelatedArrayAdapter?.clear()
        binding.searchRelativeTitle.text = ""
    }
}