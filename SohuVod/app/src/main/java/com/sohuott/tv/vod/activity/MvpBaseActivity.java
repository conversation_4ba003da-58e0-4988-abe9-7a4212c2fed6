package com.sohuott.tv.vod.activity;

import android.os.Bundle;

import com.sohuott.tv.vod.presenter.BasePresenter;
import com.sohuott.tv.vod.presenter.ReflectionPresenterFactory;
import com.sohuott.tv.vod.view.IView;

/**
 * Created by hpb on 2017/7/12.
 */
public abstract  class MvpBaseActivity<P extends BasePresenter> extends  BaseFragmentActivity implements
        IView{
    private P presenter;
    private ReflectionPresenterFactory<P> presenterFactory =
            ReflectionPresenterFactory.fromViewClass(getClass());
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getPresenter().attachView(this);

    }
    public P getPresenter(){
        if(presenter==null){
            presenter=presenterFactory.createPresenter();
        }
        return presenter;
    }

    @Override
    protected void onDestroy() {
        if(presenter!=null){
            presenter.detachView();
        }
        super.onDestroy();
    }

}
