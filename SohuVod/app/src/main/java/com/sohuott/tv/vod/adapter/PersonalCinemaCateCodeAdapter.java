package com.sohuott.tv.vod.adapter;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.PersonalCinemaModel;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Created by yizhang210244 on 2017/12/29.
 */

public class PersonalCinemaCateCodeAdapter extends PersonalCinemaCommonAdapter{

    private List<PersonalCinemaModel.CateCodeContentsBean> mCateCodeContentsBeanList;

    public PersonalCinemaCateCodeAdapter(RecyclerView rootRecyclerView, RecyclerView parentRecyclerView) {
        mRootRecyclerView = new WeakReference<RecyclerView>(rootRecyclerView).get();
        mParentRecyclerView = new WeakReference<RecyclerView>(parentRecyclerView).get();
        mParentRecyclerView.setOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if(recyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE
                        && mFocusBorderView != null){
                    View focusView = recyclerView.getFocusedChild();
                    mFocusBorderView.setFocusView(focusView);
                    FocusUtil.setFocusAnimator(focusView, mFocusBorderView);
                    reBindImage();
                }
            }
        });

    }

    public void setCateCodeContentsBeanList(List<PersonalCinemaModel.CateCodeContentsBean> cateCodeContentsBeanList) {
        mCateCodeContentsBeanList = cateCodeContentsBeanList;
    }

    private void reBindImage(){
        if(mParentRecyclerView != null){
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) mParentRecyclerView.getLayoutManager();
            if(linearLayoutManager != null){
                int lastItemPosition = linearLayoutManager.findLastVisibleItemPosition();
                int firstItemPosition = linearLayoutManager.findFirstVisibleItemPosition();
                for (int i = firstItemPosition; i <= lastItemPosition; i++) {
                    ViewHolder viewHolder = (ViewHolder) mParentRecyclerView.findViewHolderForAdapterPosition(i);
                    if(viewHolder != null){
                        if(viewHolder.isShouldBindImageAgain){
                            PersonalCinemaModel.CateCodeContentsBean contentsBean = mCateCodeContentsBeanList.get(i);
                            viewHolder.mSimpleDraweeView.setImageRes(contentsBean.getPicUrl());
                            viewHolder.isShouldBindImageAgain = false;
                        }
                    }
                }
            }
        }
    }


    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v;
        RecyclerView.ViewHolder viewHolder = null;
        v = LayoutInflater.from(parent.getContext()).inflate(R.layout.personal_cinema_cate_code_list_item, parent, false);
        viewHolder = new ViewHolder(v);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        if(viewHolder != null){
            viewHolder.mTitle.setText(mCateCodeContentsBeanList.get(position).getCateCodeName());
            if(mParentRecyclerView != null && mParentRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE){
                viewHolder.mSimpleDraweeView.setImageRes(mCateCodeContentsBeanList.get(position).getPicUrl());
                viewHolder.isShouldBindImageAgain = false;
            }else {
                viewHolder.isShouldBindImageAgain = true;
            }

        }
    }

    @Override
    public int getItemCount() {
        if(mCateCodeContentsBeanList != null){
            return mCateCodeContentsBeanList.size();
        }
        return 0;
    }

    class ViewHolder extends RecyclerView.ViewHolder{
        TextView mTitle;
        GlideImageView mSimpleDraweeView;
        boolean isShouldBindImageAgain = false;
        public ViewHolder(View itemView) {
            super(itemView);
            mTitle = (TextView) itemView.findViewById(R.id.titleTV);
            mSimpleDraweeView = (GlideImageView) itemView.findViewById(R.id.bgIV);
            mSimpleDraweeView.setClearWhenDetached(false);
            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if(event.getAction() != KeyEvent.ACTION_DOWN){
                        return false;
                    }
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_DPAD_LEFT:
                            if (getAdapterPosition() == 0) {
                                if(v.getAnimation() == null || v.getAnimation().hasEnded()){
                                    v.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                            R.anim.shake_x));
                                    if(mFocusBorderView != null){
                                        mFocusBorderView.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                R.anim.shake_x));
                                    }
                                }
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                            if (getAdapterPosition() == getItemCount() - 1) {
                                if(v.getAnimation() == null || v.getAnimation().hasEnded()){
                                    v.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                            R.anim.shake_x));
                                    if(mFocusBorderView != null){
                                        mFocusBorderView.startAnimation(AnimationUtils.loadAnimation(v.getContext(),
                                                R.anim.shake_x));
                                    }
                                }
                                return true;
                            }
                            break;
                        case KeyEvent.KEYCODE_DPAD_UP:
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            break;
                        default:
                            break;
                    }
                    return false;
                }
            });
            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if(hasFocus){
                        mSelectedPosition = getAdapterPosition();
                        if (mFocusBorderView != null) {
                            if(mRootRecyclerView != null&& mRootRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE){
                                return;
                            }
                            if(mParentRecyclerView != null && mParentRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE){
                                return;
                            }
                            mFocusBorderView.setFocusView(v);
                            FocusUtil.setFocusAnimator(v, mFocusBorderView);
                        }
                    }else {
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(v);
                            FocusUtil.setUnFocusAnimator(v);
                        }
                    }
                }
            });

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    int position = getAdapterPosition();
                    PersonalCinemaModel.CateCodeContentsBean cateCodeContentsBean = mCateCodeContentsBeanList.get(position);
                    if(cateCodeContentsBean != null){
                        ActivityLauncher.startLabelGridListActivity(v.getContext(), 0, "cat=" + cateCodeContentsBean.getCateCode(), cateCodeContentsBean.getCateCodeName());
                        RequestManager.getInstance().onEvent("6_personal_cinema", "6_personal_cinema_catecode_list_recommend_click",
                                String.valueOf(cateCodeContentsBean.getCateCode()), null, null, null, null);
                    }
                }
            });

        }
    }
}
