package com.sohuott.tv.vod.utils;

import android.content.Context;
import android.widget.ImageView;

import com.bumptech.glide.request.RequestOptions;
import com.sohuott.tv.vod.GlideApp;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohu.lib_utils.StringUtil;

/**
 * Created by wenjingbian on 2018/2/27.
 */

public class NewGlideUtils {

    /**
     * set image resource
     *
     * @param context   Context instance
     * @param imageView ImageView you want to set
     * @param url       image's url
     */
    public static void setImageRes(Context context, ImageView imageView, String url) {
        setImageRes(context, imageView, url, R.drawable.vertical_default_big_poster,
                R.drawable.vertical_default_big_poster, false, true, 0, 0);
    }

    public static void setImageRes(Context context, ImageView imageView, String url,
                                   boolean isCachedInMemo) {
        setImageRes(context, imageView, url, R.drawable.vertical_default_big_poster,
                R.drawable.vertical_default_big_poster, false, isCachedInMemo, 0, 0);
    }

    public static void setImageRes(Context context, ImageView imageView, String url,
                                   int placeHolderRes, int errorRes) {
        setImageRes(context, imageView, url, placeHolderRes, errorRes, true);
    }

    public static void setImageRes(Context context, ImageView imageView, String url,
                                   int placeHolderRes, int errorRes, boolean isCachedInMemo) {
        setImageRes(context, imageView, url, placeHolderRes, errorRes, false, isCachedInMemo, 0, 0);
    }

    public static void setCircleImageRes(Context context, ImageView imageView, String url) {
        setImageRes(context, imageView, url, R.drawable.default_avatar,
                R.drawable.default_avatar, true, true, 0, 0);
    }

    public static void setCircleImageRes(Context context, ImageView imageView, String url,
                                         boolean isCachedInMemo) {
        setImageRes(context, imageView, url, R.drawable.default_avatar,
                R.drawable.default_avatar, true, isCachedInMemo, 0, 0);
    }

    public static void setCircleImageRes(Context context, ImageView imageView, String url,
                                         int placeHolderRes, int errorRes) {
        setImageRes(context, imageView, url, placeHolderRes, errorRes, true, true, 0, 0);
    }

    public static void setCircleImageRes(Context context, ImageView imageView, String url,
                                         int placeHolderRes, int errorRes, boolean isCachedInMemo) {
        setImageRes(context, imageView, url, placeHolderRes, errorRes, true, isCachedInMemo, 0, 0);
    }

    public static void clearMemoCahe(Context context) {
        GlideApp.get(context).clearMemory();
    }

    public static void clearImageMerry(Context context,ImageView imageView){
        GlideApp.with(context).clear(imageView);
        if(imageView != null){
            imageView.setImageDrawable(null);
        }
    }

    private static void setImageRes(Context context, ImageView imageView, String url,

                                    int placeHolderRes, int errorRes, boolean isCircle,
                                    boolean isCachedInMemo, int height, int width) {
        if (StringUtil.isEmpty(url) || imageView == null || context == null) {
            LibDeprecatedLogger.e("Failed to set image resource to certain ImageView, since url or imageView or context is unavailable.");
            return;
        }

        if (placeHolderRes <= 0) {
            if (isCircle) {
                placeHolderRes = R.drawable.default_avatar;
            } else {
                placeHolderRes = R.drawable.vertical_default_big_poster;
            }
        }
        if (errorRes <= 0) {
            if (isCircle) {
                errorRes = R.drawable.default_avatar;
            } else {
                errorRes = R.drawable.vertical_default_big_poster;
            }
        }

        RequestOptions requestOptions = new RequestOptions()
                .placeholder(placeHolderRes)
                .error(errorRes);
        if (isCircle) {
            requestOptions = requestOptions.circleCrop();
        }
        if (!isCachedInMemo) {
            requestOptions = requestOptions.skipMemoryCache(true);
        }
        if(height >0 || width >0) {
            requestOptions = requestOptions.override(width, height);
        }
        GlideApp.with(context).load(url).apply(requestOptions).into(imageView);
    }
}
