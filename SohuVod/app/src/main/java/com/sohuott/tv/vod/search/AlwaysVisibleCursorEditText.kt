package com.sohuott.tv.vod.search

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatEditText

class AlwaysVisibleCursorEditText(context: Context, attrs: AttributeSet) : AppCompatEditText(context, attrs) {

    init {
        isCursorVisible = true
        isFocusable = false
        isFocusableInTouchMode = false
    }

    override fun onSelectionChanged(selStart: Int, selEnd: Int) {
        super.onSelectionChanged(selStart, selEnd)
        if (selStart == selEnd) {
            setSelection(selStart, if (selEnd == 0) 0 else selEnd + 1)
        }
    }
}
