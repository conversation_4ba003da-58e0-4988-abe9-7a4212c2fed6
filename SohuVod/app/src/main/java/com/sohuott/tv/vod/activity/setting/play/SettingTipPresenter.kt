package com.sohuott.tv.vod.activity.setting.play

import android.content.Context
import androidx.appcompat.widget.AppCompatTextView
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.getResColor

/**
 *
 * @Description
 * @date 2022/3/22 14:41
 * <AUTHOR>
 * @Version 1.0
 */
class SettingTipPresenter(private val context: Context) :
    DefaultPresenter(R.layout.item_privacy_setting_tip_layout) {
    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as SettingItem
        val name = viewHolder.getView<AppCompatTextView>(R.id.tv_privacy_tip)
        name.text = item.tips
        if (item.focusable) {
            name.setTextColor(context.getResColor(R.color.privacy_setting_text_color_selector))
        } else {
            name.setTextColor(context.getResColor(R.color.tv_color_69677f))
        }
    }
}