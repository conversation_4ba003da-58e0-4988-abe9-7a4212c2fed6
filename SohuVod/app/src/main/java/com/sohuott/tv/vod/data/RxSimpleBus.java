package com.sohuott.tv.vod.data;

import io.reactivex.subjects.PublishSubject;
import io.reactivex.subjects.Subject;

/**
 * Created by hpb on 2017/8/9.
 */
public class RxSimpleBus {

    private RxSimpleBus() {

    }

    private static class Holder {
        public static RxSimpleBus rxBus = new RxSimpleBus();
    }

    private final Subject<Object> _bus = PublishSubject.create();

    public static RxSimpleBus getInstance() {
        return Holder.rxBus;
    }

    public void send(Object o) {
        _bus.onNext(o);
    }

    public Subject<Object> toObserverable(Class<?> type) {
        _bus.ofType(type);
        return _bus;
    }

    public boolean hasObservers() {
        return _bus.hasObservers();
    }
}
