package com.sohuott.tv.vod.widget.lb;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sh.ott.video.view.ShVideoView;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.launcher.LauncherPlayerManager;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.widget.HomeViewJump;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by music on 2021/9/15.
 */

public class VipBannerView extends ConstraintLayout implements View.OnFocusChangeListener, View.OnClickListener, View.OnKeyListener {

    private List<ContentGroup.DataBean.ContentsBean> mList;
    private List<Button> mBtnList = new ArrayList();
    private List<View> mBtnSelectList = new ArrayList();

    private ImageHandler imageHandler;
//    private MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    private ValueAnimator mAnim1, mAnim2;


    private static class ImageHandler extends Handler {
        public static final int UPDATE = 0x1;
        public static final int DURATION = 4000; //3s轮播
        public static final int PLAY_VIDEO = 2000; //2s播放

        private WeakReference<VipBannerView> weakReference;

        public ImageHandler(VipBannerView vipBannerView) {
            weakReference = new WeakReference<VipBannerView>(vipBannerView);
        }

        @Override
        public void handleMessage(Message msg) {
            LibDeprecatedLogger.d("handleMessage, msg.what = " + msg.what);
            VipBannerView vipBannerView = weakReference.get();
            if (vipBannerView != null) {
                switch (msg.what) {
                    case UPDATE:
                        if (vipBannerView.launcherPlayerManager.isPlaying()) return;
                        Bundle b = msg.getData();
                        vipBannerView.mPreIndex = vipBannerView.mCurrentIndex;
                        vipBannerView.mCurrentIndex = b.getInt("index");
                        vipBannerView.updateUI();
                        break;
                    case PLAY_VIDEO:
//                        , vipBannerView.mCommonVideoView
                        vipBannerView.launcherPlayerManager.setPlayParamsAndPlay(vipBannerView.getContext(), vipBannerView.mList.get(vipBannerView.mCurrentIndex), null, true, false, vipBannerView.mCommonVideoView);
                        vipBannerView.mPlayRoot.setVisibility(VISIBLE);
                        vipBannerView.mPoster.setVisibility(GONE);
                        break;
                }
            }
        }
    }

    private ImageView mPoster;
    private Button mBtn1, mBtn2, mBtn3, mBtn4;
    private View mBtn1Select, mBtn2Select, mBtn3Select, mBtn4Select;
    private RelativeLayout mPosterRoot;
    private ConstraintLayout mBtnRoot;

    private ShVideoView mCommonVideoView;
    private CardView mPlayRoot;

    LauncherPlayerManager launcherPlayerManager;

    private int mCurrentIndex = 0;
    private int mPreIndex = -1;

    public VipBannerView(Context context) {
        super(context);
        init(context);
    }

    public VipBannerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public VipBannerView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        imageHandler = new ImageHandler(this);
        setFocusable(false);
        setFocusableInTouchMode(false);
        LayoutInflater.from(context).inflate(R.layout.vip_banner_view, this, true);
        launcherPlayerManager = new LauncherPlayerManager();
        initView();
        initListener();
//        if (mBrowseItemFocusHighlight == null) {
//            mBrowseItemFocusHighlight =
//                    new MyFocusHighlightHelper
//                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_SMALL, false);
//        }
    }

    private void initView() {
        mPoster = (ImageView) findViewById(R.id.vip_header_poster);
        mPosterRoot = (RelativeLayout) findViewById(R.id.vip_header_poster_root);
        mBtn1 = (Button) findViewById(R.id.vip_header_banner_btn1);
        mBtn2 = (Button) findViewById(R.id.vip_header_banner_btn2);
        mBtn3 = (Button) findViewById(R.id.vip_header_banner_btn3);
        mBtn4 = (Button) findViewById(R.id.vip_header_banner_btn4);
        mBtnRoot = (ConstraintLayout) findViewById(R.id.vip_header_banner_root);

        mBtn1Select = findViewById(R.id.vip_header_banner_btn1_selected);
        mBtn2Select = findViewById(R.id.vip_header_banner_btn2_selected);
        mBtn3Select = findViewById(R.id.vip_header_banner_btn3_selected);
        mBtn4Select = findViewById(R.id.vip_header_banner_btn4_selected);

        mBtnList.add(mBtn1);
        mBtnList.add(mBtn2);
        mBtnList.add(mBtn3);
        mBtnList.add(mBtn4);

        mBtnSelectList.add(mBtn1Select);
        mBtnSelectList.add(mBtn2Select);
        mBtnSelectList.add(mBtn3Select);
        mBtnSelectList.add(mBtn4Select);

        mCommonVideoView = findViewById(R.id.player_view);
        mPlayRoot = findViewById(R.id.root_player);
    }

    private void initListener() {
        mPosterRoot.setOnFocusChangeListener(this);
        mBtn1.setOnFocusChangeListener(this);
        mBtn2.setOnFocusChangeListener(this);
        mBtn3.setOnFocusChangeListener(this);
        mBtn4.setOnFocusChangeListener(this);

        mPosterRoot.setOnClickListener(this);
        mBtn1.setOnClickListener(this);
        mBtn2.setOnClickListener(this);
        mBtn3.setOnClickListener(this);
        mBtn4.setOnClickListener(this);

        mPosterRoot.setOnKeyListener(this);
//        mBtn1.setOnKeyListener(this);
//        mBtn2.setOnKeyListener(this);
//        mBtn3.setOnKeyListener(this);
//        mBtn4.setOnKeyListener(this);


        launcherPlayerManager.setPlayerCallback(new LauncherPlayerManager.PlayerCallback() {

            @Override
            public void onPlayCompleted() {
                LibDeprecatedLogger.d("VipBannerView onPlayCompleted");
                mPlayRoot.setVisibility(GONE);
                mPoster.setVisibility(VISIBLE);
                sendIndex();
            }

            @Override
            public void onPlayed() {
                LibDeprecatedLogger.d("VipBannerView onPlayed");
            }

            @Override
            public void onError() {
                LibDeprecatedLogger.d("VipBannerView onError");
                mPlayRoot.setVisibility(GONE);
            }
        });
    }


    public void setData(List<ContentGroup.DataBean.ContentsBean> list) {
        this.mList = list;
        for (int i = 0; i < mList.size(); i++) {
            mBtnList.get(i).setText(mList.get(i).name);
        }
        updateUI();
    }

    public void updateUI() {
        LibDeprecatedLogger.d("getVisibility()" + getVisibility());
        mPoster.setVisibility(VISIBLE);
        if (mPoster.getVisibility() == VISIBLE) {
            if (mPreIndex != -1) {
                startAlphaAnim(mList.get(mCurrentIndex).picUrl);
            } else {
                Glide.with(getContext())
                        .load(mList.get(mCurrentIndex).picUrl)
                        .error(R.drawable.vertical_default_big_poster)
                        .transform(new RoundedCorners(getContext().getResources().getDimensionPixelOffset(R.dimen.x10)))
                        .into(mPoster);
            }
            LibDeprecatedLogger.d("mCurrentIndex = " + mCurrentIndex + ", mList.size() = " + mList.size() + ", mList.get(mCurrentIndex).name = " + mList.get(mCurrentIndex).name);

            //设置按钮selected状态
            setBtnSelected(mCurrentIndex);
            //判断是否有片花，开始播放
//
//            if (mList.get(mCurrentIndex).parameterPianhua != null && Util.getHuapingParams(getContext()) == 0) {
//                launcherPlayerManager.setPlayParamsAndPlay(getContext(), mList.get(mCurrentIndex), null, mCommonVideoView);
//                mPlayRoot.setVisibility(VISIBLE);
//            } else {
//                mPlayRoot.setVisibility(GONE);
//            }
            //开始轮播
            sendIndex();
            sendVideoPlay();
        }
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
//        mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
        if (hasFocus) {
            switch (v.getId()) {
                case R.id.vip_header_banner_btn1:
                    mBtnRoot.bringToFront();
                    setSelected(0);
                    setBtnSelectInVisible();
                    imageHandler.removeMessages(ImageHandler.UPDATE);
                    break;
                case R.id.vip_header_banner_btn2:
                    mBtnRoot.bringToFront();
                    setSelected(1);
                    setBtnSelectInVisible();
                    imageHandler.removeMessages(ImageHandler.UPDATE);
                    break;
                case R.id.vip_header_banner_btn3:
                    mBtnRoot.bringToFront();
                    setSelected(2);
                    setBtnSelectInVisible();
                    imageHandler.removeMessages(ImageHandler.UPDATE);
                    break;
                case R.id.vip_header_banner_btn4:
                    mBtnRoot.bringToFront();
                    setSelected(3);
                    setBtnSelectInVisible();
                    imageHandler.removeMessages(ImageHandler.UPDATE);
                    break;
                case R.id.vip_header_poster_root:
//                    mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
                    mPosterRoot.bringToFront();
                    imageHandler.removeMessages(ImageHandler.UPDATE);
                    setBtnSelected(mCurrentIndex);
                    break;
            }
        } else {
            setBtnSelected(mCurrentIndex);
        }
    }


    @Override
    public void onClick(View v) {
        int index = 0;
        switch (v.getId()) {
            case R.id.vip_header_poster_root:
                index = mCurrentIndex;
                break;
            case R.id.vip_header_banner_btn1:
                index = 0;
                break;
            case R.id.vip_header_banner_btn2:
                index = 1;
                break;
            case R.id.vip_header_banner_btn3:
                index = 2;
                break;
            case R.id.vip_header_banner_btn4:
                index = 3;
                break;
        }

        HomeViewJump.clickAlbum(getContext(), mList.get(index), 0, false, index);
        releasePlayer();

        HashMap<String, String> memoInfo = new HashMap<>();
        if (mList.get(index).parameterPianhua != null && !mList.get(index).parameterPianhua.isEmpty()) {
            memoInfo.put("ctype", "1");
        } else {
            memoInfo.put("ctype", "0");
        }
        RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), mList.get(index).pathInfo, mList.get(index).objectInfo,
                memoInfo);

    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        LibDeprecatedLogger.d("dispatchKeyEvent: " + event.toString() + findFocus().toString());
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP) {
                if (mBtn1.equals(findFocus()) || mPosterRoot.equals(findFocus())) {
                    sendIndex();
                }
            }
            if (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN) {
                if (mBtn4.equals(findFocus()) || mPosterRoot.equals(findFocus())) {
                    sendIndex();
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (v.equals(mPosterRoot)) {
            if (event.getAction() == KeyEvent.ACTION_DOWN) {
                if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    mBtnList.get(mCurrentIndex).requestFocus();
                    return true;
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                        || keyCode == KeyEvent.KEYCODE_DPAD_UP
                        || keyCode == KeyEvent.ACTION_DOWN) {
                    sendIndex();
                }
            }
        } else {
            //如果视频显示，且没有播放，不响应左键
            return event.getAction() == KeyEvent.ACTION_DOWN
                    && keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                    && mPlayRoot.getVisibility() == VISIBLE
                    && !launcherPlayerManager.isPlaying();
        }
        return false;
    }

    private void setSelected(int index) {
        if (index == mCurrentIndex) return;
        mPreIndex = mCurrentIndex;
        mCurrentIndex = index;
        mPlayRoot.setVisibility(GONE);
        launcherPlayerManager.releasePlayer();
        startAlphaAnim(mList.get(index).picUrl);

        sendVideoPlay();

//        if (mList.get(index).parameterPianhua == null || Util.getHuapingParams(getContext()) == 1 || mList.get(index).parameterPianhua.equals("")) {
////            mPlayRoot.setVisibility(GONE);
////            launcherPlayerManager.releasePlayer();
//        } else {
//
////            launcherPlayerManager.setPlayParamsAndPlay(getContext(), mList.get(index), null, mCommonVideoView);
////            mPlayRoot.setVisibility(VISIBLE);
//        }

    }

    private void setBtnSelectInVisible() {
        for (int i = 0; i < mBtnSelectList.size(); i++) {
            mBtnSelectList.get(i).setVisibility(GONE);
        }
    }

    private void setBtnSelected(int index) {
        for (int i = 0; i < mBtnSelectList.size(); i++) {
            if (i == index) {
                mBtnSelectList.get(i).setVisibility(VISIBLE);
            } else {
                mBtnSelectList.get(i).setVisibility(GONE);
            }
        }
    }

    private int plusCurrentIndex() {
        if (mCurrentIndex == 3) {
            return 0;
        } else {
            return mCurrentIndex + 1;
        }
    }

    private void sendVideoPlay() {
        imageHandler.removeMessages(ImageHandler.PLAY_VIDEO);
        if (mList.get(mCurrentIndex).parameterPianhua != null
                && Util.getHuapingParams(getContext()) == 0
                && !mList.get(mCurrentIndex).parameterPianhua.equals("")
                && Util.getDynamicVideoParams(getContext())) {
            Message msg = Message.obtain();
            msg.what = ImageHandler.PLAY_VIDEO;
            imageHandler.sendMessageDelayed(msg, ImageHandler.PLAY_VIDEO);
            imageHandler.removeMessages(ImageHandler.UPDATE);
        } else {
            mPlayRoot.setVisibility(GONE);
        }
    }

    public void sendIndex() {
        imageHandler.removeMessages(ImageHandler.UPDATE);
        if (launcherPlayerManager.isPlaying()) return;
        Message msg = Message.obtain();
        Bundle bundle = new Bundle();
        bundle.putInt("index", plusCurrentIndex());
        msg.what = ImageHandler.UPDATE;
        msg.setData(bundle);
        imageHandler.sendMessageDelayed(msg, ImageHandler.DURATION);
    }


    public void startAlphaAnim(final String posterUrl) {
        if (mAnim1 != null) {
            if (mAnim1.isStarted() || mAnim1.isRunning()) {
                mAnim1.cancel();
            }
        }
        if (mAnim2 != null) {
            if (mAnim2.isStarted() || mAnim2.isRunning()) {
                mAnim2.cancel();
            }
        }

        mAnim1 = ValueAnimator.ofInt(255, 30);
        mAnim1.setDuration(500);

        LibDeprecatedLogger.d("mAnim1 : onAnimationUpdate");

        mAnim1.addUpdateListener(animation -> {

            int currentValue = (Integer) animation.getAnimatedValue();

            mPoster.setImageAlpha(currentValue);

            mPoster.requestLayout();

        });

        mAnim1.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                mPoster.setVisibility(VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {

                Glide.with(getContext())
                        .load(posterUrl)
                        .error(R.drawable.vertical_default_big_poster)
                        .transform(new RoundedCorners(getContext().getResources().getDimensionPixelOffset(R.dimen.x10)))
                        .into(mPoster);

                mAnim2 = ValueAnimator.ofInt(30, 255);
                mAnim2.setDuration(600);
                LibDeprecatedLogger.d("mAnim2 : onAnimationUpdate");

                mAnim2.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        int currentValue = (Integer) animation.getAnimatedValue();


                        mPoster.setImageAlpha(currentValue);

                        mPoster.requestLayout();
                    }
                });
                mAnim2.start();
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        mAnim1.start();
    }

    @Override
    protected void dispatchVisibilityChanged(View changedView, int visibility) {
        super.dispatchVisibilityChanged(changedView, visibility);
        if (visibility != VISIBLE) {
            mPlayRoot.setVisibility(GONE);
            launcherPlayerManager.releasePlayer();
        }
        LibDeprecatedLogger.d("VipBannerView dispatchVisibilityChanged: " + visibility);
    }

    @Override
    protected void onDetachedFromWindow() {
        LibDeprecatedLogger.d("VipBannerView onDetachedFromWindow");
        imageHandler.removeMessages(ImageHandler.UPDATE);
        launcherPlayerManager.releasePlayer();
        mPlayRoot.setVisibility(GONE);
        super.onDetachedFromWindow();
    }

    public void releasePlayer() {
        LibDeprecatedLogger.d("VipBannerView releasePlayer");
        launcherPlayerManager.releasePlayer();
        mPlayRoot.setVisibility(GONE);
        mPoster.setVisibility(VISIBLE);
        imageHandler.removeMessages(ImageHandler.PLAY_VIDEO);
        imageHandler.removeMessages(ImageHandler.UPDATE);
    }

}
