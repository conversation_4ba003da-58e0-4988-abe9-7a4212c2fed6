package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.os.Handler;
import android.os.Message;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.LabelGridListActivity;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.CustomRecyclerViewNew;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.CornerTagImageView;

import java.util.List;

/**
 * Created by wenjingbian on 2017/9/19.
 */

public class LabelGridListAdapter extends RecyclerView.Adapter<LabelGridListAdapter.LabelGridListViewHolder> {

    private static final int MSG_IMG_LOAD = 1;
    private static final int MSG_DELAY_CONTINUE_CLICK = 2;

    private FocusBorderView mFocusView;
    private CustomRecyclerViewNew mRecyclerView;
    private Context mContext;

    private LabelGridListActivity mActivity;
    private MyHandler mHandler;

    private List<ListAlbumModel> mDataSource;
    private boolean isFirst = true;
    private boolean isEnabledUpKey = true;
    private int mKeyCode; //value to record lastly keyCode
    private long mClickedTime;

    public LabelGridListAdapter(Context context, CustomRecyclerViewNew recyclerView) {
        this.mContext = context;
        this.mRecyclerView = recyclerView;
        mHandler = new MyHandler();
        mActivity = (LabelGridListActivity) mContext;
    }

    @Override
    public LabelGridListViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.list_grid_item, parent, false);
        LabelGridListViewHolder viewHolder = new LabelGridListViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(LabelGridListViewHolder holder, int position) {
        ListAlbumModel model = mDataSource.get(holder.getAdapterPosition());
        if (model == null) {
            return;
        }
        holder.label_poster.resetImageRes();
        holder.label_poster.setCornerHeightRes(R.dimen.y33);
        holder.label_poster.setCornerTypeWithType(model.tvIsFee, model.tvIsEarly, model.useTicket, model.paySeparate, model.cornerType);
        String scoreTxt = !TextUtils.isEmpty(model.scoreSource) && model.scoreSource.equals("1")
                ? model.score : model.doubanScore;
        boolean isVisible = (TextUtils.isEmpty(model.scoreSource) || !model.scoreSource.equals("1"))&& !model.score.equals("0.0");
        holder.label_score.setText(scoreTxt);
        if (isVisible) {
            holder.label_douban.setVisibility(View.VISIBLE);
        } else {
            holder.label_douban.setVisibility(View.GONE);
        }
        holder.label_title.setText(model.tvName);
        holder.label_title.setSelected(false);
        holder.label_title.setEllipsize(TextUtils.TruncateAt.MARQUEE);
        holder.label_title.setMarqueeRepeatLimit(-1);

        if (holder.getAdapterPosition() == 0) {
            loadImage();
            mActivity.setCurrLine(true, 0);
            if (isFirst) {
                holder.itemView.requestFocus();
                isFirst = false;
            }
        }
    }

    @Override
    public int getItemCount() {
        return mDataSource != null ? mDataSource.size() : 0;
    }

    public void setDataSource(List<ListAlbumModel> dataSource) {
        this.mDataSource = dataSource;
    }

    public void setFocusView(FocusBorderView focusView) {
        this.mFocusView = focusView;
    }

    public int getKeyCode() {
        return mKeyCode;
    }

    public void loadImage() {
        mHandler.removeMessages(MSG_IMG_LOAD);
        mHandler.sendEmptyMessageDelayed(MSG_IMG_LOAD, 500);
    }

    public void addItems(List<ListAlbumModel> dataSource) {
        if (mDataSource != null) {
            int start = mDataSource.size();
            mDataSource.addAll(dataSource);
            notifyItemRangeInserted(start, dataSource.size());
        }
    }

    public void releaseAll() {
        mContext = null;
        mRecyclerView = null;
        mActivity = null;
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
        if (mDataSource != null) {
            mDataSource.clear();
            mDataSource = null;
        }
    }

    class LabelGridListViewHolder extends RecyclerView.ViewHolder {

        CornerTagImageView label_poster;
        TextView label_title, label_score;
        ImageView label_douban;


        public LabelGridListViewHolder(final View itemView) {
            super(itemView);
            label_poster = (CornerTagImageView) itemView.findViewById(R.id.grid_model_image);
            label_title = (TextView) itemView.findViewById(R.id.grid_model_title);
            label_score = (TextView) itemView.findViewById(R.id.scoreTV);
            label_douban = (ImageView) itemView.findViewById(R.id.doubangIV);
            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    label_title.setSelected(hasFocus);
                    if (hasFocus) {
                        if (!mRecyclerView.getIsLongPressed()) {
                            mActivity.setCurrLine(true, getAdapterPosition());
                        }
                        if (mRecyclerView.getScrollState() != RecyclerView.SCROLL_STATE_IDLE) {
                            return;
                        }
                        if (mActivity.checkColumnNum(getAdapterPosition()) && mFocusView != null) {
                            mFocusView.setFocusView(v);
                            FocusUtil.setFocusAnimator(v, mFocusView, FocusUtil.HOME_SCALE, 100);
                        }
                    } else {
                        if (mFocusView != null) {
                            mFocusView.setUnFocusView(v);
                        }
                        FocusUtil.setUnFocusAnimator(v, 100);
                    }
                }
            });

            itemView.setOnKeyListener(new View.OnKeyListener() {
                @Override
                public boolean onKey(View v, int keyCode, KeyEvent event) {
                    if (!isEnabledUpKey) {
                        return true;
                    }

                    int position = getAdapterPosition();
                    if (event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (mRecyclerView.getIsLongPressed()) {
                            if (mKeyCode != keyCode) {
                                return true;
                            }
                        }
                        mKeyCode = keyCode;
                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN || keyCode == KeyEvent.KEYCODE_DPAD_UP) {
                            if (mClickedTime == 0) {
                                mClickedTime = System.currentTimeMillis();
                                mActivity.setColumnNum(getAdapterPosition() % 6);
                            } else if (System.currentTimeMillis() - mClickedTime > 500
                                    && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                                mClickedTime = System.currentTimeMillis();
                                mActivity.setColumnNum(getAdapterPosition() % 6);
                                ((LabelGridListActivity) mContext).setFocusability(true);
                            } else {
                                mClickedTime = System.currentTimeMillis();
                                ((LabelGridListActivity) mContext).setFocusability(false);
                            }
                        }
                    }
                    //for the first line items
                    if (position / 6 == 0 && keyCode == KeyEvent.KEYCODE_DPAD_UP
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (event.getRepeatCount() == 0 && mRecyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE) {
                            mActivity.focusOnTopBar();
                            mActivity.setCurrLine(false, -1);
                        }
                        return true;
                    }
                    //for the second line items to avoid continue click in short time(for XiaoMi TV)
                    if (position / 6 == 1 && keyCode == KeyEvent.KEYCODE_DPAD_UP
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        isEnabledUpKey = false;
                        mHandler.sendEmptyMessageDelayed(MSG_DELAY_CONTINUE_CLICK, 500);
                    }
                    //for the last row of video list
                    if (position / 6 == (getItemCount() - 1) / 6 && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                            return true;
                        } else if (position == getItemCount() - 1 && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                            return true;
                        }
                    }
                    //for the last item of every line
                    if (position % 6 == 5 && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (mRecyclerView != null) {
                            RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(position + 1);
                            if (viewHolder != null && viewHolder.itemView != null) {
                                viewHolder.itemView.requestFocus();
                            }
                            return true;
                        }
                    }
                    //for the first item of every line
                    if (position % 6 == 0 && keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                            && event.getAction() == KeyEvent.ACTION_DOWN) {
                        return true;
                    }
                    return false;
                }
            });


            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ActivityLauncher.startVideoDetailActivity(mContext, mDataSource.get(getAdapterPosition()).id, Constant.PAGE_LABEL_GRID_LIST);
                    RequestManager.getInstance().onLabelGridListItemClickEvent(mDataSource.get(getAdapterPosition()).id);
                }
            });
        }
    }

    class MyHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            int firstPos = ((GridLayoutManager) mRecyclerView.getLayoutManager()).findFirstVisibleItemPosition();
            int lastPos = ((GridLayoutManager) mRecyclerView.getLayoutManager()).findLastVisibleItemPosition();

            switch (msg.what) {
                case MSG_IMG_LOAD:
                    if (firstPos < 0 || lastPos >= getItemCount()) {
                        return;
                    }
                    LabelGridListViewHolder viewHolder;
                    CornerTagImageView poster;
                    for (int i = firstPos; i <= lastPos; i++) {
                        viewHolder = (LabelGridListViewHolder) mRecyclerView.findViewHolderForAdapterPosition(i);
                        poster = (CornerTagImageView) viewHolder.itemView.findViewById(R.id.grid_model_image);
                        poster.setImageRes(mDataSource.get(i).albumExtendsPic_240_330);
                    }
                    break;
                case MSG_DELAY_CONTINUE_CLICK:
                    isEnabledUpKey = true;
                    break;
                default:
                    break;
            }
        }
    }
}
