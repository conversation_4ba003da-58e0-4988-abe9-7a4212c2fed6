package com.sohuott.tv.vod.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;

import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.SearchInputActivity;
import com.sohuott.tv.vod.search.TNinePopLayoutListener;
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight;
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by feng<PERSON><PERSON> on 17-6-15.
 */
public class TNineKeyboardPopView extends RelativeLayout implements View.OnClickListener, View.OnKeyListener, View.OnFocusChangeListener {
    private String mPageName = "6_search";

    private static final String TAG = TNineKeyboardPopView.class.getSimpleName();


    public MyFocusHighlightHelper.BrowseItemFocusHighlight mBrowseItemFocusHighlight;

    private List<Button> buttonList = new ArrayList<>();

    private TNinePopLayoutListener tNinePopLayoutListener;

    public TNineKeyboardPopView(Context context) {
        super(context);
        init(context);
    }

    public TNineKeyboardPopView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public TNineKeyboardPopView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public void customLayoutInflater(Context context){
        LayoutInflater.from(context).inflate(
                R.layout.tnine_keyboard_pop_view, this, true);
    }

    private void init(Context context) {
        //LayoutInflater.from(context).inflate(R.layout.tnine_keyboard_pop_view, this, true);
        customLayoutInflater(context);
        for(int i = 0; i < getChildCount(); i++) {
            Button button = (Button) getChildAt(i);
            button.setOnClickListener(this);
            button.setOnKeyListener(this);
            button.setOnFocusChangeListener(this);
            buttonList.add(button);
        }

        if (mBrowseItemFocusHighlight == null) {
            mBrowseItemFocusHighlight =
                    new MyFocusHighlightHelper
                            .BrowseItemFocusHighlight(FocusHighlight.ZOOM_FACTOR_XSMALL, false);
        }
    }

    public void setPageName(String pageName){
        this.mPageName = pageName;
    }

    @Override
    public void onClick(View v) {
        if(tNinePopLayoutListener != null) {
            tNinePopLayoutListener.onPressButton(((Button)v).getText().toString());
            RequestManager.getInstance().onClickSearchT9PopItem(mPageName,((Button)v).getText().toString());
        }
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if(keyCode == KeyEvent.KEYCODE_BACK) {
            if(event.getAction() == KeyEvent.ACTION_DOWN) {
                return true;
            } else {
                if(tNinePopLayoutListener != null) {
                    tNinePopLayoutListener.onPressBack(((Button)v).getText().toString());
                    return true;
                }
            }
        }
        return false;
    }

    public void show(String content) {
        if(TextUtils.isEmpty(content)) {
            LibDeprecatedLogger.w("content is null");
            return;
        }
        int length = content.length();
        if(length == 2) {
            buttonList.get(0).setVisibility(View.INVISIBLE);
            buttonList.get(1).setVisibility(View.INVISIBLE);
            buttonList.get(4).setVisibility(View.INVISIBLE);
            buttonList.get(2).setVisibility(View.VISIBLE);
            buttonList.get(3).setVisibility(View.VISIBLE);
            buttonList.get(2).setText(String.valueOf(content.charAt(0)));
            buttonList.get(3).setText(String.valueOf(content.charAt(1)));
            buttonList.get(2).setNextFocusLeftId(buttonList.get(2).getId());
            buttonList.get(2).setNextFocusUpId(buttonList.get(2).getId());
            buttonList.get(2).setNextFocusDownId(buttonList.get(2).getId());
            buttonList.get(2).requestFocus();
        } else {
            for(int i = 0; i < length; i++) {
                buttonList.get(i).setVisibility(View.VISIBLE);
                buttonList.get(i).setText(String.valueOf(content.charAt(i)));
            }
            for(int i = length; i < buttonList.size(); i++) {
                buttonList.get(i).setVisibility(View.INVISIBLE);
            }
            buttonList.get(2).setNextFocusLeftId(buttonList.get(1).getId());
            buttonList.get(2).setNextFocusUpId(buttonList.get(0).getId());
            if(length == 4) {
                buttonList.get(2).setNextFocusDownId(buttonList.get(2).getId());
            } else if(length == 5) {
                buttonList.get(2).setNextFocusDownId(buttonList.get(4).getId());
            }
            buttonList.get(2).requestFocus();
        }
    }

    public void settTNinePopLayoutListener(TNinePopLayoutListener listener) {
        tNinePopLayoutListener = listener;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        mBrowseItemFocusHighlight.onItemFocused(v, hasFocus);
    }
}
