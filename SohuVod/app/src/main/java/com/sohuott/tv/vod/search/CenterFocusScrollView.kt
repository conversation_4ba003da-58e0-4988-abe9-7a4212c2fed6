package com.sohuott.tv.vod.search

import android.content.Context
import android.util.AttributeSet
import android.view.KeyEvent
import android.widget.ScrollView

class CenterFocusScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ScrollView(context, attrs, defStyleAttr) {

    private val fixedScrollDistance = 200 // 设定每次滑动的固定距离

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event.action == KeyEvent.ACTION_DOWN) {
            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    smoothScrollBy(0, fixedScrollDistance)
                    return true
                }
                KeyEvent.KEYCODE_DPAD_UP -> {
                    smoothScrollBy(0, -fixedScrollDistance)
                    return true
                }
            }
        }
        return super.dispatchKeyEvent(event)
    }
}
