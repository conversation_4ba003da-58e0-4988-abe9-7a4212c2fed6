package com.sohuott.tv.vod.videodetail.activity.repository

import GsonConverter
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohuott.tv.vod.app.App
import com.sohuott.tv.vod.videodetail.activity.model.DrmPlayInfoData
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.coroutineScope
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class DrmRepository {
    /**
     * @param drmType 1 Google Widevine
     *
     * 2 Apple FairPlay
     *
     * 3 Marlin BB
     *
     * 4 PlayReady
     *
     * @param vid 清晰度vid
     */
    suspend fun requestDrmPlayInfo(
        vid: Int?,
        drmType: Int,
        mkey: String?
    ): Deferred<DrmPlayInfoData> {
        return coroutineScope {
            val url =
                if (App.debug) "https://uat-hot.vrs.sohu.com/v2/video/drm/play.action?" else "https://hotvrs.vod.ystyt.aisee.tv/v2/video/drm/play.action?"
            Get<DrmPlayInfoData>(url) {
                setHeaders(HeaderHelper.getHeaders().toHeaders())
                addHeader(HeaderHelper.getCurlOpenKey(),"true")

                setClient {
                    trustSSLCertificate()
                    connectionSpecs(
                        listOf(
                            ConnectionSpec.MODERN_TLS,
                            ConnectionSpec.COMPATIBLE_TLS,
                            ConnectionSpec.CLEARTEXT
                        )
                    )
                }
                converter = GsonConverter()
                addQuery("prod", "ott")
                addQuery("vid", vid)
                addQuery("type", drmType)
                addQuery("mkey", mkey ?: "")
            }
        }
    }
}