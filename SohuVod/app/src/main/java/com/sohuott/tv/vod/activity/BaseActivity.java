package com.sohuott.tv.vod.activity;

import static com.xiaomi.mistatistic.sdk.MiStatInterface.UPLOAD_POLICY_REALTIME;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.base.ActivityManagerUtil;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.AudioFocusManager;
import com.sohuott.tv.vod.utils.UserPayStatusHelper;
import com.xiaomi.mistatistic.sdk.MiStatInterface;

import org.greenrobot.eventbus.EventBus;

/**
 * Created by fenglei on 15-11-24.
 */
public class BaseActivity extends Activity {
    protected String mPageName;
    private LoginUserInformationHelper mHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
//        if(UrlWrapper.DEBUG) {
//            StrictMode.setVmPolicy(new StrictMode.VmPolicy.Builder()
//                    .detectLeakedSqlLiteObjects()
//                    .detectLeakedClosableObjects()
//                    .penaltyLog()
//                    .penaltyDeath()
//                    .build());
//        }
        super.onCreate(savedInstanceState);
        if (getIntent() != null) {
            BaseActivityUtil.handleIntentSource(getIntent(), getLocalClassName(), this);
        }
        if (isEventBusAvailable()) {
            EventBus.getDefault().register(this);
        }

        ActivityManagerUtil.addActivityToList(this);

        mHelper = LoginUserInformationHelper.getHelper(this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            BaseActivityUtil.handleIntentSource(intent, getLocalClassName(), this);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (Util.getPartnerNo(this).equals(Constant.PARTNER_NO_XIAOMI_STORE_CHANNEL)) {
            //抢占音频焦点

            AudioFocusManager audioFocusManager = new AudioFocusManager(this);
            audioFocusManager.setOnAudioFocusChangeListener(focusChange -> {
                LibDeprecatedLogger.d("AudioFocusChange : " + focusChange);
            });
            audioFocusManager.requestFocus();
            try {
                MiStatInterface.recordPageStart(this, this.getLocalClassName());
            } catch (Exception e){
                MiStatInterface.initialize(this, "2882303761517411177", "5191741170177", Util.getPartnerNo(this));
                MiStatInterface.setUploadPolicy(UPLOAD_POLICY_REALTIME, 0);
                MiStatInterface.recordPageStart(this, this.getLocalClassName());
            }
        }
        if (TextUtils.isEmpty(mPageName)) {
            Log.e("RequestManager", "add activity type onCreate");
            mPageName = this.getLocalClassName();
        }
        RequestManager.getInstance().recordPageStart(mPageName);

        if (mHelper.getIsLogin()) {
            //refreshUser
            UserPayStatusHelper.getUserInfo(this, 0, mHelper.getLoginPassport(), mHelper.getLoginToken());
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (Util.getPartnerNo(this).equals(Constant.PARTNER_NO_XIAOMI_STORE_CHANNEL)) {
            MiStatInterface.recordPageEnd();
        }
        RequestManager.getInstance().recordPageEnd();
    }

    public void setPageName(String pageName) {
        mPageName = pageName;
    }


    @Override
    protected void onStop() {
        super.onStop();
        if (Util.isOtherPackageOnStackTop(this)) {
            RequestManager.getInstance().onMccEndEvent();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LibDeprecatedLogger.d("onDestroy(), start time: " + System.currentTimeMillis());

        if (isEventBusAvailable()) {
            EventBus.getDefault().unregister(this);
        }

        ActivityManagerUtil.removeActivityFromList(this);
        LibDeprecatedLogger.d("onDestroy(), end time: " + System.currentTimeMillis());
    }

    @Override
    public void startActivity(Intent intent) {
        super.startActivity(intent);
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }

    @Override
    public void startActivity(Intent intent, Bundle options) {
        super.startActivity(intent, options);
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        super.startActivityForResult(intent, requestCode);
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode, Bundle options) {
        super.startActivityForResult(intent, requestCode, options);
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }

    protected boolean isEventBusAvailable() {
        return false;
    }

}
