package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.databinding.ViewSpeedSelectBinding;

public class SpeedSelectView extends LinearLayout implements View.OnClickListener {

    LinearLayout root;
    TextView tvSpeed0_5;
    TextView tvSpeed0_8;
    TextView tvSpeed1_0;
    TextView tvSpeed1_25;
    TextView tvSpeed1_5;
    TextView tvSpeed2_0;

    private ViewSpeedSelectBinding mBinding;

    private static OnSpeedViewListener mOnSpeedViewListener;
    private float mCurrentSpeed;
    private ViewGroup mContainer;

    public SpeedSelectView(float currentSpeedl, Context context) {
        super(context);
        mCurrentSpeed = currentSpeedl;
        init(context);
    }

    private void init(Context context) {
        View mView = inflate(context, R.layout.view_speed_select, this);
        mBinding = ViewSpeedSelectBinding.bind(mView);
        root= mBinding.rootSpeedSelect;
        tvSpeed0_5= mBinding.tvSpeed05;
        tvSpeed0_8= mBinding.tvSpeed08;
        tvSpeed1_0= mBinding.tvSpeed10;
        tvSpeed1_25= mBinding.tvSpeed125;
        tvSpeed1_5= mBinding.tvSpeed15;
        tvSpeed2_0= mBinding.tvSpeed20;
        root.setOnClickListener(this);
        tvSpeed0_5.setOnClickListener(this);
        tvSpeed0_8.setOnClickListener(this);
        tvSpeed1_0.setOnClickListener(this);
        tvSpeed1_25.setOnClickListener(this);
        tvSpeed1_5.setOnClickListener(this);
        tvSpeed2_0.setOnClickListener(this);
        selectColor();

        tvSpeed0_5.setOnKeyListener((view, keycode, keyEvent) -> {
            if (keycode == KeyEvent.KEYCODE_DPAD_UP && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                return true;
            }
            return false;
        });

        tvSpeed2_0.setOnKeyListener((view, keycode, keyEvent) -> {
            if (keycode == KeyEvent.KEYCODE_DPAD_DOWN && keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                return true;
            }
            return false;
        });
    }

    private void selectColor() {
        tvSpeed0_5.setTextColor(getResources().getColor(R.color.c1));
        tvSpeed0_8.setTextColor(getResources().getColor(R.color.c1));
        tvSpeed1_0.setTextColor(getResources().getColor(R.color.c1));
        tvSpeed1_25.setTextColor(getResources().getColor(R.color.c1));
        tvSpeed1_5.setTextColor(getResources().getColor(R.color.c1));
        tvSpeed2_0.setTextColor(getResources().getColor(R.color.c1));

        TextView target = null;
        if (mCurrentSpeed == Speed.SPEED_0_5) {
            target = tvSpeed0_5;
        } else if (mCurrentSpeed == Speed.SPEED_0_8) {
            target = tvSpeed0_8;
        } else if (mCurrentSpeed == Speed.SPEED_1_0) {
            target = tvSpeed1_0;
        } else if (mCurrentSpeed == Speed.SPEED_1_25) {
            target = tvSpeed1_25;
        } else if (mCurrentSpeed == Speed.SPEED_1_5) {
            target = tvSpeed1_5;
        } else if (mCurrentSpeed == Speed.SPEED_2_0) {
            target = tvSpeed2_0;
        }
        if (target != null) {
            target.setTextColor(getResources().getColor(R.color.c7));
            target.postDelayed(target::requestFocus, 100);
        }
    }

    public static void show(float currentSpeed, ViewGroup container, OnSpeedViewListener onSpeedViewListener) {
        SpeedSelectView speedSelectView = new SpeedSelectView(currentSpeed, container.getContext());
        speedSelectView.mOnSpeedViewListener = onSpeedViewListener;
        speedSelectView.mContainer = container;
        container.addView(speedSelectView);
    }

    public static void hide(ViewGroup container) {
        int count = container.getChildCount();
        for (int i = 0; i < count; i++) {
            View view = container.getChildAt(i);
            if (view instanceof SpeedSelectView) {
                container.removeView(view);
                break;
            }
        }

        if (mOnSpeedViewListener != null) {
            mOnSpeedViewListener.onSpeedViewHide();
        }
    }


    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.root_speed_select:
                hide(mContainer);
                break;
            case R.id.tv_speed_0_5:
                mCurrentSpeed = Speed.SPEED_0_5;
                selectColor();
                if (mOnSpeedViewListener != null) {
                    mOnSpeedViewListener.onSpeedSelect(mCurrentSpeed);
                }
                hide(mContainer);
                break;
            case R.id.tv_speed_0_8:
                mCurrentSpeed = Speed.SPEED_0_8;
                selectColor();
                if (mOnSpeedViewListener != null) {
                    mOnSpeedViewListener.onSpeedSelect(mCurrentSpeed);
                }
                hide(mContainer);
                break;
            case R.id.tv_speed_1_0:
                mCurrentSpeed = Speed.SPEED_1_0;
                selectColor();
                if (mOnSpeedViewListener != null) {
                    mOnSpeedViewListener.onSpeedSelect(mCurrentSpeed);
                }
                hide(mContainer);
                break;
            case R.id.tv_speed_1_25:
                mCurrentSpeed = Speed.SPEED_1_25;
                selectColor();
                if (mOnSpeedViewListener != null) {
                    mOnSpeedViewListener.onSpeedSelect(mCurrentSpeed);
                }
                hide(mContainer);
                break;
            case R.id.tv_speed_1_5:
                mCurrentSpeed = Speed.SPEED_1_5;
                selectColor();
                if (mOnSpeedViewListener != null) {
                    mOnSpeedViewListener.onSpeedSelect(mCurrentSpeed);
                }
                hide(mContainer);
                break;
            case R.id.tv_speed_2_0:
                mCurrentSpeed = Speed.SPEED_2_0;
                selectColor();
                if (mOnSpeedViewListener != null) {
                    mOnSpeedViewListener.onSpeedSelect(mCurrentSpeed);
                }
                hide(mContainer);
                break;
        }
    }

    public interface OnSpeedViewListener {
        void onSpeedSelect(float speed);

        void onSpeedViewHide();
    }

    public static final class Speed {
        public static final float SPEED_0_5 = 0.5f;
        public static final float SPEED_0_8 = 0.8f;
        public static final float SPEED_1_0 = 1.0f;
        public static final float SPEED_1_25 = 1.25f;
        public static final float SPEED_1_5 = 1.5f;
        public static final float SPEED_2_0 = 2.0f;
    }
}
