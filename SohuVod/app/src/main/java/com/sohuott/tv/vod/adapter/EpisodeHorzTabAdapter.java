package com.sohuott.tv.vod.adapter;

import android.os.Bundle;
import android.os.Parcelable;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;
import com.sohuott.tv.vod.fragment.EpisodeBaseFragmentNew;
import com.sohuott.tv.vod.fragment.EpisodeBigFragment;
import com.sohuott.tv.vod.fragment.EpisodeSmlFragment;
import com.sohuott.tv.vod.fragment.EpisodeTrailerFragment;
import com.sohuott.tv.vod.fragment.EpisodeVrsFragmentNew;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AlbumInfo;
import com.sohuott.tv.vod.lib.model.AlbumInfoRecommendModel;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.model.PgcEpisodeVideos;
import com.sohuott.tv.vod.lib.model.VrsEpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.lib.widgets.ViewPager;
import com.sohuott.tv.vod.ui.EpisodeLayoutNew;
import com.sohuott.tv.vod.utils.SimpleDisposableObsever;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.widget.EpisodeHorzTabView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableObserver;

public class EpisodeHorzTabAdapter extends FragmentStatePagerAdapter
        implements TabPagerAdapter, androidx.viewpager.widget.ViewPager.OnPageChangeListener {

    private final boolean mIsInitFromPlayer;
    private final EpisodeTrailerFragment.LoadTrailerDataCallback mLoadTrailerDataCallback;
    private final String mParterNo;
    private int PAGE_COUNT = 10;

    private EpisodeHorzTabView mTabView;
    private int mTotalCount;
    private int mIndex;
    private int mAid;
    private int mVid;
    private int mSortOrder;
    private int mDataType;
    private int mType;
    private int mLayoutType;
    private int mCateCode;

    private int mPrePagerPosition = 0;

    private Map<Integer, WeakReference<EpisodeBaseFragmentNew>> mFragments;
    private FocusBorderView mFocusBorderView;
    private TextView mEpisodePoints;
    private int mVideoOrder;

    private boolean mInTouchMode;
    private boolean mEpisodeSelected = false;

    private HashMap<Integer, List<EpisodeVideos.Video>> mVideos;
    private HashMap<Integer, Integer> mRequestPosMap;
    CompositeDisposable compositeDisposable;
    private boolean mIsChildEpisode = false;
    private boolean mIstrailerTab;
    private List<AlbumInfoRecommendModel> mAlbumRecommendLists;

    //是否是全屏播控选集
    private boolean isMenu = false;

    public EpisodeHorzTabAdapter(FragmentManager fm, EpisodeHorzTabView tabView, int aid,
                                 int vid, int totalCount, int sortOrder, int layoutType,
                                 int index, int dataType, int type, int cateCode, int videoOrder,
                                 int currentIndex, String parterNo, boolean isInitFromPlayer,
                                 EpisodeTrailerFragment.LoadTrailerDataCallback callback, boolean isMenu) {
        super(fm);
        mTabView = tabView;
        mAid = aid;
        mVid = vid;
        mTotalCount = totalCount;
        mSortOrder = sortOrder;
        mIndex = index;
        mDataType = dataType;
        mType = type;
        mLayoutType = layoutType;
        mCateCode = cateCode;
        mVideoOrder = videoOrder;
        mPrePagerPosition = currentIndex;
        mParterNo = parterNo;
        mIsInitFromPlayer = isInitFromPlayer;
        mLoadTrailerDataCallback = callback;
        this.isMenu = isMenu;
        mFragments = new HashMap<>();
        mInTouchMode = Util.isSupportTouchVersion(tabView.getContext());
        mVideos = new HashMap<>();
        mRequestPosMap = new HashMap<>();
        compositeDisposable = new CompositeDisposable();
    }

    public void unsubscribe() {
        compositeDisposable.clear();
    }

    //Activity onDestroy时释放所有资源，fragment资源
    public void release() {
        mTabView = null;
        mFocusBorderView = null;
        mEpisodePoints = null;
        mFragments.clear();
        mFragments = null;
        mVideos.clear();
        mVideos = null;
        mRequestPosMap.clear();
        mRequestPosMap = null;

    }


    @Override
    public Fragment getItem(int position) {
        WeakReference<EpisodeBaseFragmentNew> fragmentWeakReference = mFragments.get(new Integer(position));
        EpisodeBaseFragmentNew fragment = null;
        if (fragmentWeakReference != null) {
            fragment = fragmentWeakReference.get();
        }
        LibDeprecatedLogger.d("fragments size = " + mFragments.size() + ", fragment = " + fragment);
        if (fragment == null) {
            switch (mLayoutType) {
                case 1:
                case 3:
                    fragment = new EpisodeSmlFragment();
                    break;
                case 2:
                    fragment = new EpisodeBigFragment();
                    break;
                case 0:
                default:
                    fragment = new EpisodeVrsFragmentNew();
                    break;

            }
            fragment.setTabView(mTabView);
            fragment.setFocusBorderView(mFocusBorderView);
            fragment.setEpisodePoints(mEpisodePoints);
            fragment.setIsInitFromPlayer(mIsInitFromPlayer);
            Bundle bundle = new Bundle();
            bundle.putInt("totalCount", mTotalCount);
            bundle.putInt("start", getStart(position));
            bundle.putInt("end", getEnd(position));
            bundle.putInt("sortOrder", mSortOrder);
            bundle.putInt("aid", mAid);
            bundle.putInt("vid", mVid);
            bundle.putInt("dataType", mDataType);
            bundle.putInt("type", mType);
            bundle.putInt("cateCode", mCateCode);
            bundle.putInt("videoOrder", mVideoOrder);
            bundle.putBoolean("episodeSelected", mEpisodeSelected);
            bundle.putBoolean("isMenu", isMenu);
            bundle.putBoolean("trailerTab", mIstrailerTab);


            //倒序、青少年模式不增加推荐视频
            if (mAlbumRecommendLists != null && mSortOrder != 0 && !TeenagersManger.isTeenager()) {

                for (int i = 0; i < mAlbumRecommendLists.size(); i++) {
                    mAlbumRecommendLists.get(i).setIndex(i);
                    mAlbumRecommendLists.get(i).setSize(mAlbumRecommendLists.size());
                }

                if (mLayoutType != 0) {
                    bundle.putSerializable("recommends", new ArrayList<>(mAlbumRecommendLists));
                }
                //手动分页推荐视频列表
                int spiltNum = (mIndex - mTotalCount % mIndex) / 2;
                if (spiltNum < mAlbumRecommendLists.size() && getCount() > 1) {
                    if (getCount() - 1 == position) {
                        //最后一页
                        bundle.putSerializable("recommends", new ArrayList<>(mAlbumRecommendLists.subList(spiltNum, mAlbumRecommendLists.size())));
                    } else if (getCount() - 2 == position) {
                        //倒数第二页
                        bundle.putSerializable("recommends", new ArrayList<>(mAlbumRecommendLists.subList(0, spiltNum)));
                    }
                } else if (spiltNum >= mAlbumRecommendLists.size()) {
                    //不需要分页
                    if (getCount() - 1 == position)
                        bundle.putSerializable("recommends", new ArrayList<>(mAlbumRecommendLists));
                }
            }

            if (mLayoutType == 0) {
                if (getData(position) != null) {
                    bundle.putSerializable("vidoes", getData(position));
                }
            }
            fragment.setArguments(bundle);
            mFragments.put(position, new WeakReference<>(fragment));
        }
        return fragment;
    }

    public Fragment getItemIfExist(int position) {
        WeakReference<EpisodeBaseFragmentNew> fragmentWeakReference = mFragments.get(new Integer(position));
        if (fragmentWeakReference != null) {
            return fragmentWeakReference.get();
        }
        return null;
    }

    @Override
    public int getCount() {
        switch (mLayoutType) {
            case 1:
            case 2:
            case 3:
                return 1;
        }
        if (mTotalCount == 0 || mIndex == 0) {
            return 0;
        }
        int albumRecommendListSize = 0;
        if (mAlbumRecommendLists != null && !TeenagersManger.isTeenager()) {
            albumRecommendListSize = mAlbumRecommendLists.size();
        }
        return (mTotalCount + albumRecommendListSize * 2) / mIndex + ((mTotalCount + albumRecommendListSize * 2) % mIndex == 0 ? 0 : 1);
    }

    @Override
    public CharSequence getPageTitle(int pos) {
        return getName(pos);
    }

    @Override
    public String getText(int pos) {
        return getName(pos);
    }

    private String getName(int pos) {
        int start = getStart(pos);
        int end = getEnd(pos);
        StringBuilder sb = new StringBuilder();
        if (mCateCode == Constant.CATECODE_MOVIE && start == 1) {
            return "完整版";
        }
        if (end < start) {
            return "推荐";
        }
        if (mSortOrder == EpisodeLayoutNew.DESC_SORT_ORDER) {
            return sb.append(end)
                    .append("-").append(start).toString();
        } else {
            return sb.append(start)
                    .append("-").append(end).toString();
        }
    }

    @Override
    public void setTabChange(boolean isTabChange) {
    }

    @Override
    public void onPageSelected(int position) {
        AppLogger.INSTANCE.e("onPageSelected, position = " + position);
        WeakReference<EpisodeBaseFragmentNew> fragmentWeakReference = mFragments.get(new Integer(position));
        Fragment fragment = null;
        if (fragmentWeakReference != null) {
            fragment = fragmentWeakReference.get();
        }
        if (fragment != null && fragment instanceof EpisodeBaseFragmentNew) {
            if (position < mPrePagerPosition) {
                ((EpisodeBaseFragmentNew) fragment).focusMoveToUp();
            } else if (position > mPrePagerPosition) {
                ((EpisodeBaseFragmentNew) fragment).focusMoveToDown();
            }
            if (mInTouchMode) {
                ((EpisodeBaseFragmentNew) fragment).onPageScrollStateStop();
            }
        }
        mPrePagerPosition = position;
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageScrollStateChanged(int state) {
        if (state == ViewPager.SCROLL_STATE_IDLE) {
            WeakReference<EpisodeBaseFragmentNew> fragmentWeakReference = mFragments.get(new Integer(mPrePagerPosition));
            Fragment fragment = null;
            if (fragmentWeakReference != null) {
                fragment = fragmentWeakReference.get();
            }
            if (fragment != null && fragment instanceof EpisodeBaseFragmentNew) {
                ((EpisodeBaseFragmentNew) fragment).onPageScrollStateStop();
            }
        } else {
            if (mEpisodePoints != null) {
                mEpisodePoints.setVisibility(View.INVISIBLE);
            }
        }
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        LibDeprecatedLogger.d("destroyItem , pos = " + position);
        super.destroyItem(container, position, object);
        mFragments.remove(position);
    }

    @Override
    public Parcelable saveState() {
        return null;
    }

    @Override
    public void restoreState(Parcelable state, ClassLoader loader) {

    }

    private int getStart(int pos) {
        int start;
        if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
            start = pos * mIndex + 1;
        } else {
            start = Math.max(1, mTotalCount - (pos + 1) * mIndex + 1);
        }
        return start;
    }

    private int getEnd(int pos) {
        int end;
//        int recommendCount = mAlbumRecommendLists == null ? 0 : mAlbumRecommendLists.size();
        if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
            end = Math.min((pos + 1) * mIndex, mTotalCount);
        } else {
            end = mTotalCount - pos * mIndex;
        }
        return end;
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        if (mFocusBorderView == null) {
            mFocusBorderView = new FocusBorderView(mTabView.getContext());
        }
    }

    public void setEpisodePoints(TextView points) {
        mEpisodePoints = points;
    }

    public void setVideoOrder(int videoOrder) {
        mVideoOrder = videoOrder;
    }

    public void setEpisodeSelected(boolean value) {
        mEpisodeSelected = value;
    }

    public void setIsChildEpisode(boolean childEpisode) {
        mIsChildEpisode = childEpisode;
    }

    public void setIsTrailerTab(boolean isTrailerTab) {
        this.mIstrailerTab = isTrailerTab;
    }

    public void setmAlbumRecommendLists(List<AlbumInfoRecommendModel> albumRecommendLists) {
        this.mAlbumRecommendLists = albumRecommendLists;
    }

    private ArrayList<EpisodeVideos.Video> getData(int pos) {
        if (mDataType != Constant.DATA_TYPE_VRS) {
            return getPgcData(pos);
        } else {
            return getVrsData(pos);
        }
    }

    private ArrayList<EpisodeVideos.Video> getVrsData(int pos) {
        LibDeprecatedLogger.d("isMenu : " + isMenu);
        int pageSize = getPageSize();
        int index = pos / PAGE_COUNT + 1;
        //已经做过数据请求,直接返回
        if (mRequestPosMap.get(index) != null) {
            List vidoes = getFragmentData(pos);
            if (vidoes == null) {
                return null;
            }
            return new ArrayList(vidoes);
        }

        //没有请求过对应数据，开始请求
        mRequestPosMap.put(index, 1);

        DisposableObserver<VrsEpisodeVideos> episodeDisposableObserver =
                new SimpleDisposableObsever<VrsEpisodeVideos>(AlbumInfo.class.getSimpleName()) {
                    @Override
                    public void onNext(VrsEpisodeVideos response) {
                        String responseData = response == null ? "null" : String.valueOf(response.status);
                        LibDeprecatedLogger.d("Get VRS episode data response: " + responseData);
                        if (response != null && response.status == 0) {
                            EpisodeVideos episodeVideos = VrsEpisodeVideos.vrsConvert2Videos(response.data);
                            if (episodeVideos != null && episodeVideos.videos != null) {
                                LibDeprecatedLogger.d("Get VRS episode data: " + episodeVideos.toString());
                                afterGetData(episodeVideos.page, episodeVideos.videos);
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.w("Get VRS episode data error!", e);
                    }

                    @Override
                    public void onComplete() {
                        //AppLogger.d("Get VRS episode data complete!");
                    }
                };
        NetworkApi.getVrsEpisdoeVideosData(mAid, 0, mSortOrder,
                mParterNo, index, pageSize * PAGE_COUNT, episodeDisposableObserver);
        compositeDisposable.add(episodeDisposableObserver);
        return null;
    }

    private ArrayList<EpisodeVideos.Video> getPgcData(int pos) {
        int pageSize = getPageSize();
        int index = pos / PAGE_COUNT + 1;
        //已经做过数据请求,直接返回
        if (mRequestPosMap.get(index) != null) {
            List vidoes = getFragmentData(pos);
            if (vidoes == null) {
                return null;
            }
            return new ArrayList(vidoes);
        }

        //没有请求过对应数据，开始请求
        mRequestPosMap.put(index, 1);
        DisposableObserver<PgcEpisodeVideos> episodeDisposableObserver =
                new SimpleDisposableObsever<PgcEpisodeVideos>(AlbumInfo.class.getSimpleName()) {
                    @Override
                    public void onNext(PgcEpisodeVideos response) {
                        String responseData = response == null ? "null" : String.valueOf(response.status);
                        LibDeprecatedLogger.d("Get PGC episode data response: " + responseData);
                        if (response != null && response.status == 0) {
                            EpisodeVideos episodeVideos = PgcEpisodeVideos.pgcConvert2Videos(response.data);
                            if (episodeVideos != null && episodeVideos.videos != null) {
                                LibDeprecatedLogger.d("Get PGC episode data: " + episodeVideos.toString());
                                afterGetData(episodeVideos.page, episodeVideos.videos);
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LibDeprecatedLogger.w("Get VRS episode data error!", e);
                    }

                    @Override
                    public void onComplete() {
                        //AppLogger.d("Get VRS episode data complete!");
                    }
                };
        NetworkApi.getPgcEpisdoeVideosData(mAid, index, pageSize * PAGE_COUNT, mSortOrder, episodeDisposableObserver);
        compositeDisposable.add(episodeDisposableObserver);
        return null;
    }

    private void afterGetData(int page, List<EpisodeVideos.Video> episodeVideos) {
        LibDeprecatedLogger.d("isMenu : " + isMenu);
        mVideos.put(page, episodeVideos);
        //查看是否需要更新当前显示页面&缓存页面
        WeakReference<EpisodeBaseFragmentNew> fragmentWeakReference = mFragments.get(new Integer(mPrePagerPosition));
        if (fragmentWeakReference != null && fragmentWeakReference.get() != null) {
            fragmentWeakReference.get().setUI(getFragmentData(mPrePagerPosition));
        }
        fragmentWeakReference = mFragments.get(new Integer(mPrePagerPosition - 1));
        if (fragmentWeakReference != null && fragmentWeakReference.get() != null) {
            fragmentWeakReference.get().setUI(getFragmentData(mPrePagerPosition - 1));
        }
        fragmentWeakReference = mFragments.get(new Integer(mPrePagerPosition + 1));
        if (fragmentWeakReference != null && fragmentWeakReference.get() != null) {
            fragmentWeakReference.get().setUI(getFragmentData(mPrePagerPosition + 1));
        }
        if (mLoadTrailerDataCallback != null && page == 1 && episodeVideos.size() > 0) {
            mLoadTrailerDataCallback.onDataLoaded(episodeVideos.get(0).tvVerId);
        }
    }

    private int getPageSize() {

//        if(mIsChildEpisode){
//            return EpisodeLayoutNew.CHILD_VRS_PAGE_SIZE;
//        }
        switch (mLayoutType) {
            case 1:
                return EpisodeLayoutNew.VRS_CHAIN_PAGE_SIZE;
            case 2:
                return EpisodeLayoutNew.CHILD_VRS_PAGE_SIZE;
            case 3:
                return EpisodeLayoutNew.PGC_PAGE_SIZE;
            case 0:
            default:
                return EpisodeLayoutNew.VRS_PAGE_SIZE;
        }
    }

    private List<EpisodeVideos.Video> getFragmentData(int pos) {
        int pageSize = getPageSize();
        int index = pos / PAGE_COUNT + 1;
        //已经做过数据请求
        if (mRequestPosMap.get(index) != 0) {
            //数据已经返回
            if (mVideos.get(index) != null) {
                int toIndex = pos % PAGE_COUNT * pageSize + pageSize;
                toIndex = mVideos.get(index).size() > toIndex ? toIndex : mVideos.get(index).size();
                int fromIndex = pos % PAGE_COUNT * pageSize;
                // 这个判断必加，为了有集数避免因为审核未通过造成的问题
                if (fromIndex < toIndex || (fromIndex == toIndex && mAlbumRecommendLists.size() > 0)) {
                    return mVideos.get(index).subList(fromIndex, toIndex);
                } else {
                    return null;
                }
            } else {
                //数据没有返回，等待数据请求
                return null;
            }
        }
        return null;
    }

    @Override
    public void finishUpdate(ViewGroup container) {
        try {
            super.finishUpdate(container);
        } catch (NullPointerException nullPointerException) {
            Log.d("EpisodeHorzTabAdapter", "Catch the NullPointerException in FragmentPagerAdapter.finishUpdate");
        }
    }
}