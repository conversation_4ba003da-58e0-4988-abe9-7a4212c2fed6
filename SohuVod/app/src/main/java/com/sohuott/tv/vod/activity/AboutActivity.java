package com.sohuott.tv.vod.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.BuildConfig;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.setting.ShowPrivacyWebViewActivity;
import com.sohuott.tv.vod.app.App;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.AboutInfo;
import com.sohuott.tv.vod.lib.model.UpdateInfo;
import com.sohuott.tv.vod.lib.utils.ToastUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.UpdatePresenter;
import com.sohuott.tv.vod.presenter.UpdatePresenterImpl;
import com.sohuott.tv.vod.utils.UpdateHelper;
import com.sohuott.tv.vod.view.AboutView;
import com.sohuott.tv.vod.view.AgreementView;

import java.util.HashMap;

/**
 * Created by fenglei on 16-3-1.
 */
public class AboutActivity extends BaseActivity implements AboutView, View.OnClickListener {

    private TextView telTV, qqTV, wechatTV, descTV, gidTV, mCurrentVerion;

    private ViewGroup mUpdateLayout, mAgreementLayout, mPrivacyLayout, mChildPrivacyLayout,

            mCollectInfoLayout, mThirdInfoLayout, mThirdDirLayout,
            mRequestPermissionLayout, mPrivacySohuVideoLayout;

    //private GlideImageView qrcodeIV;

    private UpdatePresenter mUpdatePresenter;

    private UpdateHelper mUpdateHelper;

    private boolean isCheckingUpdate = false;

    private AgreementView mAgreementView;
    private HashMap<String, String> mPath;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_my);

        mUpdatePresenter = new UpdatePresenterImpl(this, this);
        mUpdateHelper = new UpdateHelper(this, mUpdatePresenter);

        initUI();
        mUpdatePresenter.getAboutInfo();
        //mUpdatePresenter.getQrcodeInfo();
        RequestManager.getInstance().onEvent("6_about", "100001", null, null, null, null, null);
        setPageName("6_about");
        mPath = new HashMap<>(1);
        mPath.put("pageId", "1039");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), mPath, null, null);
    }

    private void initUI() {
        mUpdateLayout = findViewById(R.id.update_layout);
        mAgreementLayout = findViewById(R.id.user_agreement_layout);
        mPrivacyLayout = findViewById(R.id.privacy_layout);
        mChildPrivacyLayout = findViewById(R.id.child_privacy_layout);
        mCollectInfoLayout = findViewById(R.id.collect_info_layout);
        mThirdInfoLayout = findViewById(R.id.third_info_layout);
        mThirdDirLayout = findViewById(R.id.third_dir_layout);
        mRequestPermissionLayout = findViewById(R.id.request_permissions_layout);
        mPrivacySohuVideoLayout = findViewById(R.id.privacy_sohuvideo_layout);
        mCurrentVerion = findViewById(R.id.current_version);
        gidTV = (TextView) findViewById(R.id.tv_gid);
//        snmAccount = (TextView) findViewById(R.id.tv_snm);
        qqTV = (TextView) findViewById(R.id.tv_qq);
        telTV = (TextView) findViewById(R.id.tv_tel);
        wechatTV = (TextView) findViewById(R.id.tv_wechat);
        descTV = (TextView) findViewById(R.id.tv_desc);
//        versionTV = (TextView) findViewById(R.id.tv_version);
        //qrcodeIV = (GlideImageView) findViewById(R.id.iv_qrcode);
        String version = (Util.getHttpsParams(this) == 1 ? "v" : "V") + Util.getVersionName(this);
        if (App.getDebug()) {
            version = version + "-debug";
        }
        mCurrentVerion.setText("当前版本" + version);
        mUpdateLayout.requestFocus();
        mUpdateLayout.setOnClickListener(this);
        mAgreementLayout.setOnClickListener(this);
        mPrivacyLayout.setOnClickListener(this);
        mChildPrivacyLayout.setOnClickListener(this);
        mCollectInfoLayout.setOnClickListener(this);
        mThirdInfoLayout.setOnClickListener(this);
        mThirdDirLayout.setOnClickListener(this);
        mRequestPermissionLayout.setOnClickListener(this);
        mPrivacySohuVideoLayout.setOnClickListener(this);
        mAgreementView = new AgreementView(this, null);
        processTCLShow(version);
    }

    /**
     * TCL渠道，去掉升级功能
     *
     * @param version 当前版本号
     */
    private void processTCLShow(String version) {
        if (BuildConfig.FLAVOR.equals("nctcl")) {
            ((TextView) findViewById(R.id.check_update)).setText("");
            mUpdateLayout.setOnClickListener(null);
            mAgreementLayout.requestFocus();
        }
    }

    @Override
    public void updateDownloadProgress(int value) {
        mUpdateHelper.updateProgress(value);
    }

    @Override
    public void responseDownloadResult(boolean result, int updateStatus, int networkResponseCode) {
        mUpdateHelper.dealDownloadResult(this, result, updateStatus, networkResponseCode);
    }

    @Override
    public void addUpdateEvent(final UpdateInfo updateInfo) {
        if (updateInfo == null) {
            isCheckingUpdate = false;
            ToastUtils.showToast2(this, "检测升级信息失败，请重试！");
            return;
        }
        mUpdateHelper.showUpdateDialog(this, updateInfo, true);
        isCheckingUpdate = false;
    }

    @Override
    public void initAboutUI(AboutInfo aboutInfo) {
        LibDeprecatedLogger.d("gid = " + UserInfoHelper.getGid());
        gidTV.setText("GID: " + UserInfoHelper.getGid());
        if (aboutInfo == null || aboutInfo.status != 0 || aboutInfo.data == null) {
            return;
        }
        AboutInfo.Data data = aboutInfo.data;
        if (data != null) {
            setText(telTV, "客服电话: ", data.telephone);
            setText(qqTV, "QQ群: ", data.qq);
            setText(wechatTV, "微信: ", data.weixin);
            if (Util.getPlayParams(this) == 0) {
                descTV.setText(data.version_desc);
            } else {
                descTV.setText(data.version_desc + ".");
            }
            if (!data.user_agreement_btn) {
                mAgreementLayout.setVisibility(View.GONE);
            }
            if (!data.privacy_btn) {
                mPrivacyLayout.setVisibility(View.GONE);
            }
            if (!data.collect_info_btn) {
                mCollectInfoLayout.setVisibility(View.GONE);
            }
            if (!data.third_info_btn) {
                mThirdInfoLayout.setVisibility(View.GONE);
            }
            if (!data.third_dir_btn) {
                mThirdDirLayout.setVisibility(View.GONE);
            }
            if (!data.sohuvideo_privacy) {
                mPrivacySohuVideoLayout.setVisibility(View.GONE);
            }
            if (!data.request_permission_btn) {
                mRequestPermissionLayout.setVisibility(View.GONE);
            }
            if (!data.child_privacy_btn) {
                mChildPrivacyLayout.setVisibility(View.GONE);
            }
        }
    }

    private void setText(TextView tv, String prefix, String content) {
        if (!TextUtils.isEmpty(content)) {
            tv.setText(prefix + content);
            tv.setVisibility(View.VISIBLE);
        } else {
            tv.setVisibility(View.GONE);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.update_layout:
                if (isCheckingUpdate) {
                    ToastUtils.showToast2(AboutActivity.this, "正在检测...");
                    return;
                } else {
                    if (mUpdateHelper.getUpdateDialog() != null && mUpdateHelper.getUpdateDialog().isShowing()) {
                        return;
                    }
                }
                isCheckingUpdate = true;
                mUpdatePresenter.getUpdateInfo();
                RequestManager.getInstance().onAllEvent(new EventInfo(10254, "clk"), mPath, null, null);
                break;
            case R.id.user_agreement_layout:
                //show user agreement
//                mAgreementView.show(mAgreementLayout, AgreementPresenter.NAME_USER);
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.USER_AGREEMENT);
                RequestManager.getInstance().onAllEvent(new EventInfo(10255, "clk"), mPath, null, null);
                break;
            case R.id.privacy_layout:
                //show private agreement
//                mAgreementView.show(mPrivacyLayout, AgreementPresenter.NAME_PRIVACY);
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.PRIVATE_AGREEMENT);
                RequestManager.getInstance().onAllEvent(new EventInfo(10256, "clk"), mPath, null, null);
                break;
            case R.id.child_privacy_layout:
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.CHILD_PRIVATE_AGREEMENT);
                RequestManager.getInstance().onAllEvent(new EventInfo(10323, "clk"), mPath, null, null);

                break;
            case R.id.collect_info_layout:
                //show private agreement
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.COLLECT);
                RequestManager.getInstance().onAllEvent(new EventInfo(10257, "clk"), mPath, null, null);
                break;
            case R.id.third_info_layout:
                //show private agreement
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.SHARE);
                RequestManager.getInstance().onAllEvent(new EventInfo(10258, "clk"), mPath, null, null);
                break;
            case R.id.third_dir_layout:
                //show private agreement
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.SDK);
                RequestManager.getInstance().onAllEvent(new EventInfo(10265, "clk"), mPath, null, null);
                break;
            case R.id.privacy_sohuvideo_layout:
                RequestManager.getInstance().onAllEvent(new EventInfo(10266, "clk"), mPath, null, null);
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.SOHU_PRIVATE_AGREEMENT);
                break;
            case R.id.request_permissions_layout:
                RequestManager.getInstance().onAllEvent(new EventInfo(10259, "clk"), mPath, null, null);
                ShowPrivacyWebViewActivity.Companion.actionStart(this, ShowPrivacyWebViewActivity.PERMISSION);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mAgreementView != null) {
            mAgreementView.release();
        }
    }
}
