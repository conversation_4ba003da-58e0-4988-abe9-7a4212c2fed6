package com.sohuott.tv.vod.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.model.LocationConfigInfo;
import com.sohuott.tv.vod.lib.utils.StringUtils;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;

/**
 * Created by XiyingCao on 16-3-14.
 */
public class ActorListAdapter extends RecyclerView.Adapter<ActorListAdapter.ActorViewHolder> {

    private static final String TAG = ActorListAdapter.class.getSimpleName();
    private RecyclerView mParentRecyclerView;
    private List<VideoDetailRecommendModel.DataBean.ContentsBean> mActors;
    private LocationConfigInfo.DataBean mConfigInfo;
    private Context mContext;
    private boolean hasFirstFocus = false;

    public ActorListAdapter(List<VideoDetailRecommendModel.DataBean.ContentsBean> actors,
                            RecyclerView recyclerView) {
        AppLogger.d(TAG, "ActorListAdapter()");
        mContext = recyclerView.getContext();
        mActors = actors;
        mParentRecyclerView = new WeakReference<>(recyclerView).get();
        mConfigInfo = HomeData.getLocationConfigInfo(null);
        if (actors != null && actors.size() > 0) {
            HashMap<String, String> pathInfo = new HashMap<>(1);
            pathInfo.put("pageId", "1043");
            RequestManager.getInstance().onAllEvent(new EventInfo(10156, "imp"), pathInfo, null, null);
        }
    }

    @Override
    public ActorViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View modelView =
                LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.actor_list_item,
                        viewGroup, false);
        return new ActorViewHolder(modelView);
    }

    @Override
    public void onBindViewHolder(ActorViewHolder holder, int position) {
        ActorViewHolder viewHolder = (ActorViewHolder) holder;
        AppLogger.d(TAG, "position ? " + position);
        if (viewHolder != null) {
            viewHolder.name.setText("测试");
        }
        if (mActors == null || mActors.get(position) == null) {
            return;
        }
        VideoDetailRecommendModel.DataBean.ContentsBean actorsEntity = mActors.get(position);
        if (actorsEntity != null) {
            viewHolder.name.setText(actorsEntity.getName());
//            viewHolder.avatar.setCircleImageRes(actorsEntity.getHorPic());
            String urlPath = get252ImageUrl(actorsEntity);
            AppLogger.d(TAG, "position ? " + position);
            AppLogger.d(TAG, "actorsEntity.getName() ? " + actorsEntity.getName());
            AppLogger.d(TAG, "urlPath ? " + urlPath);
//            viewHolder.avatar.setCircleImageRes(urlPath);
            if (StringUtils.isNotEmptyStr(urlPath)) {
                Glide.with(mContext)
                        .load(urlPath).circleCrop().into(viewHolder.avatar);
            } else {
                Glide.with(mContext)
                        .load(mContext.getResources().getDrawable(R.drawable.detail_default_avatar)).into(viewHolder.avatar);
            }

            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ActivityLauncher.startActorListActivity(v.getContext(), actorsEntity.getId(), actorsEntity.getStarType() == 2,
                            actorsEntity.getName());
                    HashMap<String, String> pathInfo = new HashMap<>(1);
                    pathInfo.put("pageId", "1043");
                    RequestManager.getInstance().onAllEvent(new EventInfo(10133, "clk"), pathInfo, null, null);
                }
            });
            AppLogger.d(TAG, "ActorListAdapter() hasFirstFocus ? " + hasFirstFocus);
            if (!hasFirstFocus && position == 0) {
                viewHolder.itemView.requestFocus();
                hasFirstFocus = true;
            }
        }
    }

    private String get252ImageUrl(VideoDetailRecommendModel.DataBean.ContentsBean actorsEntity) {
        String resultUrl = actorsEntity.getVerPic();
        AppLogger.d(TAG, "get252ImageUrl picUrl ? " + resultUrl);
        mConfigInfo.newImgDomain = "photocdntv.vod.ystyt.aisee.tv";
        if (StringUtils.isNotEmptyStr(resultUrl)) {
//            if (picUrl.contains("//photocdn.tv.snmsohu.aisee.tv/")) {
//                resultUrl = picUrl.replace("/img/", "/img/c_lfill,w_252,h_252,g_faces/");
//            } else
            if (mConfigInfo != null && StringUtil.isNotEmpty(mConfigInfo.newImgDomain)) {
                AppLogger.d(TAG,
                        "get252ImageUrl mConfigInfo.newImgDomain ? " + mConfigInfo.newImgDomain);
                if (actorsEntity.getVerPic().contains(mConfigInfo.newImgDomain)) {
                    resultUrl = actorsEntity.getVerPic().replace("/img/", "/img/c_lfill,w_252,h_252,g_faces/");
                }
            }
        }
        AppLogger.d(TAG, "get252ImageUrl resultUrl ? " + resultUrl);
        return resultUrl;
    }

    @Override
    public int getItemCount() {
        if (mActors == null) {
            return 0;
        }
        return mActors.size();
    }

    class ActorViewHolder extends RecyclerView.ViewHolder {
        private View coverView;
        private GlideImageView avatar;
        private TextView name;

        public ActorViewHolder(final View itemView) {
            super(itemView);
            coverView = itemView.findViewById(R.id.detail_recommend_focus_cover);
            avatar = (GlideImageView) itemView.findViewById(R.id.detail_recommend_poster);
            name = (TextView) itemView.findViewById(R.id.detail_recommend_title);
            itemView.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean hasFocus) {
                    AppLogger.d(TAG, "itemView onFocusChange hasFocus " + hasFocus);
                    if (hasFocus) {
                        coverView.setVisibility(View.VISIBLE);
                        ViewCompat.animate(view).setDuration(200).scaleX(1.1f).scaleY(1.1f).start();
                        name.setTextColor(view.getResources().getColor(R.color.actor_name_text_focus_color));
                    } else {
                        coverView.setVisibility(View.GONE);
                        ViewCompat.animate(view).setDuration(200).scaleX(1.0f).scaleY(1.0f).start();
                        name.setTextColor(view.getResources().getColor(R.color.actor_name_text_color));
                    }
                }
            });
        }
    }
}

