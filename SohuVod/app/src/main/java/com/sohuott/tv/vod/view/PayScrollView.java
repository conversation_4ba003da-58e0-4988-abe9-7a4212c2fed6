package com.sohuott.tv.vod.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ScrollView;

/**
 * Created by haojiang on 2018/4/8.
 */

public class PayScrollView extends ScrollView {
    public PayScrollView(Context context) {
        super(context);
    }

    public PayScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public PayScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if(onScrollLisenner!=null){
            onScrollLisenner.onScrollChanged(l,t,oldl,oldt);

        }
    }

    public interface OnScrollLisenner {
        public void onScrollChanged(int l, int t, int oldl, int oldt);
    }

    private OnScrollLisenner onScrollLisenner;

    public void setOnScrollLisenner(OnScrollLisenner onScrollLisenner) {
        this.onScrollLisenner = onScrollLisenner;
    }
}
