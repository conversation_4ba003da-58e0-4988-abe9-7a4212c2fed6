package com.sohuott.tv.vod.ui;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.ViewGroup;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.model.WechatPublic;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.widget.GlideImageView;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by fenglei on 17-6-30.
 */

public class SearchVoiceDialog extends Dialog {

    private GlideImageView qrcodeIV;
    private int mQrImageWidth;
    private int mQrIMageHeight;

    public SearchVoiceDialog(Context context) {
        super(context, R.style.SearchVoiceDialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.search_voice_dialog);
        getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        qrcodeIV = (GlideImageView) findViewById(R.id.qrcode_iv);
        mQrImageWidth = qrcodeIV.getContext().getResources().getDimensionPixelSize(R.dimen.x528);
        mQrIMageHeight = mQrImageWidth;
    }

    private void getQrcode() {
        NetworkApi.getWechatLogin(UserInfoHelper.getGid(), Constant.TYPE_CAPTCHA_BIND,
                new DisposableObserver<WechatPublic>() {
            @Override
            public void onNext(WechatPublic response) {
                if (null != response) {
                    String data = response.getData();
                    int status = response.getStatus();
                    if (status == 200 && null != data) {
                        qrcodeIV.setImageRes(data,getContext().getResources().getDrawable(R.drawable.bg_launcher_poster),
                                getContext().getResources().getDrawable(R.drawable.bg_launcher_poster));
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("getWechatLogin() Error: " + e.getMessage(), e);
            }

            @Override
            public void onComplete() {
            }
        });
    }

    @Override
    public void show() {
        super.show();
        getQrcode();
        RequestManager.getInstance().onSearchVoiceExposureEvent();
    }

    @Override
    public void dismiss() {
        super.dismiss();
        LibDeprecatedLogger.d("dismiss");
    }
}
