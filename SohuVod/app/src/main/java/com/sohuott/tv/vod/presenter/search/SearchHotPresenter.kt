package com.sohuott.tv.vod.presenter.search

import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.leanback.widget.Presenter
import com.sohuott.tv.vod.databinding.SearchHotLayoutItemBinding
import com.sohuott.tv.vod.lib.db.greendao.SearchHistory
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.model.SearchHot
import com.sohuott.tv.vod.search.SearchAssociate
import com.sohuott.tv.vod.widget.lb.focus.FocusHighlight
import com.sohuott.tv.vod.widget.lb.focus.MyFocusHighlightHelper

class SearchHotPresenter: Presenter() {
    interface OnSearchHotItemKeyListener{
        fun onSearchItemRightKey()
        fun onSearchItemLeftKey()
    }
    private var mOnSearchHotItemKeyListener: OnSearchHotItemKeyListener? = null
    private var itemFocusHelper: MyFocusHighlightHelper.BrowseItemFocusHighlight? = null

    fun setOnSearchHotItemKeyListener(listener: OnSearchHotItemKeyListener){
        mOnSearchHotItemKeyListener = listener
    }
    override fun onCreateViewHolder(parent: ViewGroup?): ViewHolder {
        val binding = SearchHotLayoutItemBinding.inflate(LayoutInflater.from(parent?.context), parent, false)
        itemFocusHelper = itemFocusHelper ?: MyFocusHighlightHelper.BrowseItemFocusHighlight(
            FocusHighlight.ZOOM_FACTOR_XXXSMALL,
            false)
        return SearchHotViewHolder(binding)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder?, item: Any?) {
        val vh = viewHolder as SearchHotViewHolder
        vh.binding.root.setOnFocusChangeListener { v, hasFocus ->
            itemFocusHelper?.onItemFocused(v, hasFocus)
        }
        vh.binding.root.setOnKeyListener { v, keyCode, event ->
            if (event.action == KeyEvent.ACTION_DOWN  && item is SearchAssociate.Data.Suggest){
                if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    LibDeprecatedLogger.d("SearchHotPresenter onSearchItemRightKey")
                    mOnSearchHotItemKeyListener?.onSearchItemRightKey()
                } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                    LibDeprecatedLogger.d("SearchHotPresenter onSearchItemLeftKey")
                    mOnSearchHotItemKeyListener?.onSearchItemLeftKey()
                }
            }
            false
        }

        when(item){
            is SearchHot.Data -> {
                vh.binding.searchHotItemTitle.text = item.name
            }
            is SearchHistory -> {
                vh.binding.searchHotItemTitle.text = item.albumTitle
            }
            is SearchAssociate.Data.Suggest -> {
                //为titleview设置paddingleft50
                val params = vh.binding.searchHotItemTitle.layoutParams as ViewGroup.MarginLayoutParams
                params.leftMargin = 50
                vh.binding.searchHotItemTitle.layoutParams = params
                vh.binding.searchHotItemTitle.text = item.keyword
            }
        }
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder?) {

    }

    class SearchHotViewHolder(val binding: SearchHotLayoutItemBinding): ViewHolder(binding.root)
}