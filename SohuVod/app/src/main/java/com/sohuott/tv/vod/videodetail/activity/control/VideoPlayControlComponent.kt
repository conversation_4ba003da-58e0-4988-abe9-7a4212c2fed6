package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.text.TextUtils
import android.view.KeyEvent
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.DiffCallback
import androidx.leanback.widget.HorizontalGridView
import androidx.leanback.widget.ItemBridgeAdapter
import com.lib_dlna_core.SohuDlnaManger.Companion.getInstance
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.sh.ott.video.ad.AdTsManger
import com.sh.ott.video.player.PlayerConstants
import com.sohu.ott.base.lib_user.UserConfigHelper
import com.sohu.ott.base.lib_user.UserConstants
import com.sohu.ott.lib_widget.SoHuVideoProgressBar
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.videodetail.activity.playerIsNotStart
import com.sohuott.tv.vod.videodetail.activity.playerIsPlaying
import com.sohuott.tv.vod.view.scalemenu.ScaleScreenViewMenuNewView
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleMenuItem
import com.sohuott.tv.vod.view.scalemenu.presenter.ScaleMenuViewPresenter
import java.util.Calendar
import java.util.Date
import java.util.Formatter
import java.util.Locale

/**
 * 播放控制
 */

class VideoPlayControlComponent constructor(
    context: Context,
    private val initBean: VideoPlayMenuBean?
) :
    BaseVideoProgressComponent(context) {

    var mIControlComponentHide: IControlComponentHide? = null

    private var canHide = true

    private var mDeviceTime: TextView? = null
    private var mTitle: TextView? = null
    private var mPlayerTime: TextView? = null
    private var mPlayerTimeEnd: TextView? = null
    private var mRecordNumberTv: TextView? = null
    private var realProgress: SoHuVideoProgressBar? = null

    private var mFormatBuilder: StringBuilder? = null
    private var mFormatter: Formatter? = null

    private var mArrayAdapter: ArrayObjectAdapter? = null
    private var mAdapter: ItemBridgeAdapter? = null
    private var mMenuItemData = mutableListOf<ScaleMenuItem>()
    private var mPlayControlMenu: HorizontalGridView? = null
    private var playerControllerImage: ImageView? = null
    private var selectItemName = UserConstants.MENU_ITEM_EPISODE
    var mVideoPlayMenuBean: ScaleContentOnlySeeMenuItem? = null


    private var canUpdateProgressBar: Boolean = true
    private var currentPlayState: Int? = null

    private var onlySeeModel: Boolean = false

    //播放器状态icon,如此定义有益于兼容少儿版本
    private val PLAYER_STATE_STOP = R.mipmap.ic_player_stop
    private val PLAYER_STATE_PLAY = R.mipmap.ic_player_play
    var haveOnlySee: Boolean = false

    //南传备案号展示
    var recordNumber: String? = null //南传备案号


    /**
     * 只看他模式下 是否是自动seek的
     */

    private var upAppearAnimation: Animation
    private var upDisappearAnimation: Animation
    private var downDisappearAnimation: Animation
    private var downAppearAnimation: Animation

    private var mPlayControlMenuTop: ConstraintLayout
    private var mPlayControlMenuBottom: ConstraintLayout

    init {
        isCheckStopTry = true
        layoutInflater.inflate(R.layout.video_component_play_control, this, true)
        mDeviceTime = findViewById(R.id.timeTV)
        mPlayerTime = findViewById(R.id.player_time)
        mRecordNumberTv = findViewById(R.id.recordNumber)
        mPlayerTimeEnd = findViewById(R.id.player_time_end)
        realProgress = findViewById(R.id.realProgress)
        mTitle = findViewById(R.id.tv_controller_title)
        mPlayControlMenu = findViewById(R.id.player_controller_menu_view)
        mPlayControlMenuTop = findViewById(R.id.player_controller_menu_top)
        mPlayControlMenuBottom = findViewById(R.id.player_controller_menu_bottom)
        playerControllerImage = findViewById(R.id.player_controller)
        mFormatBuilder = java.lang.StringBuilder()
        mFormatter = Formatter(mFormatBuilder, Locale.getDefault())
        gone()
        val mScaleMenuViewPresenter = ScaleMenuViewPresenter(context = context)
        mArrayAdapter = ArrayObjectAdapter(mScaleMenuViewPresenter)
        mAdapter = ItemBridgeAdapter(mArrayAdapter)
        mPlayControlMenu?.adapter = mAdapter
        upAppearAnimation = AnimationUtils.loadAnimation(
            getContext().applicationContext,
            R.anim.up_appear
        )
        downDisappearAnimation = AnimationUtils.loadAnimation(
            getContext().applicationContext,
            R.anim.down_disappear
        )
        downAppearAnimation = AnimationUtils.loadAnimation(
            getContext().applicationContext,
            R.anim.down_appear
        )
        upDisappearAnimation = AnimationUtils.loadAnimation(
            getContext().applicationContext,
            R.anim.up_disappear
        )
    }

    fun initTabData(hasOnlySee: Boolean, initBean: ScaleContentOnlySeeMenuItem?) {
        mVideoPlayMenuBean = initBean
        mMenuItemData.clear()
        mMenuItemData.add(
            ScaleMenuItem(
                ScaleScreenViewMenuNewView.MENU_ITEM_EPISODE,
                null,
                TextUtils.equals(ScaleScreenViewMenuNewView.MENU_ITEM_EPISODE, selectItemName)
            )
        )
        mMenuItemData.add(
            ScaleMenuItem(
                ScaleScreenViewMenuNewView.MENU_ITEM_CLARITY,
                null,
                TextUtils.equals(ScaleScreenViewMenuNewView.MENU_ITEM_CLARITY, selectItemName)
            )
        )
        //当 系统播放器时不展示
        if (PlaySettingHelper.getPlaySpeedIsOpen() && !UserConfigHelper.enableSystemPlayer()) {
            mMenuItemData.add(
                ScaleMenuItem(
                    ScaleScreenViewMenuNewView.MENU_ITEM_SPEED,
                    null,
                    TextUtils.equals(ScaleScreenViewMenuNewView.MENU_ITEM_SPEED, selectItemName)
                )
            )
        }
        if (hasOnlySee == true) {
            //只看他
            mMenuItemData.add(
                ScaleMenuItem(
                    ScaleScreenViewMenuNewView.MENU_ITEM_ONLY_SEE,
                    mVideoPlayMenuBean?.imagesUrl,
                    TextUtils.equals(ScaleScreenViewMenuNewView.MENU_ITEM_ONLY_SEE, selectItemName)
                )
            )
        }
        if (!UserConfigHelper.enableSystemPlayer()) {
            mMenuItemData.add(
                ScaleMenuItem(
                    ScaleScreenViewMenuNewView.MENU_ITEM_MORE, null, TextUtils.equals(
                        ScaleScreenViewMenuNewView.MENU_ITEM_MORE, selectItemName
                    )
                )
            )
        }
    }

    fun updateTabItem() {
        mArrayAdapter?.setItems(mMenuItemData, object : DiffCallback<ScaleMenuItem>() {
            override fun areItemsTheSame(oldItem: ScaleMenuItem, newItem: ScaleMenuItem): Boolean {
                return TextUtils.equals(
                    oldItem.name,
                    newItem.name
                )
            }

            override fun areContentsTheSame(
                oldItem: ScaleMenuItem,
                newItem: ScaleMenuItem
            ): Boolean {
                return oldItem.hasSelect == newItem.hasSelect && oldItem.imagesUrl.toString() == newItem.imagesUrl.toString()
            }
        })
    }

    fun hideMenuItem() {
        mPlayControlMenu?.gone()
    }


    fun show() {
//        controller?.startUpdateProgress()
//        initTabData(mVideoPlayMenuBean)
        controller?.show()
    }

    fun hide() {
        canHide = true
        controller?.isShowing = true
        controller?.hide()
    }

    fun setTitle(title: String?) {
        mTitle?.text = title ?: ""
    }

    override fun onVisibilityChanged(isVisible: Boolean, anim: Animation?) {
        if (isVisible && visibility != VISIBLE) {
            visible()
            controller?.isShowing = true
            mIControlComponentHide?.onMenuControlIsVisible(true)
            mPlayControlMenuBottom.startAnimation(downAppearAnimation)
            mPlayControlMenuTop.startAnimation(upAppearAnimation)
            showDeviceTime()
            updateTabItem()
            val map = java.util.HashMap<String, String>(1)
            if (getInstance().getIsDlna()) {
                map["pageId"] = "1065"
            } else {
                map["pageId"] = "1045"
            }
            RequestManager.getInstance().onAllEvent(EventInfo(10135, "imp"), map, null, null)
        } else {
            if (!canHide) {
                if (visibility == VISIBLE) {
                    controller?.stopFadeOut()
                    controller?.isShowing = true
                }
                return
            }
            mPlayControlMenuBottom.startAnimation(downDisappearAnimation)
            mPlayControlMenuTop.startAnimation(upDisappearAnimation)
            gone()
            controller?.isShowing = false
            mIControlComponentHide?.onMenuControlIsVisible(false)

//            controller?.stopUpdateProgress()
        }
    }

    private var screenMode = PlayerConstants.ScreenMode.NORMAL

    override fun onScreenModeChanged(screenMode: Int) {
        super.onScreenModeChanged(screenMode)
        this.screenMode = screenMode
        if (screenMode != PlayerConstants.ScreenMode.FULL) {
            hide()
        }
    }

    override fun onProgressChanged(duration: Long, position: Long) {
        super.onProgressChanged(duration, position)
        updateRecordNumberVisibility(currentPosition = mCurrentPosition)
        //只看他进度跳转
//        if (mVideoPlayMenuBean?.isFind == true && mVideoPlayMenuBean?.onlySeeModel == true && mVideoPlayMenuBean?.mCurrentSelectOnlySeeMenuItem != null && !isTryVideo) {
//            seekOnlySeePosition()
//        } else {
        if (canUpdateProgressBar) {
            setSeekBar(mCurrentPosition, mDuration)
        }
        if (isTryVideo && isCheckStopTry && mCurrentPosition >= trySeeTime) {
            LibDeprecatedLogger.d("onProgressChanged try  fun completion")
            player?.completion()
        }
        //上报TS广告进度
        AdTsManger.getInstants().checkAppend(checkTsServerTime!!, mDuration)
        AdTsManger.getInstants().reportAdTsProgress(position)
//        }
    }

    /**
     *  更新南川备案号展示
     */
    private fun updateRecordNumberVisibility(currentPosition: Long) {
        if (recordNumber == null || recordNumber!!.isEmpty()) {
            return
        }
        mRecordNumberTv?.text = recordNumber
        val isRecordNumberVisible =
            screenMode == PlayerConstants.ScreenMode.FULL && currentPosition < 4
        // 只在状态改变时更新界面
        if (isRecordNumberVisible != (mRecordNumberTv?.visibility == VISIBLE)) {
            mRecordNumberTv?.visibility = if (isRecordNumberVisible) VISIBLE else GONE
        }
    }


//    private fun setOnlySeekProgress() {
//        setSeekBar(mCurrentPosition, mDuration)
//        player?.seekTo(mCurrentPosition * 1000)
//        i("seekOnlySee", "setOnlySeekProgress mCurrent is：$mCurrentPosition")
//        controller?.startUpdateProgress()
//    }


    @Synchronized
    fun setSeekBar(position: Long, duration: Long) {
        val curPos = if (position < 0) {
            0
        } else if (position >= duration) {
            duration
        } else {
            position
        }
        realProgress?.setFirstBar(curPos.toFloat())
        realProgress?.setSecondBar(curPos.toFloat())
        realProgress?.maxBarPosition = duration.toFloat()
        mPlayerTime?.text = formatTime((curPos).toInt())
        mPlayerTimeEnd?.text = formatTime((duration).toInt())
    }

    fun setProgressModel(
        onlySeeModel: Boolean,
        progressL: MutableList<SoHuVideoProgressBar.SecondListBean>
    ) {
        this.onlySeeModel = onlySeeModel
        if (onlySeeModel) {
            realProgress?.setSecondBarModel(SoHuVideoProgressBar.MODEL_SECOND_LIST)
            realProgress?.setSecondBar(progressL)
        } else {
            realProgress?.setSecondBarModel(SoHuVideoProgressBar.MODEL_SECOND_NORMAL)
        }

    }

    /**
     * 快进
     */
    private fun fastForward(isFast: Boolean = true) {
        canUpdateProgressBar = false
        show()
        setSeekBar(
            getIncrements(if (isFast) FAST_WARD_SECONDS else -FAST_WARD_SECONDS).toLong(),
            mDuration
        )
    }

    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        currentPlayState = playState
        canHide = currentPlayState != PlayerConstants.VideoState.PAUSED
        if (playState.playerIsNotStart()) {
            hide()
        } else if (playState.playerIsPlaying()) {
            playerControllerImage?.setImageResource(PLAYER_STATE_STOP)
        } else {
            playerControllerImage?.setImageResource(PLAYER_STATE_PLAY)
        }
    }


    /**
     * 播放器seek
     */
    fun fastForwardSeekPlay() {
        val forwardSeekProgress = realProgress?.mCurrentFirstBarPosition
        forwardSeekProgress?.let {
            canUpdateProgressBar = true
            player?.seekTo(it.toLong() * 1000)
            //暂停时seek 播放视频
            if (currentPlayState == PlayerConstants.VideoState.PAUSED) {
                player?.start()
            }

        }
    }


    fun getIsShow(): Boolean {
        return visibility == VISIBLE
    }


    /**
     * 快退
     */
    fun getBack() {
        show()
    }


    private fun getIncrements(defaultIncrements: Int): Int {
        val max: Int = realProgress?.maxBarPosition?.toInt() ?: 0
        val progress: Int = realProgress?.mCurrentFirstBarPosition?.toInt() ?: 0
        val secondaryProgress: Int = realProgress?.mCurrentSecondBarPosition?.toInt() ?: 0
        val mCurrent: Int = mCurrentPosition.toInt()
        var next = defaultIncrements
        val MAX_INCREAMENTS = 60f // unit is second
        val LENGTH_SEPRATOR = 900f // 15 * 60 unit is second
        var increments = 0
        //时间短的影片处理
        if (0 < max && max < LENGTH_SEPRATOR) {
            // next = Math.round(defaultIncrements * 0.5f);
            next = defaultIncrements
            //快进
            if (defaultIncrements >= 0) {
                increments = progress + next
            } else {
                //快退
                increments = progress + next
            }
        } else {
            if (defaultIncrements >= 0) {
                //如果第二进度条大于第一进度条
                if (progress > mCurrent) {
                    //第二进度条-第一进度条 小于900f
                    if (progress - mCurrent < LENGTH_SEPRATOR) {
                        next = Math.max(
                            next.toDouble(),
                            Math.round(
                                MAX_INCREAMENTS
                                        * ((progress - mCurrent) / LENGTH_SEPRATOR)
                            ).toDouble()
                        ).toInt()
                        LibDeprecatedLogger.d("next 900<<$next")
                    } else {
                        next = MAX_INCREAMENTS.toInt()
                        LibDeprecatedLogger.d("next 900>>$next")
                    }
                }
                increments = progress + next
            } else {
                if (mCurrent > progress) {
                    if ((mCurrent - progress) < LENGTH_SEPRATOR) {
                        next = Math.min(
                            next.toDouble(), (
                                    -1
                                            * Math.round(
                                        MAX_INCREAMENTS
                                                * ((mCurrent - progress) / LENGTH_SEPRATOR)
                                    )).toDouble()
                        ).toInt()
                        LibDeprecatedLogger.d("next >>$next")
                    } else {
                        next = -1 * MAX_INCREAMENTS.toInt()
                        LibDeprecatedLogger.d("next <<$next")
                    }
                } else {
                    LibDeprecatedLogger.d("next -15")
                    next = -FAST_WARD_SECONDS
                }
                increments = progress + next
            }
        }
        LibDeprecatedLogger.d(
            ("max:" + max + ",progress:" + progress
                    + ",secondaryProgress:" + secondaryProgress + ",next=" + next)
        )
        LibDeprecatedLogger.d("getIncrements:$increments")
        return increments
    }


    private fun showDeviceTime() {
        val currentTime = Calendar.getInstance()
        currentTime.setTime(Date())
        val sysHour = currentTime[Calendar.HOUR_OF_DAY]
        val sysMinute = currentTime[Calendar.MINUTE]
        val hourStr = String.format("%2d:", sysHour)
        val minuteStr: String = if (sysMinute < 10) {
            String.format("0%d", sysMinute)
        } else {
            String.format("%2d", sysMinute)
        }
        mDeviceTime?.text = hourStr + minuteStr
    }

    protected fun formatTime(seconds: Int): String {
        try {
            val second = seconds % 60
            val minutes = seconds / 60 % 60
            val hours = seconds / 3600
            mFormatBuilder?.setLength(0)
            return mFormatter?.format("%02d:%02d:%02d", hours, minutes, second)
                .toString()
        } catch (e: Throwable) {
            return ""
        }
    }

    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.action == KeyEvent.ACTION_DOWN) {
            when (event?.keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    hide()
                    return true
                }

                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    fastForward(false)
                    return true
                }

                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    fastForward()
                    return true
                }
                //处理点击事件
                KeyEvent.KEYCODE_ENTER,
                KeyEvent.KEYCODE_DPAD_CENTER -> {
                    if (currentPlayState?.playerIsPlaying() == true) {
                        player?.pause()
                        return super.dispatchKeyEvent(event)
                    }
                    if (currentPlayState == PlayerConstants.VideoState.PAUSED) {
                        player?.start()
                        return super.dispatchKeyEvent(event)
                    }
                    return super.dispatchKeyEvent(event)
                }
            }
        } else {
            when (event?.keyCode) {
                KeyEvent.KEYCODE_DPAD_LEFT,
                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    fastForwardSeekPlay()
                    return true
                }
            }
        }
        return super.dispatchKeyEvent(event)
    }

    companion object {
        /**
         * 自动隐藏时间
         */
        const val SHOW_TIME = 4000

        private const val FAST_WARD_SECONDS = 15

    }

}