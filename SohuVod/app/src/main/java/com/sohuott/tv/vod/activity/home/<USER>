package com.sohuott.tv.vod.activity.home

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Adapter
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.forEach
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.com.sohuott.tv.vod.base_component.recycler.SoRecyclerView
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.databinding.ActivityHomeLine2ItemBinding
import com.sohuott.tv.vod.databinding.ActivityHomeLine3ItemBinding
import com.sohuott.tv.vod.databinding.ActivityHomeLine4ItemBinding
import com.sohuott.tv.vod.databinding.ActivityHomeLine6ItemBinding
import com.sohuott.tv.vod.databinding.ActivityHomeLineHistoryItemBinding
import com.sohuott.tv.vod.databinding.ItemTypeZeroLayoutBinding
import com.sohuott.tv.vod.lib.model.ContentGroup

class HomeAdapter(private val contentList: MutableList<RowData>): RecyclerView.Adapter<ViewHolder>() {

    class RowData(val type: Int, val data: Any)
    companion object{
        const val TYPE_1 = 1 //首页两大图 -> type2/7
        const val TYPE_2 = 2 //四小图 -> type5/3/8 type41
        const val TYPE_3 = 3 //历史记录+为你推荐3 -> type13
        const val TYPE_4 = 4 //三大图 -> type36/30/31/34
        const val TYPE_5 = 5 //六横文字 -> type6/9
        const val TYPE_6 = 6 //推新-> type4
        const val TYPE_7 = 7 //六小图 -> type36/30/31/34
        const val TYPE_8 = 8 //制作人 -> type40
        const val TYPE_9 = 9 //即将上线 -> type32
        const val TYPE_10 = 10 //为你推荐40
        const val TYPE_11 = 11 //会员
        const val TYPE_12 = 12 //标题
        const val TYPE_13 = 13 //footer
    }



//    override fun onPrepareBindViewHolder(holder: SoRecyclerView.SoViewHolder, position: Int) {
//        Log.i("Catch", "onPrepareBindViewHolder:$position")
////        if (holder is ItemViewHolder6) {
////            val parentView = holder.itemView as FrameLayout
////            for (i in 0 until parentView.childCount) {
////                parentView.getChildAt(i).findViewById<ImageView>(R.id.iv_type_three_poster).
////            }
////        }
//        if (holder is ItemViewHolder1) {
//            for (i in 0 until holder.binding.root.childCount) {
//                holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_one_name).text = ((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).name
////                Glide.with(holder.binding.root.context)
////                    .load(((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).picUrl)
////                    .placeholder(R.drawable.bg_launcher_poster)
////                    .transform(RoundedCorners(holder.binding.root.context.resources?.getDimensionPixelOffset(R.dimen.x10)!!))
////                    .into(holder.binding.root.getChildAt(i).findViewById(R.id.iv_type_one_poster) as ImageView)
//                Glide.with(holder.binding.root.context).load(((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).picUrl).into(holder.binding.root.getChildAt(i).findViewById<ImageView>(R.id.iv_type_one_poster))
//            }
//        }
//    }

//    override fun onDelayBindViewHolder(holder: SoRecyclerView.SoViewHolder, position: Int) {
////        if (holder is ItemViewHolder1) {
////            for (i in 0 until holder.binding.root.childCount) {
////                holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_one_name).text = ((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).name
////                Glide.with(holder.binding.root.context)
////                    .load(((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).picUrl)
////                    .placeholder(R.drawable.bg_launcher_poster)
////                    .transform(RoundedCorners(holder.binding.root.context.resources?.getDimensionPixelOffset(R.dimen.x10)!!))
////                    .into(holder.binding.root.getChildAt(i).findViewById(R.id.iv_type_one_poster) as ImageView)
//////                Glide.with(holder.binding.root.context).load(((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).picUrl).into(holder.binding.root.getChildAt(i).findViewById<ImageView>(R.id.iv_type_one_poster))
////            }
////        }
//        Log.i("Catch", "onDelayBindViewHolder:$position")
//
//
//    }

//    override fun onUnBindDelayViewHolder(holder: SoRecyclerView.SoViewHolder) {
//        Log.i("Catch", "onUnBindDelayViewHolder:" + holder.adapterPosition)
//
//    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return when (viewType) {
            TYPE_1 -> {
                ItemViewHolder1(ActivityHomeLine2ItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            }
            TYPE_2 -> {
                ItemViewHolder2(ActivityHomeLine4ItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))

//                val contentView = LayoutInflater.from(parent.context).inflate(R.layout.activity_home_line4_item, parent, false)
//                ItemViewHolder6(contentView)
            }
            TYPE_3 -> {
//                val contentView = LayoutInflater.from(parent.context).inflate(R.layout.activity_home_line_history_item, parent, false)
//                ItemViewHolder6(contentView)
                ItemViewHolder3(ActivityHomeLineHistoryItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))

            }
            TYPE_4 -> {
                ItemViewHolder4(ActivityHomeLine3ItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))

//                val contentView = LayoutInflater.from(parent.context).inflate(R.layout.activity_home_line3_item, parent, false)
//                ItemViewHolder6(contentView)
            }
            TYPE_5 -> {
                val contentView = LayoutInflater.from(parent.context).inflate(R.layout.activity_home_line6_text_item, parent, false)
                ItemViewHolder6(contentView)
            }
            TYPE_7 -> {
                ItemViewHolder5(ActivityHomeLine6ItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))

            }
            TYPE_12 -> {
                val contentView = LayoutInflater.from(parent.context).inflate(R.layout.item_type_header_layout, parent, false)
                ItemViewHolder6(contentView)
            }
            else -> {
                val contentView = LayoutInflater.from(parent.context).inflate(R.layout.activity_home_line6_item, parent, false)
                ItemViewHolder6(contentView)
            }
        }

    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (holder is ItemViewHolder1) {
            for (i in 0 until holder.binding.root.childCount) {
                holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_one_name).text = ((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).name
//                Glide.with(holder.binding.root.context)
//                    .load(((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).picUrl)
//                    .placeholder(R.drawable.bg_launcher_poster)
//                    .transform(RoundedCorners(holder.binding.root.context.resources?.getDimensionPixelOffset(R.dimen.x10)!!))
//                    .into(holder.binding.root.getChildAt(i).findViewById(R.id.iv_type_one_poster) as ImageView)
                Glide.with(holder.binding.root.context).load(((contentList[position].data as ArrayList<*>)[i] as ContentGroup.DataBean.ContentsBean).picUrl).into(holder.binding.root.getChildAt(i).findViewById<ImageView>(R.id.iv_type_one_poster))
            }
        } else if (holder is ItemViewHolder3) {
            // TODO 历史View
            val data = contentList[position].data
            for (i in 1 until holder.binding.root.childCount) {
                ((data as List<*>)[i - 1] as? ContentGroup.DataBean.ContentsBean.AlbumListBean)?.let { albumListBean ->
                    val textView = holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_two_name)
                    textView.text = albumListBean.tvName

                    Glide.with(holder.binding.root.context).load(((data as List<*>)[i - 1] as ContentGroup.DataBean.ContentsBean.AlbumListBean).albumExtendsPic_240_180).into(holder.binding.root.getChildAt(i).findViewById<ImageView>(R.id.iv_type_two_poster))

                }
//                holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_two_name).text =
//                    ((contentList[position].data as ArrayList<*>)[i - 1] as ContentGroup.DataBean.ContentsBean).name
            }
        } else if (holder is ItemViewHolder2) {
            val data = contentList[position].data
            for (i in 0 until holder.binding.root.childCount) {
                ((data as List<*>)[i] as? ContentGroup.DataBean.ContentsBean)?.let { contentsBean ->
                    val textView = holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_two_name)
                    textView.text = contentsBean.name

                    Glide.with(holder.binding.root.context).load(contentsBean.picUrl).into(holder.binding.root.getChildAt(i).findViewById<ImageView>(R.id.iv_type_two_poster))
                }

                ((data as List<*>)[i] as? ContentGroup.DataBean.ContentsBean.AlbumListBean)?.let { albumListBean ->
                    val textView = holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_two_name)
                    textView.text = albumListBean.tvName

                    Glide.with(holder.binding.root.context).load(albumListBean.albumExtendsPic_240_180).into(holder.binding.root.getChildAt(i).findViewById<ImageView>(R.id.iv_type_two_poster))
                }
            }
        } else if (holder is ItemViewHolder4) {
            val data = contentList[position].data
            for (i in 0 until holder.binding.root.childCount) {
                ((data as List<*>)[i] as? ContentGroup.DataBean.ContentsBean.AlbumListBean)?.let { albumListBean ->
                    val textView = holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_two_name)
                    textView.text = albumListBean.tvName

                    Glide.with(holder.binding.root.context).load(((data as List<*>)[i] as ContentGroup.DataBean.ContentsBean.AlbumListBean).albumExtendsPic_240_180).into(holder.binding.root.getChildAt(i).findViewById<ImageView>(R.id.iv_type_two_poster))

                }
            }
        } else if (holder is ItemViewHolder5) {
            val data = contentList[position].data
            for (i in 0 until holder.binding.root.childCount) {
                ((data as List<*>)[i] as? ContentGroup.DataBean.ContentsBean.AlbumListBean)?.let { albumListBean ->
                    val textView = holder.binding.root.getChildAt(i).findViewById<TextView>(R.id.tv_type_three_name)
                    textView.text = albumListBean.tvName

                    Glide.with(holder.binding.root.context).load(((data as List<*>)[i] as ContentGroup.DataBean.ContentsBean.AlbumListBean).albumExtendsPic_240_180).into(holder.binding.root.getChildAt(i).findViewById<ImageView>(R.id.iv_type_three_poster))

                }
            }
        }
    }

    override fun getItemCount(): Int {
        return contentList.size
    }

    override fun getItemViewType(position: Int): Int {
        return contentList[position].type
    }




    inner class ItemViewHolder6(val itemView1 : View): ViewHolder(itemView1)
    inner class ItemViewHolder5(val binding: ActivityHomeLine6ItemBinding): ViewHolder(binding.root)

    inner class ItemViewHolder4(val binding: ActivityHomeLine3ItemBinding): ViewHolder(binding.root) {
//        var dataTxt1: TextView = itemView.findViewById(R.id.text1)
//        var icon1: ImageView = itemView.findViewById(R.id.img1)
//        var dataTxt2: TextView = itemView.findViewById(R.id.text2)
//        var icon2: ImageView = itemView.findViewById(R.id.img2)
//        var dataTxt3: TextView = itemView.findViewById(R.id.text3)
//        var icon3: ImageView = itemView.findViewById(R.id.img3)
//        var dataTxt4: TextView = itemView.findViewById(R.id.text4)
//        var icon4: ImageView = itemView.findViewById(R.id.img4)
//        var item1: FrameLayout = itemView.findViewById(R.id.item1)
//        var item2: FrameLayout = itemView.findViewById(R.id.item2)
//        var item3: FrameLayout = itemView.findViewById(R.id.item3)
//        var item4: FrameLayout = itemView.findViewById(R.id.item4)
    }

    inner class ItemViewHolder3(val binding: ActivityHomeLineHistoryItemBinding): ViewHolder(binding.root) {
//        var dataTxt1: TextView = itemView.findViewById(R.id.text1)
//        var icon1: ImageView = itemView.findViewById(R.id.img1)
//        var dataTxt2: TextView = itemView.findViewById(R.id.text2)
//        var icon2: ImageView = itemView.findViewById(R.id.img2)
//        var item1: FrameLayout = itemView.findViewById(R.id.item1)
//        var item2: FrameLayout = itemView.findViewById(R.id.item2)
    }


    inner class ItemViewHolder2(val binding: ActivityHomeLine4ItemBinding): ViewHolder(binding.root) {
//        var dataTxt1: TextView = itemView.findViewById(R.id.text1)
//        var icon1: ImageView = itemView.findViewById(R.id.img1)
//        var dataTxt2: TextView = itemView.findViewById(R.id.text2)
//        var icon2: ImageView = itemView.findViewById(R.id.img2)
//        var item1: FrameLayout = itemView.findViewById(R.id.item1)
//        var item2: FrameLayout = itemView.findViewById(R.id.item2)
    }

    inner class ItemViewHolder1(val binding: ActivityHomeLine2ItemBinding): ViewHolder(binding.root) {
//        var dataTxt1: TextView = itemView.findViewById(R.id.text1)
//        var icon1: ImageView = itemView.findViewById(R.id.img1)
//        var dataTxt2: TextView = itemView.findViewById(R.id.text2)
//        var icon2: ImageView = itemView.findViewById(R.id.img2)
//        var item1: FrameLayout = itemView.findViewById(R.id.item1)
//        var item2: FrameLayout = itemView.findViewById(R.id.item2)
    }

}