package com.sohuott.tv.vod.activity.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.com.sohuott.tv.vod.base_component.NewBaseFragment
import com.google.gson.Gson
import com.lib_viewbind_ext.viewBinding
import com.sohuott.tv.vod.AppLogger.v
import com.sohu.lib_utils.PrefUtil
import com.sohu.lib_utils.StringUtil
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.launcher.LauncherViewModel
import com.sohuott.tv.vod.databinding.FragmentHomeContentNewBinding
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.ContentGroup
import com.sohuott.tv.vod.lib.model.ContentGroup.DataBean.ContentsBean.AlbumListBean
import com.sohuott.tv.vod.lib.model.HomeRecommendBean
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.model.Header
import kotlinx.coroutines.launch

class HomeContentFragment : NewBaseFragment(R.layout.fragment_home_content_new) {

    private var viewBinding: FragmentHomeContentNewBinding? = null
    private val _binding by viewBinding(FragmentHomeContentNewBinding::bind)

    private val launcherViewModel: LauncherViewModel by viewModels()


    private var mCurrentTabPosition: Int? = null
    private var mCurrentTabCode : Int? = null
    private var mCurrentTabType: Int? = null
    private var mSubjectId: Int? = null

    private var adapter: HomeAdapter? = null

    // 用于存储合并后的数据
    private val combinedData: MutableList<HomeAdapter.RowData> = mutableListOf()

    //需要替换人工运营的推荐3时，不展示原本的为你推荐内容
    private val mNeedReplaceRecommend_3 = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments ?: return
        mCurrentTabPosition = requireArguments().getInt("position")
        mCurrentTabCode = requireArguments().getLong("id").toInt()
        mCurrentTabType = requireArguments().getInt("type")
        mSubjectId =
            PrefUtil.getString("config", "coming_id", "0")?.toInt()
        LibDeprecatedLogger.d("onCreate pos:$mCurrentTabPosition tabCode: $mCurrentTabCode tabType: $mCurrentTabType")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        LibDeprecatedLogger.d("onCreateView mCurrentTabCode：$mCurrentTabCode")
        return super.onCreateView(inflater, container, savedInstanceState)
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        LibDeprecatedLogger.d("onViewCreated mCurrentTabCode：$mCurrentTabCode")
        viewBinding = _binding

        initView()
        mCurrentTabCode?.let {
            initData(it)
        }
    }

    private fun initView() {
//        viewBinding?.vgContent?.setScrollType(SoRecyclerView.SCROLL_TYPE_ALWAYS_CENTER)
//        val layoutManager = SoRecyclerView.SoGridLayoutManager(activity, 1, RecyclerView.VERTICAL)
        val layoutManager = GridLayoutManager(activity, 1, RecyclerView.VERTICAL, false)
        viewBinding?.vgContent?.layoutManager = layoutManager


    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (mCurrentTabCode != null && isVisibleToUser) {
            initData(mCurrentTabCode!!)
        }
        LibDeprecatedLogger.d("setUserVisibleHint mCurrentTabCode：$mCurrentTabCode isVisibleToUser: $isVisibleToUser")
    }


    override fun onResume() {
        LibDeprecatedLogger.d("onResume mCurrentTabCode：$mCurrentTabCode")
        super.onResume()
    }

    private fun initData(currentTabCode: Int) {
        lifecycleScope.launch {
            launcherViewModel.loadMore(currentTabCode)
        }

        launcherViewModel.homeContentData.observe(viewLifecycleOwner) {
            LibDeprecatedLogger.d("homeContentData: $it")
            processData(it)
            adapter = HomeAdapter(combinedData)
            viewBinding?.vgContent?.adapter = adapter
            viewBinding?.vgContent?.requestFocus()
        }
    }

    //调整首页数据顺序，重组成列表
    private fun processData(content: ContentGroup?) {
//        adapter.contentGroup.data.clear()

        // 检查内容是否为空
        if (content?.data == null) return

        LibDeprecatedLogger.d("========HomeFragment setContent start========")
        LibDeprecatedLogger.d("id: $mCurrentTabCode")



        try {
            content.data.forEach { dataBean ->

                if (dataBean.contents == null) {
                    LibDeprecatedLogger.d("dataBean.contents is null")
                    return@forEach // 继续处理下一个 dataBean
                }

                dataBean.contents?.forEach { contentsBean ->
                    // 检查所有相关列表是否为空
                    if (contentsBean.albumList != null ||
                        contentsBean.subjectVideoList != null ||
                        contentsBean.pgcVideoList != null ||
                        contentsBean.producersList != null
                    ) {
                        // 处理非首屏数据
//                        combinedData.add(contentsBean)
                        processOtherScreenContent(contentsBean)
                        LibDeprecatedLogger.d("OtherScreenContent-> type: ${contentsBean.type}")
                    }
                }

                // 处理首屏数据
//                combinedData.add(dataBean)
                processFirstScreenContent(dataBean)

                // 特定条件下终止处理
//                if ((activity is MainActivity)) {
//                    AppLogger.d("是topview广告且不是第一次进入launcher，先不展示任何数据")
//                    return@forEach // 继续处理下一个 dataBean
//                }
            }

            // 添加页脚
//            combinedData.add(processFooter())

            // 使用组合数据更新适配器
//            updateAdapter(combinedData)
//            combinedData
        } catch (e: Exception) {
            LibDeprecatedLogger.e("Error processing content: ${e.message}")
            // 可以在这里处理异常，例如显示错误消息或发送错误报告
        }

        LibDeprecatedLogger.d("========HomeFragment setContent end========")
    }

    override fun onPause() {
        super.onPause()
        LibDeprecatedLogger.d("onPause mCurrentTabCode：$mCurrentTabCode")
    }

    private fun processOtherScreenContent(contentsBean: ContentGroup.DataBean.ContentsBean) {
        // 这里是处理非首屏数据的逻辑
        initPathLog(contentsBean)
        val list = contentsBean.albumList
        when (contentsBean.type) {
            Constant.TYPE_36, Constant.TYPE_30, Constant.TYPE_31, Constant.TYPE_34 -> { //3+6 、 推新
                // 剪裁 List
                if (list == null || list.size < 9) {
                    return
                }
                list.subList(0, 3).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_4, contentsBean.name)
                }
                list.subList(3, 9).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_7)
                }
            }
            Constant.TYPE_40 -> { //出品人类型
                if (contentsBean.producersList == null || contentsBean.producersList.size < 6) {
                    return
                }
                contentsBean.producersList.subList(0, 6).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_8, contentsBean.name)
                }
            }
            Constant.TYPE_41 -> { //PGC 4+4
                if (contentsBean.pgcVideoList == null || contentsBean.pgcVideoList.size < 4) {
                    return
                }
                if (contentsBean.pgcVideoList.size < 8) {
                    contentsBean.pgcVideoList.subList(0, 4).let {
                        if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_2, contentsBean.name)
                    }
                } else {
                    contentsBean.pgcVideoList.subList(0, 4).let {
                        if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_2, contentsBean.name)
                    }
                    contentsBean.pgcVideoList.subList(4, 8).let {
                        if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_2)
                    }
                }
            }
            Constant.TYPE_32 -> { //即将上线
                // 裁剪list 大于6 舍弃
                if (contentsBean.subjectVideoList == null || contentsBean.subjectVideoList.size < 6) {
                    return
                }

                contentsBean.subjectVideoList.subList(0, 6).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_9, contentsBean.name)
                }

            }
            Constant.TYPE_39 -> { //6个小图
                // 裁剪list 大于6 舍弃
                if (list == null || list.size < 6) {
                    return
                }
                list.subList(0, 6).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_7,contentsBean.name)
                }
            }
            Constant.TYPE_38 -> { //为你推荐 40个
                //TODO 为无限下滑做准备
            }
            Constant.TYPE_33, Constant.TYPE_35 -> { //推新
                if (list == null || list.size < 4) {
                    return
                }
                if (list.size > 3) {
                    list.subList(0, 4).let {
                        if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_2, contentsBean.name)
                    }
                }
                if (list.size > 7) {
                    list.subList(4, 8).let {
                        if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_2)
                    }

                }
            }
            Constant.TYPE_37 -> { //为你推荐3
                if (list == null || mNeedReplaceRecommend_3) {
                    return
                }

                list.subList(0, 3).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_3, index = 1)
                }

            }
        }

    }

    private fun addListAll(it: MutableList<*>, type: Int, title: String = "", index: Int = -1) {
        if (title.isNotEmpty()) combinedData.add(HomeAdapter.RowData(HomeAdapter.TYPE_12, Header(title, mCurrentTabType!!)))
        if (index != -1) {
            combinedData.addAll(index,listOf(HomeAdapter.RowData(type,it)))
        } else {
            combinedData.addAll(listOf(HomeAdapter.RowData(type,it)))
        }
    }

    private fun processFirstScreenContent(dataBean: ContentGroup.DataBean) {
        initFirstScreenPathLog(dataBean)
        val list = dataBean.contents
        // 这里是处理首屏数据的逻辑
        LibDeprecatedLogger.d("FirstScreenContent-> type: ${dataBean.type}")
        when (dataBean.type) {
            Constant.TYPE_4 -> { //推新
                processFirstScreenListBySize(list, 1).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_6)
                }
                // TODO 设置推新背景
            }
            Constant.TYPE_7, Constant.TYPE_2 -> { //PGC
                processFirstScreenListBySize(list, 2).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_1)
                }
            }
            Constant.TYPE_5, Constant.TYPE_8, Constant.TYPE_3 -> { //推新、PGC、4个横图
                processFirstScreenListBySize(list, 4).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_2)
                }
            }
            Constant.TYPE_9, Constant.TYPE_6 -> { //PGC、6个分类
                processFirstScreenListBySize(list, 6).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_5)
                }
            }
            Constant.TYPE_10 -> { //vip4个横图
                processFirstScreenListBySize(list, 4).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_11)
                }

                //TODO 会员头图
            }
            Constant.TYPE_12 -> { //皮肤
                //TODO 设置首页皮肤
            }
            Constant.TYPE_13 -> { //历史+为你推荐3 后台手动版
                processFirstScreenListBySize(list, 3).let {
                    if (it.isNotEmpty()) addListAll(it, HomeAdapter.TYPE_3, index = 1)
                }
                //TODO 播放历史替换逻辑
            }
        }
    }

    private fun initPathLog(contentsBean: ContentGroup.DataBean.ContentsBean) {
        if (contentsBean.albumList != null) {
            for (i in contentsBean.albumList.indices) {
                if (contentsBean.albumList[i] == null) return
                val albumListBean = contentsBean.albumList[i]
                val pathInfo = java.util.HashMap<String, String?>()
                pathInfo["pageId"] = StringUtil.toString(mCurrentTabCode!!)
                pathInfo["columnId"] = StringUtil.toString(contentsBean.id)
                pathInfo["index"] = StringUtil.toString(i + 1)
                albumListBean.pathInfo = pathInfo
                val objectInfo = java.util.HashMap<String, String?>()
                objectInfo["type"] = "视频"
                objectInfo["vid"] = StringUtil.toString(albumListBean.tvVerId)
                objectInfo["playlistid"] = StringUtil.toString(albumListBean.id)
                albumListBean.objectInfo = objectInfo
                if (!StringUtil.isEmpty(albumListBean.pdna)) {
                    val memoInfo = java.util.HashMap<String, String?>()
                    memoInfo["pdna"] = albumListBean.pdna
                    albumListBean.memoInfo = memoInfo
                }
                albumListBean.channelType = mCurrentTabType!!
            }
        } else if (contentsBean.producersList != null) {
            for (i in contentsBean.producersList.indices) {
                if (contentsBean.producersList[i] == null) return
                val producerBean = contentsBean.producersList[i]
                val pathInfo = java.util.HashMap<String, String?>()
                pathInfo["pageId"] = StringUtil.toString(mCurrentTabCode!!)
                pathInfo["columnId"] = StringUtil.toString(contentsBean.id)
                pathInfo["index"] = StringUtil.toString(i + 1)
                producerBean.pathInfo = pathInfo
                val objectInfo = java.util.HashMap<String, String?>()
                objectInfo["type"] = "user"
                objectInfo["id"] = StringUtil.toString(producerBean.uid)
                producerBean.objectInfo = objectInfo
            }
        } else if (contentsBean.subjectVideoList != null) {
            for (i in contentsBean.subjectVideoList.indices) {
                if (contentsBean.subjectVideoList[i] == null) return
                val subjectBean = contentsBean.subjectVideoList[i]
                val pathInfo = java.util.HashMap<String, String?>()
                pathInfo["pageId"] = StringUtil.toString(mCurrentTabCode!!)
                pathInfo["columnId"] = StringUtil.toString(contentsBean.id)
                pathInfo["index"] = StringUtil.toString(i + 1)
                subjectBean.pathInfo = pathInfo
                val gson = Gson()
                val albumListBean = gson.fromJson(
                    subjectBean.parameter,
                    AlbumListBean::class.java
                )
                val memoInfo = java.util.HashMap<String, String?>()
                memoInfo["playlistId"] = StringUtil.toString(albumListBean.albumId)
                memoInfo["vid"] = StringUtil.toString(albumListBean.tvVerId)
                memoInfo["catecode"] = StringUtil.toString(albumListBean.cateCode)
                subjectBean.memoInfo = memoInfo
            }
        } else if (contentsBean.pgcVideoList != null) {
            for (i in contentsBean.pgcVideoList.indices) {
                if (contentsBean.pgcVideoList[i] == null) return
                val dataEntity = contentsBean.pgcVideoList[i]
                val pathInfo = java.util.HashMap<String, String?>()
                pathInfo["pageId"] = StringUtil.toString(mCurrentTabCode!!)
                pathInfo["columnId"] = StringUtil.toString(contentsBean.id)
                pathInfo["index"] = StringUtil.toString(i + 1)
                dataEntity.pathInfo = pathInfo
                val objectInfo = java.util.HashMap<String, String?>()
                objectInfo["playlistId"] = StringUtil.toString(dataEntity.playListId)
                objectInfo["vid"] = StringUtil.toString(dataEntity.videoId)
                objectInfo["type"] = "视频"
                dataEntity.objectInfo = objectInfo
            }
        }
    }

    private fun initFirstScreenPathLog(dataBean: ContentGroup.DataBean) {
        val type = dataBean.type
        if (dataBean.contents != null && dataBean.contents.size > 0) {
            for (i in dataBean.contents.indices) {
                if (dataBean.contents[i] == null) return
                val contentsBean = dataBean.contents[i]
                val gson = Gson()
                val parameter = gson.fromJson(
                    contentsBean.parameter,
                    HomeRecommendBean.Data.Content.Parameter::class.java
                )
                val pathInfo = HashMap<String, String?>()
                pathInfo["pageId"] = StringUtil.toString(mCurrentTabCode!!)
                pathInfo["columnId"] = StringUtil.toString(dataBean.id)
                pathInfo["index"] = StringUtil.toString(i + 1)
                contentsBean.pathInfo = pathInfo
                if (type == Constant.TYPE_6 || type == Constant.TYPE_9) {
                    if (contentsBean.ottCategoryId != null) {
                        val memoInfo = HashMap<String, String?>()
                        memoInfo["collectionId"] = contentsBean.ottCategoryId
                        contentsBean.memoInfo = memoInfo
                    }
                } else if (type == Constant.TYPE_2) {
                    //首页两大图，需要判断是否为动态视频类型，增加memoInfo
                    var ctype: String
                    ctype =
                        if (contentsBean.parameterPianhua != null && !contentsBean.parameterPianhua.isEmpty()) {
                            "1"
                        } else {
                            "0"
                        }
                    val memoInfo = HashMap<String, String?>()
                    memoInfo["ctype"] = ctype
                    contentsBean.memoInfo = memoInfo
                }
                if (parameter == null) continue
                val objectInfo = HashMap<String, String?>()
                objectInfo["type"] = "视频"
                objectInfo["vid"] = parameter.tvVerId
                objectInfo["playlistid"] = parameter.albumId
                contentsBean.objectInfo = objectInfo
                contentsBean.channelType = mCurrentTabType!!
            }
        }
    }


    private fun processFirstScreenListBySize(list: List<ContentGroup.DataBean.ContentsBean>, count: Int): MutableList<ContentGroup.DataBean.ContentsBean> {
        return list.takeIf { it.isNotEmpty() && it.size >= count }?.take(count)?.toMutableList() ?: return mutableListOf()
    }

//    private fun processListBySize(list: List<ContentGroup.DataBean.ContentsBean.AlbumListBean>, count: Int): List<ContentGroup.DataBean.ContentsBean.AlbumListBean> {
//        return list.takeIf { it.isNotEmpty() && it.size >= count }?.take(count) ?: return emptyList()
//    }

//    private fun processFooter() {
//        // 这里是添加页脚的逻辑，并返回页脚对象
//        // 返回值类型应根据实际情况调整
//    }
//
//    private fun updateAdapter(combinedData: List<>) {
//        // 使用组合后的数据更新适配器
//        // 适配器的更新逻辑在这里实现
//    }
}