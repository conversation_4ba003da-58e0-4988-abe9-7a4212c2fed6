package com.sohuott.tv.vod.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;

import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatTextView;

import com.sohuott.tv.vod.R;

/**
 *
 * <AUTHOR>
 */
public class DetailTextImageView extends AppCompatTextView {
    private Drawable drawableTop;
    private int topWidth;
    private int topHeight;


    public DetailTextImageView(Context context) {
        super(context);
        init(context, null);
    }

    public DetailTextImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public DetailTextImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.DetailTextImageView);
        drawableTop = typedArray.getDrawable(R.styleable.DetailTextImageView_topDrawable);
        if (drawableTop != null) {
            topWidth = typedArray.getDimensionPixelOffset(R.styleable.DetailTextImageView_topDrawableWidth,
                    (int) getResources().getDimension(R.dimen.x45));
            topHeight = typedArray.getDimensionPixelOffset(R.styleable.DetailTextImageView_topDrawableHeight,
                    (int) getResources().getDimension(R.dimen.x45));
            int top = 0;
            drawableTop.setBounds(0, top, topWidth, topHeight + top);
            this.setCompoundDrawables(null, drawableTop, null, null);
        }
        typedArray.recycle();
    }
}