package com.sohuott.tv.vod.activity;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.request.target.DrawableImageViewTarget;
import com.bumptech.glide.request.transition.Transition;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.adapter.ListVideoAdapter;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ListAlbumModel;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.presenter.ListVideoPresenterImpl;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.ParamConstant;
import com.sohuott.tv.vod.view.BaseItemDecoration;
import com.sohuott.tv.vod.view.CustomLinearLayoutManager;
import com.sohuott.tv.vod.view.CustomLinearRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.ListVideoView;
import com.sohuott.tv.vod.widget.GlideImageView;


import java.util.List;

/**
 * Created by XiyingCao on 16-1-8.
 */
public class ListVideoActivity extends BaseActivity implements ListVideoView,
        ListVideoAdapter.TagListSelectedChangeCallback {

    private boolean mNormal = true;
    private View mParentView;
    private long mLabel;
    private int mListCount = 0;
    private GlideImageView mBgView;
    private CustomLinearRecyclerView mRecyclerView;
    private FocusBorderView mFocusBorderView;
    private LoadingView mLoadingView;
    private View mErrorView;

    private TextView mTitleView;
    private TextView mSubTitleView;
    private TextView mCategoryView;
    private TextView mIndexView;
    private TextView mCountView;
    private TextView mVideoScoreView;
    private ImageView mVideoScoreIcon;
    private TextView mVideoTitleView;
    private TextView mVideoSubTitleView;
    private TextView mVideoDetailsView;

    private ListVideoAdapter mListVideoAdapter;
    private CustomLinearLayoutManager mLayoutManager;
    private ListVideoPresenterImpl mListPresenter;
    private ImageView mContinuePlayButton;
    private boolean mIsDts = false;
    private boolean isFromPopupWindow;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LibDeprecatedLogger.d("List page onCreate!");
        setContentView(R.layout.activity_list);
        initParams();
        initView();
        mLoadingView.setVisibility(View.VISIBLE);
        mErrorView.setVisibility(View.GONE);
        mParentView.setVisibility(View.GONE);
        initializeCollectionView();
        mListVideoAdapter.setDts(mIsDts);
        RequestManager.getInstance().onTagVideoListExposureEvent(mLabel);
        setPageName("5_list_label_details");
    }

    private void initView() {
        mParentView = findViewById(R.id.parent);
        mLoadingView = (LoadingView) findViewById(R.id.detail_loading_view);
        mErrorView = findViewById(R.id.err_view);
        mBgView = (GlideImageView) findViewById(R.id.fragment_bg);
        mFocusBorderView = (FocusBorderView) findViewById(R.id.fragment_item_focus);
        mTitleView = (TextView) findViewById(R.id.title);
        mIndexView = (TextView) findViewById(R.id.list_index);
        mCountView = (TextView) findViewById(R.id.list_count);
        mSubTitleView = (TextView) findViewById(R.id.sub_title);
        mCategoryView = (TextView) findViewById(R.id.catgory);
        mVideoScoreView = (TextView) findViewById(R.id.score);
        mVideoScoreIcon = (ImageView) findViewById(R.id.doubangIV);
        mVideoTitleView = (TextView) findViewById(R.id.album_title);
        mVideoSubTitleView = (TextView) findViewById(R.id.album_director);
        mVideoDetailsView = (TextView) findViewById(R.id.album_details);
        mRecyclerView = (CustomLinearRecyclerView) findViewById(R.id.list);
        mContinuePlayButton = (ImageView) findViewById(R.id.continue_play);

        mRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mRecyclerView.addItemDecoration(new BaseItemDecoration(getResources().getDimensionPixelSize(R.dimen.x15)));
        mRecyclerView.setItemViewCacheSize(0);

        mRecyclerView.setChildDrawingOrderCallback(new RecyclerView.ChildDrawingOrderCallback() {
            @Override
            public int onGetChildDrawingOrder(int childCount, int i) {
                int pos = mRecyclerView.indexOfChild(mRecyclerView.getFocusedChild());
                if (pos < 0 || pos >= childCount - 1) {
                    return i;
                }

                if (pos == i) {
                    return i + 1;
                } else if (i == pos + 1) {
                    return i - 1;
                } else {
                    return i;
                }
            }
        });
    }

    private void initializeCollectionView() {
        mLayoutManager = new CustomLinearLayoutManager(this);
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        mLayoutManager.setCustomPadding(getResources().getDimensionPixelSize(R.dimen.x112), getResources().getDimensionPixelSize(R.dimen.x70));
        mRecyclerView.setLayoutManager(mLayoutManager);

        mListVideoAdapter = new ListVideoAdapter(mRecyclerView, mLabel);
        mListVideoAdapter.setTagListSelectedChangeCallback(this);
        mListVideoAdapter.setFocusBorderView(mFocusBorderView);
        mRecyclerView.setAdapter(mListVideoAdapter);

        mListPresenter = new ListVideoPresenterImpl(mLabel, mNormal);
        mListPresenter.setView(this);
        mListPresenter.onViewResumed();

        //TODO: Modify PartnerNo and judging condition
        if (Util.isSupportTouchVersion(this)) {
            mRecyclerView.setOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                    super.onScrollStateChanged(recyclerView, newState);
                    if (newState == RecyclerView.SCROLL_STATE_IDLE && !Util.isSupportTouchVersion(ListVideoActivity.this)) {
                        RecyclerView.ViewHolder viewHolder = mRecyclerView.findViewHolderForAdapterPosition(mListVideoAdapter.getSelctedPos());

                        if (viewHolder != null && viewHolder.itemView != null) {
                            mFocusBorderView.setFocusView(viewHolder.itemView);
                        }
                    }
                }

                @Override
                public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);
                }
            });
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        LibDeprecatedLogger.d("List page onNewIntent!");
        setIntent(intent);
        showLoading();

        mListCount = 0;
        mListVideoAdapter.clear();
        mIndexView.setText("");
        mCountView.setText("");

        initParams();

        mListPresenter.setLabelId(mLabel, mNormal);
        mListVideoAdapter.updateLabel(mLabel);
        mRecyclerView.scrollToPosition(0);
        mListVideoAdapter.resetSelctedPos(0);
        mListPresenter.onViewResumed();
        mListVideoAdapter.setDts(mIsDts);
        RequestManager.getInstance().onTagVideoListExposureEvent(mLabel);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && isFromPopupWindow) {
            ActivityLauncher.startHomeActivity(this);
            this.finish();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void add(ListAlbumModel model) {
        mListVideoAdapter.add(model);
    }

    @Override
    public void add(List<ListAlbumModel> models) {
        if (mListVideoAdapter.getItemCount() == 0) {
            mListVideoAdapter.add(models);
            if (mListVideoAdapter.getItemCount() == 0) {
                mRecyclerView.setVisibility(View.GONE);
            } else {
                mRecyclerView.setVisibility(View.VISIBLE);
            }
        }
        //this code is used to update the like count when activity onResume
        // change mListPresenter.onViewResumed(); to onResume is Ok
        /*
        else {
            int size = Math.min(mListVideoAdapter.getItemCount(), models.size());
            for (int i = 0; i < size; i++) {
                if (mListVideoAdapter.getItem(i).id == models.get(i).id &&
                        mListVideoAdapter.getItem(i).likeCount != models.get(i).likeCount) {
                    mListVideoAdapter.getItem(i).likeCount = models.get(i).likeCount;
                }
            }

            View view = mRecyclerView.getFocusedChild();
            if (view != null) {
                View likeView = view.findViewById(R.id.like_count);
                if (likeView != null && likeView instanceof TextView) {
                    if (!TextUtils.isEmpty(models.get(getSelectedPos()).likeCount)
                            && !models.get(getSelectedPos()).likeCount.equals("null")) {
                        ((TextView) likeView).setText(new Integer(models.get(getSelectedPos()).likeCount).toString());
                    } else {
                        ((TextView) likeView).setText("0");
                    }
                }
            }
        }
        */
    }

    @Override
    public void showLoading() {
        mLoadingView.setVisibility(View.VISIBLE);
        mErrorView.setVisibility(View.GONE);
        mParentView.setVisibility(View.GONE);
    }

    @Override
    public void hideLoading() {
        mLoadingView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.GONE);
        mParentView.setVisibility(View.VISIBLE);
    }

    @Override
    public void onError() {
        mLoadingView.setVisibility(View.GONE);
        mParentView.setVisibility(View.GONE);
        mErrorView.setVisibility(View.VISIBLE);
    }

    @Override
    public void setListTitle(String title) {
        if (!TextUtils.isEmpty(title)) {
            mTitleView.setText(title);
        } else {
            mTitleView.setText("");
        }
    }

    @Override
    public void setListSubTitle(String subTitle) {
        if (!TextUtils.isEmpty(subTitle)) {
            mSubTitleView.setText(subTitle);
        } else {
            mSubTitleView.setText("");
        }
    }

    @Override
    public void setListCategory(String category) {
        if (!TextUtils.isEmpty(category)) {
            mCategoryView.setText(category);
        } else {
            mCategoryView.setText("");
        }

    }

    @Override
    public void setBackground(String url) {
        if (!isDestroyed()) {  // 在开始加载图片之前检查Activity是否已经被销毁
            mBgView.setImageRes(url, getResources().getDrawable(R.drawable.activity_background),
                    getResources().getDrawable(R.drawable.activity_background), true,
                    new DrawableImageViewTarget(mBgView) {
                        @Override
                        public void onResourceReady(Drawable resource, @Nullable Transition<? super Drawable> transition) {
                            super.onResourceReady(resource, transition);
                            if (resource == null) {
                                mBgView.setAlpha(1f);
                                return;
                            }
                            mBgView.setAlpha(0.3f);
                        }

                        @Override
                        public void onLoadFailed(@Nullable Drawable errorDrawable) {
                            super.onLoadFailed(errorDrawable);
                            mBgView.setAlpha(1f);
                        }
                    });
        }
    }


    public void setVideoDetails(String comments, String title, String subTitle, String details, String scoreSource, String score, String doubanScore) {
        setListSubTitle(comments);

        if (!TextUtils.isEmpty(title)) {
            mVideoTitleView.setText(title);
        } else {
            mVideoTitleView.setText("");
        }

        if (!TextUtils.isEmpty(subTitle)) {
            mVideoSubTitleView.setText(subTitle);
        } else {
            mVideoSubTitleView.setText("");
        }

        if (!TextUtils.isEmpty(details)) {
            mVideoDetailsView.setText(details);
        } else {
            mVideoDetailsView.setText("");
        }
        if (!TextUtils.isEmpty(scoreSource) && scoreSource.equals("1")) {
            mVideoScoreIcon.setVisibility(View.INVISIBLE);
            mVideoScoreView.setText(String.valueOf(score));
        } else if (TextUtils.isEmpty(scoreSource) && String.valueOf(score).equals("0.0")){
            mVideoScoreIcon.setVisibility(View.INVISIBLE);
        } else {
            mVideoScoreIcon.setVisibility(View.VISIBLE);
            mVideoScoreView.setText(doubanScore);
        }

    }

    @Override
    public ListVideoAdapter getAdapter() {
        return mListVideoAdapter;
    }

    @Override
    public int getSelectedPos() {
        return mListVideoAdapter.getSelctedPos();
    }

    @Override
    public void setCount(int count) {
        mListCount = count;
    }

    @Override
    public void showContinue(int continuesly) {
        if (continuesly == 1) {
            mListVideoAdapter.setContinueslyPlay(true);
            mContinuePlayButton.setVisibility(View.VISIBLE);
        } else {
            mListVideoAdapter.setContinueslyPlay(false);
            mContinuePlayButton.setVisibility(View.GONE);
        }
    }

    @Override
    public void onSelectedChanged(int position) {
        int pos = 0;
        if (Util.isSupportTouchVersion(this)) {
            pos = position;
        } else {
            pos = mRecyclerView.getChildAdapterPosition(mRecyclerView.getFocusedChild());
        }
        if (pos == -1) {
            pos = 0;
        }
        updateCountText(pos);
        mListPresenter.onItemSelected(pos);
    }

    private void initParams() {
        Uri uri = getIntent().getData();
        if (uri == null) {
            mLabel = convertStringToLong(getIntent().getStringExtra(ParamConstant.PARAM_LABEL_ID));
            if (mLabel == 0L) {
                mLabel = getIntent().getLongExtra(ParamConstant.PARAM_LABEL_ID, 0L);
            }
            if (mLabel == 0L) {
                mLabel = getIntent().getIntExtra(ParamConstant.PARAM_LABEL_ID, 0);
            }
            mNormal = getIntent().getBooleanExtra(ParamConstant.PARAM_LABEL_NORMAL, true);
            mIsDts = getIntent().getBooleanExtra(ParamConstant.PARAM_IS_DTS, false);
            isFromPopupWindow = getIntent().getBooleanExtra(ParamConstant.PARAM_IS_POPUP_WINDOW, false);
        } else {
            mLabel = convertStringToLong(uri.getQueryParameter(ParamConstant.PARAM_LABEL_ID));
            if (!TextUtils.isEmpty(uri.getQueryParameter(ParamConstant.PARAM_LABEL_NORMAL))) {
                mNormal = Util.convertStringToBoolean(uri.getQueryParameter(ParamConstant.PARAM_LABEL_NORMAL));
            } else {
                mNormal = true;
            }
            mIsDts = Util.convertStringToBoolean(uri.getQueryParameter(ParamConstant.PARAM_IS_DTS));
            isFromPopupWindow = Util.convertStringToBoolean(uri.getQueryParameter(ParamConstant.PARAM_IS_POPUP_WINDOW));
        }
    }

    private long convertStringToLong(String value) {
        if (!TextUtils.isEmpty(value)) {
            try {
                return Long.valueOf(value);
            } catch (Exception e) {
                LibDeprecatedLogger.e("ListVideoActivity: failed to get labelId when converted from String: " + e.toString());
            }
        }
        return 0L;
    }

    private void updateCountText(int selctedPos) {
        if (mListCount > 0) {
            mIndexView.setText(String.valueOf(selctedPos + 1));
            mCountView.setText("/" + String.valueOf(mListCount));
            mIndexView.setVisibility(View.VISIBLE);
            mCountView.setVisibility(View.VISIBLE);
        } else {
            mIndexView.setVisibility(View.INVISIBLE);
            mCountView.setVisibility(View.INVISIBLE);
        }
    }
}