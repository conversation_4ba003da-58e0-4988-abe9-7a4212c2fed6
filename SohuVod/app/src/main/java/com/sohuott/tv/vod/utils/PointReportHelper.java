package com.sohuott.tv.vod.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Message;

import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.ReportPointResult;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohu.lib_utils.PrefUtil;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohu.lib_utils.StringUtil;

import java.util.Date;

import io.reactivex.observers.ResourceObserver;

/**
 * Created by wenjingbian on 2018/1/8.
 */

public class PointReportHelper {

    public interface PointReportListener {
        void onReportHistory();
    }

    private static final int USER_POINT_VIDEO_TIME = 1;
    private static final int USER_POINT_VIDEO_COUNT = 2;
    private static final int HISTORY_RECORD = 3;
    private static final int VIDEO_TIME_DURATION = 10 * 60 * 1000;
    private static final int VIDEO_COUNT_DURATION = 6 * 60 * 1000;
    private static final int VIDEO_HISTORY_DURATION = 5 * 60 * 1000;

    private static volatile PointReportHelper mPointReportHelper = null;
    private PointReportHandler mTimeHandler, mCountHandler, mHistoryHandler;
    private PointReportListener mListener;

    private long mStartForTime, mStartForCount, mStartForHistory;
    private long mVideoTime, mVideoCount, mVideoHistory;
    private String mPassport;
    private int mVideoTimeInterval;
    private int mVideoHistoryDuration;

    private PointReportHelper(Context context) {
        String timeInterval = PrefUtil.getString( "config", "time_interval", String.valueOf(VIDEO_TIME_DURATION));
        if (StringUtil.isEmpty(timeInterval) && !Util.isNumber(timeInterval)) {
            mVideoTimeInterval = VIDEO_TIME_DURATION;
        } else {
            mVideoTimeInterval = Integer.valueOf(timeInterval) * 60 * 1000;
        }

        mVideoHistoryDuration = Util.getHistoryBroadcastDuration(context);
        mVideoHistoryDuration = mVideoHistoryDuration != 0 ? mVideoHistoryDuration : VIDEO_HISTORY_DURATION;
    }

    public static PointReportHelper getInstance(Context context) {
        if (mPointReportHelper == null) {
            synchronized (PointReportHelper.class) {
                if (mPointReportHelper == null) {
                    mPointReportHelper = new PointReportHelper(context);
                }
            }
        }
        return mPointReportHelper;
    }

    public void setReportListener(PointReportListener listener) {
        this.mListener = listener;
    }

    public void reportVideoTime(String passport) {
        if (StringUtil.isEmpty(passport)) {
            LibDeprecatedLogger.e("[USER_POINT] Failed to report video time, passport is null or its length is 0.");
            return;
        }

        if (mStartForTime != 0) {
            LibDeprecatedLogger.e("[USER_POINT] Failed to report video time, mStartTime != 0");
            return;
        }

        LibDeprecatedLogger.d("[USER_POINT] Start timer for video time: " + System.currentTimeMillis());

        this.mPassport = passport;
        if (mTimeHandler == null) {
            mTimeHandler = new PointReportHandler();
        }
        mTimeHandler.sendEmptyMessageDelayed(USER_POINT_VIDEO_TIME, mVideoTimeInterval - mVideoTime);
        mStartForTime = System.currentTimeMillis();
    }

    public void reportVideoCount(String passport) {
        if (StringUtil.isEmpty(passport)) {
            LibDeprecatedLogger.e("[USER_POINT] Failed to report video count, passport is null or its length is 0.");
            return;
        }

        if (mStartForCount != 0) {
            LibDeprecatedLogger.e("[USER_POINT] Failed to report video count, mStartCount != 0");
            return;
        }

        LibDeprecatedLogger.d("[USER_POINT] Start timer for video count: " + System.currentTimeMillis());

        this.mPassport = passport;
        if (mCountHandler == null) {
            mCountHandler = new PointReportHandler();
        }
        mCountHandler.sendEmptyMessageDelayed(USER_POINT_VIDEO_COUNT, VIDEO_COUNT_DURATION - mVideoCount);
        mStartForCount = System.currentTimeMillis();
    }

    public void reportVideoHistory() {
        LibDeprecatedLogger.d("[USER_HISTORY] Start timer for report history:" + System.currentTimeMillis());
        if (mHistoryHandler == null) {
            mHistoryHandler = new PointReportHandler();
        }
        if (mListener != null) {
            LibDeprecatedLogger.d("[USER_POINT_HISTORY] onReportHistory()");
            mListener.onReportHistory();
        }else{
            LibDeprecatedLogger.e("[USER_POINT_HISTORY] Listener for calling onReportHistory() is null.");
        }
        mHistoryHandler.removeCallbacksAndMessages(HISTORY_RECORD);
        mHistoryHandler.sendEmptyMessageDelayed(HISTORY_RECORD, mVideoHistoryDuration - mVideoHistory);
        mStartForHistory = System.currentTimeMillis();
    }

    public void cancelReportTime() {
        if (mStartForTime != 0) {
            computeDuration(mStartForTime, 1);
            mStartForTime = 0;
        }

        LibDeprecatedLogger.d("[USER_POINT]cancelVideoTime()");
        if (mTimeHandler != null) {
            mTimeHandler.removeCallbacksAndMessages(null);
        }
    }

    public void cancelReportCount(boolean isRemoveLastRecord) {
        if (mStartForCount != 0) {
            if (isRemoveLastRecord) {
                mVideoCount = 0;
            } else {
                computeDuration(mStartForCount, 2);
            }
            mStartForCount = 0;
        } else {
            return;
        }

        LibDeprecatedLogger.d("[USER_POINT]cancelVideoCount()");
        if (mCountHandler != null) {
            mCountHandler.removeCallbacksAndMessages(null);
        }
    }

    public void cancelReportHistory(boolean isRemoveTimerRecord) {
        if (mStartForHistory != 0) {
            if (isRemoveTimerRecord) {
                mVideoHistory = 0;
            } else {
                computeDuration(mStartForHistory, 3);
            }
            mStartForHistory = 0;
        }

        LibDeprecatedLogger.d("[USER_POINT_HISTORY]cancelReportHistory()");
        if (mHistoryHandler != null) {
            mHistoryHandler.removeCallbacksAndMessages(null);
        }
    }

    public long getVideoCount() {
        return mVideoCount;
    }


    private void reportVideoTime() {
        LibDeprecatedLogger.d("[USER_POINT]reportVideoTime(): " + System.currentTimeMillis());
        mVideoTime = 0;
        mStartForTime = 0;
        mTimeHandler.sendEmptyMessageDelayed(USER_POINT_VIDEO_TIME, mVideoTimeInterval);

        String time = String.valueOf(new Date().getTime());
        String ts = SecurityUtil.encrypt(time);
        String param = SecurityUtil.encrypt("taskId=" + Constant.USER_POINT_VIDEO_TIME + "&passport="
                + mPassport, time);
        NetworkApi.reportUserPoint(ts, param, new ResourceObserver<ReportPointResult>() {
            @Override
            public void onNext(ReportPointResult value) {
                if (value == null) {
                    LibDeprecatedLogger.e("[USER_POINT] Failed to report video time: returned value is null.");
                } else {
                    LibDeprecatedLogger.d("[USER_POINT] onNext(): " + value.getMessage());
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("[USER_POINT] Failed to report video time: " + e.getMessage());
            }

            @Override
            public void onComplete() {

            }
        });
    }

    private void reportVideoCount() {
        LibDeprecatedLogger.d("[USER_POINT]reportVideoCount(): " + System.currentTimeMillis());
        mVideoCount = 0;
        mStartForCount = 0;

        String time = String.valueOf(new Date().getTime());
        String ts = SecurityUtil.encrypt(time);
        String param = SecurityUtil.encrypt("taskId=" + Constant.USER_POINT_VIDEO_COUNT + "&passport="
                + mPassport, time);
        NetworkApi.reportUserPoint(ts, param, new ResourceObserver<ReportPointResult>() {
            @Override
            public void onNext(ReportPointResult value) {
                if (value == null) {
                    LibDeprecatedLogger.e("[USER_POINT] Failed to report video count: returned value is null.");
                } else {
                    LibDeprecatedLogger.d("[USER_POINT] onNext(): " + value.getMessage());
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("[USER_POINT] Failed to report video count: " + e.getMessage());
            }

            @Override
            public void onComplete() {

            }
        });
    }

    private void reportHistoryRecord() {
        LibDeprecatedLogger.d("[USER_POINT_HISTORY]reportHistoryRecord(): " + System.currentTimeMillis());
        mVideoHistory = 0;
        mStartForHistory = 0;
        mHistoryHandler.removeCallbacksAndMessages(HISTORY_RECORD);
        mHistoryHandler.sendEmptyMessageDelayed(HISTORY_RECORD, mVideoHistoryDuration);

        if (mListener != null) {
            LibDeprecatedLogger.d("[USER_POINT_HISTORY] onReportHistory()");
            mListener.onReportHistory();
        }else{
            LibDeprecatedLogger.e("[USER_POINT_HISTORY] Listener for calling onReportHistory() is null.");
        }
    }

    private void computeDuration(long startTime, int tag) {
        long duration = System.currentTimeMillis() - startTime;
        if (tag == 1 && duration < mVideoTimeInterval) {
            mVideoTime += duration;
        } else if (tag == 2 && duration < VIDEO_COUNT_DURATION) {
            mVideoCount += duration;
        } else if (tag == 3 && duration < mVideoHistoryDuration) {
            mVideoHistory += duration;
        }
    }

    private class PointReportHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case USER_POINT_VIDEO_TIME:
                    reportVideoTime();
                    LibDeprecatedLogger.d("[USER_POINT]Time's up! Report time");
                    break;
                case USER_POINT_VIDEO_COUNT:
                    LibDeprecatedLogger.d("[USER_POINT]Time's up! Report count");
                    reportVideoCount();
                    break;
                case HISTORY_RECORD:
                    LibDeprecatedLogger.d("[USER_POINT_HISTORY]Time's up! Report history");
                    reportHistoryRecord();
                    break;
                default:
                    break;
            }
        }
    }
}
