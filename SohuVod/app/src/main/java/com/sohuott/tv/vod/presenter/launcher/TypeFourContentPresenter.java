package com.sohuott.tv.vod.presenter.launcher;

import android.content.Context;
import android.graphics.Color;
import androidx.leanback.widget.Presenter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.widget.lb.ImgConstraintLayout;


public class TypeFourContentPresenter extends Presenter {
    private Context mContext;

    @Override
    public Presenter.ViewHolder onCreateViewHolder(ViewGroup parent) {
        if (mContext == null) {
            mContext = parent.getContext();
        }
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_type_four_layout, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(Presenter.ViewHolder viewHolder, Object item) {
        if (item instanceof ContentGroup.DataBean.ContentsBean) {
            ViewHolder vh = (ViewHolder) viewHolder;
            ContentGroup.DataBean.ContentsBean contentsBean = (ContentGroup.DataBean.ContentsBean) item;

            if (contentsBean.channelType == Constant.TYPE_VIP) {
                vh.mIvTypeFourName.setTextColor(Color.parseColor("#DEBB99"));
                vh.mTypeFourFocus.setBackgroundResource(R.drawable.bg_vip_focus_selector);
                vh.mCsTypeFourRoot.setBackgroundResource(R.drawable.bg_vip_item_type_four);
            }

            vh.mIvTypeFourName.setText(contentsBean.name);


            if (contentsBean.memoInfo == null) {
                switch (Integer.parseInt(contentsBean.type)) {
                    case HomeRecommendBean.COURSES_HISTORY_TYPE:
                        RequestManager.getInstance().onAllEvent(new EventInfo(10196, "imp"),
                                contentsBean.pathInfo, null, null);
                        break;
                    case HomeRecommendBean.COURSES_ORDER_TYPE:
                        RequestManager.getInstance().onAllEvent(new EventInfo(10198, "imp"),
                                contentsBean.pathInfo, null, null);
                        break;
                    case HomeRecommendBean.COURSES_FAVORITE_TYPE:
                        RequestManager.getInstance().onAllEvent(new EventInfo(10197, "imp"),
                                contentsBean.pathInfo, null, null);
                        break;
                }
            } else {
                RequestManager.getInstance().onAllEvent(new EventInfo(10159, "imp"),
                        contentsBean.pathInfo, null, contentsBean.memoInfo);
            }

        }
    }

    @Override
    public void onUnbindViewHolder(Presenter.ViewHolder viewHolder) {

    }

    public static class ViewHolder extends Presenter.ViewHolder {

        private final TextView mIvTypeFourName;
        private View mTypeFourFocus;
        private ImgConstraintLayout mCsTypeFourRoot;

        public ViewHolder(View view) {
            super(view);
            mIvTypeFourName = (TextView) view.findViewById(R.id.tv_type_four_name);
            mTypeFourFocus = view.findViewById(R.id.tv_type_four_focus);
            mCsTypeFourRoot = (ImgConstraintLayout) view;
        }
    }
}

