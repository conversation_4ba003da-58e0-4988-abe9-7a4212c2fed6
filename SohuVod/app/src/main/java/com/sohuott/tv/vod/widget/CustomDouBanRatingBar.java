package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.sohuott.tv.vod.R;

import java.util.ArrayList;

/**
 * Created by rita on 17-6-8.
 */

public class CustomDouBanRatingBar extends LinearLayout {
    private float mRatingScore = 0;
    ArrayList<ImageView> starts;

    public CustomDouBanRatingBar(Context context) {
        super(context);
        initView(context);
    }

    public CustomDouBanRatingBar(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public CustomDouBanRatingBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.custom_douban_rating_bar, this, true);
        starts = new ArrayList<>();
        starts.add((ImageView) findViewById(R.id.start1));
        starts.add((ImageView) findViewById(R.id.start2));
        starts.add((ImageView) findViewById(R.id.start3));
        starts.add((ImageView) findViewById(R.id.start4));
        starts.add((ImageView) findViewById(R.id.start5));
    }

    public void setRatingScore(float ratingScore) {
        if (ratingScore < 0) {
            ratingScore = 0;
        } else if (ratingScore > 10) {
            ratingScore = 10;
        }
        mRatingScore = ratingScore;
        float starCount = mRatingScore / 10 * 5;
        for (int i = 0; i < Math.floor(starCount); i++) {
            starts.get(i).setImageResource(R.drawable.star_selected);
        }
        if (starCount == Math.floor(starCount) || starCount - Math.floor(starCount) < 0.25) {
            for (int i = (int) (Math.floor(starCount)); i < 5; i++) {
                starts.get(i).setImageResource(R.drawable.star_unselected);
            }
        } else if (starCount - Math.floor(starCount) >= 0.25 && starCount - Math.floor(starCount) < 0.75) {
            starts.get((int) (Math.floor(starCount))).setImageResource(R.drawable.star_half_selected);
            for (int i = (int) (Math.floor(starCount) + 1); i < 5; i++) {
                starts.get(i).setImageResource(R.drawable.star_unselected);
            }
        } else {
            starts.get((int) (Math.floor(starCount))).setImageResource(R.drawable.star_selected);
            for (int i = (int) (Math.floor(starCount) + 1); i < 5; i++) {
                starts.get(i).setImageResource(R.drawable.star_unselected);
            }
        }
    }


}
