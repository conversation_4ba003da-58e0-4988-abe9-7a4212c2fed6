package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.AllLabel;
import com.sohuott.tv.vod.ui.ScaleFocusChangeListener;
import com.sohuott.tv.vod.widget.GlideImageView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by fenglei on 16-1-19.
 */
public class AllLabelAdapter extends RecyclerView.Adapter<AllLabelAdapter.ViewHolder> {

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

        private TextView labelTitleTV;
        private TextView labelAgreeNumTV;
        private GlideImageView labelIV;
        private TextView labelNumTV;
        private List<GlideImageView> imageViewList;
        private List<TextView> textViewList;

        public ViewHolder(View v) {
            super(v);
            v.setOnFocusChangeListener(new ScaleFocusChangeListener());
            labelTitleTV = (TextView)v.findViewById(R.id.tv_label_title);
            labelAgreeNumTV = (TextView)v.findViewById(R.id.tv_label_agree_num);
            labelIV = (GlideImageView)v.findViewById(R.id.iv_label);
            labelNumTV = (TextView)v.findViewById(R.id.tv_label_num);
            imageViewList = new ArrayList<>();
            imageViewList.add((GlideImageView)v.findViewById(R.id.iv_lable1));
            imageViewList.add((GlideImageView)v.findViewById(R.id.iv_lable2));
            imageViewList.add((GlideImageView)v.findViewById(R.id.iv_lable3));
            textViewList = new ArrayList<>();
            textViewList.add((TextView)v.findViewById(R.id.tv_title1));
            textViewList.add((TextView)v.findViewById(R.id.tv_title2));
            textViewList.add((TextView)v.findViewById(R.id.tv_title3));
        }

        @Override
        public void onClick(View v) {
            if(v.equals(itemView)) {

            }
        }
    }

    private Context mContext;
    private List<AllLabel.LabelItem> mLabelList;

    public AllLabelAdapter(Context context, List<AllLabel.LabelItem> labelList) {
        mContext = context;
        mLabelList = labelList;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(
                R.layout.frgment_home_all_label_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        AllLabel.LabelItem labelItem = mLabelList.get(position);
        if(labelItem != null) {
            holder.labelTitleTV.setText(labelItem.name);
            holder.labelAgreeNumTV.setText(labelItem.likeCount);
            holder.labelIV.setImageRes(labelItem.smallPicUrl);
            holder.labelNumTV.setText(String.valueOf(labelItem.totalCount));
            if(labelItem.albumList != null) {
                int size = Math.min(3, labelItem.albumList.size());
                for(int i = 0; i < size; i++) {
                    AllLabel.LabelItemDetail labelItemDetail = labelItem.albumList.get(i);
                    if(labelItemDetail != null) {
                        holder.imageViewList.get(i).setImageRes(labelItemDetail.tvHorSmallPic);
                        holder.textViewList.get(i).setText(labelItemDetail.tvName);
                    }
                }
            }
        }
    }

    @Override
    public int getItemCount() {
        if(mLabelList == null || mLabelList.size() == 0) {
            return 0;
        }
        return mLabelList.size();
    }

}
