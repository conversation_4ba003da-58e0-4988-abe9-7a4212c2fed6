package com.sohuott.tv.vod.view;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by XiyingCao on 16-1-11.
 */
public class BaseItemDecoration extends RecyclerView.ItemDecoration {
    private int space;

    public BaseItemDecoration(int space) {
        this.space = space;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {

        if (parent.getChildPosition(view) != 0) {
            outRect.left += space;
        }
    }
}
