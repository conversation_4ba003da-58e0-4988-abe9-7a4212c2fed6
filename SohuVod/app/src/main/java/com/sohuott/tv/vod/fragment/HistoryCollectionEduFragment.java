package com.sohuott.tv.vod.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.activity.ListEduUserRelatedActivity;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.adapter.ListEduRecordAdapter;
import com.sohuott.tv.vod.lib.db.greendao.Collection;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.LoadingView;
import com.sohuott.tv.vod.lib.base.BaseFragment;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.BookedRecord;
import com.lib_statistical.model.EventInfo;
import com.sohuott.tv.vod.lib.model.VideoDetailRecommend;
import com.sohuott.tv.vod.lib.model.VideoFavorListBean;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.model.HistoryEvent;
import com.sohuott.tv.vod.presenter.HfcRecordViewPresenterImpl;
import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.ClearDialog;
import com.sohuott.tv.vod.view.CustomGridLayoutManager;
import com.sohuott.tv.vod.view.CustomRecyclerView;
import com.sohuott.tv.vod.view.FocusBorderView;
import com.sohuott.tv.vod.view.HfcRecordView;
import com.sohuott.tv.vod.widget.EduEmptyView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;

import static com.sohuott.tv.vod.activity.ListEduUserRelatedActivity.LIST_INDEX_COLLECTION;
import static com.sohuott.tv.vod.activity.ListEduUserRelatedActivity.LIST_INDEX_HISTORY;
import static com.sohuott.tv.vod.activity.ListUserRelatedActivity.LIST_INDEX_BOOKED;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class HistoryCollectionEduFragment extends BaseFragment
        implements ListEduRecordAdapter.FocusController, HfcRecordView {

    //String values to identify the entry when requested personal recommended list
    private static final String TRACK_ENTRY_HISTORY = "4";
    private static final String TRACK_ENTRY_COLLECTION = "5";

    public static final int SPAN_COUNT = 4;

    private LoadingView mChildLoadingView;
    private LinearLayout mRightErrorView;
    private EduEmptyView mEmptyView;
    private TextView tv_hfc_filter;
    private CustomRecyclerView mRecordRecyclerView;
    private ClearDialog mClearDialog;
    private FocusBorderView mFocusBorderView;

    private ListEduUserRelatedActivity mActivity;

    private ListEduRecordAdapter mRecordAdapter;

    private CustomGridLayoutManager mRecordLayoutManager;

    //Cached record lists
    private List<PlayHistory> mHistoryList;
    private List<Collection> mCollectionList;

    private HfcRecordViewPresenterImpl mPresenterImpl;
    private LoginUserInformationHelper mHelper;

    private EventBus mEventBus;
    private boolean isUpdateHistoryData;
    private boolean isDeleteItem;
    private boolean isRequestFromCloud = true;
    private boolean isFirstEnter = true;
    private HistoryEvent mHistoryEvent;
    private PlayHistory mNewPlayHistory;
    private String mSubjectId;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View listEduView = inflater.inflate(R.layout.fragment_edu_list, container, false);
        mActivity = (ListEduUserRelatedActivity) getActivity();
        mHelper = LoginUserInformationHelper.getHelper(getContext());
        initView(listEduView);
        mPresenterImpl = new HfcRecordViewPresenterImpl(getContext());
        mPresenterImpl.setHfcRecordView(this);
        changeTab(mActivity.getLeftSelectedTag());

        mEventBus = EventBus.getDefault();
        mEventBus.register(this);
        return listEduView;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mActivity != null && mActivity.getLeftSelectedTag() != ListEduUserRelatedActivity.LIST_INDEX_HISTORY) {
            return;
        }
        if (isUpdateHistoryData) {
            showRecordLoadingView();
            View currFocusView = mActivity.getCurrentFocus();
            if (currFocusView != null && currFocusView.getTag() != null && currFocusView.getTag() instanceof Integer
                    && (int) currFocusView.getTag() == LIST_INDEX_HISTORY) {
                mPresenterImpl.requestHistoryList(true, false);
            }
            mActivity.focusLeftItem(LIST_INDEX_HISTORY);
            mPresenterImpl.requestLocalHistoryById(mHistoryEvent.getDataType(), mHistoryEvent.getId())
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<PlayHistory>() {
                        @Override
                        public void accept(PlayHistory playHistory) throws Exception {
                            mNewPlayHistory = playHistory;
                        }
                    });
            return;
        }
        if (mRecordAdapter != null && mHistoryList != null && mHistoryList.size() > 0) {
            int selectPos = -1;
            mNewPlayHistory = getSelectedPlayHistory();
            if (mNewPlayHistory != null) {
                selectPos = mHistoryList.indexOf(mNewPlayHistory);
                if (selectPos >= 0 && selectPos < mHistoryList.size()) {
                    int finalSelectPos = selectPos;
                    mPresenterImpl.requestLocalHistoryById(mNewPlayHistory.getDataType(),
                            mNewPlayHistory.getDataType() == 0 ? mNewPlayHistory.getAlbumId() : mNewPlayHistory.getVideoId())
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Consumer<PlayHistory>() {
                                @Override
                                public void accept(PlayHistory playHistory) throws Exception {
                                    mHistoryList.set(finalSelectPos, playHistory);
                                    mRecordAdapter.notifyItemChanged(finalSelectPos);
                                }
                            });

                }
            }

        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mHelper = null;
        mActivity = null;
        if (mCollectionList != null) {
            mCollectionList.clear();
            mCollectionList = null;
        }
        if (mHistoryList != null) {
            mHistoryList.clear();
            mHistoryList = null;
        }
        if (mRecordAdapter != null) {
            mRecordAdapter.releaseAll();
            mRecordAdapter = null;
        }
        mEventBus.unregister(this);
    }

    @Override
    public void onFocusSelected(int position) {
        //onFocusSelected() in HistoryCollectionActivity
        if (mActivity != null) {
            mActivity.focusLeftItem(position);
        }
    }

    @Override
    public void showChildErrorView(int tag) {
        //Update right error view.
        if (mActivity == null || mActivity.getLeftSelectedTag() == -1) {
            return;
        }

        if (tag == mActivity.getLeftSelectedTag()) {
            mRightErrorView.setVisibility(View.VISIBLE);
            mChildLoadingView.setVisibility(View.GONE);
        }
    }

    @Override
    public void showParentLoadingView() {
        if (mActivity != null) {
            mActivity.showParentLoadingView();
        }
    }

    @Override
    public void hideParentLoadingView() {
        if (mActivity != null) {
            mActivity.hideParentLoadingView();
        }
    }

    @Override
    public void showEmptyView() {
        if (mActivity != null) {
            displayEmptyView(LIST_INDEX_HISTORY);
        }
    }

    @Override
    public void updateHistoryRecordView(int id) {
        if (mRecordAdapter != null) {
            mRecordAdapter.updateHistoryRecordView(id);
        }
    }

    @Override
    public void updateHistoryRecordFailedView(int id) {
        if (mRecordAdapter != null) {
            mRecordAdapter.updateHistoryRecordFailedView(id);
        }
    }

    @Override
    public void updateCollectionRecordView(int id) {
        if (mRecordAdapter != null) {
            mRecordAdapter.updateCollectionRecordView(id);
        }
    }

    @Override
    public void updateCollectionRecordFailedView(int id) {
        if (mRecordAdapter != null) {
            mRecordAdapter.updateCollectionRecordFailedView(id);
        }
    }

    @Override
    public void updateHistoryItemView(PlayHistory playHistory) {
        if (mHistoryList.contains(mRecordAdapter.getSelectedHistory())) {
            int index = mHistoryList.indexOf(mRecordAdapter.getSelectedHistory());
            mHistoryList.set(index, playHistory);
            mRecordAdapter.notifyItemChanged(index);
        }
    }

    @Override
    public void updatePersonalRecommendView(String trackEntry, List<VideoDetailRecommend.DataEntity> personalRecommendList) {
        if (mActivity == null) {
            return;
        }
        //Update data of mPromoteRecycler
        int tag = mActivity.getLeftSelectedTag();
        if ((tag == LIST_INDEX_HISTORY && trackEntry.equals(TRACK_ENTRY_HISTORY))
                || (tag == LIST_INDEX_COLLECTION && trackEntry.equals(TRACK_ENTRY_COLLECTION))) {
//            mEmptyView.setListView(personalRecommendList);
            LibDeprecatedLogger.d("This fragment is " + tag);
        }
    }

    @Override
    public void updateHistoryRecordView(List<PlayHistory> historyList, boolean onlyVRS) {
        if (mActivity == null) {
            return;
        }
        mHistoryList = historyList;
        if (mActivity.getLeftSelectedTag() == LIST_INDEX_HISTORY) {
            if (historyList != null && historyList.size() > 0) {
                LibDeprecatedLogger.d("There are records, display record view.");
                displayRecordView(LIST_INDEX_HISTORY);
            } else {
                LibDeprecatedLogger.d("No record, displaying empty view.");
                displayEmptyView(LIST_INDEX_HISTORY);
            }
        }

        if (isRequestFromCloud) {
            isRequestFromCloud = false;
        }
    }

    @Override
    public void onAddMoreHistoryRecord(List<PlayHistory> historyList, boolean onlyVRS) {
    }

    @Override
    public void updateCollectionRecordView(List<Collection> collectionList) {
        if (mActivity == null) {
            return;
        }

        mCollectionList = collectionList;
        if (mActivity.getLeftSelectedTag() == LIST_INDEX_COLLECTION) {
            if (collectionList != null && collectionList.size() > 0) {
                LibDeprecatedLogger.d("There are records, display record view.");
                displayRecordView(LIST_INDEX_COLLECTION);
            } else {
                LibDeprecatedLogger.d("No record, displaying empty view.");
                displayEmptyView(LIST_INDEX_COLLECTION);
            }
        }
    }

    @Override
    public void onAddMoreCollectionRecord(List<Collection> collectionList) {
    }

    @Override
    public void updateFavorRecordView(List<VideoFavorListBean.DataEntity.ResultEntity> favorList) {

    }

    @Override
    public void updateBookedRecordView(List<BookedRecord.DataBean> dataList) {

    }

    @Override
    public void updateBookedRecordView(String id) {

    }

    @Override
    public void updateBookedRecordFailedView(String id) {

    }

    @Override
    public void setSubjectIdInfo(String subjectId) {
        this.mSubjectId = subjectId;
    }

    @Subscribe
    public void onEventMainThread(HistoryEvent event) {
        LibDeprecatedLogger.d("Got HistoryEvent!");
        if (event != null && mRecordAdapter != null && mRecordAdapter.getLeftTag() == LIST_INDEX_HISTORY) {
            isUpdateHistoryData = true;
            mHistoryEvent = event;
        }
    }

    public void changeTab(int tag) {
        String subPageName = null;

        showRecordLoadingView();
        if (mRecordAdapter != null) {
            mRecordAdapter.isShowDeleteView(false);
        }
        switch (tag) {
            case LIST_INDEX_HISTORY:
                mPresenterImpl.requestHistoryList(true, false);
                RequestManager.getInstance().onHistoryListExposureEvent();
                subPageName = "6_list_history";
                historyExposureEvent();
                break;
            case LIST_INDEX_COLLECTION:
                mPresenterImpl.requestCollectionList(true);
                RequestManager.getInstance().onCollectionListExposureEvent();
                subPageName = "6_list_collection";
                collectionExposureEvent();
                break;
            default:
                subPageName = "";
                break;
        }
        if (isFirstEnter) {
            setSubPageName(subPageName);
        } else {
            recordSubPageEnd();
            recordSubPageStart(subPageName);
        }
        isFirstEnter = false;
    }

    private void collectionExposureEvent() {
        HashMap<String, String> path = new HashMap<>(1);
        path.put("pageId", "1017");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), path, null, null);
    }

    private void historyExposureEvent() {
        HashMap<String, String> path = new HashMap<>(1);
        path.put("pageId", "1016");
        RequestManager.getInstance().onAllEvent(new EventInfo(10135, "imp"), path, null, null);
    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        this.mFocusBorderView = focusBorderView;
    }

    public PlayHistory getSelectedPlayHistory() {
        if (mRecordAdapter != null) {
            return mRecordAdapter.getSelectedHistory();
        }
        return null;
    }

    public void resetCachedListData() {
        if (mCollectionList != null) {
            mCollectionList.clear();
            mCollectionList = null;
        }
    }

    public void isDeleteItem(boolean isDeleteItem) {
        this.isDeleteItem = isDeleteItem;
    }

    /**
     * Show loading icon on record view
     */
    private void showRecordLoadingView() {
        if (mActivity == null) {
            return;
        }

        //Visible loading view
        if (mChildLoadingView != null) {
            mChildLoadingView.setVisibility(View.VISIBLE);
        }
        //invisible error view
        if (mRightErrorView != null) {
            mRightErrorView.setVisibility(View.GONE);
        }
        //invisible empty view
        if (mEmptyView != null) {
            mEmptyView.setVisibility(View.GONE);
        }
        //invisible record view
        if (tv_hfc_filter != null) {
            tv_hfc_filter.setVisibility(View.GONE);
        }
        if (mRecordRecyclerView != null) {
            mRecordRecyclerView.setVisibility(View.GONE);
        }
    }

    public void focusChildViewAtPos() {
        if (mActivity == null || mActivity.getLeftSelectedTag() < 0) {
            return;
        }

        int selectedItemIndex = mActivity.getLeftSelectedTag();
        if ((selectedItemIndex == LIST_INDEX_HISTORY && mHistoryList != null && mHistoryList.size() > 0)
                || (selectedItemIndex == LIST_INDEX_COLLECTION && mCollectionList != null && mCollectionList.size() > 0)) {
            moveToFirstPosition();
        } else if (selectedItemIndex == LIST_INDEX_HISTORY && (mHistoryList == null || mHistoryList.size() <= 0)
                || selectedItemIndex == LIST_INDEX_COLLECTION && (mCollectionList == null || mCollectionList.size() <= 0)) {
            if (mEmptyView.getVisibility() == View.VISIBLE) {
                mEmptyView.focusAtPos();
            }
        }
    }

    public void showDeleteDialog() {
        if (tv_hfc_filter == null) {
            return;
        }
        if (tv_hfc_filter.getVisibility() == View.VISIBLE) {
            if (mRecordAdapter != null) {
                if (mRecordAdapter.getLeftTag() == ListUserRelatedActivity.LIST_INDEX_HISTORY
                        || mRecordAdapter.getLeftTag() == ListUserRelatedActivity.LIST_INDEX_COLLECTION
                        || mRecordAdapter.getLeftTag() == LIST_INDEX_BOOKED) {
                    if (mRecordRecyclerView != null) {
                        int count = mRecordRecyclerView.getChildCount();
                        if (count > 0) {
                            mClearDialog = new ClearDialog(getContext());
                            mClearDialog.show();
                            mClearDialog.setClearAllListener(new OnDialogDismissListener(true, false));
                            mClearDialog.setClearItemListener(new OnDialogDismissListener(false, false));
                        }
                    }
                }
            }
        }
    }

    /**
     * Update item view of RecordRecyclerView, add or remove delete view
     *
     * @param isShowDeleteView Show delete view or not
     */
    public boolean updateRecordItemView(boolean isShowDeleteView) {
        if (tv_hfc_filter == null) {
            return false;
        }
        if (mRecordAdapter != null) {
            mRecordAdapter.isShowDeleteView(isShowDeleteView);
        }

        if (tv_hfc_filter.getVisibility() == View.VISIBLE) {
            if (mRecordAdapter != null) {
                if (mRecordAdapter.getLeftTag() == LIST_INDEX_HISTORY
                        || mRecordAdapter.getLeftTag() == LIST_INDEX_COLLECTION) {
                    if (mRecordRecyclerView != null) {
                        if (isShowDeleteView) {
                            int count = mRecordRecyclerView.getChildCount();
                            if (count > 0) {
                                mClearDialog = new ClearDialog(getContext());
                                mClearDialog.show();
                                mClearDialog.setClearAllListener(new OnDialogDismissListener(true, false));
                                mClearDialog.setClearItemListener(new OnDialogDismissListener(false, false));
                                return true;
                            }
                        } else {
                            isDeleteItem = false;
                            if (updateDeleteView(false)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * Update child view of mRecordRecyclerView when deleted item or canceled to delete item
     *
     * @param isShowDeleteView boolean value to identify visibility of delete icon
     * @return whether block KeyEvent or not
     */
    public boolean updateDeleteView(boolean isShowDeleteView) {
        boolean isBlockKeyEvent = false;
        if (mRecordRecyclerView != null && mRecordAdapter != null) {
            ImageView imageView;
            TextView textView;
            int first = mRecordLayoutManager.findFirstVisibleItemPosition();
            int last = mRecordLayoutManager.findLastVisibleItemPosition();
            if (last < mRecordAdapter.getItemCount() - 1) {
                mRecordAdapter.notifyItemRangeChanged(last + 1, mRecordAdapter.getItemCount() - last);
            }
            if (first > 0) {
                mRecordAdapter.notifyItemRangeChanged(0, first);
            }
            for (int i = first; i <= last; i++) {
                RecyclerView.ViewHolder viewHolder = mRecordRecyclerView.findViewHolderForAdapterPosition(i);
                if (viewHolder == null || viewHolder.itemView == null) {
                    continue;
                }
                imageView = (ImageView) viewHolder.itemView.findViewById(R.id.iv_delete_item);
                textView = (TextView) viewHolder.itemView.findViewById(R.id.tv_item_hfc_title);
                if (imageView == null || textView == null) {
                    continue;
                }
                mRecordAdapter.isShowDeleteView(isShowDeleteView);
                if (isShowDeleteView) { //if show delete icon
                    if (imageView.getVisibility() != View.VISIBLE) {
                        imageView.setVisibility(View.VISIBLE);
                        isBlockKeyEvent = true;
                        if (mActivity != null && i == 0) {
                            if (mActivity.getLeftSelectedTag() == LIST_INDEX_HISTORY) {
                                RequestManager.getInstance().onHistoryDeleteExposureEvent();
                            } else if (mActivity.getLeftSelectedTag() == LIST_INDEX_COLLECTION) {
                                RequestManager.getInstance().onCollectionCancelExposureEvent();
                            }
                        }
                    } else {
                        isBlockKeyEvent = false;
                        break;
                    }
                } else {
                    if (imageView.getVisibility() == View.VISIBLE) {
                        imageView.setVisibility(View.GONE);
                        isBlockKeyEvent = true;
                    } else {
                        isBlockKeyEvent = false;
                        break;
                    }
                }
            }
        }
        return isBlockKeyEvent;
    }

    /**
     * Display empty view on the right of screen
     *
     * @param tag tag value of the selected item view
     */
    public void displayEmptyView(int tag) {
        //Hide record view and display empty view
        tv_hfc_filter.setVisibility(View.GONE);
        mRecordRecyclerView.setVisibility(View.GONE);
        mChildLoadingView.setVisibility(View.GONE);
        mEmptyView.setVisibility(View.VISIBLE);
        mEmptyView.setBtnVisibility(!mHelper.getIsLogin());
        if (!mHelper.getIsLogin()) {
            mEmptyView.setBtnText("登 录");
            mEmptyView.setBtnListener(EduEmptyView.TAG_LOGIN);
        }
        mEmptyView.setParentTag(tag);

        switch (tag) {
            case LIST_INDEX_HISTORY:
//                 mPresenterImpl.requestPersonalRecommendList(TRACK_ENTRY_HISTORY);
                break;
            case LIST_INDEX_COLLECTION:
                // mPresenterImpl.requestPersonalRecommendList(TRACK_ENTRY_COLLECTION);
                break;
            default:
                break;
        }
    }

    public HfcRecordViewPresenterImpl getHfcRecordViewPresenterImpl() {
        return mPresenterImpl;
    }

    private void initView(View view) {
        mChildLoadingView = (LoadingView) view.findViewById(R.id.child_loading_view);
        mRightErrorView = (LinearLayout) view.findViewById(R.id.err_view);
        tv_hfc_filter = (TextView) view.findViewById(R.id.tv_hfc_filter);
        mRecordRecyclerView = (CustomRecyclerView) view.findViewById(R.id.crv_hfc_record);
        mRecordRecyclerView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        mRecordRecyclerView.setItemAnimator(null);
        mRecordRecyclerView.setItemViewCacheSize(0);
        mRecordRecyclerView.setPadding(getResources().getDimensionPixelSize(R.dimen.x48), getResources().getDimensionPixelSize(R.dimen.y48),
                0, getResources().getDimensionPixelSize(R.dimen.y24));
        mRecordRecyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.right = getResources().getDimensionPixelSize(R.dimen.x48);
                outRect.bottom = getResources().getDimensionPixelSize(R.dimen.y45);
            }
        });
        mRecordRecyclerView.setChildDrawingOrderCallback(new RecyclerView.ChildDrawingOrderCallback() {
            @Override
            public int onGetChildDrawingOrder(int childCount, int i) {
                int pos = mRecordRecyclerView.indexOfChild(mRecordRecyclerView.getFocusedChild());
                if (pos < 0 || pos >= childCount - 1) {
                    return i;
                }

                if (pos == i) {
                    return i + 1;
                } else if (i == pos + 1) {
                    return i - 1;
                } else {
                    return i;
                }
            }
        });
//        mRecordRecyclerView.setOnScrollListener(new FinishScrollListener());

        mEmptyView = (EduEmptyView) view.findViewById(R.id.layout_edu_empty_view);
        mEmptyView.setFocusBorderView(mFocusBorderView);
        mEmptyView.setFocusController(this);
    }


    /**
     * Request focus of RecordRecyclerView
     */
    private void focusRecordViewAtPos() {
        if (mRecordLayoutManager == null || mRecordRecyclerView == null ||
                mRecordRecyclerView.getVisibility() != View.VISIBLE) {
            return;
        }

        int focusedPosition = mRecordLayoutManager.findFirstVisibleItemPosition();
        LibDeprecatedLogger.d("First visible item position = " + focusedPosition);
        if (mRecordRecyclerView.findViewHolderForAdapterPosition(focusedPosition) != null
                && mRecordRecyclerView.findViewHolderForAdapterPosition(focusedPosition).itemView != null) {
            mRecordRecyclerView.findViewHolderForAdapterPosition(focusedPosition).itemView.requestFocus();
        }
    }

    private void moveToFirstPosition() {
        if (mRecordLayoutManager == null || mRecordRecyclerView == null ||
                mRecordRecyclerView.getVisibility() != View.VISIBLE) {
            return;
        }
        int firstVisibleItemPosition = mRecordLayoutManager.findFirstVisibleItemPosition();
        if (firstVisibleItemPosition == 0) {
            focusRecordViewAtPos();
            return;
        }
        moveToPosition(0);
        mRecordRecyclerView.postDelayed(new InnerRunnable(this), 200);
    }
    private static class InnerRunnable implements Runnable {
        private WeakReference<HistoryCollectionEduFragment> mWrapper;
        InnerRunnable(HistoryCollectionEduFragment historyCollectionEduFragment){
            mWrapper = new WeakReference<>(historyCollectionEduFragment);
        }
        @Override
        public void run() {
            HistoryCollectionEduFragment historyCollectionEduFragment = mWrapper.get();
            if (historyCollectionEduFragment != null){
                historyCollectionEduFragment.focusRecordViewAtPos();
            }
        }
    }

    public void moveToPosition(int position) {
        int firstItem = mRecordRecyclerView.getChildLayoutPosition(mRecordRecyclerView.getChildAt(0));
        int lastItem = mRecordRecyclerView.getChildLayoutPosition(mRecordRecyclerView.getChildAt(mRecordRecyclerView.getChildCount() - 1));
        if (position < firstItem || position > lastItem) {
            mRecordRecyclerView.smoothScrollToPosition(position);
        } else {
            int movePosition = position - firstItem;
            int top = mRecordRecyclerView.getChildAt(movePosition).getTop();
            mRecordRecyclerView.smoothScrollBy(0, top);
        }
    }

    /**
     * Display record view on the right of screen
     *
     * @param id ID of the selected item view
     */
    private void displayRecordView(int id) {
        //Hide empty view and child loading view, display record view
        mChildLoadingView.setVisibility(View.GONE);
        mRightErrorView.setVisibility(View.GONE);
        mEmptyView.setVisibility(View.GONE);
        mRecordRecyclerView.setVisibility(View.VISIBLE);

        //init mRecordRecycleView
        if (mRecordAdapter == null) {
            mRecordLayoutManager = new CustomGridLayoutManager(getContext(), SPAN_COUNT);
            mRecordLayoutManager.setCustomPadding(2 * getResources().getDimensionPixelSize(R.dimen.y120), 2 * getResources().getDimensionPixelSize(R.dimen.y120));
            mRecordRecyclerView.setLayoutManager(mRecordLayoutManager);
            mRecordAdapter = new ListEduRecordAdapter(this, mRecordRecyclerView);
            mRecordAdapter.setFocusBorderView(mFocusBorderView);
            mRecordRecyclerView.setAdapter(mRecordAdapter);
            mRecordAdapter.setOnFocusSelected(this);
        }
        mRecordAdapter.setLeftTag(id);
        mRecordAdapter.notifyItemRangeRemoved(0, mRecordAdapter.getItemCount());

        //Update right view by the selected item id
        switch (id) {
            case LIST_INDEX_HISTORY:
                tv_hfc_filter.setVisibility(View.VISIBLE);
                tv_hfc_filter.setText(R.string.txt_activity_user_related_menu_filter_history);
                mRecordAdapter.setDataSource(mHistoryList);
                mRecordAdapter.notifyItemRangeInserted(0, mHistoryList.size());
                if (isUpdateHistoryData) {
                    int selectPos = mRecordAdapter.getDataIndex(mHistoryEvent.getId());
                    if (selectPos >= 0 && selectPos < mHistoryList.size()) {
                        mHistoryList.set(selectPos, mNewPlayHistory);
                        mRecordAdapter.setDataSource(mHistoryList);
                        mRecordAdapter.notifyItemChanged(selectPos);
                    }
                    isUpdateHistoryData = false;
                }
                break;
            case LIST_INDEX_COLLECTION:
                tv_hfc_filter.setVisibility(View.VISIBLE);
                tv_hfc_filter.setText(R.string.txt_activity_user_related_menu_filter_collection);
                mRecordAdapter.setDataSource(mCollectionList);
                mRecordAdapter.notifyItemRangeInserted(0, mCollectionList.size());
                break;
            default:
                break;
        }

    }

    /**
     * Custom class extended OnScrollListener to implement page loading data
     * <p>
     * Request more data of next page
     */
    private class FinishScrollListener extends RecyclerView.OnScrollListener {

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                if (mRecordRecyclerView == null) {
                    return;
                }
                if (mRecordRecyclerView.getFocusedChild() == null) {
                    return;
                }

                RecyclerView.ViewHolder viewHolder = mRecordRecyclerView.getChildViewHolder(
                        mRecordRecyclerView.getFocusedChild());
                if (viewHolder != null && viewHolder.itemView != null) {
                    mFocusBorderView.setFocusView(viewHolder.itemView);
                    FocusUtil.setFocusAnimator(viewHolder.itemView, mFocusBorderView);
                }
            }
        }
    }

    /**
     * Custom OnClickListener bind on ClearDialog
     */
    private class OnDialogDismissListener implements View.OnClickListener {

        private boolean isDeleteAll;

        private boolean isSure;

        /**
         * Constructor
         *
         * @param isDeleteAll delete all records or not
         */
        public OnDialogDismissListener(boolean isDeleteAll, boolean isSure) {
            this.isDeleteAll = isDeleteAll;
            this.isSure = isSure;
        }

        @Override
        public void onClick(View v) {
            //Dismiss ClearDialog
            mClearDialog.dismiss();

            if (isSure) {
                if (mActivity.getLeftSelectedTag() == LIST_INDEX_HISTORY) {
                    mPresenterImpl.clearAllHistoryData(true);
                    RequestManager.getInstance().onHistoryDeleteAllEvent();
                } else if (mActivity.getLeftSelectedTag() == LIST_INDEX_COLLECTION) {
                    mPresenterImpl.clearAllCollectionData(true);
                    RequestManager.getInstance().onCollectionCancelAllEvent();
                }
                return;
            }

            if (isDeleteAll) {
                mClearDialog = new ClearDialog(getContext());
                mClearDialog.show();
                mClearDialog.setClearNetualListener(new OnDialogDismissListener(true, true));
                mClearDialog.setTitle(getResources().getString(R.string.txt_activity_user_related_delete_all));
            } else {
                //Show delete icon on item of mRecordRecyclerView
                updateDeleteView(true);
                isDeleteItem = true;
            }
        }
    }
}
