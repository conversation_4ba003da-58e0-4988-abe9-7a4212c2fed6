package com.sohuott.tv.vod.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.util.ArrayMap;
import android.view.ViewGroup;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/7
 */

public abstract class CarouselBaseAdapter<T> extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    protected List<T> datas = new ArrayList<>();
    protected Context mContext;

    public CarouselBaseAdapter(Context context, List<T> list) {
        this.datas = list;
        this.mContext = context;
    }

}
