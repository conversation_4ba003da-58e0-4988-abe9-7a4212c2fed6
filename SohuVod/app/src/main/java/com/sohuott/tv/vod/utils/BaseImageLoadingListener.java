package com.sohuott.tv.vod.utils;


import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;

public class BaseImageLoadingListener<T> implements ImageLoadingListener<T> {

    @Override
    public void onLoadingStarted(T obj) {

    }

    @Override
    public void onLoadingFailed(T obj) {
        LibDeprecatedLogger.w("onLoadingFailed");
    }

    @Override
    public void onLoadingComplete(T obj) {

    }
}