package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.customview.RippleDiffuse;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.ui.EpisodeLayoutNew;
import com.sohuott.tv.vod.widget.PlayingView;

import java.util.List;

/**
 * Created by fenglei on 16-6-28.
 */
public class EpisodeVarietyFragmentNew extends EpisodeBaseFragmentNew {
    private int mWidth;
    private int mHeight;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mRootView = (ViewGroup) inflater.inflate(R.layout.fragment_episode_vrs_variety_layout, container, false);
        mWidth = getResources().getDimensionPixelSize(R.dimen.x205);
        mHeight = getResources().getDimensionPixelSize(R.dimen.y120);
        initUI();
        return mRootView;
    }

    @Override
    public void setUI(List<EpisodeVideos.Video> videoList) {
        if (mRootView == null){
            return;
        }
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            ViewGroup viewGroup = (ViewGroup) mRootView.getChildAt(i);
            if (viewGroup.getVisibility() == View.INVISIBLE) {
                return;
            }

            int num;
            if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
                num = mStart + i;
            } else {
                num = mEnd - i;
            }

            boolean found = false;
            TextView name = (TextView) (viewGroup.findViewById(R.id.tv_type_two_name));
            TextView focusName = viewGroup.findViewById(R.id.type_two_focus_name);
//            TextView focusDesc = viewGroup.findViewById(R.id.type_two_focus_desc);
            LinearLayout focusRoot = viewGroup.findViewById(R.id.type_two_focus_root);
            TextView focusEpisode = viewGroup.findViewById(R.id.type_two_focus_episode);
            RippleDiffuse focusPlay = viewGroup.findViewById(R.id.type_two_focus_play);
//            View focusView = viewGroup.findViewById(R.id.type_two_focus);
            View episodeBg = viewGroup.findViewById(R.id.focus_episode_bg);
            View nameBg = viewGroup.findViewById(R.id.name_bg);


//            TextView tvSubNameTextView = (TextView) (viewGroup.findViewById(R.id.episode_title2));
//            TextView noContentTextView = (TextView) (viewGroup.findViewById(R.id.episode_err));
            ImageView postImage = viewGroup.findViewById(R.id.iv_type_two_poster);
//            TextView pgc_length = (TextView) viewGroup.findViewById(R.id.episode_video_length);
//            noContentTextView.setText("");
//            ImageView icon = (ImageView) (viewGroup.findViewById(R.id.episode_btn_logo));

            if (videoList != null) {
                for (int j = 0; j < videoList.size(); j++) {
                    if (videoList.get(j).videoOrder == num) {
                        EpisodeVideos.Video video = videoList.get(j);
                        Glide.with(this)
                                .load(video.videoExtendsPic_320_180)
                                .transform(new RoundedCorners(getResources().getDimensionPixelOffset(R.dimen.x10)))
                                .into(postImage);
//                        pgc_length.setText(Util.formatVideoLength(video.tvLength));
                        if (mDataType != Constant.DATA_TYPE_VRS) {
                            String tvSubName = video.tvSubName;
//                            peroidTextView.setText("第" + num + "集");
                            if (!TextUtils.isEmpty(tvSubName)) {
                                name.setText(tvSubName);
                                focusName.setText(tvSubName);
//                                tvSubNameTextView.setText(tvSubName);
                            }
                        } else {
                            switch (mCateCode) {
                                case Constant.CATECODE_VARIETY:
                                    //设置综艺类的选集文案
                                    name.setText(video.tvSubName);
                                    focusName.setText(video.tvSubName);
                                    focusEpisode.setText(video.varietyPeriod);

//                                    peroidTextView.setText(video.varietyPeriod);
//                                    tvSubNameTextView.setText(video.tvSubName);
                                    break;
                            }
//                            if (video.tvStype != 1) {
////                                icon.setVisibility(View.VISIBLE);
////                                icon.setImageResource(R.drawable.episode_item_trailer);
//                            } else if (video.tvSetIsFee == 1) {
////                                icon.setVisibility(View.VISIBLE);
//                                if (video.isSyncBroadcast == 1) {
////                                    icon.setImageResource(R.drawable.episode_item_forestall);
//                                } else {
////                                    icon.setImageResource(R.drawable.episode_item_vip);
//                                }
//                            } else {
////                                icon.setVisibility(View.GONE);
//                            }
                        }

                        viewGroup.setTag(video);
                        viewGroup.setEnabled(true);
                        if (mVideoOrder == video.videoOrder && mEpisodeIsSelected) {
                            viewGroup.setSelected(true);
                            mPlayingView = (PlayingView) viewGroup.findViewById(R.id.episode_playing_view);
                            if (mPlayingView != null) {
                                mPlayingView.show();
                            }
                            if (mRootView.hasFocus()) {
                                viewGroup.requestFocus();
                            }
                        }
                        found = true;
                        break;
                    }
                }
            }
            if (!found) {
                viewGroup.setTag(Constant.EPISODE_OFFLINE);
                viewGroup.setEnabled(true);
                name.setText("数据缺失");
                focusName.setText("数据缺失");
//                peroidTextView.setEnabled(false);
            }


            viewGroup.setOnFocusChangeListener((view, hasFocus) -> {
                if (hasFocus) {
                    focusRoot.setVisibility(View.VISIBLE);
                    focusEpisode.setVisibility(View.VISIBLE);
                    episodeBg.setVisibility(View.VISIBLE);
                    nameBg.setVisibility(View.INVISIBLE);
                    name.setVisibility(View.GONE);
                    if (!name.getText().equals("数据缺失")) {
                        focusPlay.setVisibility(View.VISIBLE);
                        focusPlay.showWaveAnimation();
                    }
                } else {
                    focusRoot.setVisibility(View.GONE);
                    focusEpisode.setVisibility(View.GONE);
                    episodeBg.setVisibility(View.GONE);
                    nameBg.setVisibility(View.VISIBLE);
                    name.setVisibility(View.VISIBLE);
                    focusPlay.setVisibility(View.GONE);
                    focusPlay.cancelWaveAnimation();
                }
            });
        }
    }
}
