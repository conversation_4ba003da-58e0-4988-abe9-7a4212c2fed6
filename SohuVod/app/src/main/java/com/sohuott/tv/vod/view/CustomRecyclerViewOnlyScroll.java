package com.sohuott.tv.vod.view;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.View;

/**
 * Created by rita on 16-2-2.
 */
public class CustomRecyclerViewOnlyScroll extends RecyclerView {

    private static final String TAG = "CustomRecyclerView";
    private KeyEventAnimationCallback mKeyEventAnimationCallback;

    private long lastTimeMillis;

    public CustomRecyclerViewOnlyScroll(Context context) {
        super(context);
    }

    public CustomRecyclerViewOnlyScroll(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomRecyclerViewOnlyScroll(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        // Handle automatic focus changes.
        if (mKeyEventAnimationCallback != null) {
            if (event.getAction() == KeyEvent.ACTION_DOWN) {
                int direction = 0;
                switch (event.getKeyCode()) {
                    case KeyEvent.KEYCODE_DPAD_UP:
                        if (event.hasNoModifiers()) {
                            direction = View.FOCUS_UP;
                        }
                        break;
                    case KeyEvent.KEYCODE_DPAD_DOWN:
                        if (event.hasNoModifiers()) {
                            direction = View.FOCUS_DOWN;
                        }
                        break;
                }
                if (direction != 0) {
                    mKeyEventAnimationCallback.onKeyEvent(event, this);
                    return true;
                }
            }
        }

        return super.dispatchKeyEvent(event) || executeKey();
    }

    public void setKeyEventAnimationCallback(KeyEventAnimationCallback callback) {
        mKeyEventAnimationCallback = callback;
    }

    private boolean executeKey() {
        return getScrollState() != SCROLL_STATE_IDLE;
    }

    public interface KeyEventAnimationCallback {
        void onKeyEvent(KeyEvent event, CustomRecyclerViewOnlyScroll view);
    }
}