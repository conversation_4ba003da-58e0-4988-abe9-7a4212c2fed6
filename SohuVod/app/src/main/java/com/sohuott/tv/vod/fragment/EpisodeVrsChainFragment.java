package com.sohuott.tv.vod.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.EpisodeVideos;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.SystemUtils;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.ui.EpisodeLayoutNew;
import com.sohuott.tv.vod.utils.FocusUtil;

import java.util.List;

/**
 * Created by fenglei on 16-7-5.
 */
public class EpisodeVrsChainFragment extends EpisodeBaseFragmentNew {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        mRootView = (ViewGroup) inflater.inflate(R.layout.fragment_episode_vrs_chain_layout, container, false);
        mRootView.setClipChildren(false);
        mRootView.setClipToPadding(false);
        initUI();
        return mRootView;
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

    private void setPointsPosition(View v, String text) {
        if (mEpisodePoints == null) {
            return;
        }
        int[] location = new int[2];
        v.getLocationOnScreen(location);
        int deskWidth = v.getMeasuredWidth();
        int shadeWidth = Util.getTextViewStrWidth(text, mEpisodePoints.getPaint());
        int x;
        if (deskWidth >= shadeWidth) {
            x = location[0] + (deskWidth - shadeWidth) / 2;
        } else {
            x = location[0] - (shadeWidth - deskWidth) / 2;
        }
        if (x < 0) {
            x = 0;
        }
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mEpisodePoints.getLayoutParams();
        layoutParams.leftMargin = x;
        mEpisodePoints.setLayoutParams(layoutParams);
    }

    private String getPoint(String pointsStr) {
        String[] points = null;
        if (pointsStr.contains("|")) {
            points = pointsStr.split("\\|");
        }
        if (points != null && points.length > 0) {
            int index = points[0].indexOf("-");
            if (index < 0) {
                return points[0];
            } else {
                if (points[0].length() > (index + 1)) {
                    return points[0].substring(index + 1);
                } else {
                    return "";
                }
            }
        } else {
            return pointsStr;
        }
    }

    private void setPointsDisplay(View view, EpisodeVideos.Video point) {
        if (point != null && !TextUtils.isEmpty(point.points)) {
            String video_point = getPoint(point.points);
            if (mEpisodePoints != null && video_point != null && !TextUtils.isEmpty(video_point)) {
                mEpisodePoints.setText(video_point);
                int screenWidth = SystemUtils.getScreenWidth(getContext());
                int[] location = new int[2];
                view.getLocationOnScreen(location);
                if (location[0] >= screenWidth) {

                } else {
                    setPointsPosition(view, video_point);
                    mEpisodePoints.setVisibility(View.VISIBLE);
                    setTVOnFocus(mEpisodePoints);
                }

            } else {
                if (mEpisodePoints != null) {
                    mEpisodePoints.setVisibility(View.INVISIBLE);
                    setTVUnFocus(mEpisodePoints);
                }
            }
        }
    }

    @Override
    protected void initUI() {
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            View viewGroup = mRootView.getChildAt(i).findViewById(R.id.episode_tv);
            if (Util.getManufactureName().equalsIgnoreCase("Rockchip")) {
                viewGroup.setBackgroundResource(R.drawable.episode_vrs_item_corner);
            }
            viewGroup.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setFocusView(v);
                            FocusUtil.setFocusAnimator(v, mFocusBorderView, 1.2f, 500);
                        }
                        if (!getUserVisibleHint()) {
                            if (mEpisodePoints != null) {
                                mEpisodePoints.setVisibility(View.INVISIBLE);
                                setTVUnFocus(mEpisodePoints);
                            }
                            return;
                        }

                        EpisodeVideos.Video point = null;
                        try {
                            point = (EpisodeVideos.Video) v.getTag();
                        } catch (Exception e) {
//                            e.printStackTrace();
                        }
                        setPointsDisplay(v, point);

                    } else {
                        if (mFocusBorderView != null) {
                            mFocusBorderView.setUnFocusView(v);
                            FocusUtil.setUnFocusAnimator(v, 500);
                        }
                        if (mEpisodePoints != null) {
                            mEpisodePoints.setVisibility(View.INVISIBLE);
                            setTVUnFocus(mEpisodePoints);
                        }
                    }
                }
            });
            viewGroup.setOnKeyListener(this);
            viewGroup.setOnClickListener(this);
        }
        int dataSize = mEnd - mStart + 1;
        if (dataSize < mRootView.getChildCount()) {
            for (int i = dataSize; i < mRootView.getChildCount(); i++) {
                mRootView.getChildAt(i).setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void setUI(List<EpisodeVideos.Video> videoList) {
        if (mRootView == null) return;
        for (int i = 0; i < mRootView.getChildCount(); i++) {
            View childView = mRootView.getChildAt(i);
            if (childView.getVisibility() != View.VISIBLE) {
                return;
            }
            int num;
            if (mSortOrder == EpisodeLayoutNew.ASC_SORT_ORDER) {
                num = mStart + i;
            } else {
                num = mEnd - i;
            }
            boolean found = false;
            TextView titleView = childView.findViewById(R.id.episode_tv);
            ImageView badgeImageView = childView.findViewById(R.id.episode_btn_logo);

            if (videoList != null) {
                for (int j = 0; j < videoList.size(); j++) {
                    titleView.setText("第" + String.valueOf(num) + "集：" + videoList.get(j).tvSubName);
                    //针对更多分类，音乐频道做的处理，根据服务端反馈,只有tvStype == 2  tvFormalOrder才有用
                    if (videoList.get(j).tvStype != 2) {
                        videoList.get(j).tvStype = 1;
                    }
                    if ((videoList.get(j).tvStype == 1 && videoList.get(j).videoOrder == num)
                            || (videoList.get(j).tvStype != 1 && videoList.get(j).tvFormalOrder == num)) {
                        if (videoList.get(j).tvStype != 1) {
                            badgeImageView.setBackgroundResource(R.drawable.episode_item_trailer);
                            badgeImageView.setVisibility(View.VISIBLE);
                        } else if (videoList.get(j).tvSetIsFee == 1) {
                            if (videoList.get(j).isSyncBroadcast == 1) {
                                badgeImageView.setBackgroundResource(R.drawable.episode_item_forestall);
                                badgeImageView.setVisibility(View.VISIBLE);
                            } else {
                                if(mCateCode == Constant.EDU_CATE_CODE){ // 教育测试环境
                                    badgeImageView.setBackgroundResource(R.drawable.episode_item_fee);
                                    badgeImageView.setVisibility(View.VISIBLE);
                                }else{
                                    badgeImageView.setBackgroundResource(R.drawable.episode_item_vip);
                                    badgeImageView.setVisibility(View.VISIBLE);
                                }
                            }
                        }

                        if ((videoList.get(j).tvStype == 1 && videoList.get(j).videoOrder == mVideoOrder && mEpisodeIsSelected)
                                || (videoList.get(j).tvStype != 1 && videoList.get(j).tvFormalOrder == mVideoOrder && mEpisodeIsSelected)) {
                            childView.setSelected(true);
                            if (mRootView.hasFocus()) {
                                titleView.requestFocus();
                            }
                        }
                        titleView.setTag(videoList.get(j));
                        childView.setEnabled(true);
                        if (childView.isFocused()) {
                            EpisodeVideos.Video point = videoList.get(j);
                            setPointsDisplay(childView, point);
                        }
                        found = true;
                        break;
                    }
                }
            }
            if (!found) {
                childView.setTag(Constant.EPISODE_OFFLINE);
                childView.setEnabled(true);
                titleView.setEnabled(false);
            }
        }
    }

    @Override
    public void onPageScrollStateStop() {
        if (!isVisible()) {
            return;
        }
        if (mRootView != null) {
            View focusView = mRootView.getFocusedChild();
            if (focusView != null) {
                EpisodeVideos.Video point = null;
                try {
                    point = (EpisodeVideos.Video) focusView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                setPointsDisplay(focusView, point);
            }
            recoverFocus(focusView);
        }
    }
}
