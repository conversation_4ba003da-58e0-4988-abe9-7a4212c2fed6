package com.sohuott.tv.vod.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.model.HotSearchNew;
import com.sohuott.tv.vod.ui.SearchActorView;
import com.sohuott.tv.vod.utils.SearchUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by fenglei on 17-6-20.
 */

public class HotSearchLayout extends LinearLayout {

    public static final int HOT = 0;
    public static final int SUGGEST = 1;

    private List<HotSearchItemView> hotSearchItemViewList = new ArrayList<>();
    private List<SearchActorView> searchActorViewList = new ArrayList<>();
    private int type;

    private String mPageName = "6_search";

    public HotSearchLayout(Context context) {
        super(context);
        initUI(context);
    }

    public  HotSearchLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initUI(context);
    }

    public HotSearchLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initUI(context);
    }

    public void customLayoutInflater(Context context){
        LayoutInflater.from(context).inflate(
                R.layout.search_no_input_adapter_hot_search_with_pic_layout, this, true);
    }

    public void setPageName(String pageName){
        this.mPageName = pageName;
        if(hotSearchItemViewList != null) {
            for(int i = 0; i < hotSearchItemViewList.size(); i++) {
                hotSearchItemViewList.get(i).setPageName(mPageName);
            }
        }
    }

    private void initUI(Context context) {
        setClipChildren(false);
        setClipToPadding(false);
        setOrientation(HORIZONTAL);
//        LayoutInflater.from(context).inflate(
//                R.layout.search_no_input_adapter_hot_search_with_pic_layout, this, true);
        customLayoutInflater(context);
        for(int i = 0; i < getChildCount(); i++) {
            hotSearchItemViewList.add((HotSearchItemView)getChildAt(i).findViewById(R.id.searchAlbumView));
            searchActorViewList.add((SearchActorView)getChildAt(i).findViewById(R.id.searchActorView));
            hotSearchItemViewList.get(i).setIndex(i);
            searchActorViewList.get(i).setIndex(i);
        }
    }

    public void setType(int type) {
        this.type = type;
        if(hotSearchItemViewList != null) {
            for(int i = 0; i < hotSearchItemViewList.size(); i++) {
                hotSearchItemViewList.get(i).setType(type);
            }
        }
    }

    public void setSearchUI(List<HotSearchNew.DataBean> dataBeanList, int albumCount) {
        if(dataBeanList != null && dataBeanList.size() > 0) {
            int size = Math.min(albumCount, getChildCount());
            for(int i = 0;i < size; i++) {
                getChildAt(i).setVisibility(View.VISIBLE);
                if(dataBeanList.get(i).getCid() == SearchUtil.SEARCH_CID_ACTOR
                        || dataBeanList.get(i).getCid() == SearchUtil.SEARCH_CID_PRODUCER) {
                    searchActorViewList.get(i).setUI(dataBeanList.get(i));
                    searchActorViewList.get(i).setVisibility(View.VISIBLE);
                    hotSearchItemViewList.get(i).setVisibility(View.GONE);
                } else {
                    hotSearchItemViewList.get(i).setUI(dataBeanList.get(i));
                    searchActorViewList.get(i).setVisibility(View.GONE);
                    hotSearchItemViewList.get(i).setVisibility(View.VISIBLE);
                }
            }
            for(int i = size; i < getChildCount(); i++) {
                getChildAt(i).setVisibility(View.GONE);
            }
        }
    }

//    public void setSearchSuggestUI(List<SearchSuggest.DataBean.RBean> rBeanList) {
//        if(rBeanList != null && rBeanList.size() > 0) {
//            int size = Math.min(rBeanList.size(), hotSearchItemViewList.size());
//            for(int i = 0;i < size; i++) {
//                hotSearchItemViewList.get(i).setUI(rBeanList.get(i));
//            }
//            for(int i = size; i < hotSearchItemViewList.size(); i++) {
//                hotSearchItemViewList.get(i).setVisibility(View.GONE);
//            }
//        }
//    }

    public void setFocusBorderView(FocusBorderView focusBorderView) {
        if(hotSearchItemViewList != null) {
            for(int i = 0; i < hotSearchItemViewList.size(); i++) {
                hotSearchItemViewList.get(i).setFocusBorderView(focusBorderView);
            }
        }
    }

}
