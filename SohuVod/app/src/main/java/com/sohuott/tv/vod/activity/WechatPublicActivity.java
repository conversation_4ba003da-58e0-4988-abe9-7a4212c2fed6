 package com.sohuott.tv.vod.activity;

 import android.os.Bundle;
 import android.os.Handler;
 import android.os.Message;
 import android.view.KeyEvent;
 import android.view.View;
 import android.widget.ImageView;
 import android.widget.TextView;

 import com.sohu.ott.base.lib_user.UserInfoHelper;
 import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
 import com.lib_statistical.manager.RequestManager;
 import com.sohuott.tv.vod.R;
 import com.sohuott.tv.vod.lib.api.NetworkApi;
 import com.sohuott.tv.vod.lib.model.WechatPublic;
 import com.sohuott.tv.vod.lib.push.event.BindEvent;
 import com.sohuott.tv.vod.lib.push.event.PlayVideoEvent;
 import com.sohuott.tv.vod.lib.utils.Constant;
 import com.sohuott.tv.vod.lib.utils.ToastUtils;
 import com.sohuott.tv.vod.lib.utils.Util;
 import com.sohuott.tv.vod.utils.NormalLoadPictrue;

 import org.greenrobot.eventbus.Subscribe;

 import java.lang.ref.WeakReference;

 import io.reactivex.observers.DisposableObserver;

 /**
  * Created by xianrongchen on 2016/4/8.
  */
 public class WechatPublicActivity extends BaseActivity {

     private static final int MSG_SCAN_SUCCESS = 1;
     private static final int MSG_SCAN_FAILURE = 2;

     private boolean mBindSuccess;
     private boolean mPlaySuccess;

     private TextView mQrCodeScanSuccessTip;
     private TextView mQrCodeScanSuccessTip2;

     private ImageView mQrCodeImage;
     private ImageView mQrCodeScanSuccessImage;

     private MyHandler mHandler;

     @Override
     public void onCreate(Bundle savedInstanceState) {
         super.onCreate(savedInstanceState);

         setContentView(R.layout.activity_wechat_public);

         initView();
         initData();

         mHandler = new MyHandler(this);

         RequestManager.getInstance().onEvent("5_play_tv", "100001", null, null, null, null, null);
     }

     @Override
     public boolean onKeyUp(int keyCode, KeyEvent event) {
         if (keyCode == KeyEvent.KEYCODE_BACK && mBindSuccess && !mPlaySuccess) {
             finish();
             return true;
         } else {
             return super.onKeyUp(keyCode, event);
         }
     }

     @Override
     protected boolean isEventBusAvailable() {
         return true;
     }

     @Subscribe
     public void onEventMainThread(BindEvent event) {
         if (null == event) {
             return;
         }

         mHandler.sendEmptyMessage(MSG_SCAN_SUCCESS);
         mBindSuccess = true;
     }

     @Subscribe
     public void onEventMainThread(PlayVideoEvent event) {
         if (null == event) {
             return;
         }

         mPlaySuccess = true;
         finish();
     }

     private void initView() {
         mQrCodeScanSuccessTip = (TextView) findViewById(R.id.qrcode_scan_success_txt_tip);
         mQrCodeScanSuccessTip2 = (TextView) findViewById(R.id.qrcode_scan_success_txt_tip2);

         mQrCodeImage = (ImageView) findViewById(R.id.qrcode_image);
         mQrCodeScanSuccessImage = (ImageView) findViewById(R.id.qrcode_scan_success_image);
     }

     private void initData() {
         getWechatPublic();
     }

     private void getWechatPublic() {
         NetworkApi.getWechatPublic(UserInfoHelper.getGid(), Constant.TYPE_CAPTCHA_BIND, Util.getDeviceName(), new DisposableObserver<WechatPublic>() {
             @Override
             public void onNext(WechatPublic response) {
                 LibDeprecatedLogger.d("getWechatPublic() response: " + response);
                 if (null != response) {
                     String data = response.getData();
                     String message = response.getMessage();
                     int status = response.getStatus();

                     if (status == 200 && null != data) {
                         if (!data.trim().equals("")) {
                             new NormalLoadPictrue(WechatPublicActivity.this).getPicture(data, mQrCodeImage);


                             RequestManager.getInstance().onEvent("5_play_tv", "5_play_tvQR", null, null, null, null, null);
                         }
                     } else {
                         ToastUtils.showToast2(WechatPublicActivity.this, message);
                     }
                 }
             }

             @Override
             public void onError(Throwable e) {
                 LibDeprecatedLogger.e("getWechatPublic() Error: " + e.getMessage(), e);
             }

             @Override
             public void onComplete() {
                 LibDeprecatedLogger.d("getWechatPublic(): onComplete()");
             }
         });
     }

     private class MyHandler extends Handler {
         private WeakReference<WechatPublicActivity> activityReference;

         public MyHandler(WechatPublicActivity ref) {
             super();
             activityReference = new WeakReference<WechatPublicActivity>(ref);
         }

         @Override
         public void handleMessage(Message msg) {
             WechatPublicActivity ref = activityReference.get();
             if (null == ref) {
                 return;
             }

             int what = msg.what;
             switch (what) {
                 case MSG_SCAN_SUCCESS:
                     if (null != ref.mQrCodeImage) {
                         ref.mQrCodeImage.setAlpha(0.2f);
                     }
                     if (null != ref.mQrCodeScanSuccessImage) {
                         ref.mQrCodeScanSuccessImage.setVisibility(View.VISIBLE);
                     }
                     if (null != ref.mQrCodeScanSuccessTip) {
                         ref.mQrCodeScanSuccessTip.setVisibility(View.VISIBLE);
                     }
                     if (null != ref.mQrCodeScanSuccessTip2) {
                         ref.mQrCodeScanSuccessTip2.setVisibility(View.VISIBLE);
                     }
                     break;
                 case MSG_SCAN_FAILURE:
                     if (null != ref.mQrCodeScanSuccessImage) {
                         ref.mQrCodeScanSuccessImage.setVisibility(View.GONE);
                     }
                     if (null != ref.mQrCodeScanSuccessTip) {
                         ref.mQrCodeScanSuccessTip.setVisibility(View.GONE);
                     }
                     if (null != ref.mQrCodeScanSuccessTip2) {
                         ref.mQrCodeScanSuccessTip2.setVisibility(View.GONE);
                     }
                     break;
                 default:
                     break;
             }
         }
     }

}
