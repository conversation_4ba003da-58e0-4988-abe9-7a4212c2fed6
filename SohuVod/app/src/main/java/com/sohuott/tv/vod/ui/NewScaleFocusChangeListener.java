package com.sohuott.tv.vod.ui;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.sohuott.tv.vod.utils.FocusUtil;
import com.sohuott.tv.vod.view.FocusBorderView;

/**
 * Created by fengle<PERSON> on 16-1-13.
 */
public class NewScaleFocusChangeListener implements View.OnFocusChangeListener {

    public interface FocusCallback {
        void onFocusChange(View v, boolean hasFocus);
    }

    private FocusCallback mFocusCallback;

    public FocusBorderView mFocusBorderView;

    public float mScale = FocusUtil.HOME_SCALE;

    private TextView mScrollTextView;

    public TextView getScrollTextView() {
        return mScrollTextView;
    }

    public void setScrollTextView(TextView scrollTextView) {
        mScrollTextView = scrollTextView;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
//        if (v.getTag() != null && v.getTag() instanceof TextView) {
//            ((TextView)v.getTag()).setSelected(hasFocus);
//            if (hasFocus) {
//                ((TextView)v.getTag()).setMarqueeRepeatLimit(-1);
//                ((TextView)v.getTag()).setEllipsize(TextUtils.TruncateAt.MARQUEE);
//                if(mScrollTextView != null){
//                    mScrollTextView.setMarqueeRepeatLimit(-1);
//                    mScrollTextView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
//                }
//            } else {
//                ((TextView)v.getTag()).setEllipsize(TextUtils.TruncateAt.END);
//                if(mScrollTextView != null){
//                    mScrollTextView.setEllipsize(TextUtils.TruncateAt.END);
//                }
//            }
//        }
        if (hasFocus) {
//            if (mFocusBorderView != null) {
//                mFocusBorderView.setFocusView(v);
//            }
//            FocusUtil.setFocusAnimator(v, mFocusBorderView, mScale);
            setFocusAnimator(v,mScale,FocusUtil.FOCUS_ANIM_TIME);
        } else {
//            if (mFocusBorderView != null) {
//                mFocusBorderView.setUnFocusView(v);
//            }
//            FocusUtil.setUnFocusAnimator(v);
            setUnFocusAnimator(v);
        }
        if (mFocusCallback != null) {
            mFocusCallback.onFocusChange(v, hasFocus);
        }
    }

    private static AnimatorSet mAnimationSet;
    public static void setFocusAnimator(View focusView, float scale, int animTime) {
        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(focusView, "scaleX", 1f, scale)
                .setDuration(animTime);
        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(focusView, "scaleY", 1f, scale)
                .setDuration(animTime);
        if(mAnimationSet != null && mAnimationSet.isRunning()){
            mAnimationSet.end();
        }
        mAnimationSet = new AnimatorSet() ;
        mAnimationSet.playTogether(scalexAnimator, scaleyAnimator);
        mAnimationSet.start();
    }

//    public static void setFocusAnimator(View focusView, FocusBorderView focusBorderView,
//                                        float scale, int animTime) {
//        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(focusView, "scaleX", 1f, scale)
//                .setDuration(animTime);
//        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(focusView, "scaleY", 1f, scale)
//                .setDuration(animTime);
//        ObjectAnimator focusAnimator = null;
//        if(focusBorderView != null) {
//            focusAnimator = ObjectAnimator.ofFloat(focusBorderView, "ScaleUp",
//                    new float[] { 1f, scale }).setDuration(animTime);
//        }
//
//        AnimatorSet set = focusBorderView != null ? focusBorderView.getAnimatorSet():new AnimatorSet() ;
//        if(focusAnimator != null) {
//            set.playTogether(scalexAnimator, scaleyAnimator, focusAnimator);
//        }else {
//            set.playTogether(scalexAnimator, scaleyAnimator);
//        }
//        set.start();
//    }

    public static void setUnFocusAnimator(View focusView) {
        setUnFocusAnimator(focusView, FocusUtil.FOCUS_ANIM_TIME);
    }

    public static void setUnFocusAnimator(View focusView, int animTime) {
        ObjectAnimator scalexAnimator = ObjectAnimator.ofFloat(focusView, "scaleX", focusView.getScaleX(), 1f)
                .setDuration(animTime);
        ObjectAnimator scaleyAnimator = ObjectAnimator.ofFloat(focusView, "scaleY", focusView.getScaleY(), 1f)
                .setDuration(animTime);
        AnimatorSet set = new AnimatorSet();
        set.playTogether(scalexAnimator, scaleyAnimator);
        set.start();
    }

    public void setScale(float scale) {
        mScale = scale;
    }

    public void setFocusBorderView(FocusBorderView focusView) {
        mFocusBorderView = focusView;
    }

    public FocusBorderView getFocusBorderView() {
        return mFocusBorderView;
    }

    public void setFocusCallback(FocusCallback focusCallback) {
        mFocusCallback = focusCallback;
    }

}
