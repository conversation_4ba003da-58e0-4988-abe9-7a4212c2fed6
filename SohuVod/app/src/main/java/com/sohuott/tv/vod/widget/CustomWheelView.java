package com.sohuott.tv.vod.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.widget.Scroller;

import com.sohuott.tv.vod.R;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by wenjingbian on 2017/9/7.
 */

public class CustomWheelView extends View {


    // Wheel Values
    private ArrayList<String> mValueList = new ArrayList<>();
    private int mCurrentItem = 0;

    // Text paints
    private Paint mDecorPaint;
    private TextPaint mDecorTextPaint;
    private Paint mDecorBgPaint;
    private TextPaint mTextPaint;

    private int mFocusColor;
    private int mNearColor;
    private int mFarColor;
    private int mBgColor;


    private float mTextFirstSlop;
    private float mTextSecondSlop;
    private float mTextPaddingBottom;

    private float mCornerPixel;
    private float mCornerHeight;
//    private float mCornerLabelX;
//    private float mCornerLabelY;
//    private float mLabelPadding;

    // Label
//    private String label;

    private RectF mCenterRectF;

    /**
     * The {@link Scroller} responsible for flinging the selector.
     */
    private Scroller mFlingScroller;

    /**
     * The {@link Scroller} responsible for adjusting the selector.
     */
    private Scroller mAdjustScroller;

    // Cyclic
    boolean isCyclic = false;

    // Listeners
    private List<OnScrollListener> changingListeners = new LinkedList<OnScrollListener>();
    private List<OnExitListener> exitListeners = new LinkedList<OnExitListener>();

    /**
     * The initial offset of the scroll selector.
     */
    private int mInitialScrollOffset = 0;

    /**
     * Scroll degree in a single press
     */
    private int mSelectorElementHeight = getResources().getDimensionPixelOffset(R.dimen.y76);

    /**
     * The current offset of the scroll selector.
     */
    private int mCurrentScrollOffset;

    /**
     * The previous Y coordinate while scrolling the selector.
     */
    private int mPreviousScrollerY;

    /**
     * The keycode of the last handled DPAD down event.
     */
    private int mLastHandledDownDpadKeyCode = -1;

    /**
     * Constructor
     */
    public CustomWheelView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initData(context);
    }

    /**
     * Constructor
     */
    public CustomWheelView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initData(context);
    }

    /**
     * Constructor
     */
    public CustomWheelView(Context context) {
        super(context);
        initData(context);
    }

    /**
     * Initializes class data
     *
     * @param context the context
     */
    private void initData(Context context) {
        // create the fling and adjust scrollers
        mFlingScroller = new Scroller(getContext(), null, true);
        mAdjustScroller = new Scroller(getContext(), new DecelerateInterpolator(2.5f));
        initResourcesIfNecessary();
    }

    public ArrayList<String> getFilterData() {
        return mValueList;
    }

    public void setFilterDater(ArrayList<String> list) {
        mValueList.clear();
        mValueList.addAll(list);
        invalidate();
    }

    public void setFilterDater(List<String> list) {
        mValueList.clear();
        mValueList.addAll(list);
        invalidate();
    }

    /**
     * Gets label
     *
     * @return the label
     */
//    public String getLabel() {
//        return label;
//    }

    /**
     * Sets label
     *
     * @param newLabel the label to set
     */
//    public void setLabel(String newLabel) {
//        if (label == null || !label.equals(newLabel)) {
//            label = newLabel;
//            invalidate();
//        }
//    }

    /**
     * Adds wheel changing listener
     *
     * @param listener the listener
     */
    public void addChangingListener(OnScrollListener listener) {
        changingListeners.add(listener);
    }

    /**
     * Removes wheel changing listener
     *
     * @param listener the listener
     */
    public void removeChangingListener(OnScrollListener listener) {
        changingListeners.remove(listener);
    }

    /**
     * Notifies changing listeners
     */
    protected void notifyChangingListeners() {
        for (OnScrollListener listener : changingListeners) {
            listener.onChanged();
        }
    }


    /**
     * Adds wheel exiting listener
     *
     * @param listener the listener
     */
    public void addExitListener(OnExitListener listener) {
        exitListeners.add(listener);
    }

    /**
     * Removes wheel exiting listener
     *
     * @param listener the listener
     */
    public void removeExitListener(OnExitListener listener) {
        exitListeners.remove(listener);
    }

    /**
     * Notifies exiting listeners
     */
    protected void notifyExitListeners() {
        for (OnExitListener listener : exitListeners) {
            listener.onExit();
        }
    }

    /**
     * Gets current value
     *
     * @return the current value
     */
    public int getCurrentItem() {
        return mCurrentItem;
    }

    /**
     * Tests if wheel is cyclic. That means before the 1st item there is shown the last one
     *
     * @return true if wheel is cyclic
     */
    public boolean isCyclic() {
        return isCyclic;
    }

    /**
     * Set wheel cyclic flag
     *
     * @param isCyclic the flag to set
     */
    public void setCyclic(boolean isCyclic) {
        this.isCyclic = isCyclic;

        invalidate();
    }

    /**
     * Initializes resources
     */
    private void initResourcesIfNecessary() {
        mFocusColor = getResources().getColor(R.color.filter_wheel_focus);
        mNearColor = getResources().getColor(R.color.filter_wheel_near);
        mFarColor = getResources().getColor(R.color.filter_wheel_far);
        mBgColor = getResources().getColor(R.color.filter_wheel_bg);

        mCornerPixel = getResources().getDimension(R.dimen.y50);
        mCornerHeight = getResources().getDimension(R.dimen.y100);

//        mCornerLabelX = getResources().getDimension(R.dimen.x36);
//        mCornerLabelY = getResources().getDimension(R.dimen.y100);
//        mLabelPadding = getResources().getDimension(R.dimen.x5);


        mTextPaddingBottom = getResources().getDimension(R.dimen.y140);
        mTextFirstSlop = getResources().getDimension(R.dimen.y136);
        mTextSecondSlop = getResources().getDimension(R.dimen.y110);

        float decorStrokeWidth = getResources().getDimension(R.dimen.y4);

        if (mDecorPaint == null) {
            mDecorPaint = new Paint();
            mDecorPaint.setAntiAlias(true);
            mDecorPaint.setTextAlign(Paint.Align.CENTER);
            mDecorPaint.setStyle(Paint.Style.STROKE);
            mDecorPaint.setStrokeWidth(decorStrokeWidth);
            mDecorPaint.setColor(mNearColor);
        }

        if (mDecorBgPaint == null) {
            mDecorBgPaint = new Paint();
            mDecorBgPaint.setAntiAlias(true);
            mDecorBgPaint.setStyle(Paint.Style.FILL);
            mDecorBgPaint.setColor(mBgColor);
        }

        if (mDecorTextPaint == null) {
            mDecorTextPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
            mDecorTextPaint.setColor(mNearColor);
            mDecorTextPaint.setTextSize(getResources().getDimensionPixelSize(R.dimen.x36));
        }

        if (mTextPaint == null) {
            mTextPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
            mTextPaint.setTextSize(getResources().getDimensionPixelSize(R.dimen.x42));
            mTextPaint.setColor(mNearColor);
        }

    }

    /**
     * Returns height of wheel item
     *
     * @return the item height
     */
    private int getItemHeight() {
        return mSelectorElementHeight;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (mValueList == null || mValueList.size() == 0) {
            return;
        }
        drawCenterRect(canvas);


//        //draw the text
        if (isFocused()) {
            for (int i = 0; i < 5; i++) {
                int index = 2 - i + mCurrentItem;
                if (index >= mValueList.size() || index < 0) {
                    break;
                }
                String text = mValueList.get(index);
                if (!TextUtils.isEmpty(text)) {
                    drawText(canvas, text, index);
                }
            }
        } else {
            String text = mValueList.get(mCurrentItem);
            drawText(canvas, text, 0);
        }

        if (mCurrentScrollOffset > 0 && !mFlingScroller.isFinished()) {
            if (mCurrentItem - 1 >= 0 && !TextUtils.isEmpty(mValueList.get(mCurrentItem - 1))) {
                drawText(canvas, mValueList.get(mCurrentItem - 1), -1);
            }
        } else if (mCurrentScrollOffset < 0 && !mFlingScroller.isFinished()) {
            if (mCurrentItem + 2 < mValueList.size() && !TextUtils.isEmpty(mValueList.get(mCurrentItem + 2))) {
                drawText(canvas, mValueList.get(mCurrentItem + 2), 3);
            }
        }
    }

    /**
     * Draws rect for current value
     *
     * @param canvas the canvas for drawing
     */
    private void drawCenterRect(Canvas canvas) {
        //draw the center background
        if (isFocused() || isSelected()) {
            mDecorPaint.setColor(mFocusColor);
        } else {
            mDecorPaint.setColor(mNearColor);
        }
//        canvas.drawRoundRect(new RectF(5, getHeight() - 5 - mCornerHeight-100, getWidth() - 5, getHeight() - 5-100),
//                mCornerPixel * 1.2f, mCornerPixel, mDecorPaint);

        mCenterRectF = new RectF(5, 5 + getResources().getDimensionPixelOffset(R.dimen.y152),
                getWidth() - 5, 5 + getResources().getDimensionPixelOffset(R.dimen.y152) + getResources().getDimensionPixelOffset(R.dimen.y100));
        canvas.drawRoundRect(mCenterRectF, mCornerPixel * 1.2f, mCornerPixel, mDecorPaint);

        //draw the filter catgory
//        Paint.FontMetricsInt fontMetrics = mDecorTextPaint.getFontMetricsInt();
//        Rect rect = new Rect(0, 0, 0, 0);
//        mDecorTextPaint.getTextBounds(label, 0, label.length(), rect);
//        canvas.drawRect(mCornerLabelX - mLabelPadding, getHeight() - mCornerLabelY - rect.height(),
//                mCornerLabelX + rect.width() + mLabelPadding, getHeight() - mCornerLabelY, mDecorBgPaint);
//        canvas.drawText(label, mCornerLabelX, getHeight() - mCornerLabelY, mDecorTextPaint);
    }

    private void drawText(Canvas canvas, String item, int index) {
        index -= mCurrentItem;
        Rect rect = new Rect(0, 0, 0, 0);
        mTextPaint.getTextBounds(item, 0, item.length(), rect);
        float centerX = getWidth() / 2 - rect.centerX();
        float baseline;
        if (mFlingScroller.isFinished()) {
            if (index < 0) {
                baseline = mCenterRectF.top - 5 - Math.abs(index * getResources().getDimension(R.dimen.y40)) - (Math.abs(index) - 1) * getResources().getDimensionPixelOffset(R.dimen.x36);
            } else if (index == 0) {
                baseline = mCenterRectF.bottom - getResources().getDimensionPixelOffset(R.dimen.y29) - 5;
            } else {
                baseline = mCenterRectF.bottom + 5 + Math.abs(index * getResources().getDimension(R.dimen.y40)) + (index - 1) * getResources().getDimensionPixelOffset(R.dimen.x36);
            }
        } else if (mCurrentScrollOffset > 0) {
            if (index > 0) {
                baseline = mCenterRectF.top - 5 + (index + 2 - (float) mCurrentScrollOffset / (float) mSelectorElementHeight)
                        * (getResources().getDimension(R.dimen.y40) + getResources().getDimensionPixelOffset(R.dimen.x36));
            } else if (index == 0) {
                baseline = mCenterRectF.bottom - 5 - ((float) mCurrentScrollOffset / (float) mSelectorElementHeight)
                        * getResources().getDimensionPixelOffset(R.dimen.y29);
            } else {
                baseline = mCenterRectF.bottom + 5 + (index - (float) mCurrentScrollOffset / (float) mSelectorElementHeight)
                        * (getResources().getDimension(R.dimen.y40) + getResources().getDimensionPixelOffset(R.dimen.x36));
            }
        } else {
            if (index < 0) {
                baseline = mCenterRectF.top + 5 + (2 - index - (float) mCurrentScrollOffset / (float) mSelectorElementHeight)
                        * (getResources().getDimension(R.dimen.y40) + getResources().getDimensionPixelOffset(R.dimen.x36));
            } else if (index == 0) {
                baseline = mCenterRectF.top + 5 - ((float) mCurrentScrollOffset / (float) mSelectorElementHeight)
                        * getResources().getDimensionPixelOffset(R.dimen.y29);
            } else {
                baseline = mCenterRectF.bottom + 5 + (( index + (float) mCurrentScrollOffset / (float) mSelectorElementHeight)
                        * (getResources().getDimension(R.dimen.y40) + getResources().getDimensionPixelOffset(R.dimen.x36)));
            }
        }

        mTextPaint.setColor(mNearColor);
        switch (index) {
            case -2:
                mDecorPaint.setColor(mFarColor);
                canvas.drawText(item, centerX, baseline, mDecorTextPaint);
                break;
            case -1:
                canvas.drawText(item, centerX, baseline, mDecorTextPaint);
                break;
            case 0:
                if (mFlingScroller.isFinished() && (isSelected() || isFocused())) {
                    mTextPaint.setColor(mFocusColor);
                }
                canvas.drawText(item, centerX, baseline, mTextPaint);
                break;
            case 1:
                canvas.drawText(item, centerX, baseline, mDecorTextPaint);
                break;
            case 2:
                mDecorPaint.setColor(mFarColor);
                canvas.drawText(item, centerX, baseline, mDecorTextPaint);
                break;
            case 4:
                canvas.drawText(item, centerX, baseline, mDecorTextPaint);
                break;
            default:
                break;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
            if (mCurrentItem < mValueList.size() - 1) {
                changeValueByOne(true);
                return true;
            }
        } else if (keyCode == KeyEvent.KEYCODE_DPAD_UP) {
            if (mCurrentItem >= 0) {
                changeValueByOne(false);
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }


    @Override
    protected void onFocusChanged(boolean gainFocus, int direction, Rect previouslyFocusedRect) {
        super.onFocusChanged(gainFocus, direction, previouslyFocusedRect);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        final int keyCode = event.getKeyCode();
        switch (keyCode) {
//            case KeyEvent.KEYCODE_MENU:
//            case KeyEvent.KEYCODE_BACK:
//                if (event.getAction() == KeyEvent.ACTION_DOWN) {
//                    notifyExitListeners();
//                    return true;
//                }
//                break;
            case KeyEvent.KEYCODE_DPAD_CENTER:
            case KeyEvent.KEYCODE_ENTER:
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    // 如果用户选择有变化，更新列表页
                    notifyChangingListeners();
                    return true;
                }
                break;
            case KeyEvent.KEYCODE_DPAD_DOWN:
            case KeyEvent.KEYCODE_DPAD_UP:
                switch (event.getAction()) {
                    case KeyEvent.ACTION_DOWN:
                        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN
                                ? getCurrentItem() < mValueList.size() : getCurrentItem() > -1) {
                            requestFocus();
                            mLastHandledDownDpadKeyCode = keyCode;
                            if (mFlingScroller.isFinished()) {
                                changeValueByOne(keyCode == KeyEvent.KEYCODE_DPAD_DOWN);
                            }
                            return true;
                        }
                        break;
                    case KeyEvent.ACTION_UP:
                        if (mLastHandledDownDpadKeyCode == keyCode) {
                            mLastHandledDownDpadKeyCode = -1;
                            return true;
                        }
                        break;
                }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public void computeScroll() {
        Scroller scroller = mFlingScroller;
        if (scroller.isFinished()) {
            scroller = mAdjustScroller;
            if (scroller.isFinished()) {
                return;
            }
        }
        scroller.computeScrollOffset();
        int currentScrollerY = scroller.getCurrY();
        if (mPreviousScrollerY == 0) {
            mPreviousScrollerY = scroller.getStartY();
        }
        scrollBy(0, currentScrollerY - mPreviousScrollerY);
        mPreviousScrollerY = currentScrollerY;
        if (scroller.isFinished()) {
            onScrollerFinished(scroller);
        } else {
            invalidate();
        }
    }

    @Override
    public void scrollBy(int x, int y) {
        mCurrentScrollOffset = mFlingScroller.getCurrY() - mFlingScroller.getStartY();
    }

    @Override
    protected int computeVerticalScrollOffset() {
        return mCurrentScrollOffset;
    }

    @Override
    protected int computeVerticalScrollRange() {
        return (mValueList.size() - 1) * mSelectorElementHeight;
    }

    public void setValue(int current) {
        setValueInternal(current, false);
    }

    /**
     * Sets the current value of this CustomWheelView.
     *
     * @param current      The new value of the CustomWheelView.
     * @param notifyChange Whether to notify if the current value changed.
     */
    private void setValueInternal(int current, boolean notifyChange) {
        if (mCurrentItem == current) {
            return;
        }

        current = Math.max(current, 0);
        current = Math.min(current, mValueList.size() - 1);

        mCurrentItem = current;
        invalidate();
    }

    /**
     * Changes the current value by one which is increment or
     * decrement based on the passes argument.
     * decrement the current value.
     *
     * @param increment True to increment, false to decrement.
     */
    private void changeValueByOne(boolean increment) {

        if (!moveToFinalScrollerPosition(mFlingScroller)) {
            moveToFinalScrollerPosition(mAdjustScroller);
        }

        mPreviousScrollerY = 0;
        if (increment) {
            if (mCurrentItem < mValueList.size() - 1) {
                mCurrentItem++;
                mFlingScroller.startScroll(0, 0, 0, mSelectorElementHeight);
            }
        } else {
            if (mCurrentItem > 0) {
                mCurrentItem--;
                mFlingScroller.startScroll(0, 0, 0, -mSelectorElementHeight);
            }
        }
        invalidate();
    }

    /**
     * Move to the final position of a scroller. Ensures to force finish the scroller
     * and if it is not at its final position a scroll of the selector wheel is
     * performed to fast forward to the final position.
     *
     * @param scroller The scroller to whose final position to get.
     * @return True of the a move was performed, i.e. the scroller was not in final position.
     */
    private boolean moveToFinalScrollerPosition(Scroller scroller) {
        scroller.forceFinished(true);
        int amountToScroll = scroller.getFinalY() - scroller.getCurrY();
        int futureScrollOffset = (mCurrentScrollOffset + amountToScroll) % mSelectorElementHeight;
        int overshootAdjustment = mInitialScrollOffset - futureScrollOffset;
        if (overshootAdjustment != 0) {
            if (Math.abs(overshootAdjustment) > mSelectorElementHeight / 2) {
                if (overshootAdjustment > 0) {
                    overshootAdjustment -= mSelectorElementHeight;
                } else {
                    overshootAdjustment += mSelectorElementHeight;
                }
            }
            amountToScroll += overshootAdjustment;
            scrollBy(0, amountToScroll);
            return true;
        }
        return false;
    }

    /**
     * Callback invoked upon completion of a given <code>scroller</code>.
     */
    private void onScrollerFinished(Scroller scroller) {
        if (scroller == mFlingScroller) {
            ensureScrollWheelAdjusted();
        }
    }

    /**
     * Ensures that the scroll wheel is adjusted i.e. there is no offset and the
     * middle element is in the middle of the widget.
     *
     * @return Whether an adjustment has been made.
     */
    private boolean ensureScrollWheelAdjusted() {
        // adjust to the closest value
        int deltaY = mInitialScrollOffset - mCurrentScrollOffset;
        if (deltaY != 0) {
            mPreviousScrollerY = 0;
            if (Math.abs(deltaY) > mSelectorElementHeight / 2) {
                deltaY += (deltaY > 0) ? -mSelectorElementHeight : mSelectorElementHeight;
            }
            mAdjustScroller.startScroll(0, 0, 0, deltaY);
            invalidate();
            return true;
        }
        return false;
    }

    /**
     * Wheel changed listener interface.
     * <p>The mCurrentItemChanged() method is called whenever current wheel positions is changed:
     * <li> New Wheel position is set
     * <li> Wheel view is scrolled
     */
    public interface OnScrollListener {

        /**
         * Callback method to be invoked when current item changed
         */
        void onChanged();
    }

    public interface OnExitListener {

        /**
         * Callback method to be invoked when current item changed
         */
        void onExit();
    }
}
