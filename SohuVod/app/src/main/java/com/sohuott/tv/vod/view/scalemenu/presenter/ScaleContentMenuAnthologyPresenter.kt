package com.sohuott.tv.vod.view.scalemenu.presenter

import android.content.Context
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.base_leanback.persenter.DefaultPresenter
import com.base_leanback.viewholder.LeanBackViewHolder
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.getResDrawable
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.scaleXY
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentAnthologyMenuItem

/**
 * 具体选集
 */
class ScaleContentMenuAnthologyPresenter constructor(private val context: Context) :
    DefaultPresenter(R.layout.item_sacle_menu_anthology_layout) {
    override fun defaultBindViewHolder(
        viewHolder: LeanBackViewHolder,
        item: Any?,
        payloads: MutableList<Any>?
    ) {
        item as ScaleContentAnthologyMenuItem
        val layout = viewHolder.getView<ConstraintLayout>(R.id.cl_sacle_menu_anthology)
        val layoutRange = viewHolder.getView<ConstraintLayout>(R.id.cl_sacle_menu_anthology_range)
        val tvAnthology = viewHolder.getView<TextView>(R.id.tv_sacle_menu_anthology)
        tvAnthology.text=item.content
        layout.visible()
        layoutRange.gone()
        layout.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                layout.background=context.getResDrawable(R.drawable.bg_select_focus_e4705c_radius_11)
                v.scaleXY(1.1f)
            } else {
                layout.background=context.getResDrawable(R.drawable.bg_radius_10_color_2effffff)
                v.scaleXY(1f)
            }
        }
    }

}