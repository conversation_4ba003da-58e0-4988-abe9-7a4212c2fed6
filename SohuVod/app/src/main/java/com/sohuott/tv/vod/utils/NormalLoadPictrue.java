package com.sohuott.tv.vod.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.widget.ImageView;

import com.sohu.ott.base.lib_user.HeaderHelper;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.PayActivity;
import com.sohuott.tv.vod.data.HomeData;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.LoginQrModel;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.Util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Iterator;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;

/**
 * Created by xianrongchen on 2016/2/22.
 */
public class NormalLoadPictrue {

    private static final int MSG_PICTURE_DOWNLOAD = 1;
    private static final int TIME_OUT = 10 * 1000;

    private byte[] mPicByte;
    private String mUri = "";
    private ImageView mImageView;

    private Context mContext;
    private ImageHandler mHandler;
    private Consumer<LoginQrModel> mConsumer;
    private long mPaySourceComeFrom = PayActivity.PAY_SOURCE_UNKNOWN;
    private LoginUserInformationHelper mHelper;

    /**
     * 登录时使用，需要回调返回Header中数据。
     *
     * @param context
     * @param consumer
     */
    public NormalLoadPictrue(Context context, Consumer<LoginQrModel> consumer) {
        mContext = context;
        mHandler = new ImageHandler();
        mConsumer = consumer;
        mHelper = LoginUserInformationHelper.getHelper(mContext.getApplicationContext());
    }

    /**
     * 非登录，下载图片时使用(仅下载图片，无监听回调)
     *
     * @param context
     */
    public NormalLoadPictrue(Context context) {
        mContext = context;
        mHandler = new ImageHandler();
        mHelper = LoginUserInformationHelper.getHelper(mContext.getApplicationContext());
    }

    public void setPaySourceComeFrom(long paySourceComeFrom) {
        mPaySourceComeFrom = paySourceComeFrom;
    }

    private class ImageHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            LibDeprecatedLogger.d("msg.what--" + msg.what);
            if (msg.what == MSG_PICTURE_DOWNLOAD) {
                LibDeprecatedLogger.d("View status: " + (null != mImageView));
                if (null != mPicByte) {
                    Bitmap bitmap = BitmapFactory.decodeByteArray(mPicByte, 0, mPicByte.length);
                    LibDeprecatedLogger.d("Bitmap: " + bitmap);
                    if (null != mImageView) {
                        mImageView.setImageBitmap(bitmap);
                    }
                } else {
                    if (null != mImageView) {
                        mImageView.setImageResource(R.drawable.bg_launcher_poster);
                    }
                }
            }
        }
    }

    private Runnable mRunnable = new Runnable() {
        @Override
        public void run() {
            try {
                URL url = new URL(mUri);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("GET");

                Map<String, String> map = HeaderHelper.getHeaders();
                long channeled = mPaySourceComeFrom + 1000;
                if (!HomeData.sBootOrHomeIsStarted) {
                    String enterId = RequestManager.getInstance().getEnterId();
                    if (!TextUtils.isEmpty(enterId)) {
                        enterId = enterId.substring(0, 1);
                        if (!TextUtils.equals(enterId, "0") && !TextUtils.equals(enterId, "2")) {
                            channeled = mPaySourceComeFrom + 2000;
                        }
                    }
                }
                map.put("channeled", String.valueOf(channeled));
                map.put("loginType", String.valueOf(Util.getServerLoginUtype(mHelper.getUtype())));
                Iterator<Map.Entry<String, String>> iter = map.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry<String, String> next = iter.next();
                    conn.setRequestProperty(next.getKey(), next.getValue());
                }

                conn.setReadTimeout(TIME_OUT);

                if (conn.getResponseCode() == 200) {
                    LibDeprecatedLogger.d("Load QR ResponseCode 200");
                    if (mConsumer != null) {
//                        String token = conn.getHeaderField("code");
//                        qrcode = conn.getHeaderField("qrcode");
                        LoginQrModel qrModel = new LoginQrModel();
                        qrModel.setToken(conn.getHeaderField("code"));
                        qrModel.setQrcode(conn.getHeaderField("qrcode"));
                        LibDeprecatedLogger.d(qrModel.toString());
                        //if (!TextUtils.isEmpty(token)) {
                        Observable.just(qrModel)
                                .subscribeOn(AndroidSchedulers.mainThread())
                                .subscribe(mConsumer);
                        //}
                    }
                    InputStream fis = conn.getInputStream();
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    byte[] bytes = new byte[1024];
                    int length = -1;
                    while ((length = fis.read(bytes)) != -1) {
                        bos.write(bytes, 0, length);
                    }
                    mPicByte = bos.toByteArray();
                    int byteSize = mPicByte == null ? -1 : mPicByte.length;
                    LibDeprecatedLogger.d("Get byte size: " + byteSize);
                    bos.close();
                    fis.close();
                    sendMessage();
                } else {
                    LibDeprecatedLogger.w("Load QR fail! Code: " + conn.getResponseCode());
                }
            } catch (IOException e) {
                LibDeprecatedLogger.e("Load QR picture fail!", e);
            }
        }
    };

    private void sendMessage() {
        LibDeprecatedLogger.d("Send msg after load QR!");
        Message message = new Message();
        message.what = MSG_PICTURE_DOWNLOAD;
        mHandler.sendMessage(message);
    }

    public void getPicture(String uri, ImageView imageView) {
        mUri = uri;
        mImageView = imageView;
        new Thread(mRunnable).start();
    }

    public void release() {
        mHandler = null;
        mImageView = null;
        mPicByte = null;
        mConsumer = null;
        mHelper = null;
    }

}