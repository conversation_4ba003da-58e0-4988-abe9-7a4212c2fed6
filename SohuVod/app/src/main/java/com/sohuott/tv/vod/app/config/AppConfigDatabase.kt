package com.sohuott.tv.vod.app.config

import com.sohu.lib_utils.PrefUtil

object AppConfigDatabase {
    private const val CONFIG_INFO_PLAYER_CONFIG = "config_info_player_config"
    private const val CONFIG_INFO_PLAYER_RESOLUTION_ID = "config_info_player_resolution_id"
    private const val CONFIG_INFO_PLAYER_RESOLUTION_ID_BEFORE =
        "config_info_player_resolution_id_before"
    private const val CONFIG_INFO_PLAYER_DEFAULT =
        "{\"resolutions\":[ {\"name\":\"标清 360P\",\"isVip\":false,\"isLogin\":false,\"enable\":true,\"enableTips\":false,\"tips\":\"\",\"priority\":0,\"serviceIds\":[2,263],\"serviceIdPriority\":null,\"id\":0,\"teenagerEnable\":true}, {\"name\":\"标清 540P\",\"isVip\":false,\"isLogin\":false,\"enable\":true,\"enableTips\":false,\"tips\":\"\",\"priority\":1,\"serviceIds\":[1,261],\"serviceIdPriority\":null,\"id\":1,\"default\":true,\"teenagerEnable\":true}, {\"name\":\"准高清 720P\",\"isVip\":false,\"isLogin\":true,\"enable\":true,\"enableTips\":false,\"tips\":\"\",\"priority\":2,\"serviceIds\":[21,265],\"serviceIdPriority\":null,\"id\":2,\"teenagerEnable\":true}, {\"name\":\"高清 1080P\",\"isVip\":true,\"isLogin\":true,\"enable\":true,\"enableTips\":false,\"tips\":\"\",\"priority\":3,\"serviceIds\":[31,267,32,269],\"serviceIdPriority\":[32,269],\"id\":3,\"teenagerEnable\":false}, {\"name\":\"高清 HDR 1080P\",\"isVip\":true,\"isLogin\":true,\"enable\":true,\"enableTips\":false,\"tips\":\"了解HDR\",\"priority\":4,\"serviceIds\":[33,285],\"serviceIdPriority\":null,\"id\":4,\"teenagerEnable\":false}, {\"name\":\"超高清 4K\" ,\"isVip\":true,\"isLogin\":true,\"enable\":false,\"enableTips\":false,\"tips\":\"\",\"priority\":5,\"serviceIds\":[51,53],\"serviceIdPriority\":null,\"id\":5,\"default\":false,\"teenagerEnable\":false}]}"


    fun putAppPlayerResolutionConfig(config: String) {
        PrefUtil.putString(CONFIG_INFO_PLAYER_CONFIG, config)
    }

    fun getAppPlayerResolutionConfig(): String {
        return PrefUtil.getString(CONFIG_INFO_PLAYER_CONFIG, CONFIG_INFO_PLAYER_DEFAULT)
    }

    fun putDefaultResolutionId(id: Int) {
        PrefUtil.putInt(CONFIG_INFO_PLAYER_RESOLUTION_ID, id)
    }

    fun putUserBeforeResolutionId(id: Int) {
        PrefUtil.putInt(CONFIG_INFO_PLAYER_RESOLUTION_ID_BEFORE, id)
    }

    fun getDefaultResolutionId(): Int {
        return PrefUtil.getInt(CONFIG_INFO_PLAYER_RESOLUTION_ID, 1)
    }

    fun getUserBeforeResolutionId(): Int {
        return PrefUtil.getInt(CONFIG_INFO_PLAYER_RESOLUTION_ID_BEFORE, -1)
    }
}