package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Rect;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.sohuott.tv.vod.lib.utils.Util;

/**
 * Created by XiyingCao on 16-2-4.
 * <p/>
 * {@link LinearLayoutManager} extension which introduces workaround for focus finding bug when
 * navigating with dpad.
 */

public class NewVideoDetailManager extends LinearLayoutManager {

    Context mContext;
    boolean mInTouchMode;
    public NewVideoDetailManager(Context context) {
        super(context);
        mContext = context;
        mInTouchMode=Util.isSupportTouchVersion(context);
    }

    public void setCustomPadding(int paddingStart, int paddingEnd) {
    }


    @Override
    public boolean requestChildRectangleOnScreen(RecyclerView parent, View child, Rect rect, boolean immediate) {
        return false;
    }

    /**
     *
     * @param parent
     * @param state
     * @param child
     * @param focused
     * @return  true if the default scroll behavior should be suppressed
     */
    @Override
    public boolean onRequestChildFocus(RecyclerView parent, RecyclerView.State state, View child, View focused) {
        return mInTouchMode?true:super.onRequestChildFocus(parent, state, child, focused);
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        try {
            super.onLayoutChildren(recycler, state);
        } catch (IndexOutOfBoundsException e) {
            e.printStackTrace();
        }
    }
}