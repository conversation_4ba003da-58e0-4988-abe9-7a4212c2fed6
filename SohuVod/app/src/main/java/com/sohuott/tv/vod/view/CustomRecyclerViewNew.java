package com.sohuott.tv.vod.view;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.animation.Interpolator;
import android.view.animation.LinearInterpolator;

public class CustomRecyclerViewNew extends RecyclerView {

    private long mLastTimeMillis;
    private boolean isLongPressed;

    public CustomRecyclerViewNew(Context context) {
        super(context);
    }

    public CustomRecyclerViewNew(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomRecyclerViewNew(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return super.dispatchKeyEvent(event) || executeKey(event);
    }

//    @Override
//    public void smoothScrollBy(int dx, int dy, Interpolator interpolator) {
//        super.smoothScrollBy(dx, dy, new LinearInterpolator() {
//            @Override
//            public float getInterpolation(float input) {
//                if (isLongPressed) {
//                    return 1.5f * input + 0.06f;
//                } else {
//                    return input;
//                }
//            }
//        });
//    }

    public void isLongPressed(boolean isLongPressed) {
        this.isLongPressed = isLongPressed;
    }

    public boolean getIsLongPressed() {
        return false;
    }

    //放慢焦点响应时间，防止快速滑动时，焦点寻找失败
    private boolean executeKey(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN
                && (event.getKeyCode() == KeyEvent.KEYCODE_DPAD_DOWN
                || event.getKeyCode() == KeyEvent.KEYCODE_DPAD_UP
                || event.getKeyCode() == KeyEvent.KEYCODE_DPAD_RIGHT)) {
            boolean isInterrupt = System.currentTimeMillis() - mLastTimeMillis <= 150;
            if (!isInterrupt) {
                mLastTimeMillis = System.currentTimeMillis();
            }
            return isInterrupt;
        }
        return false;
    }

}