package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import com.sh.ott.video.player.PlayerConstants

class LauncherVideoStartPreparingComponent constructor(context: Context) :
    BaseVideoStartPreparingComponent(context) {

    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        when (playState) {
            PlayerConstants.VideoState.PLAYBACK_COMPLETED,
            PlayerConstants.VideoState.STOPPED,
            PlayerConstants.VideoState.ERROR,
            PlayerConstants.VideoState.PAUSED,
            PlayerConstants.VideoState.PLAYING_BACK,
            PlayerConstants.VideoState.PLAYING -> {
                hide()
            }

//                PlayerConstants.VideoState.BUFFERING -> {
//                    if (isInitShowState) return
//                    mPlayerLoadingView?.visibility = GONE
//                    gone()
//                }
////
//                PlayerConstants.VideoState.BUFFERED -> {
//                    isInitShowState = false
//                    mPlayerLoadingView?.visibility = GONE
//                    gone()
//                }

            else -> {
                show()
            }
        }
    }
}