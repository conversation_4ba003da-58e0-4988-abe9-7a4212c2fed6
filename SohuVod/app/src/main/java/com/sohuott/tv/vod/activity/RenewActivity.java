package com.sohuott.tv.vod.activity;

import static com.sohuott.tv.vod.activity.PayActivity.PAY_SOURCE_UNKNOWN;

import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.sohu.ott.base.lib_user.UserInfoHelper;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.LoginQrModel;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.lib.utils.UrlWrapper;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.LoadQrPicture;
import com.sohuott.tv.vod.widget.CornerTagImageView;

import io.reactivex.functions.Consumer;

/**
 * Created by xianrongchen on 2016/3/1.
 */
public class RenewActivity extends BaseActivity{
    private static final String TAG = RenewActivity.class.getSimpleName();
    private CornerTagImageView mQrCodeImage;
    private TextView mTitle, mTitleDetail;
    private long mPaySourceComeFrom = PAY_SOURCE_UNKNOWN;
    private LoginUserInformationHelper mHelper;
    public static final String PARAM_AC_TYPE = "ac_type";
    public static final int ACTYPE_RENEW = 1;
    public static final int ACTYPE_EXCHANGE = 2;
    private int mAcType = ACTYPE_RENEW;
    private boolean mIsLogin;
    private boolean mIsFirstBoot = true;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppLogger.d(TAG, "onCreate");
        setContentView(R.layout.activity_renew);
        mHelper = LoginUserInformationHelper.getHelper(getApplicationContext());
        mIsLogin = mHelper.getIsLogin();
        if(!mIsLogin){
            ActivityLauncher.startLoginActivity(this);
        }
        initView();
//        initData();
        setDisplay();
    }


    //设置窗口大小
    private void setDisplay() {
        //设置弹出窗口与屏幕对齐
        Window win = this.getWindow();
        int density = (int)(getResources().getDisplayMetrics().density);
        //设置内边距，这里设置为0
        win.getDecorView().setPadding(1 * density, 1 * density, 1 * density, 1 * density);
        WindowManager.LayoutParams lp = win.getAttributes();
        //设置窗口宽度
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        //设置窗口高度
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        //设置Dialog位置
        lp.gravity = Gravity.TOP | Gravity.LEFT;
        win.setAttributes(lp);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (getIntent() != null){
            setIntent(intent);
        }
//        initData();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(!mIsFirstBoot) {
            if (!mHelper.getIsLogin()) {
                //回到此页面如果没登录，就直接退出
                finish();
            }
        }
        if (mHelper.getIsLogin()) {
            initData();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        mIsFirstBoot = false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.dialog_scale_in, R.anim.dialog_scale_out);
    }

    private void initView() {
        mQrCodeImage = (CornerTagImageView) findViewById(R.id.renew_qrcode_image);
        mTitle = (TextView) findViewById(R.id.title);
        mTitleDetail = (TextView) findViewById(R.id.title_detail);
    }

    private void initData() {
        Intent intent = getIntent();
        if (null != intent) {
                mAcType = intent.getIntExtra(PARAM_AC_TYPE, ACTYPE_RENEW);
        }
        if (mAcType == ACTYPE_EXCHANGE) {
            mTitle.setText("兑换码激活");
            mTitleDetail.setText("手机扫码进行会员激活，操作更简单");
        }


        String qrCodeImageUrl = "";
        String singleQrCodeImageUrl = "";
        String apiKey = Util.getSohuApiKey(this.getApplicationContext());

        if (mAcType == ACTYPE_EXCHANGE){
            qrCodeImageUrl = UrlWrapper.getScanActivationQrCodeUrl(560, 560,
                    UserInfoHelper.getGid(), mHelper.getLoginPassport(), mHelper.getLoginToken(),apiKey);
            AppLogger.d(TAG, "qrCodeImageUrl ? " + qrCodeImageUrl);
            if (null != qrCodeImageUrl && !qrCodeImageUrl.trim().equals("")) {
                LoadQrPicture loadQrPicture = new LoadQrPicture(this, mLoadSucceedConsumer);
                loadQrPicture.setPaySourceComeFrom(mPaySourceComeFrom);
                loadQrPicture.getPicture(qrCodeImageUrl, mQrCodeImage);
                AppLogger.d(TAG, "loadQrPicture.getPicture ? " + qrCodeImageUrl);
            }
        } else {
            qrCodeImageUrl = UrlWrapper.getScanRenewQrCodeUrl(560, 560,
                    UserInfoHelper.getGid(), mHelper.getLoginPassport(), mHelper.getLoginToken(),apiKey);
            AppLogger.d(TAG, "qrCodeImageUrl ? " + qrCodeImageUrl);
            if (null != qrCodeImageUrl && !qrCodeImageUrl.trim().equals("")) {
                LoadQrPicture loadQrPicture = new LoadQrPicture(this, mLoadSucceedConsumer);
                loadQrPicture.setPaySourceComeFrom(mPaySourceComeFrom);
                loadQrPicture.getPicture(qrCodeImageUrl, mQrCodeImage);
                AppLogger.d(TAG, "loadQrPicture.getPicture ? " + qrCodeImageUrl);
            }
        }

    }
    private String mPollingToken;
    private String mQrcode;
    Consumer<LoginQrModel> mLoadSucceedConsumer = new Consumer<LoginQrModel>() {
        @Override
        public void accept(LoginQrModel qrModel) {
            if (qrModel == null) {
                LibDeprecatedLogger.w("Qr model is null !");
                return;

            } else if (qrModel.getToken() == null) {
                LibDeprecatedLogger.w("Token is null !");
                return;
            } else if (qrModel.getQrcode() == null) {
                LibDeprecatedLogger.w("Qrcode is null !");
                return;
            }
            mPollingToken = qrModel.getToken();
            mQrcode = qrModel.getQrcode();
        }
    };

}
