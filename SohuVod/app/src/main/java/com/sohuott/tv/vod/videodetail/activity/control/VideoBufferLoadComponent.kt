package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.widget.ImageView
import com.nineoldandroids.animation.Animator
import com.nineoldandroids.animation.AnimatorListenerAdapter
import com.nineoldandroids.animation.AnimatorSet
import com.nineoldandroids.animation.ObjectAnimator
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible

/**
 * 缓冲展示动画
 */
class VideoBufferLoadComponent constructor(context: Context) :
    BaseControlComponent(context) {
    private var mLoadingView: ImageView? = null
    private var bouncer: AnimatorSet? = null

    //是否是切换清晰度时
    var isChangeResolutionApp = false

    init {
        setBackgroundResource(R.color.transparent)
        layoutInflater.inflate(R.layout.video_component_player_loading, this, true)
        gone()
        mLoadingView = findViewById<ImageView>(R.id.player_loading_center)
        onCreateAnimatorSet()

    }

    private var mPlayState: Int = PlayerConstants.VideoState.IDLE
    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        mPlayState = playState
        updateView()
    }

    private fun onCreateAnimatorSet() {
        val rotate = ObjectAnimator.ofFloat(mLoadingView, "rotation", 0f, 360f)
        val duration: Long = 550
        rotate.setDuration(duration)
        val transUp = ObjectAnimator.ofFloat(
            mLoadingView,
            "translationY",
            0f,
            -resources.getDimensionPixelSize(R.dimen.y46).toFloat()
        )
        transUp.setDuration(duration / 2)
        val transDown = ObjectAnimator.ofFloat(
            mLoadingView,
            "translationY",
            -resources.getDimensionPixelSize(R.dimen.y46).toFloat(),
            0f
        )
        transDown.setDuration(duration / 2)
        bouncer = AnimatorSet()
        bouncer?.play(transUp)?.with(rotate)
        bouncer?.play(transDown)?.after(transUp)
        bouncer?.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                bouncer?.start()
            }
        })
    }

    private fun updateView() {
//        if (mPlayState == PlayerConstants.VideoState.PLAYING||mPlayState == PlayerConstants.VideoState.PLAYING_BACK) {
//            isChangeResolutionApp = false
//        }
//        if (isChangeResolutionApp) return
        if (mPlayState == PlayerConstants.VideoState.BUFFERING) {
            show()
        } else {
            hide()
        }
    }

    fun show() {
        visible()
        if (bouncer == null) {
            onCreateAnimatorSet()
        }
        if (bouncer != null && !bouncer!!.isRunning()) {
            bouncer!!.start()
        }
        AppLogger.d("video buffer show ")
    }

    fun hide() {
        gone()
        if (bouncer != null && bouncer!!.isRunning()) {
            bouncer!!.end()
        }
        AppLogger.d("video buffer hide ")
    }

}