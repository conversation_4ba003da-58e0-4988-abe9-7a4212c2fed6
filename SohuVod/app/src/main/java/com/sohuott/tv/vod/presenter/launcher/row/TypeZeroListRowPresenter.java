package com.sohuott.tv.vod.presenter.launcher.row;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import androidx.leanback.widget.BaseOnItemViewClickedListener;
import androidx.leanback.widget.HorizontalGridView;
import androidx.leanback.widget.Presenter;
import androidx.leanback.widget.RowHeaderPresenter;
import androidx.leanback.widget.RowPresenter;

import com.lib_statistical.manager.RequestManager;
import com.lib_statistical.model.EventInfo;
import com.sohu.lib_utils.StringUtil;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.ListUserRelatedActivity;
import com.sohuott.tv.vod.base.BaseListRowPresenter;
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory;
import com.sohuott.tv.vod.lib.model.ContentGroup;
import com.sohuott.tv.vod.lib.model.HomeRecommendBean;
import com.sohuott.tv.vod.lib.model.PgcAlbumInfo;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.widget.HomeViewJump;

import java.util.HashMap;


public class TypeZeroListRowPresenter extends BaseListRowPresenter {
    private Context mContext;
    private LoginUserInformationHelper mHelper;

    @SuppressLint("RestrictedApi")
    @Override
    protected void initializeRowViewHolder(RowPresenter.ViewHolder holder) {
        super.initializeRowViewHolder(holder);
        final ViewHolder rowViewHolder = (ViewHolder) holder;
        mContext = rowViewHolder.getGridView().getContext();

        mHelper = LoginUserInformationHelper.getHelper(mContext);
        rowViewHolder.getGridView().setHorizontalSpacing(48);
        rowViewHolder.getGridView().setFocusScrollStrategy(HorizontalGridView.FOCUS_SCROLL_ITEM);
        rowViewHolder.getGridView().setClipChildren(false);
        rowViewHolder.getGridView().setClipToPadding(false);
        RowHeaderPresenter.ViewHolder vh = rowViewHolder.getHeaderViewHolder();
//        FocusHighlightHelper.setupBrowseItemFocusHighlight(rowViewHolder.getBridgeAdapter(),
//                FocusHighlight.ZOOM_FACTOR_XSMALL, false);

        TextView textView = (TextView) vh.view.findViewById(R.id.row_header);
        textView.setTextSize(mContext.getResources().getDimensionPixelSize(R.dimen.y22));
        textView.setPadding(0, 0, 0, 48);
        textView.setSingleLine();
        textView.setMarqueeRepeatLimit(-1);
        textView.setMaxWidth(mContext.getResources().getDimensionPixelOffset(R.dimen.x1752));
        textView.setEllipsize(TextUtils.TruncateAt.END);

        AppLogger.d("TypeZeroListRowPresenter:initializeRowViewHolder");
        setOnItemViewClickedListener(new BaseOnItemViewClickedListener() {
            @Override
            public void onItemClicked(Presenter.ViewHolder itemViewHolder,
                                      Object item, RowPresenter.ViewHolder rowViewHolder, Object row) {
                AppLogger.d("onItemClicked:");
                if (item instanceof PlayHistory){
                    if (mHelper.getIsLogin()) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10138, "clk"), ((PlayHistory) item).pathInfo, null, null);
                        ActivityLauncher.startListUserRelatedActivity(((ViewHolder) rowViewHolder).getGridView().getContext(), ListUserRelatedActivity.LIST_INDEX_HISTORY);
                    } else {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10136, "clk"), ((PlayHistory)item).pathInfo, null, null);


                        ActivityLauncher.startLoginActivity(mContext, Constant.LAUNCHER_SOURCE, Integer.parseInt(((PlayHistory) item).pathInfo.get("pageId").toString()));
                    }
                } else if (item instanceof ContentGroup.DataBean.ContentsBean) {
                    if (((ContentGroup.DataBean.ContentsBean) item).type.equals(Constant.TYPE_7)){
                        RequestManager.getInstance().onAllEvent(new EventInfo(10160, "clk"), ((ContentGroup.DataBean.ContentsBean) item).pathInfo, null, ((ContentGroup.DataBean.ContentsBean) item).memoInfo);
                    } else if(((ContentGroup.DataBean.ContentsBean) item).type.equals(StringUtil.toString(HomeRecommendBean.COURSES_HISTORY_TYPE))) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10193, "clk"), ((ContentGroup.DataBean.ContentsBean) item).pathInfo, null, null);
                    } else if(((ContentGroup.DataBean.ContentsBean) item).type.equals(StringUtil.toString(HomeRecommendBean.COURSES_FAVORITE_TYPE))) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10194, "clk"), ((ContentGroup.DataBean.ContentsBean) item).pathInfo, null, null);
                    } else if(((ContentGroup.DataBean.ContentsBean) item).type.equals(StringUtil.toString(HomeRecommendBean.COURSES_ORDER_TYPE))) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10195, "clk"), ((ContentGroup.DataBean.ContentsBean) item).pathInfo, null, null);
                    } else {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), ((ContentGroup.DataBean.ContentsBean) item).pathInfo, ((ContentGroup.DataBean.ContentsBean) item).objectInfo, ((ContentGroup.DataBean.ContentsBean) item).memoInfo);
                    }

                    HomeViewJump.clickAlbum(((ViewHolder) rowViewHolder).getGridView().getContext(), item, 0, false, ((ViewHolder) rowViewHolder).getGridView().getSelectedPosition());
                } else if (item instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean) {
                    HashMap<String, String> objectInfo = new HashMap<String, String>();
                    objectInfo.put("type", "视频");
                    objectInfo.put("vid", StringUtil.toString(((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).tvVerId));
                    objectInfo.put("playlistid", StringUtil.toString(((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).id));

                    if (!StringUtil.isEmpty(((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pdna)) {
                        HashMap<String, String> memoInfo = new HashMap<String, String>();
                        memoInfo.put("pdna", ((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pdna);
                        RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), ((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pathInfo, objectInfo, memoInfo);
                    } else {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), ((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pathInfo, objectInfo, null);
                    }
                    HomeViewJump.clickAlbum(((ViewHolder) rowViewHolder).getGridView().getContext(), (ContentGroup.DataBean.ContentsBean.AlbumListBean) item, ((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pdna);
                } else if (item instanceof PgcAlbumInfo.DataEntity) {
                    RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), ((PgcAlbumInfo.DataEntity) item).pathInfo, ((PgcAlbumInfo.DataEntity) item).objectInfo, null);

                    HomeViewJump.clickAlbum(((ViewHolder) rowViewHolder).getGridView().getContext(), (PgcAlbumInfo.DataEntity) item, 0, false, ((ViewHolder) rowViewHolder).getGridView().getSelectedPosition());
                } else if (item instanceof ContentGroup.DataBean.ContentsBean.ProducersListBean) {
                    //出品人列表
                    ActivityLauncher.startProducerActivity(((ViewHolder) rowViewHolder).getGridView().getContext(), ((ContentGroup.DataBean.ContentsBean.ProducersListBean) item).uid);
                    RequestManager.getInstance().onAllEvent(new EventInfo(10133, "clk"),
                            ((ContentGroup.DataBean.ContentsBean.ProducersListBean)item).pathInfo,
                            ((ContentGroup.DataBean.ContentsBean.ProducersListBean)item).objectInfo, null);
                }
            }
        });
    }

    @Override
    protected void onBindRowViewHolder(RowPresenter.ViewHolder holder, Object item) {
        super.onBindRowViewHolder(holder, item);
        RowHeaderPresenter.ViewHolder vh = holder.getHeaderViewHolder();

        TextView textView = (TextView) vh.view.findViewById(R.id.row_header);
        textView.setTextColor(mContext.getResources().getColor(R.color.launcher_name));
        holder.setOnItemViewClickedListener(new BaseOnItemViewClickedListener() {
            @Override
            public void onItemClicked(Presenter.ViewHolder itemViewHolder, Object item, RowPresenter.ViewHolder
                    rowViewHolder, Object row) {
                AppLogger.d("item click from dummy listener: this should never happen!");
                if (item instanceof ContentGroup.DataBean.ContentsBean.AlbumListBean) {

                    HashMap<String, String> objectInfo = new HashMap<String, String>();
                    objectInfo.put("type", "视频");
                    objectInfo.put("vid", StringUtil.toString(((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).tvVerId));
                    objectInfo.put("playlistid", StringUtil.toString(((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).id));


                    if (!StringUtil.isEmpty(((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pdna)) {
                        HashMap<String, String> memoInfo = new HashMap<String, String>();
                        memoInfo.put("pdna", ((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pdna);
                        RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), ((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pathInfo, objectInfo, memoInfo);
                    } else {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10147, "clk"), ((ContentGroup.DataBean.ContentsBean.AlbumListBean) item).pathInfo, objectInfo, null);
                    }

                    HomeViewJump.clickAlbum(((ViewHolder) rowViewHolder).getGridView().getContext(), (ContentGroup.DataBean.ContentsBean.AlbumListBean) item, 0, false, ((ViewHolder) rowViewHolder).getGridView().getSelectedPosition());
                } else if (item instanceof PlayHistory){
                    if (mHelper.getIsLogin()) {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10138, "clk"), ((PlayHistory) item).pathInfo, null, null);
                        ActivityLauncher.startListUserRelatedActivity(((ViewHolder) rowViewHolder).getGridView().getContext(), ListUserRelatedActivity.LIST_INDEX_HISTORY);
                    } else {
                        RequestManager.getInstance().onAllEvent(new EventInfo(10136, "clk"), ((PlayHistory)item).pathInfo, null, null);
                        ActivityLauncher.startLoginActivity(mContext, Constant.LAUNCHER_SOURCE, Integer.parseInt(((PlayHistory) item).pathInfo.get("pageId").toString()));
                    }
                }
            }
        });
    }
}
