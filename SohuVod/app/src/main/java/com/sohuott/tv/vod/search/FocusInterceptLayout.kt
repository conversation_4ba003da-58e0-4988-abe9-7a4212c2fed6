package com.sohuott.tv.vod.search

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout

class FocusInterceptLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {


    override fun focusSearch(focused: View?, direction: Int): View? {
        val nextFocus = super.focusSearch(focused, direction)
        return if (nextFocus == null || focused == null ||!isDescendantView(nextFocus)) {
            // 如果下一个焦点为空，或者当前聚焦的 View 为空，或者下一个焦点不是当前布局的子视图或子视图的子视图，
            // 则返回当前聚焦的 View，以防止焦点传递到外部。
            focused
        } else {
            // 否则，返回下一个可聚焦的 View
            nextFocus
        }
    }

    private fun isDescendantView(view: View): Boolean {
        var currentView: View? = view
        while (currentView != null) {
            if (currentView.parent === this) {
                return true
            }
            currentView = currentView.parent as? View
        }
        return false
    }
}