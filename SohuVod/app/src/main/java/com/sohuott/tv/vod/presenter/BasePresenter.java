package com.sohuott.tv.vod.presenter;

import com.sohuott.tv.vod.view.IView;

//import org.reactivestreams.Subscription;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * Created by hpb on 2017/7/12.
 */
public abstract class BasePresenter<T extends IView>implements  IPresenter<T> {
    protected  T mvpView;
    protected CompositeDisposable compositeDisposable=new CompositeDisposable();


    @Override
    public void attachView(T mvpView) {
        this.mvpView=mvpView;
    }

    @Override
    public void detachView() {
        compositeDisposable.clear();
        this.mvpView=null;
    }


    @Override
    public boolean isDetachedView() {
        return mvpView==null;
    }

    protected void addDisposable(Disposable disposable) {
        compositeDisposable.add(disposable);
    }

    protected void clearDisposable() {
        compositeDisposable.clear();
    }

}
