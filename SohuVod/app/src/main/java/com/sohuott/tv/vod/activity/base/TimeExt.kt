package com.sohuott.tv.vod.activity.base

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*

/**
 *
 * @Description
 * @date 2022/4/15 10:02
 * <AUTHOR>
 * @Version 1.0
 */

fun countDownCoroutines(
    total: Long, onTick: (Long) -> Unit, onFinish: () -> Unit,
    scope: CoroutineScope = GlobalScope
): Job {
    return flow {
        for (i in total downTo 0) {
            emit(i)
            delay(1000)
        }
    }.flowOn(Dispatchers.Default)
        .onCompletion { onFinish.invoke() }
        .onEach { onTick.invoke(it) }
        .flowOn(Dispatchers.Main)
        .launchIn(scope)
}