package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Rect;

import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

/**
 * Created by f<PERSON><PERSON><PERSON> on 17-6-29.
 */

public class SearchResultGridLayoutManager extends GridLayoutManager {

    public SearchResultGridLayoutManager(Context context, int spanCount) {
        super(context, spanCount);
    }

    @Override
    public View onFocusSearchFailed(View focused, int focusDirection,
                                    RecyclerView.Recycler recycler, RecyclerView.State state) {
        View prevFocusedChild = findContainingItemView(focused);
        if (prevFocusedChild == null) {
            return null;
        }
        LayoutParams lp = (LayoutParams) prevFocusedChild.getLayoutParams();
        final int prevSpanStart = lp.getSpanIndex();
        final int prevSpanEnd = lp.getSpanIndex() + lp.getSpanSize();
        View view2 = super.onFocusSearchFailed(focused, focusDirection, recycler, state);
        if (view2 == null) {
            return null;
        }
        // LinearLayoutManager finds the last child. What we want is the child which has the same
        // spanIndex.
        final int layoutDir = convertFocusDirectionToLayoutDirection2(focusDirection);
        final boolean ascend = (layoutDir == 1) != false;
        final int start, inc, limit;
        if (ascend) {
            start = getChildCount() - 1;
            inc = -1;
            limit = -1;
        } else {
            start = 0;
            inc = 1;
            limit = getChildCount();
        }
        final boolean preferLastSpan = getOrientation() == VERTICAL && isLayoutRTL();

        // The focusable candidate to be picked if no perfect focusable candidate is found.
        // The best focusable candidate is the one with the highest amount of span overlap with
        // the currently focused view.
        View focusableWeakCandidate = null; // somewhat matches but not strong
        int focusableWeakCandidateSpanIndex = -1;
        int focusableWeakCandidateOverlap = 0; // how many spans overlap

        // The unfocusable candidate to become visible on the screen next, if no perfect or
        // weak focusable candidates are found to receive focus next.
        // We are only interested in partially visible unfocusable views. These are views that are
        // not fully visible, that is either partially overlapping, or out-of-bounds and right below
        // or above RV's padded bounded area. The best unfocusable candidate is the one with the
        // highest amount of span overlap with the currently focused view.
        View unfocusableWeakCandidate = null; // somewhat matches but not strong
        int unfocusableWeakCandidateSpanIndex = -1;
        int unfocusableWeakCandidateOverlap = 0; // how many spans overlap

        // The span group index of the start child. This indicates the span group index of the
        // next focusable item to receive focus, if a focusable item within the same span group
        // exists. Any focusable item beyond this group index are not relevant since they
        // were already stored in the layout before onFocusSearchFailed call and were not picked
        // by the focusSearch algorithm.
        int focusableSpanGroupIndex = getSpanGroupIndex2(recycler, state, getViewPosition(getChildAt(start)));
        for (int i = start; i != limit; i += inc) {
            int spanGroupIndex = getSpanGroupIndex2(recycler, state, getViewPosition(getChildAt(i)));
            View candidate = getChildAt(i);
            if (candidate == prevFocusedChild) {
                break;
            }

            if (candidate.hasFocusable() && spanGroupIndex != focusableSpanGroupIndex) {
                // We are past the allowable span group index for the next focusable item.
                // The search only continues if no focusable weak candidates have been found up
                // until this point, in order to find the best unfocusable candidate to become
                // visible on the screen next.
                if (focusableWeakCandidate != null) {
                    break;
                }
                continue;
            }

            final LayoutParams candidateLp = (LayoutParams) candidate.getLayoutParams();
            final int candidateStart = candidateLp.getSpanIndex();
            final int candidateEnd = candidateLp.getSpanIndex() + candidateLp.getSpanSize();
            if (candidate.hasFocusable() && candidateStart == prevSpanStart
                    && candidateEnd == prevSpanEnd) {
                return candidate; // perfect match
            }
            boolean assignAsWeek = false;
            if ((candidate.hasFocusable() && focusableWeakCandidate == null)
                    || (!candidate.hasFocusable() && unfocusableWeakCandidate == null)) {
                assignAsWeek = true;
            } else {
                int maxStart = Math.max(candidateStart, prevSpanStart);
                int minEnd = Math.min(candidateEnd, prevSpanEnd);
                int overlap = minEnd - maxStart;
                if (candidate.hasFocusable()) {
                    if (overlap > focusableWeakCandidateOverlap) {
                        assignAsWeek = true;
                    } else if (overlap == focusableWeakCandidateOverlap
                            && preferLastSpan == (candidateStart
                            > focusableWeakCandidateSpanIndex)) {
                        assignAsWeek = true;
                    }
                } else if (focusableWeakCandidate == null
                        && isViewPartiallyVisible(candidate, false, true)) {
                    if (overlap > unfocusableWeakCandidateOverlap) {
                        assignAsWeek = true;
                    } else if (overlap == unfocusableWeakCandidateOverlap
                            && preferLastSpan == (candidateStart
                            > unfocusableWeakCandidateSpanIndex)) {
                        assignAsWeek = true;
                    }
                }
            }

            if (assignAsWeek) {
                if (candidate.hasFocusable()) {
                    focusableWeakCandidate = candidate;
                    focusableWeakCandidateSpanIndex = candidateLp.getSpanIndex();
                    focusableWeakCandidateOverlap = Math.min(candidateEnd, prevSpanEnd)
                            - Math.max(candidateStart, prevSpanStart);
                } else {
                    unfocusableWeakCandidate = candidate;
                    unfocusableWeakCandidateSpanIndex = candidateLp.getSpanIndex();
                    unfocusableWeakCandidateOverlap = Math.min(candidateEnd, prevSpanEnd)
                            - Math.max(candidateStart, prevSpanStart);
                }
            }
        }
        return (focusableWeakCandidate != null) ? focusableWeakCandidate : unfocusableWeakCandidate;
    }

    private int getViewPosition(View view) {
        RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams)view.getLayoutParams();
        return layoutParams.getViewAdapterPosition();
    }

    private int getSpanGroupIndex2(RecyclerView.Recycler recycler, RecyclerView.State state,
                                   int viewPosition) {
        if (!state.isPreLayout()) {
            return getSpanSizeLookup().getSpanGroupIndex(viewPosition, getSpanCount());
        }
        final int adapterPosition = recycler.convertPreLayoutPositionToPostLayout(viewPosition);
        if (adapterPosition == -1) {
            return 0;
        }
        return getSpanSizeLookup().getSpanGroupIndex(adapterPosition, getSpanCount());
    }

    private int convertFocusDirectionToLayoutDirection2(int focusDirection) {
        int state = Integer.MIN_VALUE;
        if(focusDirection == View.FOCUS_UP) {
            if(getOrientation() == VERTICAL) {
                state = -1;
            }
        } else if(focusDirection == View.FOCUS_DOWN) {
            if(getOrientation() == VERTICAL) {
                state = 1;
            }
        }
        return state;
    }

    public boolean requestChildRectangleOnScreen(RecyclerView parent, View child, Rect rect,
                                                 boolean immediate,
                                                 boolean focusedChildVisible) {
        int[] scrollAmount = getChildRectangleOnScreenScrollAmount(parent, child, rect,
                immediate);
        int dx = scrollAmount[0];
        int dy = scrollAmount[1];
        if (!focusedChildVisible || isFocusedChildVisibleAfterScrolling(parent, dx, dy)) {
            if (dx != 0 || dy != 0) {
                if (immediate) {
                    parent.scrollBy(dx, dy);
                } else {
                    parent.smoothScrollBy(dx, dy);
                }
                return true;
            }
        }
        return false;
    }

    private int[] getChildRectangleOnScreenScrollAmount(RecyclerView parent, View child,
                                                        Rect rect, boolean immediate) {
        int[] out = new int[2];
        final int parentLeft = getPaddingLeft();
        final int parentTop = getPaddingTop() + rect.height();
        final int parentRight = getWidth() - getPaddingRight();
        final int parentBottom = getHeight() - getPaddingBottom() - rect.height();
        final int childLeft = child.getLeft() + rect.left - child.getScrollX();
        final int childTop = child.getTop() + rect.top - child.getScrollY();
        final int childRight = childLeft + rect.width();
        final int childBottom = childTop + rect.height();

        final int offScreenLeft = Math.min(0, childLeft - parentLeft);
        final int offScreenTop = Math.min(0, childTop - parentTop);
        final int offScreenRight = Math.max(0, childRight - parentRight);
        final int offScreenBottom = Math.max(0, childBottom - parentBottom);

        // Favor the "start" layout direction over the end when bringing one side or the other
        // of a large rect into view. If we decide to bring in end because start is already
        // visible, limit the scroll such that start won't go out of bounds.
        final int dx;
        if (getLayoutDirection() == ViewCompat.LAYOUT_DIRECTION_RTL) {
            dx = offScreenRight != 0 ? offScreenRight
                    : Math.max(offScreenLeft, childRight - parentRight);
        } else {
            dx = offScreenLeft != 0 ? offScreenLeft
                    : Math.min(childLeft - parentLeft, offScreenRight);
        }

        // Favor bringing the top into view over the bottom. If top is already visible and
        // we should scroll to make bottom visible, make sure top does not go out of bounds.
        final int dy = offScreenTop != 0 ? offScreenTop
                : Math.min(childTop - parentTop, offScreenBottom);
        out[0] = dx;
        out[1] = dy;
        return out;
    }

//    private int[] getChildRectangleOnScreenScrollAmount(RecyclerView parent, View child,
//                                                        Rect rect, boolean immediate) {
//        int[] out = new int[2];
//        final int parentLeft = getPaddingLeft();
//        final int parentTop = getPaddingTop();
//        final int parentRight = getWidth() - getPaddingRight();
//        final int parentBottom = getHeight() - getPaddingBottom();
//        final int childLeft = child.getLeft() + rect.left - child.getScrollX();
//        final int childTop = child.getTop() + rect.top - child.getScrollY();
//        final int childRight = childLeft + rect.width();
//        final int childBottom = childTop + rect.height();
//
//        final int offScreenLeft = Math.min(0, childLeft - parentLeft);
//        final int offScreenTop = Math.min(0, childTop - parentTop);
//        final int offScreenRight = Math.max(0, childRight - parentRight);
//        final int offScreenBottom = Math.max(0, childBottom - parentBottom);
//
//        // Favor the "start" layout direction over the end when bringing one side or the other
//        // of a large rect into view. If we decide to bring in end because start is already
//        // visible, limit the scroll such that start won't go out of bounds.
//        final int dx;
//        if (getLayoutDirection() == ViewCompat.LAYOUT_DIRECTION_RTL) {
//            dx = offScreenRight != 0 ? offScreenRight
//                    : Math.max(offScreenLeft, childRight - parentRight);
//        } else {
//            dx = offScreenLeft != 0 ? offScreenLeft
//                    : Math.min(childLeft - parentLeft, offScreenRight);
//        }
//
//        // Favor bringing the top into view over the bottom. If top is already visible and
//        // we should scroll to make bottom visible, make sure top does not go out of bounds.
//        int dy = offScreenTop != 0 ? offScreenTop
//                : Math.min(childTop - parentTop, offScreenBottom);
//
//        if(dy > 0) {
//            if(dy < rect.height()) {
//                dy = rect.height();
//            }
////            final int childOffBottom = childBottom - parentBottom;
////            if(childOffBottom <= rect.height()) {
////                dy = rect.height();
////            }
//        } else if(dy < 0) {
//            if(dy > -rect.height()) {
//                dy = -rect.height();
//            }
////            final int childOffTop = childBottom - parentTop;
////            if (Math.abs(childOffTop) <= rect.height()) {
////                dy = -rect.height();
////            }
//        }
//
//        out[0] = dx;
//        out[1] = dy;
//        return out;
//    }

    private boolean isFocusedChildVisibleAfterScrolling(RecyclerView parent, int dx, int dy) {
        final View focusedChild = parent.getFocusedChild();
        if (focusedChild == null) {
            return false;
        }
        final int parentLeft = getPaddingLeft();
        final int parentTop = getPaddingTop();
        final int parentRight = getWidth() - getPaddingRight();
        final int parentBottom = getHeight() - getPaddingBottom();
        final Rect bounds = new Rect();
        getDecoratedBoundsWithMargins(focusedChild, bounds);

        if (bounds.left - dx >= parentRight || bounds.right - dx <= parentLeft
                || bounds.top - dy >= parentBottom || bounds.bottom - dy <= parentTop) {
            return false;
        }
        return true;
    }

}
