package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.DisplayMetrics
import android.view.Gravity
import android.view.WindowManager
import android.widget.ImageView
import androidx.core.content.res.ResourcesCompat
import com.bumptech.glide.request.target.ImageViewTarget
import com.bumptech.glide.request.transition.Transition
import com.drake.net.time.Interval
import com.sh.ott.video.ShVideoLogger
import com.sh.ott.video.ad.AdRequestFLogo
import com.sh.ott.video.ad.AdRequestFactory
import com.sh.ott.video.component.AdLogoControlComponent
import com.sh.ott.video.contor.ShControlComponent
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.widget.GlideImageView
import java.util.concurrent.TimeUnit

//AdLogoControlComponent
class VideoFLogoComponent constructor(context: Context) : BaseControlComponent(context),
    AdLogoControlComponent {

    private var gifView: GlideImageView? = null
    private var mTransparentBg: Drawable? = null
    private var originalParams: LayoutParams? = null
    private var mWidthInFullScreen = 0
    private var mWidthInSmallScreen: Int = 0
    private var marginInFullScreen: Int = 0
    private var marginInSmallScreen: Int = 0
    private var mHideTimer: Interval? = null
    private var timerOut = 0L

    init {
        gone()
        gifView = GlideImageView(context)
        gifView?.scaleType = ImageView.ScaleType.FIT_XY
        mWidthInFullScreen = resources.getDimension(R.dimen.x150).toInt()
        mTransparentBg = ResourcesCompat.getDrawable(resources, R.drawable.transparent, null)
        val wm = (getContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager)
        val displayMetrics = DisplayMetrics()
        wm.defaultDisplay.getMetrics(displayMetrics)
        val scale =
            (displayMetrics.widthPixels / resources.getDimension(R.dimen.x808).toInt()).toFloat()
        mWidthInSmallScreen = (mWidthInFullScreen / scale).toInt()
        originalParams = LayoutParams(mWidthInSmallScreen, mWidthInSmallScreen)
        marginInFullScreen = resources.getDimension(R.dimen.ad_corner_margin).toInt()
        marginInSmallScreen = resources.getDimension(R.dimen.x18).toInt()
        originalParams!!.bottomMargin = marginInSmallScreen
        originalParams!!.rightMargin = marginInSmallScreen
        originalParams?.gravity = Gravity.BOTTOM or Gravity.END
        gifView?.layoutParams = originalParams
        addView(gifView)
    }

    private var img: String? = null
    override fun onAdLogoSuccess(mShowTime: Int, url: String?) {
        timerOut = mShowTime.toLong()
        img = url
//        img = "http://images.snmsohu.aisee.tv/ytv/BJ/11629/505020180904183056.png"
        onCreateShowTimer()
        AppLogger.d("VideoFLogoComponent", "ShowTime${mShowTime}  " + "url:$url")
    }

    fun reset() {
        mHideTimer?.cancel()
        mHideTimer = null
        AppLogger.d("VideoFLogoComponent", "reset ")
    }

    fun resetHide() {
        mHideTimer?.cancel()
        mHideTimer = null
        AppLogger.d("VideoFLogoComponent", "resetHide ")
    }


    /**
     * 大小窗切换
     *
     * @param fullScreen
     */
    private fun setFullScreen(fullScreen: Boolean) {
        val width = if (fullScreen) mWidthInFullScreen else mWidthInSmallScreen
        val margin = if (fullScreen) marginInFullScreen else marginInSmallScreen
        originalParams!!.width = width
        originalParams!!.height = width
        originalParams!!.bottomMargin = margin
        originalParams!!.rightMargin = margin
        originalParams?.gravity = Gravity.BOTTOM or Gravity.END
        gifView!!.layoutParams = originalParams
    }


    private fun onCreateHideTimer() {
        mHideTimer = Interval(timerOut, 1, TimeUnit.SECONDS, 0).subscribe {
            AppLogger.d("VideoFLogoComponent", "onCreateHideTimer:$it")
        }.finish {
            resetHide()
            gone()
            AppLogger.d("VideoFLogoComponent", "onCreateHideTimer finish")
        }
    }


    private fun onCreateShowTimer() {
        gifView?.setImageRes(
            img,
            mTransparentBg,
            mTransparentBg,
            true,
            object : ImageViewTarget<Drawable?>(gifView) {
                override fun onResourceReady(
                    resource: Drawable,
                    transition: Transition<in Drawable?>?
                ) {
                    super.onResourceReady(resource, transition)
                    AdRequestFLogo.getInstants().reportFLogoAd()
                    visible()
                    resetHide()
                    onCreateHideTimer()
                    gifView!!.setImageDrawable(resource)
                    mHideTimer?.start()
                    AppLogger.d("load flogoAd onResourceReady")
                }

                override fun setResource(resource: Drawable?) {

                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    mHideTimer?.cancel()
                    AppLogger.d("load flogoAd failed")
                }
            })
    }

    override fun onPlayStateChanged(playState: Int, extras: HashMap<String, Any>) {
        if (playState == PlayerConstants.VideoState.PAUSED) {
            mHideTimer?.pause()
        }
        if (playState == PlayerConstants.VideoState.PLAYING || playState == PlayerConstants.VideoState.PLAYING_BACK) {
            mHideTimer?.switch()
            ShVideoLogger.d("requestLogoAd mHideTimer switch：${mHideTimer?.state?.name}")
        }
    }

    override fun onScreenModeChanged(screenMode: Int) {
        if (screenMode == PlayerConstants.ScreenMode.FULL) {
            setFullScreen(true)
        } else {
            setFullScreen(false)
        }
    }

    override fun onAdLogoError(msg: String?) {
        ShVideoLogger.d("requestLogoAd onAdLogoError ：${msg}")
    }

}