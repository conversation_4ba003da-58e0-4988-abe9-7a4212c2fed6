package com.sohuott.tv.vod.videodetail.activity.control

import android.content.Context
import android.widget.TextView
import androidx.core.view.isVisible
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.controller.component.BaseControlComponent
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.visible
import java.lang.ref.WeakReference
import kotlin.concurrent.Volatile

class VideoNextPlayPromptComponent constructor(context: Context) :
    BaseControlComponent(context) {
    private var screenMode = PlayerConstants.ScreenMode.NORMAL
    private var mScaleFullPrepareTipsRunnable: ScaleFullPrepareTipsRunnable? = null
    private var tvScaleFullBottomTips: TextView

    //是否已经展示了即将播放
    @Volatile
    private var isAlreadyShowFullPrepareTips = false
    private var isSendMsg = false
    private var isCanShow = true
    private var mCurPos = 0L
    private var mDuration = 0L


    fun getIsShow(): Boolean {
        return isVisible
    }

    init {
        gone()

        layoutInflater.inflate(R.layout.video_component_next_play_prompt, this, true)
        tvScaleFullBottomTips = findViewById<TextView>(R.id.tv_scale_full_bottom_tips)

    }

    override fun onScreenModeChanged(screenMode: Int) {
        super.onScreenModeChanged(screenMode)
        this.screenMode = screenMode
        if (screenMode != PlayerConstants.ScreenMode.FULL) {
            gone()
        }

    }

    override fun onProgressChanged(duration: Long, position: Long) {
        mCurPos = position / 1000
        mDuration = duration / 1000
        refreshLayout()
    }

    private fun refreshLayout() {
        if (isAlreadyShowFullPrepareTips) return
        val disparity: Int = (mDuration - mCurPos).toInt()
        if (disparity in 0..20 && mCurPos > 0) {
            if (isSendMsg || screenMode != PlayerConstants.ScreenMode.FULL || !isCanShow) {
                return
            }
            show()
            isSendMsg = true
            if (mScaleFullPrepareTipsRunnable != null) {
                removeCallbacks(mScaleFullPrepareTipsRunnable)
                mScaleFullPrepareTipsRunnable = null
            }
            mScaleFullPrepareTipsRunnable = ScaleFullPrepareTipsRunnable(this)
            postDelayed(mScaleFullPrepareTipsRunnable, 5000);
        }
    }


    fun setNextInfo(hasNext: Boolean, nextName: String?) {
        val name = if (hasNext && nextName?.isNotEmpty() == true) {
            "即将播放: $nextName"
        } else {
            "即将播放完成"
        }
        isAlreadyShowFullPrepareTips = false
        isSendMsg = false
        tvScaleFullBottomTips.text = name
    }

    fun setIsCanShow(isCanShow: Boolean) {
        this.isCanShow = isCanShow
        if (!isCanShow) {
            hide()
        }
    }


    fun show() {
        visible()
    }

    fun hide() {
        gone()
    }


    inner class ScaleFullPrepareTipsRunnable internal constructor(scaleScreenView: VideoNextPlayPromptComponent) :
        Runnable {
        var mWrapper: WeakReference<VideoNextPlayPromptComponent> = WeakReference(scaleScreenView)

        override fun run() {
            val scaleScreenView = mWrapper.get() ?: return
            scaleScreenView.isAlreadyShowFullPrepareTips = true
            scaleScreenView.hide()
            scaleScreenView.mScaleFullPrepareTipsRunnable = null
        }
    }
}