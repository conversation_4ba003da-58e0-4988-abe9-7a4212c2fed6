package com.sohuott.tv.vod.activity.launcher

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.scopeNetLife
import com.drake.net.Get
import com.drake.net.okhttp.trustSSLCertificate
import com.google.gson.Gson
import com.sohu.lib_utils.PrefUtil
import com.sohu.ott.base.lib_user.HeaderHelper
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohuott.tv.vod.app.AppConstants
import com.sohuott.tv.vod.lib.api.RetrofitApi
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.model.TopInfo
import com.sohuott.tv.vod.lib.model.launcher.HomeTab
import okhttp3.ConnectionSpec
import okhttp3.Headers.Companion.toHeaders

class MainViewModel : ViewModel() {
    private val _repository = MainRepository()

    private var _topData = MutableLiveData<State<TopInfo?>>()

    val topData = _topData

    private val _homeChannelData = MutableLiveData<State<HomeTab>>()
    val homeChannelData: LiveData<State<HomeTab>>
        get() = _homeChannelData

    fun getTopBarData(passport: String?, token: String?) {
        scopeNetLife {
            try {
                val data = _repository.fetchTopBarData(passport, token).await()
                if (data?.data != null) {
                    PrefUtil.putString(AppConstants.KEY_ALERT_PRIVACY_DATA, Gson().toJson(data.data))
                    _topData.value = State.success(data)
                } else {
                    _topData.value = State.empty()
                }
            } catch (e: Exception) {
                _topData.value = State.error(Message(message = e.localizedMessage))
            }

        }
    }


    fun getChannelList() {
        val startTime = System.currentTimeMillis()
        scopeNetLife {
            LibDeprecatedLogger.d("频道列表请求开始时间: ${System.currentTimeMillis() - startTime}")
            try {
                val channelList =
                    Get<HomeTab>("${RetrofitApi.get().retrofitHost.baseHost}common/channelList.json") {
                        setHeaders(HeaderHelper.getHeaders().toHeaders())
                        param("dts", UserInfoHelper.getDtsParams())
                        setClient {
                            trustSSLCertificate()
                            connectionSpecs(
                                listOf(
                                    ConnectionSpec.MODERN_TLS,
                                    ConnectionSpec.COMPATIBLE_TLS,
                                    ConnectionSpec.CLEARTEXT
                                )
                            )
                        }
                    }.await()
                _homeChannelData.value = State.success(channelList)
            } catch (e: Exception) {
                _homeChannelData.value = State.error(Message(message = e.localizedMessage))
            }
            LibDeprecatedLogger.d("频道列表请求结束时间: ${System.currentTimeMillis() - startTime}")
        }
    }

}