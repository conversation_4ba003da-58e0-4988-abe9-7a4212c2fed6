package com.sohuott.tv.vod.view;

import android.content.Context;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.sohuott.tv.vod.R;
import com.lib_statistical.manager.RequestManager;
import com.sohuott.tv.vod.lib.model.SearchResult;
import com.sohuott.tv.vod.lib.utils.Constant;
import com.sohuott.tv.vod.utils.ActivityLauncher;
import com.sohuott.tv.vod.utils.SearchUtil;
import com.sohuott.tv.vod.widget.GlideImageView;

/**
 * Created by fenglei on 17-6-22.
 */

public class SearchResultBigPicItemView extends RelativeLayout
        implements View.OnClickListener {

    private GlideImageView cornerTagImageView;
    private TextView titleTV;
    private TextView yearTV;
    private TextView typeTV;
    private TextView descTV;
    private int position;
    private boolean mResizeEnable = false;
    private int mAlbumWidth;
    private int mAlbumHeight;

    protected String mPageName = "6_search_result";

    public SearchResultBigPicItemView(Context context) {
        super(context);
        initUI(context);
    }

    public SearchResultBigPicItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initUI(context);
    }

    public SearchResultBigPicItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initUI(context);
    }

    public GlideImageView getImageView(){
        return cornerTagImageView;
    }

    public void setAlbumResize(int width, int height){
        mAlbumWidth = width;
        mAlbumHeight = height;
        mResizeEnable = true;
    }

    public void customLayoutInflater(Context context){
        LayoutInflater.from(context).inflate(
                R.layout.search_result_recyclerview_big_pic_item, this, true);
    }

    private void initUI(Context context) {
        setFocusable(true);
        setOnClickListener(this);
        customLayoutInflater(context);
        cornerTagImageView = (GlideImageView) findViewById(R.id.search_result_big_poster_iv);
        titleTV = (TextView) findViewById(R.id.search_result_big_poster_title_tv);
        yearTV = (TextView) findViewById(R.id.search_result_big_poster_year_tv);
        typeTV = (TextView) findViewById(R.id.search_result_big_poster_type_tv);
        descTV = (TextView) findViewById(R.id.search_result_big_poster_desc_tv);
    }

    public void setData(int position, SearchResult.DataBean.ItemsBean itemsBean) {
        if(itemsBean != null) {
            setTag(itemsBean);
            String title;
            String picUrl;
            if(!TextUtils.isEmpty(itemsBean.getDataType()) &&  TextUtils.equals(itemsBean.getDataType(),"star")){
                title = itemsBean.getStarName();
                picUrl = itemsBean.getStarPic();
            }else {
                title = itemsBean.getTitle();
                picUrl = itemsBean.getPic_480_660();
            }
            titleTV.setText(title);
            yearTV.setText(itemsBean.getYear());
            typeTV.setText(itemsBean.getCategory());
            descTV.setText(itemsBean.getIntroduction());
            cornerTagImageView.setImageRes(picUrl);
            this.position = position;
        }
    }

    @Override
    public boolean requestFocus(int direction, Rect previouslyFocusedRect) {
        return super.requestFocus(direction, previouslyFocusedRect);
    }

    public void setPageName(String pageName){
        this.mPageName = pageName;
    }

    //child override
    public void jumpActivity(Context context,SearchResult.DataBean.ItemsBean itemsBean,int position){
        if(itemsBean.getDataType().equals("star")){
            ActivityLauncher.startActorListActivity(context, itemsBean.getId(), false, itemsBean.getStarName());
            RequestManager.getInstance().onClickSearchResultStarItemEvent(mPageName,position, itemsBean.getId());
        }else {
            ActivityLauncher.startVideoDetailActivity(context, itemsBean.getId(), Constant.PAGE_SEARCH);
            RequestManager.getInstance().onClickSearchResultVrsItemEvent(mPageName,position, itemsBean.getId());
        }
    }

    protected void saveSearchHistory(Context context, SearchResult.DataBean.ItemsBean itemsBean){
        SearchUtil.saveSearchHistory(context, itemsBean);
    }

    @Override
    public void onClick(View v) {
        try {
            SearchResult.DataBean.ItemsBean itemsBean = (SearchResult.DataBean.ItemsBean)v.getTag();
            if(itemsBean != null){
//                if(itemsBean.getDataType().equals("star")){
//                    ActivityLauncher.startActorListActivity(v.getContext(), itemsBean.getId(), false, itemsBean.getStarName());
//                    RequestManager.getInstance().onClickSearchResultStarItemEvent(position, itemsBean.getId());
//                }else {
//                    ActivityLauncher.startVideoDetailActivity(v.getContext(), itemsBean.getId(), Constant.PAGE_SEARCH);
//                    RequestManager.getInstance().onClickSearchResultVrsItemEvent(position, itemsBean.getId());
//                }
                jumpActivity(v.getContext(),itemsBean,position);
                //SearchUtil.saveSearchHistory(v.getContext(), itemsBean);
                saveSearchHistory(v.getContext(), itemsBean);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setOnFocus() {
        if(titleTV != null) {
            setTVOnFocus(titleTV);
        }
    }

    public void setUnFocus() {
        if(titleTV != null) {
            setTVUnFocus(titleTV);
        }
    }

    private void setTVOnFocus(TextView textView) {
        textView.setSelected(true);
        textView.setMarqueeRepeatLimit(-1);
        textView.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    }

    private void setTVUnFocus(TextView textView) {
        textView.setSelected(false);
        textView.setEllipsize(TextUtils.TruncateAt.END);
    }

}
