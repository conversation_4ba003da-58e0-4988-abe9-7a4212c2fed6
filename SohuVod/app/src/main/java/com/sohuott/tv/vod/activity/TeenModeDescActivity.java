package com.sohuott.tv.vod.activity;

import static com.sohuott.tv.vod.utils.ParamConstant.PAGE_SOURCE_TEENAGER;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.R;
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.TeenModeDesc;
import com.sohuott.tv.vod.lib.utils.LoginUserInformationHelper;
import com.sohuott.tv.vod.utils.ActivityLauncher;

import io.reactivex.observers.DisposableObserver;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/3/1.
 */
public class TeenModeDescActivity extends BaseActivity {
    private static final String TAG = TeenModeDescActivity.class.getSimpleName();
    private TextView mTitle, mTeenModeDesc, mWatchTimeDefaut, watch_time_period;
    private Button mBtn;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppLogger.d(TAG, "onCreate");
        setContentView(R.layout.activity_teenmode_desc);
        initView();
        requestTeenModeDesc();
        TeenagersManger.getInstance().exposureTeenagerModePage();
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (getIntent() != null) {
            setIntent(intent);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    private void initView() {
        mTitle = (TextView) findViewById(R.id.title);
        mTeenModeDesc = (TextView) findViewById(R.id.teen_mode_desc);
        mWatchTimeDefaut = (TextView) findViewById(R.id.watch_time_defaut);
        watch_time_period = (TextView) findViewById(R.id.watch_time_period);
        mBtn = (Button) findViewById(R.id.btn_open_now);
        mBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Boolean isLogin = LoginUserInformationHelper.getHelper(TeenModeDescActivity.this).getIsLogin();
                if (isLogin) {
                    ActivityLauncher.startChildLockActivity(TeenModeDescActivity.this, TeenagerLockActivity.TYPE_ONE_SET_PASSWORD);
                } else {
                    ActivityLauncher.startLoginActivity(TeenModeDescActivity.this, PAGE_SOURCE_TEENAGER);
                }
                TeenagersManger.getInstance().exposureShowDecDialogClickOpen();
                finish();
            }
        });
    }

    private void requestTeenModeDesc() {
        AppLogger.d(TAG, "requestTeenModeDesc");
        NetworkApi.getTeenModeDesc(new DisposableObserver<TeenModeDesc>() {
            @Override
            public void onNext(TeenModeDesc value) {
                AppLogger.d(TAG, "onNext");
                if (value != null && value.data != null) {
                    mTitle.setText(value.data.title);
                    AppLogger.d(TAG, "value.data.desc ? " + value.data.desc);
                    mTeenModeDesc.setText(value.data.desc);
                    mWatchTimeDefaut.setText(value.data.watch_time_defaut);
                    watch_time_period.setText(value.data.watch_time_period);
                }
            }

            @Override
            public void onError(Throwable e) {
                LibDeprecatedLogger.e("onError in getAboutInfo()error: " + e.getMessage(), e);
                AppLogger.d(TAG, "onError e ? " + e);
                e.printStackTrace();
            }

            @Override
            public void onComplete() {
                AppLogger.d(TAG, "onComplete");
            }
        });
    }

}
