package com.sohuott.tv.vod.presenter;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.sohu.lib_utils.StringUtil;
import com.sohu.ott.ads.sdk.SdkFactory;
import com.sohuott.tv.vod.AppLogger;
import com.sohuott.tv.vod.lib.api.NetworkApi;
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger;
import com.sohuott.tv.vod.lib.model.BootTipsBean;
import com.sohuott.tv.vod.lib.utils.Util;
import com.sohuott.tv.vod.view.BootView;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Objects;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableObserver;

/**
 * Created by wenjingbian on 2016/3/30.
 */
public class BootPresenterImpl implements BootPresenter {


    private final WeakReference<BootView> mBootView;

    private final Context mContext;

    public BootPresenterImpl(Context context, BootView bootView) {
        this.mContext = context.getApplicationContext();
        this.mBootView = new WeakReference<BootView>(bootView);
    }

    /**
     * Create and start the certain child thread by threadAction
     * int threadAction: Index of child thread you want to create
     */
    @Override
    public void createAndStartChildThread() {
//        requestAdResource();
        NetworkApi.getWithCache(mContext, new Observer<JsonObject>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(JsonObject value) {
                //数据无异常
                if (value != null &&
                        value.get("data") != null &&
                        !"\"\"".equals(value.get("data").toString()) &&
                        !StringUtil.isEmpty(new Gson().toJson(value.get("data")))) {

                    String maps = new Gson().toJson(value.get("data"));


                    setOttHostMaps(maps);

                    requestAdResource();
                } else {
                    //接口无异常，数据异常，直接请求广告
                    requestAdResource();
                }
            }

            @Override
            public void onError(Throwable e) {
                //接口异常，请求广告
                requestAdResource();
                AppLogger.v(Objects.requireNonNull(e.getMessage()));
            }

            @Override
            public void onComplete() {

            }
        }, NetworkApi.networkInterface.getOttHostMaps(), JsonObject.class);


//        String ottHostJson = Util.getOttHostParams(mContext);

//        //有缓存，设置host，请求广告
//        if (!StringUtil.isEmpty(ottHostJson)) {
//            setOttHostMaps(ottHostJson);
//
//            requestAdResource();
//        }
//        NetworkApi.getOttHostMaps(new Observer<JsonObject>() {
//                @Override
//                public void onSubscribe(Disposable d) {
//
//                }
//
//                @Override
//                public void onNext(JsonObject value) {
//                    //数据无异常
//                    if (value!= null &&
//                            value.get("data") != null &&
//                            !"\"\"".equals(value.get("data").toString()) &&
//                            !StringUtil.isEmpty(new Gson().toJson(value.get("data")))) {
//
//                        String maps = new Gson().toJson(value.get("data"));
//
//                        if (StringUtil.isEmpty(ottHostJson)) {
//                            //没有缓存过，设置maps，请求广告
//                            setOttHostMaps(maps);
//                            requestAdResource();
//                        }
//
//                        AppLogger.v("缓存是否相同 : " + maps.equals(ottHostJson));
//
//                        if (!maps.equals(ottHostJson) && !StringUtil.isEmpty(maps)) {
//                            //刷新缓存
//                            Util.setOttHostParams(mContext, maps);
//                        }
//                    } else {
//                        //接口无异常，数据异常，直接请求广告
//                        requestAdResource();
//                    }
//                }
//
//                @Override
//                public void onError(Throwable e) {
//                    //接口异常，请求广告
//                    requestAdResource();
//                    AppLogger.v(Objects.requireNonNull(e.getMessage()));
//                }
//
//                @Override
//                public void onComplete() {
//
//                }
//            });

    }

    private void requestAdResource() {
        if (mBootView != null) {
            BootView view = mBootView.get();
            if (view!=null){
                view.requestAd();
            }
        }
//        Thread childThread = new Thread(new BootRunnable());
//        childThread.setDaemon(true);
//        childThread.start();
    }

    private void setOttHostMaps(String ottHostJson) {
        if (!StringUtil.isEmpty(ottHostJson)) {
            try {
                HashMap<String, String> ottHostMaps = new Gson().fromJson(ottHostJson, HashMap.class);
                SdkFactory.getInstance().putOttHostMap(ottHostMaps);
                LibDeprecatedLogger.d("host json : " + Util.getOttHostParams(mContext));
                LibDeprecatedLogger.d("host map : " + ottHostMaps.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void getAdTips() {
        NetworkApi.getBootTips(new DisposableObserver<BootTipsBean>() {
            @Override
            public void onNext(BootTipsBean value) {
                if (value != null && mBootView != null && mBootView.get() != null) {
                    mBootView.get().setBootTips(value);
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
    }

    /**
     * Get URL of Ads from remote server
     */
//    private void loadAdsResource() {
//        long time = System.currentTimeMillis();
//
//        Advert.getInstance().requestStartPageAd(mContext,
//                new AdCallBack() {
//                    @Override
//                    public void onSuccess(AdCommon adCommon) {
//                        AppLogger.v("URL of Ads: " + adCommon == null ? null : adCommon.getStaticResource());
//                        if (mBootView != null) {
//                            BootView bootView = mBootView.get();
//                            if (null != bootView) {
//                                bootView.setAdCommon(adCommon);
//                            }
//                        }
//                    }
//
//                    @Override
//                    public void onAdsLoadedError(IAdsLoadedError iAdsLoadedError) {
//                        LibDeprecatedLogger.d("Failed to get URL of Ads in loadAdsResource()." + iAdsLoadedError.getErrorMessage() + " " + iAdsLoadedError.getErrorType());
//                        if (mBootView != null) {
//                            BootView bootView = mBootView.get();
//                            if (null != bootView) {
//                                bootView.setAdCommon(null);
//                            }
//                        }
//                    }
//                });
//    }

//    private class BootRunnable implements Runnable {
//
//        @Override
//        public void run() {
//            loadAdsResource();
//        }
//    }
}
