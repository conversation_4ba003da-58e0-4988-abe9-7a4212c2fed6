// ISohuAidlService.aidl
package com.sohuott.tv.vod.service;


interface ISohuAidlService {

    //Stop playing video
    void playerOnStop();

    //Resume playing video in a suspended state
    void playerOnResume();

    //Pause playing video for play state
    void playerOnPause();

    //Fast forward 120 seconds for a playing video
    void playerOnForward();

    //Fast backward 120 seconds for a playing video
    void playerOnBackward();

    /**
    * Fast forward/backward to the pointed time with second for a playing video
    * @param second: if you want to fast forward any seconds, please send a positive number for parameter
    *               else if you want to fast backward any seconds, please send a negative number for parameter.
    */
    void playerOnSeekTo(int second);

     /**
     * Fast forward/backward the pointed second for a playing video
     * @param second: if you want to fast forward any seconds, please send a positive number for parameter
     *                else if you want to fast backward any seconds, please send a negative number for parameter.
     */
    void playerOnFastSeek(int seond);

    /**
    * Set player state with full screen or small window
    * @param isFullScreen: true stands for the full screen, and false stands for the small window
    */
    void playerSetFullScreen(boolean isFullScreen);

    //Back to the home page from other pages
    void backToHome();

    //Exit app from any pages
    void exitApp();

}
