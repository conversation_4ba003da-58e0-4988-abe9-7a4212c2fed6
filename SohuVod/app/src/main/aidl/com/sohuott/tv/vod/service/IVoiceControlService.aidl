package com.sohuott.tv.vod.service;

import com.sohuott.tv.vod.service.IVoiceControlListener;

interface IVoiceControlService {
    void resumePlay();
    void pause();
    void finishPlayer();
    void setFullScreen(boolean fullscreen);
    void fastForward();
    void fastBackward();
    void seekTo(int sec);
    void fastSeek(int sec);
    void addVoiceControlListener(IVoiceControlListener listener);

   //back to the home page from other pages
    void backToHome();
    //exit app from any pages
    void exitApp();
}