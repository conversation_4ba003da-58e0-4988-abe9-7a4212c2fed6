import com.android.build.api.variant.BuildConfigField
import java.text.SimpleDateFormat
import java.util.Date

plugins {
    alias(libs.plugins.androidLibrary)
    alias(libs.plugins.kotlinAndroid)
    id("com.kezong.fat-aar")
}
//apply(plugin = "com.sh.ott.video.asm.monitor")
//是否整合打包改变 group 以便于区分依赖
group = "com.sh.ott.video"
//测试版本请加   -SNAPSHOT
version = "1.1-SNAPSHOT-test-05-drm-ts-yy-00009"
fataar {
    transitive = true
}
// 获取当前构建时间
val buildTime: String = SimpleDateFormat("yyyy-MM-dd HH:mm").format(Date())

apply("../upload_ali.gradle")
android {
    namespace = "com.sh.ott.video"
    compileSdk = 34

    defaultConfig {
        minSdk = 17

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
        ndk {
            abiFilters.addAll(arrayOf("armeabi", "armeabi-v7a"))
        }
        buildConfigField("String", "SH_VIDEO_BUILD_TIME", "\"$buildTime\"")
        buildConfigField("String", "SH_VIDEO_BUILD_VERSION_NAME", "\"${version}\"")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }


}


dependencies {
    compileOnly(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))
    compileOnly(libs.core.ktx)
    compileOnly(libs.kotlinx.coroutines.core)
    compileOnly(libs.kotlinx.coroutines.android)
//    implementation("com.sohu.sohuMediaPlayer:sohuMediaPlayer:1.8.6.1694765835_x")
    //提供第三方sdk使用是需要 改为embed  打入aar中否则报错找不到
    compileOnly(libs.base.logger)
    if (project.properties.get("shVideoEmbed").toString().toBoolean()) {
        embed(project(":lib-monitor"))
        embed(project(":base-player"))
        embed(project(":lib-vv"))
        embed(project(":lib-player-sofa"))
        embed(project(":base-component"))
        embed(project(":LocalRepo:Tcl_deviceinfo_sdk_aar"))
        embed(libs.protobuf.javalite)
    } else {
        compileOnly(project(":lib-monitor"))
        compileOnly(project(":base-player"))
        compileOnly(project(":lib-vv"))
        compileOnly(project(":lib-player-sofa"))
        compileOnly(project(":base-component"))
        compileOnly(project(":LocalRepo:Tcl_deviceinfo_sdk_aar"))
    }
    if (project.properties.get("shVideoUseMavenSofaRepo").toString().toBoolean()) {
        embed(libs.sohumediaplayer)
    } else {
        embed(project(":LocalRepo:test_player_aar"))
    }
    //先不打入 防止 aar app name 冲突
    compileOnly(project(":LocalRepo:Ad_core_aar"))
    compileOnly(project(":LocalRepo:Ad_ott_sdk_aar"))
}